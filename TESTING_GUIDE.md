# 🧪 Umfassender Test-Guide für Kaufverifizierung

## 📋 Übersicht
Nachdem die Google Play Developer API aktiviert wurde, können wir nun alle Komponenten der Kaufverifizierung systematisch testen.

## 🔧 Test-Umgebungen

### 1. Lokale Entwicklungsumgebung
- **Sandbox-Modus**: `IS_SANDBOX_MODE=true`
- **Simulierte Käufe**: Keine echten API-Aufrufe
- **Schnelle Iteration**: Sofortige Ergebnisse

### 2. Supabase Edge Function Tests
- **Deployed Function**: `verify-purchase`
- **Echte API-Integration**: Google Play Developer API
- **Service Account**: `<EMAIL>`

### 3. Google Play Console Test-Tracks
- **Internal Testing**: Geschlossene Benutzergruppe
- **Alpha/Beta Testing**: Erweiterte Testgruppen
- **Production**: Live-Umgebung

## 🚀 Schritt-für-Schritt Testplan

### Phase 1: Lokale Edge Function Tests

#### 1.1 Sandbox-Modus testen
```bash
# Terminal 1: Supabase lokal starten
cd /Users/<USER>/Documents/ki_test
supabase start

# Terminal 2: Edge Function lokal testen
supabase functions serve verify-purchase
```

#### 1.2 Test-Request senden
```bash
# iOS Test-Request
curl -X POST http://localhost:54321/functions/v1/verify-purchase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -d '{
    "userId": "test-user-123",
    "platform": "ios",
    "receiptData": "test-receipt-data",
    "productId": "monthly_premium"
  }'

# Android Test-Request
curl -X POST http://localhost:54321/functions/v1/verify-purchase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -d '{
    "userId": "test-user-123",
    "platform": "android",
    "receiptData": "test-receipt-data",
    "purchaseToken": "test-purchase-token",
    "packageName": "com.example.app",
    "productId": "monthly_premium"
  }'
```

### Phase 2: Produktions Edge Function Tests

#### 2.1 Deployed Function testen
```bash
# Produktions-URL verwenden
curl -X POST https://vpttdxibvjrfjzbtktqg.supabase.co/functions/v1/verify-purchase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -d '{
    "userId": "test-user-123",
    "platform": "android",
    "receiptData": "test-receipt-data",
    "purchaseToken": "REAL_PURCHASE_TOKEN",
    "packageName": "YOUR_APP_PACKAGE_NAME",
    "productId": "monthly_premium"
  }'
```

### Phase 3: Flutter App Integration Tests

#### 3.1 In-App Purchase Flow testen
1. **App starten**:
   ```bash
   flutter run
   ```

2. **Test-Szenarien**:
   - Premium-Upgrade durchführen
   - Kaufbestätigung überprüfen
   - Abonnement-Status validieren
   - Fehlerbehandlung testen

#### 3.2 Verschiedene Produkttypen testen
- **Einmalige Käufe**: Premium-Features
- **Abonnements**: Monatlich/Jährlich
- **Verbrauchsartikel**: Credits/Tokens

### Phase 4: Google Play Console Tests

#### 4.1 Test-Käufe einrichten
1. **Google Play Console öffnen**
2. **App auswählen** → **Monetarisierung** → **Produkte**
3. **Test-Käufe konfigurieren**:
   - Test-Konten hinzufügen
   - Lizenz-Tester definieren
   - Test-Karten konfigurieren

#### 4.2 Internal Testing Track
1. **APK/AAB hochladen**:
   ```bash
   flutter build appbundle --release
   ```
2. **Test-Track erstellen**
3. **Tester einladen**
4. **Käufe durchführen**

### Phase 5: End-to-End Tests

#### 5.1 Vollständiger Kaufprozess
1. **App installieren** (über Test-Track)
2. **Kauf initiieren**
3. **Google Play Billing** → **Kaufbestätigung**
4. **Edge Function** → **Verifizierung**
5. **Datenbank** → **Abonnement speichern**
6. **App** → **Premium-Features freischalten**

#### 5.2 Fehlerszenarien testen
- **Netzwerkfehler**: Offline-Verhalten
- **API-Fehler**: Google Play API nicht verfügbar
- **Ungültige Käufe**: Manipulierte Daten
- **Abgelaufene Abonnements**: Renewal-Logic

## 📊 Test-Monitoring

### Supabase Dashboard
- **Edge Function Logs**: https://supabase.com/dashboard/project/vpttdxibvjrfjzbtktqg/functions
- **Database Tables**: `subscriptions`, `purchases`
- **Real-time Updates**: Subscription changes

### Google Play Console
- **Financial Reports**: Umsatz-Tracking
- **Order Management**: Kauf-Details
- **User Feedback**: Review-Monitoring

## 🔍 Debug-Strategien

### 1. Logs analysieren
```bash
# Supabase Logs
supabase functions logs verify-purchase

# Flutter Logs
flutter logs
```

### 2. Datenbank-Queries
```sql
-- Aktuelle Abonnements prüfen
SELECT * FROM subscriptions WHERE user_id = 'test-user-123';

-- Käufe analysieren
SELECT * FROM purchases ORDER BY purchase_date DESC LIMIT 10;
```

### 3. API-Response validieren
- **Status Codes**: 200, 400, 500
- **Response Format**: JSON-Struktur
- **Error Messages**: Aussagekräftige Fehlermeldungen

## ✅ Test-Checkliste

### Funktionale Tests
- [ ] iOS Kaufverifizierung (Sandbox)
- [ ] iOS Kaufverifizierung (Production)
- [ ] Android Kaufverifizierung (Sandbox)
- [ ] Android Kaufverifizierung (Production)
- [ ] Abonnement-Speicherung in DB
- [ ] Premium-Feature Freischaltung
- [ ] Abonnement-Renewal
- [ ] Kauf-Stornierung

### Performance Tests
- [ ] API Response Time < 2s
- [ ] Concurrent Purchase Handling
- [ ] Database Performance
- [ ] Edge Function Cold Start

### Sicherheitstests
- [ ] Receipt Validation
- [ ] Purchase Token Verification
- [ ] SQL Injection Prevention
- [ ] Rate Limiting

### Benutzerfreundlichkeit
- [ ] Intuitive Kaufabwicklung
- [ ] Klare Fehlermeldungen
- [ ] Offline-Verhalten
- [ ] Loading States

## 🎯 Nächste Schritte

1. **Lokale Tests starten**: Sandbox-Modus aktivieren
2. **Edge Function testen**: API-Integration validieren
3. **Flutter App testen**: End-to-End Flow
4. **Google Play Tests**: Echte Käufe durchführen
5. **Monitoring einrichten**: Logs und Metriken

## 📞 Support und Debugging

### Häufige Probleme
- **API Key Fehler**: Service Account Berechtigungen prüfen
- **Package Name Mismatch**: App-Konfiguration validieren
- **Purchase Token Invalid**: Timing-Probleme bei Verifizierung

### Hilfreiche Links
- [Google Play Developer API Docs](https://developers.google.com/android-publisher)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Flutter In-App Purchase](https://pub.dev/packages/in_app_purchase)

---

**Status**: ✅ Bereit für umfassende Tests  
**Letzte Aktualisierung**: $(date)  
**Nächster Review**: Nach ersten Produktionstests
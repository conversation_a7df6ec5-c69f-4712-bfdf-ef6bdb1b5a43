# Projektübersicht: ki_test

Dieses Dokument bietet einen allgemeinen Überblick über das `ki_test`-Projekt für den Gemini-Assistenten.

## 1. Projektbeschreibung

`ki_test` ist eine plattformübergreifende Anwendung, die mit dem **Flutter**-Framework erstellt wurde. Basierend auf den Abhängigkeiten integriert die Anwendung eine Vielzahl von Funktionen, darunter:

*   **Benutzerauthentifizierung:** Unterstützt die Anmeldung über Google und andere Methoden mit Supabase.
*   **Monetarisierung:** Implementiert In-App-Käufe und Google Mobile Ads.
*   **KI-Integration:** Nutzt die Generative AI von Google (Gemini API) für erweiterte Funktionen.
*   **Standortdienste:** Greift auf den Gerätestandort für standortbezogene Funktionen zu.
*   **Umfangreiches UI & State Management:** Verwendet `go_router` für die Navigation und `flutter_riverpod` für das State Management.
*   **Backend/Tooling:** Verwendet Node.js für zusätzliche Entwicklungs-Skripte und -Aufgaben.

## 2. Schlüsseltechnologien

*   **Frontend-Framework:** Flutter
*   **Programmiersprache:** Dart
*   **State Management:** Flutter Riverpod
*   **Navigation:** go_router
*   **Backend-as-a-Service (BaaS):** Supabase
*   **API-Clients:** `http`, `dio`, `google_generative_ai`
*   **Entwicklungs-Skripting:** Node.js

## 3. Projektstruktur

Das Projekt folgt einer Standard-Flutter-Verzeichnisstruktur:

*   `lib/`: Enthält den Kern-Dart-Quellcode für die Anwendung.
*   `android/`, `ios/`, `web/`, `macos/`, `linux/`, `windows/`: Plattformspezifischer Code und Konfiguration.
*   `pubspec.yaml`: Definiert die Metadaten und Dart/Flutter-Abhängigkeiten des Projekts.
*   `package.json`: Definiert Node.js-Abhängigkeiten und Skripte für Entwicklungstools.
*   `_resources/assets/`: Enthält statische Assets wie Icons, Audio und Animationen.
*   `.env`: Speichert Umgebungsvariablen (z. B. API-Schlüssel).

## 4. Erstellen und Ausführen

### Flutter-Anwendung

1.  **Abhängigkeiten installieren:**
    ```bash
    flutter pub get
    ```

2.  **App ausführen (Entwicklung):**
    ```bash
    flutter run
    ```

3.  **App erstellen (Release):**
    ```bash
    flutter build <plattform>
    ```
    (z. B. `flutter build apk`, `flutter build ios`)

### Node.js-Skripte

1.  **Abhängigkeiten installieren:**
    ```bash
    npm install
    ```

2.  **Skripte ausführen:**
    Siehe den `scripts`-Abschnitt in `package.json`. Zum Beispiel:
    ```bash
    npm run dev
    npm run generate
    ```

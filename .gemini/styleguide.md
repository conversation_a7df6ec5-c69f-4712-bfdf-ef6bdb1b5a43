# Code-Stil-Richtlinien für ki_test Flutter App

## Dart/Flutter Code-Stil

### Namenskonventionen
- **Klassen**: PascalCase (z.B. `UserRepository`, `AuthService`)
- **Variablen/Funktionen**: camelCase (z.B. `userName`, `getUserData()`)
- **Konstanten**: SCREAMING_SNAKE_CASE (z.B. `API_BASE_URL`)
- **Dateien**: snake_case (z.B. `user_repository.dart`)
- **Ordner**: snake_case (z.B. `auth_feature`)

### Code-Formatierung
- Verwende `dart format` für automatische Formatierung
- Maximale Zeilenlänge: 80 Zeichen
- Verwende trailing commas bei Parameterlisten
- Einrückung: 2 Leerzeichen

### Dokumentation
- Alle öffentlichen APIs müssen dokumentiert werden
- Verwende `///` für Dart-Dokumentationskommentare
- Beispiele in der Dokumentation sind erwünscht

```dart
/// Authentifiziert einen Benutzer mit E-Mail und Passwort.
///
/// Gibt ein [User] Objekt zurück, wenn die Authentifizierung erfolgreich ist.
/// Wirft eine [AuthException], wenn die Anmeldedaten ungültig sind.
///
/// Beispiel:
/// ```dart
/// final user = await authService.signIn('<EMAIL>', 'password');
/// ```
Future<User> signIn(String email, String password) async {
  // Implementation
}
```

### Architektur-Regeln

#### Clean Architecture Struktur
```
lib/
├── core/
│   ├── constants/
│   ├── errors/
│   ├── utils/
│   └── widgets/
├── features/
│   └── auth/
│       ├── data/
│       │   ├── datasources/
│       │   ├── models/
│       │   └── repositories/
│       ├── domain/
│       │   ├── entities/
│       │   ├── repositories/
│       │   └── usecases/
│       └── presentation/
│           ├── pages/
│           ├── providers/
│           └── widgets/
└── main.dart
```

#### Dependency Injection
- Verwende Riverpod für Dependency Injection
- Provider sollten in separaten Dateien definiert werden
- Verwende `@riverpod` Annotation für Code-Generierung

```dart
@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  return AuthRepositoryImpl(
    remoteDataSource: ref.watch(authRemoteDataSourceProvider),
    localDataSource: ref.watch(authLocalDataSourceProvider),
  );
}
```

### Error Handling
- Verwende custom Exception-Klassen
- Implementiere proper try-catch Blöcke
- Logge Fehler für Debugging

```dart
class AuthException implements Exception {
  final String message;
  final String? code;
  
  const AuthException(this.message, {this.code});
  
  @override
  String toString() => 'AuthException: $message';
}
```

### State Management mit Riverpod
- Verwende `AsyncNotifier` für komplexe States
- Implementiere proper Loading/Error States
- Verwende `ref.invalidate()` für State-Refresh

```dart
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  FutureOr<User?> build() async {
    return await ref.watch(authRepositoryProvider).getCurrentUser();
  }
  
  Future<void> signIn(String email, String password) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      return await ref.read(authRepositoryProvider).signIn(email, password);
    });
  }
}
```

### Testing-Richtlinien
- Schreibe Unit Tests für alle Business Logic
- Verwende Mocks für externe Abhängigkeiten
- Widget Tests für UI-Komponenten
- Integration Tests für kritische User Flows

```dart
void main() {
  group('AuthRepository', () {
    late AuthRepository repository;
    late MockAuthRemoteDataSource mockRemoteDataSource;
    
    setUp(() {
      mockRemoteDataSource = MockAuthRemoteDataSource();
      repository = AuthRepositoryImpl(remoteDataSource: mockRemoteDataSource);
    });
    
    test('should return User when signIn is successful', () async {
      // arrange
      when(() => mockRemoteDataSource.signIn(any(), any()))
          .thenAnswer((_) async => testUserModel);
      
      // act
      final result = await repository.signIn('<EMAIL>', 'password');
      
      // assert
      expect(result, equals(testUser));
    });
  });
}
```

### Performance-Richtlinien
- Verwende `const` Konstruktoren wo möglich
- Implementiere `ListView.builder` für große Listen
- Verwende `Image.network` mit Caching
- Optimiere Build-Methoden (keine schweren Operationen)

### Sicherheits-Richtlinien
- Keine API-Keys im Code
- Verwende Umgebungsvariablen für sensible Daten
- Validiere alle Benutzereingaben
- Implementiere proper Authentication/Authorization

### Git-Konventionen
- Commit Messages: `type(scope): description`
- Typen: feat, fix, docs, style, refactor, test, chore
- Beispiel: `feat(auth): add Google sign-in functionality`

### Code Review Checkliste
- [ ] Code folgt den Stil-Richtlinien
- [ ] Tests sind vorhanden und bestehen
- [ ] Dokumentation ist aktuell
- [ ] Keine Sicherheitslücken
- [ ] Performance-Optimierungen berücksichtigt
- [ ] Error Handling implementiert
- [ ] Accessibility berücksichtigt
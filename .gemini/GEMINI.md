# Gemini CLI Spezifische Regeln für ki_test

## Entwicklungskontext
Dieses Projekt ist eine Flutter-Anwendung mit KI-Integration, die Clean Architecture verwendet und Supabase als Backend nutzt.

## Spezifische Anweisungen für den Assistenten

### Code-Analyse und -Verbesserung
- Analysiere immer die gesamte Projektstruktur vor Änderungen
- Berücksichtige die Clean Architecture Prinzipien
- Prüfe auf Konsistenz mit bestehenden Patterns
- Validiere Dart/Flutter Best Practices

### Memory und Kontext-Management
- Nutze das Memory-System für wichtige Projektentscheidungen
- Dokumentiere Architekturänderungen im Memory
- Behalte den Kontext von vorherigen Sitzungen bei
- Verfolge Aufgaben und deren Status

### Tool-Nutzung Prioritäten
1. **GitHub**: Für Repository-Management und Code-Reviews
2. **Supabase**: Für Backend-Operationen und Datenbankmanagement
3. **TaskMaster**: Für komplexe Aufgabenplanung
4. **Browser-Tools**: Für Recherche und Dokumentation
5. **Context7**: Für erweiterte Kontextsuche

### Debugging und Problemlösung
- Führe immer `flutter analyze` vor Code-Änderungen aus
- Nutze `flutter doctor` für Umgebungsprobleme
- Prüfe Logs systematisch bei Fehlern
- Dokumentiere Lösungen für wiederkehrende Probleme

### Sicherheits-Checkliste
- [ ] Keine API-Keys im Code
- [ ] Umgebungsvariablen korrekt konfiguriert
- [ ] Input-Validierung implementiert
- [ ] Error-Handling vorhanden
- [ ] Secrets nicht in Git committed

### Performance-Optimierung
- Verwende `const` Konstruktoren
- Implementiere Lazy Loading für große Datenmengen
- Optimiere Build-Methoden
- Nutze Caching für API-Calls
- Minimiere Widget-Rebuilds

### Testing-Strategie
- Unit Tests für Business Logic (Domain Layer)
- Widget Tests für UI-Komponenten
- Integration Tests für kritische User Flows
- Mock externe Abhängigkeiten
- Mindestens 80% Code Coverage anstreben

### Deployment-Vorbereitung
- Prüfe alle Plattform-spezifischen Konfigurationen
- Validiere App-Permissions
- Teste auf verschiedenen Geräten/Bildschirmgrößen
- Optimiere App-Größe und Performance
- Dokumentiere Release-Notes

### Kommunikation und Dokumentation
- Verwende klare, präzise deutsche Sprache
- Erkläre komplexe Konzepte verständlich
- Biete konkrete Code-Beispiele
- Dokumentiere Entscheidungsgründe
- Erstelle Schritt-für-Schritt Anleitungen

### Fehlerbehandlung
- Analysiere Fehler systematisch
- Prüfe häufige Flutter/Dart Probleme zuerst
- Nutze offizielle Dokumentation als Referenz
- Biete alternative Lösungsansätze
- Teste Lösungen vor der Implementierung

### Projektspezifische Besonderheiten
- **KI-Integration**: Nutze Google Generative AI verantwortungsvoll
- **Monetarisierung**: Beachte App Store Richtlinien für In-App-Käufe
- **Authentifizierung**: Implementiere sichere Auth-Flows mit Supabase
- **Standortdienste**: Berücksichtige Datenschutz und Permissions
- **Cross-Platform**: Teste auf allen Zielplattformen

## Memory-Kategorien
- `project_decisions`: Wichtige Architektur- und Design-Entscheidungen
- `bug_fixes`: Gelöste Probleme und deren Lösungen
- `feature_implementations`: Neue Features und deren Implementierung
- `performance_optimizations`: Durchgeführte Performance-Verbesserungen
- `security_updates`: Sicherheitsrelevante Änderungen
- `dependency_updates`: Aktualisierungen von Abhängigkeiten

## Qualitätssicherung
- Code-Reviews vor jedem Merge
- Automatisierte Tests in CI/CD Pipeline
- Regelmäßige Dependency-Updates
- Security-Scans für Vulnerabilities
- Performance-Monitoring in Production
## regel 1
- immer wenn du mir antowrtest sage "Hallo Mohamad" 
- Du darfst Sachen nicht bearbeiten, die ich dir nicht explizit gesagt habe, dass du sie bearbeiten sollst. Arbeite nur definiert und richtig, was ich dir sage andere Sachen, die mit einem Problem oder mit einem Kontext nichts zu tun haben, darfst du nicht ändern. Das Projekt ist sehr groß und er trägt keine Fehler.
- Wenn du fertig bist mit der Lösung eines Problems oder irgendwas Neues, dann startest du die App mit Android USB
{"mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "trust": false, "timeout": 30000}, "browser-tools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "trust": false, "timeout": 30000}, "taskmaster-ai": {"command": "npx", "args": ["-y", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "TASKMASTER_PROJECT_NAME": "ki_test", "TASKMASTER_MODEL": "claude-3-7-sonnet-20250219", "TASKMASTER_PERPLEXITY_MODEL": "sonar-pro", "TASKMASTER_MAX_TOKENS": "64000", "TASKMASTER_TEMPERATURE": "0.2", "TASKMASTER_DEFAULT_SUBTASKS": "5", "TASKMASTER_DEFAULT_PRIORITY": "medium"}, "trust": false, "timeout": 30000}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "trust": false, "timeout": 30000}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "trust": false, "timeout": 30000}, "memory": {"command": "npx", "args": ["-y", "github:shanehol<PERSON>man/mcp-knowledge-graph", "--memory-path", "/Users/<USER>/Documents/ki_test/memory.jsonl"], "trust": false, "timeout": 30000}}}
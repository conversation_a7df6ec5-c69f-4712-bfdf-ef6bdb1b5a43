# Gemini CLI Konfiguration für ki_test Projekt

# Allgemeine Einstellungen
contextFileName: "GEMINI.md"
autoAccept: false
theme: "dark"
sandbox: false

# Datei-Filterung
fileFiltering:
  enabled: true
  excludePatterns:
    - "*.log"
    - "*.tmp"
    - "node_modules/**"
    - ".dart_tool/**"
    - "build/**"
    - ".git/**"
    - "*.env"
    - "*.key"
    - "*.pem"
  includePatterns:
    - "*.dart"
    - "*.yaml"
    - "*.json"
    - "*.md"
    - "*.js"
    - "*.ts"

# Core Tools Konfiguration
coreTools:
  enabled: true
  tools:
    - "edit"
    - "search"
    - "find"
    - "write"
    - "read"

# Ausgeschlossene Tools
excludeTools:
  - "dangerous_command"
  - "system_modify"

# Bug-Kommando
bugCommand: "flutter analyze && flutter test"

# Projektspezifische Regeln
projectRules:
  language: "dart"
  framework: "flutter"
  architecture: "clean_architecture"
  stateManagement: "riverpod"
  navigation: "go_router"
  backend: "supabase"

# Memory-Einstellungen
memory:
  enabled: true
  path: "/Users/<USER>/Documents/ki_test/memory.jsonl"
  maxEntries: 1000
  autoSave: true

# Entwicklungseinstellungen
development:
  verbose: true
  debugMode: true
  hotReload: true
  testMode: false

# Code-Qualitätsregeln
codeQuality:
  enforceTests: true
  requireDocumentation: true
  maxComplexity: 10
  minCoverage: 80

# Sicherheitsregeln
security:
  scanSecrets: true
  validateInputs: true
  enforceHttps: true
  checkDependencies: true
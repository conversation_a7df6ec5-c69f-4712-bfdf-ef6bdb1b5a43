#!/bin/bash

# Deployment-Keystore mit gewünschtem SHA1 erstellen

echo "=== DEPLOYMENT-KEYSTORE ERSTELLUNG ==="
echo ""

DESIRED_SHA1="F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
KEYSTORE_FILE="android/app/deployment-keystore.jks"
ALIAS="deployment"
STORE_PASSWORD="123456"
KEY_PASSWORD="123456"

echo "Ziel-SHA1: $DESIRED_SHA1"
echo "Keystore: $KEYSTORE_FILE"
echo ""

# Prüfe, ob upload_cert.der den gewünschten SHA1 hat
echo "1. 🔍 PRÜFE VERFÜGBARE ZERTIFIKATE"
echo ""

echo "upload_cert.der SHA1:"
keytool -printcert -file upload_cert.der 2>/dev/null | grep "SHA1:" | head -1

echo ""
echo "deployment_cert.der SHA1:"
keytool -printcert -file deployment_cert.der 2>/dev/null | grep "SHA1:" | head -1

echo ""
echo "upload-keystore.jks SHA1:"
keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1

echo ""
echo "=== PROBLEM ANALYSE ==="
echo ""
echo "❌ Keiner der vorhandenen Keystores hat den gewünschten SHA1!"
echo "❌ Google Play erwartet: $DESIRED_SHA1"
echo "❌ Aber alle vorhandenen Keystores haben andere SHA1s"
echo ""
echo "=== LÖSUNG ==="
echo ""
echo "🔧 OPTION 1: Neuen Keystore mit gewünschtem SHA1 erstellen"
echo "   - Erstelle deployment-keystore.jks mit korrektem SHA1"
echo "   - Konfiguriere key.properties für deployment-keystore"
echo "   - Baue AAB mit neuem Keystore"
echo ""
echo "🔧 OPTION 2: Google Play Console anpassen"
echo "   - Lade aktuellen upload-keystore SHA1 in Google Play hoch"
echo "   - Lasse Google Play den SHA1 akzeptieren"
echo ""
echo "⚠️  WICHTIG: Der gewünschte SHA1 muss irgendwo herkommen!"
echo "   - Entweder von einem existierenden Keystore"
echo "   - Oder von einem Google Play App Signing Zertifikat"
echo ""
echo "🤔 FRAGE: Woher kommt der gewünschte SHA1?"
echo "   $DESIRED_SHA1"
echo ""
echo "Mögliche Quellen:"
echo "   - Alter Keystore (nicht mehr vorhanden?)"
echo "   - Google Play App Signing Zertifikat"
echo "   - Firebase Console Konfiguration"
echo ""
echo "=== NÄCHSTE SCHRITTE ==="
echo "1. Prüfe Google Play Console App Signing Einstellungen"
echo "2. Prüfe, ob der SHA1 von Google Play generiert wurde"
echo "3. Falls ja: Verwende Upload-Keystore und lasse Google Play signieren"
echo "4. Falls nein: Erstelle neuen Keystore mit gewünschtem SHA1"

# FINALE LÖSUNG: Google Play Console Upload-Zertifikat korrigieren

## Problem
- **Google Play erwartet**: `F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC`
- **Unsere AAB signiert mit**: `47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25`

## Lösung
**Google Play Console Upload-Zertifikat aktualisieren**

### Schritt 1: Google Play Console öffnen
- URL: https://play.google.com/console
- App: com.einsteinai.app
- Navigiere zu: **Release → Setup → App signing**

### Schritt 2: Upload-Zertifikat hochladen
**Datei**: `/Users/<USER>/Documents/ki_test/upload_certificate_correct.der`
**SHA1**: `47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25`

### Schritt 3: App Signing konfigurieren
1. **Upload certificate**: upload_certificate_correct.der
2. **App signing**: Google Play übernimmt automatisch
3. **Endnutzer SHA1**: Wird von Google Play verwaltet

### Schritt 4: AAB erneut hochladen
- **Datei**: `build/app/outputs/bundle/release/app-release.aab`
- **Version**: 1.0.6+6
- **Signierung**: Funktioniert jetzt mit korrektem Upload-Zertifikat

## Warum diese Lösung funktioniert

### Google Play App Signing Workflow:
1. **Developer** signiert AAB mit **Upload-Keystore** → SHA1: `47:3C:CC:4B...`
2. **Google Play** akzeptiert Upload → Prüft Upload-Zertifikat
3. **Google Play** re-signiert App → Endnutzer SHA1: `F7:CE:0E:B9...`
4. **Endnutzer** installiert App → Sieht Google Play SHA1

### Das Problem war:
- Google Play Console hatte **falsches Upload-Zertifikat** konfiguriert
- Erwartete SHA1 `F7:CE:0E:B9...` für Upload (falsch)
- Sollte SHA1 `47:3C:CC:4B...` für Upload erwarten (korrekt)

## Dateien für Upload

### Upload-Zertifikat (für Google Play Console):
```
/Users/<USER>/Documents/ki_test/upload_certificate_correct.der
SHA1: 47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25
```

### AAB-Datei (für App Upload):
```
/Users/<USER>/Documents/ki_test/build/app/outputs/bundle/release/app-release.aab
Version: 1.0.6+6
Größe: 49MB
```

## Ergebnis
- ✅ Upload-Zertifikat: Korrekt konfiguriert
- ✅ AAB-Upload: Funktioniert
- ✅ App-Signing: Google Play übernimmt
- ✅ Endnutzer SHA1: `F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC`

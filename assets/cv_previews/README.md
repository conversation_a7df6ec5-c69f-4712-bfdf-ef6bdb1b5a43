# CV Template Preview Assets

Diese Datei dokumentiert die benötigten Preview-Bilder für die neuen CV-Templates.

## Neue Template Preview-Bilder (benötigt)

Die folgenden PNG-<PERSON>ien müssen erstellt werden:

### 1. Infographic Template
- **Datei**: `infographic_cyan.png`
- **Größe**: 300x400px
- **Design**: Zeigt Infographic-Elemente wie Progress-Bars, Charts, Timeline
- **Farbe**: Cyan-Farbschema
- **Inhalt**: <PERSON>ckup eines Lebenslaufs mit visuellen Daten-Elementen

### 2. Artistic Template  
- **Datei**: `artistic_purple.png`
- **Größe**: 300x400px
- **Design**: Kreatives, asymmetrisches Layout mit künstlerischen Elementen
- **Farbe**: Purple-Farbschema
- **Inhalt**: Mockup mit kreativen Rahmen, unkonventionellem Layout

### 3. Startup Template
- **Datei**: `startup_orange.png`
- **Größe**: 300x400px
- **Design**: Modernes, dynamisches Layout mit Gradient-Hintergrund
- **Farbe**: Orange-Farbschema
- **Inhalt**: Mockup mit energetischem, tech-freundlichem Design

### 4. Academic Template
- **Datei**: `academic_indigo.png`
- **Größe**: 300x400px
- **Design**: Strukturiertes, formales Layout für Wissenschaft
- **Farbe**: Indigo-Farbschema
- **Inhalt**: Mockup mit Fokus auf Publikationen und Forschung

### 5. Fresh Template
- **Datei**: `fresh_lime.png`
- **Größe**: 300x400px
- **Design**: Jugendliches, modernes Layout für Berufseinsteiger
- **Farbe**: Lime-Farbschema
- **Inhalt**: Mockup mit frischen, energetischen Elementen

## Design-Richtlinien

- **Format**: PNG mit transparentem Hintergrund
- **Auflösung**: 300x400px (3:4 Verhältnis)
- **Stil**: Mockup-Darstellung des jeweiligen Template-Designs
- **Qualität**: Hochauflösend für scharfe Darstellung in der App
- **Konsistenz**: Einheitlicher Stil mit den vorhandenen Preview-Bildern

## Verwendung in der App

Diese Bilder werden in der Template-Auswahl angezeigt:
- `CvTemplateCard` Widget zeigt die Previews
- `CvTemplatePreviewDialog` verwendet sie für Vorschau
- Pfad wird in `CvTemplate.previewImagePath` definiert

## Status

- ✅ Template-Definitionen erstellt
- ✅ PDF-Generator erweitert  
- ⏳ Preview-Bilder benötigt (Designer/Grafiker erforderlich)
- ⏳ Asset-Integration testen

## Nächste Schritte

1. Grafiker beauftragen für Preview-Bild-Erstellung
2. Bilder in `assets/cv_previews/` platzieren
3. `pubspec.yaml` Assets-Sektion prüfen
4. Template-Vorschau in App testen

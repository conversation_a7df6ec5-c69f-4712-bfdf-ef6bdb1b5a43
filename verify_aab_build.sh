#!/bin/bash

# AAB-Build Verifizierungsskript

echo "=== AAB-DATEI VERIFIZIERUNG ==="
echo ""

AAB_PATH="build/app/outputs/bundle/release/app-release.aab"

# 1. Datei-Informationen
echo "1. 📦 AAB-DATEI INFORMATIONEN"
if [ -f "$AAB_PATH" ]; then
    echo "   Datei: ✅ $AAB_PATH"
    echo "   Größe: $(ls -lh "$AAB_PATH" | awk '{print $5}')"
    echo "   Erstellt: $(ls -l "$AAB_PATH" | awk '{print $6, $7, $8}')"
    echo "   Typ: $(file "$AAB_PATH" | cut -d: -f2)"
else
    echo "   Datei: ❌ AAB-Datei nicht gefunden"
    exit 1
fi
echo ""

# 2. Version-Informationen
echo "2. 📋 VERSION INFORMATIONEN"
if [ -f "pubspec.yaml" ]; then
    VERSION=$(grep "^version:" pubspec.yaml | awk '{print $2}')
    echo "   pubspec.yaml Version: $VERSION"
    
    # Version aufteilen
    VERSION_NAME=$(echo $VERSION | cut -d+ -f1)
    VERSION_CODE=$(echo $VERSION | cut -d+ -f2)
    echo "   Version Name: $VERSION_NAME"
    echo "   Version Code: $VERSION_CODE"
else
    echo "   pubspec.yaml: ❌ Nicht gefunden"
fi
echo ""

# 3. SHA1-Setup Status
echo "3. 🔐 SHA1-SETUP STATUS"
DESIRED_SHA1_NO_COLONS="f7ce0eb91958a89e95e67ed15ccff8b025aa90dc"

if [ -f "android/app/google-services.json" ]; then
    if grep -q "$DESIRED_SHA1_NO_COLONS" android/app/google-services.json; then
        echo "   google-services.json: ✅ Korrekter SHA1 konfiguriert"
    else
        echo "   google-services.json: ⚠️  SHA1 möglicherweise nicht aktuell"
    fi
else
    echo "   google-services.json: ❌ Nicht gefunden"
fi
echo ""

# 4. Keystore-Informationen
echo "4. 🔑 KEYSTORE INFORMATIONEN"
if [ -f "android/app/upload-keystore.jks" ]; then
    echo "   Upload-Keystore: ✅ Vorhanden"
    echo "   SHA1 (für Upload):"
    keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1 | sed 's/^/     /'
else
    echo "   Upload-Keystore: ❌ Nicht gefunden"
fi
echo ""

# 5. Build-Informationen
echo "5. 🔨 BUILD INFORMATIONEN"
echo "   Build-Typ: Release AAB (Android App Bundle)"
echo "   Ziel: Google Play Store Upload"
echo "   Signierung: Upload-Keystore (Google Play übernimmt App-Signing)"
echo "   Optimierungen: ✅ Tree-shaking aktiviert"
echo "   Komprimierung: ✅ Deflate-Komprimierung"
echo ""

# 6. Upload-Anweisungen
echo "=== UPLOAD-ANWEISUNGEN ==="
echo ""
echo "📁 AAB-Datei Pfad:"
echo "   $(pwd)/$AAB_PATH"
echo ""
echo "🚀 Google Play Console Upload:"
echo "   1. Gehe zu: https://play.google.com/console"
echo "   2. Wähle deine App: com.einsteinai.app"
echo "   3. Navigiere zu: Release → Production"
echo "   4. Klicke: 'Neues Release erstellen'"
echo "   5. Lade hoch: app-release.aab"
echo "   6. Version: $VERSION_NAME (Code: $VERSION_CODE)"
echo ""
echo "✅ BEREIT FÜR UPLOAD!"
echo ""
echo "📋 ZUSAMMENFASSUNG:"
echo "   - AAB-Datei: $(ls -lh "$AAB_PATH" | awk '{print $5}') bereit"
echo "   - Version: $VERSION_NAME+$VERSION_CODE"
echo "   - SHA1-Setup: Konfiguriert"
echo "   - Signierung: Upload-Keystore"
echo "   - Ziel: Google Play Store"

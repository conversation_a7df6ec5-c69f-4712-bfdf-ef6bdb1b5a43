
[functions.generate-cover-letter]
enabled = true
verify_jwt = true
import_map = "./functions/generate-cover-letter/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/generate-cover-letter/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/generate-cover-letter/*.html" ]

[functions.send-application-email]
enabled = true
verify_jwt = true
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/send-application-email/index.ts"

[functions.verify-purchase]
enabled = true
verify_jwt = true
entrypoint = "./functions/verify-purchase/index.ts"

[functions.validate-promo-code]
enabled = true
verify_jwt = true
entrypoint = "./functions/validate-promo-code/index.ts"

// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// CORS-Header für alle Anfragen
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

serve(async (req) => {
  // CORS-Vorflug-Anfrage behandeln
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Daten aus der Anfrage extrahieren
    const data = await req.json();

    // Prüfe, ob es sich um einen Test-Aufruf handelt
    if (data.test === true) {
      console.log("Test-Aufruf der delete-account Edge Function");
      return new Response(
        JSON.stringify({ success: true, message: "Edge Function ist verfügbar" }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const { userId, auth_token } = data;

    // Validierung
    if (!userId) {
      throw new Error("userId parameter is required");
    }

    if (!auth_token) {
      throw new Error("auth_token parameter is required");
    }

    // Supabase-Client mit Service-Role erstellen (für Admin-Operationen)
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Supabase-Client mit Benutzer-Token erstellen (für Authentifizierung)
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        global: {
          headers: {
            Authorization: `Bearer ${auth_token}`,
          },
        },
      }
    );

    // Überprüfe, ob der Benutzer authentifiziert ist und die richtige ID hat
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();

    if (authError || !user || user.id !== userId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Nicht autorisiert. Bitte melde dich erneut an."
        }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    console.log(`Lösche Konto für Benutzer: ${userId}`);

    // 1. Lösche alle Profil-Backups
    const { error: backupDeleteError } = await supabaseAdmin
      .from('profile_backups')
      .delete()
      .eq('profile_id', userId);

    if (backupDeleteError) {
      console.error(`Fehler beim Löschen der Profil-Backups: ${backupDeleteError.message}`);
    } else {
      console.log(`Profil-Backups für Benutzer ${userId} gelöscht`);
    }

    // 2. Lösche alle Abonnements
    const { error: subscriptionDeleteError } = await supabaseAdmin
      .from('subscriptions')
      .delete()
      .eq('user_id', userId);

    if (subscriptionDeleteError) {
      console.error(`Fehler beim Löschen der Abonnements: ${subscriptionDeleteError.message}`);
    } else {
      console.log(`Abonnements für Benutzer ${userId} gelöscht`);
    }

    // 3. Lösche alle beworbenen Jobs
    const { error: appliedJobsDeleteError } = await supabaseAdmin
      .from('applied_jobs')
      .delete()
      .eq('user_id', userId);

    if (appliedJobsDeleteError) {
      console.error(`Fehler beim Löschen der beworbenen Jobs: ${appliedJobsDeleteError.message}`);
    } else {
      console.log(`Beworbene Jobs für Benutzer ${userId} gelöscht`);
    }

    // 4. Lösche alle Favoriten
    const { error: favoritesDeleteError } = await supabaseAdmin
      .from('favorites')
      .delete()
      .eq('user_id', userId);

    if (favoritesDeleteError) {
      console.error(`Fehler beim Löschen der Favoriten: ${favoritesDeleteError.message}`);
    } else {
      console.log(`Favoriten für Benutzer ${userId} gelöscht`);
    }

    // 5. Lösche das Profil
    const { error: profileDeleteError } = await supabaseAdmin
      .from('profiles')
      .delete()
      .eq('id', userId);

    if (profileDeleteError) {
      console.error(`Fehler beim Löschen des Profils: ${profileDeleteError.message}`);
    } else {
      console.log(`Profil für Benutzer ${userId} gelöscht`);
    }

    // 6. Lösche den Benutzer aus der Auth-Tabelle
    const { error: userDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);

    if (userDeleteError) {
      console.error(`Fehler beim Löschen des Benutzers: ${userDeleteError.message}`);
      return new Response(
        JSON.stringify({
          success: false,
          error: `Fehler beim Löschen des Benutzers: ${userDeleteError.message}`
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    console.log(`Benutzer ${userId} erfolgreich gelöscht`);

    // Erfolgreiche Antwort
    return new Response(
      JSON.stringify({ success: true }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error(`Fehler bei der Kontolöschung: ${error.message}`);

    return new Response(
      JSON.stringify({
        success: false,
        error: `Fehler bei der Kontolöschung: ${error.message}`
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});

// Supabase Edge Function für Google Play Real-time Developer Notifications
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Typen für Google Play Notifications
interface GooglePlayNotification {
  version: string;
  packageName: string;
  eventTimeMillis: string;
  subscriptionNotification?: {
    version: string;
    notificationType: number;
    purchaseToken: string;
    subscriptionId: string;
  };
  testNotification?: {
    version: string;
  };
}

// Notification Types
const NOTIFICATION_TYPES = {
  SUBSCRIPTION_RECOVERED: 1,
  SUBSCRIPTION_RENEWED: 2,
  SUBSCRIPTION_CANCELED: 3,
  SUBSCRIPTION_PURCHASED: 4,
  SUBSCRIPTION_ON_HOLD: 5,
  SUBSCRIPTION_IN_GRACE_PERIOD: 6,
  SUBSCRIPTION_RESTARTED: 7,
  SUBSCRIPTION_PRICE_CHANGE_CONFIRMED: 8,
  SUBSCRIPTION_DEFERRED: 9,
  SUBSCRIPTION_PAUSED: 10,
  SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED: 11,
  SUBSCRIPTION_REVOKED: 12,
  SUBSCRIPTION_EXPIRED: 13
};

// Supabase-Client erzeugen
const supabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
);

/**
 * Verarbeitet Google Play Subscription Notifications
 */
async function handleSubscriptionNotification(
  notification: GooglePlayNotification,
  subscriptionNotification: any
): Promise<void> {
  const { notificationType, purchaseToken, subscriptionId } = subscriptionNotification;
  
  console.log(`Verarbeite Subscription Notification: Type=${notificationType}, Token=${purchaseToken}`);

  try {
    // Finde das Abonnement in der Datenbank anhand des Purchase Tokens
    const { data: purchases, error: purchaseError } = await supabaseClient
      .from('purchases')
      .select('user_id, subscription_id')
      .eq('transaction_id', purchaseToken)
      .single();

    if (purchaseError || !purchases) {
      console.error('Abonnement nicht gefunden für Purchase Token:', purchaseToken);
      return;
    }

    const { user_id, subscription_id } = purchases;

    // Verarbeite verschiedene Notification Types
    switch (notificationType) {
      case NOTIFICATION_TYPES.SUBSCRIPTION_PURCHASED:
      case NOTIFICATION_TYPES.SUBSCRIPTION_RENEWED:
      case NOTIFICATION_TYPES.SUBSCRIPTION_RECOVERED:
      case NOTIFICATION_TYPES.SUBSCRIPTION_RESTARTED:
        // Aktiviere Abonnement
        await updateSubscriptionStatus(subscription_id, 'active');
        console.log(`Abonnement ${subscription_id} aktiviert`);
        break;

      case NOTIFICATION_TYPES.SUBSCRIPTION_CANCELED:
      case NOTIFICATION_TYPES.SUBSCRIPTION_EXPIRED:
      case NOTIFICATION_TYPES.SUBSCRIPTION_REVOKED:
        // Deaktiviere Abonnement
        await updateSubscriptionStatus(subscription_id, 'cancelled');
        console.log(`Abonnement ${subscription_id} deaktiviert`);
        break;

      case NOTIFICATION_TYPES.SUBSCRIPTION_ON_HOLD:
      case NOTIFICATION_TYPES.SUBSCRIPTION_PAUSED:
        // Pausiere Abonnement
        await updateSubscriptionStatus(subscription_id, 'paused');
        console.log(`Abonnement ${subscription_id} pausiert`);
        break;

      case NOTIFICATION_TYPES.SUBSCRIPTION_IN_GRACE_PERIOD:
        // Grace Period - Abonnement bleibt aktiv
        await updateSubscriptionStatus(subscription_id, 'active');
        console.log(`Abonnement ${subscription_id} in Grace Period`);
        break;

      default:
        console.log(`Unbekannter Notification Type: ${notificationType}`);
    }

    // Aktualisiere User Premium Status
    await updateUserPremiumStatus(user_id);

  } catch (error) {
    console.error('Fehler beim Verarbeiten der Subscription Notification:', error);
    throw error;
  }
}

/**
 * Aktualisiert den Abonnement-Status in der Datenbank
 */
async function updateSubscriptionStatus(subscriptionId: string, status: string): Promise<void> {
  const { error } = await supabaseClient
    .from('subscriptions')
    .update({ 
      status: status,
      updated_at: new Date().toISOString()
    })
    .eq('id', subscriptionId);

  if (error) {
    console.error('Fehler beim Aktualisieren des Abonnement-Status:', error);
    throw error;
  }
}

/**
 * Aktualisiert den Premium-Status des Benutzers
 */
async function updateUserPremiumStatus(userId: string): Promise<void> {
  try {
    // Prüfe aktive Abonnements des Benutzers
    const { data: activeSubscriptions, error } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gte('end_date', new Date().toISOString());

    if (error) {
      console.error('Fehler beim Abrufen aktiver Abonnements:', error);
      return;
    }

    const hasPremium = activeSubscriptions && activeSubscriptions.length > 0;
    const latestSubscription = hasPremium ? activeSubscriptions[0] : null;

    // Aktualisiere User Profile
    const { error: updateError } = await supabaseClient
      .from('profiles')
      .update({
        data: {
          is_premium: hasPremium,
          premium_expiry_date: latestSubscription?.end_date || null,
          plan_type: latestSubscription?.plan_type || 'free'
        }
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Fehler beim Aktualisieren des User Premium Status:', error);
    } else {
      console.log(`User ${userId} Premium Status aktualisiert: ${hasPremium}`);
    }

  } catch (error) {
    console.error('Fehler beim Aktualisieren des User Premium Status:', error);
  }
}

/**
 * Dekodiert Base64-kodierte Google Play Notification
 */
function decodeNotification(encodedData: string): GooglePlayNotification {
  try {
    const decodedData = atob(encodedData);
    return JSON.parse(decodedData);
  } catch (error) {
    console.error('Fehler beim Dekodieren der Notification:', error);
    throw new Error('Invalid notification data');
  }
}

// Hauptfunktion
serve(async (req: Request) => {
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers, status: 200 });
  }

  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { headers, status: 405 }
    );
  }

  try {
    const body = await req.json();
    console.log('Received Google Play Notification:', JSON.stringify(body, null, 2));

    // Google Play sendet Notifications im Format: { message: { data: "base64-encoded-json" } }
    if (!body.message || !body.message.data) {
      return new Response(
        JSON.stringify({ error: "Invalid notification format" }),
        { headers, status: 400 }
      );
    }

    // Dekodiere die Notification
    const notification = decodeNotification(body.message.data);
    
    // Verarbeite Test-Notifications
    if (notification.testNotification) {
      console.log('Test notification received');
      return new Response(
        JSON.stringify({ success: true, message: "Test notification processed" }),
        { headers, status: 200 }
      );
    }

    // Verarbeite Subscription-Notifications
    if (notification.subscriptionNotification) {
      await handleSubscriptionNotification(notification, notification.subscriptionNotification);
      
      return new Response(
        JSON.stringify({ success: true, message: "Subscription notification processed" }),
        { headers, status: 200 }
      );
    }

    // Unbekannter Notification-Typ
    console.log('Unknown notification type:', notification);
    return new Response(
      JSON.stringify({ success: true, message: "Unknown notification type" }),
      { headers, status: 200 }
    );

  } catch (error) {
    console.error('Fehler beim Verarbeiten der Google Play Notification:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return new Response(
      JSON.stringify({ error: `Processing error: ${errorMessage}` }),
      { headers, status: 500 }
    );
  }
});

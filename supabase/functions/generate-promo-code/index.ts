import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GenerateCodeRequest {
  code_type: 'trial' | 'pro' | 'unlimited';
  duration_days: number;
  usage_limit?: number;
  expires_at?: string;
  custom_code?: string;
  description?: string;
}

interface GenerateCodeResponse {
  success: boolean;
  code?: string;
  message: string;
  promo_code_id?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Erstelle Supabase Client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', // Service Role für Admin-Operationen
    )

    // Hole den aktuellen User
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Prüfe ob User Admin-Rechte hat (vereinfacht - in Produktion würde man Rollen prüfen)
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(authHeader.replace('Bearer ', ''))
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse Request Body
    const requestData: GenerateCodeRequest = await req.json()

    // Validiere Input
    if (!requestData.code_type || !requestData.duration_days) {
      return new Response(
        JSON.stringify({ 
          success: false,
          message: 'code_type und duration_days sind erforderlich'
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generiere Code
    const result = await generatePromoCode(requestData, user.id, supabaseClient)

    return new Response(
      JSON.stringify(result),
      { 
        status: result.success ? 200 : 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in generate-promo-code function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        message: 'Ein unerwarteter Fehler ist aufgetreten.'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function generatePromoCode(
  request: GenerateCodeRequest, 
  createdBy: string, 
  supabaseClient: any
): Promise<GenerateCodeResponse> {
  try {
    // Generiere Code oder verwende custom_code
    let code = request.custom_code
    if (!code) {
      code = await generateUniqueCode(request.code_type, supabaseClient)
    }

    // Prüfe ob Code bereits existiert
    const { data: existingCode } = await supabaseClient
      .from('promo_codes')
      .select('id')
      .eq('code', code)
      .single()

    if (existingCode) {
      return {
        success: false,
        message: `Code "${code}" existiert bereits. Bitte wählen Sie einen anderen Code.`
      }
    }

    // Erstelle Promo Code
    const { data: promoCode, error: createError } = await supabaseClient
      .from('promo_codes')
      .insert({
        code: code,
        code_type: request.code_type,
        duration_days: request.duration_days,
        usage_limit: request.usage_limit || null,
        expires_at: request.expires_at || null,
        created_by: createdBy,
        metadata: {
          description: request.description || `${request.code_type.toUpperCase()} Code - ${request.duration_days} Tage`,
          generated_at: new Date().toISOString(),
          generator: 'admin_panel'
        }
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating promo code:', createError)
      return {
        success: false,
        message: 'Fehler beim Erstellen des Promo Codes'
      }
    }

    return {
      success: true,
      code: code,
      message: `Promo Code "${code}" erfolgreich erstellt!`,
      promo_code_id: promoCode.id
    }

  } catch (error) {
    console.error('Error in generatePromoCode:', error)
    return {
      success: false,
      message: 'Unerwarteter Fehler beim Generieren des Codes'
    }
  }
}

async function generateUniqueCode(codeType: string, supabaseClient: any): Promise<string> {
  const prefixes = {
    trial: 'TRIAL',
    pro: 'PRO',
    unlimited: 'ULTRA'
  }
  
  const prefix = prefixes[codeType as keyof typeof prefixes] || 'CODE'
  
  // Generiere zufälligen Code
  const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase()
  const timestamp = Date.now().toString().slice(-4)
  
  return `${prefix}${randomPart}${timestamp}`
}

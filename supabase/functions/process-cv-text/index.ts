// supabase/functions/process-cv-text/index.ts

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts' // Annahme: CORS Helper existiert
import Anthropic from 'https://esm.sh/@anthropic-ai/sdk@^0.21.3'; // Importiere Anthropic SDK

console.log('Function process-cv-text starting...');

// Funktion zur Interaktion mit Anthropic API
async function extractStructuredData(cvText: string): Promise<object | { error: string }> {
  console.log('extractStructuredData aufgerufen.');
  const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY');
  const modelName = Deno.env.get('ANTHROPIC_MODEL') || 'claude-3-haiku-20240307'; // Standardmodell

  if (!anthropicApiKey) {
    console.error('ANTHROPIC_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: Anthropic API Key fehlt.' };
  }

  const anthropic = new Anthropic({
    apiKey: anthropicApiKey,
  });

  const prompt = `
Analysiere den folgenden Lebenslauftext und extrahiere daraus Fähigkeiten (skills), Berufserfahrung (workExperience) und Ausbildung (education).
Gib das Ergebnis NUR als JSON-Objekt zurück, das dem folgenden Schema entspricht:

{
  "skills": ["Fähigkeit1", "Fähigkeit2", ...],
  "workExperience": [
    {
      "position": "Stellenbezeichnung",
      "company": "Firmenname",
      "startDate": "YYYY-MM-DD", // Versuche, das Datum zu schätzen, wenn nicht genau angegeben
      "endDate": "YYYY-MM-DD", // oder null, wenn noch aktuell
      "description": "Beschreibung der Tätigkeiten"
    },
    ...
  ],
  "education": [
    {
      "institution": "Name der Bildungseinrichtung",
      "degree": "Abschlussbezeichnung",
      "startDate": "YYYY-MM-DD", // Versuche, das Datum zu schätzen
      "endDate": "YYYY-MM-DD", // oder null
      "fieldOfStudy": "Fachrichtung (optional)"
    },
    ...
  ]
}

**Wichtige Hinweise:**
- Wenn Informationen fehlen (z.B. Enddatum), setze den Wert auf null.
- Versuche, Start- und Enddaten im Format YYYY-MM-DD zu schätzen oder zu extrahieren. Wenn nur ein Jahr oder Monat/Jahr angegeben ist, schätze einen sinnvollen Tag (z.B. den 1.).
- Die Beschreibungen sollten prägnant sein.
- Gib NUR das JSON-Objekt zurück, ohne zusätzliche Erklärungen oder Einleitungen.

**Lebenslauftext:**
${cvText}
`;

  console.log(`Sende Anfrage an Anthropic Modell: ${modelName}`);

  try {
    const msg = await anthropic.messages.create({
      model: modelName,
      max_tokens: 4000, // Erhöht, um sicherzustellen, dass das gesamte JSON passt
      messages: [
        { role: 'user', content: prompt },
      ],
    });

    console.log('Antwort von Anthropic erhalten.');

    // Suche nach dem JSON-Block in der Antwort
    let jsonString = null;
    if (msg.content && msg.content.length > 0 && msg.content[0].type === 'text') {
      const textContent = msg.content[0].text;
      const jsonStart = textContent.indexOf('{');
      const jsonEnd = textContent.lastIndexOf('}');
      if (jsonStart !== -1 && jsonEnd !== -1) {
        jsonString = textContent.substring(jsonStart, jsonEnd + 1);
      }
    }

    if (jsonString) {
      console.log('JSON-String aus Anthropic-Antwort extrahiert.');
      try {
        const structuredData = JSON.parse(jsonString);
        console.log('JSON erfolgreich geparsed.');
        // TODO: Hier könnte man noch eine Validierung gegen das erwartete Schema einfügen
        return structuredData;
      } catch (parseError) {
        console.error('Fehler beim Parsen des JSON aus der Anthropic-Antwort:', parseError);
        console.error('Empfangener JSON-String:', jsonString); // Logge den String, der Probleme macht
        return { error: `Fehler beim Verarbeiten der KI-Antwort (JSON ungültig): ${parseError.message}` };
      }
     } else {
      console.warn('Kein gültiger JSON-Block in der Anthropic-Antwort gefunden.');
      console.log('Komplette Anthropic-Antwort:', JSON.stringify(msg, null, 2));
      return { error: 'Die KI hat keine gültigen strukturierten Daten zurückgegeben.' };
     }
  } catch (error) {
    console.error('Fehler bei der Anfrage an die Anthropic API:', error);
    return { error: `Fehler bei der Kommunikation mit der KI: ${error.message}` };
}
}

serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }

  let cvText = null;
  try {
    const body = await req.json();
    cvText = body.cvText;
    console.log('[Serve] Request Body geparsed.');
  } catch (parseError) {
    console.error('[Serve] Fehler beim Parsen des JSON Request Body:', parseError);
    return new Response(JSON.stringify({ error: `Ungültiges JSON-Format: ${parseError.message}` }), {
         headers: { ...corsHeaders, 'Content-Type': 'application/json' },
         status: 400,
       });
    }

    if (!cvText || typeof cvText !== 'string' || cvText.trim().length === 0) {
    console.error(`[Serve] Ungültiger oder leerer cvText Parameter: ${cvText}`);
    return new Response(JSON.stringify({ error: 'Bitte gültigen, nicht-leeren cvText angeben' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

  console.log(`[Serve] Rufe extractStructuredData für Textlänge ${cvText.length} auf.`);
  const result = await extractStructuredData(cvText);

  // Prüfen, ob ein Fehlerobjekt zurückgegeben wurde
  if (typeof result === 'object' && result !== null && 'error' in result) {
      console.error(`[Serve] Fehler von extractStructuredData erhalten: ${result.error}`);
      return new Response(JSON.stringify({ error: result.error }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500, // Interner Serverfehler oder Konfigurationsfehler
      });
  }

  console.log('[Serve] Erfolgreiche Verarbeitung. Sende Ergebnis.');
  // Stelle sicher, dass wir ein Objekt zurückgeben, auch wenn das Ergebnis leer ist
  const responseData = typeof result === 'object' && result !== null ? result : {};

  // Konvertiere die Schlüsselnamen für die Kompatibilität mit der App
  const formattedResponse = {
    extractedSkills: responseData.skills || [],
    extractedExperience: responseData.workExperience || [],
    extractedEducation: responseData.education || [],
    extractedSummary: responseData.summary || null
  };

  console.log('[Serve] Formatierte Antwort:', JSON.stringify(formattedResponse, null, 2));

  return new Response(JSON.stringify(formattedResponse), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
  });
});

/*
// Example Usage from Flutter (using Supabase client):
try {
  final response = await supabase.functions.invoke(
    'process-cv-text',
    body: {'cvText': extractedPdfText}, // Send text in the body
  );

  if (response.error != null) {
    print('Supabase function error: ${response.error!.message}');
    // Handle error
  } else {
    print('Supabase function success: ${response.data}');
    // Process the structured data (response.data)
    final structuredData = response.data as Map<String, dynamic>;
    // Update profile state...
  }
} catch (e) {
  print('Error invoking Supabase function: $e');
  // Handle error
}
*/
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PromoCodeRequest {
  promo_code: string;
}

interface PromoCodeValidation {
  isValid: boolean;
  message: string;
  benefits?: {
    trial_days: number;
    plan_type: string;
  };
  promoCodeId?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Erstelle Supabase Client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Hole den aktuellen User
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse Request Body
    const { promo_code }: PromoCodeRequest = await req.json()

    if (!promo_code || promo_code.trim().length === 0) {
      return new Response(
        JSON.stringify({ 
          error: 'Promo code is required',
          isValid: false,
          message: 'Bitte geben Sie einen gültigen Promo Code ein.'
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Validiere Promo Code
    const validation = await validatePromoCode(promo_code.trim().toUpperCase(), user.id, supabaseClient)

    if (!validation.isValid) {
      return new Response(
        JSON.stringify(validation),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Promo Code ist gültig - erstelle Einlösung
    const redemptionResult = await createRedemption(promo_code.trim().toUpperCase(), user.id, validation.benefits!, validation.promoCodeId!, supabaseClient)

    if (!redemptionResult.success) {
      return new Response(
        JSON.stringify({ 
          error: redemptionResult.error,
          isValid: false,
          message: 'Fehler beim Einlösen des Promo Codes.'
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    return new Response(
      JSON.stringify({
        success: true,
        isValid: true,
        message: `Promo Code erfolgreich eingelöst! Sie erhalten ${validation.benefits!.trial_days} Tage kostenlosen Premium-Zugang.`,
        redemption: redemptionResult.redemption
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in validate-promo-code-v2 function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        isValid: false,
        message: 'Ein unerwarteter Fehler ist aufgetreten.'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function validatePromoCode(promoCode: string, userId: string, supabaseClient: any): Promise<PromoCodeValidation> {
  // Prüfe ob User diesen Promo Code bereits eingelöst hat
  const { data: existingRedemption } = await supabaseClient
    .from('promo_code_redemptions')
    .select('*')
    .eq('user_id', userId)
    .eq('promo_code', promoCode)
    .single()

  if (existingRedemption) {
    return {
      isValid: false,
      message: 'Dieser Promo Code wurde bereits von Ihnen eingelöst.'
    }
  }

  // Prüfe Code in der neuen promo_codes Tabelle
  const { data: promoCodeData, error: codeError } = await supabaseClient
    .from('promo_codes')
    .select('*')
    .eq('code', promoCode)
    .eq('is_active', true)
    .single()

  if (codeError || !promoCodeData) {
    return {
      isValid: false,
      message: 'Ungültiger Promo Code. Bitte überprüfen Sie die Eingabe.'
    }
  }

  // Prüfe ob Code abgelaufen ist
  if (promoCodeData.expires_at && new Date(promoCodeData.expires_at) < new Date()) {
    return {
      isValid: false,
      message: 'Dieser Promo Code ist abgelaufen.'
    }
  }

  // Prüfe Usage Limit
  if (promoCodeData.usage_limit && promoCodeData.usage_count >= promoCodeData.usage_limit) {
    return {
      isValid: false,
      message: 'Dieser Promo Code wurde bereits zu oft verwendet.'
    }
  }

  return {
    isValid: true,
    message: 'Promo Code ist gültig!',
    benefits: {
      trial_days: promoCodeData.duration_days,
      plan_type: promoCodeData.code_type === 'trial' ? 'pro' : promoCodeData.code_type
    },
    promoCodeId: promoCodeData.id
  }
}

async function createRedemption(promoCode: string, userId: string, benefits: { trial_days: number; plan_type: string }, promoCodeId: string, supabaseClient: any) {
  try {
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + benefits.trial_days)

    // Erstelle Promo Code Einlösung
    const { data: redemption, error: redemptionError } = await supabaseClient
      .from('promo_code_redemptions')
      .insert({
        user_id: userId,
        promo_code: promoCode,
        promo_code_id: promoCodeId,
        code_type: benefits.plan_type,
        duration_days: benefits.trial_days,
        expires_at: expiresAt.toISOString(),
        status: 'active'
      })
      .select()
      .single()

    if (redemptionError) {
      console.error('Error creating redemption:', redemptionError)
      return { success: false, error: 'Fehler beim Speichern der Einlösung' }
    }

    // Aktualisiere Usage Count
    await supabaseClient
      .from('promo_codes')
      .update({ usage_count: supabaseClient.rpc('increment_usage_count', { promo_code_id: promoCodeId }) })
      .eq('id', promoCodeId)

    // Aktualisiere Subscription (vereinfacht - sollte in separater Funktion sein)
    const { data: existingSubscription } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()

    if (existingSubscription) {
      // Aktualisiere bestehendes Abonnement
      await supabaseClient
        .from('subscriptions')
        .update({
          plan_type: benefits.plan_type,
          expires_at: expiresAt.toISOString(),
          subscription_id: `promo_${redemption.id}`,
          product_id: `${benefits.plan_type}_subscription`,
          platform: 'promo_code',
          is_premium: true,
          end_date: expiresAt.toISOString(),
          start_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSubscription.id)
    } else {
      // Erstelle neues Abonnement
      await supabaseClient
        .from('subscriptions')
        .insert({
          user_id: userId,
          status: 'active',
          subscription_id: `promo_${redemption.id}`,
          product_id: `${benefits.plan_type}_subscription`,
          platform: 'promo_code',
          plan_type: benefits.plan_type,
          is_premium: true,
          expires_at: expiresAt.toISOString(),
          end_date: expiresAt.toISOString(),
          start_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
    }

    return { success: true, redemption }
  } catch (error) {
    console.error('Error in createRedemption:', error)
    return { success: false, error: 'Unerwarteter Fehler' }
  }
}

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Globale Queue für alle Mistral Requests
interface QueuedRequest {
  id: string;
  userId: string;
  isPremium: boolean;
  requestData: {
    model: string;
    messages: Array<{ role: string; content: string }>;
    max_tokens: number;
    temperature: number;
  };
  timestamp: number;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

class MistralQueueManager {
  private queue: QueuedRequest[] = [];
  private processing = false;
  private lastRequestTime = 0;
  private readonly REQUEST_INTERVAL = 1000; // 1 Sekunde zwischen Requests

  constructor(private mistralApiKey: string) {}

  // Füge Request zur Queue hinzu
  async enqueueRequest(
    userId: string,
    isPremium: boolean,
    requestData: any
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const queuedRequest: QueuedRequest = {
        id: crypto.randomUUID(),
        userId,
        isPremium,
        requestData,
        timestamp: Date.now(),
        resolve,
        reject,
      };

      // Premium Users bekommen Priorität
      if (isPremium) {
        // Füge Premium Request vor dem ersten Free User ein
        const firstFreeIndex = this.queue.findIndex(req => !req.isPremium);
        if (firstFreeIndex === -1) {
          this.queue.push(queuedRequest);
        } else {
          this.queue.splice(firstFreeIndex, 0, queuedRequest);
        }
      } else {
        // Free Users kommen ans Ende
        this.queue.push(queuedRequest);
      }

      console.log(`Request ${queuedRequest.id} added to queue. Queue length: ${this.queue.length}, Premium: ${isPremium}`);
      
      // Starte Processing falls nicht bereits aktiv
      if (!this.processing) {
        this.processQueue();
      }
    });
  }

  // Verarbeite Queue sequenziell
  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;
    console.log('Starting queue processing...');

    while (this.queue.length > 0) {
      const request = this.queue.shift()!;
      
      try {
        // Warte mindestens 1 Sekunde seit letztem Request
        const timeSinceLastRequest = Date.now() - this.lastRequestTime;
        if (timeSinceLastRequest < this.REQUEST_INTERVAL) {
          const waitTime = this.REQUEST_INTERVAL - timeSinceLastRequest;
          console.log(`Waiting ${waitTime}ms before next request...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        console.log(`Processing request ${request.id} for user ${request.userId} (Premium: ${request.isPremium})`);
        
        // Führe Mistral API Request aus
        const result = await this.executeMistralRequest(request.requestData);
        this.lastRequestTime = Date.now();
        
        // Resolve Promise
        request.resolve(result);
        
      } catch (error) {
        console.error(`Error processing request ${request.id}:`, error);
        request.reject(error);
      }
    }

    this.processing = false;
    console.log('Queue processing completed');
  }

  // Führe einzelnen Mistral API Request aus
  private async executeMistralRequest(requestData: any): Promise<any> {
    const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.mistralApiKey}`,
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Mistral API Error ${response.status}: ${errorText}`);
    }

    return await response.json();
  }

  // Queue Status für Debugging
  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      premiumCount: this.queue.filter(req => req.isPremium).length,
      freeCount: this.queue.filter(req => !req.isPremium).length,
    };
  }
}

// Globale Queue Manager Instanz
let queueManager: MistralQueueManager | null = null;

Deno.serve(async (req) => {
  // CORS Headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialisiere Queue Manager falls noch nicht vorhanden
    if (!queueManager) {
      const mistralApiKey = Deno.env.get('MISTRAL_API_KEY');
      if (!mistralApiKey) {
        throw new Error('MISTRAL_API_KEY environment variable is required');
      }
      queueManager = new MistralQueueManager(mistralApiKey);
    }

    const { userId, isPremium, requestData, action } = await req.json();

    // Queue Status abfragen (für Debugging)
    if (action === 'status') {
      return new Response(
        JSON.stringify(queueManager.getQueueStatus()),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Request zur Queue hinzufügen
    if (!userId || requestData === undefined) {
      throw new Error('userId and requestData are required');
    }

    console.log(`Enqueueing request for user ${userId}, Premium: ${isPremium || false}`);
    
    const result = await queueManager.enqueueRequest(
      userId,
      isPremium || false,
      requestData
    );

    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Mistral Queue Manager Error:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: 'Failed to process Mistral request through queue'
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

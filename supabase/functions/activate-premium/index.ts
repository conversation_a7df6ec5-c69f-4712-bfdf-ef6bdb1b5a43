// Supabase Edge Function zum manuellen Aktivieren von Premium-Accounts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // CORS-Präflug-Anfragen behandeln
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    // Logge die Anfrage für Debugging-Zwecke
    const requestText = await req.clone().text();
    console.log("Anfrage erhalten:", requestText);

    const { user_id, duration_months = 12, plan_type = "unlimited", admin_key } = await req.json();

    // Logge die extrahierten Daten
    console.log("Extrahierte Daten:", { user_id, duration_months, plan_type });

    // Einfache Sicherheitsüberprüfung mit einem Admin-Schlüssel
    // In einer Produktionsumgebung solltest du eine bessere Authentifizierung verwenden
    const expectedAdminKey = Deno.env.get("ADMIN_ACTIVATION_KEY");
    console.log("Admin-Schlüssel erwartet:", expectedAdminKey ? "Ja" : "Nein");

    if (!expectedAdminKey || admin_key !== expectedAdminKey) {
      console.log("Authentifizierung fehlgeschlagen");
      return new Response(
        JSON.stringify({ error: "Nicht autorisiert" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 401 }
      );
    }

    console.log("Authentifizierung erfolgreich");

    if (!user_id) {
      console.log("Keine Benutzer-ID angegeben");
      return new Response(
        JSON.stringify({ error: "Benutzer-ID ist erforderlich" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }

    console.log("Benutzer-ID gefunden:", user_id);

    // Validiere den Plan-Typ
    if (!["basic", "pro", "unlimited"].includes(plan_type)) {
      console.log("Ungültiger Plan-Typ:", plan_type);
      return new Response(
        JSON.stringify({ error: "Ungültiger Plan-Typ. Erlaubt sind: basic, pro, unlimited" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }

    console.log("Plan-Typ gültig:", plan_type);

    // Supabase-Client initialisieren
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Aktuelles Datum und Enddatum berechnen
    const now = new Date();
    const endDate = new Date(now);
    endDate.setMonth(now.getMonth() + duration_months);

    // Prüfen, ob bereits ein Abonnement existiert
    const { data: existingSub, error: fetchError } = await supabaseClient
      .from("subscriptions")
      .select("*")
      .eq("user_id", user_id)
      .eq("status", "active")
      .maybeSingle();

    let subscription;

    console.log("Initialisiere Supabase-Client mit URL:", Deno.env.get("SUPABASE_URL"));

    // Bestimme die Anzahl der verbleibenden Bewerbungen basierend auf dem Plan
    let remainingApplications = null;
    if (plan_type === 'basic') {
      remainingApplications = 30;
    } else if (plan_type === 'pro') {
      remainingApplications = 150;
    }
    // Bei 'unlimited' bleibt remainingApplications null
    console.log("Verbleibende Bewerbungen:", remainingApplications);

    console.log("Suche nach existierendem Abonnement für Benutzer:", user_id);

    try {
      if (existingSub) {
        console.log("Existierendes Abonnement gefunden:", existingSub.id);

        // Bestehendes Abonnement aktualisieren
        console.log("Aktualisiere Abonnement...");
        const updateData = {
          is_premium: true,
          status: "active",
          plan_type: plan_type,
          end_date: endDate.toISOString(),
          updated_at: now.toISOString(),
          remaining_applications: remainingApplications,
          total_applications: remainingApplications
        };
        console.log("Update-Daten:", updateData);

        const { data: updatedSub, error: updateError } = await supabaseClient
          .from("subscriptions")
          .update(updateData)
          .eq("id", existingSub.id)
          .select()
          .single();

        if (updateError) {
          console.error("Fehler beim Aktualisieren des Abonnements:", updateError);
          throw updateError;
        }

        subscription = updatedSub;
        console.log("Bestehendes Abonnement aktualisiert:", subscription.id);
      } else {
        console.log("Kein existierendes Abonnement gefunden, erstelle neues Abonnement...");

        // Neues Abonnement erstellen
        const insertData = {
          user_id,
          subscription_id: "manual_activation",
          subscription_type: duration_months >= 12 ? "yearly" : "monthly",
          plan_type: plan_type,
          payment_provider: "manual",
          transaction_id: "manual_transaction",
          is_premium: true,
          auto_renew: false,
          status: "active",
          start_date: now.toISOString(),
          end_date: endDate.toISOString(),
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
          remaining_applications: remainingApplications,
          total_applications: remainingApplications
        };
        console.log("Insert-Daten:", insertData);

        const { data: newSub, error: insertError } = await supabaseClient
          .from("subscriptions")
          .insert(insertData)
          .select()
          .single();

        if (insertError) {
          console.error("Fehler beim Erstellen des Abonnements:", insertError);
          throw insertError;
        }

        subscription = newSub;
        console.log("Neues Abonnement erstellt:", subscription ? subscription.id : "Keine ID zurückgegeben");
      }
    } catch (dbError) {
      console.error("Datenbank-Fehler:", dbError);
      throw new Error(`Datenbank-Fehler: ${dbError.message || JSON.stringify(dbError)}`);
    }

    // Protokollieren der Aktivierung
    try {
      await supabaseClient
        .from("admin_logs")
        .insert({
          action: "premium_activation",
          user_id,
          details: {
            subscription_id: subscription.id,
            plan_type,
            duration_months,
            remaining_applications: remainingApplications,
            activated_at: now.toISOString(),
            expires_at: endDate.toISOString()
          }
        });
      console.log("Aktivierung protokolliert");
    } catch (error) {
      // Wenn die Tabelle nicht existiert, erstelle sie
      if (error.message.includes("relation") && error.message.includes("does not exist")) {
        try {
          await supabaseClient.rpc("create_admin_logs_table");
          console.log("Admin-Logs-Tabelle erstellt");

          // Versuche erneut zu protokollieren
          await supabaseClient
            .from("admin_logs")
            .insert({
              action: "premium_activation",
              user_id,
              details: {
                subscription_id: subscription.id,
                plan_type,
                duration_months,
                remaining_applications: remainingApplications,
                activated_at: now.toISOString(),
                expires_at: endDate.toISOString()
              }
            });
          console.log("Aktivierung protokolliert (nach Tabellenerstellung)");
        } catch (createError) {
          console.error("Fehler beim Erstellen der Admin-Logs-Tabelle:", createError);
        }
      } else {
        console.error("Fehler beim Protokollieren:", error);
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "Premium-Status erfolgreich aktiviert",
        subscription
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Fehler bei der Premium-Aktivierung:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});

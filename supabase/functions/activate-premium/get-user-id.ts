// Supabase Edge Function zum Abrufen der Benutzer-ID anhand der E-Mail-Adresse
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // CORS-Präflug-Anfragen behandeln
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders, status: 204 });
  }

  try {
    const { email, admin_key } = await req.json();
    
    // Einfache Sicherheitsüberprüfung mit einem Admin-Schlüssel
    const expectedAdminKey = Deno.env.get("ADMIN_ACTIVATION_KEY");
    if (!expectedAdminKey || admin_key !== expectedAdminKey) {
      return new Response(
        JSON.stringify({ error: "Nicht autorisiert" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 401 }
      );
    }
    
    if (!email) {
      return new Response(
        JSON.stringify({ error: "E-Mail-Adresse ist erforderlich" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Supabase-Client initialisieren
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Benutzer anhand der E-Mail-Adresse suchen
    const { data: users, error } = await supabaseClient.auth.admin.listUsers({
      filter: {
        email: email
      }
    });

    if (error) {
      throw error;
    }

    if (!users || users.users.length === 0) {
      return new Response(
        JSON.stringify({ error: `Kein Benutzer mit der E-Mail-Adresse ${email} gefunden` }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 404 }
      );
    }

    // Benutzer-ID zurückgeben
    return new Response(
      JSON.stringify({
        user_id: users.users[0].id,
        email: users.users[0].email
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Fehler beim Abrufen der Benutzer-ID:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});

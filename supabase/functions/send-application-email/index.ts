// supabase/functions/send-application-email/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.23.0'
import { SendGridClient } from './sendgrid.ts'

interface ApplicationEmailRequest {
  userId: string
  recipientEmail: string
  jobTitle: string
  coverLetter: string
  cvFileId?: string // Supabase Storage file ID für den Lebenslauf
  companyName?: string
  jobId?: string // Optional: ID des Jobs für Tracking
}

interface ApplicationRecord {
  id?: string
  user_id: string
  job_title: string
  company_name?: string
  recipient_email: string
  status: string
  sent_at: string
  job_id?: string
  cover_letter_text: string
}

serve(async (req) => {
  // CORS-Handling für Preflight-Anfragen
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 1. Extrahiere Daten aus der Anfrage
    const payload: ApplicationEmailRequest = await req.json()
    const { userId, recipientEmail, jobTitle, coverLetter, cvFileId, companyName, jobId } = payload

    // Validiere Eingabedaten
    if (!userId || !recipientEmail || !jobTitle || !coverLetter) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Fehlende Pflichtfelder: userId, recipientEmail, jobTitle, coverLetter'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      )
    }

    // 2. Initialisiere Supabase-Client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Supabase Konfiguration fehlt')
      return new Response(
        JSON.stringify({ success: false, error: 'Server-Konfigurationsfehler' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // 3. Überprüfe den Benutzer und seine Berechtigungen
    console.log('Suche Benutzer mit ID:', userId)

    // Versuche zuerst, den Benutzer in der auth.users Tabelle zu finden
    const { data: authUser, error: authError } = await supabase
      .auth
      .admin
      .getUserById(userId)

    if (authError) {
      console.error('Fehler beim Abrufen des Auth-Benutzers:', authError)
    } else {
      console.log('Auth-Benutzer gefunden:', authUser.user.email)
    }

    // Versuche dann, das Profil in der profiles Tabelle zu finden
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    // Überprüfe das Abonnement des Benutzers
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    // Wenn der Benutzer ein Free-Abonnement hat, darf er die Direkt-Bewerben-Funktion nicht nutzen
    if (subscriptionData && subscriptionData.plan_type && subscriptionData.plan_type.toLowerCase() === 'free') {
      console.error('Benutzer hat ein Free-Abonnement und darf die Direkt-Bewerben-Funktion nicht nutzen')
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Diese Funktion ist nur für Pro- und Unlimited-Abonnenten verfügbar. Bitte upgrade dein Abonnement.',
          premiumRequired: true
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403
        }
      )
    }

    console.log('Profildaten:', userData)
    console.log('Profilfehler:', userError)

    // Versuche auch, den Benutzer in der auth.users Tabelle direkt abzufragen
    const { data: authData, error: authQueryError } = await supabase
      .from('auth.users')
      .select('email, id')
      .eq('id', userId)
      .single()

    console.log('Auth Daten direkt aus Tabelle:', authData)
    console.log('Auth Fehler direkt aus Tabelle:', authQueryError)

    // Versuche auch, den Benutzer in der users Tabelle zu finden (falls vorhanden)
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select('email, id')
      .eq('id', userId)
      .single()

    console.log('Users Tabelle Daten:', usersData)
    console.log('Users Tabelle Fehler:', usersError)

    // Setze die E-Mail-Adresse des Absenders
    // Priorität: 1. Auth-Benutzer, 2. Profil, 3. users Tabelle, 4. Fallback
    let userEmail = null
    let premiumStatus = null

    // 1. Versuche, die E-Mail aus dem Auth-Benutzer zu bekommen
    if (authUser && authUser.user && authUser.user.email) {
      userEmail = authUser.user.email
      console.log('Verwende E-Mail aus Auth-Benutzer:', userEmail)
    }
    // 2. Versuche, die E-Mail aus dem Profil zu bekommen
    else if (userData && userData.email) {
      userEmail = userData.email
      premiumStatus = userData.premium_status
      console.log('Verwende E-Mail aus Profil:', userEmail)
    }
    // 3. Versuche, die E-Mail aus der users Tabelle zu bekommen
    else if (usersData && usersData.email) {
      userEmail = usersData.email
      console.log('Verwende E-Mail aus users Tabelle:', userEmail)
    }
    // 4. Versuche, die E-Mail aus der auth.users Tabelle direkt zu bekommen
    else if (authData && authData.email) {
      userEmail = authData.email
      console.log('Verwende E-Mail aus auth.users Tabelle:', userEmail)
    }
    // 5. Wenn keine E-Mail gefunden wurde, verwende die E-Mail aus der Anfrage
    else if (recipientEmail) {
      // Als letzten Ausweg verwenden wir die E-Mail des Empfängers als Absender
      // Dies ist nicht ideal, aber besser als gar keine E-Mail
      userEmail = recipientEmail
      console.log('Verwende E-Mail des Empfängers als Fallback:', userEmail)
    }
    // 6. Wenn immer noch keine E-Mail gefunden wurde, gib einen Fehler zurück
    else {
      console.error('Keine E-Mail-Adresse für den Benutzer gefunden')
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Benutzer hat keine E-Mail-Adresse',
          userId: userId,
          authError: authError ? authError.message : null,
          userError: userError ? userError.message : null
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      )
    }

    // 4. Lade den Lebenslauf aus dem Storage, falls vorhanden
    let cvFileBuffer: ArrayBuffer | null = null
    let cvFileName = 'lebenslauf.pdf'

    if (cvFileId) {
      try {
        const { data: fileData, error: fileError } = await supabase
          .storage
          .from('cvs')
          .download(`${userId}/${cvFileId}`)

        if (fileError) {
          console.error('Fehler beim Herunterladen des Lebenslaufs:', fileError)
        } else if (fileData) {
          cvFileBuffer = await fileData.arrayBuffer()
          // *** REPARATUR: Verwende standardisierten Namen "Lebenslauf.pdf" ***
          cvFileName = 'Lebenslauf.pdf'
        }
      } catch (e) {
        console.error('Fehler beim Verarbeiten des Lebenslaufs:', e)
      }
    }

    // 5. Sende die E-Mail mit SendGrid
    const sendGridApiKey = Deno.env.get('SENDGRID_API_KEY')
    if (!sendGridApiKey) {
      console.error('SendGrid API-Schlüssel fehlt')
      return new Response(
        JSON.stringify({ success: false, error: 'E-Mail-Dienst nicht konfiguriert' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    const sendGrid = new SendGridClient(sendGridApiKey)

    // Überprüfe, ob eine Benutzer-E-Mail-Adresse verfügbar ist
    if (!userEmail) {
      console.error('Benutzer hat keine E-Mail-Adresse')
      return new Response(
        JSON.stringify({ success: false, error: 'Benutzer hat keine E-Mail-Adresse' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      )
    }

    // Fallback-Absender für den Fall, dass die Benutzer-E-Mail nicht akzeptiert wird
    const fallbackSenderEmail = Deno.env.get('SENDER_EMAIL') || '<EMAIL>'
    const fallbackSenderName = Deno.env.get('SENDER_NAME') || 'JobAssistent App'

    // Benutzer-Name aus dem Profil extrahieren, falls vorhanden
    const userName = userData.display_name || userData.full_name || userEmail.split('@')[0]

    // Erstelle E-Mail-Inhalt
    const emailSubject = `Bewerbung für: ${jobTitle}`

    // Bereite Anhänge vor
    const attachments = []
    let totalAttachmentSize = 0
    const maxEmailSize = 25 * 1024 * 1024 // 25MB Limit

    // CV-Anhang hinzufügen
    if (cvFileBuffer) {
      const cvSize = cvFileBuffer.byteLength
      totalAttachmentSize += cvSize

      attachments.push({
        content: btoa(String.fromCharCode(...new Uint8Array(cvFileBuffer))), // Base64-Kodierung
        filename: cvFileName,
        type: 'application/pdf',
        disposition: 'attachment'
      })
    }

    // Zusätzliche Dokumente laden und hinzufügen
    try {
      const { data: additionalDocs, error: docsError } = await supabase
        .from('user_additional_documents')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active_for_applications', true)
        .order('upload_date', { ascending: false })

      if (docsError) {
        console.warn('Warnung beim Laden zusätzlicher Dokumente:', docsError)
      } else if (additionalDocs && additionalDocs.length > 0) {
        console.log(`Lade ${additionalDocs.length} zusätzliche Dokumente für Bewerbung`)

        for (const doc of additionalDocs) {
          try {
            // Dokument aus Storage laden
            const { data: docBuffer, error: downloadError } = await supabase.storage
              .from('additional-documents')
              .download(doc.file_path)

            if (downloadError) {
              console.warn(`Fehler beim Laden von Dokument ${doc.file_name}:`, downloadError)
              continue
            }

            const docBytes = await docBuffer.arrayBuffer()
            const docSize = docBytes.byteLength

            // Prüfe Größenbeschränkung
            if (totalAttachmentSize + docSize > maxEmailSize) {
              console.warn(`Dokument ${doc.file_name} übersteigt E-Mail-Größenlimit, wird übersprungen`)
              continue
            }

            totalAttachmentSize += docSize

            // Dokument zu Anhängen hinzufügen
            attachments.push({
              content: btoa(String.fromCharCode(...new Uint8Array(docBytes))),
              filename: doc.file_name,
              type: doc.file_type,
              disposition: 'attachment'
            })

            console.log(`Zusätzliches Dokument hinzugefügt: ${doc.file_name} (${(docSize / 1024 / 1024).toFixed(2)} MB)`)
          } catch (docError) {
            console.warn(`Fehler beim Verarbeiten von Dokument ${doc.file_name}:`, docError)
          }
        }
      }
    } catch (error) {
      console.warn('Fehler beim Laden zusätzlicher Dokumente:', error)
    }

    // Erstelle HTML-Version des Anschreibens mit besserer Formatierung
    const htmlCoverLetter = coverLetter
      .split('\n\n')
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('');

    // Erstelle eine gut formatierte HTML-E-Mail
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 650px; margin: 0 auto; }
        .header { margin-bottom: 20px; }
        .footer { margin-top: 30px; color: #666; font-size: 0.9em; border-top: 1px solid #eee; padding-top: 15px; }
        .company { font-weight: bold; }
        .job-title { font-weight: bold; }
        p { margin-bottom: 16px; }
      </style>
    </head>
    <body>
      <div class="header">
        <p>Bewerbung für die Stelle: <span class="job-title">${jobTitle}</span>${companyName ? ` bei <span class="company">${companyName}</span>` : ''}</p>
      </div>

      <div class="content">
        ${htmlCoverLetter}
      </div>

      <div class="footer">
        <p>
          Diese E-Mail wurde über JobAssistent gesendet.<br>
          ${attachments.length > 0 ? `${attachments.length} Dokument${attachments.length === 1 ? '' : 'e'} ${attachments.length === 1 ? 'ist' : 'sind'} als Anhang beigefügt.` : 'Keine Anhänge.'}
        </p>
      </div>
    </body>
    </html>
    `;

    // Erstelle eine Textversion als Fallback für E-Mail-Clients, die kein HTML unterstützen
    const textContent = `
Bewerbung für die Stelle: ${jobTitle}${companyName ? ` bei ${companyName}` : ''}

${coverLetter}

---
Diese E-Mail wurde über JobAssistent gesendet.
${attachments.length > 0 ? `${attachments.length} Dokument${attachments.length === 1 ? '' : 'e'} ${attachments.length === 1 ? 'ist' : 'sind'} als Anhang beigefügt.` : 'Keine Anhänge.'}
    `;

    // Sende die E-Mail tatsächlich über SendGrid
    console.log('Sende E-Mail über SendGrid...');

    const emailResult = await sendGrid.send({
      to: recipientEmail,
      from: {
        email: fallbackSenderEmail,
        name: userName || fallbackSenderName
      },
      subject: emailSubject,
      text: textContent,
      html: htmlContent,
      replyTo: userEmail, // Setze die Benutzer-E-Mail als Reply-To
      attachments: attachments
    });

    if (!emailResult.success) {
      console.error('Fehler beim Senden der E-Mail:', emailResult.error);
      throw new Error(`Fehler beim Senden der E-Mail: ${emailResult.error}`);
    }

    console.log('E-Mail erfolgreich gesendet:');
    console.log('- Von:', fallbackSenderEmail);
    console.log('- Reply-To:', userEmail);
    console.log('- An:', recipientEmail);
    console.log('- Betreff:', emailSubject);
    console.log('- Anhänge:', attachments.length);
    console.log('- Gesamtgröße der Anhänge:', (totalAttachmentSize / 1024 / 1024).toFixed(2), 'MB');

    // Keine Fehlerbehandlung nötig, da wir einen Erfolg simulieren

    // 6. Speichere die Bewerbung in der Datenbank für Tracking
    const applicationRecord: ApplicationRecord = {
      user_id: userId,
      job_title: jobTitle,
      company_name: companyName || 'Unbekannt',
      recipient_email: recipientEmail,
      status: 'gesendet',
      sent_at: new Date().toISOString(),
      job_id: jobId,
      cover_letter_text: coverLetter
    }

    const { data: applicationData, error: applicationError } = await supabase
      .from('applications')
      .insert(applicationRecord)
      .select()
      .single()

    if (applicationError) {
      console.error('Fehler beim Speichern der Bewerbung:', applicationError)
      // Wir geben trotzdem eine Erfolgsantwort zurück, da die E-Mail gesendet wurde
    }

    // 7. Erfolgsantwort
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Bewerbung erfolgreich gesendet',
        applicationId: applicationData?.id || null,
        emailId: emailResult.messageId || null
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Unerwarteter Fehler:', error)
    return new Response(
      JSON.stringify({ success: false, error: `Unerwarteter Fehler: ${error.message}` }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

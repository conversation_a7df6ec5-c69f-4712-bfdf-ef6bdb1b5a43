// supabase/functions/send-application-email/sendgrid.ts

interface SendGridAttachment {
  content: string; // Base64-kodierter Inhalt
  filename: string;
  type: string;
  disposition: string;
}

interface SendGridPersonalization {
  to: Array<{ email: string; name?: string }>;
  subject?: string;
  cc?: Array<{ email: string; name?: string }>;
  bcc?: Array<{ email: string; name?: string }>;
}

interface SendGridEmailOptions {
  to: string | { email: string; name?: string };
  from: { email: string; name?: string };
  subject: string;
  text?: string;
  html?: string;
  replyTo?: string;
  attachments?: SendGridAttachment[];
  cc?: string | { email: string; name?: string } | Array<{ email: string; name?: string }>;
  bcc?: string | { email: string; name?: string } | Array<{ email: string; name?: string }>;
}

interface SendGridResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export class SendGridClient {
  private apiKey: string;
  private baseUrl = 'https://api.sendgrid.com/v3';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Sendet eine E-Mail über die SendGrid API
   */
  async send(options: SendGridEmailOptions): Promise<SendGridResponse> {
    try {
      // Formatiere die Empfänger
      const to = typeof options.to === 'string' 
        ? [{ email: options.to }] 
        : [options.to];

      // Erstelle die Personalisierung
      const personalization: SendGridPersonalization = { to };
      if (options.subject) {
        personalization.subject = options.subject;
      }

      // Füge CC und BCC hinzu, falls vorhanden
      if (options.cc) {
        if (typeof options.cc === 'string') {
          personalization.cc = [{ email: options.cc }];
        } else if ('email' in options.cc) {
          personalization.cc = [options.cc as { email: string; name?: string }];
        } else {
          personalization.cc = options.cc as Array<{ email: string; name?: string }>;
        }
      }

      if (options.bcc) {
        if (typeof options.bcc === 'string') {
          personalization.bcc = [{ email: options.bcc }];
        } else if ('email' in options.bcc) {
          personalization.bcc = [options.bcc as { email: string; name?: string }];
        } else {
          personalization.bcc = options.bcc as Array<{ email: string; name?: string }>;
        }
      }

      // Erstelle den Anfragekörper
      const payload: any = {
        personalizations: [personalization],
        from: options.from,
        subject: options.subject,
        content: []
      };

      // Füge Text- und HTML-Inhalte hinzu
      if (options.text) {
        payload.content.push({
          type: 'text/plain',
          value: options.text
        });
      }

      if (options.html) {
        payload.content.push({
          type: 'text/html',
          value: options.html
        });
      }

      // Füge Reply-To hinzu, falls vorhanden
      if (options.replyTo) {
        payload.reply_to = {
          email: options.replyTo
        };
      }

      // Füge Anhänge hinzu, falls vorhanden
      if (options.attachments && options.attachments.length > 0) {
        payload.attachments = options.attachments;
      }

      // Sende die Anfrage an SendGrid
      const response = await fetch(`${this.baseUrl}/mail/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      // Verarbeite die Antwort
      if (response.ok) {
        // SendGrid gibt bei Erfolg einen leeren 202-Response zurück
        return {
          success: true,
          messageId: response.headers.get('X-Message-Id') || undefined
        };
      } else {
        // Bei Fehler versuchen wir, die Fehlermeldung zu extrahieren
        let errorMessage = `SendGrid API Fehler: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = `SendGrid API Fehler: ${JSON.stringify(errorData)}`;
        } catch (e) {
          // Ignoriere Fehler beim Parsen der Antwort
        }
        
        console.error(errorMessage);
        return {
          success: false,
          error: errorMessage
        };
      }
    } catch (error) {
      console.error('Fehler beim Senden der E-Mail:', error);
      return {
        success: false,
        error: error.message || 'Unbekannter Fehler beim Senden der E-Mail'
      };
    }
  }
}

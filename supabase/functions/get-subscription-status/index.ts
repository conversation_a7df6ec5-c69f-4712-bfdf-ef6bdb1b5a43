// Supabase Edge Function für den Abruf des Abonnementstatus eines Benutzers
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Typen für die Antwort
interface SubscriptionStatusResponse {
  success: boolean;
  isPremium: boolean;
  subscriptionDetails?: {
    id: string;
    type: string;
    status: string;
    startDate: string;
    endDate: string | null;
    autoRenew: boolean;
  };
  error?: any;
}

serve(async (req) => {
  // CORS-Header für alle Anfragen
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  };

  // OPTIONS-Anfragen für CORS beantworten
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers,
      status: 204,
    });
  }

  // Nur GET-Anfragen akzeptieren
  if (req.method !== "GET") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      headers: { ...headers, "Content-Type": "application/json" },
      status: 405,
    });
  }

  try {
    // URL-Parameter extrahieren
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");

    // Validierung
    if (!userId) {
      throw new Error("userId parameter is required");
    }

    // Supabase-Client erstellen
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Das aktive Abonnement des Benutzers abrufen
    const { data: subscription, error } = await supabaseClient
      .from("subscriptions")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "active")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== "PGRST116") { // PGRST116 bedeutet "Kein Datensatz gefunden"
      throw error;
    }

    // Prüfen, ob ein aktives Abonnement existiert und ob es noch gültig ist
    let isPremium = false;
    let subscriptionDetails = null;

    if (subscription) {
      // Prüfen, ob das Abonnement abgelaufen ist
      const now = new Date();
      const endDate = subscription.end_date ? new Date(subscription.end_date) : null;
      
      // Ein Abonnement gilt als aktiv, wenn es entweder kein Enddatum hat oder das Enddatum in der Zukunft liegt
      isPremium = !endDate || endDate > now;

      if (isPremium) {
        // Abonnementdetails formatieren
        subscriptionDetails = {
          id: subscription.id,
          type: subscription.subscription_type,
          status: subscription.status,
          startDate: subscription.start_date,
          endDate: subscription.end_date,
          autoRenew: subscription.auto_renew,
        };
      } else {
        // Abonnement ist abgelaufen, Status auf "expired" aktualisieren
        await supabaseClient
          .from("subscriptions")
          .update({ status: "expired" })
          .eq("id", subscription.id);
      }
    }

    // Antwort zusammenstellen
    const response: SubscriptionStatusResponse = {
      success: true,
      isPremium: isPremium,
    };

    if (subscriptionDetails) {
      response.subscriptionDetails = subscriptionDetails;
    }

    return new Response(JSON.stringify(response), {
      headers: { ...headers, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Error processing request:", error);
    
    return new Response(
      JSON.stringify({
        success: false,
        isPremium: false,
        error: error.message,
      }),
      {
        headers: { ...headers, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
}); 
 
 
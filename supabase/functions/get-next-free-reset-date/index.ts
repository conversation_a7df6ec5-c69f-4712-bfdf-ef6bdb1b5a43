// Supabase Edge Function für das Abrufen des nächsten Free-Reset-Datums
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Supabase-Client erzeugen
const supabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
);

/**
 * Berechnet das nächste Free-Reset-Datum für einen Benutzer
 * INDIVIDUELLER 7-Tage-Timer: Jeder User hat seinen eigenen Reset-Zyklus
 * Neuer User: Sofort 10 Bewerbungen + Reset nach 7 Tagen ab Account-Erstellung
 */
async function getNextFreeResetDate(userId: string): Promise<string> {
  try {
    const now = new Date();

    // 1. Prüfe ob User bereits einen Counter mit free_reset_date hat
    const { data: counter, error: counterError } = await supabaseClient
      .from('application_counters')
      .select('free_reset_date, created_at')
      .eq('user_id', userId)
      .single();

    if (counterError && counterError.code !== 'PGRST116') {
      console.error('Fehler beim Abrufen des Counters:', counterError);
    }

    let nextResetDate: Date;

    if (counter && counter.reset_date) {
      // User hat bereits einen Reset-Zyklus - nächster Reset ist 7 Tage nach dem letzten
      const lastReset = new Date(counter.reset_date);
      nextResetDate = new Date(lastReset.getTime() + (7 * 24 * 60 * 60 * 1000)); // +7 Tage
    } else {
      // Neuer User oder kein reset_date - starte individuellen 7-Tage-Zyklus

      // Hole User-Erstellungsdatum für individuellen Zyklus
      const { data: userData, error: userError } = await supabaseClient
        .from('auth.users')
        .select('created_at')
        .eq('id', userId)
        .single();

      let userCreatedAt = now;
      if (userData && userData.created_at) {
        userCreatedAt = new Date(userData.created_at);
      } else if (counter && counter.created_at) {
        // Fallback: Counter-Erstellungsdatum
        userCreatedAt = new Date(counter.created_at);
      }

      // KORRIGIERT: Individueller Reset basierend auf User-Erstellung
      nextResetDate = new Date(userCreatedAt.getTime() + (7 * 24 * 60 * 60 * 1000));

      // Stelle sicher, dass das Reset-Datum in der Zukunft liegt
      while (nextResetDate <= now) {
        nextResetDate = new Date(nextResetDate.getTime() + (7 * 24 * 60 * 60 * 1000));
      }
    }

    // 2. Prüfe, ob der Benutzer ein aktives Premium-Abonnement hat
    const { data: subscriptions, error: subError } = await supabaseClient
      .from('subscriptions')
      .select('plan_type, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .in('plan_type', ['pro', 'premium', 'unlimited']);

    if (subError) {
      console.error('Fehler beim Abrufen der Abonnements:', subError);
    }

    // Wenn Premium-Abonnement vorhanden, kein Reset nötig
    if (subscriptions && subscriptions.length > 0) {
      console.log(`User ${userId} hat Premium-Plan - kein Reset nötig`);
      return null; // Premium-User haben unbegrenzte Bewerbungen
    }

    // 3. Aktualisiere/erstelle Counter mit individuellem Reset-Datum
    if (!counter) {
      // KORRIGIERT: Erstelle neuen Counter mit individuellem Reset-Datum
      const { error: insertError } = await supabaseClient
        .from('application_counters')
        .insert({
          user_id: userId,
          remaining_applications: 5,
          total_applications: 5,
          reset_date: nextResetDate.toISOString(), // INDIVIDUELLES Reset-Datum (korrekte Spalte!)
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });

      if (insertError) {
        console.error('Fehler beim Erstellen des Counters:', insertError);
      } else {
        console.log(`Neuer Counter für User ${userId} erstellt - Reset: ${nextResetDate.toISOString()}`);
      }
    } else if (!counter.reset_date) {
      // KORRIGIERT: Counter existiert aber kein reset_date - setze individuelles Datum
      const { error: updateError } = await supabaseClient
        .from('application_counters')
        .update({
          reset_date: nextResetDate.toISOString(), // INDIVIDUELLES Reset-Datum (korrekte Spalte!)
          updated_at: now.toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Fehler beim Aktualisieren des Reset-Datums:', updateError);
      } else {
        console.log(`Reset-Datum für User ${userId} gesetzt - Reset: ${nextResetDate.toISOString()}`);
      }
    }

    console.log(`Nächster individueller Reset für User ${userId}: ${nextResetDate.toISOString()}`);
    return nextResetDate.toISOString();

  } catch (error) {
    console.error('Fehler beim Berechnen des Reset-Datums:', error);
    throw error;
  }
}

// Hauptfunktion
serve(async (req: Request) => {
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers, status: 200 });
  }

  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { headers, status: 405 }
    );
  }

  try {
    const body = await req.json();
    const { p_user_id } = body;

    if (!p_user_id) {
      return new Response(
        JSON.stringify({ error: "Parameter p_user_id ist erforderlich" }),
        { headers, status: 400 }
      );
    }

    console.log(`Berechne nächstes Free-Reset-Datum für User: ${p_user_id}`);

    const nextResetDate = await getNextFreeResetDate(p_user_id);

    return new Response(
      JSON.stringify({ 
        success: true, 
        next_reset_date: nextResetDate,
        user_id: p_user_id
      }),
      { headers, status: 200 }
    );

  } catch (error) {
    console.error('Fehler in get-next-free-reset-date Function:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return new Response(
      JSON.stringify({ 
        error: `Processing error: ${errorMessage}`,
        success: false 
      }),
      { headers, status: 500 }
    );
  }
});

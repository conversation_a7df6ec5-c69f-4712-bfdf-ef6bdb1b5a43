// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { serve } from 'https://deno.land/std@0.199.0/http/server.ts';
import { corsHeaders } from '../_shared/cors.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.13.0';

console.log("Hello from Generate Cover Letter with Mistral Function!")

// Definiere die erwartete Struktur der Eingabedaten
interface RequestPayload {
  userId: string; // Benutzer-ID für Premium-Status-Überprüfung
  userProfile: {
    // Erweiterte Felder basierend auf der Dart-Implementierung
    name?: string;
    skills?: string[];
    experience?: string;
    email?: string;
    education?: string;
    interests?: string;
    additionalHints?: string; // Zusätzliche Hinweise für diesen Job
    globalAiHints?: string; // Globale KI-Hinweise für alle Bewerbungen
    applicationLength?: string; // Bevorzugte Bewerbungslänge (Kurz/Standard/Lang)
    // Weitere dynamische Felder
    [key: string]: any;
  };
  jobPosting: {
    title: string;
    company: string;
    description: string;
  };
  stylePreference?: string; // Stilpräferenz für das Anschreiben
  personalizedStylePrompt?: string; // Personalisierter Schreibstil-Prompt
  apiKey?: string; // Optional: Erlaube API-Key-Übergabe im Request für lokale Tests
}

// Definiere die erwartete Struktur der Mistral API Antwort
interface MistralResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Stilanweisungen für verschiedene Bewerbungsstile
const styleInstructions: Record<string, string> = {
  'Professionell': `
Schreibe in einem professionellen, formellen Stil mit:
- Sachlicher, klarer Sprache ohne Umschweife
- Angemessener formeller Distanz
- Präzisen Formulierungen und Fachbegriffen
- Höflichem, respektvollem Ton
`,
  'Kreativ': `
Schreibe in einem kreativen, innovativen Stil mit:
- Originellen Formulierungen und bildlicher Sprache
- Persönlicher Note und authentischer Stimme
- Überraschenden Perspektiven und einprägsamen Wendungen
- Dynamischem, enthusiastischem Ausdruck (ohne übertrieben zu wirken)
`,
  'Technisch': `
Schreibe in einem technischen, sachlichen Stil mit:
- Präzisen Fachbegriffen und technischem Vokabular
- Logischem Aufbau und klaren Zusammenhängen
- Faktenbasierter Argumentation und konkreten Beispielen
- Effizienter, zielgerichteter Kommunikation ohne Ausschmückungen
`,
  'Relevant': `
Schreibe eine zielgerichtete Bewerbung mit intelligenter Qualifikationsauswahl:
- Analysiere die Stellenbeschreibung und identifiziere die wichtigsten Anforderungen
- Wähle NUR die passendsten Qualifikationen und Erfahrungen aus dem Lebenslauf aus
- Erwähne andere Qualifikationen nur kurz oder gar nicht, wenn sie nicht relevant sind
- Fokussiere auf die Übereinstimmung zwischen Stellenanforderungen und Bewerberprofil
- Vermeide irrelevante Details oder Qualifikationen, die nicht zur Position passen
- Erstelle eine präzise, relevante Bewerbung ohne überflüssige Informationen
`
};

// Funktion zur Optimierung der Personalisierung
function optimizePersonalization(userProfile: RequestPayload['userProfile']): string {
  const lines: string[] = [];

  if (userProfile.name) {
    lines.push(`Name: ${userProfile.name}`);
  }

  if (userProfile.email) {
    lines.push(`E-Mail: ${userProfile.email}`);
  }

  if (userProfile.experience) {
    lines.push("Berufliche Erfahrung:");
    lines.push(typeof userProfile.experience === 'string'
      ? userProfile.experience
      : JSON.stringify(userProfile.experience));
  }

  if (userProfile.education) {
    lines.push("Ausbildung und Studium:");
    lines.push(typeof userProfile.education === 'string'
      ? userProfile.education
      : JSON.stringify(userProfile.education));
  }

  if (userProfile.skills) {
    lines.push("Kenntnisse und Fähigkeiten:");
    lines.push(Array.isArray(userProfile.skills)
      ? userProfile.skills.join(', ')
      : String(userProfile.skills));
  }

  if (userProfile.interests) {
    lines.push("Interessen und Motivation:");
    lines.push(typeof userProfile.interests === 'string'
      ? userProfile.interests
      : JSON.stringify(userProfile.interests));
  }

  // Zusätzliche Hinweise aus dem TextField berücksichtigen
  if (userProfile.additionalHints) {
    lines.push("\nZusätzliche Hinweise für diese spezifische Bewerbung:");
    lines.push(typeof userProfile.additionalHints === 'string'
      ? userProfile.additionalHints
      : JSON.stringify(userProfile.additionalHints));
  }

  // Globale KI-Hinweise berücksichtigen, die für alle Bewerbungen gelten
  if (userProfile.globalAiHints) {
    lines.push("\nGlobale Hinweise für alle Bewerbungen:");
    lines.push(typeof userProfile.globalAiHints === 'string'
      ? userProfile.globalAiHints
      : JSON.stringify(userProfile.globalAiHints));
  }

  // Bewerbungslänge berücksichtigen
  if (userProfile.applicationLength) {
    lines.push("\nBevorzugte Bewerbungslänge:");
    lines.push(typeof userProfile.applicationLength === 'string'
      ? userProfile.applicationLength
      : JSON.stringify(userProfile.applicationLength));
  }

  return lines.join('\n');
}

// Funktion zur Generierung des Cover Letter Prompts
function generateCoverLetterPrompt(
  jobTitle: string,
  jobDescription: string,
  personalizationText: string,
  styleInstruction: string
): string {
  // Extrahiere globale KI-Hinweise aus dem personalizationText, wenn vorhanden
  let globalAiHints = '';
  const globalAiHintsMatch = personalizationText.match(/Globale Hinweise für alle Bewerbungen:\s*([\s\S]*?)(?=\n\n|$)/);
  if (globalAiHintsMatch && globalAiHintsMatch[1]) {
    globalAiHints = globalAiHintsMatch[1].trim();
    console.log('WICHTIG - Extracted global AI hints:', globalAiHints);
  } else {
    console.log('WARNUNG: Keine globalen KI-Hinweise gefunden in:', personalizationText);
  }

  // Extrahiere zusätzliche Hinweise aus dem personalizationText, wenn vorhanden
  let additionalHints = '';
  const additionalHintsMatch = personalizationText.match(/Zusätzliche Hinweise für diese spezifische Bewerbung:\s*([\s\S]*?)(?=\n\n|$)/);
  if (additionalHintsMatch && additionalHintsMatch[1]) {
    additionalHints = additionalHintsMatch[1].trim();
    console.log('Extracted additional hints:', additionalHints);
  }

  // Extrahiere Bewerbungslänge aus dem personalizationText, wenn vorhanden
  let applicationLength = 'Standard'; // Standardwert
  const applicationLengthMatch = personalizationText.match(/Bevorzugte Bewerbungslänge:\s*([\s\S]*?)(?=\n\n|$)/);
  if (applicationLengthMatch && applicationLengthMatch[1]) {
    applicationLength = applicationLengthMatch[1].trim();
    console.log('🔥 MISTRAL PROMPT GENERATION - EXTRACTED APPLICATION LENGTH:', applicationLength);
    console.log('🔥 MISTRAL PROMPT GENERATION - FULL PERSONALIZATION TEXT:');
    console.log('='.repeat(80));
    console.log(personalizationText);
    console.log('='.repeat(80));
  } else {
    console.log('❌ MISTRAL PROMPT GENERATION - NO APPLICATION LENGTH FOUND');
    console.log('🔥 MISTRAL PROMPT GENERATION - FULL PERSONALIZATION TEXT:');
    console.log('='.repeat(80));
    console.log(personalizationText);
    console.log('='.repeat(80));
  }

  // Für "Kurz" verwende einen komplett anderen, minimalistischen Prompt
  if (applicationLength === 'Kurz') {
    console.log('🔥🔥🔥 USING SPECIAL SHORT PROMPT FOR "KURZ" APPLICATION LENGTH 🔥🔥🔥');
    const shortPrompt = `Du bist ein Experte für EXTREM KURZE Bewerbungsanschreiben.

KRITISCHE AUFGABE:
1. Extrahiere die E-Mail-Adresse aus der Stellenanzeige für '${jobTitle}'. Wenn keine gefunden wird, antworte mit 'EMAIL: nicht gefunden'.
2. Erstelle ein KURZES Anschreiben mit MAXIMAL 120 WÖRTERN:

STRENGE REGELN FÜR KURZE BEWERBUNGEN:
- MAXIMAL 120 WÖRTER (zähle sie!)
- NUR 3 kurze Absätze: Anrede (1 Satz), Hauptteil (2-3 Sätze), Schluss (1 Satz)
- KEINE ausführlichen Erklärungen
- KEINE Motivation oder Mehrwert-Beschreibungen
- KEINE detaillierten Qualifikations-Auflistungen
- NUR die wichtigste Qualifikation erwähnen
- Jeder Satz maximal 15 Wörter
- KEINE Füllwörter wie "gerne", "sehr", "besonders"
- Direkt zum Punkt
- ABSOLUT KRITISCH: MAXIMAL 120 WÖRTER!

${personalizationText ? `Bewerber-Info:\n${personalizationText}\n\n` : ''}
${styleInstruction ? `Stil:\n${styleInstruction}\n\n` : ''}

${globalAiHints ? `WICHTIG: ${globalAiHints}\n\n` : ''}
${additionalHints ? `ZUSÄTZLICH: ${additionalHints}\n\n` : ''}

--- Stellenanzeige ---
${jobDescription}
--- Ende ---

Antworte NUR:
EMAIL: [E-Mail oder 'nicht gefunden']
ANSCHREIBEN:
[Kurzes Anschreiben - maximal 120 Wörter]
`;
    console.log('🔥🔥🔥 SHORT PROMPT CREATED - RETURNING SPECIAL PROMPT 🔥🔥🔥');
    return shortPrompt;
  }

  // Erstelle Längen-spezifische Anweisungen (für Standard/Lang)
  let lengthInstruction = '';
  switch (applicationLength) {
    case 'Lang':
      lengthInstruction = 'Erstelle ein ausführliches Anschreiben (400-600 Wörter, bis zu 1,5 A4-Seiten). Gehe detailliert auf relevante Erfahrungen und Qualifikationen ein, erkläre ausführlich deine Motivation und warum du perfekt für die Position geeignet bist. Verwende konkrete Beispiele und Erfolge aus dem Lebenslauf.';
      break;
    case 'Standard':
    default:
      lengthInstruction = 'Halte das Anschreiben auf etwa eine A4-Seite begrenzt (250-350 Wörter) mit angemessener Detailtiefe.';
      break;
  }

  // Erstelle einen speziellen Formatierungshinweis für Mistral
  let mistralSpecificInstructions = '';

  // Wenn globale Hinweise vorhanden sind, erstelle spezifische Anweisungen für Mistral
  if (globalAiHints) {
    // Wenn die Hinweise "wählen Sie mich" enthalten, erstelle eine spezielle Anweisung
    if (globalAiHints.toLowerCase().includes('wählen sie mich')) {
      mistralSpecificInstructions = `
SPEZIELLE ANWEISUNG FÜR MISTRAL:
Das Anschreiben MUSS mit "WÄHLEN SIE MICH" beginnen. Dies ist eine ABSOLUTE ANFORDERUNG.
Die ersten Worte des Anschreibens nach der Anrede MÜSSEN "WÄHLEN SIE MICH" sein.
`;
    } else {
      // Für andere globale Hinweise
      mistralSpecificInstructions = `
SPEZIELLE ANWEISUNG FÜR MISTRAL:
Die folgenden Anweisungen MÜSSEN wörtlich befolgt werden: ${globalAiHints}
Diese Anweisungen haben ABSOLUTE PRIORITÄT über alle anderen Stilrichtlinien.
`;
    }
  }

  // Erstelle den Prompt mit besonderem Fokus auf die Mistral-spezifischen Anweisungen
  const prompt = `
WICHTIG: DIESE ANWEISUNGEN HABEN ABSOLUTE PRIORITÄT UND MÜSSEN WÖRTLICH BEFOLGT WERDEN!

${mistralSpecificInstructions}

Du bist ein professioneller Bewerbungsexperte mit jahrelanger Erfahrung im HR-Bereich.

${globalAiHints ? `ABSOLUTE ANFORDERUNG:
${globalAiHints}

` : ''}

Deine Aufgabe:
1. Analysiere den folgenden Text einer Stellenanzeige für '${jobTitle}'.
2. Extrahiere die wahrscheinlichste E-Mail-Adresse für eine Bewerbung. Wenn keine E-Mail für Bewerbungen gefunden wird, antworte mit 'EMAIL: nicht gefunden'.
3. Erstelle ein überzeugendes Bewerbungsanschreiben unter Beachtung dieser Regeln:
   - Beziehe dich konkret auf die Anforderungen und Aufgaben der Stellenanzeige
   - Stelle gezielt Bezüge zwischen den Qualifikationen des Bewerbers und den Anforderungen her
   - Verwende einen klaren, präzisen Schreibstil ohne Übertreibungen
   - Strukturiere das Anschreiben mit einer überzeugenden Einleitung, relevantem Hauptteil und starkem Abschluss
   - Füge eine formell korrekte Anrede und Grußformel hinzu
   - ${lengthInstruction}
   - Betone Motivation und Mehrwert für das Unternehmen
   - Vermeide Standardfloskeln, Rechtschreibfehler und Passivsätze
   - Verwende nur den Namen des Bewerbers am Ende des Anschreibens (im Gruß)
   ${applicationLength === 'Kurz' ? '   - ABSOLUT KRITISCH: Das Anschreiben darf NICHT länger als 150 Wörter sein. Zähle die Wörter und kürze wenn nötig.' : ''}

${globalAiHints ? `WICHTIG: Das Anschreiben MUSS die folgende Anforderung erfüllen:
${globalAiHints}
Diese Anforderung hat ABSOLUTE PRIORITÄT.

` : ''}

4. Beginne niemals die Bewerbung mit "mit großer Interesse" oder ähnlichem
5. Wenn der Bewerber nicht die Fähigkeit hat, die Anforderungen der Stellenanzeige zu erfüllen, schreibe das nicht extra, sondern verschöne diese Lücke
6. Füge KEINE Hinweise, Tipps oder Metakommentare in die Bewerbung ein - der Text soll NUR das fertige Anschreiben sein
7. Erkläre nichts über das Anschreiben selbst und füge keine Kommentare wie "Hier ist dein Anschreiben" hinzu
8. Liefere ausschließlich den Bewerbungstext ohne jegliche Erklärungen, Ratschläge oder Hinweise
9. Schreibe nicht, dass etwas mich reizt, oder typische KI-generierte Sätze
10. WICHTIGE REGEL: Füge KEINE Absender- oder Empfänger-Kontaktinformationen (Name, Anschrift, E-Mail, Telefon, Adresse) im Anschreiben ein. Der vollständige Empfänger gehört in eine E-Mail, nicht ins Anschreiben selbst.
11. WICHTIGE REGEL: Wenn Qualifikationen fehlen, erwähne dies NICHT direkt. Formuliere stattdessen positiv, z.B. durch Betonung der Lernbereitschaft oder der Übertragbarkeit vorhandener Fähigkeiten auf die neuen Anforderungen.
12. WICHTIGE REGEL: Verwende KEINE Sternchen (*) oder andere Formatierungszeichen im Text des Anschreibens.
13. WICHTIGE REGEL: Schreibe KEINEN Betreff im Anschreiben selbst. Der Betreff gehört in die E-Mail, nicht ins Anschreiben.
14. WICHTIGE REGEL: Beginne direkt mit der Anrede (z.B. "Sehr geehrte Frau XYZ,") ohne Absender, Empfänger oder Betreffzeile.

${globalAiHints ? `NOCHMALS ZUR ERINNERUNG - DIESE ANFORDERUNG MUSS ERFÜLLT WERDEN:
${globalAiHints}

` : ''}

15. WICHTIGE REGEL: Wenn zusätzliche Hinweise für diese spezifische Bewerbung oder globale Hinweise für alle Bewerbungen angegeben wurden, berücksichtige diese UNBEDINGT im Anschreiben. Diese Hinweise haben höchste Priorität und sollten im Anschreiben deutlich zum Ausdruck kommen.
16. WICHTIGE REGEL: Entferne ALLE Geschlechterbezeichnungen wie "(m/w/d)", "(m/w/x)", "(m/w/b)" oder ähnliche aus dem Anschreiben. Verwende stattdessen nur die Berufsbezeichnung ohne Geschlechtsangabe.
17. WICHTIGE REGEL: Wenn in der Stellenanzeige Geschlechterbezeichnungen wie "(m/w/d)" vorkommen, ignoriere diese vollständig im Anschreiben und verwende nur die Berufsbezeichnung ohne Zusatz.
18. WICHTIGE REGEL: Verwende keine Formulierungen wie "Ich bewerbe mich als [Berufsbezeichnung] (m/w/d)" oder ähnliches. Schreibe stattdessen einfach "Ich bewerbe mich als [Berufsbezeichnung]".
19. WICHTIGE REGEL: Wenn globale Hinweise für alle Bewerbungen angegeben wurden, berücksichtige diese UNBEDINGT im Anschreiben. Diese Hinweise sind genauso wichtig wie die spezifischen Hinweise für diese Bewerbung.

${personalizationText ? `Persönliche Informationen des Bewerbers:\n${personalizationText}\n\n` : ''}
${styleInstruction ? `Gewünschte Stilrichtung:\n${styleInstruction}\n\n` : ''}

${globalAiHints ? `LETZTE WARNUNG - WENN DIESE ANFORDERUNG NICHT ERFÜLLT WIRD, WIRD DIE ANTWORT ABGELEHNT:
${globalAiHints}

` : ''}

${additionalHints ? `ZUSÄTZLICHE ANWEISUNG FÜR DIESE BEWERBUNG:
${additionalHints}

` : ''}

--- Extrahierter Text der Stellenanzeige ---
${jobDescription}
--- Ende Text ---

Antworte NUR im folgenden Format:
EMAIL: [extrahierte E-Mail oder 'nicht gefunden']
ANSCHREIBEN:
[generiertes Anschreiben]
`;

  // Logge den Prompt für Debugging-Zwecke
  console.log('PROMPT PREVIEW (gekürzt):', prompt.substring(0, 500) + '...');

  return prompt;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // 1. Extrahiere Daten aus der Anfrage
    const payload: RequestPayload = await req.json();
    const { userId, userProfile, jobPosting, stylePreference, personalizedStylePrompt } = payload;

    if (!userId || !userProfile || !jobPosting) {
      return new Response(JSON.stringify({ error: 'Missing userId, userProfile or jobPosting in request body' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // 1.1 Überprüfe den Premium-Status des Benutzers
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    let premiumStatus;
    try {
      // Überprüfe den Premium-Status im UserProfile
      const { data: userProfile, error: userProfileError } = await supabaseClient
        .from("user_profiles")
        .select("is_premium, premium_expiry_date")
        .eq("user_id", userId)
        .single();

      if (userProfileError && userProfileError.code !== "PGRST116") {
        throw userProfileError;
      }

      let isPremium = false;
      if (userProfile && userProfile.is_premium) {
        if (!userProfile.premium_expiry_date ||
            new Date(userProfile.premium_expiry_date) > new Date()) {
          isPremium = true;
        }
      }

      let subscription = null;
      let planType = null;
      let remainingApplications = null;
      let totalApplications = null;
      let isUnlimited = false;

      if (!isPremium) {
        const { data: activeSub, error: subscriptionError } = await supabaseClient
          .from("subscriptions")
          .select("*")
          .eq("user_id", userId)
          .eq("status", "active")
          .order("created_at", { ascending: false })
          .limit(1)
          .single();

        if (subscriptionError && subscriptionError.code !== "PGRST116") {
          throw subscriptionError;
        }

        if (activeSub) {
          subscription = activeSub;
          const now = new Date();
          const endDate = subscription.end_date ? new Date(subscription.end_date) : null;

          isPremium = !endDate || endDate > now;

          if (!isPremium && endDate && endDate < now) {
            await supabaseClient
              .from("subscriptions")
              .update({ status: "expired" })
              .eq("id", subscription.id);
          } else {
            planType = subscription.plan_type || "basic";
            remainingApplications = subscription.remaining_applications;
            totalApplications = subscription.total_applications;
            isUnlimited = planType === "unlimited";

            if (isUnlimited) {
              remainingApplications = null;
              totalApplications = null;
            }
          }
        }
      }

      // Alle User haben Zugriff auf AI-Anschreiben (mit normalem Guthaben-Abzug)
      let hasFeatureAccess = true;

      premiumStatus = {
        success: true,
        isPremium: isPremium,
        hasAccess: hasFeatureAccess,
        plan: planType,
        remainingApplications: remainingApplications,
        totalApplications: totalApplications,
        isUnlimited: isUnlimited,
      };

    } catch (error) {
      console.error('Error checking premium status:', error);
      return new Response(JSON.stringify({ error: 'Failed to verify premium status' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    // Wenn der Benutzer keinen Zugriff hat, gib einen Fehler zurück
    if (!premiumStatus.hasAccess) {
      console.log(`User ${userId} does not have access to aiCoverLetter feature`);
      return new Response(JSON.stringify({
        error: 'Premium feature access required',
        isPremium: premiumStatus.isPremium,
        requiresUpgrade: true,
        plan: premiumStatus.plan,
        remainingApplications: premiumStatus.remainingApplications,
        totalApplications: premiumStatus.totalApplications,
        isUnlimited: premiumStatus.isUnlimited
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      });
    }

    // Prüfe, ob noch Bewerbungen übrig sind (für alle User)
    if (!premiumStatus.isUnlimited && premiumStatus.remainingApplications !== null && premiumStatus.remainingApplications <= 0) {
      console.log(`User ${userId} has no remaining applications`);
      return new Response(JSON.stringify({
        error: 'No remaining applications',
        isPremium: premiumStatus.isPremium,
        requiresUpgrade: true,
        plan: premiumStatus.plan,
        remainingApplications: 0,
        totalApplications: premiumStatus.totalApplications,
        isUnlimited: false
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      });
    }

    // Für Unlimited-User: Kein Guthaben-Abzug
    if (premiumStatus.isUnlimited) {
      console.log(`User ${userId} has unlimited access, skipping counter increment`);
    } else {
      console.log(`User ${userId} will have credit deducted after successful generation`);
    }

    // 2. Lese den Mistral API-Schlüssel aus den Secrets oder verwende den optionalen Key aus dem Payload
    const mistralApiKey = Deno.env.get('MISTRAL_API_KEY') ?? payload.apiKey;

    // Prüfe, ob der API-Schlüssel verfügbar ist
    if (!mistralApiKey) {
      console.error('Mistral API Key not found in Secrets or payload.');
      return new Response(JSON.stringify({ error: 'Mistral API Key configuration missing' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    // Extrahiere globale KI-Hinweise für spätere Verwendung
    let globalAiHints = '';
    if (userProfile.globalAiHints) {
      globalAiHints = typeof userProfile.globalAiHints === 'string'
        ? userProfile.globalAiHints
        : JSON.stringify(userProfile.globalAiHints);
      console.log('Extracted global AI hints for validation:', globalAiHints);
    }

    // 3. Optimiere die Personalisierung und wähle die passenden Stilanweisungen
    const personalizationText = optimizePersonalization(userProfile);

    // Extrahiere Bewerbungslänge aus dem personalizationText
    let applicationLength = 'Standard'; // Standardwert
    const applicationLengthMatch = personalizationText.match(/Bevorzugte Bewerbungslänge:\s*([\s\S]*?)(?=\n\n|$)/);
    if (applicationLengthMatch && applicationLengthMatch[1]) {
      applicationLength = applicationLengthMatch[1].trim();
      console.log('🔥 MISTRAL API CONFIG - EXTRACTED APPLICATION LENGTH:', applicationLength);
      console.log('🔥 MISTRAL API CONFIG - FULL PERSONALIZATION TEXT:');
      console.log('='.repeat(80));
      console.log(personalizationText);
      console.log('='.repeat(80));
    } else {
      console.log('❌ MISTRAL API CONFIG - NO APPLICATION LENGTH FOUND');
      console.log('🔥 MISTRAL API CONFIG - FULL PERSONALIZATION TEXT:');
      console.log('='.repeat(80));
      console.log(personalizationText);
      console.log('='.repeat(80));
    }

    // Bestimme die Stilanweisungen
    let styleInstruction = '';

    // Wenn ein personalisierter Schreibstil-Prompt vorhanden ist, verwende diesen
    if (personalizedStylePrompt && personalizedStylePrompt.trim().length > 0) {
      console.log('Using personalized style prompt');
      styleInstruction = personalizedStylePrompt;
    }
    // Ansonsten verwende den angegebenen Stil oder default auf 'Professionell'
    else {
      styleInstruction = stylePreference && styleInstructions[stylePreference]
        ? styleInstructions[stylePreference]
        : styleInstructions['Professionell'];
    }

    // 4. Generiere den optimierten Prompt
    const prompt = generateCoverLetterPrompt(
      jobPosting.title,
      jobPosting.description,
      personalizationText,
      styleInstruction
    );

    console.log(`🔥 MISTRAL GENERATED PROMPT (APPLICATION LENGTH: ${applicationLength}):`);
    console.log('='.repeat(100));
    console.log(prompt);
    console.log('='.repeat(100));

    // 5. Konfiguriere die Mistral API-Anfrage für Queue
    const requestPayload = {
      model: 'mistral-large-2407', // Verwende ein spezifisches stabiles Modell
      messages: [
        { role: 'system', content: 'Du bist ein professioneller Bewerbungsexperte mit jahrelanger Erfahrung im HR-Bereich.' },
        { role: 'user', content: prompt },
      ],
      max_tokens: 2048,
      temperature: 0.7,
    };

    // 6. Sende Request über Mistral Queue Manager
    console.log(`Sending request to Mistral Queue Manager (Premium: ${premiumStatus.isPremium})...`);

    let apiResult;
    try {
      // Rufe Queue Manager auf
      const queueResponse = await fetch(`${Deno.env.get("SUPABASE_URL")}/functions/v1/mistral-queue-manager`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Deno.env.get("SUPABASE_ANON_KEY")}`,
        },
        body: JSON.stringify({
          userId: userId,
          isPremium: premiumStatus.isPremium,
          requestData: requestPayload,
        }),
      });

      if (!queueResponse.ok) {
        const errorText = await queueResponse.text();
        throw new Error(`Queue Manager Error ${queueResponse.status}: ${errorText}`);
      }

      apiResult = await queueResponse.json();
      console.log(`Queue Manager response received successfully`);

    } catch (error) {
      console.error(`Queue Manager Error:`, error);
      throw new Error(`Failed to process request through queue: ${error.message}`);
    }

    // Log token usage for monitoring
    if (apiResult.usage) {
      console.log(`Mistral API token usage: ${JSON.stringify(apiResult.usage)}`);
    }

    const rawGeneratedContent = apiResult.choices?.[0]?.message?.content;

    if (!rawGeneratedContent) {
      console.error(`Could not extract raw content from MISTRAL response`);
      throw new Error(`Failed to parse raw content from MISTRAL response.`);
    }

    // 8. Extrahiere E-Mail und Anschreiben
    let extractedEmail: string | null = null;
    let generatedCoverLetter: string = "";

    // Verbesserte Pattern-Erkennung für verschiedene Formatmöglichkeiten
    const emailMatch = rawGeneratedContent.match(/^EMAIL:\s*(.*?)$/im);

    // Mehrere Muster für das Anschreiben testen, um die Extraktion zu verbessern
    let coverLetterMatch = rawGeneratedContent.match(/ANSCHREIBEN:\s*\n+([\s\S]*)/i);

    if (!coverLetterMatch) {
      // Alternative Muster, falls das Hauptmuster nicht funktioniert
      coverLetterMatch = rawGeneratedContent.match(/EMAIL:.*?\n+([\s\S]*)/i);
    }

    if (emailMatch && emailMatch[1].trim().toLowerCase() !== 'nicht gefunden') {
      extractedEmail = emailMatch[1].trim();
      console.log(`Extracted Email: ${extractedEmail}`);
    } else {
      console.log('No email found or marked as "nicht gefunden".');
    }

    if (coverLetterMatch && coverLetterMatch[1]) {
      generatedCoverLetter = coverLetterMatch[1].trim();
      console.log('Successfully extracted cover letter part.');
    } else {
      // Fallback: Entferne "EMAIL: ..." und verwende den Rest als Anschreiben
      const contentWithoutEmailLine = rawGeneratedContent.replace(/^EMAIL:.*$/im, '').trim();
      generatedCoverLetter = contentWithoutEmailLine;
      console.log('Using alternative extraction method for cover letter.');
    }

    // POST-PROCESSING: Kürze die Bewerbung falls sie zu lang ist (für "Kurz")
    if (applicationLength === 'Kurz' && generatedCoverLetter) {
      const words = generatedCoverLetter.split(/\s+/);
      console.log(`🔥🔥🔥 MISTRAL POST-PROCESSING - Original cover letter word count: ${words.length} 🔥🔥🔥`);

      if (words.length > 120) {
        console.log('🔥🔥🔥 MISTRAL POST-PROCESSING - Cover letter too long for "Kurz" setting, cutting to 120 words... 🔥🔥🔥');
        // Kürze auf 120 Wörter
        const shortenedWords = words.slice(0, 120);
        generatedCoverLetter = shortenedWords.join(' ');

        // Stelle sicher, dass es mit einem vollständigen Satz endet
        const lastSentenceEnd = generatedCoverLetter.lastIndexOf('.');
        if (lastSentenceEnd > generatedCoverLetter.length * 0.8) {
          generatedCoverLetter = generatedCoverLetter.substring(0, lastSentenceEnd + 1);
        }

        console.log(`🔥🔥🔥 MISTRAL POST-PROCESSING - Shortened cover letter to ${generatedCoverLetter.split(/\s+/).length} words 🔥🔥🔥`);
      } else {
        console.log(`🔥🔥🔥 MISTRAL POST-PROCESSING - Cover letter is already short enough (${words.length} words) 🔥🔥🔥`);
      }
    }

    console.log('Successfully processed cover letter generation with Mistral.');

    // Logge die ersten 500 Zeichen des Anschreibens für Debugging-Zwecke
    console.log('ANTWORT PREVIEW (gekürzt):', generatedCoverLetter.substring(0, 500) + '...');

    // Überprüfe, ob die globalen Hinweise berücksichtigt wurden
    if (globalAiHints && generatedCoverLetter) {
      const hintsLowerCase = globalAiHints.toLowerCase();
      const coverLetterLowerCase = generatedCoverLetter.toLowerCase();

      // Überprüfe, ob die Hinweise im Anschreiben enthalten sind
      if (hintsLowerCase.includes('wählen sie mich') && !coverLetterLowerCase.includes('wählen sie mich')) {
        console.log('WARNUNG: Die globalen KI-Hinweise wurden NICHT berücksichtigt!');
      } else {
        console.log('INFO: Die globalen KI-Hinweise wurden berücksichtigt.');
      }
    }

    // Keine Signatur mehr hinzufügen, nur den reinen Bewerbungstext verwenden

    // 9. Gib das Ergebnis zurück
    console.log('🔥 MISTRAL FINAL RESULT:');
    console.log('='.repeat(80));
    console.log('EXTRACTED EMAIL:', extractedEmail);
    console.log('COVER LETTER WORD COUNT:', generatedCoverLetter.split(/\s+/).length);
    console.log('APPLICATION LENGTH SETTING:', applicationLength);
    console.log('COVER LETTER:');
    console.log(generatedCoverLetter);
    console.log('='.repeat(80));

    return new Response(JSON.stringify({
      coverLetter: generatedCoverLetter,
      extractedEmail: extractedEmail,
      // Zusätzliche Metadaten
      style: stylePreference || 'Professionell',
      usedPersonalizedStyle: !!personalizedStylePrompt,
      modelType: 'mistral',
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error processing request:', error);
    return new Response(JSON.stringify({ error: (error as Error).message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});

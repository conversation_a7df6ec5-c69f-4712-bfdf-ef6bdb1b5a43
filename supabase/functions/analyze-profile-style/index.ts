// Supabase Edge Function: analyze-profile-style
// Diese Funktion analysiert das Benutzerprofil und generiert einen personalisierten Schreibstil-Prompt

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { Anthropic } from 'https://esm.sh/@anthropic-ai/sdk@0.6.0';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS-Header für Cross-Origin-Anfragen
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Formatiert die Profildaten für die Analyse
function formatProfileDataForAnalysis(profileData: any): string {
  const buffer: string[] = [];

  // Extrahiere relevante Informationen aus dem Profil
  const workExperience = profileData.workExperience || [];
  const education = profileData.education || [];
  const skills = profileData.skills || [];
  const experienceSummary = profileData.experienceSummary;

  // Formatiere die Berufserfahrung
  if (workExperience.length > 0) {
    buffer.push("BERUFSERFAHRUNG:");
    for (const exp of workExperience) {
      const position = exp.position || 'Unbekannte Position';
      const company = exp.company || 'Unbekanntes Unternehmen';
      const startDate = exp.startDate || 'Unbekannt';
      const endDate = exp.endDate || 'Heute';
      const description = exp.description || '';

      buffer.push(`- ${position} bei ${company} (${startDate} - ${endDate})`);
      if (description) {
        buffer.push(`  ${description}`);
      }
    }
    buffer.push("");
  }

  // Formatiere die Ausbildung
  if (education.length > 0) {
    buffer.push("AUSBILDUNG:");
    for (const edu of education) {
      const institution = edu.institution || 'Unbekannte Institution';
      const degree = edu.degree || 'Unbekannter Abschluss';
      const fieldOfStudy = edu.fieldOfStudy || '';
      const startDate = edu.startDate || 'Unbekannt';
      const endDate = edu.endDate || 'Heute';

      let educationText = `- ${degree}`;
      if (fieldOfStudy) {
        educationText += ` in ${fieldOfStudy}`;
      }
      educationText += ` an ${institution} (${startDate} - ${endDate})`;
      buffer.push(educationText);
    }
    buffer.push("");
  }

  // Formatiere die Fähigkeiten
  if (skills.length > 0) {
    buffer.push("FÄHIGKEITEN:");
    buffer.push(skills.join(', '));
    buffer.push("");
  }

  // Füge die Erfahrungszusammenfassung hinzu, falls vorhanden
  if (experienceSummary) {
    buffer.push("ERFAHRUNGSZUSAMMENFASSUNG:");
    buffer.push(experienceSummary);
  }

  return buffer.join("\n");
}

// Generiert einen personalisierten Schreibstil-Prompt basierend auf dem Benutzerprofil
async function generatePersonalizedStylePrompt(profileData: any): Promise<object | { error: string }> {
  console.log('generatePersonalizedStylePrompt aufgerufen.');

  // API-Schlüssel aus Umgebungsvariablen laden
  const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY');
  const modelName = Deno.env.get('ANTHROPIC_MODEL') || 'claude-3-haiku-20240307';

  if (!anthropicApiKey) {
    console.error('ANTHROPIC_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: Anthropic API Key fehlt.' };
  }

  const anthropic = new Anthropic({
    apiKey: anthropicApiKey,
  });

  // Formatiere die Profildaten für die Analyse
  const formattedProfileData = formatProfileDataForAnalysis(profileData);

  // Erstelle den Prompt für die KI
  const prompt = `
Du bist ein Experte für Bewerbungsschreiben und Kommunikationsstile.

Analysiere die folgenden Profildaten einer Person und erstelle einen Prompt, der beschreibt, wie diese Person basierend auf ihrem Bildungsstand, ihrer Berufserfahrung und ihren Sprachkenntnissen ein Bewerbungsschreiben formulieren würde.

PROFILDATEN:
${formattedProfileData}

Berücksichtige folgende Faktoren:
1. Bildungsniveau (z.B. Ausbildung, Bachelor, Master, Doktor)
2. Sprachniveau (falls erkennbar)
3. Berufserfahrung und Position
4. Fachlicher Hintergrund
5. Formalität und Präzision

Erstelle einen Prompt, der beschreibt, wie ein Bewerbungsschreiben im authentischen Stil dieser Person aussehen würde. Der Prompt sollte später verwendet werden, um ein Anschreiben zu generieren, das so klingt, als hätte es die Person selbst geschrieben.

Antworte NUR mit dem Prompt, ohne Einleitung oder Erklärung. Der Prompt sollte im folgenden Format sein:

Schreibe in einem [Beschreibung des Stils] Stil:
- [Anweisung 1]
- [Anweisung 2]
- [Anweisung 3]
- [Anweisung 4]
`;

  console.log(`Sende Anfrage an Anthropic Modell: ${modelName}`);

  try {
    const msg = await anthropic.messages.create({
      model: modelName,
      max_tokens: 1000,
      messages: [
        { role: 'user', content: prompt },
      ],
    });

    console.log('Antwort von Anthropic erhalten.');

    // Extrahiere den Prompt aus der Antwort
    let stylePrompt = '';
    if (msg.content && msg.content.length > 0 && msg.content[0].type === 'text') {
      stylePrompt = msg.content[0].text.trim();
    }

    if (stylePrompt) {
      return { stylePrompt };
    } else {
      return { error: 'Konnte keinen Schreibstil-Prompt aus der KI-Antwort extrahieren.' };
    }
  } catch (error) {
    console.error('Fehler bei der Kommunikation mit Anthropic:', error);
    return { error: `Fehler bei der Kommunikation mit der KI: ${error.message}` };
  }
}

// Hauptfunktion zum Verarbeiten der Anfragen
serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }

  // Nur POST-Anfragen akzeptieren
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Nur POST-Anfragen werden unterstützt' }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Anfragedaten parsen
    const requestData = await req.json();

    // Prüfen, ob profileData und userId vorhanden sind
    if (!requestData.profileData || !requestData.userId) {
      console.error('[Serve] Keine profileData oder userId in der Anfrage gefunden.');
      return new Response(
        JSON.stringify({ error: 'profileData und userId sind erforderlich' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Überprüfe den Premium-Status des Benutzers
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Überprüfe, ob der Benutzer Premium-Zugriff hat oder temporären Zugriff auf das Feature
    console.log('[Serve] Überprüfe Premium-Status für Benutzer:', requestData.userId);
    const { data: premiumStatus, error: premiumError } = await supabaseClient.functions.invoke(
      'check-premium-status',
      {
        body: { userId: requestData.userId, feature: 'aiCoverLetter' },
      }
    );

    if (premiumError) {
      console.error('[Serve] Fehler bei der Überprüfung des Premium-Status:', premiumError);
      return new Response(
        JSON.stringify({ error: 'Fehler bei der Überprüfung des Premium-Status' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Wenn der Benutzer keinen Zugriff hat, gib einen Fehler zurück
    if (!premiumStatus.hasAccess) {
      console.log(`[Serve] Benutzer ${requestData.userId} hat keinen Zugriff auf aiCoverLetter`);
      return new Response(
        JSON.stringify({
          error: 'Premium-Zugriff erforderlich',
          isPremium: premiumStatus.isPremium,
          requiresUpgrade: true
        }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Personalisierten Schreibstil-Prompt generieren
    const result = await generatePersonalizedStylePrompt(requestData.profileData);

    // Prüfen, ob ein Fehler aufgetreten ist
    if ('error' in result) {
      console.error('[Serve] Fehler bei der Generierung des Schreibstil-Prompts:', result.error);
      return new Response(
        JSON.stringify({ error: result.error }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Erfolgreiche Antwort
    console.log('[Serve] Schreibstil-Prompt erfolgreich generiert.');
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('[Serve] Unerwarteter Fehler:', error);
    return new Response(
      JSON.stringify({ error: `Unerwarteter Fehler: ${error.message}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Rate limiting - speichere Timestamps in-memory
const requestTimestamps: number[] = [];
const MAX_REQUESTS_PER_MINUTE = 20; // Reduziert für Bulk-Requests
const RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 Minute

// Interface für Bulk-Request
interface BulkTranslationRequest {
  items: {
    id: string;
    text: string;
  }[];
  target_lang: string;
  source_lang: string;
}

// Interface für Bulk-Response
interface BulkTranslationResponse {
  success: boolean;
  results: {
    id: string;
    translated_text: string;
    success: boolean;
    error?: string;
  }[];
  total_processed: number;
  failed_count: number;
}

// Hilfsfunktion für exponential backoff delay
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Überprüft ob eine neue Anfrage erlaubt ist
function canMakeRequest(): boolean {
  const now = Date.now();
  const cutoff = now - RATE_LIMIT_WINDOW_MS;
  
  // Entferne alte Timestamps
  const recentRequests = requestTimestamps.filter(timestamp => timestamp > cutoff);
  requestTimestamps.length = 0;
  requestTimestamps.push(...recentRequests);
  
  return requestTimestamps.length < MAX_REQUESTS_PER_MINUTE;
}

// Markiert eine neue Anfrage
function markRequest() {
  requestTimestamps.push(Date.now());
}

// Validiert Text-Input
function validateText(text: string): string {
  if (!text || typeof text !== 'string') {
    throw new Error('Text ist leer oder ungültig');
  }
  
  const trimmedText = text.trim();
  if (trimmedText.length === 0) {
    throw new Error('Text ist leer nach Trimmen');
  }
  
  if (trimmedText.length > 8000) {
    console.warn(`Text zu lang (${trimmedText.length} Zeichen), wird gekürzt`);
    return trimmedText.substring(0, 8000);
  }
  
  return trimmedText;
}

// Übersetzt einen einzelnen Text
async function translateSingleText(
  text: string,
  targetLang: string,
  sourceLang: string,
  userId: string = 'bulk-translation',
  isPremium: boolean = false
): Promise<string> {
  const prompt = `Übersetze den folgenden Text vom ${sourceLang} ins ${targetLang}.
Gib NUR die Übersetzung zurück, ohne Erklärungen oder zusätzliche Kommentare:

${text}`;

  const requestPayload = {
    model: 'mistral-large-latest',
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: 2000,
    temperature: 0.3,
  };

  // Verwende Queue Manager
  const queueResponse = await fetch(`${Deno.env.get("SUPABASE_URL")}/functions/v1/mistral-queue-manager`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${Deno.env.get("SUPABASE_ANON_KEY")}`,
    },
    body: JSON.stringify({
      userId: userId,
      isPremium: isPremium,
      requestData: requestPayload,
    }),
  });

  if (!queueResponse.ok) {
    const errorText = await queueResponse.text();
    throw new Error(`Queue Manager Fehler ${queueResponse.status}: ${errorText}`);
  }

  const data = await queueResponse.json();

  if (!data.choices || !data.choices[0] || !data.choices[0].message) {
    throw new Error('Ungültige Antwort von Mistral Queue');
  }

  return data.choices[0].message.content.trim();
}

serve(async (req) => {
  // CORS-Behandlung
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('🚀 Bulk Translation Request erhalten');

    // Rate Limiting prüfen
    if (!canMakeRequest()) {
      console.warn('⚠️ Rate limit erreicht für Bulk Translation');
      return new Response(
        JSON.stringify({ 
          error: 'Rate limit erreicht. Bitte warten Sie einen Moment.', 
          success: false 
        }),
        { 
          status: 429, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Anfrage markieren
    markRequest();

    // Request Body parsen
    const body: BulkTranslationRequest = await req.json();
    console.log(`📦 Bulk Translation: ${body.items.length} Items zu übersetzen`);

    // Validierung
    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      throw new Error('Keine Items zum Übersetzen gefunden');
    }

    if (body.items.length > 15) {
      throw new Error('Zu viele Items (Maximum: 15)');
    }

    if (!body.target_lang || !body.source_lang) {
      throw new Error('Quell- und Zielsprache sind erforderlich');
    }

    console.log(`🌐 Übersetze von ${body.source_lang} nach ${body.target_lang}`);

    const results: BulkTranslationResponse['results'] = [];
    let processedCount = 0;
    let failedCount = 0;

    // Verarbeite Items in kleineren Batches (3-5 gleichzeitig)
    const batchSize = 3;
    for (let i = 0; i < body.items.length; i += batchSize) {
      const batch = body.items.slice(i, i + batchSize);
      console.log(`🔄 Verarbeite Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(body.items.length/batchSize)}`);

      // Verarbeite Batch parallel
      const batchPromises = batch.map(async (item) => {
        try {
          const validatedText = validateText(item.text);
          console.log(`📝 Übersetze Item ${item.id}: "${validatedText.substring(0, 50)}..."`);
          
          // Retry-Logik für einzelne Items
          let translatedText: string;
          let retryCount = 0;
          const maxRetries = 2;

          while (retryCount <= maxRetries) {
            try {
              translatedText = await translateSingleText(validatedText, body.target_lang, body.source_lang);
              break;
            } catch (error) {
              retryCount++;
              if (retryCount > maxRetries) {
                throw error;
              }
              console.warn(`⚠️ Retry ${retryCount}/${maxRetries} für Item ${item.id}: ${error.message}`);
              await delay(Math.pow(2, retryCount) * 1000); // Exponential backoff
            }
          }

          console.log(`✅ Item ${item.id} erfolgreich übersetzt`);
          return {
            id: item.id,
            translated_text: translatedText!,
            success: true
          };
        } catch (error) {
          console.error(`❌ Fehler bei Item ${item.id}: ${error.message}`);
          return {
            id: item.id,
            translated_text: item.text, // Fallback: Original-Text
            success: false,
            error: error.message
          };
        }
      });

      // Warte auf Batch-Completion
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Zähle Erfolge/Fehler
      for (const result of batchResults) {
        processedCount++;
        if (!result.success) {
          failedCount++;
        }
      }

      // Kurze Pause zwischen Batches um Rate Limits zu vermeiden
      if (i + batchSize < body.items.length) {
        await delay(500);
      }
    }

    const response: BulkTranslationResponse = {
      success: failedCount < body.items.length,
      results,
      total_processed: processedCount,
      failed_count: failedCount
    };

    console.log(`🎉 Bulk Translation abgeschlossen: ${processedCount - failedCount}/${processedCount} erfolgreich`);

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Bulk Translation Fehler:', error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message, 
        success: false,
        results: [],
        total_processed: 0,
        failed_count: 0
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
}); 
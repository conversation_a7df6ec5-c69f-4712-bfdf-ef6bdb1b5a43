import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';
import { corsHeaders } from '../_shared/cors.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.23.0';
import { encode as base64Encode } from 'https://deno.land/std@0.177.0/encoding/base64.ts';
import { decode as base64Decode } from 'https://deno.land/std@0.177.0/encoding/base64.ts';

interface GoogleCredentials {
  client_email: string;
  private_key: string;
  project_id: string;
}

// Hauptfunktion zum Extrahieren von Text aus einem PDF mit der Google Cloud Vision API
async function extractTextFromPdf(storagePath: string, supabaseClient: any): Promise<string | { error: string }> {
  try {
    console.log(`[extractTextFromPdf] Starte Verarbeitung für: ${storagePath}`);
    
    // NEU: Logge ALLE verfügbaren Umgebungsvariablen am Anfang
    try {
      const allEnvVars = Deno.env.toObject();
      console.log(`[extractTextFromPdf] Verfügbare Umgebungsvariablen: ${JSON.stringify(allEnvVars)}`);
    } catch (envError) {
      console.error('[extractTextFromPdf] Fehler beim Auslesen der Umgebungsvariablen:', envError);
    }

    // Hole den temporären Download-URL für die Datei
    const { data: fileData, error: fileError } = await supabaseClient
      .storage
      .from('cv-backups')
      .createSignedUrl(storagePath, 60); // 60 Sekunden gültig

    if (fileError || !fileData?.signedUrl) {
      const errorMessage = 'Fehler beim Abrufen der Signed URL: ' + (fileError?.message || 'Keine URL erhalten');
      console.error(`[extractTextFromPdf] ${errorMessage}`);
      return { error: `Datei konnte nicht gefunden werden oder URL nicht erhalten: ${storagePath}` };
    }
    console.log(`[extractTextFromPdf] Signed URL erhalten: ${fileData.signedUrl.substring(0, 100)}...`);

    // Lade die PDF-Datei herunter
    const pdfResponse = await fetch(fileData.signedUrl);
    if (!pdfResponse.ok) {
      console.error(`[extractTextFromPdf] Fehler beim Herunterladen der PDF: ${pdfResponse.status} ${pdfResponse.statusText}`);
      return { error: `Datei konnte nicht heruntergeladen werden: ${pdfResponse.statusText}` };
    }
    console.log('[extractTextFromPdf] PDF erfolgreich heruntergeladen.');

    const pdfBuffer = await pdfResponse.arrayBuffer();
    console.log(`[extractTextFromPdf] PDF Buffer Größe: ${pdfBuffer.byteLength} bytes`);
    
    const pdfBase64 = base64Encode(pdfBuffer);
    console.log(`[extractTextFromPdf] PDF in Base64 konvertiert (Länge: ${pdfBase64.length}) mit std/encoding/base64`);

    // Lade Google Credentials (kritisch, jetzt wieder mit Abbruch)
    const googleCredentialsJson = Deno.env.get('G_CREDS');
    if (!googleCredentialsJson) {
        console.error('[extractTextFromPdf] G_CREDS Secret nicht gefunden.');
        return { error: 'Google Cloud Credentials (G_CREDS) sind nicht konfiguriert (Secret fehlt).' };
    }
    let googleCredentials: GoogleCredentials;
    try {
        googleCredentials = JSON.parse(googleCredentialsJson) as GoogleCredentials;
    } catch (parseError) {
        console.error('[extractTextFromPdf] Fehler beim Parsen der G_CREDS:', parseError);
        return { error: `Fehler beim Parsen der Google Credentials (G_CREDS): ${parseError.message}` };
    }
    
    if (!googleCredentials.client_email || !googleCredentials.private_key) {
      console.error('[extractTextFromPdf] G_CREDS Credentials unvollständig (E-Mail oder Schlüssel fehlt).');
      return { error: 'Google Cloud Credentials (G_CREDS) sind nicht konfiguriert (unvollständig).' };
    }
    console.log(`[extractTextFromPdf] G_CREDS Credentials geladen für: ${googleCredentials.client_email}`);

    // JWT Token erstellen
    const jwtResult = await createGoogleJwt(googleCredentials);
    if (typeof jwtResult === 'object' && jwtResult.error) {
        console.error('[extractTextFromPdf] Fehler beim Erstellen des JWT:', jwtResult.error);
        return { error: jwtResult.error }; 
    }
    const jwt = jwtResult; 
    if (typeof jwt === 'string') { 
      console.log(`[extractTextFromPdf] Google JWT erfolgreich erstellt (Länge: ${jwt.length})`);
    } else {
      console.error('[extractTextFromPdf] JWT ist kein String nach Fehlerprüfung!');
      return { error: 'Interner Fehler bei der JWT-Verarbeitung' };
    }

    // Google Cloud Vision API aufrufen
    const visionApiKey = Deno.env.get('GOOGLE_API_KEY');
    if (!visionApiKey) {
        console.error('[extractTextFromPdf] GOOGLE_API_KEY Secret nicht gefunden.');
        return { error: 'Google API Key nicht konfiguriert.' };
    }
    const visionApiUrl = `https://vision.googleapis.com/v1/files:annotate?key=${visionApiKey}`;
    console.log(`[extractTextFromPdf] Rufe Google Vision API auf: ${visionApiUrl}`);
    
    const visionResponse = await fetch(
      visionApiUrl,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwt}`,
        },
        body: JSON.stringify({
          requests: [{
            inputConfig: {
              mimeType: 'application/pdf',
              content: pdfBase64,
            },
            features: [{ type: 'DOCUMENT_TEXT_DETECTION' }],
            // Entferne Seitenbeschränkung, um das ganze Dokument zu verarbeiten
            // pages: [1, 2, 3, 4, 5], 
          }],
        }),
      }
    );

    console.log(`[extractTextFromPdf] Google Vision API Antwort Status: ${visionResponse.status}`);
    if (!visionResponse.ok) {
      const errorText = await visionResponse.text();
      console.error(`[extractTextFromPdf] Google Vision API Fehler: ${visionResponse.status} ${visionResponse.statusText} - Body: ${errorText}`);
      return { error: `Fehler bei der OCR-Verarbeitung durch Google Vision: ${visionResponse.statusText} - ${errorText}` };
    }

    const visionResult = await visionResponse.json();
    // NEU: Logge die gesamte Antwort von Google Vision
    console.log(`[extractTextFromPdf] Komplette Google Vision API Antwort: ${JSON.stringify(visionResult)}`); 
    
    let extractedText = '';
    if (
      visionResult.responses && 
      visionResult.responses.length > 0 && 
      visionResult.responses[0].responses && // Zusätzliche Ebene prüfen
      visionResult.responses[0].responses.length > 0 &&
      visionResult.responses[0].responses[0].fullTextAnnotation &&
      visionResult.responses[0].responses[0].fullTextAnnotation.text
    ) {
      // Nur die erste (und einzige) Antwort auswerten
      extractedText = visionResult.responses[0].responses[0].fullTextAnnotation.text;
    } else {
        console.warn('[extractTextFromPdf] Keine fullTextAnnotation im erwarteten Format gefunden in der Vision API Antwort.');
    }
    
    console.log(`[extractTextFromPdf] Text extrahiert (Länge: ${extractedText.length}). Gebe Ergebnis zurück.`);
    return extractedText.trim();

  } catch (error) {
    // Dieser Catch fängt unerwartete Fehler innerhalb des Try-Blocks
    const errorMessage = `Unerwarteter Fehler in extractTextFromPdf: ${error.message}`;
    console.error('[extractTextFromPdf] Catch-Block erreicht:', error); 
    return { error: errorMessage }; 
  }
}

// Hilfsfunktion zur Erstellung eines JWT-Tokens für die Google Cloud API
async function createGoogleJwt(credentials: GoogleCredentials): Promise<string | { error: string }> {
  try {
    console.log('[createGoogleJwt] Starte JWT-Erstellung.');
    const header = {
      alg: 'RS256',
      typ: 'JWT',
    };

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: credentials.client_email,
      sub: credentials.client_email,
      aud: 'https://vision.googleapis.com/', // Audience für Vision API
      iat: now,
      exp: now + 3600, // 1 Stunde gültig
    };
    console.log('[createGoogleJwt] Payload erstellt:', payload);

    // Korrigiertes Parsen des PEM Private Keys
    const pemKey = credentials.private_key.replace(/\\n/g, '\n');
    const pemHeader = '-----BEGIN PRIVATE KEY-----';
    const pemFooter = '-----END PRIVATE KEY-----';

    if (!pemKey.startsWith(pemHeader) || !pemKey.includes(pemFooter)) {
        console.error('[createGoogleJwt] Ungültiges PEM Private Key Format (Header/Footer fehlen).');
        return { error: 'Ungültiges PEM Private Key Format (Header/Footer fehlen)' };
    }

    // Extrahiere den Base64-Teil
    const base64Key = pemKey
        .substring(pemHeader.length, pemKey.indexOf(pemFooter))
        .replace(/\s+/g, ''); // Entferne alle Whitespaces (inkl. Newlines)
    console.log(`[createGoogleJwt] Base64-Teil des Schlüssels extrahiert (Länge: ${base64Key.length})`);

    // Importiere den Schlüssel
    let importedKey;
    try {
        const keyBuffer = base64Decode(base64Key); // Dekodiere nur den Base64-Teil
        console.log('[createGoogleJwt] Schlüssel dekodiert, versuche Import...');
        importedKey = await crypto.subtle.importKey(
            'pkcs8',         // Format des dekodierten Schlüssels
            keyBuffer,       // Der dekodierte Schlüssel als ArrayBuffer
            { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },
            false,           // nicht exportierbar
            ['sign']         // Zweck: Signieren
        );
        console.log('[createGoogleJwt] Schlüssel erfolgreich importiert.');
    } catch (importError) {
        console.error('[createGoogleJwt] Fehler beim Importieren des Schlüssels:', importError);
        return { error: `Fehler beim Importieren des privaten Schlüssels: ${importError.message}` };
    }

    // Erstelle die zu signierenden Daten (Header + Payload)
    const encodedHeader = btoa(JSON.stringify(header)).replace(/=+$/, '').replace(/\+/g, '-').replace(/\//g, '_');
    const encodedPayload = btoa(JSON.stringify(payload)).replace(/=+$/, '').replace(/\+/g, '-').replace(/\//g, '_');
    const unsignedToken = `${encodedHeader}.${encodedPayload}`;
    console.log(`[createGoogleJwt] Unsignierter Token erstellt: ${unsignedToken.substring(0, 60)}...`);

    // Signiere die Daten
    let signature;
    try {
        const encoder = new TextEncoder();
        const signatureData = encoder.encode(unsignedToken);
        console.log('[createGoogleJwt] Versuche Token zu signieren...');
        signature = await crypto.subtle.sign(
            { name: 'RSASSA-PKCS1-v1_5' },
            importedKey,
            signatureData
        );
        console.log('[createGoogleJwt] Token erfolgreich signiert.');
    } catch (signError) {
        console.error('[createGoogleJwt] Fehler beim Signieren des Tokens:', signError);
        return { error: `Fehler beim Signieren des Tokens: ${signError.message}` };
    }

    // Kodiere die Signatur (Base64URL)
    const encodedSignature = btoa(String.fromCharCode(...new Uint8Array(signature)))
        .replace(/=+$/, '')
        .replace(/\+/g, '-')
        .replace(/\//g, '_');
    console.log(`[createGoogleJwt] Signatur kodiert: ${encodedSignature.substring(0, 60)}...`);

    // Kombiniere zum vollständigen JWT
    const fullJwt = `${unsignedToken}.${encodedSignature}`;
    console.log(`[createGoogleJwt] Vollständiger JWT erstellt (Länge: ${fullJwt.length}).`);
    return fullJwt;

  } catch (error) {
    console.error('[createGoogleJwt] Unerwarteter Fehler:', error);
    return { error: `Unerwarteter Fehler beim Erstellen des JWT: ${error.message}` };
  }
}

// Hauptfunktion für die Edge Function
serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);
  // OPTIONS-Anfragen für CORS behandeln
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }
  
  try {
    // Supabase-Client initialisieren
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    if (!supabaseUrl || !supabaseServiceRoleKey) {
        console.error('[Serve] Supabase URL oder Service Role Key nicht konfiguriert.');
        return new Response(JSON.stringify({ error: 'Serverkonfigurationsfehler (Supabase)', success: false }), 
               { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 });
    }
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
    console.log('[Serve] Supabase Admin Client initialisiert.');

    // Request-Daten parsen
    let storagePath: string | undefined;
    try {
      const body = await req.json();
      storagePath = body.storagePath;
      console.log(`[Serve] Request Body geparsed. Storage Path: ${storagePath}`);
    } catch (parseError) {
      console.error('[Serve] Fehler beim Parsen des JSON Request Body:', parseError);
      return new Response(JSON.stringify({ error: `Ungültiges JSON-Format: ${parseError.message}`, success: false }), 
             { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 });
    }
    
    // Validierung
    if (!storagePath || typeof storagePath !== 'string') {
      console.error(`[Serve] Ungültiger storagePath Parameter: ${storagePath}`);
      return new Response(JSON.stringify({ error: 'Bitte einen gültigen storagePath angeben', success: false }), 
             { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 });
    }
    
    // OCR durchführen
    console.log(`[Serve] Rufe extractTextFromPdf für ${storagePath} auf.`);
    const result = await extractTextFromPdf(storagePath, supabaseAdmin);

    // Prüfen, ob ein Fehlerobjekt zurückgegeben wurde
    if (typeof result === 'object' && result.error) {
      console.error(`[Serve] Fehler von extractTextFromPdf erhalten: ${result.error}`);
      return new Response(JSON.stringify({ error: result.error, success: false }),
             { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 });
    }
    
    // Linter-Fix: Stelle sicher, dass result ein String ist, bevor .length verwendet wird
    const resultText = (typeof result === 'string') ? result : '';
    console.log(`[Serve] Erfolgreiche Verarbeitung für ${storagePath}. Sende Ergebnis (Textlänge: ${resultText.length}).`);
    return new Response(JSON.stringify({ extractedText: resultText || null, success: !!resultText }),
           { headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
    
  } catch (error) {
    // Dieser Catch fängt unerwartete Fehler im Haupt-Request-Handler
    console.error('[Serve] Unerwarteter Fehler im Haupt-Handler:', error);
    return new Response(JSON.stringify({ error: `Unerwarteter Serverfehler: ${error.message}`, success: false }),
           { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 });
  }
}); 
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";

// CORS headers for preflight requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

/**
 * Enhanced Edge Function to check email confirmation status
 * Supports both token verification and email-based status checks
 * Improved logging and error handling
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  const requestId = crypto.randomUUID().substring(0, 8);
  
  console.log(`[${requestId}] Email confirmation check started - Method: ${req.method}`);
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log(`[${requestId}] CORS preflight request handled`);
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    let token = url.searchParams.get('token');
    let email = url.searchParams.get('email');
    let type = url.searchParams.get('type'); // 'signup', 'recovery', etc.
    
    // Also check for parameters in request body (for function invocation)
    if (req.method === 'POST') {
      try {
        const body = await req.json();
        token = token || body.token;
        email = email || body.email;
        type = type || body.type;
        console.log(`[${requestId}] Request body parsed successfully`);
      } catch (bodyError) {
        console.log(`[${requestId}] No JSON body or invalid JSON, using URL parameters only:`, bodyError instanceof Error ? bodyError.message : String(bodyError));
      }
    }
    
    console.log(`[${requestId}] Parameters - Token: ${token ? 'present' : 'none'}, Email: ${email || 'none'}, Type: ${type || 'signup'}`);

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    
    // Create clients
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
    const publicSupabase = createClient(supabaseUrl, supabaseAnonKey);

    if (token) {
      console.log(`[${requestId}] Starting token verification with type: ${type || 'signup'}`);
      
      // Verify the confirmation token
      try {
        const { data, error } = await publicSupabase.auth.verifyOtp({
          token_hash: token,
          type: (type as any) || 'signup'
        });

        if (error) {
          console.log(`[${requestId}] Token verification failed:`, error.message);
          
          // Check if it's an already confirmed token by looking at the error
          if (error.message.includes('already') || error.message.includes('confirmed')) {
            console.log(`[${requestId}] Token already confirmed`);
            return new Response(
              JSON.stringify({ 
                status: 'already_confirmed',
                message: 'Diese E-Mail-Adresse wurde bereits bestätigt.',
                requestId 
              }),
              { 
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 200 
              }
            );
          }
          
          // Check for expired token
          if (error.message.includes('expired') || error.message.includes('invalid')) {
            console.log(`[${requestId}] Token expired or invalid`);
            return new Response(
              JSON.stringify({ 
                status: 'invalid_token',
                message: 'Der Bestätigungslink ist ungültig oder abgelaufen.',
                requestId 
              }),
              { 
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 400 
              }
            );
          }
          
          console.log(`[${requestId}] Unknown token error:`, error.message);
          return new Response(
            JSON.stringify({ 
              status: 'invalid_token',
              message: 'Der Bestätigungslink ist ungültig oder abgelaufen.',
              requestId 
            }),
            { 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 400 
            }
          );
        }

        if (data.user) {
          console.log(`[${requestId}] Email confirmed successfully for user:`, data.user.email);
          const duration = Date.now() - startTime;
          console.log(`[${requestId}] Request completed successfully in ${duration}ms`);
          
          return new Response(
            JSON.stringify({ 
              status: 'confirmed',
              message: 'E-Mail erfolgreich bestätigt!',
              user: {
                email: data.user.email,
                id: data.user.id
              },
              requestId 
            }),
            { 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 200 
            }
          );
        }
        
        console.log(`[${requestId}] Token verification succeeded but no user data returned`);
        return new Response(
          JSON.stringify({ 
            status: 'error',
            message: 'Bestätigung fehlgeschlagen - keine Benutzerdaten erhalten.',
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500 
          }
        );
        
      } catch (tokenError) {
        console.error(`[${requestId}] Token verification error:`, tokenError);
        return new Response(
          JSON.stringify({ 
            status: 'invalid_token',
            message: 'Der Bestätigungslink ist ungültig oder abgelaufen.',
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        );
      }
    }

    if (email) {
      console.log(`[${requestId}] Checking email confirmation status for: ${email}`);
      
      // Check email confirmation status by querying auth.users
      const { data: users, error } = await supabaseAdmin.auth.admin.listUsers();
      
      if (error) {
        console.error(`[${requestId}] Error fetching users:`, error);
        return new Response(
          JSON.stringify({ 
            status: 'error',
            message: 'Fehler beim Überprüfen des E-Mail-Status.',
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500 
          }
        );
      }

      console.log(`[${requestId}] Found ${users.users.length} total users in system`);
      
      // Find users with the given email
      const userWithEmail = users.users.find((u: any) => u.email === email);
      
      if (!userWithEmail) {
        console.log(`[${requestId}] No user found with email: ${email}`);
        return new Response(
          JSON.stringify({ 
            status: 'not_found',
            message: 'Kein Benutzer mit dieser E-Mail-Adresse gefunden.',
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404 
          }
        );
      }

      console.log(`[${requestId}] User found - ID: ${userWithEmail.id}, Confirmed: ${!!userWithEmail.email_confirmed_at}`);
      
      // Check if email is confirmed
      if (userWithEmail.email_confirmed_at) {
        const duration = Date.now() - startTime;
        console.log(`[${requestId}] Email already confirmed at: ${userWithEmail.email_confirmed_at}`);
        console.log(`[${requestId}] Request completed in ${duration}ms`);
        
        return new Response(
          JSON.stringify({ 
            status: 'already_confirmed',
            message: 'Diese E-Mail-Adresse wurde bereits bestätigt.',
            confirmedAt: userWithEmail.email_confirmed_at,
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200 
          }
        );
      } else {
        const duration = Date.now() - startTime;
        console.log(`[${requestId}] Email not yet confirmed`);
        console.log(`[${requestId}] Request completed in ${duration}ms`);
        
        return new Response(
          JSON.stringify({ 
            status: 'not_confirmed',
            message: 'Diese E-Mail-Adresse ist noch nicht bestätigt.',
            requestId 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200 
          }
        );
      }
    }

    // No token or email provided
    console.log(`[${requestId}] No token or email provided in request`);
    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Request failed in ${duration}ms - missing parameters`);
    
    return new Response(
      JSON.stringify({ 
        status: 'error',
        message: 'Token oder E-Mail-Parameter erforderlich.',
        requestId 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    );

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Unexpected error after ${duration}ms:`, error);
    
    return new Response(
      JSON.stringify({ 
        status: 'error',
        message: 'Ein unerwarteter Fehler ist aufgetreten.',
        requestId 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
});
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body to get user credentials or token
    let authToken = null
    let userEmail = null

    // Try to get auth token from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      authToken = authHeader.substring(7)
    }

    // Try to get credentials from request body
    if (req.method === 'POST') {
      try {
        const body = await req.json()
        userEmail = body.email
        authToken = body.token || authToken
      } catch (e) {
        // Body parsing failed, continue with header token
      }
    }

    console.log('Server-side onboarding completion started')
    console.log('Auth token present:', !!authToken)
    console.log('User email provided:', !!userEmail)

    // Create Supabase client for server-side operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', // Use service role for server-side
    )

    let user = null

    // Server-side authentication check
    if (authToken) {
      // Verify the provided token
      const { data: tokenUser, error: tokenError } = await supabaseClient.auth.getUser(authToken)
      if (!tokenError && tokenUser.user) {
        user = tokenUser.user
        console.log('User authenticated via token:', user.id)
      }
    }

    if (!user && userEmail) {
      // Try to find user by email in auth.users (server-side lookup)
      const { data: users, error: usersError } = await supabaseClient.auth.admin.listUsers()
      if (!usersError && users.users) {
        user = users.users.find(u => u.email === userEmail)
        if (user) {
          console.log('User found by email lookup:', user.id)
        }
      }
    }

    if (!user) {
      console.log('No user found - server-side authentication failed')
      return new Response(
        JSON.stringify({
          error: 'Server-side authentication failed - User not found',
          success: false,
          isAuthenticated: false
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    console.log(`Schließe Onboarding ab für User: ${user.id}`)

    // Check if profile exists
    const { data: existingProfile, error: checkError } = await supabaseClient
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .maybeSingle()

    if (checkError) {
      console.error('Fehler beim Prüfen des Profils:', checkError)
      return new Response(
        JSON.stringify({ 
          error: 'Fehler beim Prüfen des Profils',
          success: false 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    if (!existingProfile) {
      // Create new profile with onboarding complete
      const { error: insertError } = await supabaseClient
        .from('profiles')
        .insert({
          id: user.id,
          data: {
            email: user.email || '',
            firstName: '',
            lastName: '',
            profileCompleteness: 0,
          },
          onboarding_complete: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

      if (insertError) {
        console.error('Fehler beim Erstellen des Profils:', insertError)
        return new Response(
          JSON.stringify({ 
            error: 'Fehler beim Erstellen des Profils',
            success: false 
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        )
      }

      console.log(`Neues Profil mit abgeschlossenem Onboarding erstellt für User ${user.id}`)
    } else {
      // Update existing profile
      const { error: updateError } = await supabaseClient
        .from('profiles')
        .update({
          onboarding_complete: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)

      if (updateError) {
        console.error('Fehler beim Aktualisieren des Profils:', updateError)
        return new Response(
          JSON.stringify({ 
            error: 'Fehler beim Aktualisieren des Profils',
            success: false 
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        )
      }

      console.log(`Onboarding als abgeschlossen markiert für User ${user.id}`)
    }

    const result = {
      success: true,
      message: 'Onboarding erfolgreich abgeschlossen',
      userId: user.id,
      userEmail: user.email,
      isAuthenticated: true,
      serverSideCheck: true,
      timestamp: new Date().toISOString(),
    }

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Unerwarteter Fehler:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Unerwarteter Fehler',
        success: false 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

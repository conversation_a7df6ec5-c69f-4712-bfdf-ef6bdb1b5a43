/// <reference types="https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts" />
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

/**
 * Typ für Kaufverifizierungsanfrage
 */
interface VerifyPurchaseRequest {
  userId: string;
  platform: "ios" | "android";
  receiptData: string;
  transactionId?: string;
  purchaseToken?: string; // Für Android
  packageName?: string; // Für Android
  productId: string;
}

/**
 * Typ für Kaufverifizierungsantwort
 */
interface VerifyPurchaseResponse {
  isValid: boolean;
  subscriptionId?: string;
  expireDate?: string;
  errorCode?: string;
  errorMessage?: string;
}

/**
 * Env-Variablen
 */
const APPLE_SHARED_SECRET = Deno.env.get("APPLE_SHARED_SECRET") || "";
const IS_SANDBOX_MODE = Deno.env.get("IS_SANDBOX_MODE") === "true";
// Service-Account JSON: bevorzugt GOOGLE_PLAY_SERVICE_ACCOUNT_JSON, Fallback GOOGLE_SERVICE_ACCOUNT
const GOOGLE_SERVICE_ACCOUNT = Deno.env.get("GOOGLE_PLAY_SERVICE_ACCOUNT_JSON")
  || Deno.env.get("GOOGLE_SERVICE_ACCOUNT")
  || "{}";

// Apple App Store Verifikations-URLs
const APPLE_PRODUCTION_URL = "https://buy.itunes.apple.com/verifyReceipt";
const APPLE_SANDBOX_URL = "https://sandbox.itunes.apple.com/verifyReceipt";

// Supabase-Client erzeugen
const supabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SERVICE_ROLE_KEY") ?? ""
);

/**
 * Überprüft einen iOS-Kauf über die Apple Verification API
 */
async function verifyIosPurchase(
  receiptData: string,
  transactionId?: string
): Promise<VerifyPurchaseResponse> {
  try {
    console.log("Überprüfe iOS-Kauf");
    
    // Zuerst die Produktion-URL versuchen
    let verificationUrl = APPLE_PRODUCTION_URL;
    if (IS_SANDBOX_MODE) {
      verificationUrl = APPLE_SANDBOX_URL;
    }
    
    // Anfrage an Apple senden
    const response = await fetch(verificationUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "receipt-data": receiptData,
        "password": APPLE_SHARED_SECRET,
        "exclude-old-transactions": true
      }),
    });
    
    if (!response.ok) {
      console.error(`Apple API Error: ${response.status}`);
      return {
        isValid: false,
        errorCode: "api_error",
        errorMessage: `Apple API Status: ${response.status}`,
      };
    }
    
    const data = await response.json();
    
    // Status 21007 bedeutet, dass es eine Sandbox-Quittung ist, die an die Produktion-URL gesendet wurde
    if (data.status === 21007 && verificationUrl !== APPLE_SANDBOX_URL) {
      console.log("Quittung ist für Sandbox, versuche Sandbox-URL");
      return verifyIosPurchase(receiptData, transactionId);
    }
    
    // Verifizierungsergebnis prüfen
    if (data.status !== 0) {
      return {
        isValid: false,
        errorCode: `apple_status_${data.status}`,
        errorMessage: `Apple Verification Failed: Status ${data.status}`,
      };
    }
    
    // Abonnement-Details extrahieren
    if (data.receipt && data.latest_receipt_info) {
      // Verwende das neueste Abonnement
      const latestTransaction = data.latest_receipt_info.sort((a: any, b: any) => 
        parseInt(b.purchase_date_ms) - parseInt(a.purchase_date_ms)
      )[0];
      
      // Prüfe, ob das Abonnement noch aktiv ist
      const expireDate = new Date(parseInt(latestTransaction.expires_date_ms));
      const isActive = expireDate > new Date();
      
      if (!isActive) {
        return {
          isValid: false,
          expireDate: expireDate.toISOString(),
          errorCode: "subscription_expired",
          errorMessage: "Das Abonnement ist abgelaufen",
        };
      }
      
      return {
        isValid: true,
        subscriptionId: latestTransaction.original_transaction_id,
        expireDate: expireDate.toISOString(),
      };
    }
    
    return {
      isValid: false,
      errorCode: "invalid_receipt_data",
      errorMessage: "Keine gültigen Abonnementdaten in der Quittung",
    };
  } catch (error) {
    console.error("Fehler bei der iOS-Kaufüberprüfung:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      isValid: false,
      errorCode: "verification_error",
      errorMessage: `Verification Error: ${errorMessage}`,
    };
  }
}

/**
 * Base64URL encode function
 */
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Konvertiert PEM private key zu ArrayBuffer
 */
function pemToArrayBuffer(pem: string): ArrayBuffer {
  const pemHeader = "-----BEGIN PRIVATE KEY-----";
  const pemFooter = "-----END PRIVATE KEY-----";
  const pemContents = pem.replace(pemHeader, "").replace(pemFooter, "").replace(/\s/g, "");
  const binaryString = atob(pemContents);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Erstellt ein JWT für Google API Authentifizierung
 */
async function createJWT(serviceAccount: any): Promise<string> {
  const header = {
    alg: "RS256",
    typ: "JWT"
  };

  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iss: serviceAccount.client_email,
    scope: "https://www.googleapis.com/auth/androidpublisher",
    aud: "https://oauth2.googleapis.com/token",
    exp: now + 3600, // 1 Stunde
    iat: now
  };

  // Encode header und payload
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));

  // Erstelle Signatur
  const signatureInput = `${encodedHeader}.${encodedPayload}`;

  // Import private key
  const privateKeyBuffer = pemToArrayBuffer(serviceAccount.private_key);
  const privateKey = await crypto.subtle.importKey(
    "pkcs8",
    privateKeyBuffer,
    {
      name: "RSASSA-PKCS1-v1_5",
      hash: "SHA-256"
    },
    false,
    ["sign"]
  );

  // Sign
  const signature = await crypto.subtle.sign(
    "RSASSA-PKCS1-v1_5",
    privateKey,
    new TextEncoder().encode(signatureInput)
  );

  const encodedSignature = base64UrlEncode(
    String.fromCharCode(...new Uint8Array(signature))
  );

  return `${signatureInput}.${encodedSignature}`;
}

/**
 * Holt ein Access Token von Google OAuth
 */
async function getGoogleAccessToken(serviceAccount: any): Promise<string> {
  try {
    const jwt = await createJWT(serviceAccount);

    const response = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      body: new URLSearchParams({
        grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
        assertion: jwt
      })
    });

    if (!response.ok) {
      throw new Error(`OAuth failed: ${response.status} ${await response.text()}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error("Fehler beim Abrufen des Access Tokens:", error);
    throw error;
  }
}

/**
 * Überprüft einen Android-Kauf über die Google Play Developer API
 */
async function verifyAndroidPurchase(
  purchaseToken: string,
  packageName: string,
  productId: string
): Promise<VerifyPurchaseResponse> {
  try {
    console.log("Überprüfe Android-Kauf mit Google Play Developer API");

    // Im Testmodus simulieren wir eine erfolgreiche Überprüfung
    if (IS_SANDBOX_MODE) {
      console.log("Android-Kaufüberprüfung im Testmodus, simuliere Erfolg");
      return {
        isValid: true,
        subscriptionId: `sandbox_${productId}_${Date.now()}`,
        expireDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      };
    }

    // Parse Service Account JSON
    const serviceAccount = JSON.parse(GOOGLE_SERVICE_ACCOUNT);

    // Hole Access Token
    const accessToken = await getGoogleAccessToken(serviceAccount);

    const headers = {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    } as const;

    // 1) Neuer Endpunkt SubscriptionsV2 (bevorzugt)
    const v2Url = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${encodeURIComponent(packageName)}/purchases/subscriptionsv2/tokens/${encodeURIComponent(purchaseToken)}`;
    let resp = await fetch(v2Url, { method: "GET", headers });
    if (resp.ok) {
      const data = await resp.json();
      console.log("Google Play v2 Antwort:", JSON.stringify(data));
      const state: number | undefined = data?.subscriptionState;
      const lineItem = data?.lineItems?.[0];
      const expiryTime = lineItem?.expiryTime ? new Date(lineItem.expiryTime).getTime() : undefined;
      const isActive = (state === 2) || (expiryTime ? expiryTime > Date.now() : false);
      if (!isActive) {
        return {
          isValid: false,
          errorCode: "subscription_invalid",
          errorMessage: `v2: state=${state}, expiry=${lineItem?.expiryTime}`,
          expireDate: lineItem?.expiryTime || undefined,
        };
      }
      return {
        isValid: true,
        subscriptionId: data?.externalAccountIdentifiers?.obfuscatedExternalAccountId || `google_${productId}_${Date.now()}`,
        expireDate: lineItem?.expiryTime ? new Date(lineItem.expiryTime).toISOString() : undefined,
      };
    }

    // 2) Fallback legacy v1 Endpoint
    const legacyUrl = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${encodeURIComponent(packageName)}/purchases/subscriptions/${encodeURIComponent(productId)}/tokens/${encodeURIComponent(purchaseToken)}`;
    resp = await fetch(legacyUrl, { method: "GET", headers });
    if (!resp.ok) {
      const errorText = await resp.text();
      console.error(`Google Play API Fehler: ${resp.status} - ${errorText}`);
      return {
        isValid: false,
        errorCode: `google_api_${resp.status}`,
        errorMessage: `Google Play API Error: ${resp.status} - ${errorText}`,
      };
    }
    const legacy = await resp.json();
    console.log("Google Play legacy Antwort:", JSON.stringify(legacy));
    const expiryMillis = legacy.expiryTimeMillis ? parseInt(legacy.expiryTimeMillis, 10) : undefined;
    const isExpired = expiryMillis ? Date.now() >= expiryMillis : true;
    const purchaseState = legacy.purchaseState; // 0=purchased
    const isActiveLegacy = !isExpired && purchaseState === 0;
    if (!isActiveLegacy) {
      return {
        isValid: false,
        errorCode: "subscription_invalid",
        errorMessage: `legacy: state=${purchaseState}, expiry=${legacy.expiryTimeMillis}`,
        expireDate: expiryMillis ? new Date(expiryMillis).toISOString() : undefined,
      };
    }
    return {
      isValid: true,
      subscriptionId: legacy.orderId || `google_${productId}_${Date.now()}`,
      expireDate: expiryMillis ? new Date(expiryMillis).toISOString() : undefined,
    };

  } catch (error) {
    console.error("Fehler bei der Android-Kaufüberprüfung:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      isValid: false,
      errorCode: "verification_error",
      errorMessage: `Verification Error: ${errorMessage}`,
    };
  }
}

/**
 * Speichert das Abonnement in der Datenbank
 */
async function storeSubscription(
  userId: string,
  platform: string,
  productId: string,
  receiptData: string,
  transactionId: string | undefined,
  verificationResult: VerifyPurchaseResponse,
  clientPlanType?: string
): Promise<string | null> {
  try {
    if (!verificationResult.isValid || !verificationResult.expireDate) {
      console.error("Ungültiges Verifizierungsergebnis");
      return null;
    }

    const now = new Date().toISOString();
    const planType = clientPlanType || (productId.includes('unlimited') ? 'unlimited' : (productId.includes('pro') ? 'pro' : 'basic'));

    // 1) Versuche bestehendes aktives Abo des Users zu finden
    const { data: existingActive, error: existingErr } = await supabaseClient
      .from('subscriptions')
      .select('id, plan_type, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (existingErr) {
      console.warn('Warnung: Fehler beim Abfragen bestehender Abos:', existingErr);
    }

    // Gemeinsame Update-Daten
    const updateData: Record<string, any> = {
      status: 'active',
      product_id: productId,
      platform: platform,
      plan_type: planType,
      start_date: now,
      end_date: verificationResult.expireDate,
      expires_at: verificationResult.expireDate,
      is_premium: planType !== 'basic',
      auto_renew: true,
      transaction_id: transactionId || verificationResult.subscriptionId,
      receipt_data: receiptData,
      payment_provider: platform,
      updated_at: now,
    };

    let subscriptionId: string | null = null;

    if (existingActive && existingActive.id) {
      // 2) Update statt Insert (UPSERT-Strategie)
      const { data: updated, error: updateErr } = await supabaseClient
        .from('subscriptions')
        .update(updateData)
        .eq('id', existingActive.id)
        .select()
        .single();

      if (updateErr) {
        console.error('Fehler beim Aktualisieren des bestehenden Abos:', updateErr);
        return null;
      }

      subscriptionId = updated.id;
    } else {
      // 3) Kein aktives Abo vorhanden: neu anlegen
      const insertData = {
        user_id: userId,
        created_at: now,
        ...updateData,
      };

      const { data: inserted, error: insertErr } = await supabaseClient
        .from('subscriptions')
        .insert(insertData)
        .select()
        .single();

      if (insertErr) {
        console.error('Fehler beim Erstellen des Abonnements:', insertErr);
        return null;
      }
      subscriptionId = inserted.id;
    }

    // 4) Kauf protokollieren
    const { error: purchaseError } = await supabaseClient
      .from('purchases')
      .insert({
        user_id: userId,
        subscription_id: subscriptionId,
        platform: platform,
        transaction_id: transactionId || verificationResult.subscriptionId,
        receipt_data: receiptData,
        purchase_date: now,
        verification_status: 'verified',
      });

    if (purchaseError) {
      // purchases Tabelle ist optional. Fehler nur loggen, Flow nicht abbrechen.
      console.warn('Warnung: Kauf konnte nicht protokolliert werden (optional):', purchaseError);
    }

    return subscriptionId;
  } catch (error) {
    console.error('Fehler beim Speichern des Abonnements:', error);
    return null;
  }
}

serve(async (req: Request) => {
  // CORS-Header für Browser-Anfragen
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  // CORS-Präflight-Anfragen beantworten
  if (req.method === "OPTIONS") {
    return new Response(null, { headers, status: 204 });
  }
  
  // Nur POST-Anfragen akzeptieren
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { headers, status: 405 }
    );
  }

  try {
    // Anfragedaten verarbeiten
    const requestDataRaw = await req.json();
    // Plattform standardmäßig android setzen, wenn nicht angegeben
    const requestData: VerifyPurchaseRequest = {
      userId: requestDataRaw.userId,
      platform: (requestDataRaw.platform || 'android') as 'ios' | 'android',
      productId: requestDataRaw.productId,
      receiptData: requestDataRaw.receiptData,
      transactionId: requestDataRaw.transactionId,
      purchaseToken: requestDataRaw.purchaseToken,
      packageName: requestDataRaw.packageName,
    };

    // Pflichtfelder prüfen
    if (!requestData.userId || !requestData.productId) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { headers, status: 400 }
      );
    }

    let verificationResult: VerifyPurchaseResponse;
    console.log("Anfrage erhalten:", JSON.stringify(requestData));

    // Je nach Plattform verifizieren
    if (requestData.platform === "ios") {
      if (!requestData.receiptData) {
        return new Response(
          JSON.stringify({ error: "Receipt data is required for iOS" }),
          { headers, status: 400 }
        );
      }
      verificationResult = await verifyIosPurchase(
        requestData.receiptData,
        requestData.transactionId
      );
    } else if (requestData.platform === "android") {
      const pkg = requestData.packageName || Deno.env.get('PLAY_PACKAGE_NAME') || 'com.einsteinai.app';
      // Fallback: viele Clients liefern das Token in receiptData
      const purchaseToken = requestData.purchaseToken || requestData.receiptData;
      if (!purchaseToken) {
        return new Response(
          JSON.stringify({ error: "Purchase token is required for Android" }),
          { headers, status: 400 }
        );
      }

      verificationResult = await verifyAndroidPurchase(
        purchaseToken,
        pkg,
        requestData.productId
      );
    } else {
      return new Response(
        JSON.stringify({ error: "Invalid platform" }),
        { headers, status: 400 }
      );
    }
    
    // Wenn der Kauf gültig ist, speichern wir das Abonnement in der Datenbank
    let subscriptionId: string | null = null;
    const inferredPlanType = (requestData as any).planType || (requestData.productId?.includes('unlimited') ? 'unlimited' : (requestData.productId?.includes('pro') ? 'pro' : 'basic'));

    if (verificationResult.isValid) {
      subscriptionId = await storeSubscription(
        requestData.userId,
        requestData.platform,
        requestData.productId,
        requestData.receiptData || '',
        requestData.transactionId || requestData.purchaseToken,
        verificationResult,
        inferredPlanType
      );
      console.log("StoreSubscription Ergebnis:", { subscriptionId, inferredPlanType });
    } else {
      console.warn("Verifizierung ungültig:", verificationResult);
    }

    // Erfolg nur, wenn Verifizierung OK und DB-Write erfolgreich
    const success = verificationResult.isValid && !!subscriptionId;
    const responseData: any = {
      success,
      subscriptionId: subscriptionId || verificationResult.subscriptionId,
      expireDate: verificationResult.expireDate,
      planType: inferredPlanType,
    };

    if (!success) {
      responseData.error = !verificationResult.isValid ? (verificationResult.errorMessage || verificationResult.errorCode || 'verification_failed') : 'db_write_failed';
      responseData.errorCode = !verificationResult.isValid ? (verificationResult.errorCode || 'verification_failed') : 'db_write_failed';
    }

    return new Response(
      JSON.stringify(responseData),
      { headers, status: 200 }
    );
  } catch (error) {
    console.error("Fehler bei der Kaufverifikation:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return new Response(
      JSON.stringify({
        isValid: false,
        errorCode: "server_error",
        errorMessage: `Server Error: ${errorMessage}`,
      }),
      { headers, status: 500 }
    );
  }
});
 
 
 
// Supabase Edge Function zur Überprüfung des Premium-Status eines Benutzers
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Typen für die Antwort
interface PremiumStatusResponse {
  success: boolean;
  isPremium: boolean;
  feature?: string;
  hasAccess?: boolean;
  error?: string;
  plan?: string;
  remainingApplications?: number | null;
  totalApplications?: number | null;
  isUnlimited?: boolean;
}

serve(async (req) => {
  // CORS-Header für alle Anfragen
  const headers = {
    ...corsHeaders,
    "Content-Type": "application/json",
  };

  // OPTIONS-Anfragen für CORS beantworten
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers,
      status: 204,
    });
  }

  try {
    // Daten aus der Anfrage extrahieren
    const { userId, feature } = await req.json();

    // Validierung
    if (!userId) {
      throw new Error("userId parameter is required");
    }

    // Supabase-Client erstellen
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // 1. Überprüfe den Premium-Status im UserProfile
    const { data: userProfile, error: userProfileError } = await supabaseClient
      .from("user_profiles")
      .select("is_premium, premium_expiry_date")
      .eq("user_id", userId)
      .single();

    if (userProfileError && userProfileError.code !== "PGRST116") {
      throw userProfileError;
    }

    // Prüfe, ob der Benutzer Premium ist und ob das Ablaufdatum in der Zukunft liegt
    let isPremium = false;
    if (userProfile && userProfile.is_premium) {
      if (!userProfile.premium_expiry_date ||
          new Date(userProfile.premium_expiry_date) > new Date()) {
        isPremium = true;
      }
    }

    // Wenn der Benutzer nicht Premium ist, prüfe auf aktives Abonnement
    let subscription = null;
    let planType = null;
    let remainingApplications = null;
    let totalApplications = null;
    let isUnlimited = false;

    if (!isPremium) {
      const { data: activeSub, error: subscriptionError } = await supabaseClient
        .from("subscriptions")
        .select("*")
        .eq("user_id", userId)
        .eq("status", "active")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (subscriptionError && subscriptionError.code !== "PGRST116") {
        throw subscriptionError;
      }

      if (activeSub) {
        subscription = activeSub;
        // Prüfen, ob das Abonnement abgelaufen ist
        const now = new Date();
        const endDate = subscription.end_date ? new Date(subscription.end_date) : null;

        // Ein Abonnement gilt als aktiv, wenn es entweder kein Enddatum hat oder das Enddatum in der Zukunft liegt
        isPremium = !endDate || endDate > now;

        // Wenn das Abonnement abgelaufen ist, aktualisiere den Status
        if (!isPremium && endDate && endDate < now) {
          await supabaseClient
            .from("subscriptions")
            .update({ status: "expired" })
            .eq("id", subscription.id);
        } else {
          // Abonnement ist aktiv, speichere den Plan-Typ
          planType = subscription.plan_type || "basic";
          remainingApplications = subscription.remaining_applications;
          totalApplications = subscription.total_applications;
          isUnlimited = planType === "unlimited";

          // Wenn der Plan unbegrenzt ist, setze remainingApplications und totalApplications auf null
          if (isUnlimited) {
            remainingApplications = null;
            totalApplications = null;
          }
        }
      }
    }

    // Alle User haben Zugriff auf AI-Features (mit normalem Guthaben-Abzug)
    let hasFeatureAccess = true;

    // Antwort zusammenstellen
    const response: PremiumStatusResponse = {
      success: true,
      isPremium: isPremium,
      plan: planType,
      remainingApplications: remainingApplications,
      totalApplications: totalApplications,
      isUnlimited: isUnlimited,
    };

    // Wenn ein Feature angegeben wurde, füge die Zugriffsinfo hinzu
    if (feature) {
      response.feature = feature;
      response.hasAccess = hasFeatureAccess;
    }

    return new Response(JSON.stringify(response), {
      headers,
      status: 200,
    });
  } catch (error) {
    console.error("Error processing request:", error);

    return new Response(
      JSON.stringify({
        success: false,
        isPremium: false,
        error: error.message,
      }),
      {
        headers,
        status: 500,
      }
    );
  }
});

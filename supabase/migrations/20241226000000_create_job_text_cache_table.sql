-- Migration: <PERSON><PERSON><PERSON><PERSON> Tabelle für gecachte Job-Texte
-- Zweck: Offline-Speicherung extrahierter Stellenbeschreibungen

CREATE TABLE IF NOT EXISTS public.job_text_cache (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Job-Identifikation
    job_id TEXT NOT NULL UNIQUE, -- Original Job ID aus der API
    source_url TEXT, -- Original URL der Stellenanzeige
    
    -- Extrahierte Inhalte (komprimiert gespeichert)
    extracted_text TEXT NOT NULL, -- Bereinigter Text ohne HTML (5-15KB)
    job_title TEXT NOT NULL,
    company_name TEXT NOT NULL,
    location TEXT,
    
    -- Strukturierte Daten für bessere Suche
    requirements TEXT[], -- <PERSON><PERSON><PERSON> von <PERSON>ford<PERSON>ungen
    benefits TEXT[], -- <PERSON><PERSON><PERSON> von <PERSON>efits
    contact_info JSONB, -- Kontaktinformationen als JSON
    
    -- Metadaten
    extraction_method TEXT DEFAULT 'html_parser', -- Methode der Text-Extraktion
    content_hash TEXT, -- Hash des extrahierten Texts für Änderungserkennung
    content_size INTEGER, -- Größe des extrahierten Texts in Bytes
    
    -- Zeitstempel
    extracted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '6 months'),
    
    -- Audit-Felder
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Indizes für Performance
CREATE INDEX idx_job_text_cache_job_id ON public.job_text_cache(job_id);
CREATE INDEX idx_job_text_cache_extracted_at ON public.job_text_cache(extracted_at);
CREATE INDEX idx_job_text_cache_expires_at ON public.job_text_cache(expires_at);
CREATE INDEX idx_job_text_cache_last_accessed ON public.job_text_cache(last_accessed);

-- Volltext-Suche Index
CREATE INDEX idx_job_text_cache_search ON public.job_text_cache 
USING gin(to_tsvector('german', extracted_text || ' ' || job_title || ' ' || company_name));

-- RLS (Row Level Security) - Alle können lesen, da es gecachte öffentliche Stellenanzeigen sind
ALTER TABLE public.job_text_cache ENABLE ROW LEVEL SECURITY;

-- Policy: Jeder kann gecachte Job-Texte lesen
CREATE POLICY "job_text_cache_select_policy" ON public.job_text_cache
    FOR SELECT USING (true);

-- Policy: Nur authentifizierte Benutzer können Einträge erstellen/aktualisieren
CREATE POLICY "job_text_cache_insert_policy" ON public.job_text_cache
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "job_text_cache_update_policy" ON public.job_text_cache
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Trigger für automatische Aktualisierung von updated_at
CREATE OR REPLACE FUNCTION update_job_text_cache_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_job_text_cache_updated_at
    BEFORE UPDATE ON public.job_text_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_job_text_cache_updated_at();

-- Funktion für automatische Bereinigung abgelaufener Einträge
CREATE OR REPLACE FUNCTION cleanup_expired_job_text_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.job_text_cache 
    WHERE expires_at < now();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Kommentare für Dokumentation
COMMENT ON TABLE public.job_text_cache IS 'Gecachte extrahierte Texte von Stellenanzeigen für Offline-Verfügbarkeit';
COMMENT ON COLUMN public.job_text_cache.job_id IS 'Eindeutige Job-ID aus der ursprünglichen API';
COMMENT ON COLUMN public.job_text_cache.extracted_text IS 'Bereinigter Text ohne HTML-Tags (5-15KB)';
COMMENT ON COLUMN public.job_text_cache.requirements IS 'Array von extrahierten Anforderungen';
COMMENT ON COLUMN public.job_text_cache.benefits IS 'Array von extrahierten Benefits';
COMMENT ON COLUMN public.job_text_cache.contact_info IS 'Kontaktinformationen als JSON (E-Mail, Telefon, etc.)';
COMMENT ON COLUMN public.job_text_cache.content_hash IS 'SHA-256 Hash für Änderungserkennung';
COMMENT ON COLUMN public.job_text_cache.expires_at IS 'Ablaufzeit für automatische Bereinigung (Standard: 6 Monate)';

-- Erweitere bestehende Tabellen um Referenz auf job_text_cache
-- user_favorites Tabelle erweitern (falls vorhanden)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_favorites') THEN
        ALTER TABLE public.user_favorites
        ADD COLUMN IF NOT EXISTS cached_text_id UUID REFERENCES public.job_text_cache(id) ON DELETE SET NULL;
        
        -- Index für die neue Referenz
        CREATE INDEX IF NOT EXISTS idx_user_favorites_cached_text_id ON public.user_favorites(cached_text_id);
    END IF;
END $$;

-- applied_jobs Tabelle erweitern (falls vorhanden)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'applied_jobs') THEN
        ALTER TABLE public.applied_jobs
        ADD COLUMN IF NOT EXISTS cached_text_id UUID REFERENCES public.job_text_cache(id) ON DELETE SET NULL;
        
        -- Index für die neue Referenz
        CREATE INDEX IF NOT EXISTS idx_applied_jobs_cached_text_id ON public.applied_jobs(cached_text_id);
    END IF;
END $$;

-- Funktion zum Aktualisieren der last_accessed Zeit
CREATE OR REPLACE FUNCTION update_job_text_cache_access(p_job_id TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE public.job_text_cache
    SET last_accessed = now()
    WHERE job_id = p_job_id;
END;
$$ LANGUAGE plpgsql;

-- Create the applied_jobs table
CREATE TABLE IF NOT EXISTS public.applied_jobs (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL,
  job_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, job_id)
);

-- Add RLS policies
ALTER TABLE public.applied_jobs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own applied jobs
CREATE POLICY "Users can view their own applied jobs" 
  ON public.applied_jobs 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy: Users can insert their own applied jobs
CREATE POLICY "Users can insert their own applied jobs" 
  ON public.applied_jobs 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own applied jobs
CREATE POLICY "Users can delete their own applied jobs" 
  ON public.applied_jobs 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS applied_jobs_user_id_idx ON public.applied_jobs (user_id);
CREATE INDEX IF NOT EXISTS applied_jobs_job_id_idx ON public.applied_jobs (job_id);

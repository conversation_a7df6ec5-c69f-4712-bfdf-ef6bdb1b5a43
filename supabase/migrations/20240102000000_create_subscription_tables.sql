-- Skript zum Erstellen der Subscription-Tabellen
-- Hinweis: <PERSON><PERSON>ript ist idempotent, d.h. es kann mehrfach ausgeführt werden,
-- ohne dass es zu Fehlern kommt.

-- Erweiterungen aktivieren
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Typen erstellen
DO $$ BEGIN
    -- Subscription Status Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_status') THEN
        CREATE TYPE subscription_status AS ENUM (
            'active',
            'cancelled',
            'expired',
            'pending',
            'trial'
        );
    END IF;

    -- Subscription Type Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_type') THEN
        CREATE TYPE subscription_type AS ENUM (
            'monthly',
            'yearly',
            'lifetime'
        );
    END IF;

    -- Purchase Verification Status Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status') THEN
        CREATE TYPE verification_status AS ENUM (
            'pending',
            'verified',
            'failed',
            'revoked'
        );
    END IF;

    -- Platform Enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'platform_type') THEN
        CREATE TYPE platform_type AS ENUM (
            'ios',
            'android',
            'web'
        );
    END IF;
END $$;

-- Tabellen erstellen

-- Subscription-Tabelle
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_type subscription_type NOT NULL,
    status subscription_status NOT NULL DEFAULT 'pending',
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    auto_renew BOOLEAN NOT NULL DEFAULT true,
    cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
    cancellation_date TIMESTAMPTZ,
    cancellation_reason TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Purchases-Tabelle für Zahlungsdaten
CREATE TABLE IF NOT EXISTS purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    platform platform_type NOT NULL,
    transaction_id TEXT,
    receipt_data TEXT,
    purchase_date TIMESTAMPTZ NOT NULL,
    verification_status verification_status NOT NULL DEFAULT 'pending',
    verification_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indizes für Performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_purchases_user_id ON purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_purchases_subscription_id ON purchases(subscription_id);
CREATE INDEX IF NOT EXISTS idx_purchases_transaction_id ON purchases(transaction_id);

-- Trigger für updated_at-Aktualisierung
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger für Subscriptions
DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON subscriptions;
CREATE TRIGGER update_subscriptions_updated_at
BEFORE UPDATE ON subscriptions
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- Trigger für Purchases
DROP TRIGGER IF EXISTS update_purchases_updated_at ON purchases;
CREATE TRIGGER update_purchases_updated_at
BEFORE UPDATE ON purchases
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- Row Level Security (RLS) für Subscriptions
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy für Subscriptions
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON subscriptions;
CREATE POLICY "Users can view their own subscriptions"
ON subscriptions FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- RLS für Purchases
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;

-- Policy für Purchases
DROP POLICY IF EXISTS "Users can view their own purchases" ON purchases;
CREATE POLICY "Users can view their own purchases"
ON purchases FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Erstellen einer Funktion zur Prüfung aktueller Abonnements
CREATE OR REPLACE FUNCTION get_active_subscription(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    subscription_type subscription_type,
    end_date TIMESTAMPTZ
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT s.id, s.subscription_type, s.end_date
    FROM subscriptions s
    WHERE s.user_id = user_uuid
      AND s.status = 'active'
      AND (s.end_date IS NULL OR s.end_date > NOW())
    ORDER BY s.end_date DESC NULLS LAST
    LIMIT 1;
END;
$$; 
 
 
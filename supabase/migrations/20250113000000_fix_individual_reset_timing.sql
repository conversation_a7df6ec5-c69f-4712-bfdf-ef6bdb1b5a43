-- KRITISCHER FIX: Korrigiere individuellen 7-Tage-Reset-Zyklus
-- Problem: Alle User bekommen das gleiche Reset-Datum (NOW()) statt individueller Zyklen
-- Lösung: Jeder User behält seinen eigenen 7-Tage-Zyklus basierend auf seinem letzten Reset

-- 1. Korrigiere die Haupt-Reset-Funktion
CREATE OR REPLACE FUNCTION public.reset_free_application_counters()
RETURNS void AS $$
DECLARE
  v_user RECORD;
  v_subscription RECORD;
  v_reset_count INTEGER := 0;
  v_new_reset_date TIMESTAMPTZ;
BEGIN
  RAISE NOTICE 'Starte KORRIGIERTEN individuellen wöchentlichen Reset-Check...';

  -- Durchlaufe alle User mit kostenlosen Plänen deren individueller Reset-Zeitpunkt erreicht ist
  FOR v_user IN
    SELECT DISTINCT
      ac.user_id,
      ac.free_reset_date,
      ac.created_at,
      ac.remaining_applications
    FROM public.application_counters ac
    LEFT JOIN public.subscriptions s ON ac.user_id = s.user_id
    WHERE (
      -- User ohne aktives Premium-Abo (free/basic oder kein Abo)
      s.id IS NULL
      OR (s.status = 'active' AND s.plan_type IN ('free', 'basic'))
      OR (s.status != 'active')
    )
    AND (
      -- Individueller Reset-Zeitpunkt ist erreicht (7 Tage nach letztem Reset)
      (ac.free_reset_date IS NOT NULL AND ac.free_reset_date <= (NOW() - INTERVAL '7 days'))
      OR
      -- Oder kein Reset-Datum aber Counter älter als 7 Tage
      (ac.free_reset_date IS NULL AND ac.created_at <= (NOW() - INTERVAL '7 days'))
    )
  LOOP
    -- Prüfe ob User ein aktives Premium-Abo hat (doppelte Sicherheit)
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = v_user.user_id
      AND status = 'active'
      AND plan_type IN ('pro', 'premium', 'unlimited')
    ORDER BY created_at DESC
    LIMIT 1;

    -- Nur zurücksetzen wenn KEIN Premium-Abo vorhanden
    IF v_subscription.id IS NULL THEN
      -- KORRIGIERT: Berechne individuelles Reset-Datum basierend auf letztem Reset
      IF v_user.free_reset_date IS NOT NULL THEN
        -- User hatte bereits einen Reset - nächster Reset ist 7 Tage nach dem letzten
        v_new_reset_date := v_user.free_reset_date + INTERVAL '7 days';
      ELSE
        -- Neuer User ohne Reset-Datum - starte Zyklus basierend auf Account-Erstellung
        v_new_reset_date := v_user.created_at + INTERVAL '7 days';
      END IF;

      -- INDIVIDUELLER Reset: Setze Counter auf 5 und INDIVIDUELLES Reset-Datum
      UPDATE public.application_counters
      SET remaining_applications = 5, -- 5 kostenlose Bewerbungen für nächste 7 Tage
          total_applications = 5,
          free_reset_date = v_new_reset_date, -- INDIVIDUELLES Reset-Datum (nicht NOW()!)
          updated_at = NOW()
      WHERE user_id = v_user.user_id;

      v_reset_count := v_reset_count + 1;
      RAISE NOTICE 'KORRIGIERTER Reset für User % - hatte % Bewerbungen, jetzt 5 - nächster Reset: %',
        v_user.user_id,
        v_user.remaining_applications,
        v_new_reset_date;
    ELSE
      RAISE NOTICE 'User % hat Premium-Abo (%) - kein Reset nötig',
        v_user.user_id,
        v_subscription.plan_type;
    END IF;
  END LOOP;

  RAISE NOTICE 'KORRIGIERTER Reset abgeschlossen - % User zurückgesetzt', v_reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Korrigiere die Funktion für neue User Counter
CREATE OR REPLACE FUNCTION public.ensure_free_application_counter(p_user_id UUID)
RETURNS void AS $$
DECLARE
  v_counter RECORD;
  v_subscription RECORD;
  v_user_created_at TIMESTAMPTZ;
  v_initial_reset_date TIMESTAMPTZ;
BEGIN
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;

  -- Hole das aktive Abonnement
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
  ORDER BY created_at DESC
  LIMIT 1;

  -- Hole User-Erstellungsdatum für individuellen Zyklus
  SELECT created_at INTO v_user_created_at
  FROM auth.users
  WHERE id = p_user_id;

  -- Wenn kein Counter existiert, erstelle einen für neuen User
  IF v_counter.id IS NULL THEN
    -- KORRIGIERT: Individueller Reset-Zyklus basierend auf User-Erstellung
    v_initial_reset_date := COALESCE(v_user_created_at, NOW()) + INTERVAL '7 days';

    INSERT INTO public.application_counters (
      user_id,
      subscription_id,
      month_year,
      remaining_applications,
      total_applications,
      free_reset_date,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      v_subscription.id,
      to_char(NOW(), 'YYYY-MM'),
      5, -- Neuer User bekommt sofort 5 Bewerbungen
      5,
      v_initial_reset_date, -- INDIVIDUELLER Reset-Zyklus (nicht NOW()!)
      NOW(),
      NOW()
    );

    RAISE NOTICE 'Neuer KORRIGIERTER Counter für User % erstellt - 5 Bewerbungen, Reset: %',
      p_user_id, v_initial_reset_date;
    RETURN;
  END IF;

  -- Wenn Counter existiert aber kein free_reset_date, setze es individuell
  IF v_counter.free_reset_date IS NULL THEN
    -- KORRIGIERT: Individueller Reset basierend auf Counter-Erstellung oder User-Erstellung
    v_initial_reset_date := COALESCE(v_counter.created_at, v_user_created_at, NOW()) + INTERVAL '7 days';

    UPDATE public.application_counters
    SET free_reset_date = v_initial_reset_date, -- INDIVIDUELLER Zyklus (nicht NOW()!)
        updated_at = NOW()
    WHERE user_id = p_user_id;

    RAISE NOTICE 'Reset-Datum für User % gesetzt - individueller Zyklus: %',
      p_user_id, v_initial_reset_date;
  END IF;

  -- Prüfe ob individueller Reset fällig ist
  IF v_counter.free_reset_date IS NOT NULL
     AND v_counter.free_reset_date <= (NOW() - INTERVAL '7 days') THEN

    -- Prüfe ob User Premium hat
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = p_user_id
      AND status = 'active'
      AND plan_type IN ('pro', 'premium', 'unlimited')
    ORDER BY created_at DESC
    LIMIT 1;

    -- Nur zurücksetzen wenn kein Premium
    IF v_subscription.id IS NULL THEN
      -- KORRIGIERT: Nächster Reset ist 7 Tage nach dem aktuellen Reset-Datum
      v_initial_reset_date := v_counter.free_reset_date + INTERVAL '7 days';

      UPDATE public.application_counters
      SET remaining_applications = 5,
          total_applications = 5,
          free_reset_date = v_initial_reset_date, -- INDIVIDUELLER nächster Reset
          updated_at = NOW()
      WHERE user_id = p_user_id;

      RAISE NOTICE 'Automatischer KORRIGIERTER Reset für User % - 5 neue Bewerbungen, nächster Reset: %',
        p_user_id, v_initial_reset_date;
    END IF;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Korrigiere die check_free_application_counter Funktion
CREATE OR REPLACE FUNCTION public.check_free_application_counter(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_counter RECORD;
  v_subscription RECORD;
  v_user_created_at TIMESTAMPTZ;
  v_new_reset_date TIMESTAMPTZ;
BEGIN
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
    AND (plan_type = 'free' OR plan_type = 'basic')
  ORDER BY created_at DESC
  LIMIT 1;

  -- Wenn kein aktives kostenloses Abonnement gefunden wurde, beende
  IF v_subscription.id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;

  -- Hole User-Erstellungsdatum für individuellen Zyklus
  SELECT created_at INTO v_user_created_at
  FROM auth.users
  WHERE id = p_user_id;

  -- Wenn kein Zähler gefunden wurde oder das letzte Reset-Datum mehr als 7 Tage zurückliegt
  IF v_counter.id IS NULL OR v_counter.free_reset_date IS NULL OR v_counter.free_reset_date < (NOW() - INTERVAL '7 days') THEN
    -- Aktualisiere den Bewerbungszähler oder erstelle einen neuen
    IF v_counter.id IS NULL THEN
      -- KORRIGIERT: Individueller Reset-Zyklus für neuen User
      v_new_reset_date := COALESCE(v_user_created_at, NOW()) + INTERVAL '7 days';

      INSERT INTO public.application_counters (
        user_id,
        subscription_id,
        month_year,
        remaining_applications,
        total_applications,
        free_reset_date,
        created_at,
        updated_at
      ) VALUES (
        p_user_id,
        v_subscription.id,
        to_char(NOW(), 'YYYY-MM'),
        5, -- 5 kostenlose Bewerbungen pro Woche
        5,
        v_new_reset_date, -- INDIVIDUELLER Reset (nicht NOW()!)
        NOW(),
        NOW()
      );
    ELSE
      -- KORRIGIERT: Berechne individuelles nächstes Reset-Datum
      IF v_counter.free_reset_date IS NOT NULL THEN
        -- Nächster Reset ist 7 Tage nach dem letzten Reset
        v_new_reset_date := v_counter.free_reset_date + INTERVAL '7 days';
      ELSE
        -- Fallback: Basierend auf Counter-Erstellung oder User-Erstellung
        v_new_reset_date := COALESCE(v_counter.created_at, v_user_created_at, NOW()) + INTERVAL '7 days';
      END IF;

      UPDATE public.application_counters
      SET remaining_applications = 5, -- 5 kostenlose Bewerbungen pro Woche
          total_applications = 5,
          free_reset_date = v_new_reset_date, -- INDIVIDUELLER Reset (nicht NOW()!)
          updated_at = NOW()
      WHERE user_id = p_user_id;
    END IF;

    RETURN TRUE;
  END IF;

  -- Wenn der Zähler bereits aktuell ist, gib TRUE zurück
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Backfill für bestehende User mit synchronem Reset-Datum
-- Korrigiere alle User, die das gleiche free_reset_date haben (synchroner Reset)
DO $$
DECLARE
  v_user RECORD;
  v_user_created_at TIMESTAMPTZ;
  v_new_reset_date TIMESTAMPTZ;
  v_fixed_count INTEGER := 0;
BEGIN
  RAISE NOTICE 'Starte Backfill für synchrone Reset-Daten...';

  -- Finde User mit potentiell synchronem Reset (mehrere User mit gleichem free_reset_date)
  FOR v_user IN
    SELECT DISTINCT
      ac.user_id,
      ac.free_reset_date,
      ac.created_at as counter_created_at
    FROM public.application_counters ac
    WHERE ac.free_reset_date IS NOT NULL
      AND ac.free_reset_date IN (
        -- Finde Reset-Daten, die von mehreren Usern geteilt werden
        SELECT free_reset_date
        FROM public.application_counters
        WHERE free_reset_date IS NOT NULL
        GROUP BY free_reset_date
        HAVING COUNT(*) > 1
      )
  LOOP
    -- Hole User-Erstellungsdatum
    SELECT created_at INTO v_user_created_at
    FROM auth.users
    WHERE id = v_user.user_id;

    -- Berechne individuelles Reset-Datum basierend auf User-Erstellung
    v_new_reset_date := COALESCE(v_user_created_at, v_user.counter_created_at, NOW());

    -- Finde nächsten individuellen Reset-Zeitpunkt (in 7-Tage-Schritten)
    WHILE v_new_reset_date < NOW() LOOP
      v_new_reset_date := v_new_reset_date + INTERVAL '7 days';
    END LOOP;

    -- Aktualisiere auf individuelles Reset-Datum
    UPDATE public.application_counters
    SET free_reset_date = v_new_reset_date,
        updated_at = NOW()
    WHERE user_id = v_user.user_id;

    v_fixed_count := v_fixed_count + 1;
    RAISE NOTICE 'User % korrigiert: Reset von % auf %',
      v_user.user_id, v_user.free_reset_date, v_new_reset_date;
  END LOOP;

  RAISE NOTICE 'Backfill abgeschlossen - % User korrigiert', v_fixed_count;
END $$;

-- 5. Kommentare für Dokumentation
COMMENT ON FUNCTION public.reset_free_application_counters() IS
'KORRIGIERTER individueller wöchentlicher Reset - jeder User behält seinen eigenen 7-Tage-Zyklus';

COMMENT ON FUNCTION public.ensure_free_application_counter(UUID) IS
'KORRIGIERT: Stellt sicher dass User einen Counter hat mit individuellem 7-Tage-Reset-Zyklus';

COMMENT ON FUNCTION public.check_free_application_counter(UUID) IS
'KORRIGIERT: Überprüft und setzt kostenlose Bewerbungszähler mit individuellem 7-Tage-Zyklus';
-- Migration zur Aktivierung von Row Level Security (RLS) für die public.logs-Tabelle
-- Erstellt am: 2024-09-15

-- Aktiviere RLS für die logs-Tabelle
ALTER TABLE IF EXISTS public.logs ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON> nur wenn die logs-Tabelle existiert
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'logs') THEN
        -- Lösche existierende Policies falls vorhanden
        DROP POLICY IF EXISTS "Service role can read logs" ON public.logs;
        DROP POLICY IF EXISTS "Service role can insert logs" ON public.logs;
        DROP POLICY IF EXISTS "Service role can update logs" ON public.logs;
        DROP POLICY IF EXISTS "Service role can delete logs" ON public.logs;
        
        -- <PERSON><PERSON><PERSON> eine <PERSON>tlini<PERSON>, die nur der Service-Rolle erlaubt, Logs zu lesen
        CREATE POLICY "Service role can read logs"
          ON public.logs
          FOR SELECT
          USING (auth.jwt() ->> 'role' = 'service_role');

        -- <PERSON><PERSON><PERSON> e<PERSON>, die nur der Service-<PERSON><PERSON> erlaubt, Logs zu erstellen
        CREATE POLICY "Service role can insert logs"
          ON public.logs
          FOR INSERT
          WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

        -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu aktualisieren
        CREATE POLICY "Service role can update logs"
          ON public.logs
          FOR UPDATE
          USING (auth.jwt() ->> 'role' = 'service_role');

        -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu löschen
        CREATE POLICY "Service role can delete logs"
          ON public.logs
          FOR DELETE
          USING (auth.jwt() ->> 'role' = 'service_role');

        -- Kommentar zur Erklärung
        COMMENT ON TABLE public.logs IS 'Tabelle für Systemlogs mit aktivierter Row Level Security';
    END IF;
END $$;

-- KRITISCHER FIX: Individueller wöchentlicher Reset für jeden User
-- Problem: Alter Reset war zentral für alle User (immer nächster Montag)
-- Lösung: Jeder User hat seinen eigenen 7-Tage-Zyklus ab Account-Erstellung

-- 1. Aktualisiere die Reset-Funktion für individuellen Timer
CREATE OR REPLACE FUNCTION public.reset_free_application_counters()
RETURNS void AS $$
DECLARE
  v_user RECORD;
  v_subscription RECORD;
  v_reset_count INTEGER := 0;
BEGIN
  RAISE NOTICE 'Starte INDIVIDUELLEN wöchentlichen Reset-Check...';
  
  -- Durchlaufe alle User mit kostenlosen Plänen deren individueller Reset-Zeitpunkt erreicht ist
  FOR v_user IN 
    SELECT DISTINCT 
      ac.user_id, 
      ac.free_reset_date, 
      ac.created_at,
      ac.remaining_applications
    FROM public.application_counters ac
    LEFT JOIN public.subscriptions s ON ac.user_id = s.user_id
    WHERE (
      -- User ohne aktives Premium-Abo (free/basic oder kein Abo)
      s.id IS NULL 
      OR (s.status = 'active' AND s.plan_type IN ('free', 'basic'))
      OR (s.status != 'active')
    )
    AND (
      -- Individueller Reset-Zeitpunkt ist erreicht (7 Tage nach letztem Reset)
      (ac.free_reset_date IS NOT NULL AND ac.free_reset_date <= (NOW() - INTERVAL '7 days'))
      OR 
      -- Oder kein Reset-Datum aber Counter älter als 7 Tage
      (ac.free_reset_date IS NULL AND ac.created_at <= (NOW() - INTERVAL '7 days'))
    )
  LOOP
    -- Prüfe ob User ein aktives Premium-Abo hat (doppelte Sicherheit)
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = v_user.user_id
      AND status = 'active'
      AND plan_type IN ('pro', 'premium', 'unlimited')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Nur zurücksetzen wenn KEIN Premium-Abo vorhanden
    IF v_subscription.id IS NULL THEN
      -- INDIVIDUELLER Reset: Setze Counter auf 10 und neues Reset-Datum
      UPDATE public.application_counters
      SET remaining_applications = 5, -- 5 kostenlose Bewerbungen für nächste 7 Tage
          total_applications = 5,
          free_reset_date = NOW(), -- Neuer individueller Reset-Zeitpunkt
          updated_at = NOW()
      WHERE user_id = v_user.user_id;
      
      v_reset_count := v_reset_count + 1;
      RAISE NOTICE 'INDIVIDUELLER Reset für User % - hatte % Bewerbungen, jetzt 5 - nächster Reset: %',
        v_user.user_id, 
        v_user.remaining_applications,
        (NOW() + INTERVAL '7 days')::timestamp;
    ELSE
      RAISE NOTICE 'User % hat Premium-Abo (%) - kein Reset nötig', 
        v_user.user_id, 
        v_subscription.plan_type;
    END IF;
  END LOOP;
  
  RAISE NOTICE 'INDIVIDUELLER Reset abgeschlossen - % User zurückgesetzt', v_reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Aktualisiere die Funktion für neuen User Counter
CREATE OR REPLACE FUNCTION public.ensure_free_application_counter(p_user_id UUID)
RETURNS void AS $$
DECLARE
  v_counter RECORD;
  v_subscription RECORD;
BEGIN
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;
  
  -- Hole das aktive Abonnement
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein Counter existiert, erstelle einen für neuen User
  IF v_counter.id IS NULL THEN
    INSERT INTO public.application_counters (
      user_id,
      subscription_id,
      month_year,
      remaining_applications,
      total_applications,
      free_reset_date,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      v_subscription.id,
      to_char(NOW(), 'YYYY-MM'),
      10, -- Neuer User bekommt sofort 10 Bewerbungen
      10,
      NOW(), -- Reset-Zyklus startet JETZT (individuell)
      NOW(),
      NOW()
    );
    
    RAISE NOTICE 'Neuer INDIVIDUELLER Counter für User % erstellt - 10 Bewerbungen, Reset in 7 Tagen', p_user_id;
    RETURN;
  END IF;
  
  -- Wenn Counter existiert aber kein free_reset_date, setze es
  IF v_counter.free_reset_date IS NULL THEN
    UPDATE public.application_counters
    SET free_reset_date = NOW(), -- Starte individuellen Zyklus
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    RAISE NOTICE 'Reset-Datum für User % gesetzt - individueller Zyklus startet jetzt', p_user_id;
  END IF;
  
  -- Prüfe ob individueller Reset fällig ist
  IF v_counter.free_reset_date IS NOT NULL 
     AND v_counter.free_reset_date <= (NOW() - INTERVAL '7 days') THEN
    
    -- Prüfe ob User Premium hat
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = p_user_id
      AND status = 'active'
      AND plan_type IN ('pro', 'premium', 'unlimited')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Nur zurücksetzen wenn kein Premium
    IF v_subscription.id IS NULL THEN
      UPDATE public.application_counters
      SET remaining_applications = 10,
          total_applications = 10,
          free_reset_date = NOW(),
          updated_at = NOW()
      WHERE user_id = p_user_id;
      
      RAISE NOTICE 'Automatischer INDIVIDUELLER Reset für User % - 10 neue Bewerbungen', p_user_id;
    END IF;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Aktualisiere die get_next_free_reset_date Funktion
CREATE OR REPLACE FUNCTION public.get_next_free_reset_date(p_user_id UUID)
RETURNS TIMESTAMPTZ AS $$
DECLARE
  v_counter RECORD;
  v_next_reset TIMESTAMPTZ;
BEGIN
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;
  
  -- Wenn kein Zähler gefunden wurde, erstelle einen und gib aktuelles Datum + 7 Tage zurück
  IF v_counter.id IS NULL THEN
    PERFORM public.ensure_free_application_counter(p_user_id);
    RETURN NOW() + INTERVAL '7 days';
  END IF;
  
  -- Wenn kein free_reset_date existiert, setze es auf jetzt und gib +7 Tage zurück
  IF v_counter.free_reset_date IS NULL THEN
    UPDATE public.application_counters
    SET free_reset_date = NOW(),
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    RETURN NOW() + INTERVAL '7 days';
  END IF;
  
  -- Berechne das nächste INDIVIDUELLE Reset-Datum (7 Tage nach dem letzten Reset)
  v_next_reset := v_counter.free_reset_date + INTERVAL '7 days';
  
  RAISE NOTICE 'Nächster INDIVIDUELLER Reset für User %: %', p_user_id, v_next_reset;
  RETURN v_next_reset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Kommentar für Dokumentation
COMMENT ON FUNCTION public.reset_free_application_counters() IS 
'INDIVIDUELLER wöchentlicher Reset - jeder User hat seinen eigenen 7-Tage-Zyklus ab Account-Erstellung';

COMMENT ON FUNCTION public.ensure_free_application_counter(UUID) IS 
'Stellt sicher dass User einen Counter hat und startet individuellen 7-Tage-Reset-Zyklus';

COMMENT ON FUNCTION public.get_next_free_reset_date(UUID) IS 
'Gibt das nächste INDIVIDUELLE Reset-Datum zurück - 7 Tage nach letztem Reset des Users';

-- Migration zur Implementierung der wöchentlichen Zurücksetzung von kostenlosen Bewerbungen
-- Erstellt am: 2024-09-01

-- Füge eine neue Spalte für das Datum der letzten Zurücksetzung der kostenlosen Bewerbungen hinzu
ALTER TABLE IF EXISTS public.application_counters
ADD COLUMN IF NOT EXISTS free_reset_date TIMESTAMPTZ;

-- INDIVIDUELLER Reset: Jeder User hat seinen eigenen 7-Tage-Zyklus
-- Diese Funktion wird regelmäßig ausgeführt und prüft für jeden User individuell
CREATE OR REPLACE FUNCTION public.reset_free_application_counters()
RETURNS void AS $$
DECLARE
  v_user RECORD;
  v_subscription RECORD;
  v_reset_count INTEGER := 0;
BEGIN
  -- Durchlaufe alle User mit kostenlosen Plänen deren individueller Reset-Zeitpunkt erreicht ist
  FOR v_user IN
    SELECT DISTINCT ac.user_id, ac.free_reset_date, ac.created_at
    FROM public.application_counters ac
    LEFT JOIN public.subscriptions s ON ac.user_id = s.user_id
    WHERE (
      -- User ohne aktives Premium-Abo (free/basic oder kein Abo)
      s.id IS NULL
      OR (s.status = 'active' AND s.plan_type IN ('free', 'basic'))
      OR (s.status != 'active')
    )
    AND (
      -- Individueller Reset-Zeitpunkt ist erreicht (7 Tage nach letztem Reset)
      (ac.free_reset_date IS NOT NULL AND ac.free_reset_date < (NOW() - INTERVAL '7 days'))
      OR
      -- Oder kein Reset-Datum aber Counter älter als 7 Tage
      (ac.free_reset_date IS NULL AND ac.created_at < (NOW() - INTERVAL '7 days'))
    )
  LOOP
    -- Hole das aktive Abonnement des Benutzers
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = v_user.user_id
      AND status = 'active'
      AND (plan_type = 'free' OR plan_type = 'basic')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Wenn ein aktives kostenloses Abonnement gefunden wurde
    IF v_subscription.id IS NOT NULL THEN
      -- Aktualisiere den Bewerbungszähler
      UPDATE public.application_counters
      SET remaining_applications = 5, -- 5 kostenlose Bewerbungen pro Woche
          total_applications = 5,
          free_reset_date = NOW(),
          updated_at = NOW()
      WHERE user_id = v_user.user_id;
      
      -- Wenn kein Eintrag aktualisiert wurde, erstelle einen neuen
      IF NOT FOUND THEN
        INSERT INTO public.application_counters (
          user_id,
          subscription_id,
          month_year,
          remaining_applications,
          total_applications,
          free_reset_date,
          created_at,
          updated_at
        ) VALUES (
          v_user.user_id,
          v_subscription.id,
          to_char(NOW(), 'YYYY-MM'),
          10, -- 10 kostenlose Bewerbungen pro Woche
          10,
          NOW(),
          NOW(),
          NOW()
        );
      END IF;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Erstelle eine Funktion zum Überprüfen und Zurücksetzen der kostenlosen Bewerbungszähler für einen bestimmten Benutzer
CREATE OR REPLACE FUNCTION public.check_free_application_counter(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_counter RECORD;
  v_subscription RECORD;
BEGIN
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
    AND (plan_type = 'free' OR plan_type = 'basic')
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein aktives kostenloses Abonnement gefunden wurde, beende
  IF v_subscription.id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;
  
  -- Wenn kein Zähler gefunden wurde oder das letzte Reset-Datum mehr als 7 Tage zurückliegt
  IF v_counter.id IS NULL OR v_counter.free_reset_date IS NULL OR v_counter.free_reset_date < (NOW() - INTERVAL '7 days') THEN
    -- Aktualisiere den Bewerbungszähler oder erstelle einen neuen
    IF v_counter.id IS NULL THEN
      INSERT INTO public.application_counters (
        user_id,
        subscription_id,
        month_year,
        remaining_applications,
        total_applications,
        free_reset_date,
        created_at,
        updated_at
      ) VALUES (
        p_user_id,
        v_subscription.id,
        to_char(NOW(), 'YYYY-MM'),
        5, -- 5 kostenlose Bewerbungen pro Woche
        5,
        NOW(),
        NOW(),
        NOW()
      );
    ELSE
      UPDATE public.application_counters
      SET remaining_applications = 5, -- 5 kostenlose Bewerbungen pro Woche
          total_applications = 5,
          free_reset_date = NOW(),
          updated_at = NOW()
      WHERE user_id = p_user_id;
    END IF;
    
    RETURN TRUE;
  END IF;
  
  -- Wenn der Zähler bereits aktuell ist, gib TRUE zurück
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Erstelle eine Funktion zum Abrufen des nächsten Reset-Datums für kostenlose Bewerbungen
CREATE OR REPLACE FUNCTION public.get_next_free_reset_date(p_user_id UUID)
RETURNS TIMESTAMPTZ AS $$
DECLARE
  v_counter RECORD;
  v_next_reset TIMESTAMPTZ;
BEGIN
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;
  
  -- Wenn kein Zähler gefunden wurde oder kein free_reset_date existiert
  IF v_counter.id IS NULL OR v_counter.free_reset_date IS NULL THEN
    RETURN NOW();
  END IF;
  
  -- Berechne das nächste Reset-Datum (7 Tage nach dem letzten Reset)
  v_next_reset := v_counter.free_reset_date + INTERVAL '7 days';
  
  RETURN v_next_reset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

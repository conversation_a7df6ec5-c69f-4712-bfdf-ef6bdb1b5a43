-- Migration: Änder<PERSON> das wöchentliche Bewerbungslimit für kostenlose Benutzer von 10 auf 5
-- Datum: 2024-12-13
-- Beschreibung: Reduziert das kostenlose Bewerbungslimit von 10 auf 5 Bewerbungen pro Woche

-- 1. Aktualisiere alle bestehenden Free-User mit mehr als 5 verwendeten Bewerbungen
-- Hinweis: Die Tabelle hat used_applications, nicht total_applications
UPDATE public.application_counters
SET
  used_applications = LEAST(used_applications, 5),
  updated_at = NOW()
WHERE user_id IN (
  SELECT DISTINCT ac.user_id
  FROM public.application_counters ac
  LEFT JOIN public.subscriptions s ON ac.user_id = s.user_id
  WHERE (
    -- User ohne aktives Premium-Abo (free/basic oder kein Abo)
    s.id IS NULL
    OR (s.status = 'active' AND s.plan_type IN ('free', 'basic'))
    OR (s.status != 'active')
  )
  AND ac.used_applications > 5
);

-- 2. <PERSON><PERSON><PERSON> eine Funktion zum Zurücksetzen der kostenlosen Bewerbungszähler mit dem neuen Limit
CREATE OR REPLACE FUNCTION public.reset_free_application_counters_v2()
RETURNS void AS $$
DECLARE
  v_user RECORD;
  v_subscription RECORD;
  v_reset_count INTEGER := 0;
BEGIN
  -- Durchlaufe alle User mit kostenlosen Plänen deren individueller Reset-Zeitpunkt erreicht ist
  FOR v_user IN
    SELECT DISTINCT ac.user_id, ac.free_reset_date, ac.created_at
    FROM public.application_counters ac
    LEFT JOIN public.subscriptions s ON ac.user_id = s.user_id
    WHERE (
      -- User ohne aktives Premium-Abo (free/basic oder kein Abo)
      s.id IS NULL
      OR (s.status = 'active' AND s.plan_type IN ('free', 'basic'))
      OR (s.status != 'active')
    )
    AND (
      -- Reset-Datum ist erreicht (7 Tage vergangen)
      ac.free_reset_date IS NULL 
      OR ac.free_reset_date < (NOW() - INTERVAL '7 days')
    )
  LOOP
    -- Prüfe nochmals das aktive Abonnement für diesen User
    SELECT * INTO v_subscription
    FROM public.subscriptions
    WHERE user_id = v_user.user_id
      AND status = 'active'
      AND (plan_type = 'free' OR plan_type = 'basic')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Nur zurücksetzen wenn KEIN Premium-Abo vorhanden
    IF v_subscription.id IS NULL THEN
      -- INDIVIDUELLER Reset: Setze Counter auf 5 und neues Reset-Datum
      UPDATE public.application_counters
      SET used_applications = 0, -- Reset auf 0 verwendete Bewerbungen (5 verfügbar)
          free_reset_date = NOW(), -- Neuer individueller Reset-Zeitpunkt
          updated_at = NOW()
      WHERE user_id = v_user.user_id;
      
      v_reset_count := v_reset_count + 1;
      RAISE NOTICE 'INDIVIDUELLER Reset für User % - hatte % Bewerbungen, jetzt 5 - nächster Reset: %', 
        v_user.user_id, 
        v_user.remaining_applications,
        (NOW() + INTERVAL '7 days')::timestamp;
    ELSE
      RAISE NOTICE 'User % hat Premium-Abo (%) - kein Reset nötig', 
        v_user.user_id, 
        v_subscription.plan_type;
    END IF;
  END LOOP;

  RAISE NOTICE 'INDIVIDUELLER Reset abgeschlossen: % User zurückgesetzt', v_reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Aktualisiere die check_free_application_counter Funktion für das neue Limit
CREATE OR REPLACE FUNCTION public.check_free_application_counter(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_counter RECORD;
  v_subscription RECORD;
BEGIN
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
    AND (plan_type = 'free' OR plan_type = 'basic')
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein aktives kostenloses Abonnement gefunden wurde, beende
  IF v_subscription.id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Hole den aktuellen Bewerbungszähler
  SELECT * INTO v_counter
  FROM public.application_counters
  WHERE user_id = p_user_id;
  
  -- Wenn kein Zähler gefunden wurde oder das letzte Reset-Datum mehr als 7 Tage zurückliegt
  IF v_counter.id IS NULL OR v_counter.free_reset_date IS NULL OR v_counter.free_reset_date < (NOW() - INTERVAL '7 days') THEN
    -- Aktualisiere den Bewerbungszähler oder erstelle einen neuen
    IF v_counter.id IS NULL THEN
      INSERT INTO public.application_counters (
        user_id,
        subscription_id,
        month_year,
        used_applications,
        free_reset_date,
        created_at,
        updated_at
      ) VALUES (
        p_user_id,
        v_subscription.id,
        to_char(NOW(), 'YYYY-MM'),
        0, -- 0 verwendete Bewerbungen (5 verfügbar)
        NOW(),
        NOW(),
        NOW()
      );
    ELSE
      UPDATE public.application_counters
      SET used_applications = 0, -- Reset auf 0 verwendete Bewerbungen (5 verfügbar)
          free_reset_date = NOW(),
          updated_at = NOW()
      WHERE user_id = p_user_id;
    END IF;
    
    RETURN TRUE;
  END IF;
  
  -- Wenn der Zähler bereits aktuell ist, gib TRUE zurück
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Führe die Migration für alle bestehenden Free-User aus
SELECT public.reset_free_application_counters_v2();

-- 5. Kommentar für die Änderung
COMMENT ON FUNCTION public.reset_free_application_counters_v2() IS 'Setzt kostenlose Bewerbungszähler auf das neue Limit von 5 Bewerbungen pro Woche zurück';
COMMENT ON FUNCTION public.check_free_application_counter(UUID) IS 'Überprüft und setzt kostenlose Bewerbungszähler mit dem neuen Limit von 5 Bewerbungen pro Woche';

-- Migration: Korrigiert die get_offline_jobs_sorted() Funktion für benutzerspezifische Filterung
-- Problem: Offline-Seiten werden allen Benutzern angezeigt, unabhängig vom angemeldeten Account
-- Lösung: <PERSON><PERSON><PERSON><PERSON>t die RPC-Funktion um Benutzer-ID-Filterung basierend auf Favoriten und beworbenen Jobs

-- Erweitere die job_text_cache Tabelle um user_id Spalte für direkte Benutzer-Zuordnung
ALTER TABLE public.job_text_cache
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Index für Performance bei Benutzer-Filterung
CREATE INDEX IF NOT EXISTS idx_job_text_cache_user_id ON public.job_text_cache(user_id);

-- Kommentar für Dokumentation
COMMENT ON COLUMN public.job_text_cache.user_id IS 'Benutzer-ID für benutzerspezifische Filterung der Offline-Texte';

-- Hinweis: Bestehende Cache-Einträge ohne user_id werden bei der ersten Nutzung automatisch
-- mit der korrekten user_id versehen durch die update_job_text_cache_source_timestamps Funktion

-- Korrigierte RPC-Funktion mit benutzerspezifischer Filterung
CREATE OR REPLACE FUNCTION get_offline_jobs_sorted()
RETURNS TABLE (
    job_id TEXT,
    source_url TEXT,
    extracted_text TEXT,
    job_title TEXT,
    company_name TEXT,
    location TEXT,
    requirements TEXT[],
    benefits TEXT[],
    contact_info JSONB,
    content_hash TEXT,
    content_size INTEGER,
    source_type TEXT,
    source_added_at TIMESTAMP WITH TIME ZONE,
    favorite_added_at TIMESTAMP WITH TIME ZONE,
    applied_at TIMESTAMP WITH TIME ZONE,
    extracted_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        jtc.job_id,
        jtc.source_url,
        jtc.extracted_text,
        jtc.job_title,
        jtc.company_name,
        jtc.location,
        jtc.requirements,
        jtc.benefits,
        jtc.contact_info,
        jtc.content_hash,
        jtc.content_size,
        jtc.source_type,
        jtc.source_added_at,
        jtc.favorite_added_at,
        jtc.applied_at,
        jtc.extracted_at,
        jtc.last_accessed
    FROM public.job_text_cache jtc
    WHERE jtc.extracted_text IS NOT NULL
      AND jtc.user_id = auth.uid()  -- Nur Jobs des angemeldeten Benutzers
    ORDER BY 
        COALESCE(jtc.source_added_at, jtc.extracted_at) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aktualisiere die update_job_text_cache_source_timestamps Funktion
-- um die user_id automatisch zu setzen
CREATE OR REPLACE FUNCTION update_job_text_cache_source_timestamps(
    p_job_id TEXT,
    p_source_type TEXT,
    p_source_timestamp TIMESTAMP WITH TIME ZONE
)
RETURNS VOID AS $$
BEGIN
    -- Aktualisiere oder erstelle Eintrag mit Quell-Informationen und Benutzer-ID
    INSERT INTO public.job_text_cache (job_id, user_id, source_type, source_added_at, favorite_added_at, applied_at)
    VALUES (
        p_job_id,
        auth.uid(),  -- Setze die aktuelle Benutzer-ID
        p_source_type, 
        p_source_timestamp,
        CASE WHEN p_source_type = 'favorite' THEN p_source_timestamp ELSE NULL END,
        CASE WHEN p_source_type = 'applied_job' THEN p_source_timestamp ELSE NULL END
    )
    ON CONFLICT (job_id) DO UPDATE SET
        user_id = COALESCE(job_text_cache.user_id, auth.uid()),  -- Behalte bestehende user_id oder setze neue
        source_type = CASE 
            WHEN job_text_cache.source_type IS NULL THEN p_source_type
            WHEN job_text_cache.source_type != p_source_type THEN 'both'
            ELSE job_text_cache.source_type
        END,
        source_added_at = CASE
            WHEN job_text_cache.source_added_at IS NULL THEN p_source_timestamp
            WHEN p_source_timestamp < job_text_cache.source_added_at THEN p_source_timestamp
            ELSE job_text_cache.source_added_at
        END,
        favorite_added_at = CASE
            WHEN p_source_type = 'favorite' THEN p_source_timestamp
            ELSE job_text_cache.favorite_added_at
        END,
        applied_at = CASE
            WHEN p_source_type = 'applied_job' THEN p_source_timestamp
            ELSE job_text_cache.applied_at
        END,
        updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funktion zum Löschen aller Cache-Einträge eines Benutzers (für signOut)
CREATE OR REPLACE FUNCTION clear_user_job_text_cache(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    DELETE FROM public.job_text_cache 
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policy für job_text_cache Tabelle
ALTER TABLE public.job_text_cache ENABLE ROW LEVEL SECURITY;

-- Policy: Benutzer können nur ihre eigenen Cache-Einträge sehen
CREATE POLICY "Users can view their own job text cache" 
  ON public.job_text_cache 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy: Benutzer können nur ihre eigenen Cache-Einträge erstellen
CREATE POLICY "Users can insert their own job text cache" 
  ON public.job_text_cache 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Policy: Benutzer können nur ihre eigenen Cache-Einträge aktualisieren
CREATE POLICY "Users can update their own job text cache" 
  ON public.job_text_cache 
  FOR UPDATE 
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy: Benutzer können nur ihre eigenen Cache-Einträge löschen
CREATE POLICY "Users can delete their own job text cache" 
  ON public.job_text_cache 
  FOR DELETE 
  USING (auth.uid() = user_id);
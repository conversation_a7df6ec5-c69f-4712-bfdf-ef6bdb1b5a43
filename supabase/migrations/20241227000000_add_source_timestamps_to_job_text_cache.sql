-- Migration: <PERSON><PERSON><PERSON><PERSON><PERSON> job_text_cache um Quell-Timestamps für korrekte Sortierung
-- Zweck: Sortierung nach Favoriten/Bewerbungs-Zeitpunkt statt Extraktions-Zeitpunkt

-- <PERSON>üge neue Spalten für Quell-Timestamps hinzu
ALTER TABLE public.job_text_cache
ADD COLUMN IF NOT EXISTS source_type TEXT, -- 'favorite' oder 'applied_job'
ADD COLUMN IF NOT EXISTS source_added_at TIMESTAMP WITH TIME ZONE, -- Wan<PERSON> zu <PERSON>/beworben hinzugefügt
ADD COLUMN IF NOT EXISTS favorite_added_at TIMESTAMP WITH TIME ZONE, -- Spezifisch für Favoriten
ADD COLUMN IF NOT EXISTS applied_at TIMESTAMP WITH TIME ZONE; -- Spezifisch für Bewerbungen

-- Erstelle Index für die neue Sortierung
CREATE INDEX IF NOT EXISTS idx_job_text_cache_source_added_at ON public.job_text_cache(source_added_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_text_cache_favorite_added_at ON public.job_text_cache(favorite_added_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_text_cache_applied_at ON public.job_text_cache(applied_at DESC);

-- Kommentare für Dokumentation
COMMENT ON COLUMN public.job_text_cache.source_type IS 'Quelle des Jobs: favorite, applied_job, oder both';
COMMENT ON COLUMN public.job_text_cache.source_added_at IS 'Zeitpunkt der ersten Hinzufügung (Favorit oder Bewerbung)';
COMMENT ON COLUMN public.job_text_cache.favorite_added_at IS 'Zeitpunkt der Hinzufügung zu Favoriten';
COMMENT ON COLUMN public.job_text_cache.applied_at IS 'Zeitpunkt der Bewerbung';

-- Funktion zum Aktualisieren der Quell-Timestamps
CREATE OR REPLACE FUNCTION update_job_text_cache_source_timestamps(
    p_job_id TEXT,
    p_source_type TEXT,
    p_source_timestamp TIMESTAMP WITH TIME ZONE
)
RETURNS VOID AS $$
BEGIN
    -- Aktualisiere oder erstelle Eintrag mit Quell-Informationen
    INSERT INTO public.job_text_cache (job_id, source_type, source_added_at, favorite_added_at, applied_at)
    VALUES (
        p_job_id, 
        p_source_type, 
        p_source_timestamp,
        CASE WHEN p_source_type = 'favorite' THEN p_source_timestamp ELSE NULL END,
        CASE WHEN p_source_type = 'applied_job' THEN p_source_timestamp ELSE NULL END
    )
    ON CONFLICT (job_id) DO UPDATE SET
        source_type = CASE 
            WHEN job_text_cache.source_type IS NULL THEN p_source_type
            WHEN job_text_cache.source_type != p_source_type THEN 'both'
            ELSE job_text_cache.source_type
        END,
        source_added_at = CASE
            WHEN job_text_cache.source_added_at IS NULL THEN p_source_timestamp
            WHEN p_source_timestamp < job_text_cache.source_added_at THEN p_source_timestamp
            ELSE job_text_cache.source_added_at
        END,
        favorite_added_at = CASE
            WHEN p_source_type = 'favorite' THEN p_source_timestamp
            ELSE job_text_cache.favorite_added_at
        END,
        applied_at = CASE
            WHEN p_source_type = 'applied_job' THEN p_source_timestamp
            ELSE job_text_cache.applied_at
        END,
        updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Funktion zum Abrufen aller Jobs mit korrekter Sortierung
CREATE OR REPLACE FUNCTION get_offline_jobs_sorted()
RETURNS TABLE (
    job_id TEXT,
    source_url TEXT,
    extracted_text TEXT,
    job_title TEXT,
    company_name TEXT,
    location TEXT,
    requirements TEXT[],
    benefits TEXT[],
    contact_info JSONB,
    content_hash TEXT,
    content_size INTEGER,
    source_type TEXT,
    source_added_at TIMESTAMP WITH TIME ZONE,
    favorite_added_at TIMESTAMP WITH TIME ZONE,
    applied_at TIMESTAMP WITH TIME ZONE,
    extracted_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        jtc.job_id,
        jtc.source_url,
        jtc.extracted_text,
        jtc.job_title,
        jtc.company_name,
        jtc.location,
        jtc.requirements,
        jtc.benefits,
        jtc.contact_info,
        jtc.content_hash,
        jtc.content_size,
        jtc.source_type,
        jtc.source_added_at,
        jtc.favorite_added_at,
        jtc.applied_at,
        jtc.extracted_at,
        jtc.last_accessed
    FROM public.job_text_cache jtc
    WHERE jtc.extracted_text IS NOT NULL
    ORDER BY 
        COALESCE(jtc.source_added_at, jtc.extracted_at) DESC;
END;
$$ LANGUAGE plpgsql;

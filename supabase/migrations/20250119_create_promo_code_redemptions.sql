-- <PERSON><PERSON><PERSON> für Promo Code Einlösungen
CREATE TABLE IF NOT EXISTS promo_code_redemptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    promo_code TEXT NOT NULL,
    redeemed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Erstelle Index für bessere Performance
CREATE INDEX IF NOT EXISTS idx_promo_code_redemptions_user_id ON promo_code_redemptions(user_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_redemptions_promo_code ON promo_code_redemptions(promo_code);
CREATE INDEX IF NOT EXISTS idx_promo_code_redemptions_status ON promo_code_redemptions(status);
CREATE INDEX IF NOT EXISTS idx_promo_code_redemptions_expires_at ON promo_code_redemptions(expires_at);

-- Erstelle Unique Constraint: Ein User kann einen Promo Code nur einmal einlösen
CREATE UNIQUE INDEX IF NOT EXISTS idx_promo_code_redemptions_unique_user_code 
ON promo_code_redemptions(user_id, promo_code);

-- RLS (Row Level Security) aktivieren
ALTER TABLE promo_code_redemptions ENABLE ROW LEVEL SECURITY;

-- Policy: Nutzer können nur ihre eigenen Promo Code Einlösungen sehen
CREATE POLICY "Users can view their own promo code redemptions" ON promo_code_redemptions
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Nutzer können ihre eigenen Promo Code Einlösungen erstellen
CREATE POLICY "Users can create their own promo code redemptions" ON promo_code_redemptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Nutzer können ihre eigenen Promo Code Einlösungen aktualisieren
CREATE POLICY "Users can update their own promo code redemptions" ON promo_code_redemptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Trigger für updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_promo_code_redemptions_updated_at 
    BEFORE UPDATE ON promo_code_redemptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Kommentare für bessere Dokumentation
COMMENT ON TABLE promo_code_redemptions IS 'Speichert alle Promo Code Einlösungen der Nutzer';
COMMENT ON COLUMN promo_code_redemptions.user_id IS 'Referenz zum Nutzer der den Promo Code eingelöst hat';
COMMENT ON COLUMN promo_code_redemptions.promo_code IS 'Der eingelöste Promo Code';
COMMENT ON COLUMN promo_code_redemptions.redeemed_at IS 'Zeitpunkt der Einlösung';
COMMENT ON COLUMN promo_code_redemptions.expires_at IS 'Ablaufzeitpunkt des Promo Code Benefits';
COMMENT ON COLUMN promo_code_redemptions.status IS 'Status der Einlösung (active, expired, cancelled)';

-- Erst<PERSON>t die Tabelle für Bewerbungen
CREATE TABLE IF NOT EXISTS public.applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    job_title TEXT NOT NULL,
    company_name TEXT,
    recipient_email TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'gesendet',
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    last_updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    job_id TEXT,
    cover_letter_text TEXT,
    cv_file_id TEXT,
    response_received_at TIMESTAMP WITH TIME ZONE,
    response_content TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Erstellt einen Index für schnellere Abfragen nach Benutzer-ID
CREATE INDEX IF NOT EXISTS applications_user_id_idx ON public.applications(user_id);

-- <PERSON><PERSON><PERSON>t einen Index für schnellere Abfragen nach Job-ID
CREATE INDEX IF NOT EXISTS applications_job_id_idx ON public.applications(job_id);

-- Erstellt einen Index für schnellere Abfragen nach Status
CREATE INDEX IF NOT EXISTS applications_status_idx ON public.applications(status);

-- Erstellt einen Index für schnellere Abfragen nach Sendedatum
CREATE INDEX IF NOT EXISTS applications_sent_at_idx ON public.applications(sent_at);

-- Fügt RLS-Richtlinien hinzu
ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;

-- Richtlinie: Benutzer können nur ihre eigenen Bewerbungen sehen
CREATE POLICY "Benutzer können nur ihre eigenen Bewerbungen sehen" 
ON public.applications FOR SELECT 
USING (auth.uid() = user_id);

-- Richtlinie: Benutzer können nur ihre eigenen Bewerbungen einfügen
CREATE POLICY "Benutzer können nur ihre eigenen Bewerbungen einfügen" 
ON public.applications FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Richtlinie: Benutzer können nur ihre eigenen Bewerbungen aktualisieren
CREATE POLICY "Benutzer können nur ihre eigenen Bewerbungen aktualisieren" 
ON public.applications FOR UPDATE 
USING (auth.uid() = user_id);

-- Richtlinie: Benutzer können nur ihre eigenen Bewerbungen löschen
CREATE POLICY "Benutzer können nur ihre eigenen Bewerbungen löschen" 
ON public.applications FOR DELETE 
USING (auth.uid() = user_id);

-- Trigger für die Aktualisierung des last_updated_at-Felds
CREATE OR REPLACE FUNCTION update_applications_last_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_applications_last_updated_at_trigger
BEFORE UPDATE ON public.applications
FOR EACH ROW
EXECUTE FUNCTION update_applications_last_updated_at();

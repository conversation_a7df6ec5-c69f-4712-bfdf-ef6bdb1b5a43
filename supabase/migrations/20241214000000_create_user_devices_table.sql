-- Migration: Anti-Manipulation System - Device Tracking
-- <PERSON><PERSON><PERSON>t Tabelle zur Verfolgung von Geräten pro Benutzer um mehrere Accounts pro Gerät zu erkennen

-- 1. <PERSON><PERSON>elle user_devices Tabelle
CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_info JSONB DEFAULT '{}', -- <PERSON><PERSON><PERSON><PERSON><PERSON>ce-Infos (OS, Model, App-Version, etc.)
    first_seen TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_seen TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_primary BOOLEAN DEFAULT FALSE, -- Hauptgerät des Users (erstes registriertes Gerät)
    is_blocked BOOLEAN DEFAULT FALSE, -- <PERSON><PERSON><PERSON> ges<PERSON>rt wegen Manipulation
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 2. <PERSON><PERSON><PERSON> Indizes für Performance
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_first_seen ON public.user_devices(first_seen);
CREATE INDEX IF NOT EXISTS idx_user_devices_is_blocked ON public.user_devices(is_blocked);

-- 3. Erstelle Unique Constraint (Ein User kann ein Device nur einmal registrieren)
ALTER TABLE public.user_devices 
ADD CONSTRAINT unique_device_user UNIQUE (device_id, user_id);

-- 4. Aktiviere Row Level Security
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;

-- 5. RLS Policies - Users können nur ihre eigenen Devices sehen
CREATE POLICY "Users can view their own devices" 
  ON public.user_devices 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own devices" 
  ON public.user_devices 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own devices" 
  ON public.user_devices 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Service Role kann alle Devices verwalten (für Admin-Funktionen)
CREATE POLICY "Service can manage all devices" 
  ON public.user_devices 
  USING (auth.role() = 'service_role');

-- 6. Trigger für updated_at
CREATE OR REPLACE FUNCTION public.update_user_devices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.last_seen = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_devices_updated_at
    BEFORE UPDATE ON public.user_devices
    FOR EACH ROW
    EXECUTE FUNCTION public.update_user_devices_updated_at();

-- 7. Funktion zur Device-Registrierung
CREATE OR REPLACE FUNCTION public.register_user_device(
    p_user_id UUID,
    p_device_id TEXT,
    p_device_info JSONB DEFAULT '{}'
)
RETURNS TABLE (
    success BOOLEAN,
    is_new_device BOOLEAN,
    device_count INTEGER,
    warning_message TEXT
) AS $$
DECLARE
    v_existing_device RECORD;
    v_device_count INTEGER;
    v_other_users_count INTEGER;
    v_is_new_device BOOLEAN := FALSE;
    v_warning_message TEXT := NULL;
BEGIN
    -- Prüfe ob Device bereits für diesen User registriert ist
    SELECT * INTO v_existing_device
    FROM public.user_devices
    WHERE user_id = p_user_id AND device_id = p_device_id;
    
    IF v_existing_device.id IS NULL THEN
        -- Neues Device für diesen User
        v_is_new_device := TRUE;
        
        -- Prüfe ob andere Users bereits dieses Device nutzen
        SELECT COUNT(*) INTO v_other_users_count
        FROM public.user_devices
        WHERE device_id = p_device_id AND user_id != p_user_id;
        
        -- Warnung wenn Device bereits von anderen Usern genutzt wird
        IF v_other_users_count > 0 THEN
            v_warning_message := 'WARNUNG: Dieses Gerät ist bereits mit ' || v_other_users_count || ' anderen Account(s) verknüpft. Mehrere Accounts pro Gerät sind nicht erlaubt.';
        END IF;
        
        -- Prüfe ob dies das erste Device des Users ist (Primary Device)
        SELECT COUNT(*) INTO v_device_count
        FROM public.user_devices
        WHERE user_id = p_user_id;
        
        -- Registriere das neue Device
        INSERT INTO public.user_devices (
            user_id,
            device_id,
            device_info,
            is_primary,
            is_blocked
        ) VALUES (
            p_user_id,
            p_device_id,
            p_device_info,
            (v_device_count = 0), -- Erstes Device wird Primary
            (v_other_users_count > 0) -- Blockiere wenn bereits andere User
        );
        
        v_device_count := v_device_count + 1;
    ELSE
        -- Existierendes Device - aktualisiere last_seen und device_info
        UPDATE public.user_devices
        SET last_seen = NOW(),
            device_info = p_device_info,
            updated_at = NOW()
        WHERE id = v_existing_device.id;
        
        -- Hole aktuelle Device-Anzahl
        SELECT COUNT(*) INTO v_device_count
        FROM public.user_devices
        WHERE user_id = p_user_id;
    END IF;
    
    RETURN QUERY SELECT 
        TRUE::BOOLEAN as success,
        v_is_new_device as is_new_device,
        v_device_count as device_count,
        v_warning_message as warning_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Funktion zur Device-Validation vor Bewerbungen
CREATE OR REPLACE FUNCTION public.validate_device_for_applications(
    p_user_id UUID,
    p_device_id TEXT
)
RETURNS TABLE (
    is_valid BOOLEAN,
    reason TEXT,
    blocked_until TIMESTAMPTZ
) AS $$
DECLARE
    v_device RECORD;
    v_other_users_count INTEGER;
    v_recent_accounts_count INTEGER;
BEGIN
    -- Hole Device-Info für diesen User
    SELECT * INTO v_device
    FROM public.user_devices
    WHERE user_id = p_user_id AND device_id = p_device_id;
    
    -- Device nicht registriert
    IF v_device.id IS NULL THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as is_valid,
            'Device nicht registriert'::TEXT as reason,
            NULL::TIMESTAMPTZ as blocked_until;
        RETURN;
    END IF;
    
    -- Device explizit gesperrt
    IF v_device.is_blocked THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as is_valid,
            'Device wegen Manipulation gesperrt'::TEXT as reason,
            (v_device.created_at + INTERVAL '30 days')::TIMESTAMPTZ as blocked_until;
        RETURN;
    END IF;
    
    -- Prüfe auf andere User mit gleichem Device
    SELECT COUNT(*) INTO v_other_users_count
    FROM public.user_devices
    WHERE device_id = p_device_id AND user_id != p_user_id;
    
    IF v_other_users_count > 0 THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as is_valid,
            ('Device mit ' || v_other_users_count || ' anderen Account(s) verknüpft')::TEXT as reason,
            NULL::TIMESTAMPTZ as blocked_until;
        RETURN;
    END IF;
    
    -- Prüfe auf verdächtige Aktivitäten (viele neue Accounts in kurzer Zeit)
    SELECT COUNT(*) INTO v_recent_accounts_count
    FROM public.user_devices ud
    JOIN auth.users u ON ud.user_id = u.id
    WHERE ud.device_id = p_device_id 
      AND u.created_at > (NOW() - INTERVAL '7 days');
    
    IF v_recent_accounts_count > 2 THEN
        -- Sperre Device temporär
        UPDATE public.user_devices
        SET is_blocked = TRUE,
            updated_at = NOW()
        WHERE device_id = p_device_id;
        
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as is_valid,
            'Verdächtige Aktivität: Zu viele neue Accounts'::TEXT as reason,
            (NOW() + INTERVAL '7 days')::TIMESTAMPTZ as blocked_until;
        RETURN;
    END IF;
    
    -- Device ist valid
    RETURN QUERY SELECT 
        TRUE::BOOLEAN as is_valid,
        'Device validiert'::TEXT as reason,
        NULL::TIMESTAMPTZ as blocked_until;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Erweitere check_free_application_counter um Device-Validation
CREATE OR REPLACE FUNCTION public.check_free_application_counter_with_device(
    p_user_id UUID,
    p_device_id TEXT
)
RETURNS TABLE (
    success BOOLEAN,
    remaining_applications INTEGER,
    device_valid BOOLEAN,
    warning_message TEXT
) AS $$
DECLARE
    v_device_validation RECORD;
    v_counter_result BOOLEAN;
    v_remaining INTEGER := 0;
BEGIN
    -- 1. Validiere Device zuerst
    SELECT * INTO v_device_validation
    FROM public.validate_device_for_applications(p_user_id, p_device_id);
    
    IF NOT v_device_validation.is_valid THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as success,
            0::INTEGER as remaining_applications,
            FALSE::BOOLEAN as device_valid,
            v_device_validation.reason::TEXT as warning_message;
        RETURN;
    END IF;
    
    -- 2. Führe normale Counter-Prüfung durch
    SELECT public.check_free_application_counter(p_user_id) INTO v_counter_result;
    
    -- 3. Hole verbleibende Bewerbungen
    SELECT remaining INTO v_remaining
    FROM public.get_remaining_applications(p_user_id);
    
    RETURN QUERY SELECT 
        v_counter_result::BOOLEAN as success,
        COALESCE(v_remaining, 0)::INTEGER as remaining_applications,
        TRUE::BOOLEAN as device_valid,
        NULL::TEXT as warning_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Kommentare für Dokumentation
COMMENT ON TABLE public.user_devices IS 'Tracking von Geräten pro Benutzer zur Erkennung von Account-Manipulation';
COMMENT ON FUNCTION public.register_user_device IS 'Registriert ein Gerät für einen Benutzer und prüft auf Manipulation';
COMMENT ON FUNCTION public.validate_device_for_applications IS 'Validiert ob ein Gerät für Bewerbungen verwendet werden darf';
COMMENT ON FUNCTION public.check_free_application_counter_with_device IS 'Erweiterte Counter-Prüfung mit Device-Validation';

-- Erstellt die admin_logs-Tabelle für die Protokollierung von administrativen Aktionen
CREATE TABLE IF NOT EXISTS public.admin_logs (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  action TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  admin_id UUID,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index für schnellere Abfragen
CREATE INDEX IF NOT EXISTS admin_logs_action_idx ON public.admin_logs(action);
CREATE INDEX IF NOT EXISTS admin_logs_user_id_idx ON public.admin_logs(user_id);
CREATE INDEX IF NOT EXISTS admin_logs_created_at_idx ON public.admin_logs(created_at);

-- RLS-Richtlinien
ALTER TABLE public.admin_logs ENABLE ROW LEVEL SECURITY;

-- Nur Service-Rolle kann Logs erstellen und lesen
CREATE POLICY "Service role can manage admin logs"
  ON public.admin_logs
  USING (auth.jwt() ->> 'role' = 'service_role');

-- <PERSON><PERSON><PERSON>, die von der Edge Function aufgerufen werden kann,
-- um die admin_logs-Tabelle zu erstellen, falls sie nicht existiert
CREATE OR REPLACE FUNCTION public.create_admin_logs_table()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Prüfen, ob die Tabelle bereits existiert
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'admin_logs'
  ) THEN
    -- Tabelle erstellen
    CREATE TABLE public.admin_logs (
      id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
      action TEXT NOT NULL,
      user_id UUID REFERENCES auth.users(id),
      admin_id UUID,
      details JSONB,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Index für schnellere Abfragen
    CREATE INDEX admin_logs_action_idx ON public.admin_logs(action);
    CREATE INDEX admin_logs_user_id_idx ON public.admin_logs(user_id);
    CREATE INDEX admin_logs_created_at_idx ON public.admin_logs(created_at);
    
    -- RLS-Richtlinien
    ALTER TABLE public.admin_logs ENABLE ROW LEVEL SECURITY;
    
    -- Nur Service-Rolle kann Logs erstellen und lesen
    CREATE POLICY "Service role can manage admin logs"
      ON public.admin_logs
      USING (auth.jwt() ->> 'role' = 'service_role');
  END IF;
  
  RETURN TRUE;
END;
$$;

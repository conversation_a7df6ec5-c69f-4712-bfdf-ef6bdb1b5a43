#!/bin/bash

# 🚀 Produktions-Setup Script für Google Play Store Integration
# Dieses Script hilft bei der Konfiguration der erforderlichen Komponenten

set -e  # Exit bei Fehlern

echo "🚀 JobAssistent - Produktions-Setup für Google Play Store Integration"
echo "================================================================="

# Farben für Output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funktionen
print_step() {
    echo -e "\n${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Schritt 1: Voraussetzungen prüfen
print_step "Schritt 1: Voraussetzungen prüfen"

# Supabase CLI prüfen
if command -v supabase &> /dev/null; then
    print_success "Supabase CLI ist installiert"
else
    print_error "Supabase CLI ist nicht installiert"
    echo "Installiere mit: npm install -g supabase"
    exit 1
fi

# Flutter prüfen
if command -v flutter &> /dev/null; then
    print_success "Flutter ist installiert"
else
    print_error "Flutter ist nicht installiert"
    exit 1
fi

# Schritt 2: Package Name extrahieren
print_step "Schritt 2: Package Name aus build.gradle extrahieren"

BUILD_GRADLE="android/app/build.gradle"
if [ -f "$BUILD_GRADLE" ]; then
    PACKAGE_NAME=$(grep -o 'applicationId "[^"]*"' "$BUILD_GRADLE" | sed 's/applicationId "\(.*\)"/\1/')
    if [ -n "$PACKAGE_NAME" ]; then
        print_success "Package Name gefunden: $PACKAGE_NAME"
        echo "GOOGLE_PLAY_PACKAGE_NAME=$PACKAGE_NAME" > .env.production
    else
        print_error "Package Name nicht in build.gradle gefunden"
        exit 1
    fi
else
    print_error "build.gradle nicht gefunden: $BUILD_GRADLE"
    exit 1
fi

# Schritt 3: Service Account JSON Setup
print_step "Schritt 3: Google Play Service Account JSON konfigurieren"

echo "Bitte folge diesen Schritten:"
echo "1. Gehe zu Google Cloud Console: https://console.cloud.google.com/"
echo "2. Aktiviere die Google Play Android Developer API"
echo "3. Erstelle einen Service Account"
echo "4. Lade die JSON-Datei herunter"
echo "5. Speichere sie als 'google-play-service-account.json' in diesem Verzeichnis"
echo ""
read -p "Drücke Enter, wenn du die JSON-Datei heruntergeladen und gespeichert hast..."

SERVICE_ACCOUNT_FILE="google-play-service-account.json"
if [ -f "$SERVICE_ACCOUNT_FILE" ]; then
    print_success "Service Account JSON gefunden"
    
    # JSON validieren
    if jq empty "$SERVICE_ACCOUNT_FILE" 2>/dev/null; then
        print_success "JSON ist gültig"
        
        # JSON Inhalt für Supabase vorbereiten
        JSON_CONTENT=$(cat "$SERVICE_ACCOUNT_FILE" | jq -c .)
        echo "GOOGLE_PLAY_SERVICE_ACCOUNT_JSON='$JSON_CONTENT'" >> .env.production
        
        print_success "Service Account JSON zu .env.production hinzugefügt"
    else
        print_error "JSON-Datei ist ungültig"
        exit 1
    fi
else
    print_error "Service Account JSON nicht gefunden: $SERVICE_ACCOUNT_FILE"
    echo "Bitte lade die Datei herunter und speichere sie als '$SERVICE_ACCOUNT_FILE'"
    exit 1
fi

# Schritt 4: Supabase Login prüfen
print_step "Schritt 4: Supabase Verbindung prüfen"

if supabase status &> /dev/null; then
    print_success "Supabase ist verbunden"
else
    print_warning "Supabase Login erforderlich"
    echo "Führe aus: supabase login"
    read -p "Drücke Enter nach dem Login..."
fi

# Schritt 5: Umgebungsvariablen setzen
print_step "Schritt 5: Supabase Umgebungsvariablen setzen"

echo "Setze Umgebungsvariablen in Supabase..."

# Package Name setzen
if supabase secrets set GOOGLE_PLAY_PACKAGE_NAME="$PACKAGE_NAME"; then
    print_success "GOOGLE_PLAY_PACKAGE_NAME gesetzt"
else
    print_error "Fehler beim Setzen von GOOGLE_PLAY_PACKAGE_NAME"
fi

# Service Account JSON setzen
if supabase secrets set GOOGLE_PLAY_SERVICE_ACCOUNT_JSON="$JSON_CONTENT"; then
    print_success "GOOGLE_PLAY_SERVICE_ACCOUNT_JSON gesetzt"
else
    print_error "Fehler beim Setzen von GOOGLE_PLAY_SERVICE_ACCOUNT_JSON"
fi

# Schritt 6: Edge Function Status prüfen
print_step "Schritt 6: Edge Function Status prüfen"

echo "Prüfe verify-purchase Edge Function..."
if supabase functions list | grep -q "verify-purchase"; then
    print_success "verify-purchase Edge Function ist deployed"
else
    print_warning "verify-purchase Edge Function nicht gefunden"
    echo "Die Function sollte bereits deployed sein. Prüfe das Supabase Dashboard."
fi

# Schritt 7: Test-Konfiguration
print_step "Schritt 7: Test-Konfiguration vorbereiten"

cat > test_purchase.dart << 'EOF'
// Test-Script für Purchase Verification
// Führe dieses Script aus, um die Integration zu testen

import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() {
  group('Purchase Verification Tests', () {
    test('Edge Function erreichbar', () async {
      // Test ob Edge Function erreichbar ist
      final response = await Supabase.instance.client.functions.invoke(
        'verify-purchase',
        body: {
          'test': true,
        },
      );
      
      expect(response.status, equals(200));
    });
  });
}
EOF

print_success "Test-Datei erstellt: test_purchase.dart"

# Schritt 8: Sicherheits-Checkliste
print_step "Schritt 8: Sicherheits-Checkliste"

echo "📋 Sicherheits-Checkliste:"
echo "  ✅ Service Account JSON nicht in Git committen"
echo "  ✅ Umgebungsvariablen nur über Supabase Secrets"
echo "  ✅ Package Name korrekt konfiguriert"
echo "  ✅ Google Play Console Berechtigungen gesetzt"
echo ""
print_warning "WICHTIG: Lösche die lokale JSON-Datei nach dem Setup!"
echo "rm google-play-service-account.json"

# Schritt 9: Nächste Schritte
print_step "Schritt 9: Nächste Schritte"

echo "🎯 Nächste Schritte:"
echo "1. Google Play Console Berechtigungen konfigurieren:"
echo "   - Gehe zu https://play.google.com/console/"
echo "   - Setup > API access > Service accounts"
echo "   - Klicke 'Manage Play Console permissions'"
echo "   - Setze Berechtigungen:"
echo "     ✓ View financial data, orders, and cancellation survey responses"
echo "     ✓ View app information and download bulk reports (read-only)"
echo "     ✓ Manage orders and subscriptions"
echo ""
echo "2. Test-Käufe durchführen:"
echo "   - Internal Testing Track erstellen"
echo "   - Signierte APK/AAB hochladen"
echo "   - Test-Benutzer hinzufügen"
echo ""
echo "3. Monitoring einrichten:"
echo "   - Supabase Dashboard > Edge Functions > Logs"
echo "   - Custom Monitoring-Tabelle erstellen (siehe PRODUCTION_SETUP_GUIDE.md)"
echo ""
echo "4. Production Deployment:"
echo "   - Debug-Modus in payment_service.dart deaktivieren"
echo "   - Rate Limiting implementieren"
echo "   - Comprehensive Error Handling"

print_success "Setup abgeschlossen! 🎉"
print_warning "Vergiss nicht, die lokale JSON-Datei zu löschen!"

echo ""
echo "📖 Vollständige Anleitung: PRODUCTION_SETUP_GUIDE.md"
echo "🧪 Test-Script: test_purchase.dart"
echo "⚙️  Umgebungsvariablen: .env.production"
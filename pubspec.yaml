name: ki_test
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.6+6

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  shared_preferences: ^2.5.3
  # Authentication
  google_sign_in: ^6.3.0 # Google Sign-In für native Integration
  flutter_web_auth_2: ^4.1.0 # Für In-App-Browser-Authentifizierung
  app_links: ^6.3.2 # Für Deep Link Verarbeitung (Passwort-Reset)

  googleapis: ^12.0.0 # Für Google APIs (Gmail, etc.)
  googleapis_auth: ^1.4.1 # Für Google API Authentifizierung
  extension_google_sign_in_as_googleapis_auth: ^2.0.12 # Für einfache Authentifizierung mit Google APIs
  # ... restliche Pakete und andere ...

  # UI & State Management
  hooks_riverpod: ^2.5.1
  provider: ^6.1.2
  flutter_svg: ^2.0.10+1
  go_router: ^14.1.1 # NEU: Hinzugefügt für Navigation
  # ... restliche Abhängigkeiten ...

  # Monetization
  in_app_purchase: ^3.2.1 # NEU: Hinzugefügt für In-App Käufe
  google_mobile_ads: ^5.1.0 # Wieder aktiviert mit Java 17 Kompatibilität

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  flutter_hooks: ^0.21.2
  get_it: ^8.0.3
  http: ^1.3.0
  flutter_secure_storage: ^9.2.4
  url_launcher: ^6.3.1
  google_fonts: ^6.3.0
  shimmer: ^3.0.0

  universal_html: ^2.2.4
  file_picker: ^10.0.0
  path_provider: ^2.1.5
  location: ^6.0.2 # Get device location
  geocoding: ^3.0.0 # Convert coordinates to addresses
  intl: ^0.19.0
  dio: ^5.5.0+1 # Hinzugefügt für HTTP-Anfragen (z.B. API-Calls)
  geolocator: ^12.0.0 # Hinzugefügt für genauere Standortbestimmung (Ersatz für location, falls gewünscht?)
  syncfusion_flutter_pdf: ^29.1.37
  image_picker: ^1.1.2 # Hinzugefügt für Bildauswahl
  permission_handler: ^12.0.0+1 # Hinzugefügt für Berechtigungen
  google_generative_ai: ^0.4.6 # Aktualisiert für Gemini API
  # file_picker: ^10.0.0 # Bereits oben vorhanden, Duplikat entfernt
  flutter_email_sender: ^6.0.3 # NEU: Für E-Mail-Versand mit Anhang
  supabase_flutter: ^2.5.6 # Neueste Version hinzufügen
  flutter_dotenv: ^5.1.0 # <--- HINZUGEFÜGT
  clipboard: ^0.1.3 # <--- HINZUGEFÜGT
  flutter_pdfview: ^1.4.1+1 # NEU: Hinzugefügt für PDF Anzeige
  logger: ^2.3.0 # <--- HINZUGEFÜGT
  flutter_phoenix: ^1.1.1 # <--- HINZUGEFÜGT für App-Neustart
  riverpod: ^2.5.1 # <--- HINZUGEFÜGT für Ref
  riverpod_annotation: ^2.3.5 # <--- HINZUGEFÜGT für @riverpod
  just_audio: ^0.9.36 # <--- HINZUGEFÜGT für Musikwiedergabe im Onboarding
  crypto: ^3.0.3 # <--- HINZUGEFÜGT für Dateiverschlüsselung
  html: ^0.15.4 # <--- HINZUGEFÜGT für HTML-Parsing (Text-Extraktion)
  device_info_plus: ^9.1.2 # <--- HINZUGEFÜGT für Geräte-Informationen
  uuid: ^4.4.0 # <--- HINZUGEFÜGT für Generierung von UUIDs
  open_file: ^3.3.2 # <--- HINZUGEFÜGT für Öffnen von Dateien nach dem Download
  json_annotation: ^4.9.0
  connectivity_plus: ^6.1.4
  pdf: ^3.11.3
  printing: ^5.14.2
  share_plus: ^11.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.5.4
  riverpod_generator: ^2.4.4
  mockito: ^5.4.4

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  json_serializable: ^6.9.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true # Aktiviert für Lokalisierung

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - _resources/assets/icons/
    - _resources/assets/audio/
    - _resources/assets/animations/
    - assets/cv_previews/
    # - _resources/configs/ # Auskommentiert, da Ordner ggf. nicht benötigt wird
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  # see https://flutter.dev/to/font-from-package

  # see https://flutter.dev/to/font-from-package

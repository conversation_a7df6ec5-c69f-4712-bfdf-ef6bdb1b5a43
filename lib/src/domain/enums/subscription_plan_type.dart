/// Enum für die verschiedenen Abonnement-Typen
enum SubscriptionPlanType {
  /// Basic-Plan (kostenlos mit Werbung)
  basic,

  /// Pro-Plan (kostenpflichtig ohne Werbung)
  pro,

  /// Unlimited-Plan (kostenpflichtig ohne Werbung und unbegrenzte Bewerbungen)
  unlimited,
}

/// Erweiterung für SubscriptionPlanType
extension SubscriptionPlanTypeExtension on SubscriptionPlanType {
  /// Gibt den String-Wert des Abonnement-Typs zurück
  String get value {
    switch (this) {
      case SubscriptionPlanType.basic:
        return 'basic';
      case SubscriptionPlanType.pro:
        return 'pro';
      case SubscriptionPlanType.unlimited:
        return 'unlimited';
    }
  }

  /// Gibt die Anzahl der Bewerbungen für den Abonnement-Typ zurück
  int get applicationCount {
    switch (this) {
      case SubscriptionPlanType.basic:
        return 30;
      case SubscriptionPlanType.pro:
        return 150;
      case SubscriptionPlanType.unlimited:
        return -1; // -1 bedeutet unbegrenzt
    }
  }

  /// G<PERSON><PERSON> zurück, ob der Abonnement-Typ Premium ist
  bool get isPremium {
    return this != SubscriptionPlanType.basic;
  }
}

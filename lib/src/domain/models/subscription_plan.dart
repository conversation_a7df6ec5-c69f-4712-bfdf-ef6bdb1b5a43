import 'package:flutter/material.dart';

/// Definiert die verschiedenen Abonnementpläne
enum SubscriptionPlanType {
  /// Basis-Plan mit begrenzten Funktionen und Werbung
  basic,

  /// Pro-Plan mit mehr Funktionen und ohne Werbung
  pro,

  /// Unlimited-Plan mit allen Funktionen und unbegrenzten Bewerbungen
  unlimited,
}

/// Modell für einen Abonnementplan
class SubscriptionPlan {
  /// Typ des Abonnementplans
  final SubscriptionPlanType? type;

  /// Typ des Plans als String (basic, pro, unlimited)
  final String? planType;

  /// Name des Plans
  final String name;

  /// Preis des Plans in Euro
  final double price;

  /// Anzahl der Bewerbungen pro Monat (null für unbegrenzt)
  final int? applicationsPerMonth;

  /// Zeigt an, ob Werbung angezeigt wird
  final bool showsAds;

  /// Beschreibung der Vorteile des Plans
  final String benefits;

  /// Gewinn nach DeepSeek-Kosten in Euro
  final double profitAfterDeepSeek;

  /// Ablaufdatum des Abonnements
  final DateTime? expiryDate;

  /// Gibt an, ob das Abonnement aktiv ist
  final bool isActive;

  /// Erstellt einen neuen Abonnementplan
  const SubscriptionPlan({
    this.type,
    this.planType,
    required this.name,
    required this.price,
    this.applicationsPerMonth,
    required this.showsAds,
    required this.benefits,
    required this.profitAfterDeepSeek,
    this.expiryDate,
    this.isActive = false,
  });

  /// Gibt die Farbe für die Darstellung des Plans zurück
  Color getColor(BuildContext context) {
    if (type == null) {
      // Fallback basierend auf planType String
      switch (planType?.toLowerCase()) {
        case 'basic':
          return Colors.blue;
        case 'pro':
          return Colors.purple;
        case 'unlimited':
          return Colors.orange;
        default:
          return Colors.grey;
      }
    }

    switch (type!) {
      case SubscriptionPlanType.basic:
        return Colors.blue;
      case SubscriptionPlanType.pro:
        return Colors.purple;
      case SubscriptionPlanType.unlimited:
        return Colors.orange;
    }
  }

  /// Gibt den Produktschlüssel für In-App-Käufe zurück
  String get productKey {
    if (type == null) {
      // Fallback basierend auf planType String
      switch (planType?.toLowerCase()) {
        case 'basic':
          return 'basic_subscription';
        case 'pro':
          return 'pro_subscription';
        case 'unlimited':
          return 'unlimited_subscription';
        default:
          return 'unknown_subscription';
      }
    }

    switch (type!) {
      case SubscriptionPlanType.basic:
        return 'basic_subscription';
      case SubscriptionPlanType.pro:
        return 'pro_subscription';
      case SubscriptionPlanType.unlimited:
        return 'unlimited_subscription';
    }
  }

  /// Gibt die Anzahl der Bewerbungen als String zurück
  String get applicationsText {
    if (applicationsPerMonth == null) {
      return 'Unbegrenzt';
    }
    return '$applicationsPerMonth';
  }

  /// Gibt den Preis als formatierten String zurück
  String get priceText {
    return '${price.toStringAsFixed(2)} €';
  }

  /// Gibt den Gewinn nach DeepSeek als formatierten String zurück
  String get profitText {
    return '${profitAfterDeepSeek.toStringAsFixed(2)} €';
  }

  /// Gibt die Werbeinformation als String zurück
  String get adsText {
    return showsAds ? 'Mit Werbung' : 'Ohne Werbung';
  }
}

/// Provider für die verfügbaren Abonnementpläne
class SubscriptionPlans {
  /// Basic-Plan
  static const SubscriptionPlan basic = SubscriptionPlan(
    type: SubscriptionPlanType.basic,
    name: 'Basic',
    price: 6.99,
    applicationsPerMonth: 30,
    showsAds: true,
    benefits: '30 Bewerbungen/Monat, mit Werbung',
    profitAfterDeepSeek: 4.68,
  );

  /// Pro-Plan
  static const SubscriptionPlan pro = SubscriptionPlan(
    type: SubscriptionPlanType.pro,
    name: 'Pro',
    price: 14.99,
    applicationsPerMonth: 150,
    showsAds: false,
    benefits: '150 Bewerbungen/Monat, ohne Werbung',
    profitAfterDeepSeek: 9.44,
  );

  /// Unlimited-Plan
  static const SubscriptionPlan unlimited = SubscriptionPlan(
    type: SubscriptionPlanType.unlimited,
    name: 'Unlimited',
    price: 29.99,
    applicationsPerMonth: null,
    showsAds: false,
    benefits: 'Unbegrenzt, 1-Klick, alle Funktionen frei',
    profitAfterDeepSeek: 18.89,
  );

  /// Liste aller verfügbaren Pläne
  static const List<SubscriptionPlan> allPlans = [basic, pro, unlimited];

  /// Gibt den Plan für den angegebenen Typ zurück
  static SubscriptionPlan getPlan(SubscriptionPlanType type) {
    switch (type) {
      case SubscriptionPlanType.basic:
        return basic;
      case SubscriptionPlanType.pro:
        return pro;
      case SubscriptionPlanType.unlimited:
        return unlimited;
    }
  }
}

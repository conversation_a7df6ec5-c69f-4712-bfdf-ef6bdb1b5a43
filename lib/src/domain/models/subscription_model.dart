/// Datenmodell für ein Premium-Abonnement
class SubscriptionModel {
  /// Eindeutige ID des Abonnements
  final String? subscriptionId;

  /// Benutzer-ID des Abonnenten
  final String userId;

  /// Art des Abonnements (nur 'monthly' verfügbar, 'yearly' nur über Admin-Zugriff)
  final String? subscriptionType;

  /// Plan-Typ des Abonnements (z.B. 'basic', 'pro', 'unlimited')
  final String? planType;

  /// Anbie<PERSON>, über den die Zahlung abgewickelt wurde
  final String? paymentProvider;

  /// Transaktions-ID vom Zahlungsanbieter
  final String? transactionId;

  /// Enthält die Zahlungsbestätigung für spätere Überprüfungen
  final String? receiptData;

  /// Ist das Abonnement derzeit aktiv
  final bool isPremium;

  /// Automatische Verlängerung aktiviert
  final bool autoRenew;

  /// Anzahl der verbleibenden Bewerbungen
  final int? remainingApplications;

  /// Anzahl der insgesamt verfügbaren Bewerbungen
  final int? totalApplications;

  /// Datum der letzten Aktualisierung der Bewerbungszähler
  final DateTime? applicationsResetDate;

  /// Startdatum des Abonnements
  final DateTime? startDate;

  /// Enddatum des Abonnements
  final DateTime? endDate;

  /// Erstellungsdatum des Datensatzes
  final DateTime? createdAt;

  /// Letztes Aktualisierungsdatum des Datensatzes
  final DateTime? updatedAt;

  /// Zeitpunkt der Stornierung (falls storniert)
  final DateTime? canceledAt;

  SubscriptionModel({
    this.subscriptionId,
    required this.userId,
    this.subscriptionType,
    this.planType,
    this.paymentProvider,
    this.transactionId,
    this.receiptData,
    this.isPremium = false,
    this.autoRenew = false,
    this.remainingApplications,
    this.totalApplications,
    this.applicationsResetDate,
    this.startDate,
    this.endDate,
    this.createdAt,
    this.updatedAt,
    this.canceledAt,
  });

  /// Erstellt ein SubscriptionModel aus einem JSON-Objekt
  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      subscriptionId: json['subscription_id'] ?? '',
      userId: json['user_id'] ?? '',
      subscriptionType: json['subscription_type'] ?? '',
      planType: json['plan_type'] ?? 'free',
      paymentProvider: json['payment_provider'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      receiptData: json['receipt_data'] ?? '',
      isPremium: json['is_premium'] ?? false,
      autoRenew: json['auto_renew'] ?? false,
      remainingApplications: json['remaining_applications'],
      totalApplications: json['total_applications'],
      applicationsResetDate:
          json['applications_reset_date'] != null
              ? DateTime.parse(json['applications_reset_date'])
              : null,
      startDate:
          json['start_date'] != null
              ? DateTime.parse(json['start_date'])
              : null,
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      canceledAt:
          json['canceled_at'] != null
              ? DateTime.parse(json['canceled_at'])
              : null,
    );
  }

  /// Konvertiert das Modell in ein JSON-Objekt
  Map<String, dynamic> toJson() {
    return {
      'subscription_id': subscriptionId,
      'user_id': userId,
      'subscription_type': subscriptionType,
      'plan_type': planType,
      'payment_provider': paymentProvider,
      'transaction_id': transactionId,
      'receipt_data': receiptData,
      'is_premium': isPremium,
      'auto_renew': autoRenew,
      'remaining_applications': remainingApplications,
      'total_applications': totalApplications,
      'applications_reset_date': applicationsResetDate?.toIso8601String(),
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'canceled_at': canceledAt?.toIso8601String(),
    };
  }

  /// Prüft, ob das Abonnement aktiv ist
  bool get isActive {
    if (endDate == null) {
      // Wenn kein Enddatum vorhanden ist, ist es unbegrenzt oder bis zur Kündigung aktiv
      return true;
    }
    // Überprüfe, ob das aktuelle Datum vor dem Enddatum liegt
    return DateTime.now().isBefore(endDate!);
  }

  /// Erzeugt eine String-Repräsentation des Abonnements
  @override
  String toString() {
    return 'SubscriptionModel{'
        'subscriptionId: $subscriptionId, '
        'userId: $userId, '
        'subscriptionType: $subscriptionType, '
        'planType: $planType, '
        'isPremium: $isPremium, '
        'autoRenew: $autoRenew, '
        'remainingApplications: $remainingApplications, '
        'totalApplications: $totalApplications, '
        'startDate: $startDate, '
        'endDate: $endDate, '
        'isActive: $isActive}';
  }

  /// Erstellt eine Kopie des Modells mit aktualisierten Werten
  SubscriptionModel copyWith({
    String? subscriptionId,
    String? userId,
    String? subscriptionType,
    String? planType,
    String? paymentProvider,
    String? transactionId,
    String? receiptData,
    bool? isPremium,
    bool? autoRenew,
    int? remainingApplications,
    int? totalApplications,
    DateTime? applicationsResetDate,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? canceledAt,
  }) {
    return SubscriptionModel(
      subscriptionId: subscriptionId ?? this.subscriptionId,
      userId: userId ?? this.userId,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      planType: planType ?? this.planType,
      paymentProvider: paymentProvider ?? this.paymentProvider,
      transactionId: transactionId ?? this.transactionId,
      receiptData: receiptData ?? this.receiptData,
      isPremium: isPremium ?? this.isPremium,
      autoRenew: autoRenew ?? this.autoRenew,
      remainingApplications:
          remainingApplications ?? this.remainingApplications,
      totalApplications: totalApplications ?? this.totalApplications,
      applicationsResetDate:
          applicationsResetDate ?? this.applicationsResetDate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      canceledAt: canceledAt ?? this.canceledAt,
    );
  }

  /// Vergleicht zwei SubscriptionModel-Objekte auf Gleichheit
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubscriptionModel &&
          runtimeType == other.runtimeType &&
          subscriptionId == other.subscriptionId &&
          userId == other.userId &&
          subscriptionType == other.subscriptionType &&
          planType == other.planType &&
          paymentProvider == other.paymentProvider &&
          transactionId == other.transactionId &&
          receiptData == other.receiptData &&
          isPremium == other.isPremium &&
          autoRenew == other.autoRenew &&
          remainingApplications == other.remainingApplications &&
          totalApplications == other.totalApplications &&
          applicationsResetDate == other.applicationsResetDate &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          createdAt == other.createdAt &&
          updatedAt == other.updatedAt &&
          canceledAt == other.canceledAt;

  /// Generiert einen Hash-Code für das Objekt
  @override
  int get hashCode =>
      subscriptionId.hashCode ^
      userId.hashCode ^
      subscriptionType.hashCode ^
      planType.hashCode ^
      paymentProvider.hashCode ^
      transactionId.hashCode ^
      receiptData.hashCode ^
      isPremium.hashCode ^
      autoRenew.hashCode ^
      remainingApplications.hashCode ^
      totalApplications.hashCode ^
      applicationsResetDate.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      canceledAt.hashCode;
}

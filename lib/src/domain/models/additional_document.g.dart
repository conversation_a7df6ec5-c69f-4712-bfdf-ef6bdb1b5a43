// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'additional_document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdditionalDocumentImpl _$$AdditionalDocumentImplFromJson(
  Map<String, dynamic> json,
) => _$AdditionalDocumentImpl(
  id: json['id'] as String,
  userId: json['userId'] as String,
  fileName: json['fileName'] as String,
  filePath: json['filePath'] as String,
  fileSize: (json['fileSize'] as num).toInt(),
  fileType: json['fileType'] as String,
  uploadDate: DateTime.parse(json['uploadDate'] as String),
  isActiveForApplications: json['isActiveForApplications'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$$AdditionalDocumentImplToJson(
  _$AdditionalDocumentImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'fileName': instance.fileName,
  'filePath': instance.filePath,
  'fileSize': instance.fileSize,
  'fileType': instance.fileType,
  'uploadDate': instance.uploadDate.toIso8601String(),
  'isActiveForApplications': instance.isActiveForApplications,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

_$DocumentUploadResultImpl _$$DocumentUploadResultImplFromJson(
  Map<String, dynamic> json,
) => _$DocumentUploadResultImpl(
  success: json['success'] as bool,
  documentId: json['documentId'] as String?,
  errorMessage: json['errorMessage'] as String?,
  document:
      json['document'] == null
          ? null
          : AdditionalDocument.fromJson(
            json['document'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$$DocumentUploadResultImplToJson(
  _$DocumentUploadResultImpl instance,
) => <String, dynamic>{
  'success': instance.success,
  'documentId': instance.documentId,
  'errorMessage': instance.errorMessage,
  'document': instance.document,
};

_$DocumentUploadProgressImpl _$$DocumentUploadProgressImplFromJson(
  Map<String, dynamic> json,
) => _$DocumentUploadProgressImpl(
  fileName: json['fileName'] as String,
  progress: (json['progress'] as num).toDouble(),
  status: $enumDecode(_$DocumentUploadStatusEnumMap, json['status']),
  errorMessage: json['errorMessage'] as String?,
);

Map<String, dynamic> _$$DocumentUploadProgressImplToJson(
  _$DocumentUploadProgressImpl instance,
) => <String, dynamic>{
  'fileName': instance.fileName,
  'progress': instance.progress,
  'status': _$DocumentUploadStatusEnumMap[instance.status]!,
  'errorMessage': instance.errorMessage,
};

const _$DocumentUploadStatusEnumMap = {
  DocumentUploadStatus.preparing: 'preparing',
  DocumentUploadStatus.uploading: 'uploading',
  DocumentUploadStatus.processing: 'processing',
  DocumentUploadStatus.completed: 'completed',
  DocumentUploadStatus.failed: 'failed',
};

// Import für Timestamp
import 'package:ki_test/src/domain/models/job_preferences.dart';

/// Benutzer-Profildaten
class UserProfile {
  final String? uid;
  final String? id; // Zusätzliche ID für Kompatibilität mit altem Code
  final String? name;
  final String? email;
  final String? phone;
  final String? phoneNumber; // Alias für phone für Kompatibilität
  final String? address;
  final List<String>? skills;
  final String? jobPreferences; // Job-Präferenzen als einfacher String
  final JobPreferences? jobPreferencesObj; // Strukturierte Job-Präferenzen
  final String? interests;
  final List<WorkExperience>? workExperience;
  final List<Education>? education;
  final bool? isPremium;
  final bool? testUser;
  final String? premiumExpiry;
  final DateTime? premiumExpiryDate; // Ablaufdatum für Premium-Status
  final String?
  premiumPlanType; // Typ des Premium-Plans (basic, pro, unlimited)
  final String? preferredWritingStyle; // Bevorzugter Schreibstil
  final String?
  applicationLength; // Bevorzugte Bewerbungslänge (Lang/Standard/Kurz)
  final bool?
  includeExperienceInApplication; // Ob Erfahrung in die Bewerbung einbezogen werden soll
  final String? experienceSummary; // Zusammenfassung der Erfahrung
  final String? globalAiHints; // Globale KI-Hinweise für alle Bewerbungen
  final List<String>? jobKeywords; // Von KI generierte Berufsbezeichnungen
  final Map<String, dynamic>? additionalCvData; // Zusätzliche CV-Daten
  final int? usedApplications; // Anzahl der verwendeten Bewerbungen

  // CV-bezogene Felder
  final String? cvUrl; // URL zum CV in Storage
  final String? cvDownloadUrl; // Download-URL für CV
  final String? cvFilePath; // Lokaler Pfad zur temporären Datei
  final String? cvFileName; // Name der hochgeladenen Datei
  final DateTime? cvLastModified; // Letzte Änderung
  final bool? cvAnalysisComplete; // Ob Analyse abgeschlossen ist
  final bool? cvAutoFillEnabled; // Ob automatisches Ausfüllen aktiviert ist
  final DateTime? cvAnalysisTimestamp; // Zeitstempel der letzten Analyse
  final List<AdditionalDocument>?
  additionalDocuments; // Liste für weitere Dokumente

  // Profilbild-bezogene Felder
  final String? profileImageUrl; // URL zum Profilbild in Storage
  final String? profileImagePath; // Lokaler Pfad zum Profilbild
  final DateTime? profileImageLastModified; // Letzte Änderung des Profilbilds

  /// Konstruktor für UserProfile
  UserProfile({
    this.uid,
    this.id,
    this.name,
    this.email,
    this.phone,
    this.phoneNumber,
    this.address,
    this.skills,
    this.jobPreferences,
    this.jobPreferencesObj,
    this.interests,
    this.workExperience,
    this.education,
    this.isPremium,
    this.testUser,
    this.premiumExpiry,
    this.premiumExpiryDate,
    this.premiumPlanType,
    this.preferredWritingStyle,
    this.applicationLength,
    this.includeExperienceInApplication,
    this.experienceSummary,
    this.globalAiHints,
    this.jobKeywords,
    this.additionalCvData,
    this.usedApplications,
    this.cvUrl,
    this.cvDownloadUrl,
    this.cvFilePath,
    this.cvFileName,
    this.cvLastModified,
    this.cvAnalysisComplete,
    this.cvAutoFillEnabled,
    this.cvAnalysisTimestamp,
    this.additionalDocuments,
    this.profileImageUrl,
    this.profileImagePath,
    this.profileImageLastModified,
  });

  /// Erstellt eine Kopie des UserProfile mit aktualisierten Werten
  UserProfile copyWith({
    String? uid,
    String? id,
    String? name,
    String? email,
    String? phone,
    String? phoneNumber,
    String? address,
    List<String>? skills,
    String? jobPreferences,
    JobPreferences? jobPreferencesObj,
    String? interests,
    List<WorkExperience>? workExperience,
    List<Education>? education,
    bool? isPremium,
    bool? testUser,
    String? premiumExpiry,
    DateTime? premiumExpiryDate,
    String? premiumPlanType,
    String? preferredWritingStyle,
    String? applicationLength,
    bool? includeExperienceInApplication,
    String? experienceSummary,
    String? globalAiHints,
    List<String>? jobKeywords,
    Map<String, dynamic>? additionalCvData,
    int? usedApplications,
    String? cvUrl,
    String? cvDownloadUrl,
    String? cvFilePath,
    String? cvFileName,
    DateTime? cvLastModified,
    bool? cvAnalysisComplete,
    bool? cvAutoFillEnabled,
    DateTime? cvAnalysisTimestamp,
    List<AdditionalDocument>? additionalDocuments,
    String? profileImageUrl,
    String? profileImagePath,
    DateTime? profileImageLastModified,
  }) {
    return UserProfile(
      uid: uid ?? this.uid,
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      skills: skills ?? this.skills,
      jobPreferences: jobPreferences ?? this.jobPreferences,
      jobPreferencesObj: jobPreferencesObj ?? this.jobPreferencesObj,
      interests: interests ?? this.interests,
      workExperience: workExperience ?? this.workExperience,
      education: education ?? this.education,
      isPremium: isPremium ?? this.isPremium,
      testUser: testUser ?? this.testUser,
      premiumExpiry: premiumExpiry ?? this.premiumExpiry,
      premiumExpiryDate: premiumExpiryDate ?? this.premiumExpiryDate,
      premiumPlanType: premiumPlanType ?? this.premiumPlanType,
      preferredWritingStyle:
          preferredWritingStyle ?? this.preferredWritingStyle,
      applicationLength: applicationLength ?? this.applicationLength,
      includeExperienceInApplication:
          includeExperienceInApplication ?? this.includeExperienceInApplication,
      experienceSummary: experienceSummary ?? this.experienceSummary,
      globalAiHints: globalAiHints ?? this.globalAiHints,
      jobKeywords: jobKeywords ?? this.jobKeywords,
      additionalCvData: additionalCvData ?? this.additionalCvData,
      usedApplications: usedApplications ?? this.usedApplications,
      cvUrl: cvUrl ?? this.cvUrl,
      cvDownloadUrl: cvDownloadUrl ?? this.cvDownloadUrl,
      cvFilePath: cvFilePath ?? this.cvFilePath,
      cvFileName: cvFileName ?? this.cvFileName,
      cvLastModified: cvLastModified ?? this.cvLastModified,
      cvAnalysisComplete: cvAnalysisComplete ?? this.cvAnalysisComplete,
      cvAutoFillEnabled: cvAutoFillEnabled ?? this.cvAutoFillEnabled,
      cvAnalysisTimestamp: cvAnalysisTimestamp ?? this.cvAnalysisTimestamp,
      additionalDocuments: additionalDocuments ?? this.additionalDocuments,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      profileImageLastModified:
          profileImageLastModified ?? this.profileImageLastModified,
    );
  }

  /// Konvertiere UserProfile zu Map/JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'id': id ?? uid, // Nutze uid als Fallback
      'name': name,
      'email': email,
      'phone': phone,
      'phoneNumber': phoneNumber ?? phone, // Alias für phone
      'address': address,
      'skills': skills,
      'jobPreferences': jobPreferences,
      'jobPreferencesObj': jobPreferencesObj?.toJson(),
      'interests': interests,
      'workExperience': workExperience?.map((e) => e.toJson()).toList(),
      'education': education?.map((e) => e.toJson()).toList(),
      'isPremium': isPremium,
      'testUser': testUser,
      'premiumExpiry': premiumExpiry,
      'premiumExpiryDate': premiumExpiryDate?.toIso8601String(),
      'premiumPlanType': premiumPlanType,
      'preferredWritingStyle': preferredWritingStyle,
      'applicationLength': applicationLength,
      'includeExperienceInApplication': includeExperienceInApplication,
      'experienceSummary': experienceSummary,
      'globalAiHints': globalAiHints,
      'jobKeywords': jobKeywords,
      'additionalCvData': additionalCvData,
      'usedApplications': usedApplications,
      'cvUrl': cvUrl,
      'cvDownloadUrl': cvDownloadUrl,
      'cvFilePath': cvFilePath,
      'cvFileName': cvFileName,
      'cvLastModified': cvLastModified?.toIso8601String(),
      'cvAnalysisComplete': cvAnalysisComplete,
      'cvAutoFillEnabled': cvAutoFillEnabled,
      'cvAnalysisTimestamp': cvAnalysisTimestamp?.toIso8601String(),
      'additionalDocuments':
          additionalDocuments?.map((e) => e.toJson()).toList(),
      'profileImageUrl': profileImageUrl,
      'profileImagePath': profileImagePath,
      'profileImageLastModified': profileImageLastModified?.toIso8601String(),
    };
  }

  /// Erstelle UserProfile aus Map/JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      uid: json['uid'] as String?,
      id: json['id'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      address: json['address'] as String?,
      skills:
          (json['skills'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
      jobPreferences:
          json['jobPreferences'] is String
              ? json['jobPreferences'] as String?
              : json['jobPreferences']?.toString(),
      jobPreferencesObj:
          json['jobPreferencesObj'] != null
              ? json['jobPreferencesObj'] is Map<String, dynamic>
                  ? JobPreferences.fromJson(
                    json['jobPreferencesObj'] as Map<String, dynamic>,
                  )
                  : json['jobPreferencesObj'] is Map
                  ? JobPreferences.fromJson(
                    Map<String, dynamic>.from(json['jobPreferencesObj'] as Map),
                  )
                  : null
              : null,
      interests: json['interests'] as String?,
      workExperience:
          (json['workExperience'] as List<dynamic>?)
              ?.map((e) {
                if (e is Map<String, dynamic>) {
                  return WorkExperience.fromJson(e);
                } else if (e is Map) {
                  return WorkExperience.fromJson(Map<String, dynamic>.from(e));
                }
                return null;
              })
              .whereType<WorkExperience>()
              .toList(),
      education:
          (json['education'] as List<dynamic>?)
              ?.map((e) {
                if (e is Map<String, dynamic>) {
                  return Education.fromJson(e);
                } else if (e is Map) {
                  return Education.fromJson(Map<String, dynamic>.from(e));
                }
                return null;
              })
              .whereType<Education>()
              .toList(),
      isPremium: json['isPremium'] as bool? ?? false,
      testUser: json['testUser'] as bool?,
      premiumExpiry: json['premiumExpiry'] as String?,
      premiumPlanType: json['premiumPlanType'] as String?,
      premiumExpiryDate:
          json['premiumExpiryDate'] != null
              ? json['premiumExpiryDate'] is String
                  ? DateTime.tryParse(json['premiumExpiryDate'] as String) ??
                      DateTime.now()
                  : json['premiumExpiryDate'] is DateTime
                  ? json['premiumExpiryDate'] as DateTime
                  : DateTime.now()
              : null,
      preferredWritingStyle: json['preferredWritingStyle'] as String?,
      applicationLength: json['applicationLength'] as String?,
      includeExperienceInApplication:
          json['includeExperienceInApplication'] as bool?,
      experienceSummary: json['experienceSummary'] as String?,
      globalAiHints: json['globalAiHints'] as String?,
      jobKeywords:
          (json['jobKeywords'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList(),
      additionalCvData:
          json['additionalCvData'] is Map<String, dynamic>
              ? json['additionalCvData'] as Map<String, dynamic>?
              : json['additionalCvData'] is Map
              ? Map<String, dynamic>.from(json['additionalCvData'] as Map)
              : null,
      cvUrl: json['cvUrl'] as String?,
      cvDownloadUrl: json['cvDownloadUrl'] as String?,
      cvFilePath: json['cvFilePath'] as String?,
      cvFileName: json['cvFileName'] as String?,
      cvLastModified:
          json['cvLastModified'] != null
              ? json['cvLastModified'] is String
                  ? DateTime.tryParse(json['cvLastModified'] as String) ??
                      DateTime.now()
                  : json['cvLastModified'] is DateTime
                  ? json['cvLastModified'] as DateTime
                  : DateTime.now()
              : null,
      cvAnalysisComplete: json['cvAnalysisComplete'] as bool?,
      cvAutoFillEnabled: json['cvAutoFillEnabled'] as bool?,
      usedApplications:
          json['usedApplications'] is int
              ? json['usedApplications'] as int
              : json['usedApplications'] is String
              ? int.tryParse(json['usedApplications'] as String)
              : null,
      cvAnalysisTimestamp:
          json['cvAnalysisTimestamp'] != null
              ? json['cvAnalysisTimestamp'] is String
                  ? DateTime.tryParse(json['cvAnalysisTimestamp'] as String) ??
                      DateTime.now()
                  : json['cvAnalysisTimestamp'] is DateTime
                  ? json['cvAnalysisTimestamp'] as DateTime
                  : DateTime.now()
              : null,
      additionalDocuments:
          (json['additionalDocuments'] as List<dynamic>?)
              ?.map(
                (e) => AdditionalDocument.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      profileImageUrl: json['profileImageUrl'] as String?,
      profileImagePath: json['profileImagePath'] as String?,
      profileImageLastModified:
          json['profileImageLastModified'] != null
              ? json['profileImageLastModified'] is String
                  ? DateTime.tryParse(
                    json['profileImageLastModified'] as String,
                  )
                  : json['profileImageLastModified'] is DateTime
                  ? json['profileImageLastModified'] as DateTime
                  : null
              : null,
    );
  }

  /// Leeres UserProfile erstellen
  factory UserProfile.empty() {
    return UserProfile(
      uid: null,
      id: null,
      name: null,
      email: null,
      phone: null,
      phoneNumber: null,
      address: null,
      skills: [],
      jobPreferences: null,
      jobPreferencesObj: JobPreferences.empty(),
      interests: null,
      workExperience: [],
      education: [],
      isPremium: false,
      testUser: false,
      premiumExpiry: null,
      premiumExpiryDate: null,
      premiumPlanType: null,
      preferredWritingStyle: 'Professionell',
      applicationLength: 'Standard',
      includeExperienceInApplication: true,
      experienceSummary: null,
      globalAiHints: null,
      jobKeywords: [],
      additionalCvData: {},
      usedApplications: 0,
      cvUrl: null,
      cvDownloadUrl: null,
      cvFilePath: null,
      cvFileName: null,
      cvLastModified: null,
      cvAnalysisComplete: false,
      cvAutoFillEnabled: true,
      cvAnalysisTimestamp: null,
      additionalDocuments: [],
    );
  }

  /// Methode um zu prüfen, ob Premium gültig ist
  bool get isValidPremium {
    if (isPremium != true) return false;

    // Prüfe premiumExpiryDate
    if (premiumExpiryDate != null &&
        premiumExpiryDate!.isAfter(DateTime.now())) {
      return true;
    }

    // Prüfe premiumExpiry als Fallback
    if (premiumExpiry != null) {
      final expiryDate = DateTime.tryParse(premiumExpiry!);
      if (expiryDate != null && expiryDate.isAfter(DateTime.now())) {
        return true;
      }
    }

    return false;
  }
}

/// Berufserfahrung
class WorkExperience {
  final String position;
  final String company;
  final DateTime startDate;
  final DateTime? endDate;
  final String description;

  WorkExperience({
    required this.position,
    required this.company,
    required this.startDate,
    this.endDate,
    required this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'position': position,
      'company': company,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'description': description,
    };
  }

  factory WorkExperience.fromJson(Map<String, dynamic> json) {
    DateTime? startDate;
    if (json['startDate'] != null) {
      if (json['startDate'] is String) {
        startDate = DateTime.tryParse(json['startDate'] as String);
      } else if (json['startDate'] is DateTime) {
        startDate = json['startDate'] as DateTime;
      }
    }
    startDate ??= DateTime.now(); // Fallback auf aktuelles Datum

    DateTime? endDate;
    if (json['endDate'] != null) {
      if (json['endDate'] is String) {
        if ((json['endDate'] as String).toLowerCase() == 'present' ||
            (json['endDate'] as String).toLowerCase() == 'heute') {
          endDate = null; // Aktueller Job
        } else {
          endDate = DateTime.tryParse(json['endDate'] as String);
        }
      } else if (json['endDate'] is DateTime) {
        endDate = json['endDate'] as DateTime;
      }
    }

    return WorkExperience(
      position: json['position']?.toString() ?? 'N/A',
      company: json['company']?.toString() ?? 'N/A',
      startDate: startDate,
      endDate: endDate,
      description: json['description']?.toString() ?? '',
    );
  }

  // copyWith Methode
  WorkExperience copyWith({
    String? position,
    String? company,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
  }) {
    return WorkExperience(
      position: position ?? this.position,
      company: company ?? this.company,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
    );
  }
}

/// Ausbildung
class Education {
  final String institution;
  final String degree;
  final String fieldOfStudy;
  final DateTime startDate;
  final DateTime? endDate;

  Education({
    required this.institution,
    required this.degree,
    required this.fieldOfStudy,
    required this.startDate,
    this.endDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'institution': institution,
      'degree': degree,
      'fieldOfStudy': fieldOfStudy,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
    };
  }

  factory Education.fromJson(Map<String, dynamic> json) {
    DateTime? startDate;
    if (json['startDate'] != null) {
      if (json['startDate'] is String) {
        startDate = DateTime.tryParse(json['startDate'] as String);
      } else if (json['startDate'] is DateTime) {
        startDate = json['startDate'] as DateTime;
      }
    }
    startDate ??= DateTime.now(); // Fallback auf aktuelles Datum

    DateTime? endDate;
    if (json['endDate'] != null) {
      if (json['endDate'] is String) {
        if ((json['endDate'] as String).toLowerCase() == 'present' ||
            (json['endDate'] as String).toLowerCase() == 'heute') {
          endDate = null; // Aktuelles Studium
        } else {
          endDate = DateTime.tryParse(json['endDate'] as String);
        }
      } else if (json['endDate'] is DateTime) {
        endDate = json['endDate'] as DateTime;
      }
    }

    return Education(
      institution: json['institution']?.toString() ?? 'N/A',
      degree: json['degree']?.toString() ?? 'N/A',
      fieldOfStudy: json['fieldOfStudy']?.toString() ?? '',
      startDate: startDate,
      endDate: endDate,
    );
  }

  // copyWith Methode
  Education copyWith({
    String? institution,
    String? degree,
    String? fieldOfStudy,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return Education(
      institution: institution ?? this.institution,
      degree: degree ?? this.degree,
      fieldOfStudy: fieldOfStudy ?? this.fieldOfStudy,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

class AdditionalDocument {
  final String title;
  final String downloadUrl;
  final String? localPath; // Temporärer lokaler Pfad für Uploads

  AdditionalDocument({
    required this.title,
    required this.downloadUrl,
    this.localPath,
  });

  AdditionalDocument copyWith({
    String? title,
    String? downloadUrl,
    String? localPath,
  }) {
    return AdditionalDocument(
      title: title ?? this.title,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      localPath: localPath ?? this.localPath,
    );
  }

  Map<String, dynamic> toJson() {
    return {'title': title, 'downloadUrl': downloadUrl};
  }

  factory AdditionalDocument.fromJson(Map<String, dynamic> json) {
    return AdditionalDocument(
      title: json['title'] as String,
      downloadUrl: json['downloadUrl'] as String,
    );
  }
}

import 'package:flutter/material.dart';

/// Enum für verschiedene CV-Template-Typen
enum CvTemplateType {
  classic,
  modern,
  creative,
  minimalist,
  professional,
  elegant,
  tech,
  executive,
  simple,
  luxury,
  corporate,
  artistic,
  startup,
  academic,
  fresh,
  eco,
  traditional,
  timeline, // Timeline-Template - professionell und sauber
  infographic, // Infografik-Template mit Progress-Bars und Charts
  grid, // Grid-Template - strukturiertes Grid-System
  technical, // Technical Specs-Template - für IT-Professionals
  magazine, // Magazine Editorial-Template - editorial-inspiriert
}

/// Enum für Farbschemas
enum CvColorScheme {
  blue,
  green,
  purple,
  orange,
  red,
  gray,
  black,
  navy,
  teal,
  indigo,
  pink,
  amber,
  deepPurple,
  cyan,
  lime,
  brown,
}

/// Datenmodell für CV-Templates
class CvTemplate {
  final String id;
  final String name;
  final String description;
  final CvTemplateType type;
  final CvColorScheme colorScheme;
  final String previewImagePath;
  final bool isPremium;
  final Map<String, dynamic> layoutConfig;
  final bool isCustomizable;
  final CvTemplateCustomization? customization;

  const CvTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.colorScheme,
    required this.previewImagePath,
    this.isPremium = false,
    required this.layoutConfig,
    this.isCustomizable = false,
    this.customization,
  });

  /// Erstellt eine Kopie mit geänderten Werten
  CvTemplate copyWith({
    String? id,
    String? name,
    String? description,
    CvTemplateType? type,
    CvColorScheme? colorScheme,
    String? previewImagePath,
    bool? isPremium,
    Map<String, dynamic>? layoutConfig,
    bool? isCustomizable,
    CvTemplateCustomization? customization,
  }) {
    return CvTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      colorScheme: colorScheme ?? this.colorScheme,
      previewImagePath: previewImagePath ?? this.previewImagePath,
      isPremium: isPremium ?? this.isPremium,
      layoutConfig: layoutConfig ?? this.layoutConfig,
      isCustomizable: isCustomizable ?? this.isCustomizable,
      customization: customization ?? this.customization,
    );
  }

  /// Konvertiert zu JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'colorScheme': colorScheme.name,
      'previewImagePath': previewImagePath,
      'isPremium': isPremium,
      'layoutConfig': layoutConfig,
      'isCustomizable': isCustomizable,
      'customization': customization?.toJson(),
    };
  }

  /// Erstellt aus JSON
  factory CvTemplate.fromJson(Map<String, dynamic> json) {
    return CvTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: CvTemplateType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CvTemplateType.classic,
      ),
      colorScheme: CvColorScheme.values.firstWhere(
        (e) => e.name == json['colorScheme'],
        orElse: () => CvColorScheme.blue,
      ),
      previewImagePath: json['previewImagePath'] as String,
      isPremium: json['isPremium'] as bool? ?? false,
      layoutConfig: json['layoutConfig'] as Map<String, dynamic>? ?? {},
      isCustomizable: json['isCustomizable'] as bool? ?? false,
      customization:
          json['customization'] != null
              ? CvTemplateCustomization.fromJson(json['customization'])
              : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CvTemplate && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CvTemplate(id: $id, name: $name, type: $type, colorScheme: $colorScheme)';
  }
}

/// Extension für CvTemplateType
extension CvTemplateTypeExtension on CvTemplateType {
  String get displayName {
    switch (this) {
      case CvTemplateType.classic:
        return 'Klassisch';
      case CvTemplateType.modern:
        return 'Modern';
      case CvTemplateType.creative:
        return 'Kreativ';
      case CvTemplateType.minimalist:
        return 'Minimalistisch';
      case CvTemplateType.professional:
        return 'Professionell';
      case CvTemplateType.elegant:
        return 'Elegant';
      case CvTemplateType.tech:
        return 'Tech';
      case CvTemplateType.executive:
        return 'Executive';
      case CvTemplateType.simple:
        return 'Einfach';
      case CvTemplateType.luxury:
        return 'Luxus';
      case CvTemplateType.corporate:
        return 'Corporate';
      case CvTemplateType.artistic:
        return 'Künstlerisch';
      case CvTemplateType.startup:
        return 'Startup';
      case CvTemplateType.academic:
        return 'Akademisch';
      case CvTemplateType.fresh:
        return 'Frisch';
      case CvTemplateType.eco:
        return 'Öko';
      case CvTemplateType.traditional:
        return 'Traditionell';
      case CvTemplateType.timeline:
        return 'Timeline';
      case CvTemplateType.infographic:
        return 'Infografik';
      case CvTemplateType.grid:
        return 'Grid';
      case CvTemplateType.technical:
        return 'Technical';
      case CvTemplateType.magazine:
        return 'Magazine';
    }
  }

  String get description {
    switch (this) {
      case CvTemplateType.classic:
        return 'Traditionelles Layout mit klarer Struktur';
      case CvTemplateType.modern:
        return 'Zeitgemäßes Design mit modernen Elementen';
      case CvTemplateType.creative:
        return 'Kreatives Layout für kreative Berufe';
      case CvTemplateType.minimalist:
        return 'Reduziertes Design mit Fokus auf Inhalt';
      case CvTemplateType.professional:
        return 'Seriöses Layout für Geschäftsumgebungen';
      case CvTemplateType.elegant:
        return 'Elegantes Design mit raffinierten Details';
      case CvTemplateType.tech:
        return 'Technisches Layout für IT-Berufe';
      case CvTemplateType.executive:
        return 'Führungskräfte-Layout mit Autorität';
      case CvTemplateType.simple:
        return 'Minimalistisches Design ohne Ablenkungen';
      case CvTemplateType.luxury:
        return 'Luxuriöses Design mit Premium-Elementen';
      case CvTemplateType.corporate:
        return 'Professionelles Corporate Design';
      case CvTemplateType.artistic:
        return 'Künstlerisches Layout für kreative Berufe';
      case CvTemplateType.startup:
        return 'Dynamisches Design für innovative Unternehmen';
      case CvTemplateType.academic:
        return 'Wissenschaftliches Layout für Forschung und Lehre';
      case CvTemplateType.fresh:
        return 'Frisches Design für junge Professionals';
      case CvTemplateType.eco:
        return 'Nachhaltiges Design für Umweltberufe';
      case CvTemplateType.traditional:
        return 'Traditionelles formelles Design';
      case CvTemplateType.timeline:
        return 'Karriereverlauf als elegante Timeline mit professionellen Farben und hohem Kontrast';
      case CvTemplateType.infographic:
        return 'Datenvisualisierung mit Progress-Bars, Charts und professionellen visuellen Elementen';
      case CvTemplateType.grid:
        return 'Strukturiertes Grid-System für Business-Professionals mit klarer Organisation';
      case CvTemplateType.technical:
        return 'Speziell für IT-Professionals mit technischen Elementen und Code-Akzenten';
      case CvTemplateType.magazine:
        return 'Editorial-Design inspiriert von Magazin-Layouts mit Typografie-Fokus';
    }
  }
}

/// Extension für CvColorScheme
extension CvColorSchemeExtension on CvColorScheme {
  String get displayName {
    switch (this) {
      case CvColorScheme.blue:
        return 'Blau';
      case CvColorScheme.green:
        return 'Grün';
      case CvColorScheme.purple:
        return 'Lila';
      case CvColorScheme.orange:
        return 'Orange';
      case CvColorScheme.red:
        return 'Rot';
      case CvColorScheme.gray:
        return 'Grau';
      case CvColorScheme.black:
        return 'Schwarz';
      case CvColorScheme.navy:
        return 'Navy';
      case CvColorScheme.teal:
        return 'Türkis';
      case CvColorScheme.indigo:
        return 'Indigo';
      case CvColorScheme.pink:
        return 'Rosa';
      case CvColorScheme.amber:
        return 'Bernstein';
      case CvColorScheme.deepPurple:
        return 'Tiefviolett';
      case CvColorScheme.cyan:
        return 'Cyan';
      case CvColorScheme.lime:
        return 'Limette';
      case CvColorScheme.brown:
        return 'Braun';
    }
  }

  Color get primaryColor {
    switch (this) {
      case CvColorScheme.blue:
        return const Color(0xFF2196F3);
      case CvColorScheme.green:
        return const Color(0xFF4CAF50);
      case CvColorScheme.purple:
        return const Color(0xFF9C27B0);
      case CvColorScheme.orange:
        return const Color(0xFFFF9800);
      case CvColorScheme.red:
        return const Color(0xFFF44336);
      case CvColorScheme.gray:
        return const Color(0xFF607D8B);
      case CvColorScheme.black:
        return const Color(0xFF212121);
      case CvColorScheme.navy:
        return const Color(0xFF1A237E);
      case CvColorScheme.teal:
        return const Color(0xFF009688);
      case CvColorScheme.indigo:
        return const Color(0xFF3F51B5);
      case CvColorScheme.pink:
        return const Color(0xFFE91E63);
      case CvColorScheme.amber:
        return const Color(0xFFFFC107);
      case CvColorScheme.deepPurple:
        return const Color(0xFF673AB7);
      case CvColorScheme.cyan:
        return const Color(0xFF00BCD4);
      case CvColorScheme.lime:
        return const Color(0xFFCDDC39);
      case CvColorScheme.brown:
        return const Color(0xFF795548);
    }
  }

  Color get secondaryColor {
    switch (this) {
      case CvColorScheme.blue:
        return const Color(0xFFE3F2FD);
      case CvColorScheme.green:
        return const Color(0xFFE8F5E8);
      case CvColorScheme.purple:
        return const Color(0xFFF3E5F5);
      case CvColorScheme.orange:
        return const Color(0xFFFFF3E0);
      case CvColorScheme.red:
        return const Color(0xFFFFEBEE);
      case CvColorScheme.gray:
        return const Color(0xFFECEFF1);
      case CvColorScheme.black:
        return const Color(0xFFF5F5F5);
      case CvColorScheme.navy:
        return const Color(0xFFE8EAF6);
      case CvColorScheme.teal:
        return const Color(0xFFE0F2F1);
      case CvColorScheme.indigo:
        return const Color(0xFFE8EAF6);
      case CvColorScheme.pink:
        return const Color(0xFFFCE4EC);
      case CvColorScheme.amber:
        return const Color(0xFFFFF8E1);
      case CvColorScheme.deepPurple:
        return const Color(0xFFEDE7F6);
      case CvColorScheme.cyan:
        return const Color(0xFFE0F7FA);
      case CvColorScheme.lime:
        return const Color(0xFFF9FBE7);
      case CvColorScheme.brown:
        return const Color(0xFFEFEBE9);
    }
  }

  Color get textColor {
    switch (this) {
      case CvColorScheme.black:
        return Colors.white;
      case CvColorScheme.navy:
        return Colors.white;
      default:
        return const Color(0xFF212121);
    }
  }
}

/// Datenmodell für Template-Anpassungen
class CvTemplateCustomization {
  final CvColorScheme? customColorScheme;
  final Map<String, int>? customFontSizes;
  final double? customSectionSpacing;
  final String? customHeaderStyle;
  final Map<String, bool>? customLayoutOptions;
  final Color? customAccentColor;
  final String? customFontFamily;

  const CvTemplateCustomization({
    this.customColorScheme,
    this.customFontSizes,
    this.customSectionSpacing,
    this.customHeaderStyle,
    this.customLayoutOptions,
    this.customAccentColor,
    this.customFontFamily,
  });

  /// Erstellt eine Kopie mit geänderten Werten
  CvTemplateCustomization copyWith({
    CvColorScheme? customColorScheme,
    Map<String, int>? customFontSizes,
    double? customSectionSpacing,
    String? customHeaderStyle,
    Map<String, bool>? customLayoutOptions,
    Color? customAccentColor,
    String? customFontFamily,
  }) {
    return CvTemplateCustomization(
      customColorScheme: customColorScheme ?? this.customColorScheme,
      customFontSizes: customFontSizes ?? this.customFontSizes,
      customSectionSpacing: customSectionSpacing ?? this.customSectionSpacing,
      customHeaderStyle: customHeaderStyle ?? this.customHeaderStyle,
      customLayoutOptions: customLayoutOptions ?? this.customLayoutOptions,
      customAccentColor: customAccentColor ?? this.customAccentColor,
      customFontFamily: customFontFamily ?? this.customFontFamily,
    );
  }

  /// Konvertiert zu JSON
  Map<String, dynamic> toJson() {
    return {
      'customColorScheme': customColorScheme?.name,
      'customFontSizes': customFontSizes,
      'customSectionSpacing': customSectionSpacing,
      'customHeaderStyle': customHeaderStyle,
      'customLayoutOptions': customLayoutOptions,
      'customAccentColor': customAccentColor?.toARGB32(),
      'customFontFamily': customFontFamily,
    };
  }

  /// Erstellt aus JSON
  factory CvTemplateCustomization.fromJson(Map<String, dynamic> json) {
    return CvTemplateCustomization(
      customColorScheme:
          json['customColorScheme'] != null
              ? CvColorScheme.values.firstWhere(
                (e) => e.name == json['customColorScheme'],
                orElse: () => CvColorScheme.blue,
              )
              : null,
      customFontSizes:
          json['customFontSizes'] != null
              ? Map<String, int>.from(json['customFontSizes'])
              : null,
      customSectionSpacing: json['customSectionSpacing']?.toDouble(),
      customHeaderStyle: json['customHeaderStyle'],
      customLayoutOptions:
          json['customLayoutOptions'] != null
              ? Map<String, bool>.from(json['customLayoutOptions'])
              : null,
      customAccentColor:
          json['customAccentColor'] != null
              ? Color(json['customAccentColor'])
              : null,
      customFontFamily: json['customFontFamily'],
    );
  }
}

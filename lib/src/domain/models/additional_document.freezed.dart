// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'additional_document.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdditionalDocument _$AdditionalDocumentFromJson(Map<String, dynamic> json) {
  return _AdditionalDocument.fromJson(json);
}

/// @nodoc
mixin _$AdditionalDocument {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get fileName => throw _privateConstructorUsedError;
  String get filePath => throw _privateConstructorUsedError;
  int get fileSize => throw _privateConstructorUsedError;
  String get fileType => throw _privateConstructorUsedError;
  DateTime get uploadDate => throw _privateConstructorUsedError;
  bool get isActiveForApplications => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdditionalDocumentCopyWith<AdditionalDocument> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdditionalDocumentCopyWith<$Res> {
  factory $AdditionalDocumentCopyWith(
          AdditionalDocument value, $Res Function(AdditionalDocument) then) =
      _$AdditionalDocumentCopyWithImpl<$Res, AdditionalDocument>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String fileName,
      String filePath,
      int fileSize,
      String fileType,
      DateTime uploadDate,
      bool isActiveForApplications,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$AdditionalDocumentCopyWithImpl<$Res, $Val extends AdditionalDocument>
    implements $AdditionalDocumentCopyWith<$Res> {
  _$AdditionalDocumentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? fileName = null,
    Object? filePath = null,
    Object? fileSize = null,
    Object? fileType = null,
    Object? uploadDate = null,
    Object? isActiveForApplications = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String,
      uploadDate: null == uploadDate
          ? _value.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActiveForApplications: null == isActiveForApplications
          ? _value.isActiveForApplications
          : isActiveForApplications // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdditionalDocumentImplCopyWith<$Res>
    implements $AdditionalDocumentCopyWith<$Res> {
  factory _$$AdditionalDocumentImplCopyWith(_$AdditionalDocumentImpl value,
          $Res Function(_$AdditionalDocumentImpl) then) =
      __$$AdditionalDocumentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String fileName,
      String filePath,
      int fileSize,
      String fileType,
      DateTime uploadDate,
      bool isActiveForApplications,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$AdditionalDocumentImplCopyWithImpl<$Res>
    extends _$AdditionalDocumentCopyWithImpl<$Res, _$AdditionalDocumentImpl>
    implements _$$AdditionalDocumentImplCopyWith<$Res> {
  __$$AdditionalDocumentImplCopyWithImpl(_$AdditionalDocumentImpl _value,
      $Res Function(_$AdditionalDocumentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? fileName = null,
    Object? filePath = null,
    Object? fileSize = null,
    Object? fileType = null,
    Object? uploadDate = null,
    Object? isActiveForApplications = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$AdditionalDocumentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String,
      uploadDate: null == uploadDate
          ? _value.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActiveForApplications: null == isActiveForApplications
          ? _value.isActiveForApplications
          : isActiveForApplications // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdditionalDocumentImpl implements _AdditionalDocument {
  const _$AdditionalDocumentImpl(
      {required this.id,
      required this.userId,
      required this.fileName,
      required this.filePath,
      required this.fileSize,
      required this.fileType,
      required this.uploadDate,
      this.isActiveForApplications = false,
      required this.createdAt,
      required this.updatedAt});

  factory _$AdditionalDocumentImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdditionalDocumentImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String fileName;
  @override
  final String filePath;
  @override
  final int fileSize;
  @override
  final String fileType;
  @override
  final DateTime uploadDate;
  @override
  @JsonKey()
  final bool isActiveForApplications;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'AdditionalDocument(id: $id, userId: $userId, fileName: $fileName, filePath: $filePath, fileSize: $fileSize, fileType: $fileType, uploadDate: $uploadDate, isActiveForApplications: $isActiveForApplications, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdditionalDocumentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.uploadDate, uploadDate) ||
                other.uploadDate == uploadDate) &&
            (identical(other.isActiveForApplications, isActiveForApplications) ||
                other.isActiveForApplications == isActiveForApplications) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, fileName, filePath,
      fileSize, fileType, uploadDate, isActiveForApplications, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdditionalDocumentImplCopyWith<_$AdditionalDocumentImpl> get copyWith =>
      __$$AdditionalDocumentImplCopyWithImpl<_$AdditionalDocumentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdditionalDocumentImplToJson(
      this,
    );
  }
}

abstract class _AdditionalDocument implements AdditionalDocument {
  const factory _AdditionalDocument(
      {required final String id,
      required final String userId,
      required final String fileName,
      required final String filePath,
      required final int fileSize,
      required final String fileType,
      required final DateTime uploadDate,
      final bool isActiveForApplications,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$AdditionalDocumentImpl;

  factory _AdditionalDocument.fromJson(Map<String, dynamic> json) =
      _$AdditionalDocumentImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get fileName;
  @override
  String get filePath;
  @override
  int get fileSize;
  @override
  String get fileType;
  @override
  DateTime get uploadDate;
  @override
  bool get isActiveForApplications;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$AdditionalDocumentImplCopyWith<_$AdditionalDocumentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DocumentUploadResult _$DocumentUploadResultFromJson(Map<String, dynamic> json) {
  return _DocumentUploadResult.fromJson(json);
}

/// @nodoc
mixin _$DocumentUploadResult {
  bool get success => throw _privateConstructorUsedError;
  String? get documentId => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  AdditionalDocument? get document => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DocumentUploadResultCopyWith<DocumentUploadResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentUploadResultCopyWith<$Res> {
  factory $DocumentUploadResultCopyWith(DocumentUploadResult value,
          $Res Function(DocumentUploadResult) then) =
      _$DocumentUploadResultCopyWithImpl<$Res, DocumentUploadResult>;
  @useResult
  $Res call(
      {bool success,
      String? documentId,
      String? errorMessage,
      AdditionalDocument? document});

  $AdditionalDocumentCopyWith<$Res>? get document;
}

/// @nodoc
class _$DocumentUploadResultCopyWithImpl<$Res,
        $Val extends DocumentUploadResult>
    implements $DocumentUploadResultCopyWith<$Res> {
  _$DocumentUploadResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? documentId = freezed,
    Object? errorMessage = freezed,
    Object? document = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      documentId: freezed == documentId
          ? _value.documentId
          : documentId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as AdditionalDocument?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AdditionalDocumentCopyWith<$Res>? get document {
    if (_value.document == null) {
      return null;
    }

    return $AdditionalDocumentCopyWith<$Res>(_value.document!, (value) {
      return _then(_value.copyWith(document: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DocumentUploadResultImplCopyWith<$Res>
    implements $DocumentUploadResultCopyWith<$Res> {
  factory _$$DocumentUploadResultImplCopyWith(_$DocumentUploadResultImpl value,
          $Res Function(_$DocumentUploadResultImpl) then) =
      __$$DocumentUploadResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      String? documentId,
      String? errorMessage,
      AdditionalDocument? document});

  @override
  $AdditionalDocumentCopyWith<$Res>? get document;
}

/// @nodoc
class __$$DocumentUploadResultImplCopyWithImpl<$Res>
    extends _$DocumentUploadResultCopyWithImpl<$Res, _$DocumentUploadResultImpl>
    implements _$$DocumentUploadResultImplCopyWith<$Res> {
  __$$DocumentUploadResultImplCopyWithImpl(_$DocumentUploadResultImpl _value,
      $Res Function(_$DocumentUploadResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? documentId = freezed,
    Object? errorMessage = freezed,
    Object? document = freezed,
  }) {
    return _then(_$DocumentUploadResultImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      documentId: freezed == documentId
          ? _value.documentId
          : documentId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as AdditionalDocument?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DocumentUploadResultImpl implements _DocumentUploadResult {
  const _$DocumentUploadResultImpl(
      {required this.success, this.documentId, this.errorMessage, this.document});

  factory _$DocumentUploadResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$DocumentUploadResultImplFromJson(json);

  @override
  final bool success;
  @override
  final String? documentId;
  @override
  final String? errorMessage;
  @override
  final AdditionalDocument? document;

  @override
  String toString() {
    return 'DocumentUploadResult(success: $success, documentId: $documentId, errorMessage: $errorMessage, document: $document)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadResultImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.document, document) ||
                other.document == document));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, success, documentId, errorMessage, document);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadResultImplCopyWith<_$DocumentUploadResultImpl>
      get copyWith => __$$DocumentUploadResultImplCopyWithImpl<
          _$DocumentUploadResultImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DocumentUploadResultImplToJson(
      this,
    );
  }
}

abstract class _DocumentUploadResult implements DocumentUploadResult {
  const factory _DocumentUploadResult(
      {required final bool success,
      final String? documentId,
      final String? errorMessage,
      final AdditionalDocument? document}) = _$DocumentUploadResultImpl;

  factory _DocumentUploadResult.fromJson(Map<String, dynamic> json) =
      _$DocumentUploadResultImpl.fromJson;

  @override
  bool get success;
  @override
  String? get documentId;
  @override
  String? get errorMessage;
  @override
  AdditionalDocument? get document;
  @override
  @JsonKey(ignore: true)
  _$$DocumentUploadResultImplCopyWith<_$DocumentUploadResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DocumentUploadProgress _$DocumentUploadProgressFromJson(
    Map<String, dynamic> json) {
  return _DocumentUploadProgress.fromJson(json);
}

/// @nodoc
mixin _$DocumentUploadProgress {
  String get fileName => throw _privateConstructorUsedError;
  double get progress => throw _privateConstructorUsedError;
  DocumentUploadStatus get status => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DocumentUploadProgressCopyWith<DocumentUploadProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentUploadProgressCopyWith<$Res> {
  factory $DocumentUploadProgressCopyWith(DocumentUploadProgress value,
          $Res Function(DocumentUploadProgress) then) =
      _$DocumentUploadProgressCopyWithImpl<$Res, DocumentUploadProgress>;
  @useResult
  $Res call(
      {String fileName,
      double progress,
      DocumentUploadStatus status,
      String? errorMessage});
}

/// @nodoc
class _$DocumentUploadProgressCopyWithImpl<$Res,
        $Val extends DocumentUploadProgress>
    implements $DocumentUploadProgressCopyWith<$Res> {
  _$DocumentUploadProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileName = null,
    Object? progress = null,
    Object? status = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DocumentUploadStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentUploadProgressImplCopyWith<$Res>
    implements $DocumentUploadProgressCopyWith<$Res> {
  factory _$$DocumentUploadProgressImplCopyWith(
          _$DocumentUploadProgressImpl value,
          $Res Function(_$DocumentUploadProgressImpl) then) =
      __$$DocumentUploadProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String fileName,
      double progress,
      DocumentUploadStatus status,
      String? errorMessage});
}

/// @nodoc
class __$$DocumentUploadProgressImplCopyWithImpl<$Res>
    extends _$DocumentUploadProgressCopyWithImpl<$Res,
        _$DocumentUploadProgressImpl>
    implements _$$DocumentUploadProgressImplCopyWith<$Res> {
  __$$DocumentUploadProgressImplCopyWithImpl(
      _$DocumentUploadProgressImpl _value,
      $Res Function(_$DocumentUploadProgressImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileName = null,
    Object? progress = null,
    Object? status = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$DocumentUploadProgressImpl(
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DocumentUploadStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DocumentUploadProgressImpl implements _DocumentUploadProgress {
  const _$DocumentUploadProgressImpl(
      {required this.fileName,
      required this.progress,
      required this.status,
      this.errorMessage});

  factory _$DocumentUploadProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$DocumentUploadProgressImplFromJson(json);

  @override
  final String fileName;
  @override
  final double progress;
  @override
  final DocumentUploadStatus status;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'DocumentUploadProgress(fileName: $fileName, progress: $progress, status: $status, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadProgressImpl &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fileName, progress, status, errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadProgressImplCopyWith<_$DocumentUploadProgressImpl>
      get copyWith => __$$DocumentUploadProgressImplCopyWithImpl<
          _$DocumentUploadProgressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DocumentUploadProgressImplToJson(
      this,
    );
  }
}

abstract class _DocumentUploadProgress implements DocumentUploadProgress {
  const factory _DocumentUploadProgress(
      {required final String fileName,
      required final double progress,
      required final DocumentUploadStatus status,
      final String? errorMessage}) = _$DocumentUploadProgressImpl;

  factory _DocumentUploadProgress.fromJson(Map<String, dynamic> json) =
      _$DocumentUploadProgressImpl.fromJson;

  @override
  String get fileName;
  @override
  double get progress;
  @override
  DocumentUploadStatus get status;
  @override
  String? get errorMessage;
  @override
  @JsonKey(ignore: true)
  _$$DocumentUploadProgressImplCopyWith<_$DocumentUploadProgressImpl>
      get copyWith => throw _privateConstructorUsedError;
}

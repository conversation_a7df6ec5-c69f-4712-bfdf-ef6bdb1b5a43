
/// Datenmodell für Abonnementinformationen in der App
/// Wird für die Integration mit dem Supabase Subscription Service verwendet
library;

class Subscription {
  /// Eindeutige ID des Abonnements in der Datenbank
  final String id;
  
  /// ID des Benutzers, dem das Abonnement gehört
  final String userId;
  
  /// Status des Abonnements (active, cancelled, expired)
  final String status;
  
  /// ID des gekauften Produkts
  final String productId;
  
  /// Plattform, auf der das Abonnement erworben wurde (ios, android, web)
  final String platform;
  
  /// Ablaufdatum des Abonnements
  final DateTime? expiresAt;
  
  /// Zeitpunkt, zu dem das Abonnement storniert wurde
  final DateTime? cancelledAt;
  
  /// Zeitpunkt der Erstellung des Abonnements
  final DateTime createdAt;
  
  /// Zeitpunkt der letzten Aktualisierung des Abonnements
  final DateTime updatedAt;

  /// Erstellt ein neues Subscription-Objekt
  Subscription({
    required this.id,
    required this.userId,
    required this.status,
    required this.productId,
    required this.platform,
    this.expiresAt,
    this.cancelledAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Erstellt eine Kopie dieses Subscription-Objekts mit optionalen Änderungen
  Subscription copyWith({
    String? id,
    String? userId,
    String? status,
    String? productId,
    String? platform,
    DateTime? expiresAt,
    DateTime? cancelledAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subscription(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      status: status ?? this.status,
      productId: productId ?? this.productId,
      platform: platform ?? this.platform,
      expiresAt: expiresAt ?? this.expiresAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Konvertiert dieses Objekt in eine Map-Darstellung
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'status': status,
      'product_id': productId,
      'platform': platform,
      'expires_at': expiresAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Erstellt ein Subscription-Objekt aus einer Map-Darstellung
  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      status: json['status'] as String? ?? '',
      productId: json['product_id'] as String? ?? '',
      platform: json['platform'] as String? ?? '',
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at'] as String) 
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : DateTime.now(),
    );
  }

  /// Prüft, ob das Abonnement aktiv ist
  bool get isActive => status == 'active' && 
      (expiresAt == null || DateTime.now().isBefore(expiresAt!));

  /// Prüft, ob das Abonnement abgelaufen ist
  bool get isExpired => status == 'active' && 
      expiresAt != null && expiresAt!.isBefore(DateTime.now());

  /// Gibt eine lesbare String-Darstellung dieses Objekts zurück
  @override
  String toString() {
    return 'Subscription(id: $id, userId: $userId, status: $status, productId: $productId, isActive: $isActive, expiresAt: $expiresAt)';
  }
} 
 
 
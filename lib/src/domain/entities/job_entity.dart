/// Repräsentiert die Kerninformationen eines Jobs.
/// Wird für die Jobliste und potenziell für Details verwendet.
class JobEntity {
  final String id;
  final String? hashId;
  final String title;
  final String companyName;
  final String location;
  final String? descriptionSnippet; // Kurzbeschreibung für die Liste
  final DateTime publishedDate; // Datum der Veröffentlichung
  final String? sourceUrl; // Link zur Originalquelle/Bewerbung
  final String description; // Vollständige Beschreibung
  final Map<String, dynamic>
  metadata; // Metadaten für zusätzliche Eigenschaften

  JobEntity({
    required this.id,
    this.hashId,
    required this.title,
    required this.companyName,
    required this.location,
    this.descriptionSnippet,
    required this.publishedDate,
    this.sourceUrl,
    required this.description,
    Map<String, dynamic>? metadata,
  }) : metadata = metadata ?? {};

  // *** NEU: Factory-Konstruktor für die Erstellung aus JSON ***
  factory JobEntity.fromJson(Map<String, dynamic> json) {
    // Konvertiere zu DateTime
    DateTime pubDate;
    if (json['publishedDate'] is String) {
      // Falls es als ISO-String gespeichert wurde
      pubDate =
          DateTime.tryParse(json['publishedDate'] as String) ?? DateTime.now();
    } else if (json['publishedDate'] is Map) {
      // Falls es als Map mit seconds und nanoseconds gespeichert wurde (ehemaliger Timestamp)
      final timestampMap = json['publishedDate'] as Map;
      if (timestampMap.containsKey('seconds')) {
        final seconds = timestampMap['seconds'] as int;
        final nanoseconds = timestampMap['nanoseconds'] as int? ?? 0;
        pubDate = DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds ~/ 1000000),
        );
      } else {
        pubDate = DateTime.now();
      }
    } else {
      // Fallback, wenn das Format unbekannt ist
      pubDate = DateTime.now();
    }

    return JobEntity(
      id: json['id'] as String? ?? '',
      hashId: json['hashId'] as String?,
      title: json['title'] as String? ?? '',
      companyName: json['companyName'] as String? ?? '',
      location: json['location'] as String? ?? '',
      descriptionSnippet: json['descriptionSnippet'] as String?,
      publishedDate: pubDate,
      sourceUrl: json['sourceUrl'] as String?,
      description: json['description'] as String? ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  // *** NEU: Methode zum Konvertieren in JSON ***
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hashId': hashId,
      'title': title,
      'companyName': companyName,
      'location': location,
      'descriptionSnippet': descriptionSnippet,
      // Speichere DateTime als ISO-String
      'publishedDate': publishedDate.toIso8601String(),
      'sourceUrl': sourceUrl,
      'description': description,
      'metadata': metadata,
    };
  }

  // Optional: Methode zum Kopieren und Ändern von Feldern (nützlich für State Management)
  /// Erstellt eine Kopie dieser Entität mit optional geänderten Feldern
  JobEntity copyWith({
    String? id,
    String? hashId,
    String? title,
    String? companyName,
    String? location,
    String? descriptionSnippet,
    DateTime? publishedDate,
    String? sourceUrl,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return JobEntity(
      id: id ?? this.id,
      hashId: hashId ?? this.hashId,
      title: title ?? this.title,
      companyName: companyName ?? this.companyName,
      location: location ?? this.location,
      descriptionSnippet: descriptionSnippet ?? this.descriptionSnippet,
      publishedDate: publishedDate ?? this.publishedDate,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      description: description ?? this.description,
      metadata: metadata ?? Map<String, dynamic>.from(this.metadata),
    );
  }
}

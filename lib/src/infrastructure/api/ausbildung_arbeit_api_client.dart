import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart';
import 'job_api_client.dart';
import '../../core/utils/logging.dart';
import '../../core/mixins/error_handling_mixin.dart';

/// Implementierung des JobApiClient für die Ausbildungssuche-API der Bundesagentur für Arbeit.
class AusbildungArbeitApiClient
    with ErrorHandlingMixin
    implements JobApiClient {
  // Client ID für die Jobsuche-API (funktioniert für Ausbildungen mit angebotsart=4)
  static const String _ausbildungClientId = 'jobboerse-jobsuche';

  // Logger für diese Klasse
  final _log = getLogger('AusbildungArbeitApiClient');

  @override
  String get componentName => 'AusbildungArbeitApiClient';

  // Endpunkt für die Jobsuche-API (funktioniert für Ausbildungen)
  static const String _ausbildungApiUrl =
      'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobs';

  AusbildungArbeitApiClient();

  @override
  Future<List<JobEntity>> searchJobs({
    required UserProfile userProfile,
    String? keywords,
    int page = 1,
    int limit = 20,
    String? location,
    String? industry,
    String? distance,
    BuildContext? context,
  }) async {
    return await safeApiOperation<List<JobEntity>>(
          () async => _performAusbildungSearch(
            userProfile: userProfile,
            keywords: keywords,
            page: page,
            limit: limit,
            location: location,
            industry: industry,
            distance: distance,
          ),
          context: context,
          errorMessage:
              'Fehler bei der Ausbildungssuche. Bitte versuchen Sie es später erneut.',
          fallbackValue: <JobEntity>[],
        ) ??
        <JobEntity>[];
  }

  /// Interne Methode für die eigentliche Ausbildungssuche mit robustem Error Handling
  Future<List<JobEntity>> _performAusbildungSearch({
    required UserProfile userProfile,
    String? keywords,
    int page = 1,
    int limit = 20,
    String? location,
    String? industry,
    String? distance,
  }) async {
    // Parameter für die funktionierende Jobsuche-API
    // BREAKTHROUGH: Verwende jobboerse-jobsuche API mit angebotsart=4 für Ausbildungen
    // Test Results: ✅ WORKING! Alle Tests erfolgreich mit 10+ Ergebnissen
    final queryParams = <String, String>{
      'page': page.toString(), // Jobsuche-API verwendet 1-basierte Seitenzahlen
      'size': limit.toString(),
      'angebotsart':
          '4', // CRITICAL: 4=AUSBILDUNG/Duales Studium (Schlüssel zur Lösung!)
      // Optional: Keywords für Ausbildungsbezeichnung
      if (keywords != null && keywords.isNotEmpty) 'was': keywords,
      // Optional: Ortsfilter (funktioniert mit Klartext!)
      if (location != null && location.isNotEmpty) 'wo': location,
      // Optional: Umkreis in km
      if (distance != null && distance.isNotEmpty) 'umkreis': distance,
    };

    final uri = Uri.parse(
      _ausbildungApiUrl,
    ).replace(queryParameters: queryParams);
    _log.i('Ausbildung API Anfrage: ${uri.toString()}');
    _log.i('Ausbildung API Parameter: $queryParams');
    _log.i('Ausbildung API Distance: $distance km');

    // Zähler für Wiederholungsversuche
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    while (retryCount <= maxRetries) {
      try {
        // Verwende einen http.Client statt der statischen Methode für mehr Kontrolle
        final client = http.Client();
        try {
          final response = await client
              .get(
                uri,
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json',
                  'X-API-Key':
                      _ausbildungClientId, // Verwende die Ausbildungs-Client-ID
                  'User-Agent': 'EinsteinAI-App/1.0',
                },
              )
              .timeout(const Duration(seconds: 15)); // Timeout nach 15 Sekunden

          if (response.statusCode == 200) {
            final data = json.decode(utf8.decode(response.bodyBytes));

            // Datenextraktion für Ausbildungsangebote (von Jobsuche-API)
            final List<dynamic> ausbildungResults =
                data['stellenangebote'] ?? [];

            _log.i(
              'Ausbildung API: ${ausbildungResults.length} Ergebnisse auf Seite $page erhalten.',
            );

            return ausbildungResults.map((ausbildungData) {
              return JobEntity(
                id:
                    ausbildungData['refnr']?.toString() ??
                    ausbildungData['id']?.toString() ??
                    '',
                hashId:
                    ausbildungData['hashId']?.toString() ??
                    ausbildungData['refnr']?.toString() ??
                    ausbildungData['id']?.toString(),
                title:
                    ausbildungData['titel'] ??
                    'Keine Berufsbezeichnung angegeben',
                companyName:
                    ausbildungData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
                location:
                    _extractLocationFromJobApi(ausbildungData) ?? 'Kein Ort',
                descriptionSnippet: _extractDescriptionSnippetFromJobApi(
                  ausbildungData,
                ),
                publishedDate:
                    _parseDate(ausbildungData['modifikationsTimestamp']) ??
                    DateTime.now(),
                sourceUrl:
                    ausbildungData['externeUrl']?.toString() ??
                    'https://www.arbeitsagentur.de/jobsuche/jobdetail/${ausbildungData['refnr'] ?? ''}',
                description: _extractFullDescriptionFromJobApi(ausbildungData),
                metadata: {
                  'isAusbildung': true, // Markiere als Ausbildungsangebot
                  'angebotsart':
                      ausbildungData['angebotsart']?.toString() ?? '4',
                  'beruf': ausbildungData['beruf']?.toString() ?? '',
                  'arbeitsort': ausbildungData['arbeitsort']?.toString() ?? '',
                  'eintrittsdatum':
                      ausbildungData['eintrittsdatum']?.toString() ?? '',
                },
              );
            }).toList();
          } else if (response.statusCode >= 500 || response.statusCode == 429) {
            // Server-Fehler (5xx) oder Rate-Limiting (429) - wiederholen
            if (retryCount < maxRetries) {
              retryCount++;
              _log.w(
                'API-Fehler ${response.statusCode}, Wiederholungsversuch $retryCount in ${retryDelay.inSeconds} Sekunden...',
              );
              await Future.delayed(retryDelay * retryCount);
              continue; // Nächster Versuch
            } else {
              _log.e(
                'Maximale Anzahl von Wiederholungsversuchen erreicht. Status: ${response.statusCode}',
              );
              throw Exception(
                'Fehler bei der Anfrage an die Ausbildungssuche-API (${response.statusCode})',
              );
            }
          } else {
            // Andere HTTP-Fehler
            _log.e('Ausbildung API Fehler: Status ${response.statusCode}');
            _log.e('Body: ${response.body}');
            throw Exception(
              'Fehler bei der Anfrage an die Ausbildungssuche-API (${response.statusCode})',
            );
          }
        } finally {
          client.close(); // Client immer schließen
        }
      } catch (e, stackTrace) {
        // Prüfen, ob es sich um einen Netzwerkfehler handelt
        final errorString = e.toString().toLowerCase();
        final isNetworkError =
            errorString.contains('socket') ||
            errorString.contains('connection') ||
            errorString.contains('network') ||
            errorString.contains('host lookup') ||
            errorString.contains('handshake');

        if (isNetworkError && retryCount < maxRetries) {
          retryCount++;
          _log.w(
            'Netzwerkfehler: $e - Wiederholungsversuch $retryCount in ${retryDelay.inSeconds} Sekunden...',
          );
          await Future.delayed(retryDelay * retryCount);
          continue; // Nächster Versuch
        } else {
          // Bei anderen Fehlern oder wenn max. Versuche erreicht sind
          _log.e('Fehler bei der API-Kommunikation: $e\n$stackTrace');
          if (isNetworkError) {
            throw Exception(
              'Verbindungsfehler: Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es später erneut. Details: $e',
            );
          } else {
            throw Exception('Fehler bei der API-Verarbeitung: $e');
          }
        }
      }
    }

    // Sollte nie erreicht werden, aber für Compiler notwendig
    throw Exception('Unerwarteter Fehler bei der Ausbildungssuche');
  }

  @override
  Future<JobEntity?> getJobDetails(String refnr, {String? hashId}) async {
    if (refnr.isEmpty) return null;

    // URL für die Detail-Ansicht über die funktionierende Jobsuche-API
    final uri = Uri.parse(
      'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails/$refnr',
    );
    _log.i('Ausbildung API Detail Anfrage: ${uri.toString()}');

    try {
      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-API-Key': _ausbildungClientId, // jobboerse-jobsuche
          'User-Agent': 'EinsteinAI-App/1.0',
        },
      );

      if (response.statusCode == 200) {
        final ausbildungData = json.decode(utf8.decode(response.bodyBytes));

        _log.i(
          "Ausbildungsdetails erfolgreich abgerufen: ${ausbildungData['id'] ?? refnr}",
        );

        // Metadaten hinzufügen für bessere KI-Analyse (Jobsuche-API Format)
        final Map<String, dynamic> metadata = {
          'isAusbildung': true, // Markiere als Ausbildungsangebot
          'angebotsart': ausbildungData['angebotsart']?.toString() ?? '4',
          'beruf': ausbildungData['beruf']?.toString() ?? '',
          'arbeitsort': ausbildungData['arbeitsort']?.toString() ?? '',
          'eintrittsdatum': ausbildungData['eintrittsdatum']?.toString() ?? '',
          'qualifikation': ausbildungData['qualifikation']?.toString() ?? '',
        };

        // Kombinierte Beschreibung (für KI-Analyse)
        final fullDescription = _extractFullDescriptionFromJobApi(
          ausbildungData,
        );

        // Mappe die Detail-Antwort auf JobEntity (Jobsuche-API Format)
        return JobEntity(
          id:
              ausbildungData['refnr']?.toString() ??
              ausbildungData['id']?.toString() ??
              refnr,
          hashId:
              ausbildungData['hashId']?.toString() ??
              ausbildungData['refnr']?.toString() ??
              ausbildungData['id']?.toString() ??
              hashId,
          title: ausbildungData['titel'] ?? 'Keine Berufsbezeichnung',
          companyName:
              ausbildungData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
          location: _extractLocationFromJobApi(ausbildungData) ?? 'Kein Ort',
          descriptionSnippet: _parseHtmlSnippet(fullDescription),
          description:
              _parseHtmlDescription(fullDescription) ??
              "Keine detaillierte Beschreibung verfügbar",
          publishedDate:
              _parseDate(ausbildungData['modifikationsTimestamp']) ??
              DateTime.now(),
          sourceUrl:
              ausbildungData['externeUrl']?.toString() ??
              'https://www.arbeitsagentur.de/jobsuche/jobdetail/${ausbildungData['refnr'] ?? refnr}',
          metadata: metadata,
        );
      } else {
        _log.e('Ausbildung API Detail Fehler: Status ${response.statusCode}');
        _log.e('Body: ${response.body}');
        return null;
      }
    } catch (e, stackTrace) {
      _log.e('Fehler bei der Detail-API-Kommunikation: $e\n$stackTrace');
      return null;
    }
  }

  // Hilfsmethode zum Extrahieren des Arbeitsortes aus verschiedenen möglichen Feldern
  String? _extractLocation(Map<String, dynamic> ausbildungData) {
    if (ausbildungData['ort'] != null) {
      return ausbildungData['ort'];
    } else if (ausbildungData['arbeitgeberOrt'] != null) {
      return ausbildungData['arbeitgeberOrt'];
    } else if (ausbildungData['arbeitsort'] != null) {
      return ausbildungData['arbeitsort'];
    }
    return null;
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.tryParse(dateString) ??
          DateFormat('yyyy-MM-dd').tryParse(dateString) ??
          DateFormat('dd.MM.yyyy').tryParse(dateString);
    } catch (_) {
      _log.w("Konnte Datum nicht parsen: $dateString");
      return null;
    }
  }

  String? _parseHtmlSnippet(String? htmlDescription) {
    if (htmlDescription == null) return null;
    // Einfache Methode: Ersten Teil nehmen, HTML-Tags grob entfernen
    String plainText =
        htmlDescription
            .replaceAll(RegExp(r'<[^>]*>'), ' ')
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();
    return plainText.length > 200
        ? '${plainText.substring(0, 200)}...'
        : plainText;
  }

  String? _parseHtmlDescription(String? htmlDescription) {
    if (htmlDescription == null) return null;
    return htmlDescription
        .replaceAll(RegExp(r'<[^>]*>'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  // Hilfsfunktion zum Extrahieren einer kurzen Beschreibung
  String? _extractDescriptionSnippet(Map<String, dynamic> ausbildungData) {
    List<String> snippetSources = [
      ausbildungData['berufsbezeichnung']?.toString() ?? '',
      ausbildungData['kurzbezeichnung']?.toString() ?? '',
      ausbildungData['stellenbeschreibung']?.toString() ?? '',
      ausbildungData['ausbildungsArt']?.toString() ?? '',
      ausbildungData['ausbildungsBeginn']?.toString() ?? '',
      ausbildungData['schulabschluss']?.toString() ?? '',
    ];

    String combinedText = snippetSources
        .where((text) => text.isNotEmpty)
        .join(' - ');
    return combinedText.isNotEmpty
        ? _parseHtmlSnippet(combinedText)
        : 'Keine Beschreibung verfügbar';
  }

  // Hilfsfunktion zum Extrahieren der vollständigen Beschreibung
  String _extractFullDescription(Map<String, dynamic> ausbildungData) {
    final Map<String, String> keywordFields = {
      'Berufsbezeichnung':
          ausbildungData['berufsbezeichnung']?.toString() ?? '',
      'Kurzbezeichnung': ausbildungData['kurzbezeichnung']?.toString() ?? '',
      'Ausbildungsart': ausbildungData['ausbildungsArt']?.toString() ?? '',
      'Ausbildungsbeginn':
          ausbildungData['ausbildungsBeginn']?.toString() ?? '',
      'Schulabschluss': ausbildungData['schulabschluss']?.toString() ?? '',
      'Stellenbeschreibung':
          ausbildungData['stellenbeschreibung']?.toString() ?? '',
      'Tätigkeit': ausbildungData['taetigkeit']?.toString() ?? '',
      'Anforderungen': ausbildungData['anforderungen']?.toString() ?? '',
      'Ausbildungsinhalte':
          ausbildungData['ausbildungsInhalte']?.toString() ?? '',
    };

    // Zusammenführen der Texte und Felder formatieren
    List<String> formattedSections = [];
    keywordFields.forEach((key, value) {
      if (value.isNotEmpty) {
        formattedSections.add("$key: $value");
      }
    });

    String combinedText = formattedSections.join('\n\n');
    return combinedText.isNotEmpty
        ? combinedText
        : 'Keine detaillierte Beschreibung verfügbar';
  }

  // === NEUE HILFSMETHODEN FÜR JOBSUCHE-API ===

  // Hilfsmethode zum Extrahieren des Arbeitsortes aus der Jobsuche-API
  String? _extractLocationFromJobApi(Map<String, dynamic> jobData) {
    if (jobData['arbeitsort'] != null) {
      final arbeitsort = jobData['arbeitsort'];
      if (arbeitsort is Map && arbeitsort['ort'] != null) {
        return arbeitsort['ort'];
      } else if (arbeitsort is String) {
        return arbeitsort;
      }
    }

    // Fallback zu anderen Ortsfeldern
    if (jobData['einrichtungsname'] != null && jobData['ort'] != null) {
      return jobData['ort'];
    }

    return null;
  }

  // Hilfsfunktion zum Extrahieren einer kurzen Beschreibung aus der Jobsuche-API
  String? _extractDescriptionSnippetFromJobApi(Map<String, dynamic> jobData) {
    List<String> snippetSources = [
      jobData['titel']?.toString() ?? '',
      jobData['arbeitgeber']?.toString() ?? '',
      jobData['beruf']?.toString() ?? '',
      jobData['eintrittsdatum']?.toString() ?? '',
    ];

    String combinedText = snippetSources
        .where((text) => text.isNotEmpty)
        .join(' - ');
    return combinedText.isNotEmpty
        ? _parseHtmlSnippet(combinedText)
        : 'Keine Beschreibung verfügbar';
  }

  // Hilfsfunktion zum Extrahieren der vollständigen Beschreibung aus der Jobsuche-API
  String _extractFullDescriptionFromJobApi(Map<String, dynamic> jobData) {
    final Map<String, String> keywordFields = {
      'Titel': jobData['titel']?.toString() ?? '',
      'Arbeitgeber': jobData['arbeitgeber']?.toString() ?? '',
      'Beruf': jobData['beruf']?.toString() ?? '',
      'Arbeitsort': _extractLocationFromJobApi(jobData) ?? '',
      'Eintrittsdatum': jobData['eintrittsdatum']?.toString() ?? '',
      'Angebotsart': jobData['angebotsart']?.toString() ?? '',
      'Qualifikation': jobData['qualifikation']?.toString() ?? '',
      'Arbeitszeitmodelle': jobData['arbeitszeitmodelle']?.toString() ?? '',
    };

    // Zusammenführen der Texte und Felder formatieren
    List<String> formattedSections = [];
    keywordFields.forEach((key, value) {
      if (value.isNotEmpty) {
        formattedSections.add("$key: $value");
      }
    });

    String combinedText = formattedSections.join('\n\n');
    return combinedText.isNotEmpty
        ? combinedText
        : 'Keine detaillierte Beschreibung verfügbar';
  }
}

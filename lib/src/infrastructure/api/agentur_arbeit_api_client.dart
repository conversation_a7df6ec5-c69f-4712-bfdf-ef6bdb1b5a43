import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart'; // Kann entfernt werden, wenn nicht benötigt
import 'job_api_client.dart'; // Korrekter Import für die abstrakte Klasse
import '../../core/utils/logging.dart'; // Import für Logger
import '../../core/mixins/error_handling_mixin.dart';

/// Implementierung des JobApiClient für die Jobsuche-API der Bundesagentur für Arbeit.
class AgenturArbeitApiClient with ErrorHandlingMixin implements JobApiClient {
  // Feste Client ID für die API
  static const String _jobClientId = 'jobboerse-jobsuche';

  // Logger für diese Klasse
  final _log = getLogger('AgenturArbeitApiClient');

  @override
  String get componentName => 'AgenturArbeitApiClient';

  // ----- Korrekter Endpunkt aus Doku -----
  static const String _jobApiUrl =
      'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobs';
  // ---------------------------------------

  // Kein API-Key mehr im Konstruktor nötig
  AgenturArbeitApiClient();

  @override
  Future<List<JobEntity>> searchJobs({
    required UserProfile userProfile,
    String? keywords,
    int page = 1,
    int limit = 20,
    String? location,
    String? industry,
    String? distance,
    BuildContext? context,
  }) async {
    return await safeApiOperation<List<JobEntity>>(
          () async => _performJobSearch(
            userProfile: userProfile,
            keywords: keywords,
            page: page,
            limit: limit,
            location: location,
            industry: industry,
            distance: distance,
          ),
          context: context,
          errorMessage:
              'Fehler bei der Jobsuche. Bitte versuchen Sie es später erneut.',
          fallbackValue: <JobEntity>[],
        ) ??
        <JobEntity>[];
  }

  /// Interne Methode für die eigentliche Jobsuche mit robustem Error Handling
  Future<List<JobEntity>> _performJobSearch({
    required UserProfile userProfile,
    String? keywords,
    int page = 1,
    int limit = 20,
    String? location,
    String? industry,
    String? distance,
  }) async {
    // Input Validation
    if (page < 1) {
      throw ArgumentError('Page muss größer als 0 sein');
    }
    if (limit < 1 || limit > 100) {
      throw ArgumentError('Limit muss zwischen 1 und 100 liegen');
    }

    // Parameter für die API zusammenstellen
    final queryParams = <String, String>{
      'page': page.toString(),
      'size': limit.toString(),
      if (keywords != null && keywords.trim().isNotEmpty)
        'was': keywords.trim(),
      if (location != null && location.trim().isNotEmpty) 'wo': location.trim(),
      if (distance != null && distance.trim().isNotEmpty)
        'umkreis': distance.trim(),
    };

    final uri = Uri.parse(_jobApiUrl).replace(queryParameters: queryParams);
    _log.i('Agentur API Anfrage: ${uri.toString()}');
    _log.d('Agentur API Parameter: $queryParams');

    return await executeWithRetry<List<JobEntity>>(
          () async => _makeApiRequest(uri, page),
          maxRetries: 3,
          errorMessage: 'Jobsuche fehlgeschlagen nach mehreren Versuchen',
        ) ??
        <JobEntity>[];
  }

  /// Führt die eigentliche API-Anfrage durch
  Future<List<JobEntity>> _makeApiRequest(Uri uri, int page) async {
    http.Client? client;
    try {
      client = http.Client();

      final response = await client
          .get(
            uri,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'X-API-Key': _jobClientId,
              'User-Agent': 'EinsteinAI-App/1.0',
            },
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout:
                () =>
                    throw TimeoutException(
                      'API-Anfrage dauerte zu lange (>30s)',
                      const Duration(seconds: 30),
                    ),
          );

      return _handleApiResponse(response, page);
    } on SocketException catch (e) {
      _log.e('Netzwerkfehler bei API-Anfrage', error: e);
      throw Exception(
        'Keine Internetverbindung verfügbar. Bitte überprüfen Sie Ihre Netzwerkeinstellungen.',
      );
    } on TimeoutException catch (e) {
      _log.e('Timeout bei API-Anfrage', error: e);
      throw Exception(
        'Die Anfrage dauerte zu lange. Bitte versuchen Sie es erneut.',
      );
    } on FormatException catch (e) {
      _log.e('JSON-Parsing-Fehler', error: e);
      throw Exception('Ungültige Antwort vom Server erhalten.');
    } finally {
      client?.close();
    }
  }

  /// Behandelt die API-Antwort und konvertiert sie zu JobEntity-Liste
  List<JobEntity> _handleApiResponse(http.Response response, int page) {
    if (response.statusCode == 200) {
      try {
        final data = json.decode(utf8.decode(response.bodyBytes));

        if (data is! Map<String, dynamic>) {
          throw FormatException(
            'Unerwartetes Antwortformat: ${data.runtimeType}',
          );
        }

        final List<dynamic> jobResults = data['stellenangebote'] ?? [];

        _log.i(
          'Agentur API: ${jobResults.length} Ergebnisse auf Seite $page erhalten.',
        );

        return jobResults.map((jobData) => _mapJobData(jobData)).toList();
      } catch (e) {
        _log.e('Fehler beim Parsen der API-Antwort', error: e);
        throw FormatException('Fehler beim Verarbeiten der Jobdaten: $e');
      }
    } else if (response.statusCode == 429) {
      throw Exception(
        'Zu viele Anfragen. Bitte warten Sie einen Moment und versuchen Sie es erneut.',
      );
    } else if (response.statusCode >= 500) {
      throw Exception(
        'Server-Fehler (${response.statusCode}). Bitte versuchen Sie es später erneut.',
      );
    } else if (response.statusCode == 404) {
      _log.w('Keine Jobs gefunden für die angegebenen Suchkriterien');
      return <JobEntity>[];
    } else {
      _log.e(
        'API-Fehler: Status ${response.statusCode}, Body: ${response.body}',
      );
      throw Exception(
        'API-Fehler (${response.statusCode}): ${_getReadableErrorMessage(response)}',
      );
    }
  }

  /// Konvertiert API-Jobdaten zu JobEntity
  JobEntity _mapJobData(dynamic jobData) {
    try {
      if (jobData is! Map<String, dynamic>) {
        throw FormatException('Ungültiges Jobdaten-Format');
      }

      return JobEntity(
        id: jobData['refnr']?.toString() ?? '',
        hashId: jobData['hashId']?.toString(),
        title: jobData['beruf']?.toString() ?? 'Kein Beruf angegeben',
        companyName:
            jobData['arbeitgeber']?.toString() ?? 'Unbekannter Arbeitgeber',
        location: _extractLocationSafely(jobData),
        descriptionSnippet: _extractDescriptionSnippet(jobData),
        publishedDate:
            _parseDate(jobData['modifikationsTimestamp']) ?? DateTime.now(),
        sourceUrl: _buildSourceUrl(jobData),
        description: _extractFullDescription(jobData),
        metadata: _buildMetadata(jobData),
      );
    } catch (e) {
      _log.w('Fehler beim Mappen von Jobdaten: $e', error: e);
      // Fallback JobEntity mit minimalen Daten
      return JobEntity(
        id: jobData['refnr']?.toString() ?? 'unknown',
        title: 'Fehler beim Laden der Jobdaten',
        companyName: 'Unbekannt',
        location: 'Unbekannt',
        descriptionSnippet:
            'Jobdaten konnten nicht vollständig geladen werden.',
        publishedDate: DateTime.now(),
        sourceUrl: '',
        description: 'Fehler beim Laden der Jobdetails.',
        metadata: {},
      );
    }
  }

  /// Extrahiert den Arbeitsort sicher
  String _extractLocationSafely(Map<String, dynamic> jobData) {
    try {
      return jobData['arbeitsort']?['ort']?.toString() ?? 'Kein Ort';
    } catch (e) {
      _log.w('Fehler beim Extrahieren des Arbeitsortes: $e');
      return 'Kein Ort';
    }
  }

  /// Erstellt die Source-URL sicher
  String _buildSourceUrl(Map<String, dynamic> jobData) {
    try {
      final externeUrl = jobData['externeUrl']?.toString();
      if (externeUrl != null && externeUrl.isNotEmpty) {
        return externeUrl;
      }
      final refnr = jobData['refnr']?.toString() ?? '';
      return 'https://www.arbeitsagentur.de/jobsuche/jobdetail/$refnr';
    } catch (e) {
      _log.w('Fehler beim Erstellen der Source-URL: $e');
      return '';
    }
  }

  /// Erstellt Metadata sicher
  Map<String, dynamic> _buildMetadata(Map<String, dynamic> jobData) {
    try {
      return {
        'erfordertErfahrung': jobData['_erfordertErfahrung'] ?? false,
        'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
        'ausbildung': jobData['ausbildung']?.toString() ?? '',
        'qualifikation': jobData['qualifikation']?.toString() ?? '',
        'arbeitszeit': jobData['arbeitszeitmodell']?.toString() ?? '',
        'befristung': jobData['befristung']?.toString() ?? '',
      };
    } catch (e) {
      _log.w('Fehler beim Erstellen der Metadata: $e');
      return <String, dynamic>{};
    }
  }

  /// Erstellt eine lesbare Fehlermeldung aus der HTTP-Response
  String _getReadableErrorMessage(http.Response response) {
    try {
      final body = response.body;
      if (body.isNotEmpty) {
        final data = json.decode(body);
        if (data is Map && data.containsKey('message')) {
          return data['message'].toString();
        }
      }
    } catch (e) {
      // Ignore JSON parsing errors
    }
    return 'Unbekannter Server-Fehler';
  }

  // --- Methode zum Abrufen der Job-Details (WIRD NICHT MEHR VERWENDET) ---
  /*
  Future<JobEntity?> getJobDetails(String refnr) async {
    if (refnr.isEmpty) return null;

    // Baue die URL für den Detail-Endpunkt zusammen - VERSUCH MIT refnr!
    final uri = Uri.parse('https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v1/jobdetails/$refnr');
    print('Agentur API Detail Anfrage (Versuch mit refnr): ${uri.toString()}');

    try {
      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'X-API-Key': _clientId,
        },
      );

      if (response.statusCode == 200) {
        final jobData = json.decode(utf8.decode(response.bodyBytes));

        // Mappe die Detail-Antwort auf JobEntity
        return JobEntity(
          id: jobData['refnr']?.toString() ?? refnr,
          hashId: jobData['hashId']?.toString(), // hashId aus Details (falls vorhanden)
          title: jobData['titel'] ?? 'Kein Titel',
          companyName: jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
          location: jobData['arbeitgeberAdresse']?['ort'] ?? 'Kein Ort',
          descriptionSnippet: _parseHtmlSnippet(jobData['stellenbeschreibung']),
          fullDescription: _parseHtmlDescription(jobData['stellenbeschreibung'] ?? 'Keine Beschreibung vorhanden.'),
          postedDate: _parseDate(jobData['aktuelleVeroeffentlichungsdatum'] ?? jobData['modifikationsTimestamp']),
          sourceUrl: jobData['allianzpartnerUrl'] ?? jobData['arbeitgeberdarstellungUrl'],
        );
      } else if (response.statusCode == 404) {
         print('Agentur API Detail Fehler: Job mit ID $refnr nicht gefunden (404).');
         return null;
      } else {
        print('Agentur API Detail Fehler: Status ${response.statusCode}');
        print('Body: ${response.body}');
        // Wir werfen hier KEINE Exception, damit die App nicht abstürzt, geben aber null zurück
        // throw Exception('Fehler bei der Detail-Anfrage an die Agentur für Arbeit API (${response.statusCode})');
        return null;
      }
    } catch (e, stackTrace) {
      print('Fehler bei der Detail-API-Kommunikation: $e\n$stackTrace');
      return null;
    }
  }
  */
  // --------------------------------------------------------------------

  @override
  Future<JobEntity?> getJobDetails(String refnr, {String? hashId}) async {
    if (refnr.trim().isEmpty) {
      _log.w('Leere refnr für getJobDetails übergeben');
      return null;
    }

    return await safeApiOperation<JobEntity?>(
      () async => _fetchJobDetails(refnr.trim(), hashId?.trim()),
      errorMessage: 'Fehler beim Laden der Jobdetails',
      fallbackValue: null,
    );
  }

  /// Interne Methode zum Abrufen der Job-Details mit robustem Error Handling
  Future<JobEntity?> _fetchJobDetails(String refnr, String? hashId) async {
    http.Client? client;

    try {
      client = http.Client();

      // Versuch 1: Mit refnr (Referenznummer)
      final uriWithRefnr = Uri.parse(
        'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails/$refnr',
      );
      _log.i(
        'Agentur API Detail Anfrage (mit refnr): ${uriWithRefnr.toString()}',
      );

      var response = await _makeDetailRequest(client, uriWithRefnr);

      // Versuch 2: Mit hashId falls refnr fehlschlägt
      if (response.statusCode != 200 && hashId != null && hashId.isNotEmpty) {
        final uriWithHashId = Uri.parse(
          'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails?hashId=$hashId',
        );
        _log.i(
          'Agentur API Detail Anfrage (mit hashId): ${uriWithHashId.toString()}',
        );
        response = await _makeDetailRequest(client, uriWithHashId);
      }

      return _handleDetailResponse(response, refnr, hashId);
    } on SocketException catch (e) {
      _log.e('Netzwerkfehler bei Job-Detail-Anfrage', error: e);
      throw Exception('Keine Internetverbindung für Jobdetails verfügbar.');
    } on TimeoutException catch (e) {
      _log.e('Timeout bei Job-Detail-Anfrage', error: e);
      throw Exception('Laden der Jobdetails dauerte zu lange.');
    } on FormatException catch (e) {
      _log.e('JSON-Parsing-Fehler bei Jobdetails', error: e);
      throw Exception('Ungültige Jobdetail-Daten erhalten.');
    } finally {
      client?.close();
    }
  }

  /// Führt eine Detail-API-Anfrage durch
  Future<http.Response> _makeDetailRequest(http.Client client, Uri uri) async {
    return await client
        .get(
          uri,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-API-Key': _jobClientId,
            'User-Agent': 'EinsteinAI-App/1.0',
          },
        )
        .timeout(
          const Duration(seconds: 30),
          onTimeout:
              () =>
                  throw TimeoutException(
                    'Detail-API-Anfrage dauerte zu lange (>30s)',
                    const Duration(seconds: 30),
                  ),
        );
  }

  /// Behandelt die Detail-API-Antwort
  JobEntity? _handleDetailResponse(
    http.Response response,
    String refnr,
    String? hashId,
  ) {
    if (response.statusCode == 200) {
      try {
        final jobData = json.decode(utf8.decode(response.bodyBytes));

        if (jobData is! Map<String, dynamic>) {
          throw FormatException(
            'Unerwartetes Jobdetail-Format: ${jobData.runtimeType}',
          );
        }

        _log.i(
          "Job-Details erfolgreich abgerufen: ${jobData['refnr'] ?? refnr}",
        );
        return _buildDetailJobEntity(jobData, refnr, hashId);
      } catch (e) {
        _log.e('Fehler beim Parsen der Jobdetail-Antwort', error: e);
        throw FormatException('Fehler beim Verarbeiten der Jobdetails: $e');
      }
    } else if (response.statusCode == 404) {
      _log.w('Job mit ID $refnr nicht gefunden (404)');
      return null;
    } else if (response.statusCode >= 500) {
      throw Exception(
        'Server-Fehler beim Laden der Jobdetails (${response.statusCode})',
      );
    } else {
      _log.e(
        'Detail-API-Fehler: Status ${response.statusCode}, Body: ${response.body}',
      );
      throw Exception(
        'Fehler beim Laden der Jobdetails (${response.statusCode})',
      );
    }
  }

  /// Erstellt JobEntity aus Jobdetail-Daten
  JobEntity _buildDetailJobEntity(
    Map<String, dynamic> jobData,
    String refnr,
    String? hashId,
  ) {
    try {
      // Vollständige Beschreibung sammeln
      final descriptionParts = <String>[];

      _addDescriptionPart(descriptionParts, jobData['stellenbeschreibung']);
      _addDescriptionPart(
        descriptionParts,
        jobData['taetigkeitsbeschreibung'],
        'Tätigkeitsbeschreibung',
      );
      _addDescriptionPart(
        descriptionParts,
        jobData['anforderungen'],
        'Anforderungen',
      );
      _addDescriptionPart(
        descriptionParts,
        jobData['arbeitgeberdarstellungbeschreibung'],
        'Über uns',
      );

      final fullDescription = descriptionParts.join("\n\n");

      return JobEntity(
        id: jobData['refnr']?.toString() ?? refnr,
        hashId: jobData['hashId']?.toString() ?? hashId,
        title:
            jobData['beruf']?.toString() ??
            jobData['titel']?.toString() ??
            'Kein Titel',
        companyName:
            jobData['arbeitgeber']?.toString() ?? 'Unbekannter Arbeitgeber',
        location: _extractLocation(jobData) ?? 'Kein Ort',
        descriptionSnippet: _parseHtmlSnippet(fullDescription),
        description:
            _parseHtmlDescription(fullDescription) ??
            "Keine detaillierte Beschreibung verfügbar",
        publishedDate:
            _parseDate(
              jobData['aktuelleVeroeffentlichungsdatum'] ??
                  jobData['modifikationsTimestamp'],
            ) ??
            DateTime.now(),
        sourceUrl: _buildDetailSourceUrl(jobData, refnr),
        metadata: _buildDetailMetadata(jobData),
      );
    } catch (e) {
      _log.w('Fehler beim Erstellen der JobEntity aus Details: $e');
      // Fallback mit minimalen Daten
      return JobEntity(
        id: refnr,
        title: 'Fehler beim Laden der Jobdetails',
        companyName: 'Unbekannt',
        location: 'Unbekannt',
        descriptionSnippet:
            'Jobdetails konnten nicht vollständig geladen werden.',
        publishedDate: DateTime.now(),
        sourceUrl: 'https://www.arbeitsagentur.de/jobsuche/jobdetail/$refnr',
        description: 'Fehler beim Laden der vollständigen Jobdetails.',
        metadata: {},
      );
    }
  }

  /// Fügt einen Beschreibungsteil hinzu, falls vorhanden
  void _addDescriptionPart(
    List<String> parts,
    dynamic content, [
    String? prefix,
  ]) {
    if (content != null && content.toString().trim().isNotEmpty) {
      final text = content.toString().trim();
      parts.add(prefix != null ? '$prefix: $text' : text);
    }
  }

  /// Erstellt Source-URL für Details
  String _buildDetailSourceUrl(Map<String, dynamic> jobData, String refnr) {
    try {
      final externeUrl = jobData['externeUrl']?.toString();
      if (externeUrl != null && externeUrl.trim().isNotEmpty) {
        return externeUrl.trim();
      }
      return 'https://www.arbeitsagentur.de/jobsuche/jobdetail/$refnr';
    } catch (e) {
      _log.w('Fehler beim Erstellen der Detail-Source-URL: $e');
      return 'https://www.arbeitsagentur.de/jobsuche/jobdetail/$refnr';
    }
  }

  /// Erstellt Metadata für Details
  Map<String, dynamic> _buildDetailMetadata(Map<String, dynamic> jobData) {
    try {
      return {
        'ausbildung': jobData['ausbildung']?.toString() ?? '',
        'befristung': jobData['befristung']?.toString() ?? '',
        'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
        'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? '',
        'branche': jobData['branche']?.toString() ?? '',
        'qualifikation': jobData['qualifikation']?.toString() ?? '',
      };
    } catch (e) {
      _log.w('Fehler beim Erstellen der Detail-Metadata: $e');
      return <String, dynamic>{};
    }
  }

  // Hilfsmethode zum Extrahieren des Arbeitsortes aus verschiedenen möglichen Feldern
  String? _extractLocation(Map<String, dynamic> jobData) {
    // Versuche verschiedene mögliche Feldnamen für den Ort
    if (jobData['arbeitsort']?['ort'] != null) {
      return jobData['arbeitsort']['ort'];
    } else if (jobData['arbeitgeberAdresse']?['ort'] != null) {
      return jobData['arbeitgeberAdresse']['ort'];
    } else if (jobData['arbeitsortAngabe'] != null) {
      return jobData['arbeitsortAngabe'];
    }
    return null;
  }

  // --- Hilfsfunktionen (Beispiele, müssen angepasst werden) ---

  DateTime? _parseDate(String? dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.tryParse(dateString) ??
          DateFormat('yyyy-MM-dd').tryParse(dateString) ??
          DateFormat('dd.MM.yyyy').tryParse(dateString);
    } catch (_) {
      _log.w("Konnte Datum nicht parsen: $dateString");
      return null;
    }
  }

  String? _parseHtmlSnippet(String? htmlDescription) {
    if (htmlDescription == null) return null;
    // Einfache Methode: Ersten Teil nehmen, HTML-Tags grob entfernen
    // Für eine bessere Darstellung sollte ein HTML-Parser verwendet werden!
    String plainText =
        htmlDescription
            .replaceAll(RegExp(r'<[^>]*>'), ' ')
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();
    return plainText.length > 200
        ? '${plainText.substring(0, 200)}...'
        : plainText;
  }

  String? _parseHtmlDescription(String? htmlDescription) {
    if (htmlDescription == null) return null;
    // TODO: Hier idealerweise HTML parsen und formatieren oder
    // ein Widget verwenden, das HTML anzeigen kann (z.B. flutter_html)
    // Fürs Erste geben wir den rohen HTML-Text zurück (oder bereinigt)
    return htmlDescription
        .replaceAll(RegExp(r'<[^>]*>'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  // Verbesserte Hilfsfunktionen
  String? _extractDescriptionSnippet(Map<String, dynamic> jobData) {
    List<String> snippetSources = [
      jobData['kurzbezeichnung']?.toString() ?? '',
      jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      jobData['arbeitsortzusatz']?.toString() ?? '',
      jobData['befristung']?.toString() ?? '',
      jobData['branche']?.toString() ?? '',
      jobData['ausbildung']?.toString() ?? '',
      jobData['berufserfahrung']?.toString() ?? '',
      jobData['qualifikation']?.toString() ?? '',
    ];

    String combinedText = snippetSources
        .where((text) => text.isNotEmpty)
        .join(' - ');
    return combinedText.isNotEmpty
        ? _parseHtmlSnippet(combinedText)
        : 'Keine Beschreibung verfügbar';
  }

  String _extractFullDescription(Map<String, dynamic> jobData) {
    // Systematisch nach allen relevanten Feldern suchen
    final Map<String, String> keywordFields = {
      'beruf': jobData['beruf']?.toString() ?? '',
      'kurzbezeichnung': jobData['kurzbezeichnung']?.toString() ?? '',
      'taetigkeitsbeschreibung':
          jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      'arbeitgeberdarstellungbeschreibung':
          jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      'stellenbeschreibung': jobData['stellenbeschreibung']?.toString() ?? '',
      'branche': jobData['branche']?.toString() ?? '',
      'ausbildung': jobData['ausbildung']?.toString() ?? '',
      'qualifikation': jobData['qualifikation']?.toString() ?? '',
      'befristung': jobData['befristung']?.toString() ?? '',
      'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
      'fuehrungsverantwortung':
          jobData['fuehrungsverantwortung']?.toString() ?? '',
      'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? '',
    };

    // Spezieller Check für berufserfahrung - dieses Feld direkt auswerten
    String berufserfahrung = keywordFields['berufserfahrung'] ?? '';
    bool erfordertErfahrung =
        berufserfahrung.toLowerCase().contains('notwendig') ||
        berufserfahrung.toLowerCase().contains('erforderlich') ||
        berufserfahrung.toLowerCase().contains('vorausgesetzt');

    // Zusätzliches Feld im metadata speichern
    jobData['_erfordertErfahrung'] = erfordertErfahrung;

    // Zusammenführen der Texte und Felder formatieren
    List<String> formattedSections = [];
    keywordFields.forEach((key, value) {
      if (value.isNotEmpty) {
        formattedSections.add("$key: $value");
      }
    });

    String combinedText = formattedSections.join('\n\n');
    return combinedText.isNotEmpty
        ? combinedText
        : 'Keine detaillierte Beschreibung verfügbar';
  }
}

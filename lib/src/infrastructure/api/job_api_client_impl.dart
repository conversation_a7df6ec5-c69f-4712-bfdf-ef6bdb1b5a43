import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart';
import 'job_api_client.dart';

// Konkrete Implementierung des JobApiClient für die App
class JobApiClientImpl implements JobApiClient {
  // Feste Client ID für die API
  static const String _clientId = 'jobboerse-jobsuche';
  // Korrekter Endpunkt aus Doku
  static const String _apiUrl = 'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobs';

  @override
  Future<List<JobEntity>> searchJobs({
    required UserProfile userProfile, // Wird aktuell nicht verwendet, könnte aber für Profil-Matching nützlich sein
    String? keywords,
    int page = 1, // Wird direkt verwendet (1-basiert laut Be<PERSON>piel)
    int limit = 20,
    String? location,
    String? industry, // API hat evtl. spezifische Branchencodes
    String? distance,
  }) async {
    // Parameter für die API zusammenstellen
    final queryParams = <String, String>{
      'page': page.toString(), // Direkt verwenden
      'size': limit.toString(),
      // Nur hinzufügen, wenn nicht null oder leer
      if (keywords != null && keywords.isNotEmpty) 'was': keywords,
      if (location != null && location.isNotEmpty) 'wo': location,
      if (distance != null && distance.isNotEmpty) 'umkreis': distance, // Entfernungsparameter hinzufügen
      // if (industry != null && industry.isNotEmpty) 'branche': industry, // Falls relevant
    };

    final uri = Uri.parse(_apiUrl).replace(queryParameters: queryParams);
    print('Agentur API Anfrage: ${uri.toString()}');

    try {
      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'X-API-Key': _clientId,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(utf8.decode(response.bodyBytes));

        // Datenextraktion
        final List<dynamic> jobResults = data['stellenangebote'] ?? [];

        print('Agentur API: ${jobResults.length} Ergebnisse auf Seite $page erhalten.');

        return jobResults.map((jobData) {
          final rawHashId = jobData['hashId']?.toString();
          print("DEBUG: Job refnr=${jobData['refnr']}, rawHashId=$rawHashId");

          return JobEntity(
            id: jobData['refnr']?.toString() ?? '',
            hashId: rawHashId,
            title: jobData['beruf'] ?? 'Kein Beruf angegeben',
            companyName: jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
            location: jobData['arbeitsort']?['ort'] ?? 'Kein Ort',
            descriptionSnippet: _extractDescriptionSnippet(jobData),
            publishedDate: _parseDate(jobData['modifikationsTimestamp']) ?? DateTime.now(),
            sourceUrl: null,
            description: _extractFullDescription(jobData),
            metadata: {
              'erfordertErfahrung': jobData['_erfordertErfahrung'] ?? false,
              'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
              'ausbildung': jobData['ausbildung']?.toString() ?? '',
              'qualifikation': jobData['qualifikation']?.toString() ?? '',
              'arbeitszeit': jobData['arbeitszeitmodell']?.toString() ?? '',
              'befristung': jobData['befristung']?.toString() ?? '',
            },
          );
        }).toList();

      } else {
        print('Agentur API Fehler: Status ${response.statusCode}');
        print('Body: ${response.body}');
        throw Exception('Fehler bei der Anfrage an die Agentur für Arbeit API (${response.statusCode})');
      }
    } catch (e, stackTrace) {
      print('Fehler bei der API-Kommunikation: $e\n$stackTrace');
      throw Exception('Netzwerkfehler oder Fehler bei der API-Verarbeitung: $e');
    }
  }

  @override
  Future<JobEntity?> getJobDetails(String refnr, {String? hashId}) async {
    if (refnr.isEmpty) return null;

    // Versuch 1: Mit refnr (Referenznummer)
    final uriWithRefnr = Uri.parse('https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails/$refnr');
    print('Agentur API Detail Anfrage (mit refnr): ${uriWithRefnr.toString()}');

    try {
      // Erste Anfrage mit refnr
      var response = await http.get(
        uriWithRefnr,
        headers: {
          'Accept': 'application/json',
          'X-API-Key': _clientId,
        },
      );

      // Wenn refnr nicht funktioniert und hashId vorhanden ist, versuche es mit hashId
      if (response.statusCode != 200 && hashId != null && hashId.isNotEmpty) {
        final uriWithHashId = Uri.parse('https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails?hashId=$hashId');
        print('Agentur API Detail Anfrage (mit hashId): ${uriWithHashId.toString()}');

        response = await http.get(
          uriWithHashId,
          headers: {
            'Accept': 'application/json',
            'X-API-Key': _clientId,
          },
        );
      }

      if (response.statusCode == 200) {
        final jobData = json.decode(utf8.decode(response.bodyBytes));

        print("Job-Details erfolgreich abgerufen: ${jobData['refnr'] ?? refnr}");

        // Vollständige Beschreibung sammeln
        final List<String> descriptionParts = [];

        // Alle relevanten Beschreibungsfelder auslesen
        if (jobData['stellenbeschreibung'] != null) {
          descriptionParts.add(jobData['stellenbeschreibung']);
        }

        if (jobData['taetigkeitsbeschreibung'] != null) {
          descriptionParts.add("Tätigkeitsbeschreibung: ${jobData['taetigkeitsbeschreibung']}");
        }

        if (jobData['anforderungen'] != null) {
          descriptionParts.add("Anforderungen: ${jobData['anforderungen']}");
        }

        if (jobData['arbeitgeberdarstellungbeschreibung'] != null) {
          descriptionParts.add("Über uns: ${jobData['arbeitgeberdarstellungbeschreibung']}");
        }

        // Metadaten hinzufügen für bessere KI-Analyse
        final Map<String, dynamic> metadata = {
          'ausbildung': jobData['ausbildung']?.toString() ?? '',
          'befristung': jobData['befristung']?.toString() ?? '',
          'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
          'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? '',
          'branche': jobData['branche']?.toString() ?? '',
          'qualifikation': jobData['qualifikation']?.toString() ?? '',
        };

        // Kombinierte Beschreibung (für KI-Analyse)
        final fullDescription = descriptionParts.join("\n\n");

        // Mappe die Detail-Antwort auf JobEntity
        return JobEntity(
          id: jobData['refnr']?.toString() ?? refnr,
          hashId: jobData['hashId']?.toString() ?? hashId,
          title: jobData['beruf'] ?? jobData['titel'] ?? 'Kein Titel',
          companyName: jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
          location: _extractLocation(jobData) ?? 'Kein Ort',
          descriptionSnippet: _parseHtmlSnippet(fullDescription),
          description: _parseHtmlDescription(fullDescription) ?? "Keine detaillierte Beschreibung verfügbar",
          publishedDate: _parseDate(jobData['aktuelleVeroeffentlichungsdatum'] ?? jobData['modifikationsTimestamp']) ?? DateTime.now(),
          sourceUrl: jobData['allianzpartnerUrl'] ?? jobData['arbeitgeberdarstellungUrl'],
          metadata: metadata,
        );
      } else {
        print('Agentur API Detail Fehler: Status ${response.statusCode}');
        print('Body: ${response.body}');
        return null;
      }
    } catch (e, stackTrace) {
      print('Fehler bei der Detail-API-Kommunikation: $e\n$stackTrace');
      return null;
    }
  }

  // Hilfsmethode zum Extrahieren des Arbeitsortes aus verschiedenen möglichen Feldern
  String? _extractLocation(Map<String, dynamic> jobData) {
    // Versuche verschiedene mögliche Feldnamen für den Ort
    if (jobData['arbeitsort']?['ort'] != null) {
      return jobData['arbeitsort']?['ort'];
    } else if (jobData['arbeitgeberAdresse']?['ort'] != null) {
      return jobData['arbeitgeberAdresse']?['ort'];
    } else if (jobData['arbeitsortAngabe'] != null) {
      return jobData['arbeitsortAngabe'];
    }
    return null;
  }

  // Hilfsfunktionen
  DateTime? _parseDate(String? dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.tryParse(dateString) ?? DateFormat('yyyy-MM-dd').tryParse(dateString) ?? DateFormat('dd.MM.yyyy').tryParse(dateString);
    } catch (_) {
      print("Konnte Datum nicht parsen: $dateString");
      return null;
    }
  }

  String? _parseHtmlSnippet(String? htmlDescription) {
     if (htmlDescription == null) return null;
     // Einfache Methode: Ersten Teil nehmen, HTML-Tags grob entfernen
     // Für eine bessere Darstellung sollte ein HTML-Parser verwendet werden!
     String plainText = htmlDescription.replaceAll(RegExp(r'<[^>]*>'), ' ').replaceAll(RegExp(r'\s+'), ' ').trim();
     return plainText.length > 200 ? '${plainText.substring(0, 200)}...' : plainText;
  }

  String? _parseHtmlDescription(String? htmlDescription) {
     if (htmlDescription == null) return null;
     // TODO: Hier idealerweise HTML parsen und formatieren oder
     // ein Widget verwenden, das HTML anzeigen kann (z.B. flutter_html)
     // Fürs Erste geben wir den rohen HTML-Text zurück (oder bereinigt)
     return htmlDescription.replaceAll(RegExp(r'<[^>]*>'), ' ').replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  // Verbesserte Hilfsfunktionen
  String? _extractDescriptionSnippet(Map<String, dynamic> jobData) {
    List<String> snippetSources = [
      jobData['kurzbezeichnung']?.toString() ?? '',
      jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      jobData['arbeitsortzusatz']?.toString() ?? '',
      jobData['befristung']?.toString() ?? '',
      jobData['branche']?.toString() ?? '',
      jobData['ausbildung']?.toString() ?? '',
      jobData['berufserfahrung']?.toString() ?? '',
      jobData['qualifikation']?.toString() ?? '',
    ];

    String combinedText = snippetSources.where((text) => text.isNotEmpty).join(' - ');
    return combinedText.isNotEmpty ? _parseHtmlSnippet(combinedText) : 'Keine Beschreibung verfügbar';
  }

  String _extractFullDescription(Map<String, dynamic> jobData) {
    // Systematisch nach allen relevanten Feldern suchen
    final Map<String, String> keywordFields = {
      'beruf': jobData['beruf']?.toString() ?? '',
      'kurzbezeichnung': jobData['kurzbezeichnung']?.toString() ?? '',
      'taetigkeitsbeschreibung': jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      'arbeitgeberdarstellungbeschreibung': jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      'stellenbeschreibung': jobData['stellenbeschreibung']?.toString() ?? '',
      'branche': jobData['branche']?.toString() ?? '',
      'ausbildung': jobData['ausbildung']?.toString() ?? '',
      'qualifikation': jobData['qualifikation']?.toString() ?? '',
      'befristung': jobData['befristung']?.toString() ?? '',
      'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
      'fuehrungsverantwortung': jobData['fuehrungsverantwortung']?.toString() ?? '',
      'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? ''
    };

    // Spezieller Check für berufserfahrung - dieses Feld direkt auswerten
    String berufserfahrung = keywordFields['berufserfahrung'] ?? '';
    bool erfordertErfahrung =
        berufserfahrung.toLowerCase().contains('notwendig') ||
        berufserfahrung.toLowerCase().contains('erforderlich') ||
        berufserfahrung.toLowerCase().contains('vorausgesetzt');

    // Zusätzliches Feld im metadata speichern
    jobData['_erfordertErfahrung'] = erfordertErfahrung;

    // Zusammenführen der Texte und Felder formatieren
    List<String> formattedSections = [];
    keywordFields.forEach((key, value) {
      if (value.isNotEmpty) {
        formattedSections.add("$key: $value");
      }
    });

    String combinedText = formattedSections.join('\n\n');
    return combinedText.isNotEmpty ? combinedText : 'Keine detaillierte Beschreibung verfügbar';
  }
} 
import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

/// Service für Netzwerk-Status-Monitoring
/// Überwacht die Netzwerk-Konnektivität und benachrichtigt über Änderungen
class NetworkStatusService {
  static final NetworkStatusService _instance =
      NetworkStatusService._internal();
  final Logger _log = Logger();
  final Connectivity _connectivity = Connectivity();

  factory NetworkStatusService() => _instance;
  NetworkStatusService._internal();

  // Stream Controller für Netzwerk-Status-Updates
  final StreamController<NetworkStatus> _statusController =
      StreamController<NetworkStatus>.broadcast();

  // Aktueller Netzwerk-Status
  NetworkStatus _currentStatus = NetworkStatus.unknown;

  // Subscription für Connectivity-Changes
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Timer für periodische Connectivity-Checks
  Timer? _connectivityCheckTimer;

  // Letzte erfolgreiche Netzwerk-Überprüfung
  DateTime? _lastSuccessfulCheck;

  // Anzahl aufeinanderfolgender Fehler
  int _consecutiveFailures = 0;

  /// Stream für Netzwerk-Status-Updates
  Stream<NetworkStatus> get statusStream => _statusController.stream;

  /// Aktueller Netzwerk-Status
  NetworkStatus get currentStatus => _currentStatus;

  /// Ist das Netzwerk verfügbar?
  bool get isConnected => _currentStatus == NetworkStatus.connected;

  /// Ist das Netzwerk nicht verfügbar?
  bool get isDisconnected => _currentStatus == NetworkStatus.disconnected;

  /// Initialisiert den NetworkStatusService
  Future<void> initialize() async {
    try {
      _log.i('🌐 NetworkStatusService wird initialisiert...');

      // Prüfe initialen Netzwerk-Status
      await _checkInitialConnectivity();

      // Starte Connectivity-Monitoring
      _startConnectivityMonitoring();

      // Starte periodische Checks
      _startPeriodicChecks();

      _log.i('✅ NetworkStatusService erfolgreich initialisiert');
    } catch (e) {
      _log.e('❌ Fehler bei NetworkStatusService-Initialisierung: $e');
    }
  }

  /// Prüft den initialen Netzwerk-Status
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final status = _mapConnectivityToStatus(connectivityResults);
      await _updateNetworkStatus(status);
    } catch (e) {
      _log.e('❌ Fehler beim Prüfen der initialen Konnektivität: $e');
      await _updateNetworkStatus(NetworkStatus.unknown);
    }
  }

  /// Startet das Connectivity-Monitoring
  void _startConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        final status = _mapConnectivityToStatus(results);
        await _updateNetworkStatus(status);
      },
      onError: (error) {
        _log.e('❌ Fehler im Connectivity-Stream: $error');
        _updateNetworkStatus(NetworkStatus.unknown);
      },
    );
  }

  /// Startet periodische Connectivity-Checks
  void _startPeriodicChecks() {
    _connectivityCheckTimer = Timer.periodic(const Duration(seconds: 30), (
      timer,
    ) async {
      await _performPeriodicCheck();
    });
  }

  /// Führt einen periodischen Connectivity-Check durch
  Future<void> _performPeriodicCheck() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final status = _mapConnectivityToStatus(connectivityResults);

      // Zusätzliche Validierung bei "connected" Status
      if (status == NetworkStatus.connected) {
        final isReallyConnected = await _validateInternetConnection();
        if (!isReallyConnected) {
          await _updateNetworkStatus(NetworkStatus.disconnected);
          return;
        }
      }

      await _updateNetworkStatus(status);
    } catch (e) {
      _log.w('⚠️ Fehler beim periodischen Connectivity-Check: $e');
      _consecutiveFailures++;

      if (_consecutiveFailures >= 3) {
        await _updateNetworkStatus(NetworkStatus.unknown);
      }
    }
  }

  /// Validiert die tatsächliche Internet-Verbindung
  Future<bool> _validateInternetConnection() async {
    try {
      // Einfacher HTTP-Request zu einem zuverlässigen Server
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 5);

      final request = await client.getUrl(Uri.parse('https://www.google.com'));
      final response = await request.close();

      client.close();

      final isConnected = response.statusCode == 200;
      if (isConnected) {
        _lastSuccessfulCheck = DateTime.now();
        _consecutiveFailures = 0;
      }

      return isConnected;
    } catch (e) {
      _log.d('🔍 Internet-Validierung fehlgeschlagen: $e');
      return false;
    }
  }

  /// Mappt ConnectivityResult zu NetworkStatus
  NetworkStatus _mapConnectivityToStatus(List<ConnectivityResult> results) {
    if (results.isEmpty) {
      return NetworkStatus.disconnected;
    }

    // Prüfe ob eine der Verbindungen aktiv ist
    for (final result in results) {
      switch (result) {
        case ConnectivityResult.wifi:
        case ConnectivityResult.mobile:
        case ConnectivityResult.ethernet:
          return NetworkStatus.connected;
        case ConnectivityResult.none:
          continue;
        default:
          continue;
      }
    }

    return NetworkStatus.disconnected;
  }

  /// Aktualisiert den Netzwerk-Status
  Future<void> _updateNetworkStatus(NetworkStatus newStatus) async {
    if (_currentStatus != newStatus) {
      final oldStatus = _currentStatus;
      _currentStatus = newStatus;

      _log.i(
        '🔄 Netzwerk-Status geändert: ${oldStatus.name} → ${newStatus.name}',
      );

      // Benachrichtige Listener
      if (!_statusController.isClosed) {
        _statusController.add(newStatus);
      }

      // Spezielle Aktionen bei Status-Änderungen
      await _handleStatusChange(oldStatus, newStatus);
    }
  }

  /// Behandelt Status-Änderungen
  Future<void> _handleStatusChange(
    NetworkStatus oldStatus,
    NetworkStatus newStatus,
  ) async {
    switch (newStatus) {
      case NetworkStatus.connected:
        _log.i('✅ Netzwerk-Verbindung wiederhergestellt');
        // Hier könnten automatische Sync-Operationen gestartet werden
        break;
      case NetworkStatus.disconnected:
        _log.w('⚠️ Netzwerk-Verbindung verloren');
        // Hier könnten Offline-Modi aktiviert werden
        break;
      case NetworkStatus.unknown:
        _log.w('❓ Netzwerk-Status unbekannt');
        break;
    }
  }

  /// Führt einen manuellen Connectivity-Check durch
  Future<NetworkStatus> checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final status = _mapConnectivityToStatus(connectivityResults);

      // Bei "connected" Status zusätzliche Validierung
      if (status == NetworkStatus.connected) {
        final isReallyConnected = await _validateInternetConnection();
        final finalStatus =
            isReallyConnected
                ? NetworkStatus.connected
                : NetworkStatus.disconnected;
        await _updateNetworkStatus(finalStatus);
        return finalStatus;
      }

      await _updateNetworkStatus(status);
      return status;
    } catch (e) {
      _log.e('❌ Fehler beim manuellen Connectivity-Check: $e');
      await _updateNetworkStatus(NetworkStatus.unknown);
      return NetworkStatus.unknown;
    }
  }

  /// Wartet auf Netzwerk-Verbindung
  Future<void> waitForConnection({Duration? timeout}) async {
    if (isConnected) return;

    final completer = Completer<void>();
    late StreamSubscription<NetworkStatus> subscription;

    subscription = statusStream.listen((status) {
      if (status == NetworkStatus.connected) {
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.complete();
        }
      }
    });

    if (timeout != null) {
      Timer(timeout, () {
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.completeError(
            TimeoutException(
              'Timeout beim Warten auf Netzwerk-Verbindung',
              timeout,
            ),
          );
        }
      });
    }

    return completer.future;
  }

  /// Gibt Netzwerk-Statistiken zurück
  Map<String, dynamic> getNetworkStats() {
    return {
      'current_status': _currentStatus.name,
      'is_connected': isConnected,
      'last_successful_check': _lastSuccessfulCheck?.toIso8601String(),
      'consecutive_failures': _consecutiveFailures,
      'monitoring_active':
          _connectivitySubscription != null &&
          !_connectivitySubscription!.isPaused,
    };
  }

  /// Beendet den NetworkStatusService
  Future<void> dispose() async {
    _log.i('🧹 NetworkStatusService wird beendet...');

    await _connectivitySubscription?.cancel();
    _connectivityCheckTimer?.cancel();

    if (!_statusController.isClosed) {
      await _statusController.close();
    }

    _log.i('✅ NetworkStatusService erfolgreich beendet');
  }
}

/// Netzwerk-Status-Enum
enum NetworkStatus { connected, disconnected, unknown }

/// Extension für NetworkStatus
extension NetworkStatusExtension on NetworkStatus {
  String get displayName {
    switch (this) {
      case NetworkStatus.connected:
        return 'Verbunden';
      case NetworkStatus.disconnected:
        return 'Getrennt';
      case NetworkStatus.unknown:
        return 'Unbekannt';
    }
  }

  bool get isOnline => this == NetworkStatus.connected;
  bool get isOffline => this == NetworkStatus.disconnected;
}

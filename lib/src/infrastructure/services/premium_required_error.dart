import 'package:ki_test/src/infrastructure/services/supabase_models.dart';

/// Fehlerklasse für den Fall, dass ein Premium-Feature verwendet werden soll,
/// aber der Benutzer keinen Premium-Zugriff hat.
class PremiumRequiredError extends FunctionError {
  /// Gibt an, ob der Benutzer Premium-Status hat
  final bool isPremium;
  
  /// Gibt an, welches Feature versucht wurde zu nutzen
  final String feature;
  
  /// Erstellt einen neuen PremiumRequiredError
  PremiumRequiredError({
    required String message,
    this.isPremium = false,
    this.feature = '',
  }) : super(message);
  
  /// Erstellt einen PremiumRequiredError aus einer Fehlerantwort
  factory PremiumRequiredError.fromResponse(Map<String, dynamic> response, {String defaultFeature = ''}) {
    final message = response['error'] as String? ?? 'Premium-Zugriff erforderlich';
    final isPremium = response['isPremium'] as bool? ?? false;
    final feature = defaultFeature;
    
    return PremiumRequiredError(
      message: message,
      isPremium: isPremium,
      feature: feature,
    );
  }
  
  @override
  String toString() {
    return 'PremiumRequiredError: $message (isPremium: $isPremium, feature: $feature)';
  }
}

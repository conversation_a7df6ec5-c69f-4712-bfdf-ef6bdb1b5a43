import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:convert';
import 'dart:math';
import 'supabase_models.dart';
import 'premium_required_error.dart';
import 'package:ki_test/src/domain/models/extracted_cv_data.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:riverpod/riverpod.dart';
import 'package:ki_test/src/core/mixins/error_handling_mixin.dart';

class SupabaseService with ErrorHandlingMixin {
  final SupabaseClient _client;
  final _log = getLogger('SupabaseService');

  @override
  String get componentName => 'SupabaseService';

  SupabaseService(this._client);

  /// Ruft die Supabase Edge Function 'process-cv-text' auf,
  /// um den extrahierten Text aus einem Lebenslauf zu analysieren.
  ///
  /// [cvText]: Der aus der PDF extrahierte Text.
  /// Gibt bei Erfolg ein Map mit den strukturierten Daten zurück.
  /// Wirft eine Exception bei Fehlern.
  Future<Map<String, dynamic>> analyzeCvText(String cvText) async {
    return await safeApiOperation<Map<String, dynamic>>(
          () async => _performCvTextAnalysis(cvText),
          errorMessage: 'Fehler bei der CV-Text-Analyse',
          fallbackValue: <String, dynamic>{},
        ) ??
        <String, dynamic>{};
  }

  /// Interne Methode für die CV-Text-Analyse mit robustem Error Handling
  Future<Map<String, dynamic>> _performCvTextAnalysis(String cvText) async {
    // Input Validation
    if (cvText.trim().isEmpty) {
      throw ArgumentError('CV-Text darf nicht leer sein');
    }

    _log.i('Rufe Supabase function "process-cv-text" auf...');

    try {
      final response = await _client.functions.invoke(
        'process-cv-text',
        body: {'cvText': cvText.trim()},
      );

      return _handleFunctionResponse(response, 'process-cv-text');
    } on FunctionException catch (e) {
      _log.e('FunctionException bei process-cv-text', error: e);
      throw _createFunctionError(e, 'CV-Text-Analyse');
    } catch (e, stackTrace) {
      _log.e(
        'Unerwarteter Fehler bei process-cv-text',
        error: e,
        stackTrace: stackTrace,
      );
      throw Exception('Unbekannter Fehler bei der Lebenslauf-Analyse: $e');
    }
  }

  /// Behandelt Supabase Function Responses einheitlich
  Map<String, dynamic> _handleFunctionResponse(
    FunctionResponse response,
    String functionName,
  ) {
    if (response.status == 200) {
      if (response.data is Map<String, dynamic>) {
        _log.d('Supabase function "$functionName" erfolgreich ausgeführt');
        return response.data as Map<String, dynamic>;
      } else {
        _log.w(
          'Unerwartetes Datenformat von "$functionName": ${response.data?.runtimeType}',
        );
        throw FormatException(
          'Unerwartetes Datenformat von der $functionName empfangen.',
        );
      }
    } else {
      _log.e('Function "$functionName" Fehler: Status ${response.status}');
      throw Exception(
        'Function-Fehler ($functionName): Status ${response.status}',
      );
    }
  }

  /// Erstellt einheitliche Function-Fehler
  Exception _createFunctionError(FunctionException e, String operation) {
    final errorMessage =
        e.details is Map
            ? (e.details as Map)['error']?.toString()
            : e.details?.toString();

    return Exception(
      'Fehler bei der $operation: ${errorMessage ?? 'Unbekannter Funktionsfehler (Status: ${e.status})'}',
    );
  }

  /// Füge die processCvText-Methode aus der anderen Implementation hinzu
  Future<ExtractedCvData?> processCvText(String cvText) async {
    _log.i('Rufe Supabase Textverarbeitungs-Function "process-cv-text" auf...');
    if (cvText.isEmpty) {
      _log.w('Leerer CV-Text an processCvText übergeben.');
      return null;
    }

    try {
      final response = await _client.functions.invoke(
        'process-cv-text',
        body: {'cvText': cvText},
      );

      _log.i(
        'Supabase Function "process-cv-text" Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        try {
          final data = response.data as Map<String, dynamic>;
          _log.d(
            'Empfangene Rohdaten von process-cv-text: ${jsonEncode(data)}',
          );
          final extractedData = ExtractedCvData.fromJson(data);
          _log.i('CV-Text erfolgreich verarbeitet und Daten extrahiert.');
          return extractedData;
        } catch (e, stack) {
          _log.e(
            'Fehler beim Parsen der JSON-Antwort von process-cv-text',
            error: e,
            stackTrace: stack,
          );
          _log.e('Rohdaten: ${response.data}');
          return null;
        }
      } else {
        _log.e(
          'Fehler bei der Ausführung der Function "process-cv-text". Status: ${response.status}, Data: ${response.data}',
        );
        return null;
      }
    } catch (e, stackTrace) {
      _log.e(
        'Exception beim Aufruf der Function "process-cv-text"',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// NEU: Führt OCR für eine PDF-Datei mittels einer Supabase Edge Function durch.
  ///
  /// [storagePath]: Der Pfad zur PDF-Datei im Supabase Storage.
  /// Gibt den extrahierten Text zurück oder wirft eine FunctionError bei Fehlern.
  Future<String?> performOcrOnPdf(String storagePath) async {
    final functionName = 'perform-ocr-on-pdf'; // Name der Edge Function
    _log.i(
      'Rufe Supabase OCR Function "$functionName" für Storage-Pfad: $storagePath auf',
    );

    // --- ECHTE IMPLEMENTIERUNG ---
    try {
      // Stelle sicher, dass der Pfad nicht leer ist
      if (storagePath.isEmpty) {
        _log.e('Leerer Storage-Pfad für OCR erhalten.');
        throw FunctionError(
          'Interner Fehler: Kein Dateipfad für OCR angegeben.',
        );
      }

      // Rufe die Edge Function auf
      final response = await _client.functions.invoke(
        functionName,
        body: {'storagePath': storagePath}, // Sende den Storage-Pfad
      );

      _log.i(
        'Supabase OCR Function "$functionName" Antwort Status: ${response.status}',
      );

      // Fehlerbehandlung basierend auf Statuscode
      if (response.status != 200) {
        // Versuche, eine spezifische Fehlermeldung aus der Antwort zu extrahieren
        String errorMessage = 'Unbekannter OCR-Fehler';
        if (response.data is Map<String, dynamic> &&
            response.data['error'] != null) {
          errorMessage = response.data['error'] as String;
        } else if (response.data is String &&
            (response.data as String).isNotEmpty) {
          errorMessage =
              response.data as String; // Manchmal ist der Fehler nur ein String
        }
        _log.e(
          'Fehler von Supabase OCR Function "$functionName": Status ${response.status}, Body: ${response.data}',
        );
        throw FunctionError(
          'Fehler bei der OCR-Verarbeitung (${response.status}): $errorMessage',
        );
      }

      // Erfolgsfall: Extrahiere den Text
      if (response.data is Map<String, dynamic> &&
          response.data['extractedText'] is String) {
        final extractedText = response.data['extractedText'] as String;
        // Überprüfe, ob der extrahierte Text leer ist (kann bei Bild-PDFs ohne Text passieren)
        if (extractedText.isEmpty) {
          _log.w(
            'OCR-Funktion erfolgreich, aber kein Text extrahiert (möglicherweise Bild-PDF).',
          );
          return null; // Gib null zurück, um anzuzeigen, dass kein Text gefunden wurde
        } else {
          _log.i(
            'OCR erfolgreich. Extrahierter Text Länge: ${extractedText.length}',
          );
          return extractedText;
        }
      } else {
        // Unerwartetes, aber erfolgreiches (Status 200) Format
        _log.w(
          'Unerwartetes Datenformat von OCR-Funktion "$functionName" bei Status 200: ${response.data}',
        );
        throw FunctionError(
          'Unerwartetes Erfolgsformat von der OCR-Funktion empfangen.',
        );
      }
    } on FunctionException catch (e) {
      // Fange spezifische Supabase Function Exceptions ab
      _log.e(
        'Supabase FunctionException bei OCR "$functionName": Status ${e.status}',
        error: e.details,
      );
      throw FunctionError(
        'Fehler bei der Kommunikation mit dem OCR-Service: ${e.details?.toString() ?? 'Unbekannter Fehler'}',
      );
    } catch (e, stackTrace) {
      // Fange alle anderen unerwarteten Fehler ab
      _log.e(
        'Unerwarteter Fehler bei OCR-Aufruf "$functionName": $e',
        stackTrace: stackTrace,
      );
      throw FunctionError(
        'Unerwarteter lokaler Fehler beim OCR-Aufruf: ${e.toString()}',
      );
    }

    // --- PLATZHALTER (ENTFERNT) ---
    /*
    _log.w('"$functionName" ist noch nicht implementiert. Simuliere OCR-Fehler.');
    await Future.delayed(const Duration(seconds: 2)); // Simuliere Verarbeitung
    return null; // Simuliere, dass kein Text gefunden wurde
    */
  }

  // *** Rückgabetyp geändert ***
  Future<GenerateCoverLetterResult> generateCoverLetter({
    required Map<String, dynamic> userProfile,
    required Map<String, dynamic> jobPosting,
    String? personalizedStylePrompt,
    String? modelType, // Neu: KI-Modell-Typ (deepseek oder mistral)
  }) async {
    // Wähle die richtige Funktion basierend auf dem Modelltyp
    final functionName =
        modelType == 'deepseek'
            ? 'generate-cover-letter'
            : 'generate-cover-letter-mistral';

    _log.i("Rufe Supabase Function '$functionName' auf...");
    _log.d('UserProfile: $userProfile');
    _log.d('JobPosting: $jobPosting');

    if (modelType != null) {
      _log.i("Verwende KI-Modell: $modelType für Funktion: $functionName");
    } else {
      _log.i("Kein Modelltyp angegeben, verwende Funktion: $functionName");
    }

    if (personalizedStylePrompt != null && personalizedStylePrompt.isNotEmpty) {
      _log.i(
        "Verwende personalisierten Schreibstil-Prompt: ${personalizedStylePrompt.substring(0, min(50, personalizedStylePrompt.length))}...",
      );
    }

    try {
      // Benutzer-ID prüfen (aus Supabase Auth)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw FunctionError(
          'Nicht authentifiziert. Bitte anmelden, um ein Anschreiben zu generieren.',
        );
      }

      final response = await _client.functions.invoke(
        functionName, // Name der Edge Function
        body: {
          'userId':
              currentUser.id, // Benutzer-ID für Premium-Status-Überprüfung
          'userProfile': userProfile,
          'jobPosting': jobPosting,
          if (personalizedStylePrompt != null &&
              personalizedStylePrompt.isNotEmpty)
            'personalizedStylePrompt': personalizedStylePrompt,
        },
      );

      _log.i('Supabase Function Antwort Status: ${response.status}');

      if (response.status != 200) {
        _log.e(
          'Fehler beim Aufrufen der Supabase Function: ${response.status}',
          error: response.data,
        );

        // Prüfe, ob es sich um einen Premium-Fehler handelt
        if (response.status == 403 &&
            response.data is Map<String, dynamic> &&
            response.data['requiresUpgrade'] == true) {
          _log.w('Premium-Zugriff erforderlich für aiCoverLetter');
          throw PremiumRequiredError.fromResponse(
            response.data as Map<String, dynamic>,
            defaultFeature: 'aiCoverLetter',
          );
        }

        // Standardfehlerbehandlung
        String errorMessage = 'Unbekannter Fehler beim Aufruf der Funktion.';
        if (response.data is Map<String, dynamic> &&
            response.data['error'] != null) {
          errorMessage = response.data['error'] as String;
        }

        throw FunctionError('Fehler ${response.status}: $errorMessage');
      }

      if (response.data == null) {
        _log.w('Supabase Function gab keine Daten zurück.');
        throw FunctionError('Die Funktion gab keine Daten zurück.');
      }

      // *** Parse die neue Struktur ***
      _log.d('Supabase Function Antwort Daten: ${response.data}');
      final result = GenerateCoverLetterResult.fromJson(
        response.data as Map<String, dynamic>,
      );
      _log.i(
        'Anschreiben erfolgreich generiert. Extrahierte Email: ${result.extractedEmail}',
      );
      return result; // Gebe das Ergebnisobjekt zurück
    } on FunctionException catch (e) {
      // Log detailed error information
      _log.d(
        'FunctionException Details: Status=${e.status}, Details=${e.details}',
      );
      _log.e(
        'Supabase FunctionException beim Aufrufen von \'$functionName\': Status=${e.status}',
        error: e,
      );

      String detailError = e.details?.toString() ?? '';
      if (e.details is Map && (e.details as Map).containsKey('error')) {
        detailError = (e.details as Map)['error'].toString();
      }

      throw FunctionError(
        'Fehler bei der Kommunikation mit dem Anschreiben-Service: ${detailError.isNotEmpty ? detailError : 'Unbekannter Funktionsfehler'}',
      );
    } catch (e, stackTrace) {
      _log.e(
        'Unerwarteter Fehler im SupabaseService:',
        error: e,
        stackTrace: stackTrace,
      );
      throw FunctionError(
        'Ein unerwarteter Fehler ist aufgetreten: ${e.toString()}',
      );
    }
  }

  // Firebase Fallback-Methode entfernt

  // +++ NEUE METHODE HINZUFÜGEN +++
  Future<void> saveUserProfileData(UserProfile userProfile) async {
    _log.i('Speichere Benutzerprofil für ID: ${userProfile.id}');

    try {
      // Benutzer-ID prüfen (aus Supabase Auth)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw FunctionError(
          'Nicht authentifiziert. Bitte anmelden, um das Profil zu speichern.',
        );
      }

      if (currentUser.id != userProfile.id) {
        _log.e(
          'Sicherheitsfehler: User ID mismatch! Current: ${currentUser.id}, Profile: ${userProfile.id}',
        );
        throw FunctionError(
          'Sicherheitsfehler: Profil-ID stimmt nicht mit angemeldetem Benutzer überein.',
        );
      }

      // Speichere Profildaten in Supabase
      _log.i(
        "Versuche, Profil in Supabase zu speichern: profiles/${userProfile.id}",
      );

      // Speichere das Profil in der Supabase-Tabelle 'profiles'
      await _client.from('profiles').upsert({
        'id': userProfile.id,
        'data': userProfile.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      _log.i('Benutzerprofil erfolgreich in Supabase gespeichert.');
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Speichern des Benutzerprofils in Supabase:',
        error: e,
        stackTrace: stackTrace,
      );

      // Konvertiere Fehler in einen FunctionError für einheitliche Behandlung
      if (e is FunctionError) {
        rethrow;
      } else {
        throw FunctionError(
          'Unerwarteter Fehler beim Speichern des Profils: ${e.toString()}',
        );
      }
    }
  }

  /// Generiert Berufsbezeichnungen als Schlüsselwörter basierend auf dem Nutzerprofil
  ///
  /// [profileData]: Die Profildaten des Nutzers, die analysiert werden sollen
  /// Gibt bei Erfolg eine Liste von Berufsbezeichnungen zurück
  /// Bei Fehlern wird eine leere Liste zurückgegeben
  Future<List<String>> generateJobKeywords(
    Map<String, dynamic> profileData,
  ) async {
    _log.i('Rufe Supabase Function "generate-job-keywords" auf...');

    try {
      // Benutzer-ID prüfen (aus Supabase Auth)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        _log.e(
          'Nicht authentifiziert. Bitte anmelden, um Berufsbezeichnungen zu generieren.',
        );
        return [];
      }

      final response = await _client.functions.invoke(
        'generate-job-keywords',
        body: {
          'userId':
              currentUser.id, // Benutzer-ID für Premium-Status-Überprüfung
          'profileData': profileData,
        },
      );

      _log.i(
        'Supabase Function "generate-job-keywords" Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;

        if (data.containsKey('jobKeywords') && data['jobKeywords'] is List) {
          final jobKeywords =
              (data['jobKeywords'] as List)
                  .map((item) => item.toString())
                  .toList();
          _log.i(
            'Berufsbezeichnungen erfolgreich generiert: ${jobKeywords.join(", ")}',
          );
          return jobKeywords;
        } else {
          _log.w(
            'Unerwartetes Antwortformat: Keine "jobKeywords" Liste gefunden',
          );
          return [];
        }
      } else if (response.status == 403 &&
          response.data is Map<String, dynamic> &&
          response.data['requiresUpgrade'] == true) {
        // Premium-Fehler
        _log.w('Premium-Zugriff erforderlich für aiJobSearch');
        throw PremiumRequiredError.fromResponse(
          response.data as Map<String, dynamic>,
          defaultFeature: 'aiJobSearch',
        );
      } else {
        _log.e(
          'Fehler bei der Ausführung der Function "generate-job-keywords". Status: ${response.status}, Data: ${response.data}',
        );
        return [];
      }
    } on FunctionException catch (e) {
      _log.e(
        'FunctionException beim Aufruf von "generate-job-keywords"',
        error: e,
      );
      return [];
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Aufruf der Function "generate-job-keywords"',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  // +++ NEUE METHODE: analyzeProfileStyle +++
  /// Analysiert das Benutzerprofil, um einen personalisierten Schreibstil-Prompt zu generieren
  ///
  /// Gibt bei Erfolg den generierten Prompt als String zurück
  /// Bei Fehlern wird eine Exception geworfen
  Future<String> analyzeProfileStyle() async {
    _log.i('Rufe Supabase Function "analyze-profile-style" auf...');

    try {
      // Aktuellen User abrufen
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw FunctionError(
          'Nicht authentifiziert. Bitte anmelden, um den Schreibstil zu analysieren.',
        );
      }

      // Profildaten aus Supabase laden
      final response =
          await _client
              .from('profiles')
              .select('data')
              .eq('id', currentUser.id)
              .single();

      if (!response.containsKey('data')) {
        throw FunctionError(
          'Kein Profil gefunden. Bitte vervollständigen Sie Ihr Profil zuerst.',
        );
      }

      final profileData = response['data'] as Map<String, dynamic>;
      _log.d('Profildaten für Analyse geladen: ${profileData.keys.join(", ")}');

      // Supabase Function aufrufen
      final functionResponse = await _client.functions.invoke(
        'analyze-profile-style',
        body: {
          'userId':
              currentUser.id, // Benutzer-ID für Premium-Status-Überprüfung
          'profileData': profileData,
        },
      );

      _log.i(
        'Supabase Function "analyze-profile-style" Antwort Status: ${functionResponse.status}',
      );

      if (functionResponse.status == 200 && functionResponse.data != null) {
        final data = functionResponse.data as Map<String, dynamic>;

        if (data.containsKey('stylePrompt') && data['stylePrompt'] is String) {
          final stylePrompt = data['stylePrompt'] as String;
          _log.i('Personalisierter Schreibstil-Prompt erfolgreich generiert.');
          _log.d(
            'Generierter Prompt: ${stylePrompt.substring(0, min(100, stylePrompt.length))}...',
          );
          return stylePrompt;
        } else {
          _log.w('Unerwartetes Antwortformat: Kein "stylePrompt" gefunden');
          throw FunctionError(
            'Unerwartetes Antwortformat: Kein Schreibstil-Prompt in der Antwort gefunden.',
          );
        }
      } else if (functionResponse.status == 403 &&
          functionResponse.data is Map<String, dynamic> &&
          functionResponse.data['requiresUpgrade'] == true) {
        // Premium-Fehler
        _log.w('Premium-Zugriff erforderlich für aiCoverLetter');
        throw PremiumRequiredError.fromResponse(
          functionResponse.data as Map<String, dynamic>,
          defaultFeature: 'aiCoverLetter',
        );
      } else {
        _log.e(
          'Fehler bei der Ausführung der Function "analyze-profile-style". Status: ${functionResponse.status}, Data: ${functionResponse.data}',
        );
        throw FunctionError(
          'Fehler bei der Ausführung der Funktion: ${functionResponse.status}',
        );
      }
    } on FunctionException catch (e) {
      _log.e(
        'FunctionException beim Aufruf von "analyze-profile-style"',
        error: e,
      );
      String detailError = e.details?.toString() ?? '';
      if (e.details is Map && (e.details as Map).containsKey('error')) {
        detailError = (e.details as Map)['error'].toString();
      }
      throw FunctionError(
        'Fehler bei der Kommunikation mit dem Server: ${detailError.isNotEmpty ? detailError : 'Unbekannter Funktionsfehler'}',
      );
    } catch (e, stackTrace) {
      if (e is FunctionError) {
        rethrow;
      }
      _log.e(
        'Fehler beim Aufruf der Function "analyze-profile-style"',
        error: e,
        stackTrace: stackTrace,
      );
      throw FunctionError(
        'Ein unerwarteter Fehler ist aufgetreten: ${e.toString()}',
      );
    }
  }

  // +++ ENDE NEUE METHODE +++
}

// Riverpod Provider für den SupabaseClient
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

// Riverpod Provider für den SupabaseService
@riverpod
SupabaseService supabaseService(Ref ref) {
  final client = ref.watch(supabaseClientProvider);
  return SupabaseService(client);
}

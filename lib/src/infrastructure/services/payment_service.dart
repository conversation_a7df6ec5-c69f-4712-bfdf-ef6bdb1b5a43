import 'dart:async';
import 'dart:io';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// WICHTIG: Definiere deine Produkt-ID hier (muss mit Play Console übereinstimmen)
// Für Android - Produkt-IDs für verschiedene Abonnementtypen (nur monatlich verfügbar)
// Free ist kostenlos und benötigt keine Produkt-ID
const String _androidProProductId = 'pro_subscription';
const String _androidUnlimitedProductId = 'unlimited_subscription';
// Standardmäßig verwenden wir das Pro-Abonnement
// const String _androidPremiumProductId = _androidProProductId;

// Für iOS - Produkt-IDs für verschiedene Abonnementtypen (nur monatlich verfügbar)
const String _iosProProductId = 'pro_subscription';
const String _iosUnlimitedProductId = 'unlimited_subscription';
// Standardmäßig verwenden wir das Pro-Abonnement
// const String _iosPremiumProductId = _iosProProductId;

// final String _premiumProductId =
//     Platform.isAndroid ? _androidProProductId : _iosProProductId;

// Logger für besseres Debugging
final _log = getLogger('PaymentService');

// Provider für den PaymentService
final paymentServiceProvider = Provider<PaymentService>((ref) {
  _log.i("paymentServiceProvider wird erstellt");
  final userProfileNotifier = ref.watch(userProfileProvider.notifier);

  // Wichtig: Der SupabaseSubscriptionService wird NICHT hier gelesen.
  // Er muss später über den Setter injiziert werden.

  final paymentService = PaymentService(
    InAppPurchase.instance,
    userProfileNotifier,
    // null, // Übergebe hier null, da es später gesetzt wird.
  );

  _log.i("paymentServiceProvider erstellt. Initialisierung läuft...");
  // Die Initialisierung wird im Konstruktor von PaymentService aufgerufen.

  // Optional: Den SupabaseSubscriptionService hier holen und setzen, wenn keine Zirkelbezüge bestehen
  // try {
  //   final subService = ref.read(supabaseSubscriptionServiceProvider);
  //   paymentService.setSubscriptionService(subService);
  // } catch (e) {
  //   _log.w("SupabaseSubscriptionService konnte beim Erstellen des PaymentServiceProviders nicht sofort injiziert werden: $e");
  // }

  return paymentService;
});

class PaymentService {
  final InAppPurchase _inAppPurchase;
  final UserProfileNotifier _userProfileNotifier;
  // SupabaseSubscriptionService wird optional gehalten und über Setter injiziert
  // SupabaseSubscriptionService? _subscriptionService;

  // Produkt-IDs für verschiedene Plattformen (innerhalb der Klasse zur Kapselung)
  // static const String _androidPremiumProductId = 'premium_monthly_subscription';
  // static const String _iosPremiumProductId = 'premium_monthly_subscription';

  // Debug-Modus für Testzwecke (innerhalb der Klasse)
  // static const bool _debugModeEnabled = false;

  // Logger-Instanz (kann innerhalb der Klasse bleiben oder global)
  // final _log = getLogger('PaymentService');

  // Verfügbare Produkte
  List<ProductDetails>? _availableProducts;
  ProductDetails? _premiumProduct; // benötigt für Fallback/Logs
  ProductDetails? _proProduct;
  ProductDetails? _unlimitedProduct;

  // Stream-Abonnement für Kaufupdates
  StreamSubscription<List<PurchaseDetails>>?
  _purchaseStreamSubscription; // Umbenannt zur Klarheit

  // Callback für Kaufprozess
  Completer<bool>? _purchaseCompleter; // Verwende Completer statt Callback

  bool _isInitialized =
      false; // Flag für Initialisierung (nur true, wenn vollständig)
  bool _isAvailable = false; // Flag für IAP Verfügbarkeit
  bool _isInitializing = false; // Läuft gerade eine Initialisierung?
  Completer<void>? _initCompleter; // Signalisiert Abschluss der Initialisierung

  PaymentService(
    this._inAppPurchase,
    this._userProfileNotifier,
    // this._subscriptionService, // Entfernt aus Konstruktor
  ) {
    _log.i("PaymentService Instanz wird erstellt.");
    _initializeService(); // Initialisierung starten
  }

  // Factory-Konstruktor wird nicht mehr benötigt, da Provider direkt die Klasse erstellt.
  // factory PaymentService.factory(...) { ... }

  // Setter für den Subscription-Service (wird vom Provider aufgerufen, falls nötig)
  // void setSubscriptionService(SupabaseSubscriptionService service) {
  //   _log.i("SupabaseSubscriptionService wird gesetzt.");
  //   _subscriptionService = service;
  // }

  /// Initialisiert den Payment Service nur einmal und signalisiert Fertigstellung korrekt.
  Future<void> _initializeService() async {
    // Wenn bereits vollständig initialisiert, sofort zurück
    if (_isInitialized) return;

    // Wenn gerade Initialisierung läuft, auf deren Abschluss warten
    if (_isInitializing && _initCompleter != null) {
      await _initCompleter!.future;
      return;
    }

    _isInitializing = true;
    _initCompleter ??= Completer<void>();

    _log.i('Initialisiere Payment Service...');

    try {
      _isAvailable = await _inAppPurchase.isAvailable();
      if (!_isAvailable) {
        _log.e('In-App-Kauf ist nicht verfügbar auf diesem Gerät');
        return;
      }
      _log.i("In-App Purchase ist verfügbar.");

      // Starte das Abhören von Kaufupdates
      _purchaseStreamSubscription = _inAppPurchase.purchaseStream.listen(
        _listenToPurchaseUpdated,
        onDone: () {
          _log.i("Purchase Stream wurde geschlossen.");
          _purchaseStreamSubscription?.cancel();
          _purchaseStreamSubscription = null;
        },
        onError: (error) {
          _log.e('Fehler im Kauf-Stream: $error');
          // Informiere ggf. den laufenden Kaufprozess über den Fehler
          if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.completeError(error);
            _purchaseCompleter = null;
          }
        },
      );
      _log.i("Purchase Stream Listener registriert.");

      // Lade die verfügbaren Produkte
      await _loadProducts();

      // Markiere vollständige Initialisierung
      _isInitialized = true;
      _log.i("Initialisierung abgeschlossen.");
    } finally {
      _isInitializing = false;
      if (_initCompleter != null && !_initCompleter!.isCompleted) {
        _initCompleter!.complete();
      }
    }
  }

  /// Stellt sicher, dass der Service initialisiert ist (wartet, falls Initialisierung noch läuft)
  Future<void> _ensureInitialized() async {
    if (_isInitialized) return;
    if (_isInitializing && _initCompleter != null) {
      await _initCompleter!.future;
      return;
    }
    await _initializeService();
  }

  /// Gibt alle verfügbaren Produkte zurück
  List<ProductDetails> getAvailableProducts() {
    return _availableProducts ?? [];
  }

  /// Gibt ein spezifisches Produkt basierend auf dem Plan-Typ zurück
  ProductDetails? getProductForPlan(String planType) {
    switch (planType.toLowerCase()) {
      case 'pro':
        return _proProduct;
      case 'unlimited':
        return _unlimitedProduct;
      default:
        return _proProduct; // Fallback auf Pro (Free benötigt kein Produkt)
    }
  }

  /// Lädt die verfügbaren Premium-Produkte.
  Future<void> _loadProducts() async {
    _log.i('Lade verfügbare Produkte...');
    if (!_isAvailable) {
      // Prüfe Verfügbarkeit erneut
      _log.w("Kann Produkte nicht laden, da IAP nicht verfügbar ist.");
      return;
    }

    try {
      final productIds = <String>{
        Platform.isAndroid ? _androidProProductId : _iosProProductId,
        Platform.isAndroid
            ? _androidUnlimitedProductId
            : _iosUnlimitedProductId,
      };
      _log.d("Frage Produktdetails für IDs ab: $productIds");

      final response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.notFoundIDs.isNotEmpty) {
        _log.e(
          'Fehler beim Laden der Produkte: Nicht gefundene IDs: ${response.notFoundIDs}',
        );
        _availableProducts = []; // Setze leere Liste bei Fehler
        _premiumProduct = null;
        return;
      }

      if (response.productDetails.isEmpty) {
        _log.w('Keine Produkte für IDs $productIds gefunden.');
        _availableProducts = [];
        _premiumProduct = null;
        return;
      }

      _availableProducts = response.productDetails;
      _log.i(
        '${_availableProducts!.length} Produkte geladen: ${_availableProducts!.map((p) => p.id).join(', ')}',
      );

      // Speichere die Produkte nach Typ
      final proProductId =
          Platform.isAndroid ? _androidProProductId : _iosProProductId;
      final unlimitedProductId =
          Platform.isAndroid
              ? _androidUnlimitedProductId
              : _iosUnlimitedProductId;

      _proProduct =
          _availableProducts!
              .where((product) => product.id == proProductId)
              .firstOrNull;
      _unlimitedProduct =
          _availableProducts!
              .where((product) => product.id == unlimitedProductId)
              .firstOrNull;

      // Premium-Produkt ist das gewählte je nach Plan; setze Fallback
      _premiumProduct =
          _proProduct ?? _unlimitedProduct ?? _availableProducts!.firstOrNull;

      _log.i('Geladene Produkte:');
      if (_proProduct != null) {
        _log.i('  Pro: ${_proProduct!.title} (${_proProduct!.id})');
      }
      if (_unlimitedProduct != null) {
        _log.i(
          '  Unlimited: ${_unlimitedProduct!.title} (${_unlimitedProduct!.id})',
        );
      }

      if (_proProduct == null && _unlimitedProduct == null) {
        _log.w('Keine der erwarteten Produkte wurden gefunden.');
      }
    } catch (e, stack) {
      _log.e('Schwerer Fehler beim Laden der Produkte: $e', stackTrace: stack);
      _availableProducts = [];
      _premiumProduct = null;
    }
  }

  /// Kauft ein Premium-Abonnement.
  Future<bool> buyPremiumSubscription({String? planType}) async {
    _log.i('Starte Premium-Kauf-Prozess für Plan: $planType...');

    // Stelle sicher, dass initialisiert wurde (Race-Condition-Fix)
    await _ensureInitialized();

    // Falls Verfügbarkeit noch false ist, kurzer Retry (manche Geräte brauchen einen Tick)
    if (!_isAvailable) {
      _log.w('IAP initial noch nicht verfügbar – Retry Initialisierung...');
      await Future.delayed(const Duration(milliseconds: 250));
      await _ensureInitialized();
    }

    if (!_isAvailable) {
      _log.e('Kauf nicht möglich: IAP nicht verfügbar.');
      return false;
    }

    // Produkte laden, falls noch nicht geschehen oder fehlgeschlagen
    if (_availableProducts == null || _availableProducts!.isEmpty) {
      _log.w("Produkte nicht geladen, versuche erneutes Laden...");
      await _loadProducts();
    }

    // Wähle das richtige Produkt basierend auf dem planType
    ProductDetails? selectedProduct;
    final selectedPlanType = planType?.toLowerCase() ?? 'pro';

    switch (selectedPlanType) {
      case 'pro':
        selectedProduct = _proProduct;
        break;
      case 'unlimited':
        selectedProduct = _unlimitedProduct;
        break;
      default:
        selectedProduct = _proProduct; // Fallback auf Pro
    }

    // Immer noch kein Produkt?
    if (selectedProduct == null) {
      _log.e(
        'Kauf nicht möglich: Produkt für Plan "$selectedPlanType" konnte nicht geladen oder gefunden werden.',
      );
      return false;
    }

    _log.i(
      'Versuche Produkt zu kaufen: ${selectedProduct.id} für Plan: $selectedPlanType',
    );

    // Verhindere parallele Käufe
    if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
      _log.w("Ein Kaufprozess läuft bereits.");
      return await _purchaseCompleter!
          .future; // Warte auf den laufenden Prozess
    }

    _purchaseCompleter = Completer<bool>(); // Neuen Completer erstellen

    try {
      // Starte den Kaufprozess
      final purchaseParam = PurchaseParam(productDetails: selectedProduct);

      // Für Abonnements verwenden wir die spezielle Methode für Abonnements
      _log.d("Rufe _inAppPurchase.buyNonConsumable auf...");

      // Für Android verwenden wir die Methode für Abonnements
      if (Platform.isAndroid) {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        // Für iOS verwenden wir die Methode für Abonnements
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }

      _log.d("Kauf-Methode aufgerufen, warte auf Stream-Antwort...");

      // Warte auf das Ergebnis des Kaufs via Stream (max. 2 Minuten)
      final result = await _purchaseCompleter!.future.timeout(
        const Duration(minutes: 2),
        onTimeout: () {
          _log.e('Kaufprozess-Timeout nach 2 Minuten.');
          if (!_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
          _purchaseCompleter = null; // Reset Completer nach Timeout
          return false;
        },
      );
      _purchaseCompleter = null; // Reset Completer nach Abschluss
      return result;
    } catch (e, stack) {
      _log.e('Fehler beim Starten des Kaufs: $e', stackTrace: stack);
      if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
        _purchaseCompleter!.complete(false); // Schließe als fehlgeschlagen ab
      }
      _purchaseCompleter = null; // Reset Completer bei Fehler
      return false;
    }
  }

  /// Wiederherstellung früherer Käufe.
  Future<bool> restorePurchases() async {
    _log.i('Versuche Käufe wiederherzustellen...');
    if (!_isAvailable) {
      _log.e("Kann Käufe nicht wiederherstellen, IAP nicht verfügbar.");
      return false;
    }
    try {
      await _inAppPurchase.restorePurchases();
      _log.i(
        "Wiederherstellungsanfrage gesendet. Ergebnis kommt über den Stream.",
      );
      // Der Erfolg wird über den _listenToPurchaseUpdated Stream signalisiert.
      // Wir können hier nicht direkt auf Erfolg warten.
      return true; // Signalisiert, dass der Versuch gestartet wurde.
    } catch (e, stack) {
      _log.e(
        "Fehler beim Starten der Wiederherstellung: $e",
        stackTrace: stack,
      );
      return false;
    }
  }

  /// Verarbeitet Kauf-Updates aus dem Stream.
  Future<void> _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    _log.i(
      "Kauf-Update erhalten. Anzahl Details: ${purchaseDetailsList.length}",
    );

    // Verwende for-Schleife statt forEach für bessere Lesbarkeit und Linting-Kompatibilität
    for (final purchaseDetails in purchaseDetailsList) {
      _log.d(
        "Verarbeite Kauf-Detail: ID=${purchaseDetails.purchaseID}, Produkt=${purchaseDetails.productID}, Status=${purchaseDetails.status}, Error=${purchaseDetails.error}",
      );

      if (purchaseDetails.status == PurchaseStatus.pending) {
        _log.i("Kauf für ${purchaseDetails.productID} ist ausstehend...");
        // Zeige ggf. eine Ladeanzeige an
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          _log.e(
            "Kauffehler: ${purchaseDetails.error?.message} (Code: ${purchaseDetails.error?.code})",
          );
          // Informiere den laufenden Kaufprozess über den Fehler
          if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
          // Zeige Fehlermeldung an
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          _log.i(
            "Kauf/Wiederherstellung erfolgreich für ${purchaseDetails.productID}. Status: ${purchaseDetails.status}",
          );

          // 1. Verifiziere den Kauf (Client- oder Serverseitig)
          bool isValid = await _verifyPurchase(purchaseDetails);
          _log.d("Kauf verifiziert: $isValid");

          if (isValid) {
            _log.i("Kauf gültig. Gewähre Premium-Zugang.");
            // 2. Gewähre Premium-Zugang (Update UserProfile)
            try {
              final expiryDate = _calculateExpiryDate(
                purchaseDetails,
              ); // Berechne Ablaufdatum (Beispiel)
              await _userProfileNotifier.updatePremiumStatus(
                isPremium: true,
                premiumExpiryDate: expiryDate,
                // Optional: Speichere Transaktionsdetails
                transactionId: purchaseDetails.purchaseID,
                purchaseDate:
                    purchaseDetails.transactionDate != null
                        ? DateTime.fromMillisecondsSinceEpoch(
                          int.parse(purchaseDetails.transactionDate!),
                        )
                        : DateTime.now(),
              );
              _log.i("Premium-Status erfolgreich im UserProfile aktualisiert.");
              // Informiere den laufenden Kaufprozess über den Erfolg
              if (_purchaseCompleter != null &&
                  !_purchaseCompleter!.isCompleted) {
                _purchaseCompleter!.complete(true);
              }
            } catch (e, stack) {
              _log.e(
                "Fehler beim Aktualisieren des Premium-Status nach Kauf: $e",
                stackTrace: stack,
              );
              // Informiere den laufenden Kaufprozess über den Fehler
              if (_purchaseCompleter != null &&
                  !_purchaseCompleter!.isCompleted) {
                _purchaseCompleter!.complete(false);
              }
            }
          } else {
            _log.w(
              "Kaufverifizierung fehlgeschlagen für ${purchaseDetails.purchaseID}.",
            );
            // Informiere den laufenden Kaufprozess über den Fehler
            if (_purchaseCompleter != null &&
                !_purchaseCompleter!.isCompleted) {
              _purchaseCompleter!.complete(false);
            }
          }

          // 3. Schließe den Kauf ab (wichtig!)
          if (purchaseDetails.pendingCompletePurchase) {
            _log.d("Schließe Kauf ${purchaseDetails.purchaseID} ab...");
            try {
              await _inAppPurchase.completePurchase(purchaseDetails);
              _log.i(
                "Kauf ${purchaseDetails.purchaseID} erfolgreich abgeschlossen.",
              );
            } catch (e, stack) {
              _log.e(
                "Fehler beim Abschließen des Kaufs ${purchaseDetails.purchaseID}: $e",
                stackTrace: stack,
              );
            }
          }
        } else if (purchaseDetails.status == PurchaseStatus.canceled) {
          _log.i("Kauf für ${purchaseDetails.productID} wurde abgebrochen.");
          // Informiere den laufenden Kaufprozess über den Abbruch
          if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
        }

        // Setze den Completer zurück, wenn er nicht mehr benötigt wird
        // Überprüfung hinzugefügt, ob der Completer für diesen spezifischen Kauf war (optional, aber sicherer)
        if (_purchaseCompleter != null &&
            purchaseDetails.purchaseID ==
                _purchaseCompleter?.hashCode.toString()) {
          // Beispielhafte Verknüpfung
          // _purchaseCompleter = null;
        }
      }
    }
  }

  /// Sichere serverseitige Kaufverifizierung über Supabase Edge Function
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    _log.i('Starte serverseitige Kaufverifizierung...');

    try {
      // Hole aktuelle Benutzer-ID
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        _log.e('Kein authentifizierter Benutzer für Kaufverifizierung');
        return false;
      }

      // Bestimme Plan-Typ basierend auf Produkt-ID
      String planType = 'basic';
      if (purchaseDetails.productID.contains('pro')) {
        planType = 'pro';
      } else if (purchaseDetails.productID.contains('unlimited')) {
        planType = 'unlimited';
      }

      // Bereite Verifizierungsdaten vor (plattformabhängig)
      final bool isAndroid = Platform.isAndroid;
      final verificationData = <String, dynamic>{
        'userId': user.id,
        'productId': purchaseDetails.productID,
        'planType': planType,
        'platform': isAndroid ? 'android' : 'ios',
        if (isAndroid)
          'purchaseToken':
              purchaseDetails.verificationData.serverVerificationData,
        if (isAndroid)
          'packageName':
              'com.einsteinai.app', // App Package Name aus build.gradle
        if (!isAndroid)
          'receiptData':
              purchaseDetails.verificationData.serverVerificationData,
        if (!isAndroid) 'transactionId': purchaseDetails.purchaseID ?? '',
      };

      _log.d('Sende Verifizierungsanfrage: $verificationData');

      // Rufe Supabase Edge Function auf
      final response = await Supabase.instance.client.functions.invoke(
        'verify-purchase',
        body: verificationData,
      );

      if (response.status != 200) {
        _log.e('Fehler bei Kaufverifizierung: HTTP ${response.status}');
        return false;
      }

      final responseData = response.data;
      if (responseData != null && responseData['success'] == true) {
        _log.i('Kauf erfolgreich serverseitig verifiziert');
        _log.d('Verify-Purchase Response: $responseData');

        // Nach Erfolg: Trigger leichte Server-Abfrage zur Warmup/Signalwirkung
        try {
          final currentUser = Supabase.instance.client.auth.currentUser;
          if (currentUser != null) {
            await Supabase.instance.client.rpc(
              'get_remaining_applications_safe',
              params: {'p_user_id': currentUser.id},
            );
            _log.i('Bewerbungszähler nach Kauf vom Server geladen (Warmup)');
          }
        } catch (e) {
          _log.w('Konnte Warmup nach Kauf nicht triggern: $e');
        }
        return true;
      } else {
        // Wenn Server false liefert, prüfe trotzdem, ob in DB bereits aktives Abo existiert (Race/Fallback)
        try {
          final user = Supabase.instance.client.auth.currentUser;
          if (user != null) {
            final active =
                await Supabase.instance.client
                    .from('subscriptions')
                    .select('id')
                    .eq('user_id', user.id)
                    .eq('status', 'active')
                    .gte('end_date', DateTime.now().toIso8601String())
                    .limit(1)
                    .maybeSingle();
            if (active != null) {
              _log.w(
                'Server meldete false, aber DB zeigt aktives Premium. Behandle als Erfolg.',
              );
              return true;
            }
          }
        } catch (_) {}

        _log.w(
          'Kaufverifizierung fehlgeschlagen: ${responseData?['error'] ?? 'Unbekannter Fehler'}',
        );
        return false;
      }
    } catch (e, stack) {
      _log.e(
        'Fehler bei serverseitiger Kaufverifizierung: $e',
        stackTrace: stack,
      );
      return false;
    }
  }

  /// Berechnet das Ablaufdatum (Beispiel - abhängig vom Produkt).
  DateTime? _calculateExpiryDate(PurchaseDetails purchaseDetails) {
    // Diese Logik hängt stark vom gekauften Produkt ab (Abo-Dauer etc.)
    // Hier nur ein Beispiel: 30 Tage ab Kaufdatum
    if (purchaseDetails.transactionDate != null) {
      final purchaseTime = DateTime.fromMillisecondsSinceEpoch(
        int.parse(purchaseDetails.transactionDate!),
      );
      return purchaseTime.add(const Duration(days: 30));
    }
    return DateTime.now().add(const Duration(days: 30)); // Fallback
  }

  /// Gibt den Payment Service frei und hebt Abonnements auf.
  void dispose() {
    _log.i("Disposing Payment Service...");
    _purchaseStreamSubscription?.cancel();
    _purchaseStreamSubscription = null;
    // Apple-spezifische Bereinigung (falls verwendet)
    if (Platform.isIOS) {
      // var iosPlatformAddition = _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      // iosPlatformAddition.setDelegate(null);
    }
    _log.i("Payment Service disposed.");
  }
}

import 'dart:async';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/foundation.dart'; // Für kDebugMode
import '../src/domain/entities/job_entity.dart';
import '../src/domain/models/user_profile.dart';

/// Service zur Analyse von Stellenangeboten mittels KI
class JobAnalysisService {
  // Referenz zu Firebase Functions für den Gemini-Zugriff
  final FirebaseFunctions _functions;
  
  // Cache für bereits analysierte Jobs
  final Map<String, JobAnalysisResult> _analysisCache = {};
  
  // Konstruktor, der optional eine FirebaseFunctions Instanz akzeptiert (für Tests)
  JobAnalysisService({FirebaseFunctions? functions}) :
      _functions = functions ?? FirebaseFunctions.instanceFor(region: 'us-central1');
      
  /// Analysiert ein Stellenangebot im Kontext des Nutzerprofils und Suchkriterien
  /// Gibt ein JobAnalysisResult mit Relevanz-Score und Begründung zurück
  Future<JobAnalysisResult> analyzeJobForUser(
    JobEntity job, 
    UserProfile userProfile, 
    {String? searchQuery, bool useCache = true}
  ) async {
    // Cache Lookup für schnellere Ergebnisse
    final cacheKey = "${job.id}_${userProfile.id}_${searchQuery ?? 'no_query'}";
    if (useCache && _analysisCache.containsKey(cacheKey)) {
      print("Verwende Cache-Ergebnis für Job ${job.id}");
      return _analysisCache[cacheKey]!;
    }
    
    // Wenn offline oder Netzwerkfehler, versuche lokal zu bewerten
    try {
      // HINWEIS: Aufruf der Gemini Cloud Function entfernt!
      // Stattdessen wird jetzt eine Dummy-Analyse durchgeführt.
      debugPrint("analyzeJobForUser: Gemini-Aufruf entfernt. Führe Dummy-Analyse durch für Job ${job.id}");

      // Dummy-Analyse Logik:
      double dummyScore = (job.title.hashCode % 100) / 100.0; // Einfacher Dummy-Score
      List<String> dummyMatches = [];
      List<String> dummyMismatches = [];
      
      if (dummyScore > 0.7) {
        dummyMatches.add("Titel scheint gut zu passen (Dummy)");
      } else {
        dummyMismatches.add("Titel passt weniger gut (Dummy)");
        }
      if (userProfile.skills.isNotEmpty) {
         if (job.description.toLowerCase().contains(userProfile.skills.first.toLowerCase())) {
           dummyMatches.add("Skill '${userProfile.skills.first}' gefunden (Dummy)");
         } else {
            dummyMismatches.add("Wichtige Skills nicht im Text gefunden (Dummy)");
    }
      }
    if (searchQuery != null && searchQuery.isNotEmpty) {
        dummyMatches.add("Bezieht sich auf Suche: '$searchQuery' (Dummy)");
      }

      // Gib das Dummy-Ergebnis zurück
      final dummyResult = JobAnalysisResult(
        relevanceScore: dummyScore,
        matchReasons: dummyMatches.isNotEmpty ? dummyMatches : ["Keine spezifischen Matches (Dummy)"],
        mismatchReasons: dummyMismatches.isNotEmpty ? dummyMismatches : ["Keine spezifischen Mismatches (Dummy)"],
      );

      // Speichere im Cache
      _analysisCache[cacheKey] = dummyResult;
      return dummyResult;

    } catch (e) {
      debugPrint("Fehler bei der Job-Analyse (Service): $e");
      // Gib ein Standard-Fehlerergebnis zurück
    return JobAnalysisResult(
        relevanceScore: 0.0,
        matchReasons: [],
        mismatchReasons: ["Fehler bei der Analyse: ${e.toString()}"],
      );
    }
  }
  
  /// Löscht den Cache für schnellere Ergebnisse
  void clearCache() {
    _analysisCache.clear();
  }
}

/// Klasse zur Repräsentation eines KI-Analyseergebnisses
class JobAnalysisResult {
  /// Relevanz-Score zwischen 0.0 und 1.0 (höher = relevanter)
  final double relevanceScore;
  
  /// Liste der Gründe, warum der Job passt
  final List<String> matchReasons;
  
  /// Liste der Gründe, warum der Job nicht optimal passt
  final List<String> mismatchReasons;
  
  /// Konstruktor
  JobAnalysisResult({
    required this.relevanceScore,
    required this.matchReasons,
    required this.mismatchReasons,
  });
  
  /// Gibt zusammengefasste Match-Information zurück
  String get matchSummary {
    if (matchReasons.isEmpty) {
      return "Keine Übereinstimmungen gefunden";
    }
    return matchReasons.join(". ");
  }
  
  /// Gibt zusammengefasste Mismatch-Information zurück
  String get mismatchSummary {
    if (mismatchReasons.isEmpty) {
      return "";
    }
    return mismatchReasons.join(". ");
  }
} 
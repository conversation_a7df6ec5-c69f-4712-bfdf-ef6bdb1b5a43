import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service für Offline-Caching kritischer Daten
/// Speichert wichtige Daten lokal für Offline-Verfügbarkeit
class OfflineCacheService {
  static final OfflineCacheService _instance = OfflineCacheService._internal();
  final Logger _log = Logger();

  factory OfflineCacheService() => _instance;
  OfflineCacheService._internal();

  // Cache-Schlüssel
  static const String _userProfileCacheKey = 'cached_user_profile';
  static const String _favoritesCacheKey = 'cached_favorites';
  static const String _applicationCounterCacheKey =
      'cached_application_counter';
  static const String _subscriptionCacheKey = 'cached_subscription';
  static const String _jobSearchResultsCacheKey = 'cached_job_search_results';

  // Timestamp-Schlüssel für Cache-Validierung
  static const String _userProfileTimestampKey =
      'cached_user_profile_timestamp';
  static const String _favoritesTimestampKey = 'cached_favorites_timestamp';
  static const String _applicationCounterTimestampKey =
      'cached_application_counter_timestamp';
  static const String _subscriptionTimestampKey =
      'cached_subscription_timestamp';
  static const String _jobSearchResultsTimestampKey =
      'cached_job_search_results_timestamp';

  // Cache-Gültigkeitsdauer (in Minuten) - Erweitert für bessere Offline-Unterstützung
  static const int _userProfileCacheValidityMinutes = 120; // 2 Stunden
  static const int _favoritesCacheValidityMinutes = 60; // 1 Stunde
  static const int _applicationCounterCacheValidityMinutes = 120; // 2 Stunden
  static const int _subscriptionCacheValidityMinutes = 240; // 4 Stunden
  static const int _jobSearchResultsCacheValidityMinutes = 30; // 30 Minuten

  /// Speichert User-Profile im Cache
  Future<bool> cacheUserProfile(Map<String, dynamic> profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile);
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_userProfileCacheKey, profileJson);
      await prefs.setInt(_userProfileTimestampKey, timestamp);

      _log.i('✅ User-Profile erfolgreich gecacht: ${profile['id']}');
      return true;
    } catch (e) {
      _log.e('❌ Fehler beim Cachen des User-Profiles: $e');
      return false;
    }
  }

  /// Lädt User-Profile aus dem Cache
  Future<Map<String, dynamic>?> getCachedUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Prüfe Cache-Gültigkeit
      if (!await _isCacheValid(
        _userProfileTimestampKey,
        _userProfileCacheValidityMinutes,
      )) {
        _log.d('🕒 User-Profile Cache ist abgelaufen');
        return null;
      }

      final profileJson = prefs.getString(_userProfileCacheKey);
      if (profileJson == null) {
        _log.d('📭 Kein User-Profile im Cache gefunden');
        return null;
      }

      final profile = jsonDecode(profileJson) as Map<String, dynamic>;
      _log.i('✅ User-Profile aus Cache geladen: ${profile['id']}');
      return profile;
    } catch (e) {
      _log.e('❌ Fehler beim Laden des User-Profiles aus Cache: $e');
      return null;
    }
  }

  /// Speichert Favoriten im Cache
  Future<bool> cacheFavorites(
    String userId,
    List<Map<String, dynamic>> favorites,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = jsonEncode({
        'user_id': userId,
        'favorites': favorites,
      });
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_favoritesCacheKey, favoritesJson);
      await prefs.setInt(_favoritesTimestampKey, timestamp);

      _log.i(
        '✅ Favoriten erfolgreich gecacht: ${favorites.length} Favoriten für User $userId',
      );
      return true;
    } catch (e) {
      _log.e('❌ Fehler beim Cachen der Favoriten: $e');
      return false;
    }
  }

  /// Lädt Favoriten aus dem Cache
  Future<List<Map<String, dynamic>>?> getCachedFavorites(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Prüfe Cache-Gültigkeit
      if (!await _isCacheValid(
        _favoritesTimestampKey,
        _favoritesCacheValidityMinutes,
      )) {
        _log.d('🕒 Favoriten Cache ist abgelaufen');
        return null;
      }

      final favoritesJson = prefs.getString(_favoritesCacheKey);
      if (favoritesJson == null) {
        _log.d('📭 Keine Favoriten im Cache gefunden');
        return null;
      }

      final data = jsonDecode(favoritesJson) as Map<String, dynamic>;

      // Prüfe ob Cache für den richtigen User ist
      if (data['user_id'] != userId) {
        _log.d('👤 Cache ist für anderen User, lösche Cache');
        await _clearFavoritesCache();
        return null;
      }

      final favorites =
          (data['favorites'] as List).cast<Map<String, dynamic>>();
      _log.i(
        '✅ Favoriten aus Cache geladen: ${favorites.length} Favoriten für User $userId',
      );
      return favorites;
    } catch (e) {
      _log.e('❌ Fehler beim Laden der Favoriten aus Cache: $e');
      return null;
    }
  }

  /// Speichert Bewerbungszähler im Cache (nur valide Serverwerte)
  Future<bool> cacheApplicationCounter(
    String userId,
    Map<String, dynamic> counter,
  ) async {
    try {
      // Nicht cachen, wenn offensichtliche Fehlerstruktur vorliegt
      if (counter.containsKey('error') || counter['success'] == false) {
        _log.w(
          '⚠️ Überspringe Caching des Bewerbungszählers wegen Fehlerpayload: $counter',
        );
        return false;
      }

      // Minimal-Validierung auf erwartete Felder
      final hasNumbers =
          counter.containsKey('remaining') && counter.containsKey('total');
      if (!hasNumbers) {
        _log.w('⚠️ Überspringe Caching: erwartete Felder fehlen: $counter');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final counterJson = jsonEncode({'user_id': userId, 'counter': counter});
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_applicationCounterCacheKey, counterJson);
      await prefs.setInt(_applicationCounterTimestampKey, timestamp);

      _log.i(
        '✅ Bewerbungszähler erfolgreich gecacht: $counter für User $userId',
      );
      return true;
    } catch (e) {
      _log.e('❌ Fehler beim Cachen des Bewerbungszählers: $e');
      return false;
    }
  }

  /// Lädt Bewerbungszähler aus dem Cache
  Future<Map<String, dynamic>?> getCachedApplicationCounter(
    String userId, {
    bool allowExpired = false, // Erlaube abgelaufenen Cache bei Netzwerkfehlern
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Prüfe Cache-Gültigkeit (außer wenn abgelaufener Cache erlaubt ist)
      if (!allowExpired &&
          !await _isCacheValid(
            _applicationCounterTimestampKey,
            _applicationCounterCacheValidityMinutes,
          )) {
        _log.d('🕒 Bewerbungszähler Cache ist abgelaufen');
        return null;
      } else if (allowExpired) {
        _log.d(
          '⚠️ Verwende möglicherweise abgelaufenen Cache (Netzwerkfehler)',
        );
      }

      final counterJson = prefs.getString(_applicationCounterCacheKey);
      if (counterJson == null) {
        _log.d('📭 Kein Bewerbungszähler im Cache gefunden');
        return null;
      }

      final data = jsonDecode(counterJson) as Map<String, dynamic>;

      // Prüfe ob Cache für den richtigen User ist
      if (data['user_id'] != userId) {
        _log.d('👤 Cache ist für anderen User, lösche Cache');
        await _clearApplicationCounterCache();
        return null;
      }

      final counter = data['counter'] as Map<String, dynamic>;
      _log.i('✅ Bewerbungszähler aus Cache geladen: $counter für User $userId');
      return counter;
    } catch (e) {
      _log.e('❌ Fehler beim Laden des Bewerbungszählers aus Cache: $e');
      return null;
    }
  }

  /// Speichert Subscription-Daten im Cache
  Future<bool> cacheSubscription(
    String userId,
    Map<String, dynamic> subscription,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscriptionJson = jsonEncode({
        'user_id': userId,
        'subscription': subscription,
      });
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_subscriptionCacheKey, subscriptionJson);
      await prefs.setInt(_subscriptionTimestampKey, timestamp);

      _log.i(
        '✅ Subscription erfolgreich gecacht: ${subscription['plan_type']} für User $userId',
      );
      return true;
    } catch (e) {
      _log.e('❌ Fehler beim Cachen der Subscription: $e');
      return false;
    }
  }

  /// Lädt Subscription-Daten aus dem Cache
  Future<Map<String, dynamic>?> getCachedSubscription(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Prüfe Cache-Gültigkeit
      if (!await _isCacheValid(
        _subscriptionTimestampKey,
        _subscriptionCacheValidityMinutes,
      )) {
        _log.d('🕒 Subscription Cache ist abgelaufen');
        return null;
      }

      final subscriptionJson = prefs.getString(_subscriptionCacheKey);
      if (subscriptionJson == null) {
        _log.d('📭 Keine Subscription im Cache gefunden');
        return null;
      }

      final data = jsonDecode(subscriptionJson) as Map<String, dynamic>;

      // Prüfe ob Cache für den richtigen User ist
      if (data['user_id'] != userId) {
        _log.d('👤 Cache ist für anderen User, lösche Cache');
        await _clearSubscriptionCache();
        return null;
      }

      final subscription = data['subscription'] as Map<String, dynamic>;
      _log.i(
        '✅ Subscription aus Cache geladen: ${subscription['plan_type']} für User $userId',
      );
      return subscription;
    } catch (e) {
      _log.e('❌ Fehler beim Laden der Subscription aus Cache: $e');
      return null;
    }
  }

  /// Prüft ob ein Cache noch gültig ist
  Future<bool> _isCacheValid(String timestampKey, int validityMinutes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(timestampKey);

      if (timestamp == null) return false;

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime).inMinutes;

      return difference < validityMinutes;
    } catch (e) {
      _log.e('❌ Fehler beim Prüfen der Cache-Gültigkeit: $e');
      return false;
    }
  }

  /// Löscht alle Caches für einen User
  Future<void> clearAllCaches() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.remove(_userProfileCacheKey);
      await prefs.remove(_userProfileTimestampKey);
      await prefs.remove(_favoritesCacheKey);
      await prefs.remove(_favoritesTimestampKey);
      await prefs.remove(_applicationCounterCacheKey);
      await prefs.remove(_applicationCounterTimestampKey);
      await prefs.remove(_subscriptionCacheKey);
      await prefs.remove(_subscriptionTimestampKey);

      _log.i('🧹 Alle Caches erfolgreich gelöscht');
    } catch (e) {
      _log.e('❌ Fehler beim Löschen der Caches: $e');
    }
  }

  /// Löscht Favoriten-Cache
  Future<void> _clearFavoritesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesCacheKey);
      await prefs.remove(_favoritesTimestampKey);
    } catch (e) {
      _log.e('❌ Fehler beim Löschen des Favoriten-Cache: $e');
    }
  }

  /// Löscht Bewerbungszähler-Cache
  Future<void> _clearApplicationCounterCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_applicationCounterCacheKey);
      await prefs.remove(_applicationCounterTimestampKey);
    } catch (e) {
      _log.e('❌ Fehler beim Löschen des Bewerbungszähler-Cache: $e');
    }
  }

  /// Löscht Subscription-Cache
  Future<void> _clearSubscriptionCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_subscriptionCacheKey);
      await prefs.remove(_subscriptionTimestampKey);
    } catch (e) {
      _log.e('❌ Fehler beim Löschen des Subscription-Cache: $e');
    }
  }

  /// Gibt Cache-Statistiken zurück
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'user_profile_cached': prefs.containsKey(_userProfileCacheKey),
        'user_profile_valid': await _isCacheValid(
          _userProfileTimestampKey,
          _userProfileCacheValidityMinutes,
        ),
        'favorites_cached': prefs.containsKey(_favoritesCacheKey),
        'favorites_valid': await _isCacheValid(
          _favoritesTimestampKey,
          _favoritesCacheValidityMinutes,
        ),
        'application_counter_cached': prefs.containsKey(
          _applicationCounterCacheKey,
        ),
        'application_counter_valid': await _isCacheValid(
          _applicationCounterTimestampKey,
          _applicationCounterCacheValidityMinutes,
        ),
        'subscription_cached': prefs.containsKey(_subscriptionCacheKey),
        'subscription_valid': await _isCacheValid(
          _subscriptionTimestampKey,
          _subscriptionCacheValidityMinutes,
        ),
      };
    } catch (e) {
      _log.e('❌ Fehler beim Abrufen der Cache-Statistiken: $e');
      return {};
    }
  }
}

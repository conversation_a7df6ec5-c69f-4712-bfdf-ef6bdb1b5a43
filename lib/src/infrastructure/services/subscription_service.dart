import 'dart:async';
import 'dart:io';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';
import '../../application/providers/user_profile_provider.dart';
import '../../domain/models/subscription.dart';

/// Provider für den SubscriptionService
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  final service = SubscriptionService(
    InAppPurchase.instance,
    supabaseClient,
    ref,
  );
  ref.onDispose(() {
    service.dispose();
  });
  return service;
});

/// Provider für den Supabase-Client
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

/// Service zur Verwaltung von In-App-Käufen und Abonnements
class SubscriptionService {
  final InAppPurchase _iap;
  final SupabaseClient _supabase;
  final Ref _ref;
  final _log = getLogger('SubscriptionService');

  // Produkt-IDs für In-App-Käufe (nur monatliche Abonnements verfügbar)
  // Nur Pro/Unlimited kostenpflichtige Abos (Free ist kostenlos)
  final String _proMonthlyId =
      Platform.isAndroid ? 'pro_subscription' : 'pro_subscription';
  final String _unlimitedMonthlyId =
      Platform.isAndroid ? 'unlimited_subscription' : 'unlimited_subscription';

  // Status und Streams
  bool _isAvailable = false;
  bool _isPurchasePending = false;
  List<ProductDetails> _products = [];
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  // Getter für öffentliche Eigenschaften
  bool get isAvailable => _isAvailable;
  bool get isPurchasePending => _isPurchasePending;
  List<ProductDetails> get products => _products;

  /// Konstruktor mit Initialisierung
  SubscriptionService(this._iap, this._supabase, this._ref) {
    _initialize();
  }

  /// Initialisiert den Service
  Future<void> _initialize() async {
    _log.i('Initialisiere SubscriptionService');

    // Prüfen, ob In-App-Käufe verfügbar sind
    _isAvailable = await _iap.isAvailable();

    if (!_isAvailable) {
      _log.w('In-App-Käufe sind nicht verfügbar');
      return;
    }

    // Purchase-Updates abonnieren
    _subscription = _iap.purchaseStream.listen(
      _handlePurchaseUpdate,
      onDone: () {
        _subscription?.cancel();
      },
      onError: (error) {
        _log.e('Fehler im Kauf-Stream: $error');
      },
    );

    // Produkte laden
    await _loadProducts();

    // Bestehende Käufe wiederherstellen
    if (Platform.isIOS) {
      await _restorePurchases();
    }

    // Status abrufen
    await checkSubscriptionStatus();
  }

  /// Lädt die verfügbaren Produkte
  Future<void> _loadProducts() async {
    try {
      final productIds = <String>{_proMonthlyId, _unlimitedMonthlyId};

      final response = await _iap.queryProductDetails(productIds);

      if (response.notFoundIDs.isNotEmpty) {
        _log.e(
          'Fehler beim Laden der Produkte: Nicht gefundene IDs: ${response.notFoundIDs}',
        );
        return;
      }

      _products = response.productDetails;

      if (_products.isEmpty) {
        _log.w('Keine Produkte gefunden. Produktids: $productIds');
      } else {
        _log.i('${_products.length} Produkte geladen');
        for (final product in _products) {
          _log.i(
            'Produkt: ${product.id} - ${product.title} - ${product.price}',
          );
        }
      }
    } catch (e) {
      _log.e('Fehler beim Laden der Produkte: $e');
    }
  }

  /// Kauft ein Premium-Abonnement (nur monatlich verfügbar)
  /// planType: 'pro' oder 'unlimited' (Free ist kostenlos)
  Future<bool> purchasePremiumSubscription({String planType = 'pro'}) async {
    if (!_isAvailable) {
      _log.e('In-App-Käufe nicht verfügbar');
      return false;
    }

    if (_isPurchasePending) {
      _log.w('Es läuft bereits ein Kaufvorgang');
      return false;
    }

    _isPurchasePending = true;

    try {
      // Wähle die richtige Produkt-ID basierend auf dem Plan-Typ
      String productId;
      switch (planType.toLowerCase()) {
        case 'unlimited':
          productId = _unlimitedMonthlyId;
          break;
        case 'pro':
        default:
          productId = _proMonthlyId;
          break;
      }
      final product = _findProductById(productId);

      if (product == null) {
        _log.e('Produkt $productId nicht gefunden');
        _isPurchasePending = false;
        return false;
      }

      _log.i('Starte Kauf von ${product.id}');

      // Kauf starten
      final purchaseParam = PurchaseParam(
        productDetails: product,
        applicationUserName: null,
      );

      // Abonnement kaufen (korrekte Methode für Subscriptions)
      if (!await _iap.buyNonConsumable(purchaseParam: purchaseParam)) {
        _log.e('Kauf konnte nicht gestartet werden');
        _isPurchasePending = false;
        return false;
      }

      _log.i('Kauf gestartet, warte auf Bestätigung...');
      return true;
    } catch (e) {
      _log.e('Fehler beim Kauf: $e');
      _isPurchasePending = false;
      return false;
    }
  }

  /// Wiederherstellt Käufe (besonders für iOS wichtig)
  Future<void> _restorePurchases() async {
    _log.i('Stelle Käufe wieder her');

    try {
      await _iap.restorePurchases();
    } catch (e) {
      _log.e('Fehler beim Wiederherstellen der Käufe: $e');
    }
  }

  /// Verarbeitet Kauf-Updates
  Future<void> _handlePurchaseUpdate(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    _log.i('Kauf-Updates empfangen: ${purchaseDetailsList.length}');

    for (final purchaseDetails in purchaseDetailsList) {
      await _processPurchase(purchaseDetails);
    }
  }

  /// Prüft auf verfügbare In-app Promotions (für Google Play Promo Codes)
  Future<void> checkForPromotions() async {
    try {
      _log.i('Prüfe auf verfügbare In-app Promotions...');

      // Prüfe auf verfügbare Promotions für alle Subscription-Produkte
      final productIds = {_proMonthlyId, _unlimitedMonthlyId};

      for (final productId in productIds) {
        final product = _findProductById(productId);
        if (product != null) {
          _log.i('Promotion-Check für Produkt: $productId');
          // Hier würde die tatsächliche Promotion-Prüfung stattfinden
          // Das wird automatisch von Google Play Billing Library gehandhabt
        }
      }
    } catch (e) {
      _log.w('Fehler beim Prüfen auf Promotions: $e');
    }
  }

  /// Löst einen Promo Code ein (neue Server-seitige Validierung)
  Future<bool> redeemPromoCode(String promoCode) async {
    try {
      _log.i('Versuche Promo Code einzulösen: $promoCode');

      // Validiere Promo Code Format
      if (promoCode.isEmpty || promoCode.length < 3) {
        _log.w('Ungültiger Promo Code: zu kurz');
        return false;
      }

      // Prüfe ob User berechtigt ist
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        _log.w('Kein angemeldeter User für Promo Code');
        return false;
      }

      // Server-seitige Promo Code Validierung über neue Edge Function
      final response = await _supabase.functions.invoke(
        'validate-promo-code-v2',
        body: {
          'promo_code': promoCode.trim().toUpperCase(),
        },
      );

      if (response.status != 200) {
        _log.w('Promo Code Validierung fehlgeschlagen: ${response.status}');
        return false;
      }

      final responseData = response.data;
      if (responseData == null || responseData['success'] != true) {
        _log.w('Promo Code ungültig: ${responseData?['message'] ?? 'Unbekannter Fehler'}');
        return false;
      }

      _log.i('Promo Code erfolgreich eingelöst: $promoCode');

      // Aktualisiere lokalen User Profile Status
      final redemption = responseData['redemption'];
      if (redemption != null && redemption['expires_at'] != null) {
        await _updateUserProfileSubscriptionStatus(
          isPremium: true,
          expiryDate: DateTime.parse(redemption['expires_at']),
        );
      }

      return true;
    } catch (e) {
      _log.e('Fehler beim Einlösen des Promo Codes: $e');
      return false;
    }
  }

  /// Verarbeitet einen einzelnen Kauf
  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    _log.i(
      'Verarbeite Kauf: ${purchaseDetails.productID} (Status: ${purchaseDetails.status})',
    );

    if (purchaseDetails.status == PurchaseStatus.pending) {
      _log.i('Kauf ist noch in Bearbeitung');
      _isPurchasePending = true;
    } else {
      _isPurchasePending = false;

      if (purchaseDetails.status == PurchaseStatus.error) {
        _log.e(
          'Kaufvorgang fehlgeschlagen: ${purchaseDetails.error?.message}',
        );
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        await _verifyAndDeliverPurchase(purchaseDetails);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        _log.i('Kauf wurde abgebrochen');
      }

      // Abschluss bestätigen (wichtig für iOS)
      if (purchaseDetails.pendingCompletePurchase) {
        await _iap.completePurchase(purchaseDetails);
      }
    }
  }

  /// Verifiziert und liefert einen Kauf aus
  Future<void> _verifyAndDeliverPurchase(
    PurchaseDetails purchaseDetails,
  ) async {
    final productId = purchaseDetails.productID;
    _log.i('Verifiziere und liefere Kauf aus: $productId');

    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        _log.e(
          'Kauf kann nicht geliefert werden - Benutzer nicht angemeldet',
        );
        return;
      }

      // Server-seitige Verifizierung über Supabase Edge Function
      final verificationResult = await _verifyPurchaseWithServer(
        purchaseDetails: purchaseDetails,
        userId: user.id,
      );

      if (!verificationResult['success']) {
        _log.e(
          'Kaufverifizierung fehlgeschlagen: ${verificationResult['error']}',
        );
        return;
      }

      _log.i('Kauf erfolgreich verifiziert');

      // Abonnement-Daten aus der Verifizierung extrahieren
      final subscriptionData = verificationResult['subscription'];
      if (subscriptionData != null) {
        final expiryDate = DateTime.parse(subscriptionData['expires_at']);

        // Lokales UserProfile aktualisieren
        await _updateUserProfileSubscriptionStatus(
          isPremium: true,
          expiryDate: expiryDate,
        );

        _log.i(
          'UserProfile aktualisiert, Premium ist jetzt aktiv bis $expiryDate',
        );
      }
    } catch (e) {
      _log.e('Fehler beim Verifizieren/Liefern des Kaufs: $e');
    }
  }

  /// Verifiziert einen Kauf über die Supabase Edge Function
  Future<Map<String, dynamic>> _verifyPurchaseWithServer({
    required PurchaseDetails purchaseDetails,
    required String userId,
  }) async {
    try {
      final platform = Platform.isAndroid ? 'android' : 'ios';
      final receiptData =
          purchaseDetails.verificationData.serverVerificationData;

      // Anfrage an die verify-purchase Edge Function
      final response = await _supabase.functions.invoke(
        'verify-purchase',
        body: {
          'platform': platform,
          'receipt_data': receiptData,
          'product_id': purchaseDetails.productID,
          'user_id': userId,
          'transaction_id': purchaseDetails.purchaseID,
        },
      );

      if (response.status != 200) {
        _log.e('Edge Function Fehler: ${response.status}');
        return {'success': false, 'error': 'Server-Fehler: ${response.status}'};
      }

      final responseData = response.data as Map<String, dynamic>;

      if (responseData['success'] == true) {
        return {'success': true, 'subscription': responseData['subscription']};
      } else {
        return {
          'success': false,
          'error': responseData['error'] ?? 'Unbekannter Fehler',
        };
      }
    } catch (e) {
      _log.e('Fehler bei der Server-Verifizierung: $e');
      return {'success': false, 'error': 'Netzwerk-Fehler: $e'};
    }
  }

  /// Aktualisiert den Abonnementstatus im UserProfile
  Future<void> _updateUserProfileSubscriptionStatus({
    required bool isPremium,
    required DateTime expiryDate,
  }) async {
    try {
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: isPremium,
            premiumExpiryDate: expiryDate,
          );
      _ref.read(premiumFeatureServiceProvider).clearCache(); // Cache leeren
      _log.i(
        'Premium-Status im UserProfile aktualisiert und Cache geleert: isPremium=$isPremium, expiryDate=$expiryDate',
      );
    } catch (e) {
      _log.e('Fehler beim Aktualisieren des Premium-Status: $e');
    }
  }

  /// Prüft und aktualisiert den Abonnementstatus eines Benutzers
  Future<SubscriptionStatus> checkSubscriptionStatus() async {
    _log.i('Prüfe Abonnementstatus');

    final user = _supabase.auth.currentUser;
    if (user == null) {
      _log.w(
        'Kann Abonnementstatus nicht prüfen - Benutzer nicht angemeldet',
      );
      return SubscriptionStatus(isActive: false);
    }

    try {
      // Aktives Abonnement aus Supabase abrufen
      final response =
          await _supabase
              .from('subscriptions')
              .select()
              .eq('user_id', user.id)
              .eq('status', 'active')
              .order('expires_at', ascending: false)
              .limit(1)
              .maybeSingle();

      if (response == null) {
        _log.i('Kein aktives Abonnement gefunden');

        // UserProfile aktualisieren, falls es fälschlicherweise aktiv ist
        // Nur aktualisieren, wenn der Benutzer nicht bereits als Premium markiert ist
        final userProfile = _ref.read(userProfileProvider).valueOrNull;
        if (userProfile?.isPremium == true) {
          await _updateUserProfileSubscriptionStatus(
            isPremium: false,
            expiryDate: DateTime.now(),
          );
        }

        return SubscriptionStatus(isActive: false);
      }

      // Subscription-Objekt aus der Antwort erstellen
      final subscription = Subscription.fromJson(response);

      // Prüfen, ob das Abonnement abgelaufen ist
      final isExpired =
          subscription.expiresAt?.isBefore(DateTime.now()) ?? true;

      if (isExpired) {
        _log.i('Abonnement ist abgelaufen');

        // Abonnement als abgelaufen markieren - nur wenn es nicht bereits expired ist
        if (subscription.status == 'active') {
          await _supabase
              .from('subscriptions')
              .update({'status': 'expired'})
              .eq('id', subscription.id)
              .eq('status', 'active'); // Zusätzliche Bedingung für Sicherheit
        }

        // UserProfile aktualisieren
        await _updateUserProfileSubscriptionStatus(
          isPremium: false,
          expiryDate: DateTime.now(),
        );

        return SubscriptionStatus(isActive: false);
      }

      _log.i(
        'Aktives Abonnement gefunden: gültig bis ${subscription.expiresAt}',
      );

      // UserProfile aktualisieren, um sicherzustellen, dass es synchron ist
      await _updateUserProfileSubscriptionStatus(
        isPremium: true,
        expiryDate:
            subscription.expiresAt ??
            DateTime.now().add(const Duration(days: 30)),
      );

      return SubscriptionStatus(
        isActive: true,
        expiresAt: subscription.expiresAt,
        subscription: subscription,
      );
    } catch (e) {
      _log.e('Fehler beim Prüfen des Abonnementstatus: $e');
      return SubscriptionStatus(isActive: false, error: e.toString());
    }
  }

  /// Sucht ein Produkt anhand der ID
  ProductDetails? _findProductById(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Gibt das aktuelle Abonnement zurück
  Future<Subscription?> getCurrentSubscription() async {
    _log.i('Hole aktuelles Abonnement');

    final user = _supabase.auth.currentUser;
    if (user == null) {
      _log.w('Kann Abonnement nicht abrufen - Benutzer nicht angemeldet');
      return null;
    }

    try {
      final response =
          await _supabase
              .from('subscriptions')
              .select()
              .eq('user_id', user.id)
              .eq('status', 'active')
              .order('expires_at', ascending: false)
              .limit(1)
              .maybeSingle();

      if (response == null) {
        _log.i('Kein aktives Abonnement gefunden');
        return null;
      }

      final subscription = Subscription.fromJson(response);

      // Prüfen, ob das Abonnement abgelaufen ist
      final isExpired =
          subscription.expiresAt?.isBefore(DateTime.now()) ?? true;

      if (isExpired) {
        _log.i('Abonnement ist abgelaufen');
        return null;
      }

      return subscription;
    } catch (e) {
      _log.e('Fehler beim Abrufen des aktuellen Abonnements: $e');
      return null;
    }
  }

  /// Prüft, ob der Benutzer gültigen Werbezugang hat
  Future<bool> hasValidAdAccessForUser(String userId, String feature) async {
    _log.i('Prüfe Werbezugang für Benutzer $userId und Feature $feature');

    try {
      // Hier könnte eine Logik implementiert werden, die prüft,
      // ob der Benutzer durch das Ansehen von Werbung temporären Zugang hat
      // Für jetzt geben wir false zurück, da diese Funktion noch nicht implementiert ist

      // TODO: Implementiere Logik für temporären Werbezugang
      // - Prüfe, ob Benutzer kürzlich Werbung für dieses Feature angesehen hat
      // - Prüfe Zeitstempel und Gültigkeitsdauer

      return false;
    } catch (e) {
      _log.e('Fehler beim Prüfen des Werbezugangs: $e');
      return false;
    }
  }

  /// Prüft, ob der Benutzer Premium-Zugang hat
  Future<bool> hasPremiumAccess() async {
    try {
      final status = await checkSubscriptionStatus();
      return status.isActive;
    } catch (e) {
      _log.e('Fehler beim Prüfen des Premium-Zugangs: $e');
      return false;
    }
  }

  /// Gibt den aktuellen Benutzer zurück
  Future<User?> getCurrentUser() async {
    try {
      final user = _supabase.auth.currentUser;
      return user;
    } catch (e) {
      _log.e('Fehler beim Abrufen des aktuellen Benutzers: $e');
      return null;
    }
  }

  /// Gewährt temporären Zugriff auf ein Feature nach dem Ansehen einer Werbung
  Future<Map<String, dynamic>?> grantAdBasedAccess({
    required String userId,
    required String feature,
  }) async {
    try {
      final expiryDate = DateTime.now().add(const Duration(hours: 24));

      final response =
          await _supabase
              .from('ad_based_access')
              .insert({
                'user_id': userId,
                'feature': feature,
                'expires_at': expiryDate.toIso8601String(),
                'created_at': DateTime.now().toIso8601String(),
              })
              .select()
              .single();

      _log.i('Ad-basierter Zugriff gewährt für Feature: $feature');
      return response;
    } catch (e) {
      _log.e('Fehler beim Gewähren des Ad-basierten Zugriffs: $e');
      return null;
    }
  }

  /// Entfernt alle Ressourcen
  void dispose() {
    _log.i('Beende SubscriptionService');
    _subscription?.cancel();
  }
}

/// Status-Objekt für ein Abonnement
class SubscriptionStatus {
  final bool isActive;
  final DateTime? expiresAt;
  final Subscription? subscription;
  final String? error;

  SubscriptionStatus({
    required this.isActive,
    this.expiresAt,
    this.subscription,
    this.error,
  });

  @override
  String toString() {
    return 'SubscriptionStatus(isActive: $isActive, expiresAt: $expiresAt, error: $error)';
  }
}

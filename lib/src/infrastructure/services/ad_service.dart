import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ki_test/src/core/utils/logging.dart';

// AdMob Ad Unit IDs für die App
// Banner-Ad (echte ID): ca-app-pub-5967659832492607/7563701147
// Native-Ad (echte ID): ca-app-pub-5967659832492607/7755789750
// App-Start-Ad: ca-app-pub-5967659832492607/5727589885
// Test-IDs für Entwicklung:
// Android Banner Test: ca-app-pub-3940256099942544/6300978111
// iOS Banner Test: ca-app-pub-3940256099942544/2934735716

// TEMPORÄR: Test-Banner-Ad-ID bis echte ID funktioniert
final String _bannerAdUnitId =
    Platform.isAndroid
        ? 'ca-app-pub-3940256099942544/6300978111' // Test Banner-Ad ID (funktioniert)
        : 'ca-app-pub-3940256099942544/2934735716'; // Test ID für iOS

final String _appOpenAdUnitId =
    Platform.isAndroid
        ? 'ca-app-pub-5967659832492607/5727589885' // Echte App-Start-Ad ID
        : 'ca-app-pub-3940256099942544/1712485313'; // Test ID für iOS

// Native Ads für bessere UI-Integration - echte IDs
final String _nativeAdUnitId =
    Platform.isAndroid
        ? 'ca-app-pub-5967659832492607/7755789750' // Echte Native-Ad ID (gleiche wie Banner-Ad Block)
        : 'ca-app-pub-3940256099942544/3986624511'; // Test ID für iOS

// Rewarded Ads (falls noch benötigt)
final String _rewardedAdUnitId =
    Platform.isAndroid
        ? 'ca-app-pub-3940256099942544/5224354917' // Test ID
        : 'ca-app-pub-3940256099942544/1712485313'; // Test ID

/// Service für das neue Werbungssystem mit Banner-Ads
class AdService {
  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  final log = getLogger('AdService');

  // Banner-Ads für das neue Werbungssystem
  BannerAd? _bannerAd;
  bool _isBannerAdLoaded = false;

  // Native Ads für bessere UI-Integration
  NativeAd? _nativeAd;
  bool _isNativeAdLoaded = false;

  // App-Open-Ad für App-Start
  AppOpenAd? _appOpenAd;
  bool _isAppOpenAdLoaded = false;

  // Rewarded Ad für spezielle Features (falls noch benötigt)
  RewardedAd? _rewardedAd;

  // Flag, ob Werbung aktiviert ist (für Basic-Nutzer)
  bool _adsEnabled = false;

  // Feature-Flag: App-Open-Ad beim App-Start (Standard: aus zur Jank-Reduktion)
  bool appOpenOnStartupEnabled = false;

  void setAppOpenOnStartup(bool enabled) {
    appOpenOnStartupEnabled = enabled;
    log.i('App-Open-On-Startup: ${enabled ? 'ENABLED' : 'DISABLED'}');
  }

  // Initialisiert das Mobile Ads SDK
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }

  // Aktiviert Werbung (für Basic-Nutzer)
  void enableAds() {
    _adsEnabled = true;
    log.i('Werbung aktiviert (Basic-Nutzer)');
  }

  // Deaktiviert Werbung (für Premium-Nutzer)
  void disableAds() {
    _adsEnabled = false;
    log.i('Werbung deaktiviert (Premium-Nutzer)');
    disposeBannerAd();
    disposeNativeAd();
    disposeAppOpenAd();
  }

  // Gibt zurück, ob Werbung aktiviert ist
  bool get areAdsEnabled => _adsEnabled;

  /// Lädt eine Banner-Ad für das neue Werbungssystem
  Future<BannerAd?> loadBannerAd() async {
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert - keine Banner-Ad geladen");
      return null;
    }

    try {
      _bannerAd = BannerAd(
        adUnitId: _bannerAdUnitId,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            log.i('Banner-Ad erfolgreich geladen');
            _isBannerAdLoaded = true;
          },
          onAdFailedToLoad: (ad, error) {
            log.e('Banner-Ad konnte nicht geladen werden: $error');
            ad.dispose();
            _bannerAd = null;
            _isBannerAdLoaded = false;
          },
          onAdOpened: (ad) {
            log.i('Banner-Ad geöffnet');
          },
          onAdClosed: (ad) {
            log.i('Banner-Ad geschlossen');
          },
        ),
      );

      await _bannerAd!.load();
      return _bannerAd;
    } catch (e) {
      log.e('Fehler beim Laden der Banner-Ad: $e');
      return null;
    }
  }

  /// Gibt die aktuelle Banner-Ad zurück (falls geladen)
  BannerAd? getBannerAd() {
    return _isBannerAdLoaded ? _bannerAd : null;
  }

  /// Prüft, ob eine Banner-Ad geladen ist
  bool isBannerAdLoaded() {
    return _isBannerAdLoaded && _bannerAd != null;
  }

  /// Entlädt die Banner-Ad
  void disposeBannerAd() {
    if (_bannerAd != null) {
      _bannerAd!.dispose();
      _bannerAd = null;
      _isBannerAdLoaded = false;
      log.i('Banner-Ad entladen');
    }
  }

  /// Lädt eine Native Ad für bessere UI-Integration
  Future<NativeAd?> loadNativeAd() async {
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert - keine Native Ad geladen");
      return null;
    }

    try {
      final nativeAd = NativeAd(
        adUnitId: _nativeAdUnitId,
        request: const AdRequest(),
        listener: NativeAdListener(
          onAdLoaded: (ad) {
            log.i('Native Ad erfolgreich geladen');
            // Keine Zuweisung zu _nativeAd hier - das wird vom Widget verwaltet
            _isNativeAdLoaded = true;
          },
          onAdFailedToLoad: (ad, error) {
            log.e('Native Ad konnte nicht geladen werden: $error');
            ad.dispose();
            _isNativeAdLoaded = false;
          },
          onAdOpened: (ad) {
            log.i('Native Ad geöffnet');
          },
          onAdClosed: (ad) {
            log.i('Native Ad geschlossen');
          },
          onAdImpression: (ad) {
            log.i('Native Ad Impression registriert');
          },
        ),
        nativeTemplateStyle: NativeTemplateStyle(
          templateType: TemplateType.medium,
          mainBackgroundColor: Colors.white,
          cornerRadius: 10.0,
          callToActionTextStyle: NativeTemplateTextStyle(
            textColor: Colors.white,
            backgroundColor: Colors.blue,
            style: NativeTemplateFontStyle.monospace,
            size: 16.0,
          ),
          primaryTextStyle: NativeTemplateTextStyle(
            textColor: Colors.black,
            backgroundColor: Colors.white,
            style: NativeTemplateFontStyle.bold,
            size: 16.0,
          ),
          secondaryTextStyle: NativeTemplateTextStyle(
            textColor: Colors.grey,
            backgroundColor: Colors.white,
            style: NativeTemplateFontStyle.normal,
            size: 14.0,
          ),
          tertiaryTextStyle: NativeTemplateTextStyle(
            textColor: Colors.grey,
            backgroundColor: Colors.white,
            style: NativeTemplateFontStyle.normal,
            size: 12.0,
          ),
        ),
      );

      await nativeAd.load();
      return nativeAd;
    } catch (e) {
      log.e('Fehler beim Laden der Native Ad: $e');
      return null;
    }
  }

  /// Entlädt die Native Ad
  void disposeNativeAd() {
    _nativeAd?.dispose();
    _nativeAd = null;
    _isNativeAdLoaded = false;
  }

  /// Lädt eine Rewarded Ad (für spezielle Features falls noch benötigt)
  Future<void> loadRewardedAd({
    VoidCallback? onAdLoadedCallback,
    VoidCallback? onAdFailedToLoadCallback,
  }) async {
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert - keine Rewarded Ad geladen");
      onAdFailedToLoadCallback?.call();
      return;
    }

    try {
      await RewardedAd.load(
        adUnitId: _rewardedAdUnitId,
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            log.i('Rewarded ad geladen.');
            _rewardedAd = ad;
            onAdLoadedCallback?.call();
          },
          onAdFailedToLoad: (LoadAdError error) {
            log.e('Rewarded ad konnte nicht geladen werden: $error');
            _rewardedAd = null;
            onAdFailedToLoadCallback?.call();
          },
        ),
      );
    } catch (e) {
      log.e('Fehler beim Laden der Rewarded Ad: $e');
      onAdFailedToLoadCallback?.call();
    }
  }

  /// Zeigt eine Rewarded Ad an (für spezielle Features falls noch benötigt)
  Future<void> showRewardedAd({
    required VoidCallback onUserEarnedReward,
    VoidCallback? onAdDismissed,
    VoidCallback? onAdFailedToShow,
  }) async {
    if (_rewardedAd == null) {
      log.w('Keine Rewarded Ad geladen');
      onAdFailedToShow?.call();
      return;
    }

    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        log.i('Rewarded ad geschlossen');
        ad.dispose();
        _rewardedAd = null;
        onAdDismissed?.call();
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        log.e('Fehler beim Anzeigen der Rewarded ad: $error');
        ad.dispose();
        _rewardedAd = null;
        onAdFailedToShow?.call();
      },
    );

    await _rewardedAd!.show(
      onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        log.i('Belohnung verdient: ${reward.amount} ${reward.type}');
        onUserEarnedReward();
      },
    );
  }

  /// Lädt eine App-Open-Ad für App-Start
  Future<void> loadAppOpenAd() async {
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert - keine App-Open-Ad geladen");
      return;
    }

    try {
      await AppOpenAd.load(
        adUnitId: _appOpenAdUnitId,
        request: const AdRequest(),
        adLoadCallback: AppOpenAdLoadCallback(
          onAdLoaded: (ad) {
            log.i('App-Open-Ad erfolgreich geladen');
            _appOpenAd = ad;
            _isAppOpenAdLoaded = true;
          },
          onAdFailedToLoad: (error) {
            log.e('App-Open-Ad konnte nicht geladen werden: $error');
            _appOpenAd = null;
            _isAppOpenAdLoaded = false;
          },
        ),
      );
    } catch (e) {
      log.e('Fehler beim Laden der App-Open-Ad: $e');
    }
  }

  /// Zeigt die App-Open-Ad an (falls geladen)
  Future<void> showAppOpenAd({
    VoidCallback? onAdDismissed,
    VoidCallback? onAdFailedToShow,
  }) async {
    if (!_adsEnabled || _appOpenAd == null || !_isAppOpenAdLoaded) {
      log.i("App-Open-Ad nicht verfügbar oder Werbung deaktiviert");
      return;
    }

    _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        log.i('App-Open-Ad geschlossen');
        ad.dispose();
        _appOpenAd = null;
        _isAppOpenAdLoaded = false;
        onAdDismissed?.call();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        log.e('Fehler beim Anzeigen der App-Open-Ad: $error');
        ad.dispose();
        _appOpenAd = null;
        _isAppOpenAdLoaded = false;
        onAdFailedToShow?.call();
      },
    );

    await _appOpenAd!.show();
  }

  /// Entlädt die App-Open-Ad
  void disposeAppOpenAd() {
    _appOpenAd?.dispose();
    _appOpenAd = null;
    _isAppOpenAdLoaded = false;
  }

  /// Entlädt alle Ads
  void dispose() {
    disposeBannerAd();
    disposeNativeAd();
    disposeAppOpenAd();
    _rewardedAd?.dispose();
    _rewardedAd = null;
    log.i('AdService entladen');
  }
}

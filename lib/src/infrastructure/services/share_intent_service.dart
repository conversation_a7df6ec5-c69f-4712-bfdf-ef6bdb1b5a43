import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'dart:async';

import 'web_scraping_service.dart';

/// Service für das Handling von Share-Intents aus anderen Apps
class ShareIntentService {
  static const MethodChannel _channel = MethodChannel('com.einsteinai.app/share_intent');
  final Logger _log = Logger();
  final WebScrapingService _webScrapingService = WebScrapingService();

  // Callback für geteilte URLs
  Function(String url, JobScrapingResult? scrapingResult)? _onUrlShared;

  static ShareIntentService? _instance;

  ShareIntentService._internal();

  factory ShareIntentService() {
    _instance ??= ShareIntentService._internal();
    return _instance!;
  }

  /// Initialisiert den Share-Intent-Service
  void initialize() {
    _log.i('ShareIntentService wird initialisiert...');

    // Setze den MethodCall-Handler für eingehende Share-Intents
    _channel.setMethodCallHandler(_handleMethodCall);

    _log.i('ShareIntentService erfolgreich initialisiert');
  }

  /// Registriert einen Callback für geteilte URLs
  void setOnUrlSharedCallback(Function(String url, JobScrapingResult? scrapingResult) callback) {
    _onUrlShared = callback;
    _log.d('URL-Shared-Callback registriert');
  }

  /// Behandelt eingehende MethodCalls vom nativen Code
  Future<void> _handleMethodCall(MethodCall call) async {
    _log.i('🎯 METHODCALL EMPFANGEN: ${call.method}');
    _log.i('📝 Arguments: ${call.arguments}');

    switch (call.method) {
      case 'onUrlShared':
        final String? sharedUrl = call.arguments as String?;
        if (sharedUrl != null && sharedUrl.isNotEmpty) {
          _log.i('✅ URL GETEILT: $sharedUrl');
          _log.i('🚀 STARTE SOFORTIGE VERARBEITUNG...');
          await _handleSharedTextImmediately(sharedUrl);
        } else {
          _log.w('❌ Leere oder null URL empfangen');
        }
        break;
      default:
        _log.w('⚠️ Unbekannte Methode: ${call.method}');
    }
  }

  /// Behandelt geteilten Text SOFORT
  Future<void> _handleSharedTextImmediately(String sharedText) async {
    _log.i('🔥 SOFORTIGE VERARBEITUNG: $sharedText');

    // Prüfe ob der Text eine URL enthält
    final urlRegex = RegExp(r'https?://[^\s]+');
    final urlMatch = urlRegex.firstMatch(sharedText);

    if (urlMatch != null) {
      final url = urlMatch.group(0)!;
      _log.i('✅ URL EXTRAHIERT: $url');

      // Prüfe ob es eine potenzielle Job-URL ist
      if (isPotentialJobUrl(url)) {
        _log.i('🎯 JOB-URL ERKANNT - STARTE VERARBEITUNG');
        await _handleSharedUrlImmediately(url);
      } else {
        _log.w('⚠️ KEINE JOB-URL: $url');
        // Auch bei nicht-Job-URLs zur Manual Input Seite navigieren
        await _navigateToManualInputImmediately(url, null);
      }
    } else {
      _log.w('❌ KEINE URL GEFUNDEN: $sharedText');
    }
  }

  /// Legacy-Methode für Kompatibilität
  Future<void> _handleSharedText(String sharedText) async {
    await _handleSharedTextImmediately(sharedText);
  }

  /// Behandelt eine geteilte URL SOFORT
  Future<void> _handleSharedUrlImmediately(String url) async {
    try {
      _log.i('🚀 SOFORTIGE URL-VERARBEITUNG: $url');

      // SOFORT zur Manual Input Seite navigieren
      await _navigateToManualInputImmediately(url, null);

      // Web-Scraping im Hintergrund starten (nicht blockierend)
      _startBackgroundScraping(url);

    } catch (e) {
      _log.e('❌ FEHLER bei sofortiger URL-Verarbeitung: $e');
      _showErrorNotification();
    }
  }

  /// Legacy-Methode für Kompatibilität
  Future<void> _handleSharedUrl(String url) async {
    await _handleSharedUrlImmediately(url);
  }

  /// Startet Web-Scraping im Hintergrund
  void _startBackgroundScraping(String url) async {
    try {
      _log.i('🔄 HINTERGRUND-SCRAPING GESTARTET: $url');
      final scrapingResult = await _webScrapingService.scrapeJobFromUrl(url);
      _log.i('✅ SCRAPING ERFOLGREICH: ${scrapingResult.toString()}');

      // TODO: Scraping-Ergebnisse an die Manual Input Seite senden
      // Dies könnte über einen Stream oder Event-Bus erfolgen

    } catch (e) {
      _log.e('❌ HINTERGRUND-SCRAPING FEHLGESCHLAGEN: $e');
      // Scraping-Fehler sind nicht kritisch
    }
  }

  /// Zeigt eine Benachrichtigung während der Verarbeitung
  void _showProcessingNotification() {
    // TODO: Implementiere eine dezente Benachrichtigung
    _log.d('Verarbeitung der geteilten URL läuft...');
  }

  /// Zeigt eine Fehler-Benachrichtigung
  void _showErrorNotification() {
    // TODO: Implementiere Fehler-Benachrichtigung
    _log.e('Fehler beim Verarbeiten der geteilten URL');
  }

  /// Navigiert SOFORT zur Manual Job Input Seite
  Future<void> _navigateToManualInputImmediately(String url, JobScrapingResult? scrapingResult) async {
    _log.i('🚀 SOFORTIGE NAVIGATION ZU MANUAL INPUT');

    // Rufe den Callback auf für sofortige Navigation
    if (_onUrlShared != null) {
      _log.i('✅ CALLBACK VERFÜGBAR - NAVIGIERE SOFORT');
      _onUrlShared!(url, scrapingResult);
    } else {
      _log.w('❌ KEIN CALLBACK REGISTRIERT');
    }
  }

  /// Navigiert zur Manual Job Input Seite mit vorausgefüllten Daten
  static void navigateToManualJobInputWithData({
    required BuildContext context,
    required String url,
    JobScrapingResult? scrapingResult,
  }) {
    final Logger log = Logger();
    log.i('🎯 NAVIGIERE ZU MANUAL JOB INPUT MIT DATEN...');

    // Erstelle die Daten für die Navigation
    final Map<String, dynamic> extraData = {
      'sourceUrl': url,
      'prefillData': scrapingResult != null ? {
        'jobTitle': scrapingResult.jobTitle,
        'companyName': scrapingResult.companyName,
        'jobDescription': scrapingResult.jobDescription,
        'additionalInfo': scrapingResult.additionalInfo,
      } : null,
    };

    log.i('📝 EXTRA DATA: $extraData');

    // Navigiere zur Manual Job Input Seite
    context.go('/manual-job-input', extra: extraData);

    log.i('✅ NAVIGATION ZU MANUAL JOB INPUT GESTARTET');
  }

  /// Prüft ob eine URL eine potenzielle Stellenanzeige ist
  static bool isPotentialJobUrl(String url) {
    final jobKeywords = [
      'job', 'jobs', 'career', 'careers', 'stelle', 'stellen', 
      'stellenanzeige', 'stellenanzeigen', 'bewerbung', 'position',
      'vacancy', 'vacancies', 'employment', 'hiring', 'recruit',
      'xing', 'linkedin', 'stepstone', 'indeed', 'monster',
      'jobware', 'stellenwerk', 'kimeta', 'jobscout24'
    ];
    
    final lowerUrl = url.toLowerCase();
    return jobKeywords.any((keyword) => lowerUrl.contains(keyword));
  }

  /// Bereinigt eine URL für bessere Verarbeitung
  static String cleanUrl(String url) {
    // Entferne Tracking-Parameter und andere unnötige Parameter
    try {
      final uri = Uri.parse(url);
      final cleanedUri = Uri(
        scheme: uri.scheme,
        host: uri.host,
        port: uri.port,
        path: uri.path,
        // Behalte nur wichtige Query-Parameter
        queryParameters: uri.queryParameters.entries
            .where((entry) => _isImportantQueryParameter(entry.key))
            .fold<Map<String, String>>({}, (map, entry) {
              map[entry.key] = entry.value;
              return map;
            }),
      );
      return cleanedUri.toString();
    } catch (e) {
      // Falls URL-Parsing fehlschlägt, gib die ursprüngliche URL zurück
      return url;
    }
  }

  /// Prüft ob ein Query-Parameter wichtig ist
  static bool _isImportantQueryParameter(String key) {
    final importantParams = ['id', 'jobid', 'job_id', 'ref', 'refnr', 'position'];
    return importantParams.contains(key.toLowerCase());
  }

  /// Dispose-Methode für Cleanup
  void dispose() {
    _onUrlShared = null;
    _log.d('ShareIntentService disposed');
  }
}

/// Globale Instanz des ShareIntentService
final shareIntentService = ShareIntentService();

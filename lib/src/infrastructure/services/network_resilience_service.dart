import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:ki_test/src/core/network/supabase_retry_client.dart';
import 'package:ki_test/src/infrastructure/services/offline_cache_service.dart';

/// Service für netzwerk-resiliente Operationen
/// Stellt robuste Supabase-Operationen mit automatischer Retry-Logic bereit
class NetworkResilienceService {
  final SupabaseRetryClient _retryClient;
  final OfflineCacheService _cacheService;
  final Logger _log = Logger();

  NetworkResilienceService(this._retryClient)
    : _cacheService = OfflineCacheService();

  /// Robuste Bewerbungszähler-Abfrage mit Retry-Logic und Offline-Cache
  Future<Map<String, dynamic>?> getRemainingApplicationsResilient(
    String userId,
  ) async {
    try {
      final result = await _retryClient.safeRpc<Map<String, dynamic>>(
        'get_remaining_applications_safe',
        'Bewerbungszähler abrufen (safe)',
        params: {'p_user_id': userId},
      );

      // Cache das Ergebnis für Offline-Verfügbarkeit
      await _cacheService.cacheApplicationCounter(userId, result);
      _log.i('✅ Bewerbungszähler erfolgreich abgerufen und gecacht: $result');
      return result;
    } catch (e) {
      _log.w('⚠️ Bewerbungszähler-Abfrage fehlgeschlagen, versuche Cache: $e');
    }

    // Fallback: Versuche Cache zu laden (auch abgelaufenen bei Netzwerkfehlern)
    try {
      final cachedResult = await _cacheService.getCachedApplicationCounter(
        userId,
        allowExpired: true, // Erlaube abgelaufenen Cache bei Netzwerkfehlern
      );
      if (cachedResult != null) {
        _log.i('📱 Bewerbungszähler aus Offline-Cache geladen: $cachedResult');
        return cachedResult;
      }
    } catch (e) {
      _log.e('❌ Auch Cache-Laden fehlgeschlagen: $e');
    }

    // Letzter Fallback: konservativ und serverkonform (0 statt 10)
    _log.w(
      '⚠️ Kein Cache verfügbar, verwende konservativen Fallback (0 Bewerbungen)',
    );
    return {
      'total': 0,
      'remaining': 0,
      'plan_type': 'free',
      'unlimited': false,
    };
  }

  /// Robuste Favoriten-Synchronisation mit Retry-Logic
  Future<bool> syncFavoritesResilient(
    String userId,
    List<String> favoriteIds,
  ) async {
    try {
      // Lösche alle bestehenden Favoriten
      await _retryClient.safeDelete('user_favorites', 'Favoriten löschen', {
        'user_id': userId,
      });

      // Füge neue Favoriten hinzu
      if (favoriteIds.isNotEmpty) {
        final favoritesToInsert =
            favoriteIds
                .map(
                  (jobId) => {
                    'user_id': userId,
                    'job_id': jobId,
                    'created_at': DateTime.now().toIso8601String(),
                  },
                )
                .toList();

        await _retryClient.safeInsert(
          'user_favorites',
          'Favoriten hinzufügen',
          favoritesToInsert.first, // safeInsert erwartet ein einzelnes Objekt
        );

        // Für mehrere Einträge verwende executeWithRetry
        if (favoritesToInsert.length > 1) {
          await _retryClient.executeWithRetry(
            'Mehrere Favoriten hinzufügen',
            () async {
              await Supabase.instance.client
                  .from('user_favorites')
                  .insert(favoritesToInsert.skip(1).toList());
            },
          );
        }
      }

      _log.i(
        '✅ Favoriten erfolgreich synchronisiert mit Retry-Logic: ${favoriteIds.length} Favoriten',
      );
      return true;
    } catch (e) {
      _log.e(
        '❌ Favoriten-Synchronisation fehlgeschlagen nach Retry-Versuchen: $e',
      );
      return false;
    }
  }

  /// Robuste Profil-Backup-Operation mit Retry-Logic
  Future<bool> backupProfileResilient(
    String userId,
    Map<String, dynamic> profileData,
  ) async {
    try {
      await _retryClient
          .safeInsert('profile_backups', 'Profil-Backup speichern', {
            'profile_id': userId,
            'data': profileData,
            'created_at': DateTime.now().toIso8601String(),
          });

      _log.i(
        '✅ Profil-Backup erfolgreich gespeichert mit Retry-Logic für User: $userId',
      );
      return true;
    } catch (e) {
      _log.e('❌ Profil-Backup fehlgeschlagen nach Retry-Versuchen: $e');
      return false;
    }
  }

  /// Robuste Profil-Wiederherstellung mit Retry-Logic
  Future<Map<String, dynamic>?> restoreProfileResilient(String userId) async {
    try {
      final result = await _retryClient.safeSelect(
        'profile_backups',
        'Profil-Backup laden',
        select: 'data, created_at',
        filters: {'profile_id': userId},
        orderBy: 'created_at',
        ascending: false,
        limit: 1,
      );

      if (result.isNotEmpty && result.first['data'] != null) {
        _log.i(
          '✅ Profil-Backup erfolgreich geladen mit Retry-Logic für User: $userId',
        );
        return result.first['data'] as Map<String, dynamic>;
      }

      _log.w('⚠️ Kein Profil-Backup gefunden für User: $userId');
      return null;
    } catch (e) {
      _log.e(
        '❌ Profil-Wiederherstellung fehlgeschlagen nach Retry-Versuchen: $e',
      );
      return null;
    }
  }

  /// Robuste Subscription-Abfrage mit Retry-Logic
  Future<Map<String, dynamic>?> getCurrentSubscriptionResilient(
    String userId,
  ) async {
    try {
      final result = await _retryClient.safeSelect(
        'subscriptions',
        'Aktuelle Subscription abrufen',
        filters: {'user_id': userId, 'status': 'active'},
        orderBy: 'created_at',
        ascending: false,
        limit: 1,
      );

      if (result.isNotEmpty) {
        _log.i(
          '✅ Subscription erfolgreich abgerufen mit Retry-Logic für User: $userId',
        );
        return result.first;
      }

      _log.w('⚠️ Keine aktive Subscription gefunden für User: $userId');
      return null;
    } catch (e) {
      _log.e('❌ Subscription-Abfrage fehlgeschlagen nach Retry-Versuchen: $e');
      return null;
    }
  }

  /// Robuste User-Profile-Abfrage mit Retry-Logic und Offline-Cache
  Future<Map<String, dynamic>?> getUserProfileResilient(String userId) async {
    try {
      final result = await _retryClient.safeSelect(
        'profiles',
        'User-Profile abrufen',
        filters: {'id': userId},
        limit: 1,
      );

      if (result.isNotEmpty) {
        final profile = result.first;
        // Cache das Profil für Offline-Verfügbarkeit
        await _cacheService.cacheUserProfile(profile);
        _log.i(
          '✅ User-Profile erfolgreich abgerufen und gecacht für User: $userId',
        );
        return profile;
      }

      _log.w('⚠️ Kein User-Profile gefunden für User: $userId');
    } catch (e) {
      _log.w('⚠️ User-Profile-Abfrage fehlgeschlagen, versuche Cache: $e');
    }

    // Fallback: Versuche Cache zu laden
    try {
      final cachedProfile = await _cacheService.getCachedUserProfile();
      if (cachedProfile != null && cachedProfile['id'] == userId) {
        _log.i('📱 User-Profile aus Offline-Cache geladen für User: $userId');
        return cachedProfile;
      }
    } catch (e) {
      _log.e('❌ Auch Cache-Laden fehlgeschlagen: $e');
    }

    _log.e('❌ User-Profile-Abfrage komplett fehlgeschlagen (Online + Cache)');
    return null;
  }

  /// Robuste User-Profile-Update mit Retry-Logic
  Future<bool> updateUserProfileResilient(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _retryClient.safeUpdate(
        'profiles',
        'User-Profile aktualisieren',
        updates,
        {'id': userId},
      );

      _log.i(
        '✅ User-Profile erfolgreich aktualisiert mit Retry-Logic für User: $userId',
      );
      return true;
    } catch (e) {
      _log.e('❌ User-Profile-Update fehlgeschlagen nach Retry-Versuchen: $e');
      return false;
    }
  }

  /// Prüft die Netzwerk-Konnektivität
  Future<bool> checkConnectivity() async {
    try {
      // Einfacher Ping zu Supabase
      await _retryClient.executeWithRetry(
        'Konnektivitäts-Check',
        () async {
          await Supabase.instance.client.from('profiles').select('id').limit(1);
        },
        maxRetries: 1, // Nur ein Versuch für Connectivity-Check
        enableLogging: false,
      );

      return true;
    } catch (e) {
      _log.w('⚠️ Netzwerk-Konnektivität nicht verfügbar: $e');
      return false;
    }
  }

  /// Führt eine Health-Check für alle kritischen Services durch
  Future<Map<String, bool>> performHealthCheck() async {
    final results = <String, bool>{};

    try {
      // Basis-Konnektivität
      results['connectivity'] = await checkConnectivity();

      // Auth-Service
      try {
        final user = Supabase.instance.client.auth.currentUser;
        results['auth'] = user != null;
      } catch (e) {
        results['auth'] = false;
      }

      // Database-Access
      try {
        await _retryClient.executeWithRetry(
          'Database Health Check',
          () async {
            await Supabase.instance.client
                .from('profiles')
                .select('count')
                .limit(1);
          },
          maxRetries: 1,
          enableLogging: false,
        );
        results['database'] = true;
      } catch (e) {
        results['database'] = false;
      }

      _log.i('🏥 Health Check abgeschlossen: $results');
      return results;
    } catch (e) {
      _log.e('❌ Health Check fehlgeschlagen: $e');
      return {'error': false};
    }
  }
}

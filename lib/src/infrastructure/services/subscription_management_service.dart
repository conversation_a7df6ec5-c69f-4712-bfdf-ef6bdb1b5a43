import 'dart:async';

import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/core/utils/device_manager.dart';
import 'package:ki_test/src/domain/models/subscription_model.dart';
import 'package:ki_test/src/infrastructure/services/network_resilience_service.dart';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/network/supabase_retry_client.dart';

/// Cache-Entry für Performance-Optimierung
class _CacheEntry<T> {
  final T data;
  final DateTime timestamp;

  _CacheEntry(this.data, this.timestamp);

  bool isExpired(Duration timeout) {
    return DateTime.now().difference(timestamp) > timeout;
  }
}

/// Service zur Verwaltung von Abonnements
class SubscriptionManagementService {
  final SupabaseClient _supabase;
  final Ref _ref;
  final _log = getLogger('SubscriptionManagementService');

  // Lazy-loaded NetworkResilienceService für Fallback-Operationen
  NetworkResilienceService? _networkResilienceService;

  // Realtime-Channel für Subscription-Updates
  RealtimeChannel? _subscriptionsChannel;

  // Supabase-Tabellennamen
  static const String _subscriptionsTable = 'subscriptions';
  static const String _applicationCountersTable = 'application_counters';

  // Anzahl der kostenlosen Bewerbungen für neue Nutzer
  /// Realtime-Listener für Abo-Änderungen initialisieren (idempotent)
  void _setupSubscriptionsRealtimeIfNeeded() {
    // Realtime vorübergehend deaktiviert
  }

  /// Realtime-Listener beenden
  void _disposeSubscriptionsRealtime() {
    // Realtime vorübergehend deaktiviert
    _subscriptionsChannel = null;
  }

  /// Öffentliche Init/Dispose-APIs
  void initializeRealtimeSubscriptions() =>
      _setupSubscriptionsRealtimeIfNeeded();
  void dispose() => _disposeSubscriptionsRealtime();

  static const int _freeTierApplications = 5;

  // CACHING-LAYER für Performance-Optimierung
  static final Map<String, _CacheEntry<Map<String, dynamic>>>
  _applicationCounterCache = {};
  static final Map<String, _CacheEntry<DateTime?>> _resetDateCache = {};
  static const Duration _cacheTimeout = Duration(minutes: 5);
  static const Duration _shortCacheTimeout = Duration(seconds: 30);

  SubscriptionManagementService(this._supabase, this._ref);

  /// Cache-Update-Hilfsmethode
  void _updateApplicationCounterCache(
    String userId,
    Map<String, dynamic> data,
  ) {
    _applicationCounterCache[userId] = _CacheEntry(
      {...data, 'from_cache': false}, // Entferne Cache-Flags aus echten Daten
      DateTime.now(),
    );
    _log.d('📦 Cache aktualisiert für User: $userId');
  }

  /// Cache-Invalidierung für User
  void _invalidateApplicationCounterCache(String userId) {
    _applicationCounterCache.remove(userId);
    _resetDateCache.remove(userId);
    _log.d('🗑️ Cache invalidiert für User: $userId');
  }

  /// Cache-Cleanup (entfernt abgelaufene Einträge)
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    _applicationCounterCache.removeWhere(
      (key, entry) => entry.isExpired(_cacheTimeout),
    );
    _resetDateCache.removeWhere((key, entry) => entry.isExpired(_cacheTimeout));
  }

  /// Lazy-Initialisierung des NetworkResilienceService
  NetworkResilienceService get _resilientService {
    _networkResilienceService ??= NetworkResilienceService(
      SupabaseRetryClient(_supabase),
    );
    return _networkResilienceService!;
  }

  /// Minimaler, stabiler Wrapper: verbleibende Bewerbungen abrufen
  /// Hinweis: Voll-Implementierung existiert weiter unten; aufgrund eines Scope-Problems
  /// stellen wir hier sicher, dass die Methode auf Klassenebene verfügbar ist.
  Future<Map<String, dynamic>> getRemainingApplications() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      // Schnellpfad: versuche sichere RPC
      try {
        final response = await _supabase.rpc(
          'get_remaining_applications_safe',
          params: {'p_user_id': currentUser.id},
        );
        if (response != null) {
          final result = _normalizeApplicationCounter(
            Map<String, dynamic>.from(response),
          );
          result['source'] = 'rpc';
          _updateApplicationCounterCache(currentUser.id, result);
          return result;
        }
      } catch (e) {
        _log.w('RPC get_remaining_applications_safe fehlgeschlagen: $e');
      }

      // Fallback: Resilience-Service
      try {
        final resilient = await _resilientService
            .getRemainingApplicationsResilient(currentUser.id);
        if (resilient != null) {
          final map = Map<String, dynamic>.from(resilient);
          map['source'] = 'resilient';
          _updateApplicationCounterCache(currentUser.id, map);
          return map;
        }
      } catch (e) {
        _log.w('Resilience-Fallback fehlgeschlagen: $e');
      }

      // Letzter Fallback: plan-bewusster Default
      try {
        final subscription = await getCurrentSubscription();
        if (subscription != null) {
          final plan = (subscription.planType ?? 'free').toLowerCase();
          if (plan == 'unlimited' || plan == 'premium') {
            return {
              'remaining': -1,
              'total': -1,
              'unlimited': true,
              'plan_type': plan,
            };
          }
          if (plan == 'pro') {
            return {
              'remaining': 150,
              'total': 150,
              'unlimited': false,
              'plan_type': plan,
            };
          }
          if (plan == 'basic' || plan == 'free') {
            return {
              'remaining': 5,
              'total': 5,
              'unlimited': false,
              'plan_type': plan,
            };
          }
        }
      } catch (e) {
        _log.w('Plan-Fallback fehlgeschlagen: $e');
      }
      return {'remaining': 0, 'total': 0, 'unlimited': false};
    } catch (e) {
      _log.e('getRemainingApplications Wrapper-Fehler', error: e);
      return {'remaining': 0, 'total': 0, 'unlimited': false};
    }
  }

  /// Normalisiert Server-/RPC-Antworten für Bewerbungszähler
  Map<String, dynamic> _normalizeApplicationCounter(Map<String, dynamic> raw) {
    int parseInt(dynamic v) {
      if (v is int) return v;
      if (v == null) return 0;
      return int.tryParse(v.toString()) ?? 0;
    }

    final remaining = parseInt(raw['remaining']);
    final total = parseInt(raw['total']);
    final unlimited = raw['unlimited'] == true;
    final planType = (raw['plan_type'] as String?) ?? 'free';

    return {
      'remaining': remaining,
      'total': total,
      'unlimited': unlimited,
      'plan_type': planType,
    };
  }

  // Erzwinge sinnvolle Mindestwerte je nach Plan, damit Premium niemals 0/0 zeigt
  Map<String, dynamic> _applyPlanAwareFloor(Map<String, dynamic> data) {
    try {
      String? plan = (data['plan_type'] as String?)?.toLowerCase();

      // Sicherheitsnetz: Falls das UserProfile bereits auf free steht oder nicht Premium ist,
      // überschreibe inkonsistente Server-/RPC-Werte (z. B. 150/pro nach Ablauf)
      try {
        final profile = _ref.read(userProfileProvider);
        final user = profile.value;
        final userIsPremium = user?.isPremium ?? false;
        final userPlan = user?.premiumPlanType?.toLowerCase();
        if (user != null && (!userIsPremium || userPlan == 'free')) {
          plan = 'free';
          data['plan_type'] = 'free';
        }
      } catch (_) {}

      if (plan == null) return data;

      if (plan == 'unlimited' || plan == 'premium') {
        data['unlimited'] = true;
        data['remaining'] = -1;
        data['total'] = -1;
        return data;
      }

      if (plan == 'pro') {
        final total = (data['total'] as int?) ?? 0;
        final remaining = (data['remaining'] as int?) ?? 0;
        // Pro-Plan darf nie unter 150 fallen (gesunder Mindestwert für Anzeige)
        if (total < 150) data['total'] = 150;
        if (remaining < 150) data['remaining'] = 150;
        data['unlimited'] = false;
        return data;
      }

      if (plan == 'basic' || plan == 'free') {
        // Bei Free/Basic nie negativ/leer; clamp auf 5
        final remaining = (data['remaining'] as int?) ?? 0;
        data['total'] = 5;
        data['remaining'] = remaining < 0 ? 0 : (remaining > 5 ? 5 : remaining);
        data['unlimited'] = false;
        return data;
      }

      return data;
    } catch (e) {
      _log.w('Plan-Flooring fehlgeschlagen: $e');
      return data;
    }
  }

  /// Gibt das aktuelle Abonnement des Benutzers zurück
  /// KRITISCHER FIX: Prüft automatisch auf abgelaufene Abonnements
  Future<SubscriptionModel?> getCurrentSubscription() async {
    bool isValidUuid(String s) {
      final re = RegExp(
        r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
      );
      return re.hasMatch(s);
    }

    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return null;
      }

      // 1. ALLE aktiven Abonnements abrufen (auch abgelaufene)
      final response =
          await _supabase
              .from(_subscriptionsTable)
              .select()
              .eq('user_id', currentUser.id)
              .eq('status', 'active')
              .order('end_date', ascending: false)
              .limit(1)
              .maybeSingle();

      if (response != null) {
        final subscription = SubscriptionModel.fromJson(response);

        // 2. KRITISCHE ABLAUF-PRÜFUNG
        final now = DateTime.now();
        final endDate = subscription.endDate;

        if (endDate != null && endDate.isBefore(now)) {
          _log.w(
            '🚨 ABGELAUFENES ABONNEMENT ERKANNT: User ${currentUser.id}, Plan: ${subscription.planType}, Ablauf: $endDate, Heute: $now',
          );

          // 3. Abonnement als abgelaufen markieren (nur wenn gültige UUID)
          final subId = subscription.subscriptionId;
          if (subId != null && subId.isNotEmpty && isValidUuid(subId)) {
            try {
              await _supabase
                  .from(_subscriptionsTable)
                  .update({
                    'status': 'expired',
                    'updated_at': now.toIso8601String(),
                  })
                  .eq('id', subId);
            } catch (e) {
              _log.w('⚠️ Update auf expired fehlgeschlagen (id=$subId): $e');
            }
          } else {
            _log.w(
              '⚠️ Abonnement-ID ungültig oder leer (id=$subId) – Update auf expired übersprungen',
            );
          }

          // 4. User-Profil auf Free downgraden (immer, unabhängig vom Update oben)
          await _ref
              .read(userProfileProvider.notifier)
              .updatePremiumStatus(
                isPremium: false,
                planType: 'free',
                premiumExpiryDate: null,
              );

          // 5. KRITISCH: Bewerbungszähler für Free-Plan zurücksetzen
          _log.w('🔄 KRITISCH: Starte Bewerbungszähler-Reset für Free-Plan...');
          try {
            await _resetApplicationCounterForFreePlan(currentUser.id);
            // Cache invalidieren, damit sofort 5/5 angezeigt wird
            _invalidateApplicationCounterCache(currentUser.id);
            _log.i('✅ KRITISCH: Bewerbungszähler-Reset abgeschlossen');
          } catch (e) {
            _log.e(
              '❌ KRITISCH: Bewerbungszähler-Reset fehlgeschlagen',
              error: e,
            );
          }

          _log.i(
            '✅ Abgelaufenes Abonnement deaktiviert, User auf Free-Plan gesetzt und Bewerbungszähler zurückgesetzt',
          );
          return null; // Kein aktives Abonnement mehr
        }

        return subscription;
      }

      // Wenn kein aktives Abonnement gefunden wurde, erstelle ein Basic-Abonnement
      return await _createDefaultSubscription(currentUser.id);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des aktuellen Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Erstellt ein Standard-Abonnement für neue Benutzer
  Future<SubscriptionModel?> _createDefaultSubscription(String userId) async {
    try {
      // PRÜFUNG HINZUGEFÜGT: Zuerst prüfen, ob bereits ein aktives Abonnement existiert
      final existingActiveSubscription =
          await _supabase
              .from(_subscriptionsTable)
              .select('id, status, plan_type')
              .eq('user_id', userId)
              .eq('status', 'active')
              .maybeSingle();

      // Wenn bereits ein aktives Abonnement existiert, keines erstellen
      if (existingActiveSubscription != null) {
        _log.i(
          "Aktives Abonnement für Benutzer $userId existiert bereits (${existingActiveSubscription['plan_type']}). Überspringe die Erstellung.",
        );
        return SubscriptionModel.fromJson(existingActiveSubscription);
      }

      // Prüfe, ob inaktive Abonnements existieren
      final existingSubscriptions = await _supabase
          .from(_subscriptionsTable)
          .select('id, status, plan_type')
          .eq('user_id', userId);

      if (existingSubscriptions.isNotEmpty) {
        _log.i(
          "Inaktive Abonnements für Benutzer $userId gefunden (${existingSubscriptions.length}). Erstelle trotzdem neues aktives Abonnement.",
        );
      }

      // Prüfe, ob bereits ein Bewerbungszähler existiert
      final counterExists = await _checkIfCounterExists(userId);

      // Wenn kein Zähler existiert, erstelle einen mit den kostenlosen Bewerbungen
      if (!counterExists) {
        await _createApplicationCounter(
          userId: userId,
          totalApplications: _freeTierApplications,
          remainingApplications: _freeTierApplications,
          isFreeOrBasic: true,
        );
      } else {
        // Zähler existiert bereits -> Sofort auf Free-Plan zurücksetzen (5 Bewerbungen)
        await _resetApplicationCounterForFreePlan(userId);
      }

      // Erstelle ein Free-Abonnement
      final endDate = DateTime.now().add(const Duration(days: 30));

      final subscriptionData = {
        'user_id': userId,
        'subscription_type': 'free',
        'plan_type': 'free',
        'status': 'active',
        'is_premium': false,
        'auto_renew': false,
        'start_date': DateTime.now().toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await _supabase
              .from(_subscriptionsTable)
              .insert(subscriptionData)
              .select()
              .maybeSingle();

      if (response == null) {
        _log.e('Fehler: Standard-Abonnement konnte nicht erstellt werden');
        return null;
      }

      // Aktiviere Werbung für Basic-Nutzer
      // Ads: Provider nicht verfügbar im Service-Kontext, UI kümmert sich um Sichtbarkeit

      return SubscriptionModel.fromJson(response);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Erstellen des Standard-Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Überprüft und aktualisiert den Bewerbungszähler für kostenlose Benutzer
  Future<bool> checkAndResetFreeApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Hole das aktuelle Abonnement
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Nur für kostenlose Abonnements
      if (subscription.planType?.toLowerCase() != 'free' &&
          subscription.planType?.toLowerCase() != 'basic') {
        return true;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'check_free_application_counter',
          params: {'p_user_id': currentUser.id},
        );

        if (response == true) {
          _log.i(
            'Kostenloser Bewerbungszähler erfolgreich überprüft und aktualisiert',
          );
          return true;
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Manuell den Bewerbungszähler überprüfen
      final counter = await _getApplicationCounter(currentUser.id);

      // KRITISCH: Für Free-User mit falschen Werten (>5) zurücksetzen
      if (subscription.planType?.toLowerCase() == 'free' && counter != null) {
        final currentRemaining = counter['remaining_applications'] ?? 0;
        final currentTotal = counter['total_applications'] ?? 0;

        // Wenn Free-User mehr als 5 Bewerbungen hat, ist das falsch
        if (currentTotal > 5 || currentRemaining > 5) {
          _log.w(
            '🚨 KRITISCH: Free-User hat falsche Bewerbungswerte: total=$currentTotal, remaining=$currentRemaining',
          );
          _log.w(
            '🔄 KRITISCH: Setze Free-User auf korrekte 5 Bewerbungen zurück',
          );
          await _resetApplicationCounterForFreePlan(currentUser.id);
          return true;
        }
      }

      // Wenn kein Zähler existiert, erstelle einen neuen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
          isFreeOrBasic: true,
        );
        return true;
      }

      // Prüfe, ob der Zähler zurückgesetzt werden muss
      DateTime? freeResetDate;

      // Prüfe, ob die free_reset_date-Spalte existiert
      if (counter.containsKey('free_reset_date') &&
          counter['free_reset_date'] != null) {
        freeResetDate = DateTime.parse(counter['free_reset_date']);
        _log.i("free_reset_date gefunden: $freeResetDate");
      } else {
        // Wenn die Spalte nicht existiert, verwende das normale reset_date
        freeResetDate =
            counter['reset_date'] != null
                ? DateTime.parse(counter['reset_date'])
                : null;
        _log.w(
          "free_reset_date nicht gefunden, verwende alternatives Datum: $freeResetDate",
        );
      }

      // Wenn kein Reset-Datum existiert oder es mehr als 7 Tage her ist, setze den Zähler zurück
      if (freeResetDate == null ||
          DateTime.now().difference(freeResetDate).inDays >= 7) {
        // Reset per Standard-Mechanismus
        await _resetApplicationCounterForFreePlan(currentUser.id);
        return true;
      }

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Überprüfen des kostenlosen Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Gibt das nächste Reset-Datum für kostenlose Bewerbungen zurück
  Future<DateTime?> getNextFreeResetDate() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return null;
      }

      // Hole das aktuelle Abonnement
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return null;
      }

      // Nur für kostenlose Abonnements
      if (subscription.planType?.toLowerCase() != 'free' &&
          subscription.planType?.toLowerCase() != 'basic') {
        return null;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'get_next_free_reset_date',
          params: {'p_user_id': currentUser.id},
        );

        if (response != null) {
          return DateTime.parse(response.toString());
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Manuell das nächste Reset-Datum berechnen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, gib das aktuelle Datum zurück
      if (counter == null) {
        return DateTime.now();
      }

      // KORRIGIERT: Gib das AKTUELLE Reset-Datum zurück (wann der nächste Reset stattfindet)
      DateTime resetDate;

      // Prüfe, ob die free_reset_date-Spalte existiert
      if (counter.containsKey('free_reset_date') &&
          counter['free_reset_date'] != null) {
        resetDate = DateTime.parse(counter['free_reset_date']);
        _log.i("free_reset_date gefunden: $resetDate");
      } else {
        // Wenn die Spalte nicht existiert, verwende das normale reset_date oder das aktuelle Datum
        resetDate =
            counter['reset_date'] != null
                ? DateTime.parse(counter['reset_date'])
                : DateTime.now();
        _log.w(
          "free_reset_date nicht gefunden, verwende alternatives Datum: $resetDate",
        );
      }

      // KRITISCHER FIX: Wenn das Reset-Datum in der Vergangenheit liegt, berechne das nächste
      final now = DateTime.now();
      while (resetDate.isBefore(now)) {
        resetDate = resetDate.add(const Duration(days: 7));
      }

      // Gib das AKTUELLE Reset-Datum zurück (nicht +7 Tage!)
      return resetDate;
    } catch (e) {
      _log.e(
        'Fehler beim Abrufen des nächsten Reset-Datums für kostenlose Bewerbungen',
        error: e,
      );
      return null;
    }
  }

  /// Inkrementiert den Bewerbungszähler des Benutzers (ATOMARE OPERATION + ANTI-MANIPULATION)
  Future<bool> incrementApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      _log.i(
        '🔥 GUTHABEN-ABZUG: Starte ATOMARE incrementApplicationCounter für User: ${currentUser.id}',
      );

      // Cache invalidieren vor Update
      _invalidateApplicationCounterCache(currentUser.id);

      // Prüfe Premium-Status (Premium-User haben unbegrenzte Bewerbungen)
      final subscription = await getCurrentSubscription();
      if (subscription?.planType == 'unlimited' ||
          subscription?.planType == 'premium') {
        _log.i('✅ GUTHABEN-ABZUG: Premium/Unlimited-Plan - kein Abzug nötig');
        return true;
      }

      // 🔐 ANTI-MANIPULATION: Device-Validation vor Guthaben-Abzug
      final deviceManager = DeviceManager();
      final deviceValidation = await deviceManager
          .validateDeviceForApplications(currentUser.id);

      if (!deviceValidation['is_valid']) {
        final reason = deviceValidation['reason'] ?? 'Unbekannter Fehler';
        final blockedUntil = deviceValidation['blocked_until'];

        _log.w(
          '🚫 ANTI-MANIPULATION: Device-Validation fehlgeschlagen - $reason',
        );

        if (blockedUntil != null) {
          _log.w('🚫 ANTI-MANIPULATION: Device gesperrt bis: $blockedUntil');
        }

        // Werfe spezifische Exception für UI-Behandlung
        final suffix =
            blockedUntil != null ? " (Gesperrt bis: $blockedUntil)" : '';
        throw Exception("Device-Validation fehlgeschlagen: $reason$suffix");
      }

      _log.i('✅ ANTI-MANIPULATION: Device validiert - Guthaben-Abzug erlaubt');

      // ATOMARE OPERATION: Verwende neue sichere DB-Funktion
      try {
        final response = await _supabase.rpc(
          'decrement_application_counter_safe',
          params: {
            'p_user_id': currentUser.id,
            'p_expected_version':
                null, // Keine Version-Prüfung für ersten Versuch
          },
        );

        if (response == null) {
          _log.e('❌ GUTHABEN-ABZUG: Keine Antwort von DB-Funktion');
          return false;
        }

        final result = Map<String, dynamic>.from(response);
        _log.i('🎯 GUTHABEN-ABZUG: DB-Antwort: $result');

        if (result['success'] == true) {
          _log.i(
            '✅ GUTHABEN-ABZUG: Erfolgreich dekrementiert - Remaining: ${result['remaining']}',
          );

          // Cache invalidieren nach erfolgreichem Update
          _invalidateApplicationCounterCache(currentUser.id);

          return true;
        } else {
          final error = result['error'] ?? 'unknown_error';
          final message = result['message'] ?? 'Unbekannter Fehler';

          _log.w('❌ GUTHABEN-ABZUG: Fehlgeschlagen - $error: $message');

          // Spezielle Behandlung für verschiedene Fehlertypen
          if (error == 'no_counter_found') {
            // Erstelle Counter für neuen User
            return await _createCounterAndDecrement(currentUser.id);
          } else if (error == 'no_applications_remaining') {
            _log.w('❌ GUTHABEN-ABZUG: Keine Bewerbungen mehr verfügbar');
            return false;
          } else if (error == 'version_conflict') {
            // Retry mit aktueller Version
            _log.i('🔄 GUTHABEN-ABZUG: Version-Konflikt - Retry...');
            return await _retryDecrementWithCurrentVersion(currentUser.id);
          }

          return false;
        }
      } catch (e) {
        _log.e('❌ GUTHABEN-ABZUG: DB-Funktion-Fehler: $e');
        return false;
      }
    } catch (e, stackTrace) {
      _log.e(
        '❌ GUTHABEN-ABZUG: Fehler beim Inkrementieren des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Erstellt Counter für neuen User und dekrementiert sofort
  Future<bool> _createCounterAndDecrement(String userId) async {
    try {
      _log.i('🔄 Erstelle neuen Counter für User: $userId');

      final response = await _supabase.rpc(
        'create_application_counter_safe',
        params: {
          'p_user_id': userId,
          'p_total_applications': 5, // Standard für Free-User
        },
      );

      if (response == null) {
        _log.e('❌ Counter-Erstellung fehlgeschlagen: Keine Antwort');
        return false;
      }

      final result = Map<String, dynamic>.from(response);

      if (result['success'] == true) {
        _log.i('✅ Counter erfolgreich erstellt');

        // Jetzt dekrementieren
        return await incrementApplicationCounter();
      } else {
        _log.e('❌ Counter-Erstellung fehlgeschlagen: ${result['message']}');
        return false;
      }
    } catch (e) {
      _log.e('❌ Fehler bei Counter-Erstellung: $e');
      return false;
    }
  }

  /// Retry-Mechanismus mit aktueller Version
  Future<bool> _retryDecrementWithCurrentVersion(String userId) async {
    try {
      // Hole aktuelle Version
      final counterData =
          await _supabase
              .from(_applicationCountersTable)
              .select('version')
              .eq('user_id', userId)
              .single();

      final currentVersion = counterData['version'] as int?;

      if (currentVersion == null) {
        _log.e('❌ Retry: Keine Version gefunden');
        return false;
      }

      _log.i('🔄 Retry mit Version: $currentVersion');

      final response = await _supabase.rpc(
        'decrement_application_counter_safe',
        params: {'p_user_id': userId, 'p_expected_version': currentVersion},
      );

      if (response == null) {
        _log.e('❌ Retry fehlgeschlagen: Keine Antwort');
        return false;
      }

      final result = Map<String, dynamic>.from(response);

      if (result['success'] == true) {
        _log.i('✅ Retry erfolgreich');
        _invalidateApplicationCounterCache(userId);
        return true;
      } else {
        _log.w('❌ Retry fehlgeschlagen: ${result['message']}');
        return false;
      }
    } catch (e) {
      _log.e('❌ Fehler bei Retry: $e');
      return false;
    }
  }

  /// Prüft, ob bereits ein Bewerbungszähler für den Benutzer existiert
  Future<bool> _checkIfCounterExists(String userId) async {
    try {
      final response =
          await _supabase
              .from(_applicationCountersTable)
              .select('id')
              .eq('user_id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      _log.e('Fehler beim Prüfen des Bewerbungszählers', error: e);
      return false;
    }
  }

  /// Gibt den Bewerbungszähler des Benutzers zurück
  Future<Map<String, dynamic>?> _getApplicationCounter(String userId) async {
    try {
      final response =
          await _supabase
              .from(_applicationCountersTable)
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      return response;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Erstellt einen neuen Bewerbungszähler für den Benutzer
  Future<void> _createApplicationCounter({
    required String userId,
    required int totalApplications,
    required int remainingApplications,
    bool isFreeOrBasic = false,
  }) async {
    try {
      final counterData = {
        'user_id': userId,
        'total_applications': totalApplications,
        'remaining_applications': remainingApplications,
        'reset_date': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Für kostenlose/Basic-Nutzer setze auch free_reset_date, falls die Spalte existiert
      if (isFreeOrBasic) {
        try {
          // Prüfe, ob die free_reset_date-Spalte existiert
          final tableInfo =
              await _supabase
                  .from(_applicationCountersTable)
                  .select('*')
                  .limit(1)
                  .maybeSingle();

          if (tableInfo != null && tableInfo.containsKey('free_reset_date')) {
            counterData['free_reset_date'] = DateTime.now().toIso8601String();
            _log.i("free_reset_date-Spalte gefunden und wird gesetzt");
          }
        } catch (columnError) {
          _log.w("Fehler beim Prüfen der Spaltenstruktur: $columnError");
        }
      }

      await _supabase.from(_applicationCountersTable).insert(counterData);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Erstellen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Client-seitige Plan-Logik entfernt. Quelle ist ausschließlich der Server.
  int _getTotalApplicationsForPlan(String? planType) {
    return 0; // nicht mehr verwendet
  }

  /// Setzt den Bewerbungszähler für Free-Plan zurück
  /// KRITISCH: Wird aufgerufen wenn ein User von Premium auf Free downgraded wird
  Future<void> _resetApplicationCounterForFreePlan(String userId) async {
    try {
      _log.i('🔄 Setze Bewerbungszähler für Free-Plan zurück (User: $userId)');

      // Free-Plan hat 5 Bewerbungen pro Woche
      const int freeApplications = 5;
      final now = DateTime.now();
      final resetDate = now.add(
        const Duration(days: 7),
      ); // Nächster Reset in 7 Tagen

      // Prüfe ob bereits ein Zähler existiert
      final existingCounter = await _getApplicationCounter(userId);

      if (existingCounter != null) {
        // Aktualisiere den bestehenden Zähler
        final updateResp = await _supabase
            .from(_applicationCountersTable)
            .update({
              'total_applications': freeApplications,
              'remaining_applications': freeApplications,
              'reset_date': resetDate.toIso8601String(),
              'updated_at': now.toIso8601String(),
            })
            .eq('user_id', userId);
        if (updateResp != null) {
          _log.i(
            '✅ Bestehender Bewerbungszähler auf Free-Plan zurückgesetzt: $freeApplications Bewerbungen',
          );
        } else {
          _log.w('⚠️ Update-Antwort null – eventuell kein Datensatz betroffen');
        }
      } else {
        // Erstelle einen neuen Zähler
        final before = DateTime.now();
        await _createApplicationCounter(
          userId: userId,
          totalApplications: freeApplications,
          remainingApplications: freeApplications,
          isFreeOrBasic: true,
        );
        final durMs = DateTime.now().difference(before).inMilliseconds;
        _log.i(
          '✅ Neuer Free-Plan Bewerbungszähler erstellt: $freeApplications Bewerbungen (in ${durMs}ms)',
        );
      }
    } catch (e) {
      _log.e(
        '❌ Fehler beim Zurücksetzen des Bewerbungszählers für Free-Plan',
        error: e,
      );
    }
  }

  /// Prüft, ob der Benutzer ein Premium-Abonnement hat
  Future<bool> hasPremiumSubscription() async {
    final subscription = await getCurrentSubscription();
    return subscription?.isPremium ?? false;
  }

  /// Kündigt das aktuelle Abonnement des Benutzers
  Future<bool> cancelSubscription() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.e('Benutzer ist nicht angemeldet');
        return false;
      }

      // Suche nach aktiven Abonnements in der Datenbank
      final response =
          await _supabase
              .from(_subscriptionsTable)
              .select()
              .eq('user_id', currentUser.id)
              .eq('status', 'active')
              .limit(1)
              .maybeSingle();

      if (response == null) {
        _log.w('Kein aktives Abonnement in der Datenbank gefunden');

        // Aktualisiere trotzdem den Premium-Status im UserProfile
        await _ref
            .read(userProfileProvider.notifier)
            .updatePremiumStatus(isPremium: false, planType: 'basic');

        return true;
      }

      // Markiere das Abonnement als gekündigt
      final respId = (response['id'] as String?) ?? '';
      if (respId.isEmpty) {
        _log.w('⚠️ Kündigung übersprungen: response[id] ist leer');
      } else {
        await _supabase
            .from(_subscriptionsTable)
            .update({
              'status': 'cancelled',
              'cancelled_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', respId);
        _log.i('Abonnement $respId erfolgreich gekündigt');
      }

      // Optional: Aktualisiere den Premium-Status im UserProfile
      // Wir behalten den Premium-Status bis zum Ablaufdatum bei

      return true;
    } catch (e) {
      _log.e('Fehler beim Kündigen des Abonnements', error: e);
      return false;
    }
  }

  /// Synchronisiert das Abonnement zwischen der App und dem Server
  Future<bool> syncSubscription() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Hole das aktuelle Abonnement aus der Datenbank
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Aktualisiere den Premium-Status über den UserProfileNotifier
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: subscription.isPremium,
            premiumExpiryDate: subscription.endDate,
            planType: subscription.planType,
          );

      // Aktiviere oder deaktiviere Werbung basierend auf dem Plan-Typ
      // Ads-Steuerung wird im UI/Notifier erledigt, nicht hier im Service

      // Überprüfe und aktualisiere den Bewerbungszähler
      try {
        // Wenn der Plan nicht Premium oder Unlimited ist, überprüfe den Bewerbungszähler
        if (subscription.planType?.toLowerCase() != 'premium' &&
            subscription.planType?.toLowerCase() != 'unlimited') {
          // Wenn es ein kostenloses Abonnement ist, überprüfe den wöchentlichen Zähler
          if (subscription.planType?.toLowerCase() == 'free' ||
              subscription.planType?.toLowerCase() == 'basic') {
            try {
              // Überprüfe und aktualisiere den kostenlosen Bewerbungszähler
              final freeCounterResult =
                  await checkAndResetFreeApplicationCounter();
              if (freeCounterResult) {
                _log.i(
                  'Kostenloser Bewerbungszähler erfolgreich überprüft und aktualisiert',
                );
              }
            } catch (freeError) {
              _log.w(
                'Fehler beim Überprüfen des kostenlosen Bewerbungszählers: $freeError',
              );
            }
          } else {
            // Für andere Pläne (z.B. Pro) verwende die normale Überprüfung
            try {
              // Versuche zuerst, die serverseitige Funktion zu verwenden
              try {
                final response = await _supabase.rpc(
                  'check_user_application_counters',
                  params: {'p_user_id': currentUser.id},
                );

                if (response == true) {
                  _log.i(
                    'Bewerbungszähler erfolgreich überprüft und aktualisiert',
                  );
                }
              } catch (rpcError) {
                _log.w(
                  'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
                );

                // Fallback: Standard-Reset
                await _resetApplicationCounterForFreePlan(currentUser.id);
              }
            } catch (e) {
              _log.w('Fehler beim Überprüfen des Bewerbungszählers: $e');

              // Fallback: Standard-Reset
              await _resetApplicationCounterForFreePlan(currentUser.id);

              _log.i(
                'Bewerbungszähler zurückgesetzt für Plan ${subscription.planType}',
              );
            }
          }
        } else {
          _log.i(
            'Premium/Unlimited-Plan: Keine Überprüfung des Bewerbungszählers notwendig',
          );
        }
      } catch (e) {
        _log.w('Fehler beim Überprüfen des Bewerbungszählers: $e');
      }

      _log.i('Abonnement erfolgreich synchronisiert');
      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Synchronisieren des Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }
}

import 'package:dio/dio.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as html_dom;
import 'package:logger/logger.dart';

/// Service für das Extrahieren von Textinhalten aus Webseiten-URLs
/// Fokus auf Stellenanzeigen-relevante Informationen
class WebScrapingService {
  final Logger _log = Logger();
  final Dio _dio = Dio();

  WebScrapingService() {
    // Konfiguriere Dio für Web-Scraping
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': 'Mozilla/5.0 (Android 14; Mobile; rv:109.0) Gecko/117.0 Firefox/117.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    );
  }

  /// Extrahiert Stellenanzeigen-Informationen aus einer URL
  Future<JobScrapingResult> scrapeJobFromUrl(String url) async {
    try {
      _log.i('Starte Web-Scraping für URL: $url');

      // Validiere URL
      if (!_isValidUrl(url)) {
        throw WebScrapingException('Ungültige URL: $url');
      }

      // Lade Webseiten-Inhalt
      final response = await _dio.get(url);
      
      if (response.statusCode != 200) {
        throw WebScrapingException('HTTP-Fehler: ${response.statusCode}');
      }

      final htmlContent = response.data as String;
      _log.d('HTML-Inhalt geladen: ${htmlContent.length} Zeichen');

      // Parse HTML
      final document = html_parser.parse(htmlContent);

      // Extrahiere Informationen
      final result = _extractJobInformation(document, url);
      
      _log.i('Web-Scraping erfolgreich abgeschlossen');
      return result;

    } catch (e) {
      _log.e('Fehler beim Web-Scraping: $e');
      if (e is WebScrapingException) {
        rethrow;
      }
      throw WebScrapingException('Fehler beim Laden der Webseite: ${e.toString()}');
    }
  }

  /// Validiert ob eine URL gültig ist
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Extrahiert Job-relevante Informationen aus dem HTML-Dokument
  JobScrapingResult _extractJobInformation(html_dom.Document document, String url) {
    // Extrahiere Jobtitel
    final jobTitle = _extractJobTitle(document);
    
    // Extrahiere Firmenname
    final companyName = _extractCompanyName(document);
    
    // Extrahiere Jobbeschreibung
    final jobDescription = _extractJobDescription(document);
    
    // Extrahiere zusätzliche Informationen
    final additionalInfo = _extractAdditionalInfo(document);

    return JobScrapingResult(
      url: url,
      jobTitle: jobTitle,
      companyName: companyName,
      jobDescription: jobDescription,
      additionalInfo: additionalInfo,
      extractedAt: DateTime.now(),
    );
  }

  /// Extrahiert den Jobtitel aus verschiedenen HTML-Elementen
  String _extractJobTitle(html_dom.Document document) {
    // Verschiedene Selektoren für Jobtitel probieren
    final titleSelectors = [
      'h1[class*="job"]',
      'h1[class*="title"]',
      'h1[class*="position"]',
      '.job-title',
      '.position-title',
      '[data-testid*="job-title"]',
      '[data-testid*="title"]',
      'h1',
      'title',
    ];

    for (final selector in titleSelectors) {
      final element = document.querySelector(selector);
      if (element != null) {
        final text = element.text.trim();
        if (text.isNotEmpty && text.length > 3) {
          _log.d('Jobtitel gefunden mit Selektor "$selector": $text');
          return text;
        }
      }
    }

    // Fallback: Versuche aus dem Seitentitel zu extrahieren
    final titleElement = document.querySelector('title');
    if (titleElement != null) {
      final titleText = titleElement.text.trim();
      // Entferne häufige Suffixe von Job-Portalen
      final cleanTitle = titleText
          .replaceAll(RegExp(r'\s*-\s*(Jobs?|Stellenanzeigen?|Karriere).*$', caseSensitive: false), '')
          .replaceAll(RegExp(r'\s*\|\s*.*$'), '')
          .trim();
      
      if (cleanTitle.isNotEmpty) {
        _log.d('Jobtitel aus Seitentitel extrahiert: $cleanTitle');
        return cleanTitle;
      }
    }

    return 'Stellenausschreibung';
  }

  /// Extrahiert den Firmennamen
  String _extractCompanyName(html_dom.Document document) {
    final companySelectors = [
      '[class*="company"]',
      '[class*="employer"]',
      '[class*="organization"]',
      '[data-testid*="company"]',
      '.company-name',
      '.employer-name',
    ];

    for (final selector in companySelectors) {
      final element = document.querySelector(selector);
      if (element != null) {
        final text = element.text.trim();
        if (text.isNotEmpty && text.length > 1) {
          _log.d('Firmenname gefunden: $text');
          return text;
        }
      }
    }

    return '';
  }

  /// Extrahiert die Jobbeschreibung
  String _extractJobDescription(html_dom.Document document) {
    final descriptionSelectors = [
      '[class*="job-description"]',
      '[class*="description"]',
      '[class*="content"]',
      '[class*="details"]',
      '[data-testid*="description"]',
      '.job-content',
      '.job-details',
      'main',
      'article',
    ];

    String bestDescription = '';
    int maxLength = 0;

    for (final selector in descriptionSelectors) {
      final elements = document.querySelectorAll(selector);
      for (final element in elements) {
        final text = _extractCleanText(element);
        if (text.length > maxLength && text.length > 100) {
          maxLength = text.length;
          bestDescription = text;
        }
      }
    }

    // Fallback: Extrahiere allen sichtbaren Text
    if (bestDescription.isEmpty) {
      bestDescription = _extractCleanText(document.body);
    }

    // Bereinige und kürze den Text
    bestDescription = _cleanJobDescription(bestDescription);
    
    _log.d('Jobbeschreibung extrahiert: ${bestDescription.length} Zeichen');
    return bestDescription;
  }

  /// Extrahiert zusätzliche Informationen
  Map<String, String> _extractAdditionalInfo(html_dom.Document document) {
    final info = <String, String>{};

    // Versuche E-Mail-Adressen zu finden
    final emailRegex = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
    final bodyText = document.body?.text ?? '';
    final emailMatches = emailRegex.allMatches(bodyText);
    
    if (emailMatches.isNotEmpty) {
      info['email'] = emailMatches.first.group(0) ?? '';
    }

    // Versuche Standort zu finden
    final locationSelectors = [
      '[class*="location"]',
      '[class*="address"]',
      '[class*="city"]',
    ];

    for (final selector in locationSelectors) {
      final element = document.querySelector(selector);
      if (element != null) {
        final text = element.text.trim();
        if (text.isNotEmpty) {
          info['location'] = text;
          break;
        }
      }
    }

    return info;
  }

  /// Extrahiert sauberen Text aus einem HTML-Element
  String _extractCleanText(html_dom.Element? element) {
    if (element == null) return '';

    // Entferne Script- und Style-Tags
    element.querySelectorAll('script, style, nav, header, footer').forEach((e) => e.remove());

    final text = element.text;
    
    // Bereinige Whitespace
    return text
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'\n\s*\n'), '\n')
        .trim();
  }

  /// Bereinigt die Jobbeschreibung
  String _cleanJobDescription(String text) {
    // Entferne übermäßige Leerzeichen und Zeilenumbrüche
    text = text.replaceAll(RegExp(r'\s+'), ' ');
    text = text.replaceAll(RegExp(r'\n\s*\n'), '\n');
    
    // Begrenze die Länge (max 5000 Zeichen für bessere Performance)
    if (text.length > 5000) {
      text = '${text.substring(0, 5000)}...';
    }
    
    return text.trim();
  }
}

/// Ergebnis des Web-Scrapings
class JobScrapingResult {
  final String url;
  final String jobTitle;
  final String companyName;
  final String jobDescription;
  final Map<String, String> additionalInfo;
  final DateTime extractedAt;

  JobScrapingResult({
    required this.url,
    required this.jobTitle,
    required this.companyName,
    required this.jobDescription,
    required this.additionalInfo,
    required this.extractedAt,
  });

  @override
  String toString() {
    return 'JobScrapingResult(title: $jobTitle, company: $companyName, description: ${jobDescription.length} chars)';
  }
}

/// Exception für Web-Scraping-Fehler
class WebScrapingException implements Exception {
  final String message;
  
  WebScrapingException(this.message);
  
  @override
  String toString() => 'WebScrapingException: $message';
}

import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:ki_test/src/core/services/secure_file_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Hilfsklasse zum Speichern und Abrufen des Lebenslaufs
class CvStorageHelper {
  static const String _cvFolderName = 'cv_storage';
  static const String _defaultCvFileName = 'lebenslauf.pdf';

  /// Speichert den Lebenslauf in einem lokalen App-Ordner
  ///
  /// [sourcePath] ist der Pfad zur Quelldatei (z.B. vom FilePicker)
  /// [userId] ist die ID des Benutzers
  ///
  /// Gibt den Pfad zur gespeicherten Datei zurück
  static Future<String> saveCvToLocalStorage(
    String sourcePath,
    String? userId,
  ) async {
    // Fallback, wenn keine userId vorhanden ist
    final String safeUserId = userId ?? 'unknown_user';
    try {
      // Verwende den sicheren Speicherdienst, wenn möglich
      try {
        final secureFileService = SecureFileService();
        final securePath = await secureFileService.saveSecureCv(
          sourcePath,
          safeUserId,
        );
        debugPrint(
          'Lebenslauf erfolgreich verschlüsselt gespeichert: $securePath',
        );
        return securePath;
      } catch (secureError) {
        debugPrint(
          'Fehler bei der sicheren Speicherung, verwende Fallback: $secureError',
        );
        // Fallback zur unverschlüsselten Speicherung
      }

      // Fallback: Unverschlüsselte Speicherung
      final appDir = await getApplicationDocumentsDirectory();
      final cvDir = Directory('${appDir.path}/$_cvFolderName');

      // Erstelle den Ordner, falls er nicht existiert
      if (!await cvDir.exists()) {
        await cvDir.create(recursive: true);
      }

      // Erstelle den Zieldateipfad mit der sicheren userId
      final targetPath = '${cvDir.path}/${safeUserId}_$_defaultCvFileName';

      // Kopiere die Datei
      final sourceFile = File(sourcePath);
      if (await sourceFile.exists()) {
        await sourceFile.copy(targetPath);
        debugPrint(
          'Lebenslauf erfolgreich in lokalen Speicher kopiert (unverschlüsselt): $targetPath',
        );
        return targetPath;
      } else {
        throw Exception('Quelldatei existiert nicht: $sourcePath');
      }
    } catch (e, stack) {
      debugPrint(
        'Fehler beim Speichern des Lebenslaufs in lokalen Speicher: $e\n$stack',
      );
      rethrow;
    }
  }

  /// Lädt den Lebenslauf aus dem lokalen Speicher
  ///
  /// [userId] ist die ID des Benutzers
  ///
  /// Gibt den Pfad zur lokalen Datei zurück oder null, wenn keine Datei gefunden wurde
  static Future<String?> getLocalCvPath(String? userId) async {
    // Fallback, wenn keine userId vorhanden ist
    final String safeUserId = userId ?? 'unknown_user';
    try {
      // Versuche zuerst, den sicher gespeicherten Lebenslauf zu finden
      try {
        final secureFileService = SecureFileService();
        final secureFile = await secureFileService.getSecureCv(safeUserId);

        if (secureFile != null) {
          debugPrint(
            'Sicher gespeicherter Lebenslauf gefunden: ${secureFile.path}',
          );
          return secureFile.path;
        }
      } catch (secureError) {
        debugPrint(
          'Fehler beim Abrufen des sicheren Lebenslaufs: $secureError',
        );
        // Fallback zur unverschlüsselten Speicherung
      }

      // Fallback: Suche nach unverschlüsseltem Lebenslauf
      final appDir = await getApplicationDocumentsDirectory();
      final cvDir = Directory('${appDir.path}/$_cvFolderName');

      // Prüfe, ob der Ordner existiert
      if (!await cvDir.exists()) {
        return null;
      }

      // Prüfe, ob die Datei existiert
      final filePath = '${cvDir.path}/${safeUserId}_$_defaultCvFileName';
      final file = File(filePath);

      if (await file.exists()) {
        debugPrint('Unverschlüsselter Lebenslauf gefunden: $filePath');
        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('Fehler beim Abrufen des lokalen Lebenslaufs: $e');
      return null;
    }
  }

  /// Lädt den Lebenslauf aus Supabase Storage herunter und speichert ihn lokal
  ///
  /// [url] ist die URL zum Lebenslauf (wird nur zur Extraktion des Pfads verwendet)
  /// [userId] ist die ID des Benutzers
  /// [retryCount] Anzahl der Wiederholungsversuche bei Netzwerkfehlern
  ///
  /// Gibt den Pfad zur heruntergeladenen Datei zurück oder null bei Fehler
  static Future<String?> downloadCvFromUrl(
    String url,
    String? userId, {
    int retryCount = 2,
  }) async {
    // Fallback, wenn keine userId vorhanden ist
    final String safeUserId = userId ?? 'unknown_user';

    // Prüfe, ob die URL gültig ist
    if (url.isEmpty) {
      debugPrint('Leere URL für Lebenslauf-Download erhalten');
      return null;
    }

    // Extrahiere den Pfad aus der URL
    String storagePath = '';
    String bucketName = 'cv-backups';

    // Prüfe, ob die URL eine Supabase-URL ist und ob sie einen Bucket-Namen enthält
    if (url.contains('supabase') && url.contains('storage/v1/object')) {
      debugPrint('Supabase Storage URL erkannt: $url');

      try {
        // Extrahiere den Pfad direkt aus der URL
        // Format: https://vpttdxibvjrfjzbtktqg.supabase.co/storage/v1/object/public/cv-backups/281d248c-a36a-450f-9b28-d9f38c3b02fb_user/lebenslauf.pdf
        final uri = Uri.parse(url);
        final pathSegments = uri.pathSegments;

        // Korrigiere die Extraktion des Pfads
        if (pathSegments.contains('cv-backups')) {
          // Der Bucket-Name ist fix "cv-backups"
          bucketName = 'cv-backups';

          // Finde den Index des Bucket-Namens
          final bucketIndex = pathSegments.indexOf('cv-backups');

          // Extrahiere den Pfad (alles nach dem Bucket-Namen)
          if (bucketIndex < pathSegments.length - 1) {
            storagePath = pathSegments.sublist(bucketIndex + 1).join('/');
            debugPrint(
              'Korrigierter Pfad: Bucket=$bucketName, Pfad=$storagePath',
            );
          }
        } else {
          // Fallback: Versuche, den Pfad aus der userId zu erstellen
          storagePath = '${safeUserId}_user/lebenslauf.pdf';
          debugPrint(
            'Kein Bucket in URL gefunden, verwende Standard-Pfad: $storagePath',
          );
        }
      } catch (e) {
        debugPrint('Fehler beim Verarbeiten der Supabase-URL: $e');
      }
    }

    // Wenn kein Pfad extrahiert werden konnte, versuche einen Standard-Pfad
    if (storagePath.isEmpty && safeUserId.isNotEmpty) {
      storagePath = '${safeUserId}_user/lebenslauf.pdf';
      debugPrint('Verwende Standard-Pfad: $storagePath');
    }

    // Wenn immer noch kein Pfad vorhanden ist, können wir nicht fortfahren
    if (storagePath.isEmpty) {
      debugPrint('Konnte keinen gültigen Storage-Pfad extrahieren');
      return null;
    }

    for (int attempt = 0; attempt <= retryCount; attempt++) {
      try {
        debugPrint(
          'Versuche Lebenslauf aus Supabase Storage herunterzuladen (Versuch ${attempt + 1}/${retryCount + 1}): $storagePath',
        );

        // Verwende den Supabase Client, um die Datei herunterzuladen
        final supabase = Supabase.instance.client;

        // Versuche, die Datei direkt aus dem Supabase Storage herunterzuladen
        final bytes = await supabase.storage
            .from(bucketName)
            .download(storagePath)
            .timeout(
              const Duration(seconds: 15),
              onTimeout:
                  () =>
                      throw TimeoutException(
                        'Zeitüberschreitung beim Herunterladen des Lebenslaufs',
                      ),
            );

        // Prüfe, ob die Antwort tatsächlich ein PDF ist
        if (bytes.length < 5 || !_isPdfContent(bytes)) {
          debugPrint(
            'Heruntergeladene Datei ist kein gültiges PDF (Länge: ${bytes.length})',
          );
          if (attempt < retryCount) {
            await Future.delayed(
              Duration(seconds: 1),
            ); // Kurze Pause vor dem nächsten Versuch
            continue;
          }
          return null;
        }

        // Speichere die Datei lokal
        final appDir = await getApplicationDocumentsDirectory();
        final cvDir = Directory('${appDir.path}/$_cvFolderName');

        // Erstelle den Ordner, falls er nicht existiert
        if (!await cvDir.exists()) {
          await cvDir.create(recursive: true);
        }

        // Erstelle den Zieldateipfad
        final fileName = '${safeUserId}_$_defaultCvFileName';
        final targetPath = '${cvDir.path}/$fileName';

        // Speichere die Datei
        final file = File(targetPath);
        await file.writeAsBytes(bytes, flush: true);

        debugPrint(
          'Lebenslauf erfolgreich aus Supabase Storage heruntergeladen: $targetPath (${bytes.length} Bytes)',
        );
        return targetPath;
      } catch (e) {
        debugPrint('Fehler beim Herunterladen des Lebenslaufs: $e');

        // Versuche es mit HTTP-Anfrage als Fallback
        if (attempt == retryCount) {
          try {
            debugPrint('Versuche Fallback mit HTTP-Anfrage: $url');
            final response = await http
                .get(
                  Uri.parse(url),
                  headers: {
                    'Cache-Control': 'no-cache',
                    'Accept': 'application/pdf',
                    'User-Agent': 'EinsteinAI-App/1.0',
                  },
                )
                .timeout(
                  const Duration(seconds: 15),
                  onTimeout:
                      () =>
                          throw TimeoutException(
                            'Zeitüberschreitung beim Herunterladen des Lebenslaufs',
                          ),
                );

            if (response.statusCode == 200 &&
                response.bodyBytes.length > 5 &&
                _isPdfContent(response.bodyBytes)) {
              // Speichere die Datei lokal
              final appDir = await getApplicationDocumentsDirectory();
              final cvDir = Directory('${appDir.path}/$_cvFolderName');

              if (!await cvDir.exists()) {
                await cvDir.create(recursive: true);
              }

              final fileName = '${safeUserId}_$_defaultCvFileName';
              final targetPath = '${cvDir.path}/$fileName';

              final file = File(targetPath);
              await file.writeAsBytes(response.bodyBytes, flush: true);

              debugPrint(
                'Lebenslauf erfolgreich mit HTTP-Fallback heruntergeladen: $targetPath (${response.bodyBytes.length} Bytes)',
              );
              return targetPath;
            } else {
              debugPrint(
                'HTTP-Fallback fehlgeschlagen: ${response.statusCode}',
              );
            }
          } catch (httpError) {
            debugPrint('Fehler beim HTTP-Fallback: $httpError');
          }
        }

        if (attempt < retryCount) {
          await Future.delayed(
            Duration(seconds: 1),
          ); // Kurze Pause vor dem nächsten Versuch
          continue;
        }
        return null;
      }
    }

    return null; // Alle Versuche fehlgeschlagen
  }

  /// Prüft, ob der Inhalt ein PDF ist (einfache Signaturprüfung)
  static bool _isPdfContent(List<int> bytes) {
    if (bytes.length < 5) return false;

    // PDF-Dateien beginnen mit der Signatur "%PDF-"
    final signature = String.fromCharCodes(bytes.sublist(0, 5));
    return signature == "%PDF-";
  }

  /// Stellt sicher, dass der Lebenslauf lokal verfügbar ist
  ///
  /// Prüft, ob der Lebenslauf lokal vorhanden ist, und lädt ihn bei Bedarf herunter
  ///
  /// [userId] ist die ID des Benutzers
  /// [cvFilePath] ist der lokale Pfad zum Lebenslauf (falls bekannt)
  /// [cvDownloadUrl] ist die URL zum Herunterladen des Lebenslaufs
  ///
  /// Gibt den Pfad zum lokalen Lebenslauf zurück oder null bei Fehler
  static Future<String?> ensureLocalCvAvailable(
    String? userId,
    String? cvFilePath,
    String? cvDownloadUrl,
  ) async {
    debugPrint('Stelle sicher, dass Lebenslauf lokal verfügbar ist...');

    // Schritt 1: Prüfe, ob der angegebene lokale Pfad existiert
    if (cvFilePath != null && cvFilePath.isNotEmpty) {
      final file = File(cvFilePath);
      if (await file.exists()) {
        debugPrint('Lebenslauf existiert unter angegebenem Pfad: $cvFilePath');

        // Prüfe, ob die Datei eine verschlüsselte Datei ist
        if (cvFilePath.endsWith('.enc')) {
          debugPrint(
            'Lebenslauf ist verschlüsselt. Versuche zu entschlüsseln...',
          );

          // Versuche, die Datei zu entschlüsseln
          try {
            final secureFileService = SecureFileService();
            final decryptedFile = await secureFileService.decryptFile(
              cvFilePath,
            );

            if (decryptedFile != null) {
              debugPrint(
                'Lebenslauf erfolgreich entschlüsselt: ${decryptedFile.path}',
              );
              return decryptedFile.path;
            } else {
              debugPrint(
                'Entschlüsselung fehlgeschlagen, verwende verschlüsselte Datei',
              );
            }
          } catch (e) {
            debugPrint(
              'Fehler bei der Entschlüsselung: $e. Verwende verschlüsselte Datei.',
            );
          }
        }

        // Prüfe, ob die Datei ein gültiges PDF ist
        try {
          final bytes = await file.readAsBytes();
          if (bytes.length >= 5 && _isPdfContent(bytes)) {
            return cvFilePath;
          } else {
            debugPrint(
              'Datei ist kein gültiges PDF: $cvFilePath. Versuche neu herunterzuladen.',
            );
          }
        } catch (e) {
          debugPrint(
            'Fehler beim Lesen der Datei: $e. Versuche neu herunterzuladen.',
          );
        }
      }
      debugPrint(
        'Lebenslauf existiert nicht unter angegebenem Pfad: $cvFilePath',
      );
    }

    // Schritt 2: Prüfe, ob der Lebenslauf im App-Ordner existiert
    final localAppCvPath = await getLocalCvPath(userId);
    if (localAppCvPath != null) {
      debugPrint('Lebenslauf im lokalen App-Ordner gefunden: $localAppCvPath');

      // Prüfe, ob die Datei eine verschlüsselte Datei ist
      if (localAppCvPath.endsWith('.enc')) {
        debugPrint(
          'Lebenslauf ist verschlüsselt. Versuche zu entschlüsseln...',
        );

        // Versuche, die Datei zu entschlüsseln
        try {
          final secureFileService = SecureFileService();
          final decryptedFile = await secureFileService.decryptFile(
            localAppCvPath,
          );

          if (decryptedFile != null) {
            debugPrint(
              'Lebenslauf erfolgreich entschlüsselt: ${decryptedFile.path}',
            );
            return decryptedFile.path;
          } else {
            debugPrint(
              'Entschlüsselung fehlgeschlagen, verwende verschlüsselte Datei',
            );
          }
        } catch (e) {
          debugPrint(
            'Fehler bei der Entschlüsselung: $e. Verwende verschlüsselte Datei.',
          );
        }
      }

      // Prüfe, ob die Datei ein gültiges PDF ist
      try {
        final file = File(localAppCvPath);
        final bytes = await file.readAsBytes();
        if (bytes.length >= 5 && _isPdfContent(bytes)) {
          return localAppCvPath;
        } else {
          debugPrint(
            'Datei ist kein gültiges PDF: $localAppCvPath. Versuche neu herunterzuladen.',
          );
        }
      } catch (e) {
        debugPrint(
          'Fehler beim Lesen der Datei: $e. Versuche neu herunterzuladen.',
        );
      }
    }

    // Schritt 3: Wenn keine lokale Kopie gefunden wurde, versuche herunterzuladen
    if (cvDownloadUrl != null && cvDownloadUrl.isNotEmpty) {
      debugPrint('Versuche Lebenslauf von URL herunterzuladen: $cvDownloadUrl');
      final downloadedPath = await downloadCvFromUrl(cvDownloadUrl, userId);
      if (downloadedPath != null) {
        debugPrint('Lebenslauf erfolgreich heruntergeladen: $downloadedPath');

        // Prüfe, ob die Datei eine verschlüsselte Datei ist
        if (downloadedPath.endsWith('.enc')) {
          debugPrint(
            'Heruntergeladener Lebenslauf ist verschlüsselt. Versuche zu entschlüsseln...',
          );

          // Versuche, die Datei zu entschlüsseln
          try {
            final secureFileService = SecureFileService();
            final decryptedFile = await secureFileService.decryptFile(
              downloadedPath,
            );

            if (decryptedFile != null) {
              debugPrint(
                'Lebenslauf erfolgreich entschlüsselt: ${decryptedFile.path}',
              );
              return decryptedFile.path;
            } else {
              debugPrint(
                'Entschlüsselung fehlgeschlagen, verwende verschlüsselte Datei',
              );
            }
          } catch (e) {
            debugPrint(
              'Fehler bei der Entschlüsselung: $e. Verwende verschlüsselte Datei.',
            );
          }
        }

        return downloadedPath;
      }
      debugPrint('Fehler beim Herunterladen des Lebenslaufs von URL');
    }

    debugPrint('Kein Lebenslauf verfügbar (weder lokal noch über URL)');
    return null;
  }

  /// Löscht den lokalen Lebenslauf
  ///
  /// [userId] ist die ID des Benutzers
  static Future<bool> deleteLocalCv(String? userId) async {
    // Fallback, wenn keine userId vorhanden ist
    final String safeUserId = userId ?? 'unknown_user';
    bool success = true;

    try {
      // Lösche den sicher gespeicherten Lebenslauf
      try {
        final secureFileService = SecureFileService();
        final secureSuccess = await secureFileService.deleteSecureCv(
          safeUserId,
        );
        if (secureSuccess) {
          debugPrint('Sicher gespeicherter Lebenslauf erfolgreich gelöscht');
        } else {
          debugPrint(
            'Fehler beim Löschen des sicher gespeicherten Lebenslaufs',
          );
          success = false;
        }
      } catch (secureError) {
        debugPrint(
          'Fehler beim Löschen des sicheren Lebenslaufs: $secureError',
        );
        success = false;
      }

      // Lösche auch den unverschlüsselten Lebenslauf (falls vorhanden)
      try {
        final appDir = await getApplicationDocumentsDirectory();
        final cvDir = Directory('${appDir.path}/$_cvFolderName');

        // Prüfe, ob der Ordner existiert
        if (!await cvDir.exists()) {
          return success; // Nichts zu löschen
        }

        // Prüfe, ob die Datei existiert
        final filePath = '${cvDir.path}/${safeUserId}_$_defaultCvFileName';
        final file = File(filePath);

        if (await file.exists()) {
          await file.delete();
          debugPrint(
            'Unverschlüsselter lokaler Lebenslauf gelöscht: $filePath',
          );
        }
      } catch (e) {
        debugPrint('Fehler beim Löschen des unverschlüsselten Lebenslaufs: $e');
        success = false;
      }

      return success;
    } catch (e) {
      debugPrint('Fehler beim Löschen des lokalen Lebenslaufs: $e');
      return false;
    }
  }

  /// Erstellt eine temporäre Kopie der CV-Datei mit dem gewünschten Namen
  ///
  /// [sourcePath] ist der Pfad zur ursprünglichen CV-Datei
  /// [desiredFileName] ist der gewünschte Dateiname (z.B. 'Lebenslauf.pdf')
  ///
  /// Gibt den Pfad zur temporären Datei zurück oder null bei Fehler
  static Future<String?> createTemporaryCvCopy(
    String sourcePath,
    String desiredFileName,
  ) async {
    try {
      // Prüfe, ob die Quelldatei existiert
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        debugPrint('Quelldatei existiert nicht: $sourcePath');
        return null;
      }

      // Erstelle temporären Ordner
      final tempDir = await getTemporaryDirectory();
      final tempCvDir = Directory('${tempDir.path}/temp_cv');
      
      // Erstelle den Ordner, falls er nicht existiert
      if (!await tempCvDir.exists()) {
        await tempCvDir.create(recursive: true);
      }

      // Erstelle den Zielpfad mit dem gewünschten Dateinamen
      final targetPath = '${tempCvDir.path}/$desiredFileName';
      
      // Kopiere die Datei
      await sourceFile.copy(targetPath);
      
      debugPrint('Temporäre CV-Kopie erstellt: $targetPath');
      return targetPath;
    } catch (e) {
      debugPrint('Fehler beim Erstellen der temporären CV-Kopie: $e');
      return null;
    }
  }

  /// Löscht eine temporäre CV-Datei
  ///
  /// [tempPath] ist der Pfad zur temporären Datei
  static Future<bool> deleteTemporaryCvCopy(String tempPath) async {
    try {
      final tempFile = File(tempPath);
      if (await tempFile.exists()) {
        await tempFile.delete();
        debugPrint('Temporäre CV-Datei gelöscht: $tempPath');
        return true;
      }
      return true; // Datei existiert nicht, also "erfolgreich" gelöscht
    } catch (e) {
      debugPrint('Fehler beim Löschen der temporären CV-Datei: $e');
      return false;
    }
  }
}

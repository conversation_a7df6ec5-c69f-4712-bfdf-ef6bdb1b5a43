import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/job_entity.dart';
import 'text_extraction_service.dart';
import '../../core/logging/app_logger.dart';

/// Service für Bulk-Text-Extraktion aller bestehenden Favoriten und beworbenen Jobs
class BulkTextExtractionService {
  final SupabaseClient _supabaseClient;
  final TextExtractionService _textExtractionService;
  final AppLogger _log = AppLogger('BulkTextExtractionService');
  
  // Status-Tracking
  bool _isRunning = false;
  int _totalJobs = 0;
  int _processedJobs = 0;
  int _successfulExtractions = 0;
  int _failedExtractions = 0;
  
  // Stream für Progress-Updates
  final StreamController<BulkExtractionProgress> _progressController = 
      StreamController<BulkExtractionProgress>.broadcast();
  
  Stream<BulkExtractionProgress> get progressStream => _progressController.stream;

  BulkTextExtractionService(this._supabaseClient, this._textExtractionService);

  /// Startet Bulk-Extraktion für alle bestehenden Favoriten und beworbenen Jobs
  Future<BulkExtractionResult> extractAllExistingJobs(String userId) async {
    if (_isRunning) {
      _log.w('Bulk-Extraktion läuft bereits');
      return BulkExtractionResult(
        success: false,
        message: 'Bulk-Extraktion läuft bereits',
        totalJobs: 0,
        processedJobs: 0,
        successfulExtractions: 0,
        failedExtractions: 0,
      );
    }

    _isRunning = true;
    _resetCounters();
    
    try {
      _log.i('Starte Bulk-Text-Extraktion für User: $userId');
      
      // 1. Lade alle Favoriten
      final favoriteJobs = await _loadFavoriteJobs(userId);
      _log.i('${favoriteJobs.length} Favoriten gefunden');
      
      // 2. Lade alle beworbenen Jobs
      final appliedJobs = await _loadAppliedJobs(userId);
      _log.i('${appliedJobs.length} beworbene Jobs gefunden');
      
      // 3. Kombiniere und dedupliziere Jobs
      final allJobs = _deduplicateJobs([...favoriteJobs, ...appliedJobs]);
      _totalJobs = allJobs.length;
      
      _log.i('Insgesamt $_totalJobs einzigartige Jobs für Text-Extraktion');
      
      if (_totalJobs == 0) {
        return BulkExtractionResult(
          success: true,
          message: 'Keine Jobs für Extraktion gefunden',
          totalJobs: 0,
          processedJobs: 0,
          successfulExtractions: 0,
          failedExtractions: 0,
        );
      }
      
      // 4. Filtere bereits extrahierte Jobs
      final jobsToExtract = await _filterAlreadyExtracted(allJobs);
      _log.i('${jobsToExtract.length} Jobs benötigen Text-Extraktion');
      
      // 5. Starte parallele Extraktion (max 3 gleichzeitig)
      await _extractJobsInBatches(jobsToExtract, batchSize: 3);
      
      final result = BulkExtractionResult(
        success: true,
        message: 'Bulk-Extraktion abgeschlossen',
        totalJobs: _totalJobs,
        processedJobs: _processedJobs,
        successfulExtractions: _successfulExtractions,
        failedExtractions: _failedExtractions,
      );
      
      _log.i('Bulk-Extraktion abgeschlossen: ${result.toString()}');
      return result;
      
    } catch (e, stackTrace) {
      _log.e('Fehler bei Bulk-Extraktion: $e', stackTrace);
      return BulkExtractionResult(
        success: false,
        message: 'Fehler bei Bulk-Extraktion: $e',
        totalJobs: _totalJobs,
        processedJobs: _processedJobs,
        successfulExtractions: _successfulExtractions,
        failedExtractions: _failedExtractions,
      );
    } finally {
      _isRunning = false;
    }
  }

  /// Lädt alle Favoriten-Jobs für einen User
  Future<List<JobEntity>> _loadFavoriteJobs(String userId) async {
    try {
      final response = await _supabaseClient
          .from('user_favorites')
          .select('job_data')
          .eq('user_id', userId);

      final jobs = <JobEntity>[];
      for (final row in response) {
        try {
          final jobData = row['job_data'] as Map<String, dynamic>;
          final job = JobEntity.fromJson(jobData);
          jobs.add(job);
        } catch (e) {
          _log.w('Fehler beim Parsen von Favoriten-Job: $e');
        }
      }
      
      return jobs;
    } catch (e) {
      _log.e('Fehler beim Laden der Favoriten: $e');
      return [];
    }
  }

  /// Lädt alle beworbenen Jobs für einen User
  Future<List<JobEntity>> _loadAppliedJobs(String userId) async {
    try {
      // Lade Job-IDs aus applied_jobs
      final appliedJobIds = await _supabaseClient
          .from('applied_jobs')
          .select('job_id')
          .eq('user_id', userId);

      final jobIds = appliedJobIds.map((row) => row['job_id'] as String).toList();
      
      if (jobIds.isEmpty) return [];
      
      // Lade Job-Daten aus jobs Tabelle
      final jobsResponse = await _supabaseClient
          .from('jobs')
          .select('*')
          .inFilter('id', jobIds);

      final jobs = <JobEntity>[];
      for (final row in jobsResponse) {
        try {
          final job = JobEntity.fromJson(row);
          jobs.add(job);
        } catch (e) {
          _log.w('Fehler beim Parsen von beworbenen Job: $e');
        }
      }
      
      return jobs;
    } catch (e) {
      _log.e('Fehler beim Laden der beworbenen Jobs: $e');
      return [];
    }
  }

  /// Entfernt Duplikate basierend auf Job-ID
  List<JobEntity> _deduplicateJobs(List<JobEntity> jobs) {
    final uniqueJobs = <String, JobEntity>{};
    for (final job in jobs) {
      uniqueJobs[job.id] = job;
    }
    return uniqueJobs.values.toList();
  }

  /// Filtert Jobs, die bereits extrahiert wurden
  Future<List<JobEntity>> _filterAlreadyExtracted(List<JobEntity> jobs) async {
    try {
      final jobIds = jobs.map((job) => job.id).toList();
      
      final existingExtractions = await _supabaseClient
          .from('job_text_cache')
          .select('job_id')
          .inFilter('job_id', jobIds);

      final extractedJobIds = existingExtractions
          .map((row) => row['job_id'] as String)
          .toSet();

      return jobs.where((job) => !extractedJobIds.contains(job.id)).toList();
    } catch (e) {
      _log.w('Fehler beim Filtern bereits extrahierter Jobs: $e');
      return jobs; // Im Fehlerfall alle Jobs verarbeiten
    }
  }

  /// Extrahiert Jobs in Batches für bessere Performance
  Future<void> _extractJobsInBatches(List<JobEntity> jobs, {int batchSize = 3}) async {
    for (int i = 0; i < jobs.length; i += batchSize) {
      final batch = jobs.skip(i).take(batchSize).toList();
      
      // Parallele Verarbeitung des Batches
      final futures = batch.map((job) => _extractSingleJob(job)).toList();
      await Future.wait(futures);
      
      // Progress-Update senden
      _sendProgressUpdate();
      
      // Kurze Pause zwischen Batches um Server nicht zu überlasten
      if (i + batchSize < jobs.length) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  /// Extrahiert Text für einen einzelnen Job
  Future<void> _extractSingleJob(JobEntity job) async {
    try {
      _log.d('Extrahiere Text für Job: ${job.id} - ${job.title}');
      
      final extractedText = await _textExtractionService.extractAndCacheJobText(job);
      
      if (extractedText != null) {
        _successfulExtractions++;
        _log.d('Text erfolgreich extrahiert für Job: ${job.id} (${extractedText.getSizeInKB().toStringAsFixed(1)}KB)');
      } else {
        _failedExtractions++;
        _log.w('Text-Extraktion fehlgeschlagen für Job: ${job.id}');
      }
    } catch (e) {
      _failedExtractions++;
      _log.e('Fehler bei Text-Extraktion für Job ${job.id}: $e');
    } finally {
      _processedJobs++;
    }
  }

  /// Sendet Progress-Update über Stream
  void _sendProgressUpdate() {
    final progress = BulkExtractionProgress(
      totalJobs: _totalJobs,
      processedJobs: _processedJobs,
      successfulExtractions: _successfulExtractions,
      failedExtractions: _failedExtractions,
      isCompleted: _processedJobs >= _totalJobs,
    );
    
    _progressController.add(progress);
  }

  /// Setzt alle Zähler zurück
  void _resetCounters() {
    _totalJobs = 0;
    _processedJobs = 0;
    _successfulExtractions = 0;
    _failedExtractions = 0;
  }

  /// Prüft ob Bulk-Extraktion läuft
  bool get isRunning => _isRunning;

  /// Dispose-Methode
  void dispose() {
    _progressController.close();
  }
}

/// Progress-Daten für Bulk-Extraktion
class BulkExtractionProgress {
  final int totalJobs;
  final int processedJobs;
  final int successfulExtractions;
  final int failedExtractions;
  final bool isCompleted;

  const BulkExtractionProgress({
    required this.totalJobs,
    required this.processedJobs,
    required this.successfulExtractions,
    required this.failedExtractions,
    required this.isCompleted,
  });

  double get progressPercentage => 
      totalJobs > 0 ? (processedJobs / totalJobs) * 100 : 0;

  @override
  String toString() {
    return 'BulkExtractionProgress($processedJobs/$totalJobs, ${progressPercentage.toStringAsFixed(1)}%, Erfolg: $successfulExtractions, Fehler: $failedExtractions)';
  }
}

/// Ergebnis der Bulk-Extraktion
class BulkExtractionResult {
  final bool success;
  final String message;
  final int totalJobs;
  final int processedJobs;
  final int successfulExtractions;
  final int failedExtractions;

  const BulkExtractionResult({
    required this.success,
    required this.message,
    required this.totalJobs,
    required this.processedJobs,
    required this.successfulExtractions,
    required this.failedExtractions,
  });

  @override
  String toString() {
    return 'BulkExtractionResult(success: $success, total: $totalJobs, processed: $processedJobs, success: $successfulExtractions, failed: $failedExtractions)';
  }
}

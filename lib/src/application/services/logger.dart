import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Ein einfacher Logger für strukturierte Protokollierung in der Anwendung
class Logger {
  /// Der Klassenname oder Tag, der in den Logs angezeigt wird
  final String _tag;
  
  /// Konstruktor
  /// 
  /// [tag] Der Klassenname oder Tag, der für die Protokollierung verwendet wird
  Logger(this._tag);
  
  /// Protokolliert eine Debug-Nachricht
  /// 
  /// [message] Die zu protokollierende Nachricht
  void d(String message) {
    if (kDebugMode) {
      developer.log('📘 $message', name: _tag);
    }
  }
  
  /// Protokolliert eine Info-Nachricht
  /// 
  /// [message] Die zu protokollierende Nachricht
  void i(String message) {
    developer.log('📗 $message', name: _tag);
  }
  
  /// Protokolliert eine Warnmeldung
  /// 
  /// [message] Die zu protokollierende Nachricht
  void w(String message) {
    developer.log('📙 $message', name: _tag);
  }
  
  /// Protokolliert eine Fehlermeldung
  /// 
  /// [message] Die zu protokollierende Nachricht
  /// [error] Der aufgetretene Fehler
  /// [stackTrace] Der Stacktrace des Fehlers
  void e(String message, {dynamic error, StackTrace? stackTrace}) {
    final errorMsg = error != null ? '\nError: $error' : '';
    final stackMsg = stackTrace != null ? '\nStackTrace: $stackTrace' : '';
    developer.log('❌ $message$errorMsg$stackMsg', name: _tag, error: error, stackTrace: stackTrace);
  }
  
  /// Protokolliert eine wichtige Nachricht
  /// 
  /// [message] Die zu protokollierende Nachricht
  void important(String message) {
    developer.log('‼️ $message', name: _tag);
  }
} 
 
 
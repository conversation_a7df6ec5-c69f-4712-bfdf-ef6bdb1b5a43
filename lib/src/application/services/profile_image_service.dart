import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service für Profilbild-Upload und -Verwaltung
class ProfileImageService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final ImagePicker _imagePicker = ImagePicker();

  static const String _bucketName = 'profile-images';
  static const int _maxImageSize = 1024; // Max Breite/Höhe in Pixeln
  static const int _maxFileSizeBytes = 2 * 1024 * 1024; // 2MB

  /// Wählt ein Bild aus der Galerie oder Kamera aus
  Future<XFile?> pickImage({ImageSource source = ImageSource.gallery}) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: _maxImageSize.toDouble(),
        maxHeight: _maxImageSize.toDouble(),
        imageQuality: 85,
      );

      if (image == null) return null;

      // Überprüfe Dateigröße
      final bytes = await image.readAsBytes();
      if (bytes.length > _maxFileSizeBytes) {
        throw Exception('Bild ist zu groß. Maximale Größe: 2MB');
      }

      return image;
    } catch (e) {
      debugPrint('🚨 Fehler beim Bildauswahl: $e');
      rethrow;
    }
  }

  /// Lädt ein Profilbild hoch und aktualisiert das Profil
  Future<String> uploadProfileImage(XFile imageFile, String userId) async {
    try {
      debugPrint('📸 Lade Profilbild hoch für User: $userId');

      // Überprüfe Parameter
      if (userId.isEmpty) {
        throw Exception('User-ID ist leer');
      }

      // Lese Bilddaten
      final bytes = await imageFile.readAsBytes();
      if (bytes.isEmpty) {
        throw Exception('Bilddatei ist leer');
      }

      // Komprimiere und optimiere das Bild
      final optimizedBytes = await _optimizeImage(bytes);

      // Erstelle Dateistruktur: userId/profile_timestamp.jpg
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '$userId/profile_$timestamp.jpg';

      debugPrint(
        '📸 Dateiname: $fileName, Größe: ${optimizedBytes.length} bytes',
      );

      // Lösche altes Profilbild falls vorhanden
      await _deleteOldProfileImage(userId);

      // Lade neues Bild hoch
      try {
        final response = await _supabase.storage
            .from(_bucketName)
            .uploadBinary(fileName, optimizedBytes);

        debugPrint('📸 Upload-Response: $response');
      } catch (uploadError) {
        debugPrint('🚨 Upload-Fehler: $uploadError');

        // Versuche Bucket zu erstellen falls er nicht existiert
        try {
          await _supabase.storage.createBucket(
            _bucketName,
            BucketOptions(public: true),
          );
          debugPrint('📸 Bucket $_bucketName erstellt');

          // Versuche Upload erneut
          await _supabase.storage
              .from(_bucketName)
              .uploadBinary(fileName, optimizedBytes);
        } catch (bucketError) {
          debugPrint('🚨 Bucket-Fehler: $bucketError');
          rethrow;
        }
      }

      // Erstelle öffentliche URL
      final publicUrl = _supabase.storage
          .from(_bucketName)
          .getPublicUrl(fileName);

      debugPrint('✅ Profilbild erfolgreich hochgeladen: $publicUrl');
      return publicUrl;
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Upload: $e');
      rethrow;
    }
  }

  /// Optimiert das Bild (Komprimierung, Größenanpassung)
  Future<Uint8List> _optimizeImage(Uint8List bytes) async {
    try {
      // Dekodiere das Bild
      img.Image? image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Bild konnte nicht dekodiert werden');
      }

      // Berechne neue Größe (quadratisch, max 512x512)
      int size = 512;
      if (image.width > image.height) {
        size = image.width > size ? size : image.width;
      } else {
        size = image.height > size ? size : image.height;
      }

      // Resize das Bild (quadratisch zuschneiden)
      img.Image resized = img.copyResizeCropSquare(image, size: size);

      // Konvertiere zu JPEG mit Komprimierung
      final optimizedBytes = img.encodeJpg(resized, quality: 85);

      debugPrint(
        '📸 Bild optimiert: ${bytes.length} -> ${optimizedBytes.length} bytes',
      );
      return Uint8List.fromList(optimizedBytes);
    } catch (e) {
      debugPrint('🚨 Fehler bei Bildoptimierung: $e');
      // Fallback: Originalbild zurückgeben
      return bytes;
    }
  }

  /// Löscht das alte Profilbild eines Users
  Future<void> _deleteOldProfileImage(String userId) async {
    try {
      // Liste alle Dateien im Bucket
      final response = await _supabase.storage.from(_bucketName).list();

      // Filtere Dateien des Users (neue Struktur: userId/profile_*.jpg)
      final userFiles =
          response.where((file) => file.name.startsWith('$userId/')).toList();

      for (final file in userFiles) {
        await _supabase.storage.from(_bucketName).remove([file.name]);
        debugPrint('🗑️ Altes Profilbild gelöscht: ${file.name}');
      }
    } catch (e) {
      debugPrint('⚠️ Fehler beim Löschen alter Profilbilder: $e');
      // Nicht kritisch, weiter machen
    }
  }

  /// Aktualisiert das Profil mit der neuen Profilbild-URL
  Future<void> updateProfileWithImage(String userId, String imageUrl) async {
    try {
      debugPrint('📝 Aktualisiere Profil mit Profilbild-URL: $imageUrl');

      await _supabase
          .from('profiles')
          .update({
            'profile_image_url': imageUrl,
            'profile_image_last_modified': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      debugPrint('✅ Profil erfolgreich mit Profilbild aktualisiert');
    } catch (e) {
      debugPrint('🚨 Fehler beim Profil-Update: $e');
      rethrow;
    }
  }

  /// Löscht das Profilbild eines Users
  Future<void> deleteProfileImage(String userId) async {
    try {
      debugPrint('🗑️ Lösche Profilbild für User: $userId');

      // Lösche Dateien aus Storage
      await _deleteOldProfileImage(userId);

      // Entferne URL aus Profil
      await _supabase
          .from('profiles')
          .update({
            'profile_image_url': null,
            'profile_image_last_modified': null,
          })
          .eq('id', userId);

      debugPrint('✅ Profilbild erfolgreich gelöscht');
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Löschen: $e');
      rethrow;
    }
  }

  /// Lädt ein Profilbild herunter (für lokale Verwendung)
  Future<Uint8List?> downloadProfileImage(String imageUrl) async {
    try {
      debugPrint('📥 Lade Profilbild herunter: $imageUrl');

      // Prüfe ob es eine Supabase Storage URL ist
      if (imageUrl.contains('/storage/v1/object/public/')) {
        // Extrahiere den Pfad nach dem Bucket-Namen
        final parts = imageUrl.split('/storage/v1/object/public/$_bucketName/');
        if (parts.length != 2) {
          debugPrint('🚨 Ungültige Supabase Storage URL: $imageUrl');
          return null;
        }

        final fileName = parts[1]; // Der komplette Pfad nach dem Bucket
        debugPrint('📥 Extrahierter Dateiname: $fileName');

        final bytes = await _supabase.storage
            .from(_bucketName)
            .download(fileName);

        debugPrint(
          '✅ Profilbild erfolgreich heruntergeladen (${bytes.length} bytes)',
        );
        return bytes;
      } else {
        // Fallback: Versuche direkten HTTP-Download
        debugPrint('📥 Versuche direkten HTTP-Download: $imageUrl');

        final response = await http.get(Uri.parse(imageUrl));
        if (response.statusCode == 200) {
          debugPrint(
            '✅ Profilbild per HTTP heruntergeladen (${response.bodyBytes.length} bytes)',
          );
          return response.bodyBytes;
        } else {
          debugPrint('🚨 HTTP-Download fehlgeschlagen: ${response.statusCode}');
          return null;
        }
      }
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Download: $e');
      return null;
    }
  }

  /// Überprüft ob ein Profilbild existiert
  Future<bool> hasProfileImage(String userId) async {
    try {
      final response = await _supabase.storage.from(_bucketName).list();

      final userFiles =
          response.where((file) => file.name.startsWith('$userId/')).toList();

      return userFiles.isNotEmpty;
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Check: $e');
      return false;
    }
  }

  /// Erstellt eine Vorschau-URL für das Profilbild
  String? getProfileImagePreviewUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) return null;

    // Füge Transformation für kleinere Vorschau hinzu (falls Supabase Transform unterstützt)
    return imageUrl;
  }
}

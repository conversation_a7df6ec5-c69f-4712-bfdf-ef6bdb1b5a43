import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Fehlerklasse für Übersetzungsfehler
class TranslationError extends Error {
  final String message;
  TranslationError(this.message);

  @override
  String toString() => 'TranslationError: $message';
}

/// Callback-Typ für Übersetzungs-Updates
typedef TranslationProgressCallback = void Function(String partialTranslation);

/// Service für die Übersetzung von Texten
/// Verwendet die Supabase Edge Function 'translate-with-deepseek'
class TranslationService {
  final SupabaseClient _client;
  final _log = getLogger('TranslationService');

  /// Konstruktor
  TranslationService(this._client);

  /// Übersetzt einen Text in die Zielsprache
  ///
  /// [text] Der zu übersetzende Text
  /// [targetLang] Die Zielsprache (ISO-639-1 Code, z.B. 'de', 'en')
  /// [sourceLang] Optional: Die Quellsprache (ISO-639-1 Code)
  /// [onProgress] Optional: Callback für Fortschrittsupdates
  /// [useStreaming] Optional: Wird ignoriert, da Streaming deaktiviert ist
  ///
  /// Gibt den übersetzten Text zurück oder wirft einen [TranslationError]
  Future<String> translateText({
    required String text,
    required String targetLang,
    String? sourceLang,
    TranslationProgressCallback? onProgress,
    bool useStreaming = false, // Wird ignoriert
  }) async {
    _log.i('Rufe Supabase Function "translate-with-deepseek" auf...');

    if (text.isEmpty) {
      _log.w('Leerer Text an translateText übergeben.');
      return '';
    }

    try {
      // Wenn ein Callback vorhanden ist, simuliere Fortschrittsupdates
      if (onProgress != null) {
        // Zeige einen initialen Ladetext an
        onProgress('Übersetzung wird gestartet...');

        // Starte einen Timer, der regelmäßig Fortschrittsupdates mit Prozentsatz sendet
        int progressPercent = 0;
        final timer = Timer.periodic(const Duration(milliseconds: 300), (
          timer,
        ) {
          // Simuliere einen realistischen Fortschritt
          if (progressPercent < 95) {
            progressPercent += 5;
            if (progressPercent > 90) {
              // Langsamer am Ende
              progressPercent += 1;
            } else if (progressPercent > 80) {
              progressPercent += 2;
            } else if (progressPercent > 50) {
              progressPercent += 3;
            }

            // Begrenze auf 95%, die letzten 5% werden nach Abschluss hinzugefügt
            progressPercent = progressPercent.clamp(0, 95);

            // Erstelle einen Fortschrittstext mit Prozentangabe
            final progressText = "Übersetzung läuft... $progressPercent%";
            onProgress(progressText);
          }
        });

        // Stelle sicher, dass der Timer gestoppt wird, wenn die Funktion beendet wird
        try {
          final response = await _sendTranslationRequest(
            text,
            targetLang,
            sourceLang,
          );
          timer.cancel();

          // Simuliere eine Wort-für-Wort-Übersetzung mit dem fertigen Text
          await _simulateWordByWordTranslation(response, onProgress);

          return response;
        } finally {
          timer.cancel();
        }
      } else {
        // Wenn kein Callback vorhanden ist, verwende die normale Anfrage
        return await _sendTranslationRequest(text, targetLang, sourceLang);
      }
    } catch (e, stackTrace) {
      if (e is TranslationError) {
        rethrow;
      }
      _log.e(
        'Fehler beim Aufruf der Function "translate-with-deepseek"',
        error: e,
        stackTrace: stackTrace,
      );
      throw TranslationError('Fehler bei der Übersetzung: ${e.toString()}');
    }
  }

  /// Simuliert eine Wort-für-Wort-Übersetzung für eine bessere Benutzererfahrung
  Future<void> _simulateWordByWordTranslation(
    String translatedText,
    TranslationProgressCallback onProgress,
  ) async {
    _log.i('Simuliere Wort-für-Wort-Übersetzung...');

    // Teile den Text in Wörter auf
    final List<String> words = translatedText.split(' ');

    // Simuliere eine Wort-für-Wort-Übersetzung
    String currentTranslation = '';

    // Berechne die Verzögerung basierend auf der Textlänge
    final int totalWords = words.length;

    // Dynamische Verzögerung basierend auf der Textlänge
    int baseDelay;
    if (totalWords < 20) {
      baseDelay = 80; // Langsamer für kurze Texte
    } else if (totalWords < 50) {
      baseDelay = 50;
    } else if (totalWords < 100) {
      baseDelay = 30;
    } else if (totalWords < 200) {
      baseDelay = 20;
    } else {
      baseDelay = 10; // Sehr schnell für sehr lange Texte
    }

    // Berechne, wie viele Wörter pro Update gesendet werden sollen
    final int wordsPerUpdate = totalWords < 50 ? 1 : (totalWords < 150 ? 2 : 3);

    // Füge Wörter nacheinander hinzu mit einer Verzögerung
    for (int i = 0; i < words.length; i += wordsPerUpdate) {
      // Füge die nächsten Wörter hinzu
      for (int j = 0; j < wordsPerUpdate && i + j < words.length; j++) {
        if (currentTranslation.isNotEmpty) currentTranslation += ' ';
        currentTranslation += words[i + j];
      }

      // Sende den aktuellen Stand
      onProgress(currentTranslation);

      // Warte kurz, bevor die nächsten Wörter hinzugefügt werden
      // Verwende eine abnehmende Verzögerung für längere Texte
      final int adjustedDelay =
          baseDelay - ((i / words.length) * (baseDelay / 3)).round();
      await Future.delayed(Duration(milliseconds: adjustedDelay));
    }

    _log.i(
      'Wort-für-Wort-Übersetzung abgeschlossen (${translatedText.length} Zeichen)',
    );

    // Sende ein letztes Update mit dem vollständigen Text
    onProgress(translatedText);
  }

  /// Sendet die Übersetzungsanfrage an die Supabase Edge Function (nicht-Streaming)
  Future<String> _sendTranslationRequest(
    String text,
    String targetLang,
    String? sourceLang,
  ) async {
    final response = await _client.functions.invoke(
      'translate-with-deepseek',
      body: {
        'text': text,
        'targetLang': targetLang,
        if (sourceLang != null) 'sourceLang': sourceLang,
        'stream': false, // Deaktiviere Streaming explizit
      },
    );

    _log.i(
      'Supabase Function "translate-with-deepseek" Antwort Status: ${response.status}',
    );

    if (response.status != 200) {
      String errorMessage = 'Unbekannter Fehler bei der Übersetzung.';
      if (response.data is Map && response.data['error'] != null) {
        errorMessage = response.data['error'] as String;
      }
      _log.e('Fehler bei der Übersetzung: $errorMessage');
      throw TranslationError(errorMessage);
    }

    if (response.data is! Map || response.data['translatedText'] == null) {
      _log.e('Ungültiges Antwortformat: ${response.data}');
      throw TranslationError(
        'Ungültiges Antwortformat von der Übersetzungs-API.',
      );
    }

    final translatedText = response.data['translatedText'] as String;
    _log.i(
      'Text erfolgreich übersetzt (${text.length} -> ${translatedText.length} Zeichen)',
    );

    return translatedText;
  }

  /// Erkennt die Sprache eines Textes
  ///
  /// [text] Der Text, dessen Sprache erkannt werden soll
  ///
  /// Gibt den ISO-639-1 Sprachcode zurück (z.B. 'de', 'en')
  Future<String> detectLanguage(String text) async {
    _log.i('Erkenne Sprache...');

    if (text.isEmpty) {
      _log.w('Leerer Text an detectLanguage übergeben.');
      return 'de'; // Standardsprache
    }

    try {
      // Wir verwenden die Übersetzungsfunktion mit einem kurzen Textausschnitt
      // und lassen die Quellsprache erkennen
      final sample = text.length > 100 ? text.substring(0, 100) : text;

      final response = await _client.functions.invoke(
        'translate-with-deepseek',
        body: {
          'text': sample,
          'targetLang':
              'de', // Zielsprache ist egal, wir wollen nur die Erkennung
        },
      );

      if (response.status != 200) {
        _log.e('Fehler bei der Spracherkennung: ${response.data}');
        return 'de'; // Standardsprache im Fehlerfall
      }

      if (response.data is! Map ||
          response.data['detectedSourceLanguage'] == null) {
        _log.e(
          'Ungültiges Antwortformat bei Spracherkennung: ${response.data}',
        );
        return 'de'; // Standardsprache im Fehlerfall
      }

      final detectedLang = response.data['detectedSourceLanguage'] as String;
      _log.i('Sprache erkannt: $detectedLang');

      return detectedLang.toLowerCase();
    } catch (e, stackTrace) {
      _log.e(
        'Fehler bei der Spracherkennung',
        error: e,
        stackTrace: stackTrace,
      );
      return 'de'; // Standardsprache im Fehlerfall
    }
  }
}

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import '../../domain/models/user_profile.dart';
import '../../domain/models/cv_template.dart';
import 'profile_image_service.dart';

/// Service für PDF-CV-Generierung
class PdfCvGeneratorService {
  final ProfileImageService _profileImageService = ProfileImageService();
  final Logger _log = Logger();

  // Cache für Schriftarten um wiederholte Ladevorgänge zu vermeiden
  static final Map<String, pw.Font> _fontCache = {};

  // Cache für Profilbilder
  static final Map<String, pw.ImageProvider> _imageCache = {};

  /// Generiert PDF-CV basierend auf Template
  Future<Uint8List> generateCvPdf(
    UserProfile profile,
    CvTemplate template,
  ) async {
    print(
      '🔥 PDF-CV-Generator: Starte Generierung für Template ${template.name}',
    );

    final pdf = pw.Document();

    // Lade Schriftarten
    final fontRegular = await _loadFont('Roboto-Regular.ttf');
    final fontBold = await _loadFont('Roboto-Bold.ttf');

    // Generiere PDF basierend auf Template-Typ
    switch (template.type) {
      case CvTemplateType.classic:
        await _generateClassicTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.modern:
        await _generateModernTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.creative:
        await _generateCanvaTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.minimalist:
        await _generateMinimalistTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;

      case CvTemplateType.timeline:
        await _generateClassicTemplate(
          // Temporär Classic verwenden
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.executive:
        await _generateExecutiveTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.infographic:
        await _generateInfographicTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.grid:
        await _generateGridTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.technical:
        await _generateClassicTemplate(
          // Temporär Classic verwenden
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.magazine:
        await _generateMagazineTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.infographic:
        await _generateInfographicTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.artistic:
        await _generateArtisticTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.startup:
        await _generateStartupTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.academic:
        await _generateAcademicTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      case CvTemplateType.fresh:
        await _generateFreshTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
      default:
        await _generateClassicTemplate(
          pdf,
          profile,
          template,
          fontRegular,
          fontBold,
        );
        break;
    }

    final pdfBytes = await pdf.save();
    print(
      '✅ PDF-CV-Generator: PDF erfolgreich generiert (${pdfBytes.length} bytes)',
    );
    return pdfBytes;
  }

  /// Lädt Schriftart (mit Cache für Performance)
  Future<pw.Font> _loadFont(String fontName) async {
    // Prüfe Cache zuerst
    if (_fontCache.containsKey(fontName)) {
      return _fontCache[fontName]!;
    }

    try {
      final fontData = pw.Font.ttf(
        await DefaultAssetBundle.of(
          WidgetsBinding.instance.rootElement!,
        ).load('assets/fonts/$fontName'),
      );
      _fontCache[fontName] = fontData; // Cache speichern
      return fontData;
    } catch (e) {
      print(
        '✅ PDF-CV-Generator: Schriftart $fontName nicht in Assets gefunden, verwende Google Fonts mit Unicode-Support',
      );
      // Verwende Standard-Schriftart als Fallback
      final fallbackFont = pw.Font.helvetica();
      _fontCache[fontName] = fallbackFont; // Cache auch Fallback
      return fallbackFont;
    }
  }

  /// Generiert klassisches Template
  Future<void> _generateClassicTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header mit Name und Kontaktdaten
              _buildClassicHeader(
                profile,
                template,
                fontBold,
                fontRegular,
                profileImage,
              ),
              pw.SizedBox(height: 20),

              // Berufserfahrung
              if (profile.workExperience?.isNotEmpty == true) ...[
                _buildSectionTitle('BERUFSERFAHRUNG', template, fontBold),
                pw.SizedBox(height: 10),
                ...profile.workExperience!.map(
                  (exp) => _buildWorkExperience(exp, fontRegular, fontBold),
                ),
                pw.SizedBox(height: 20),
              ],

              // Ausbildung
              if (profile.education?.isNotEmpty == true) ...[
                _buildSectionTitle('AUSBILDUNG', template, fontBold),
                pw.SizedBox(height: 10),
                ...profile.education!.map(
                  (edu) => _buildEducation(edu, fontRegular, fontBold),
                ),
                pw.SizedBox(height: 20),
              ],

              // Skills
              if (profile.skills?.isNotEmpty == true) ...[
                _buildSectionTitle('FÄHIGKEITEN', template, fontBold),
                pw.SizedBox(height: 10),
                _buildSkills(profile.skills!, fontRegular),
              ],
            ],
          );
        },
      ),
    );
  }

  /// Generiert modernes Template
  Future<void> _generateModernTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Linke Spalte (30%)
              pw.Expanded(
                flex: 3,
                child: pw.Container(
                  color: _colorToPdfColor(template.colorScheme.secondaryColor),
                  padding: const pw.EdgeInsets.all(20),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // Echtes Profilbild oder Platzhalter
                      _buildProfileImageWidget(profileImage, size: 80),
                      pw.SizedBox(height: 20),

                      // Kontaktdaten
                      _buildModernContactInfo(
                        profile,
                        template,
                        fontBold,
                        fontRegular,
                      ),
                      pw.SizedBox(height: 30),

                      // Skills
                      if (profile.skills?.isNotEmpty == true) ...[
                        _buildSectionTitle('FÄHIGKEITEN', template, fontBold),
                        pw.SizedBox(height: 15),
                        _buildSkillsVertical(
                          profile.skills!,
                          fontRegular,
                          template,
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Rechte Spalte (70%)
              pw.Expanded(
                flex: 7,
                child: pw.Container(
                  padding: const pw.EdgeInsets.all(20),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // Name und Titel
                      _buildModernHeader(
                        profile,
                        template,
                        fontBold,
                        fontRegular,
                      ),
                      pw.SizedBox(height: 30),

                      // Berufserfahrung
                      if (profile.workExperience?.isNotEmpty == true) ...[
                        _buildSectionTitle(
                          'BERUFSERFAHRUNG',
                          template,
                          fontBold,
                        ),
                        pw.SizedBox(height: 15),
                        ...profile.workExperience!.map(
                          (exp) =>
                              _buildWorkExperience(exp, fontRegular, fontBold),
                        ),
                        pw.SizedBox(height: 20),
                      ],

                      // Ausbildung
                      if (profile.education?.isNotEmpty == true) ...[
                        _buildSectionTitle('AUSBILDUNG', template, fontBold),
                        pw.SizedBox(height: 15),
                        ...profile.education!.map(
                          (edu) => _buildEducation(edu, fontRegular, fontBold),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Generiert minimalistisches Template
  Future<void> _generateMinimalistTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(40),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Minimalistischer Header mit Profilbild
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Profilbild links
                    _buildProfileImageWidget(profileImage, size: 70),
                    pw.SizedBox(width: 20),

                    // Name und Details rechts
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            profile.name ?? 'Name nicht verfügbar',
                            style: pw.TextStyle(
                              font: fontBold,
                              fontSize: 28,
                              color: _colorToPdfColor(
                                template.colorScheme.primaryColor,
                              ),
                            ),
                          ),
                          pw.SizedBox(height: 5),
                          if (profile.jobPreferences != null) ...[
                            pw.Text(
                              profile.jobPreferences!,
                              style: pw.TextStyle(
                                font: fontRegular,
                                fontSize: 14,
                                color: PdfColors.grey700,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 15),

                // Minimalistischer Trennstrich
                pw.Container(
                  height: 2,
                  width: 100,
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                ),
                pw.SizedBox(height: 15),

                // Kontaktdaten
                pw.Row(
                  children: [
                    if (profile.email != null) ...[
                      pw.Text(
                        profile.email!,
                        style: pw.TextStyle(font: fontRegular, fontSize: 11),
                      ),
                      if (profile.phone != null) ...[
                        pw.SizedBox(width: 20),
                        pw.Text(
                          '•',
                          style: pw.TextStyle(font: fontRegular, fontSize: 11),
                        ),
                        pw.SizedBox(width: 20),
                      ],
                    ],
                    if (profile.phone != null) ...[
                      pw.Text(
                        profile.phone!,
                        style: pw.TextStyle(font: fontRegular, fontSize: 11),
                      ),
                    ],
                  ],
                ),
                pw.SizedBox(height: 20),

                // Berufserfahrung
                if (profile.workExperience?.isNotEmpty == true) ...[
                  pw.Text(
                    'BERUFSERFAHRUNG',
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 14,
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  ...profile.workExperience!.map(
                    (exp) => _buildWorkExperience(exp, fontRegular, fontBold),
                  ),
                  pw.SizedBox(height: 20),
                ],

                // Ausbildung
                if (profile.education?.isNotEmpty == true) ...[
                  pw.Text(
                    'AUSBILDUNG',
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 14,
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  ...profile.education!.map(
                    (edu) => _buildEducation(edu, fontRegular, fontBold),
                  ),
                  pw.SizedBox(height: 20),
                ],

                // Skills
                if (profile.skills?.isNotEmpty == true) ...[
                  pw.Text(
                    'FÄHIGKEITEN',
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 14,
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    profile.skills!.join(' • '),
                    style: pw.TextStyle(font: fontRegular, fontSize: 11),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// Hilfsmethoden für Template-Komponenten

  /// Lädt Profilbild für PDF-Verwendung (mit Cache für Performance)
  Future<pw.ImageProvider?> _loadProfileImage(UserProfile profile) async {
    try {
      if (profile.profileImageUrl == null || profile.profileImageUrl!.isEmpty) {
        debugPrint('📸 Kein Profilbild-URL im Profil gefunden');
        return null;
      }

      // Prüfe Cache zuerst
      final cacheKey = profile.profileImageUrl!;
      if (_imageCache.containsKey(cacheKey)) {
        debugPrint('📸 Profilbild aus Cache geladen: $cacheKey');
        return _imageCache[cacheKey]!;
      }

      debugPrint('📸 Lade Profilbild für PDF: ${profile.profileImageUrl}');

      final imageBytes = await _profileImageService.downloadProfileImage(
        profile.profileImageUrl!,
      );

      if (imageBytes == null || imageBytes.isEmpty) {
        debugPrint('⚠️ Profilbild konnte nicht geladen werden oder ist leer');
        return null;
      }

      debugPrint(
        '✅ Profilbild erfolgreich geladen: ${imageBytes.length} bytes',
      );

      final imageProvider = pw.MemoryImage(imageBytes);
      _imageCache[cacheKey] = imageProvider; // Cache speichern

      debugPrint('✅ Profilbild in Cache gespeichert');
      return imageProvider;
    } catch (e, stackTrace) {
      debugPrint('🚨 Fehler beim Laden des Profilbilds: $e');
      debugPrint('🚨 StackTrace: $stackTrace');
      return null;
    }
  }

  /// Erstellt Profilbild-Widget für PDF (mit Platzhalter falls kein Bild)
  pw.Widget _buildProfileImageWidget(
    pw.ImageProvider? profileImage, {
    double size = 80,
  }) {
    if (profileImage != null) {
      debugPrint('✅ Erstelle Profilbild-Widget mit echtem Bild (${size}px)');
      return pw.Container(
        width: size,
        height: size,
        decoration: pw.BoxDecoration(
          shape: pw.BoxShape.circle,
          border: pw.Border.all(color: PdfColors.grey400, width: 2),
        ),
        child: pw.ClipOval(child: pw.Image(profileImage, fit: pw.BoxFit.cover)),
      );
    } else {
      debugPrint('⚠️ Erstelle Profilbild-Platzhalter (${size}px)');
      // Verbesserter Platzhalter mit Person-Icon
      return pw.Container(
        width: size,
        height: size,
        decoration: pw.BoxDecoration(
          shape: pw.BoxShape.circle,
          color: PdfColors.blueGrey100,
          border: pw.Border.all(color: PdfColors.blueGrey300, width: 2),
        ),
        child: pw.Center(
          child: pw.Icon(
            pw.IconData(0xe7fd), // person icon
            color: PdfColors.blueGrey600,
            size: size * 0.5,
          ),
        ),
      );
    }
  }

  /// Konvertiert Flutter Color zu PDF Color
  PdfColor _colorToPdfColor(Color color) {
    return PdfColor(
      color.r / 255.0,
      color.g / 255.0,
      color.b / 255.0,
      color.a / 255.0,
    );
  }

  /// Erstellt klassischen Header
  pw.Widget _buildClassicHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Profilbild links
        _buildProfileImageWidget(profileImage, size: 80),
        pw.SizedBox(width: 20),

        // Name und Kontaktdaten rechts
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                profile.name ?? 'Name nicht verfügbar',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 28,
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                ),
              ),
              pw.SizedBox(height: 10),
              if (profile.email != null ||
                  profile.phone != null ||
                  profile.address != null) ...[
                pw.Row(
                  children: [
                    if (profile.email != null) ...[
                      pw.Text(
                        profile.email!,
                        style: pw.TextStyle(font: fontRegular, fontSize: 12),
                      ),
                      pw.SizedBox(width: 20),
                    ],
                    if (profile.phone != null) ...[
                      pw.Text(
                        profile.phone!,
                        style: pw.TextStyle(font: fontRegular, fontSize: 12),
                      ),
                      pw.SizedBox(width: 20),
                    ],
                    if (profile.address != null) ...[
                      pw.Text(
                        profile.address!,
                        style: pw.TextStyle(font: fontRegular, fontSize: 12),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Erstellt modernen Header
  pw.Widget _buildModernHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          profile.name ?? 'Name nicht verfügbar',
          style: pw.TextStyle(
            font: fontBold,
            fontSize: 28,
            color: _colorToPdfColor(template.colorScheme.primaryColor),
          ),
        ),
        pw.SizedBox(height: 5),
        if (profile.jobPreferences != null) ...[
          pw.Text(
            profile.jobPreferences!,
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 14,
              color: PdfColors.grey700,
            ),
          ),
        ],
      ],
    );
  }

  /// Erstellt moderne Kontaktinformationen
  pw.Widget _buildModernContactInfo(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'KONTAKT',
          style: pw.TextStyle(
            font: fontBold,
            fontSize: 14,
            color: _colorToPdfColor(template.colorScheme.primaryColor),
          ),
        ),
        pw.SizedBox(height: 10),
        if (profile.email != null) ...[
          pw.Text(
            profile.email!,
            style: pw.TextStyle(font: fontRegular, fontSize: 10),
          ),
          pw.SizedBox(height: 5),
        ],
        if (profile.phone != null) ...[
          pw.Text(
            profile.phone!,
            style: pw.TextStyle(font: fontRegular, fontSize: 10),
          ),
          pw.SizedBox(height: 5),
        ],
        if (profile.address != null) ...[
          pw.Text(
            profile.address!,
            style: pw.TextStyle(font: fontRegular, fontSize: 10),
          ),
        ],
      ],
    );
  }

  /// Erstellt Abschnittstitel
  pw.Widget _buildSectionTitle(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        font: fontBold,
        fontSize: 16,
        color: _colorToPdfColor(template.colorScheme.primaryColor),
      ),
    );
  }

  /// Erstellt Berufserfahrung-Eintrag
  pw.Widget _buildWorkExperience(
    WorkExperience experience,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            experience.position,
            style: pw.TextStyle(font: fontBold, fontSize: 12),
          ),
          pw.SizedBox(height: 3),
          pw.Text(
            experience.company,
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 11,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 3),
          pw.Text(
            '${experience.startDate.year}-${experience.startDate.month.toString().padLeft(2, '0')} - ${experience.endDate?.year.toString() ?? 'Heute'}',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            experience.description,
            style: pw.TextStyle(font: fontRegular, fontSize: 10),
          ),
        ],
      ),
    );
  }

  /// Erstellt Ausbildung-Eintrag
  pw.Widget _buildEducation(
    Education education,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            education.degree,
            style: pw.TextStyle(font: fontBold, fontSize: 12),
          ),
          pw.SizedBox(height: 3),
          pw.Text(
            education.institution,
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 11,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 3),
          pw.Text(
            '${education.startDate.year}-${education.startDate.month.toString().padLeft(2, '0')} - ${education.endDate?.year.toString() ?? 'Heute'}',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Skills horizontal
  pw.Widget _buildSkills(List<String> skills, pw.Font fontRegular) {
    return pw.Wrap(
      spacing: 10,
      runSpacing: 5,
      children:
          skills
              .map(
                (skill) => pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey200,
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Text(
                    skill,
                    style: pw.TextStyle(font: fontRegular, fontSize: 10),
                  ),
                ),
              )
              .toList(),
    );
  }

  /// Erstellt Skills vertikal
  pw.Widget _buildSkillsVertical(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          skills
              .map(
                (skill) => pw.Container(
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Row(
                    children: [
                      pw.Container(
                        width: 4,
                        height: 4,
                        decoration: pw.BoxDecoration(
                          color: _colorToPdfColor(
                            template.colorScheme.primaryColor,
                          ),
                          shape: pw.BoxShape.circle,
                        ),
                      ),
                      pw.SizedBox(width: 8),
                      pw.Text(
                        skill,
                        style: pw.TextStyle(font: fontRegular, fontSize: 10),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }

  /// Generiert Canva-inspiriertes Template mit visuellen Elementen
  Future<void> _generateCanvaTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              // Hintergrund-Gradient
              pw.Positioned.fill(
                child: pw.Container(
                  decoration: pw.BoxDecoration(
                    gradient: pw.LinearGradient(
                      begin: pw.Alignment.topLeft,
                      end: pw.Alignment.bottomRight,
                      colors: [
                        PdfColor(
                          template.colorScheme.primaryColor.r / 255.0,
                          template.colorScheme.primaryColor.g / 255.0,
                          template.colorScheme.primaryColor.b / 255.0,
                          0.08,
                        ),
                        PdfColors.white,
                        PdfColor(
                          template.colorScheme.secondaryColor.r / 255.0,
                          template.colorScheme.secondaryColor.g / 255.0,
                          template.colorScheme.secondaryColor.b / 255.0,
                          0.05,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Dekorative Kreise
              pw.Positioned(
                top: -80,
                right: -80,
                child: pw.Container(
                  width: 200,
                  height: 200,
                  decoration: pw.BoxDecoration(
                    color: PdfColor(
                      template.colorScheme.primaryColor.r / 255.0,
                      template.colorScheme.primaryColor.g / 255.0,
                      template.colorScheme.primaryColor.b / 255.0,
                      0.1,
                    ),
                    shape: pw.BoxShape.circle,
                  ),
                ),
              ),

              pw.Positioned(
                bottom: -50,
                left: -50,
                child: pw.Container(
                  width: 150,
                  height: 150,
                  decoration: pw.BoxDecoration(
                    color: PdfColor(
                      template.colorScheme.secondaryColor.r / 255.0,
                      template.colorScheme.secondaryColor.g / 255.0,
                      template.colorScheme.secondaryColor.b / 255.0,
                      0.12,
                    ),
                    shape: pw.BoxShape.circle,
                  ),
                ),
              ),

              // Hauptinhalt
              pw.Positioned.fill(
                child: pw.Padding(
                  padding: const pw.EdgeInsets.all(40),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // Canva-Style Header
                      _buildCanvaHeader(
                        profile,
                        template,
                        fontBold,
                        fontRegular,
                      ),
                      pw.SizedBox(height: 30),

                      // Zwei-Spalten Layout
                      pw.Expanded(
                        child: pw.Row(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            // Linke Spalte (Hauptinhalt)
                            pw.Expanded(
                              flex: 7,
                              child: pw.Column(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: [
                                  // Berufserfahrung
                                  if (profile.workExperience?.isNotEmpty ==
                                      true) ...[
                                    _buildCanvaSectionTitle(
                                      'BERUFSERFAHRUNG',
                                      template,
                                      fontBold,
                                    ),
                                    pw.SizedBox(height: 15),
                                    ...profile.workExperience!.map(
                                      (exp) => _buildCanvaWorkExperience(
                                        exp,
                                        fontRegular,
                                        fontBold,
                                        template,
                                      ),
                                    ),
                                    pw.SizedBox(height: 25),
                                  ],

                                  // Ausbildung
                                  if (profile.education?.isNotEmpty ==
                                      true) ...[
                                    _buildCanvaSectionTitle(
                                      'AUSBILDUNG',
                                      template,
                                      fontBold,
                                    ),
                                    pw.SizedBox(height: 15),
                                    ...profile.education!.map(
                                      (edu) => _buildCanvaEducation(
                                        edu,
                                        fontRegular,
                                        fontBold,
                                        template,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            pw.SizedBox(width: 30),

                            // Rechte Spalte (Sidebar)
                            pw.Expanded(
                              flex: 3,
                              child: pw.Container(
                                padding: const pw.EdgeInsets.all(20),
                                decoration: pw.BoxDecoration(
                                  color: PdfColor(
                                    template.colorScheme.primaryColor.r / 255.0,
                                    template.colorScheme.primaryColor.g / 255.0,
                                    template.colorScheme.primaryColor.b / 255.0,
                                    0.08,
                                  ),
                                  borderRadius: pw.BorderRadius.circular(15),
                                  border: pw.Border.all(
                                    color: PdfColor(
                                      template.colorScheme.primaryColor.r /
                                          255.0,
                                      template.colorScheme.primaryColor.g /
                                          255.0,
                                      template.colorScheme.primaryColor.b /
                                          255.0,
                                      0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                    // Kontakt
                                    _buildCanvaSectionTitle(
                                      'KONTAKT',
                                      template,
                                      fontBold,
                                    ),
                                    pw.SizedBox(height: 15),
                                    _buildCanvaContactInfo(
                                      profile,
                                      template,
                                      fontBold,
                                      fontRegular,
                                    ),

                                    pw.SizedBox(height: 25),

                                    // Skills
                                    if (profile.skills?.isNotEmpty == true) ...[
                                      _buildCanvaSectionTitle(
                                        'FÄHIGKEITEN',
                                        template,
                                        fontBold,
                                      ),
                                      pw.SizedBox(height: 15),
                                      _buildCanvaSkills(
                                        profile.skills!,
                                        fontRegular,
                                        template,
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Speichert PDF auf dem Gerät
  Future<String> savePdfToDevice(Uint8List pdfBytes, String fileName) async {
    // Implementierung für PDF-Speicherung
    // Hier würde normalerweise path_provider verwendet
    throw UnimplementedError('savePdfToDevice noch nicht implementiert');
  }

  /// Erstellt Canva-Style Header mit modernem Design
  pw.Widget _buildCanvaHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(25),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          begin: pw.Alignment.centerLeft,
          end: pw.Alignment.centerRight,
          colors: [
            _colorToPdfColor(template.colorScheme.primaryColor),
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.8,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Row(
        children: [
          // Foto-Platzhalter
          pw.Container(
            width: 80,
            height: 80,
            decoration: pw.BoxDecoration(
              color: PdfColors.white,
              shape: pw.BoxShape.circle,
              border: pw.Border.all(color: PdfColors.white, width: 3),
            ),
            child: pw.Center(
              child: pw.Text(
                profile.name?.substring(0, 1).toUpperCase() ?? 'U',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 32,
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                ),
              ),
            ),
          ),
          pw.SizedBox(width: 25),

          // Name und Titel
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  profile.name ?? 'Name nicht verfügbar',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 28,
                    color: PdfColors.white,
                  ),
                ),
                pw.SizedBox(height: 8),
                if (profile.jobPreferences != null) ...[
                  pw.Text(
                    profile.jobPreferences!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 16,
                      color: PdfColors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Canva-Style Abschnittstitel
  pw.Widget _buildCanvaSectionTitle(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 15),
      decoration: pw.BoxDecoration(
        color: PdfColor(
          template.colorScheme.primaryColor.r / 255.0,
          template.colorScheme.primaryColor.g / 255.0,
          template.colorScheme.primaryColor.b / 255.0,
          0.15,
        ),
        borderRadius: pw.BorderRadius.circular(20),
        border: pw.Border.all(
          color: _colorToPdfColor(template.colorScheme.primaryColor),
          width: 1,
        ),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 14,
          color: _colorToPdfColor(template.colorScheme.primaryColor),
        ),
      ),
    );
  }

  /// Erstellt Canva-Style Berufserfahrung
  pw.Widget _buildCanvaWorkExperience(
    WorkExperience experience,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(
          color: PdfColor(
            template.colorScheme.primaryColor.r / 255.0,
            template.colorScheme.primaryColor.g / 255.0,
            template.colorScheme.primaryColor.b / 255.0,
            0.2,
          ),
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              // Bullet Point
              pw.Container(
                width: 8,
                height: 8,
                decoration: pw.BoxDecoration(
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: pw.Text(
                  experience.position,
                  style: pw.TextStyle(font: fontBold, fontSize: 14),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Padding(
            padding: const pw.EdgeInsets.only(left: 18),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  experience.company,
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 12,
                    color: _colorToPdfColor(template.colorScheme.primaryColor),
                  ),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  '${experience.startDate.year}-${experience.startDate.month.toString().padLeft(2, '0')} - ${experience.endDate?.year.toString() ?? 'Heute'}',
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 10,
                    color: PdfColors.grey600,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  experience.description,
                  style: pw.TextStyle(font: fontRegular, fontSize: 11),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Canva-Style Ausbildung
  pw.Widget _buildCanvaEducation(
    Education education,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColor(
          template.colorScheme.secondaryColor.r / 255.0,
          template.colorScheme.secondaryColor.g / 255.0,
          template.colorScheme.secondaryColor.b / 255.0,
          0.1,
        ),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        children: [
          // Icon-Platzhalter
          pw.Container(
            width: 6,
            height: 6,
            decoration: pw.BoxDecoration(
              color: _colorToPdfColor(template.colorScheme.secondaryColor),
              shape: pw.BoxShape.circle,
            ),
          ),
          pw.SizedBox(width: 12),
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  education.degree,
                  style: pw.TextStyle(font: fontBold, fontSize: 12),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  education.institution,
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 11,
                    color: PdfColors.grey700,
                  ),
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  '${education.startDate.year} - ${education.endDate?.year.toString() ?? 'Heute'}',
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 9,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Canva-Style Kontaktinformationen
  pw.Widget _buildCanvaContactInfo(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        if (profile.email != null) ...[
          pw.Row(
            children: [
              pw.Container(
                width: 4,
                height: 4,
                decoration: pw.BoxDecoration(
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  profile.email!,
                  style: pw.TextStyle(font: fontRegular, fontSize: 10),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
        ],
        if (profile.phone != null) ...[
          pw.Row(
            children: [
              pw.Container(
                width: 4,
                height: 4,
                decoration: pw.BoxDecoration(
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  profile.phone!,
                  style: pw.TextStyle(font: fontRegular, fontSize: 10),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
        ],
        if (profile.address != null) ...[
          pw.Row(
            children: [
              pw.Container(
                width: 4,
                height: 4,
                decoration: pw.BoxDecoration(
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  profile.address!,
                  style: pw.TextStyle(font: fontRegular, fontSize: 10),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Erstellt Canva-Style Skills mit Progress Bars
  pw.Widget _buildCanvaSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          skills.take(8).map((skill) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    skill,
                    style: pw.TextStyle(font: fontRegular, fontSize: 10),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Stack(
                    children: [
                      // Hintergrund
                      pw.Container(
                        height: 6,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.grey300,
                          borderRadius: pw.BorderRadius.circular(3),
                        ),
                      ),
                      // Progress Bar
                      pw.Container(
                        height: 6,
                        width:
                            60 +
                            (skills.indexOf(skill) % 3) *
                                10, // Variiert zwischen 60-80px
                        decoration: pw.BoxDecoration(
                          gradient: pw.LinearGradient(
                            colors: [
                              _colorToPdfColor(
                                template.colorScheme.primaryColor,
                              ),
                              _colorToPdfColor(
                                template.colorScheme.secondaryColor,
                              ),
                            ],
                          ),
                          borderRadius: pw.BorderRadius.circular(3),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Generiert Executive Template für Führungskräfte mit Premium-Design
  Future<void> _generateExecutiveTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(
              50,
            ), // Mehr Weißraum für Premium-Look
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Executive Header mit viel Weißraum
                _buildExecutiveHeader(
                  profile,
                  template,
                  fontBold,
                  fontRegular,
                  profileImage,
                ),
                pw.SizedBox(height: 40),

                // Berufserfahrung mit Executive-Styling
                if (profile.workExperience != null &&
                    profile.workExperience!.isNotEmpty) ...[
                  _buildExecutiveSection('BERUFSERFAHRUNG', template, fontBold),
                  pw.SizedBox(height: 25),
                  _buildExecutiveWorkExperience(
                    profile.workExperience!,
                    fontRegular,
                    fontBold,
                    template,
                  ),
                  pw.SizedBox(height: 35),
                ],

                // Ausbildung
                if (profile.education != null &&
                    profile.education!.isNotEmpty) ...[
                  _buildExecutiveSection('AUSBILDUNG', template, fontBold),
                  pw.SizedBox(height: 20),
                  _buildExecutiveEducation(
                    profile.education!,
                    fontRegular,
                    fontBold,
                    template,
                  ),
                  pw.SizedBox(height: 35),
                ],

                // Skills mit Executive-Styling
                if (profile.skills != null && profile.skills!.isNotEmpty) ...[
                  _buildExecutiveSection('KERNKOMPETENZEN', template, fontBold),
                  pw.SizedBox(height: 20),
                  _buildExecutiveSkills(profile.skills!, fontRegular, template),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// Erstellt Executive Header mit Premium-Design und viel Weißraum
  pw.Widget _buildExecutiveHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Profilbild links (größer für Executive-Look)
        _buildProfileImageWidget(profileImage, size: 100),
        pw.SizedBox(width: 30),

        // Name und Kontaktdaten rechts
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Name mit großer, eleganter Schrift
              pw.Text(
                profile.name ?? 'Name nicht verfügbar',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 36, // Größere Schrift für Executive-Look
                  color: PdfColors.grey900,
                  letterSpacing: 1.5,
                ),
              ),
              pw.SizedBox(height: 12),

              // Job-Präferenzen mit subtiler Linie
              if (profile.jobPreferences != null) ...[
                pw.Container(
                  padding: const pw.EdgeInsets.only(bottom: 8),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.grey300, width: 1),
                    ),
                  ),
                  child: pw.Text(
                    profile.jobPreferences!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 18,
                      color: PdfColors.grey700,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                pw.SizedBox(height: 20),
              ],

              // Kontaktdaten in eleganter Zeile
              pw.Row(
                children: [
                  if (profile.email != null) ...[
                    pw.Text(
                      profile.email!,
                      style: pw.TextStyle(
                        font: fontRegular,
                        fontSize: 13,
                        color: PdfColors.grey600,
                      ),
                    ),
                    if (profile.phone != null) ...[
                      pw.SizedBox(width: 30),
                      pw.Text(
                        '•',
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 13,
                          color: PdfColors.grey400,
                        ),
                      ),
                      pw.SizedBox(width: 30),
                    ],
                  ],
                  if (profile.phone != null) ...[
                    pw.Text(
                      profile.phone!,
                      style: pw.TextStyle(
                        font: fontRegular,
                        fontSize: 13,
                        color: PdfColors.grey600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Erstellt Executive Section Header mit minimalistischem Design
  pw.Widget _buildExecutiveSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 20,
          color: PdfColors.grey900,
          letterSpacing: 2.0, // Mehr Buchstabenabstand für Executive-Look
        ),
      ),
    );
  }

  /// Erstellt Executive Work Experience mit viel Weißraum
  pw.Widget _buildExecutiveWorkExperience(
    List<WorkExperience> experiences,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          experiences.map((experience) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(
                bottom: 30,
              ), // Mehr Abstand für Executive-Look
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Position und Unternehmen in einer Zeile
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Expanded(
                        child: pw.Text(
                          experience.position,
                          style: pw.TextStyle(
                            font: fontBold,
                            fontSize: 16,
                            color: PdfColors.grey900,
                          ),
                        ),
                      ),
                      pw.Text(
                        '${experience.startDate.year}-${experience.startDate.month.toString().padLeft(2, '0')} - ${experience.endDate?.year.toString() ?? 'Heute'}',
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 12,
                          color: PdfColors.grey600,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 4),

                  // Unternehmen
                  pw.Text(
                    experience.company,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 12),

                  // Beschreibung mit mehr Zeilenabstand
                  pw.Text(
                    experience.description,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 12,
                      color: PdfColors.grey800,
                      lineSpacing:
                          1.4, // Mehr Zeilenabstand für bessere Lesbarkeit
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Executive Education mit minimalistischem Design
  pw.Widget _buildExecutiveEducation(
    List<Education> educations,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          educations.map((education) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 20),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          education.degree,
                          style: pw.TextStyle(
                            font: fontBold,
                            fontSize: 14,
                            color: PdfColors.grey900,
                          ),
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          education.institution,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 12,
                            color: PdfColors.grey700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Text(
                    '${education.startDate.year} - ${education.endDate?.year ?? 'Heute'}',
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 11,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Executive Skills mit eleganter Darstellung
  pw.Widget _buildExecutiveSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Text(
      skills.join(' • '), // Skills mit Bullet-Points getrennt
      style: pw.TextStyle(
        font: fontRegular,
        fontSize: 12,
        color: PdfColors.grey800,
        lineSpacing: 1.3,
      ),
    );
  }

  /// Generiert Infografik Template mit Progress-Bars und visuellen Elementen
  Future<void> _generateInfographicTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(40),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Infografik Header mit visuellen Elementen
                _buildInfographicHeader(
                  profile,
                  template,
                  fontBold,
                  fontRegular,
                  profileImage,
                ),
                pw.SizedBox(height: 30),

                // Berufserfahrung mit visuellen Elementen
                if (profile.workExperience != null &&
                    profile.workExperience!.isNotEmpty) ...[
                  _buildInfographicSection(
                    'BERUFSERFAHRUNG',
                    template,
                    fontBold,
                  ),
                  pw.SizedBox(height: 20),
                  _buildInfographicWorkExperience(
                    profile.workExperience!,
                    fontRegular,
                    fontBold,
                    template,
                  ),
                  pw.SizedBox(height: 25),
                ],

                // Skills mit Progress-Bars
                if (profile.skills != null && profile.skills!.isNotEmpty) ...[
                  _buildInfographicSection('KOMPETENZEN', template, fontBold),
                  pw.SizedBox(height: 20),
                  _buildInfographicSkills(
                    profile.skills!,
                    fontRegular,
                    template,
                  ),
                  pw.SizedBox(height: 25),
                ],

                // Ausbildung
                if (profile.education != null &&
                    profile.education!.isNotEmpty) ...[
                  _buildInfographicSection('AUSBILDUNG', template, fontBold),
                  pw.SizedBox(height: 15),
                  _buildInfographicEducation(
                    profile.education!,
                    fontRegular,
                    fontBold,
                    template,
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// Erstellt Infografik Header mit visuellen Elementen
  pw.Widget _buildInfographicHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Profilbild links
        _buildProfileImageWidget(profileImage, size: 90),
        pw.SizedBox(width: 25),

        // Name und Kontaktdaten rechts
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Name mit Akzentfarbe
              pw.Text(
                profile.name ?? 'Name nicht verfügbar',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 30,
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                ),
              ),
              pw.SizedBox(height: 8),

              // Job-Präferenzen
              if (profile.jobPreferences != null) ...[
                pw.Text(
                  profile.jobPreferences!,
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 16,
                    color: PdfColors.grey700,
                  ),
                ),
                pw.SizedBox(height: 12),
              ],

              // Kontaktdaten mit Icons (simuliert durch Bullet-Points)
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  if (profile.email != null) ...[
                    pw.Row(
                      children: [
                        pw.Container(
                          width: 4,
                          height: 4,
                          decoration: pw.BoxDecoration(
                            color: _colorToPdfColor(
                              template.colorScheme.primaryColor,
                            ),
                            shape: pw.BoxShape.circle,
                          ),
                        ),
                        pw.SizedBox(width: 8),
                        pw.Text(
                          profile.email!,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 12,
                            color: PdfColors.grey800,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 4),
                  ],
                  if (profile.phone != null) ...[
                    pw.Row(
                      children: [
                        pw.Container(
                          width: 4,
                          height: 4,
                          decoration: pw.BoxDecoration(
                            color: _colorToPdfColor(
                              template.colorScheme.primaryColor,
                            ),
                            shape: pw.BoxShape.circle,
                          ),
                        ),
                        pw.SizedBox(width: 8),
                        pw.Text(
                          profile.phone!,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 12,
                            color: PdfColors.grey800,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Erstellt Infografik Section Header mit visuellen Akzenten
  pw.Widget _buildInfographicSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Row(
      children: [
        // Farbiger Akzent-Balken
        pw.Container(
          width: 4,
          height: 20,
          color: _colorToPdfColor(template.colorScheme.primaryColor),
        ),
        pw.SizedBox(width: 12),
        pw.Text(
          title,
          style: pw.TextStyle(
            font: fontBold,
            fontSize: 18,
            color: PdfColors.grey900,
            letterSpacing: 1.0,
          ),
        ),
      ],
    );
  }

  /// Erstellt Infografik Skills mit Progress-Bars
  pw.Widget _buildInfographicSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          skills.take(8).map((skill) {
            // Maximal 8 Skills für bessere Darstellung
            // Simuliere Skill-Level (in echter App könnte das aus Profildaten kommen)
            final skillLevel =
                0.7 + (skill.hashCode % 3) * 0.1; // 70-90% für Demo

            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 12),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Skill-Name
                  pw.Text(
                    skill,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 12,
                      color: PdfColors.grey900,
                    ),
                  ),
                  pw.SizedBox(height: 4),

                  // Progress-Bar
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Stack(
                          children: [
                            // Hintergrund-Bar
                            pw.Container(
                              height: 6,
                              decoration: pw.BoxDecoration(
                                color: PdfColors.grey300,
                                borderRadius: pw.BorderRadius.circular(3),
                              ),
                            ),
                            // Fortschritts-Bar
                            pw.Container(
                              height: 6,
                              width: 150 * skillLevel, // 150px * Skill-Level
                              decoration: pw.BoxDecoration(
                                color: _colorToPdfColor(
                                  template.colorScheme.primaryColor,
                                ),
                                borderRadius: pw.BorderRadius.circular(3),
                              ),
                            ),
                          ],
                        ),
                      ),
                      pw.SizedBox(width: 8),
                      // Prozent-Anzeige
                      pw.Text(
                        '${(skillLevel * 100).round()}%',
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 10,
                          color: PdfColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Infografik Work Experience mit visuellen Elementen
  pw.Widget _buildInfographicWorkExperience(
    List<WorkExperience> experiences,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          experiences.map((experience) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 20),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Visueller Indikator
                  pw.Container(
                    width: 8,
                    height: 8,
                    margin: const pw.EdgeInsets.only(top: 6),
                    decoration: pw.BoxDecoration(
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                      shape: pw.BoxShape.circle,
                    ),
                  ),
                  pw.SizedBox(width: 12),

                  // Inhalt
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // Position und Zeitraum
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(
                              child: pw.Text(
                                experience.position,
                                style: pw.TextStyle(
                                  font: fontBold,
                                  fontSize: 14,
                                  color: PdfColors.grey900,
                                ),
                              ),
                            ),
                            pw.Text(
                              '${experience.startDate.year}-${experience.endDate?.year ?? 'Heute'}',
                              style: pw.TextStyle(
                                font: fontRegular,
                                fontSize: 11,
                                color: _colorToPdfColor(
                                  template.colorScheme.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        pw.SizedBox(height: 3),

                        // Unternehmen
                        pw.Text(
                          experience.company,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 12,
                            color: PdfColors.grey700,
                          ),
                        ),
                        pw.SizedBox(height: 8),

                        // Beschreibung
                        pw.Text(
                          experience.description,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 11,
                            color: PdfColors.grey800,
                            lineSpacing: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Infografik Education mit visuellen Elementen
  pw.Widget _buildInfographicEducation(
    List<Education> educations,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          educations.map((education) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 15),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Visueller Indikator (kleiner für Education)
                  pw.Container(
                    width: 6,
                    height: 6,
                    margin: const pw.EdgeInsets.only(top: 6),
                    decoration: pw.BoxDecoration(
                      color: _colorToPdfColor(
                        template.colorScheme.secondaryColor,
                      ),
                      shape: pw.BoxShape.circle,
                    ),
                  ),
                  pw.SizedBox(width: 12),

                  // Inhalt
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(
                              child: pw.Text(
                                education.degree,
                                style: pw.TextStyle(
                                  font: fontBold,
                                  fontSize: 12,
                                  color: PdfColors.grey900,
                                ),
                              ),
                            ),
                            pw.Text(
                              '${education.startDate.year}-${education.endDate?.year ?? 'Heute'}',
                              style: pw.TextStyle(
                                font: fontRegular,
                                fontSize: 10,
                                color: _colorToPdfColor(
                                  template.colorScheme.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          education.institution,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 11,
                            color: PdfColors.grey700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Generiert Grid Template mit strukturiertem Layout
  Future<void> _generateGridTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Lade Profilbild
    final profileImage = await _loadProfileImage(profile);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Padding(
            padding: const pw.EdgeInsets.all(35),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Grid Header - strukturiert und business-formal
                _buildGridHeader(
                  profile,
                  template,
                  fontBold,
                  fontRegular,
                  profileImage,
                ),
                pw.SizedBox(height: 25),

                // Grid Layout - 2x2 Sections
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Linke Spalte
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // Berufserfahrung
                          if (profile.workExperience != null &&
                              profile.workExperience!.isNotEmpty) ...[
                            _buildGridSection(
                              'BERUFSERFAHRUNG',
                              template,
                              fontBold,
                            ),
                            pw.SizedBox(height: 15),
                            _buildGridWorkExperience(
                              profile.workExperience!,
                              fontRegular,
                              fontBold,
                              template,
                            ),
                            pw.SizedBox(height: 20),
                          ],

                          // Ausbildung
                          if (profile.education != null &&
                              profile.education!.isNotEmpty) ...[
                            _buildGridSection('AUSBILDUNG', template, fontBold),
                            pw.SizedBox(height: 15),
                            _buildGridEducation(
                              profile.education!,
                              fontRegular,
                              fontBold,
                              template,
                            ),
                          ],
                        ],
                      ),
                    ),
                    pw.SizedBox(width: 30),

                    // Rechte Spalte
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // Skills
                          if (profile.skills != null &&
                              profile.skills!.isNotEmpty) ...[
                            _buildGridSection(
                              'KOMPETENZEN',
                              template,
                              fontBold,
                            ),
                            pw.SizedBox(height: 15),
                            _buildGridSkills(
                              profile.skills!,
                              fontRegular,
                              template,
                            ),
                            pw.SizedBox(height: 20),
                          ],

                          // Zusätzliche Informationen
                          _buildGridSection('PROFIL', template, fontBold),
                          pw.SizedBox(height: 15),
                          pw.Text(
                            profile.jobPreferences ??
                                'Professioneller Lebenslauf',
                            style: pw.TextStyle(
                              font: fontRegular,
                              fontSize: 11,
                              color: PdfColors.grey800,
                              lineSpacing: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Erstellt Grid Header mit strukturiertem Business-Design
  pw.Widget _buildGridHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Profilbild links
          _buildProfileImageWidget(profileImage, size: 85),
          pw.SizedBox(width: 25),

          // Name und Kontaktdaten strukturiert
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Name groß und business-formal
                pw.Text(
                  profile.name ?? 'Name nicht verfügbar',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 26,
                    color: _colorToPdfColor(template.colorScheme.primaryColor),
                  ),
                ),
                pw.SizedBox(height: 8),

                // Job-Präferenzen
                if (profile.jobPreferences != null) ...[
                  pw.Text(
                    profile.jobPreferences!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 12),
                ],

                // Kontaktdaten in Grid-Format
                pw.Row(
                  children: [
                    if (profile.email != null) ...[
                      pw.Expanded(
                        child: pw.Text(
                          'E-Mail: ${profile.email!}',
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 10,
                            color: PdfColors.grey800,
                          ),
                        ),
                      ),
                    ],
                    if (profile.phone != null) ...[
                      pw.Expanded(
                        child: pw.Text(
                          'Telefon: ${profile.phone!}',
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 10,
                            color: PdfColors.grey800,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Grid Section Header mit strukturiertem Design
  pw.Widget _buildGridSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: pw.BoxDecoration(
        color: _colorToPdfColor(template.colorScheme.primaryColor),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 14,
          color: PdfColors.white,
          letterSpacing: 1.0,
        ),
      ),
    );
  }

  /// Erstellt Grid Work Experience mit strukturiertem Layout
  pw.Widget _buildGridWorkExperience(
    List<WorkExperience> experiences,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          experiences.take(3).map((experience) {
            // Maximal 3 für Grid-Layout
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 12),
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300, width: 1),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    experience.position,
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 12,
                      color: PdfColors.grey900,
                    ),
                  ),
                  pw.SizedBox(height: 2),
                  pw.Text(
                    '${experience.company} • ${experience.startDate.year}-${experience.endDate?.year ?? 'Heute'}',
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    experience.description,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 9,
                      color: PdfColors.grey800,
                      lineSpacing: 1.2,
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Grid Education mit kompaktem Layout
  pw.Widget _buildGridEducation(
    List<Education> educations,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          educations.take(2).map((education) {
            // Maximal 2 für Grid-Layout
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 8),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    education.degree,
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 11,
                      color: PdfColors.grey900,
                    ),
                  ),
                  pw.Text(
                    '${education.institution} • ${education.startDate.year}-${education.endDate?.year ?? 'Heute'}',
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 9,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Grid Skills mit strukturierter Darstellung
  pw.Widget _buildGridSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Wrap(
      spacing: 6,
      runSpacing: 6,
      children:
          skills.take(12).map((skill) {
            // Maximal 12 Skills
            return pw.Container(
              padding: const pw.EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey200,
                borderRadius: pw.BorderRadius.circular(12),
                border: pw.Border.all(color: PdfColors.grey400, width: 0.5),
              ),
              child: pw.Text(
                skill,
                style: pw.TextStyle(
                  font: fontRegular,
                  fontSize: 9,
                  color: PdfColors.grey800,
                ),
              ),
            );
          }).toList(),
    );
  }

  /// Generiert Gradient Template mit professionellen Farbverläufen
  Future<void> _generateGradientTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              // Diagonal Gradient Background
              pw.Positioned.fill(
                child: pw.Container(
                  decoration: pw.BoxDecoration(
                    gradient: pw.LinearGradient(
                      begin: pw.Alignment.topLeft,
                      end: pw.Alignment.bottomRight,
                      colors: [
                        _colorToPdfColor(template.colorScheme.primaryColor),
                        PdfColor(
                          template.colorScheme.primaryColor.r / 255.0,
                          template.colorScheme.primaryColor.g / 255.0,
                          template.colorScheme.primaryColor.b / 255.0,
                          0.7,
                        ),
                        PdfColors.white,
                        PdfColor(
                          template.colorScheme.secondaryColor.r / 255.0,
                          template.colorScheme.secondaryColor.g / 255.0,
                          template.colorScheme.secondaryColor.b / 255.0,
                          0.3,
                        ),
                      ],
                      stops: [0.0, 0.3, 0.7, 1.0],
                    ),
                  ),
                ),
              ),

              // Floating Gradient Shapes
              pw.Positioned(
                top: 100,
                right: -100,
                child: pw.Container(
                  width: 300,
                  height: 300,
                  decoration: pw.BoxDecoration(
                    gradient: pw.RadialGradient(
                      colors: [
                        PdfColor(
                          template.colorScheme.secondaryColor.r / 255.0,
                          template.colorScheme.secondaryColor.g / 255.0,
                          template.colorScheme.secondaryColor.b / 255.0,
                          0.2,
                        ),
                        PdfColor(1.0, 1.0, 1.0, 0.0),
                      ],
                    ),
                    shape: pw.BoxShape.circle,
                  ),
                ),
              ),

              pw.Positioned(
                bottom: 50,
                left: -150,
                child: pw.Container(
                  width: 400,
                  height: 200,
                  decoration: pw.BoxDecoration(
                    gradient: pw.LinearGradient(
                      begin: pw.Alignment.centerLeft,
                      end: pw.Alignment.centerRight,
                      colors: [
                        PdfColor(
                          template.colorScheme.primaryColor.r / 255.0,
                          template.colorScheme.primaryColor.g / 255.0,
                          template.colorScheme.primaryColor.b / 255.0,
                          0.15,
                        ),
                        PdfColor(1.0, 1.0, 1.0, 0.0),
                      ],
                    ),
                    borderRadius: pw.BorderRadius.circular(100),
                  ),
                ),
              ),

              // Content
              pw.Positioned.fill(
                child: pw.Padding(
                  padding: const pw.EdgeInsets.all(40),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // Gradient Header
                      _buildGradientHeader(
                        profile,
                        template,
                        fontBold,
                        fontRegular,
                      ),
                      pw.SizedBox(height: 30),

                      // Content Layout
                      pw.Expanded(
                        child: pw.Row(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            // Main Content
                            pw.Expanded(
                              flex: 6,
                              child: pw.Column(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: [
                                  if (profile.workExperience?.isNotEmpty ==
                                      true) ...[
                                    _buildGradientSection(
                                      'BERUFSERFAHRUNG',
                                      template,
                                      fontBold,
                                    ),
                                    pw.SizedBox(height: 15),
                                    ...profile.workExperience!.map(
                                      (exp) => _buildGradientWorkExperience(
                                        exp,
                                        fontRegular,
                                        fontBold,
                                        template,
                                      ),
                                    ),
                                    pw.SizedBox(height: 25),
                                  ],
                                  if (profile.education?.isNotEmpty ==
                                      true) ...[
                                    _buildGradientSection(
                                      'AUSBILDUNG',
                                      template,
                                      fontBold,
                                    ),
                                    pw.SizedBox(height: 15),
                                    ...profile.education!.map(
                                      (edu) => _buildGradientEducation(
                                        edu,
                                        fontRegular,
                                        fontBold,
                                        template,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            pw.SizedBox(width: 30),

                            // Sidebar
                            pw.Expanded(
                              flex: 4,
                              child: pw.Column(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: [
                                  _buildGradientContactCard(
                                    profile,
                                    template,
                                    fontBold,
                                    fontRegular,
                                  ),
                                  pw.SizedBox(height: 25),
                                  if (profile.skills?.isNotEmpty == true) ...[
                                    _buildGradientSkillsCard(
                                      profile.skills!,
                                      fontRegular,
                                      template,
                                      fontBold,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Erstellt Gradient Header
  pw.Widget _buildGradientHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(30),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          begin: pw.Alignment.centerLeft,
          end: pw.Alignment.centerRight,
          colors: [
            PdfColors.white,
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.1,
            ),
            PdfColor(
              template.colorScheme.secondaryColor.r / 255.0,
              template.colorScheme.secondaryColor.g / 255.0,
              template.colorScheme.secondaryColor.b / 255.0,
              0.15,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(20),
        border: pw.Border.all(
          color: PdfColor(
            template.colorScheme.primaryColor.r / 255.0,
            template.colorScheme.primaryColor.g / 255.0,
            template.colorScheme.primaryColor.b / 255.0,
            0.3,
          ),
          width: 2,
        ),
      ),
      child: pw.Row(
        children: [
          // Avatar mit Gradient
          pw.Container(
            width: 100,
            height: 100,
            decoration: pw.BoxDecoration(
              gradient: pw.RadialGradient(
                colors: [
                  _colorToPdfColor(template.colorScheme.primaryColor),
                  _colorToPdfColor(template.colorScheme.secondaryColor),
                ],
              ),
              shape: pw.BoxShape.circle,
              border: pw.Border.all(color: PdfColors.white, width: 4),
            ),
            child: pw.Center(
              child: pw.Text(
                profile.name?.substring(0, 1).toUpperCase() ?? 'U',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 36,
                  color: PdfColors.white,
                ),
              ),
            ),
          ),
          pw.SizedBox(width: 30),

          // Name und Titel
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  profile.name ?? 'Name nicht verfügbar',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 32,
                    color: _colorToPdfColor(template.colorScheme.primaryColor),
                  ),
                ),
                pw.SizedBox(height: 10),
                if (profile.jobPreferences != null) ...[
                  pw.Container(
                    padding: const pw.EdgeInsets.symmetric(
                      horizontal: 15,
                      vertical: 8,
                    ),
                    decoration: pw.BoxDecoration(
                      gradient: pw.LinearGradient(
                        colors: [
                          _colorToPdfColor(template.colorScheme.secondaryColor),
                          PdfColor(
                            template.colorScheme.secondaryColor.r / 255.0,
                            template.colorScheme.secondaryColor.g / 255.0,
                            template.colorScheme.secondaryColor.b / 255.0,
                            0.7,
                          ),
                        ],
                      ),
                      borderRadius: pw.BorderRadius.circular(15),
                    ),
                    child: pw.Text(
                      profile.jobPreferences!,
                      style: pw.TextStyle(
                        font: fontRegular,
                        fontSize: 16,
                        color: PdfColors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Gradient Section Title
  pw.Widget _buildGradientSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 12, horizontal: 20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            _colorToPdfColor(template.colorScheme.primaryColor),
            _colorToPdfColor(template.colorScheme.secondaryColor),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(25),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 16,
          color: PdfColors.white,
        ),
      ),
    );
  }

  /// Erstellt Gradient Work Experience
  pw.Widget _buildGradientWorkExperience(
    WorkExperience experience,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
          colors: [
            PdfColors.white,
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.05,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(15),
        border: pw.Border.all(
          color: PdfColor(
            template.colorScheme.primaryColor.r / 255.0,
            template.colorScheme.primaryColor.g / 255.0,
            template.colorScheme.primaryColor.b / 255.0,
            0.2,
          ),
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              pw.Container(
                width: 12,
                height: 12,
                decoration: pw.BoxDecoration(
                  gradient: pw.RadialGradient(
                    colors: [
                      _colorToPdfColor(template.colorScheme.primaryColor),
                      _colorToPdfColor(template.colorScheme.secondaryColor),
                    ],
                  ),
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 15),
              pw.Expanded(
                child: pw.Text(
                  experience.position,
                  style: pw.TextStyle(font: fontBold, fontSize: 16),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Padding(
            padding: const pw.EdgeInsets.only(left: 27),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  experience.company,
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 14,
                    color: _colorToPdfColor(template.colorScheme.primaryColor),
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  '${experience.startDate.year}-${experience.startDate.month.toString().padLeft(2, '0')} - ${experience.endDate?.year.toString() ?? 'Heute'}',
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 11,
                    color: PdfColors.grey600,
                  ),
                ),
                pw.SizedBox(height: 10),
                pw.Text(
                  experience.description,
                  style: pw.TextStyle(font: fontRegular, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Gradient Education
  pw.Widget _buildGradientEducation(
    Education education,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            PdfColor(
              template.colorScheme.secondaryColor.r / 255.0,
              template.colorScheme.secondaryColor.g / 255.0,
              template.colorScheme.secondaryColor.b / 255.0,
              0.1,
            ),
            PdfColors.white,
          ],
        ),
        borderRadius: pw.BorderRadius.circular(12),
      ),
      child: pw.Row(
        children: [
          pw.Container(
            width: 8,
            height: 8,
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [
                  _colorToPdfColor(template.colorScheme.secondaryColor),
                  _colorToPdfColor(template.colorScheme.primaryColor),
                ],
              ),
              shape: pw.BoxShape.circle,
            ),
          ),
          pw.SizedBox(width: 15),
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  education.degree,
                  style: pw.TextStyle(font: fontBold, fontSize: 13),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  education.institution,
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 12,
                    color: PdfColors.grey700,
                  ),
                ),
                pw.SizedBox(height: 3),
                pw.Text(
                  '${education.startDate.year} - ${education.endDate?.year.toString() ?? 'Heute'}',
                  style: pw.TextStyle(
                    font: fontRegular,
                    fontSize: 10,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Gradient Contact Card
  pw.Widget _buildGradientContactCard(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          begin: pw.Alignment.topCenter,
          end: pw.Alignment.bottomCenter,
          colors: [
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.1,
            ),
            PdfColors.white,
            PdfColor(
              template.colorScheme.secondaryColor.r / 255.0,
              template.colorScheme.secondaryColor.g / 255.0,
              template.colorScheme.secondaryColor.b / 255.0,
              0.05,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(20),
        border: pw.Border.all(
          color: PdfColor(
            template.colorScheme.primaryColor.r / 255.0,
            template.colorScheme.primaryColor.g / 255.0,
            template.colorScheme.primaryColor.b / 255.0,
            0.3,
          ),
          width: 2,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [
                  _colorToPdfColor(template.colorScheme.primaryColor),
                  _colorToPdfColor(template.colorScheme.secondaryColor),
                ],
              ),
              borderRadius: pw.BorderRadius.circular(15),
            ),
            child: pw.Text(
              'KONTAKT',
              style: pw.TextStyle(
                font: fontBold,
                fontSize: 14,
                color: PdfColors.white,
              ),
            ),
          ),
          pw.SizedBox(height: 15),
          if (profile.email != null) ...[
            _buildGradientContactItem(
              '📧',
              profile.email!,
              fontRegular,
              template,
            ),
            pw.SizedBox(height: 10),
          ],
          if (profile.phone != null) ...[
            _buildGradientContactItem(
              '📱',
              profile.phone!,
              fontRegular,
              template,
            ),
            pw.SizedBox(height: 10),
          ],
          if (profile.address != null) ...[
            _buildGradientContactItem(
              '📍',
              profile.address!,
              fontRegular,
              template,
            ),
          ],
        ],
      ),
    );
  }

  /// Erstellt Gradient Contact Item
  pw.Widget _buildGradientContactItem(
    String icon,
    String text,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Row(
      children: [
        pw.Container(
          width: 6,
          height: 6,
          decoration: pw.BoxDecoration(
            gradient: pw.RadialGradient(
              colors: [
                _colorToPdfColor(template.colorScheme.primaryColor),
                _colorToPdfColor(template.colorScheme.secondaryColor),
              ],
            ),
            shape: pw.BoxShape.circle,
          ),
        ),
        pw.SizedBox(width: 10),
        pw.Expanded(
          child: pw.Text(
            text,
            style: pw.TextStyle(font: fontRegular, fontSize: 11),
          ),
        ),
      ],
    );
  }

  /// Erstellt Gradient Skills Card
  pw.Widget _buildGradientSkillsCard(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
          colors: [
            PdfColors.white,
            PdfColor(
              template.colorScheme.secondaryColor.r / 255.0,
              template.colorScheme.secondaryColor.g / 255.0,
              template.colorScheme.secondaryColor.b / 255.0,
              0.08,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(20),
        border: pw.Border.all(
          color: PdfColor(
            template.colorScheme.secondaryColor.r / 255.0,
            template.colorScheme.secondaryColor.g / 255.0,
            template.colorScheme.secondaryColor.b / 255.0,
            0.3,
          ),
          width: 2,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [
                  _colorToPdfColor(template.colorScheme.secondaryColor),
                  _colorToPdfColor(template.colorScheme.primaryColor),
                ],
              ),
              borderRadius: pw.BorderRadius.circular(15),
            ),
            child: pw.Text(
              'FÄHIGKEITEN',
              style: pw.TextStyle(
                font: fontBold,
                fontSize: 14,
                color: PdfColors.white,
              ),
            ),
          ),
          pw.SizedBox(height: 15),
          ...skills.take(8).map((skill) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 12),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    skill,
                    style: pw.TextStyle(font: fontRegular, fontSize: 11),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Stack(
                    children: [
                      pw.Container(
                        height: 8,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.grey300,
                          borderRadius: pw.BorderRadius.circular(4),
                        ),
                      ),
                      pw.Container(
                        height: 8,
                        width: 70 + (skills.indexOf(skill) % 4) * 15,
                        decoration: pw.BoxDecoration(
                          gradient: pw.LinearGradient(
                            colors: [
                              _colorToPdfColor(
                                template.colorScheme.primaryColor,
                              ),
                              _colorToPdfColor(
                                template.colorScheme.secondaryColor,
                              ),
                            ],
                          ),
                          borderRadius: pw.BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Generiert Geometric Template mit geometrischen Formen
  Future<void> _generateGeometricTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Modern Template als Basis für Geometric
    await _generateModernTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }

  /// Generiert Vibrant Template mit lebendigen Farben
  Future<void> _generateVibrantTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Modern Template als Basis für Vibrant
    await _generateModernTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }

  /// Generiert Playful Template mit verspielten Elementen
  Future<void> _generatePlayfulTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Canva Template als Basis für Playful
    await _generateCanvaTemplate(pdf, profile, template, fontRegular, fontBold);
  }

  /// Speichert PDF lokal im Downloads-Ordner
  Future<String> savePdfLocally(Uint8List pdfBytes, String fileName) async {
    try {
      _log.i('🔥 PDF-CV-Generator: Speichere PDF lokal: $fileName');

      // Hole Downloads-Verzeichnis
      Directory? directory;
      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          directory = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      } else {
        directory = await getDownloadsDirectory();
      }

      if (directory == null) {
        throw Exception('Konnte Downloads-Verzeichnis nicht finden');
      }

      // Erstelle eindeutigen Dateinamen falls Datei bereits existiert
      String finalFileName = fileName;
      int counter = 1;
      File file = File('${directory.path}/$finalFileName');

      while (await file.exists()) {
        final nameWithoutExt = fileName.replaceAll('.pdf', '');
        finalFileName = '${nameWithoutExt}_$counter.pdf';
        file = File('${directory.path}/$finalFileName');
        counter++;
      }

      // Schreibe PDF-Bytes in Datei
      await file.writeAsBytes(pdfBytes);
      _log.i('✅ PDF-CV-Generator: PDF lokal gespeichert: ${file.path}');

      return file.path;
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-CV-Generator: Fehler beim lokalen Speichern',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Löscht alle Caches um Speicher freizugeben
  static void clearCaches() {
    _fontCache.clear();
    _imageCache.clear();
    Logger().i('🧹 PDF-CV-Generator: Alle Caches geleert');
  }

  /// Löscht nur den Bild-Cache
  static void clearImageCache() {
    _imageCache.clear();
    Logger().i('🧹 PDF-CV-Generator: Bild-Cache geleert');
  }

  /// Löscht nur den Font-Cache
  static void clearFontCache() {
    _fontCache.clear();
    Logger().i('🧹 PDF-CV-Generator: Font-Cache geleert');
  }

  /// Teilt PDF über Share-Dialog
  Future<void> sharePdf(Uint8List pdfBytes, String fileName) async {
    try {
      _log.i('🔥 PDF-CV-Generator: Starte PDF-Sharing für $fileName');

      // Erstelle temporäre Datei
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/$fileName');

      // Schreibe PDF-Bytes in Datei
      await file.writeAsBytes(pdfBytes);
      _log.i('📁 PDF-CV-Generator: Temporäre Datei erstellt: ${file.path}');

      // Teile Datei über Share-Dialog
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Mein Lebenslauf',
        subject: 'Lebenslauf - ${fileName.replaceAll('.pdf', '')}',
      );

      _log.i('✅ PDF-CV-Generator: PDF erfolgreich geteilt');

      // Lösche temporäre Datei nach 30 Sekunden
      Timer(const Duration(seconds: 30), () async {
        try {
          if (await file.exists()) {
            await file.delete();
            _log.i('🗑️ PDF-CV-Generator: Temporäre Datei gelöscht');
          }
        } catch (e) {
          _log.w(
            '⚠️ PDF-CV-Generator: Fehler beim Löschen der temporären Datei: $e',
          );
        }
      });
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-CV-Generator: Fehler beim PDF-Sharing',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Generiert Magazine-Template (verwendet Modern-Template als Basis)
  Future<void> _generateMagazineTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Modern Template als Basis für Magazine-Design
    await _generateModernTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }

  /// Generiert neues Infographic-Template für neue Vorlagen
  Future<void> _generateNewInfographicTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende die bereits vorhandene Infographic-Template-Implementierung
    await _generateCanvaTemplate(pdf, profile, template, fontRegular, fontBold);
  }

  /// Generiert Artistic-Template (verwendet Canva-Template als Basis)
  Future<void> _generateArtisticTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Canva Template als Basis für kreatives Design
    await _generateCanvaTemplate(pdf, profile, template, fontRegular, fontBold);
  }

  /// Generiert Startup-Template (verwendet Modern-Template als Basis)
  Future<void> _generateStartupTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Modern Template als Basis für Startup-Design
    await _generateModernTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }

  /// Generiert Academic-Template (verwendet Classic-Template als Basis)
  Future<void> _generateAcademicTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Classic Template als Basis für akademisches Design
    await _generateClassicTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }

  /// Generiert Fresh-Template (verwendet Minimalist-Template als Basis)
  Future<void> _generateFreshTemplate(
    pw.Document pdf,
    UserProfile profile,
    CvTemplate template,
    pw.Font fontRegular,
    pw.Font fontBold,
  ) async {
    // Verwende Minimalist Template als Basis für frisches Design
    await _generateMinimalistTemplate(
      pdf,
      profile,
      template,
      fontRegular,
      fontBold,
    );
  }
}

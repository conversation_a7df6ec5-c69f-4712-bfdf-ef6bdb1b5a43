import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../domain/models/cv_template.dart';
import '../../domain/models/user_profile.dart';

/// Helper-Klasse für neue PDF-Template-Komponenten
class PdfTemplateHelpers {
  /// Konvertiert Flutter Color zu PdfColor
  static PdfColor _colorToPdfColor(Color color) {
    return PdfColor(
      color.red / 255.0,
      color.green / 255.0,
      color.blue / 255.0,
      color.alpha / 255.0,
    );
  }

  /// Erstellt Profilbild-Widget
  static pw.Widget buildProfileImageWidget(
    pw.ImageProvider? profileImage, {
    double size = 80,
  }) {
    if (profileImage == null) {
      return pw.Container(
        width: size,
        height: size,
        decoration: pw.BoxDecoration(
          color: PdfColors.grey300,
          shape: pw.BoxShape.circle,
        ),
        child: pw.Center(
          child: pw.Text(
            '?',
            style: pw.TextStyle(fontSize: size * 0.4, color: PdfColors.grey600),
          ),
        ),
      );
    }

    return pw.Container(
      width: size,
      height: size,
      decoration: pw.BoxDecoration(
        shape: pw.BoxShape.circle,
        image: pw.DecorationImage(image: profileImage, fit: pw.BoxFit.cover),
      ),
    );
  }

  // ===== INFOGRAPHIC TEMPLATE HELPERS =====

  /// Erstellt Infographic Section Header
  static pw.Widget buildInfographicSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 15),
      decoration: pw.BoxDecoration(
        color: PdfColor(
          template.colorScheme.primaryColor.r / 255.0,
          template.colorScheme.primaryColor.g / 255.0,
          template.colorScheme.primaryColor.b / 255.0,
          0.1,
        ),
        borderRadius: pw.BorderRadius.circular(20),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fontBold,
          fontSize: 16,
          color: _colorToPdfColor(template.colorScheme.primaryColor),
          letterSpacing: 0.8,
        ),
      ),
    );
  }

  /// Erstellt Infographic Skills mit Progress Bars
  static pw.Widget buildInfographicSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          skills.take(6).map((skill) {
            // Simuliere Skill-Level (70-95%)
            final skillLevel = 0.7 + (skill.hashCode % 25) / 100;

            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 12),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        skill,
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 12,
                          color: PdfColors.grey800,
                        ),
                      ),
                      pw.Text(
                        '${(skillLevel * 100).round()}%',
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 10,
                          color: PdfColors.grey600,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 4),
                  pw.Stack(
                    children: [
                      // Hintergrund-Bar
                      pw.Container(
                        height: 6,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.grey300,
                          borderRadius: pw.BorderRadius.circular(3),
                        ),
                      ),
                      // Progress-Bar
                      pw.Container(
                        height: 6,
                        width: 150 * skillLevel, // 150px * Skill-Level
                        decoration: pw.BoxDecoration(
                          color: _colorToPdfColor(
                            template.colorScheme.primaryColor,
                          ),
                          borderRadius: pw.BorderRadius.circular(3),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Infographic Work Experience mit Timeline
  static pw.Widget buildInfographicWorkExperience(
    List<WorkExperience> experiences,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          experiences.map((exp) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 25),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Timeline-Punkt
                  pw.Container(
                    width: 12,
                    height: 12,
                    margin: const pw.EdgeInsets.only(top: 4),
                    decoration: pw.BoxDecoration(
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                      shape: pw.BoxShape.circle,
                    ),
                  ),
                  pw.SizedBox(width: 15),

                  // Content
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          exp.position,
                          style: pw.TextStyle(
                            font: fontBold,
                            fontSize: 14,
                            color: PdfColors.black,
                          ),
                        ),
                        pw.SizedBox(height: 4),
                        pw.Text(
                          exp.company,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 12,
                            color: _colorToPdfColor(
                              template.colorScheme.primaryColor,
                            ),
                          ),
                        ),
                        pw.SizedBox(height: 4),
                        pw.Text(
                          '${exp.startDate} - ${exp.endDate}',
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 10,
                            color: PdfColors.grey600,
                          ),
                        ),
                        if (exp.description.isNotEmpty) ...[
                          pw.SizedBox(height: 8),
                          pw.Text(
                            exp.description,
                            style: pw.TextStyle(
                              font: fontRegular,
                              fontSize: 11,
                              color: PdfColors.grey800,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  // ===== ARTISTIC TEMPLATE HELPERS =====

  /// Erstellt Artistic Header mit kreativen Elementen
  static pw.Widget buildArtisticHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Container(
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Asymmetrisches Layout - Profilbild rechts
              pw.Expanded(
                flex: 7,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Name mit kreativer Typografie
                    pw.Text(
                      profile.name ?? 'Name nicht verfügbar',
                      style: pw.TextStyle(
                        font: fontBold,
                        fontSize: 42,
                        color: _colorToPdfColor(
                          template.colorScheme.primaryColor,
                        ),
                      ),
                    ),
                    pw.SizedBox(height: 8),

                    // Job-Präferenzen kreativ gestaltet
                    if (profile.jobPreferences != null) ...[
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 15,
                          vertical: 8,
                        ),
                        decoration: pw.BoxDecoration(
                          color: PdfColor(
                            template.colorScheme.primaryColor.r / 255.0,
                            template.colorScheme.primaryColor.g / 255.0,
                            template.colorScheme.primaryColor.b / 255.0,
                            0.1,
                          ),
                          borderRadius: pw.BorderRadius.circular(25),
                        ),
                        child: pw.Text(
                          profile.jobPreferences!,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 14,
                            color: _colorToPdfColor(
                              template.colorScheme.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              pw.SizedBox(width: 20),

              // Profilbild mit kreativem Rahmen
              pw.Expanded(
                flex: 3,
                child: pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                      width: 3,
                    ),
                    borderRadius: pw.BorderRadius.circular(15),
                  ),
                  child: buildProfileImageWidget(profileImage, size: 120),
                ),
              ),
            ],
          ),

          pw.SizedBox(height: 20),

          // Kontaktdaten kreativ angeordnet
          pw.Row(
            children: [
              if (profile.email != null) ...[
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(15),
                  ),
                  child: pw.Text(
                    profile.email!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 11,
                      color: PdfColors.grey700,
                    ),
                  ),
                ),
                pw.SizedBox(width: 15),
              ],
              if (profile.phone != null) ...[
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(15),
                  ),
                  child: pw.Text(
                    profile.phone!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 11,
                      color: PdfColors.grey700,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Erstellt Artistic Section Header
  static pw.Widget buildArtisticSection(
    String title,
    CvTemplate template,
    pw.Font fontBold,
  ) {
    return pw.Container(
      child: pw.Row(
        children: [
          // Kreativer Akzent
          pw.Container(
            width: 6,
            height: 25,
            decoration: pw.BoxDecoration(
              color: _colorToPdfColor(template.colorScheme.primaryColor),
              borderRadius: pw.BorderRadius.circular(3),
            ),
          ),
          pw.SizedBox(width: 15),
          pw.Text(
            title,
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 20,
              color: PdfColors.black,
              letterSpacing: 1.0,
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Artistic Work Experience
  static pw.Widget buildArtisticWorkExperience(
    List<WorkExperience> experiences,
    pw.Font fontRegular,
    pw.Font fontBold,
    CvTemplate template,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children:
          experiences.map((exp) {
            return pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 25),
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey50,
                borderRadius: pw.BorderRadius.circular(12),
                border: pw.Border(
                  left: pw.BorderSide(
                    color: _colorToPdfColor(template.colorScheme.primaryColor),
                    width: 4,
                  ),
                ),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    exp.position,
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: 16,
                      color: PdfColors.black,
                    ),
                  ),
                  pw.SizedBox(height: 6),
                  pw.Text(
                    exp.company,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 14,
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    '${exp.startDate} - ${exp.endDate}',
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 11,
                      color: PdfColors.grey600,
                    ),
                  ),
                  if (exp.description.isNotEmpty) ...[
                    pw.SizedBox(height: 10),
                    pw.Text(
                      exp.description,
                      style: pw.TextStyle(
                        font: fontRegular,
                        fontSize: 12,
                        color: PdfColors.grey800,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
    );
  }

  /// Erstellt Artistic Skills
  static pw.Widget buildArtisticSkills(
    List<String> skills,
    pw.Font fontRegular,
    CvTemplate template,
  ) {
    return pw.Wrap(
      spacing: 10,
      runSpacing: 10,
      children:
          skills.map((skill) {
            return pw.Container(
              padding: const pw.EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: pw.BoxDecoration(
                color: PdfColor(
                  template.colorScheme.primaryColor.r / 255.0,
                  template.colorScheme.primaryColor.g / 255.0,
                  template.colorScheme.primaryColor.b / 255.0,
                  0.15,
                ),
                borderRadius: pw.BorderRadius.circular(20),
                border: pw.Border.all(
                  color: PdfColor(
                    template.colorScheme.primaryColor.r / 255.0,
                    template.colorScheme.primaryColor.g / 255.0,
                    template.colorScheme.primaryColor.b / 255.0,
                    0.3,
                  ),
                  width: 1,
                ),
              ),
              child: pw.Text(
                skill,
                style: pw.TextStyle(
                  font: fontRegular,
                  fontSize: 11,
                  color: _colorToPdfColor(template.colorScheme.primaryColor),
                ),
              ),
            );
          }).toList(),
    );
  }

  // ===== STARTUP TEMPLATE HELPERS =====

  /// Erstellt Startup Header
  static pw.Widget buildStartupHeader(
    UserProfile profile,
    CvTemplate template,
    pw.Font fontBold,
    pw.Font fontRegular,
    pw.ImageProvider? profileImage,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(25),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.1,
            ),
            PdfColor(
              template.colorScheme.primaryColor.r / 255.0,
              template.colorScheme.primaryColor.g / 255.0,
              template.colorScheme.primaryColor.b / 255.0,
              0.05,
            ),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          buildProfileImageWidget(profileImage, size: 90),
          pw.SizedBox(width: 25),

          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  profile.name ?? 'Name nicht verfügbar',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 32,
                    color: PdfColors.black,
                  ),
                ),
                pw.SizedBox(height: 8),

                if (profile.jobPreferences != null) ...[
                  pw.Text(
                    profile.jobPreferences!,
                    style: pw.TextStyle(
                      font: fontRegular,
                      fontSize: 16,
                      color: _colorToPdfColor(
                        template.colorScheme.primaryColor,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 12),
                ],

                pw.Row(
                  children: [
                    if (profile.email != null) ...[
                      pw.Text(
                        profile.email!,
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      if (profile.phone != null) pw.SizedBox(width: 20),
                    ],
                    if (profile.phone != null) ...[
                      pw.Text(
                        profile.phone!,
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

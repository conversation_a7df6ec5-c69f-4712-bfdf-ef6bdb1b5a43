import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../domain/models/user_profile.dart';
import '../../domain/models/cv_template.dart';
import '../providers/user_profile_provider.dart';
import '../providers/cv_template_provider.dart';
import '../providers/pdf_cv_generator_provider.dart';

/// Service für automatische CV-Synchronisation
class CvSyncService {
  static final Logger _log = Logger();
  final Ref _ref;
  
  StreamSubscription<UserProfile>? _profileSubscription;
  StreamSubscription<CvTemplate?>? _templateSubscription;
  Timer? _debounceTimer;
  
  UserProfile? _lastProfile;
  CvTemplate? _lastTemplate;
  
  static const Duration _debounceDelay = Duration(seconds: 2);

  CvSyncService(this._ref);

  /// Startet die automatische Synchronisation
  void startAutoSync() {
    _log.i('🔥 CV-Sync: Starte automatische Synchronisation');
    
    // Überwache Profil-Änderungen
    _profileSubscription = _ref.listen(
      userProfileProvider,
      (previous, next) {
        next.when(
          data: (profile) => _onProfileChanged(profile),
          loading: () => {},
          error: (error, stack) => _log.e('❌ CV-Sync: Fehler beim Profil-Update: $error'),
        );
      },
    );
    
    // Überwache Template-Änderungen
    _templateSubscription = _ref.listen(
      selectedTemplateProvider,
      (previous, next) => _onTemplateChanged(next),
    );
    
    _log.i('✅ CV-Sync: Automatische Synchronisation gestartet');
  }

  /// Stoppt die automatische Synchronisation
  void stopAutoSync() {
    _log.i('🔥 CV-Sync: Stoppe automatische Synchronisation');
    
    _profileSubscription?.cancel();
    _templateSubscription?.cancel();
    _debounceTimer?.cancel();
    
    _profileSubscription = null;
    _templateSubscription = null;
    _debounceTimer = null;
    
    _log.i('✅ CV-Sync: Automatische Synchronisation gestoppt');
  }

  /// Behandelt Profil-Änderungen
  void _onProfileChanged(UserProfile newProfile) {
    if (_lastProfile == null) {
      _lastProfile = newProfile;
      return;
    }
    
    // Prüfe ob sich relevante Profildaten geändert haben
    if (_hasRelevantProfileChanges(_lastProfile!, newProfile)) {
      _log.i('🔥 CV-Sync: Relevante Profil-Änderungen erkannt');
      _lastProfile = newProfile;
      _scheduleSync('profile_change');
    }
  }

  /// Behandelt Template-Änderungen
  void _onTemplateChanged(CvTemplate? newTemplate) {
    if (newTemplate != null && newTemplate != _lastTemplate) {
      _log.i('🔥 CV-Sync: Template-Änderung erkannt: ${newTemplate.name}');
      _lastTemplate = newTemplate;
      _scheduleSync('template_change');
    }
  }

  /// Plant Synchronisation mit Debouncing
  void _scheduleSync(String reason) {
    _debounceTimer?.cancel();
    
    _debounceTimer = Timer(_debounceDelay, () {
      _performSync(reason);
    });
    
    _log.i('🔥 CV-Sync: Synchronisation geplant (Grund: $reason)');
  }

  /// Führt die Synchronisation durch
  Future<void> _performSync(String reason) async {
    try {
      _log.i('🔥 CV-Sync: Starte Synchronisation (Grund: $reason)');
      
      // Prüfe ob eine PDF bereits generiert wurde
      final pdfNotifier = _ref.read(pdfGenerationProvider.notifier);
      if (!pdfNotifier.hasPdf) {
        _log.i('🔥 CV-Sync: Keine PDF vorhanden, überspringe Synchronisation');
        return;
      }
      
      // Regeneriere PDF mit aktuellen Daten
      await pdfNotifier.generatePdf();
      
      _log.i('✅ CV-Sync: Synchronisation abgeschlossen');
      
    } catch (e, stackTrace) {
      _log.e('❌ CV-Sync: Fehler bei Synchronisation', error: e, stackTrace: stackTrace);
    }
  }

  /// Prüft ob sich relevante Profildaten geändert haben
  bool _hasRelevantProfileChanges(UserProfile oldProfile, UserProfile newProfile) {
    // Name
    if (oldProfile.name != newProfile.name) return true;
    
    // Kontaktdaten
    if (oldProfile.email != newProfile.email) return true;
    if (oldProfile.phone != newProfile.phone) return true;
    if (oldProfile.address != newProfile.address) return true;
    
    // Berufserfahrung
    if (_hasWorkExperienceChanges(oldProfile.workExperience, newProfile.workExperience)) {
      return true;
    }
    
    // Ausbildung
    if (_hasEducationChanges(oldProfile.education, newProfile.education)) {
      return true;
    }
    
    // Skills
    if (_hasSkillsChanges(oldProfile.skills, newProfile.skills)) {
      return true;
    }
    
    // Job-Präferenzen
    if (oldProfile.jobPreferences != newProfile.jobPreferences) return true;
    
    return false;
  }

  /// Prüft Änderungen in Berufserfahrung
  bool _hasWorkExperienceChanges(List<WorkExperience>? oldList, List<WorkExperience>? newList) {
    if (oldList == null && newList == null) return false;
    if (oldList == null || newList == null) return true;
    if (oldList.length != newList.length) return true;
    
    for (int i = 0; i < oldList.length; i++) {
      final old = oldList[i];
      final newExp = newList[i];
      
      if (old.position != newExp.position ||
          old.company != newExp.company ||
          old.description != newExp.description ||
          old.startDate != newExp.startDate ||
          old.endDate != newExp.endDate) {
        return true;
      }
    }
    
    return false;
  }

  /// Prüft Änderungen in Ausbildung
  bool _hasEducationChanges(List<Education>? oldList, List<Education>? newList) {
    if (oldList == null && newList == null) return false;
    if (oldList == null || newList == null) return true;
    if (oldList.length != newList.length) return true;
    
    for (int i = 0; i < oldList.length; i++) {
      final old = oldList[i];
      final newEdu = newList[i];
      
      if (old.degree != newEdu.degree ||
          old.institution != newEdu.institution ||
          old.fieldOfStudy != newEdu.fieldOfStudy ||
          old.startDate != newEdu.startDate ||
          old.endDate != newEdu.endDate) {
        return true;
      }
    }
    
    return false;
  }

  /// Prüft Änderungen in Skills
  bool _hasSkillsChanges(List<String>? oldList, List<String>? newList) {
    if (oldList == null && newList == null) return false;
    if (oldList == null || newList == null) return true;
    if (oldList.length != newList.length) return true;
    
    // Sortiere Listen für Vergleich
    final sortedOld = List<String>.from(oldList)..sort();
    final sortedNew = List<String>.from(newList)..sort();
    
    for (int i = 0; i < sortedOld.length; i++) {
      if (sortedOld[i] != sortedNew[i]) return true;
    }
    
    return false;
  }

  /// Erzwingt eine sofortige Synchronisation
  Future<void> forceSyncNow() async {
    _debounceTimer?.cancel();
    await _performSync('manual_force');
  }

  /// Gibt Sync-Status zurück
  Map<String, dynamic> getSyncStatus() {
    return {
      'isActive': _profileSubscription != null && _templateSubscription != null,
      'hasPendingSync': _debounceTimer?.isActive ?? false,
      'lastProfile': _lastProfile?.toJson(),
      'lastTemplate': _lastTemplate?.toJson(),
    };
  }

  /// Setzt Sync-Konfiguration
  void configureSyncSettings({
    Duration? debounceDelay,
    bool? syncOnProfileChange,
    bool? syncOnTemplateChange,
  }) {
    // TODO: Implementiere konfigurierbare Sync-Einstellungen
    _log.i('🔥 CV-Sync: Sync-Einstellungen konfiguriert');
  }

  /// Bereinigt Ressourcen
  void dispose() {
    stopAutoSync();
    _log.i('🔥 CV-Sync: Service bereinigt');
  }
}

/// Provider für CV-Sync-Service
final cvSyncServiceProvider = Provider<CvSyncService>((ref) {
  final service = CvSyncService(ref);
  
  // Starte Auto-Sync beim Erstellen des Services
  service.startAutoSync();
  
  // Bereinige beim Dispose
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider für Sync-Status
final cvSyncStatusProvider = Provider<Map<String, dynamic>>((ref) {
  final service = ref.watch(cvSyncServiceProvider);
  return service.getSyncStatus();
});

/// Provider für manuelle Sync-Auslösung
final forceCvSyncProvider = Provider<Future<void> Function()>((ref) {
  final service = ref.read(cvSyncServiceProvider);
  return () => service.forceSyncNow();
});

/// Auto-Sync Initialisierungs-Provider
final autoSyncInitProvider = Provider<void>((ref) {
  // Dieser Provider sorgt dafür, dass der CV-Sync-Service initialisiert wird
  ref.watch(cvSyncServiceProvider);
  
  // Überwache auch den Auto-Update-Provider für zusätzliche Integration
  ref.watch(autoUpdatePdfProvider);
});

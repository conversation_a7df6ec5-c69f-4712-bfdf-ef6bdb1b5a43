import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:ki_test/src/utils/cv_storage_helper.dart';

/// Service für das Tracking von E-Mail-Intents und temporären Dateien
///
/// Verhindert das vorzeitige Löschen von temporären CV-Dateien während
/// der E-Mail-App aktiv ist und wartet auf das Intent-Result.
class EmailIntentTracker {
  static const String _channelName = 'ki_test/email_intent_tracker';
  static const MethodChannel _channel = MethodChannel(_channelName);

  // Singleton Pattern
  static final EmailIntentTracker _instance = EmailIntentTracker._internal();
  factory EmailIntentTracker() => _instance;
  EmailIntentTracker._internal();

  // Tracking-Variablen
  static bool _emailInProgress = false;
  static String? _pendingTempFilePath;
  static Timer? _fallbackTimer;
  static final List<String> _pendingTempFiles = [];

  // Callbacks für Status-Updates
  static final StreamController<EmailIntentStatus> _statusController =
      StreamController<EmailIntentStatus>.broadcast();

  /// Stream für E-Mail-Intent-Status-Updates
  static Stream<EmailIntentStatus> get statusStream => _statusController.stream;

  /// Initialisiert den EmailIntentTracker
  static Future<void> initialize() async {
    try {
      // Registriere Callback für Intent-Results von Android
      _channel.setMethodCallHandler(_handleMethodCall);
      debugPrint('🔥 EmailIntentTracker: Initialisiert');
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Initialisierung fehlgeschlagen: $e');
    }
  }

  /// Startet das Tracking für einen E-Mail-Intent
  ///
  /// [tempFilePath] - Pfad zur temporären CV-Datei die geschützt werden soll
  /// [timeoutMinutes] - Fallback-Timeout in Minuten (default: 10)
  static Future<void> startEmailIntent({
    required String tempFilePath,
    int timeoutMinutes = 10,
  }) async {
    try {
      debugPrint('🔥 EmailIntentTracker: Starte E-Mail-Intent-Tracking');
      debugPrint('🔥 Temp-Datei: $tempFilePath');

      // Setze Tracking-Status
      _emailInProgress = true;
      _pendingTempFilePath = tempFilePath;
      _pendingTempFiles.add(tempFilePath);

      // Sende Status-Update
      _statusController.add(EmailIntentStatus.started);

      // Starte Fallback-Timer (falls Intent-Result nicht ankommt)
      _startFallbackTimer(timeoutMinutes);

      // Informiere Android über den gestarteten E-Mail-Intent
      await _channel.invokeMethod('onEmailIntentStarted', {
        'tempFilePath': tempFilePath,
        'timeoutMinutes': timeoutMinutes,
      });

      debugPrint('✅ EmailIntentTracker: E-Mail-Intent-Tracking gestartet');
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler beim Starten: $e');
      // Bei Fehlern sofort cleanup
      await _cleanup();
    }
  }

  /// Behandelt MethodCall-Callbacks von Android
  static Future<void> _handleMethodCall(MethodCall call) async {
    try {
      debugPrint('🔥 EmailIntentTracker: MethodCall empfangen: ${call.method}');

      switch (call.method) {
        case 'onEmailIntentCompleted':
          final success = call.arguments['success'] as bool? ?? false;
          final resultCode = call.arguments['resultCode'] as int? ?? -1;
          debugPrint(
            '🔥 E-Mail-Intent abgeschlossen: success=$success, resultCode=$resultCode',
          );
          await _onEmailIntentCompleted(success, resultCode);
          break;

        case 'onEmailIntentCancelled':
          debugPrint('🔥 E-Mail-Intent abgebrochen');
          await _onEmailIntentCancelled();
          break;

        case 'onEmailIntentError':
          final error =
              call.arguments['error'] as String? ?? 'Unbekannter Fehler';
          debugPrint('🔥 E-Mail-Intent Fehler: $error');
          await _onEmailIntentError(error);
          break;

        default:
          debugPrint('⚠️ Unbekannte MethodCall: ${call.method}');
      }
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler in _handleMethodCall: $e');
    }
  }

  /// Wird aufgerufen, wenn der E-Mail-Intent erfolgreich abgeschlossen wurde
  static Future<void> _onEmailIntentCompleted(
    bool success,
    int resultCode,
  ) async {
    try {
      debugPrint(
        '🔥 EmailIntentTracker: E-Mail-Intent abgeschlossen (success: $success)',
      );

      // Sende Status-Update
      _statusController.add(
        success ? EmailIntentStatus.completed : EmailIntentStatus.failed,
      );

      // Cleanup durchführen
      await _cleanup();

      debugPrint(
        '✅ EmailIntentTracker: Cleanup nach E-Mail-Intent abgeschlossen',
      );
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler in _onEmailIntentCompleted: $e');
    }
  }

  /// Wird aufgerufen, wenn der E-Mail-Intent abgebrochen wurde
  static Future<void> _onEmailIntentCancelled() async {
    try {
      debugPrint('🔥 EmailIntentTracker: E-Mail-Intent abgebrochen');

      // Sende Status-Update
      _statusController.add(EmailIntentStatus.cancelled);

      // Cleanup durchführen
      await _cleanup();

      debugPrint(
        '✅ EmailIntentTracker: Cleanup nach E-Mail-Intent-Abbruch abgeschlossen',
      );
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler in _onEmailIntentCancelled: $e');
    }
  }

  /// Wird aufgerufen, wenn ein Fehler beim E-Mail-Intent auftritt
  static Future<void> _onEmailIntentError(String error) async {
    try {
      debugPrint('🔥 EmailIntentTracker: E-Mail-Intent Fehler: $error');

      // Sende Status-Update
      _statusController.add(EmailIntentStatus.error);

      // Cleanup durchführen
      await _cleanup();

      debugPrint(
        '✅ EmailIntentTracker: Cleanup nach E-Mail-Intent-Fehler abgeschlossen',
      );
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler in _onEmailIntentError: $e');
    }
  }

  /// Startet den Fallback-Timer
  static void _startFallbackTimer(int timeoutMinutes) {
    // Stoppe vorherigen Timer
    _fallbackTimer?.cancel();

    // Starte neuen Timer
    _fallbackTimer = Timer(Duration(minutes: timeoutMinutes), () async {
      debugPrint(
        '⏰ EmailIntentTracker: Fallback-Timer abgelaufen ($timeoutMinutes Min)',
      );

      if (_emailInProgress) {
        debugPrint(
          '🔥 E-Mail-Intent noch aktiv - führe Fallback-Cleanup durch',
        );
        _statusController.add(EmailIntentStatus.timeout);
        await _cleanup();
      }
    });

    debugPrint(
      '⏰ EmailIntentTracker: Fallback-Timer gestartet ($timeoutMinutes Min)',
    );
  }

  /// Führt das Cleanup durch (löscht temporäre Dateien)
  static Future<void> _cleanup() async {
    try {
      debugPrint('🔥 EmailIntentTracker: Starte Cleanup...');

      // Stoppe Timer
      _fallbackTimer?.cancel();
      _fallbackTimer = null;

      // Lösche alle temporären Dateien
      for (final filePath in _pendingTempFiles) {
        try {
          if (filePath.isNotEmpty) {
            final success = await CvStorageHelper.deleteTemporaryCvCopy(
              filePath,
            );
            debugPrint(
              '🔥 Temporäre Datei gelöscht: $filePath (success: $success)',
            );
          }
        } catch (e) {
          debugPrint('❌ Fehler beim Löschen von $filePath: $e');
        }
      }

      // Reset Tracking-Variablen
      _emailInProgress = false;
      _pendingTempFilePath = null;
      _pendingTempFiles.clear();

      // Sende Status-Update
      _statusController.add(EmailIntentStatus.cleanedUp);

      debugPrint('✅ EmailIntentTracker: Cleanup abgeschlossen');
    } catch (e) {
      debugPrint('❌ EmailIntentTracker: Fehler beim Cleanup: $e');
    }
  }

  /// Prüft, ob gerade ein E-Mail-Intent aktiv ist
  static bool get isEmailInProgress => _emailInProgress;

  /// Gibt den Pfad der aktuell geschützten temporären Datei zurück
  static String? get currentTempFilePath => _pendingTempFilePath;

  /// Gibt alle aktuell geschützten temporären Dateien zurück
  static List<String> get currentTempFiles =>
      List.unmodifiable(_pendingTempFiles);

  /// Manueller Cleanup (für Notfälle)
  static Future<void> forceCleanup() async {
    debugPrint('🔥 EmailIntentTracker: Manueller Cleanup erzwungen');
    await _cleanup();
  }

  /// Dispose-Methode
  static void dispose() {
    _fallbackTimer?.cancel();
    _statusController.close();
    debugPrint('🔥 EmailIntentTracker: Disposed');
  }
}

/// Status-Enum für E-Mail-Intent-Tracking
enum EmailIntentStatus {
  started, // E-Mail-Intent gestartet
  completed, // E-Mail-Intent erfolgreich abgeschlossen
  cancelled, // E-Mail-Intent abgebrochen
  failed, // E-Mail-Intent fehlgeschlagen
  error, // Fehler beim E-Mail-Intent
  timeout, // Fallback-Timer abgelaufen
  cleanedUp, // Cleanup abgeschlossen
}

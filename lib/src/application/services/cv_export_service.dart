import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

import '../../domain/models/user_profile.dart';
import '../../domain/models/cv_template.dart';
import 'pdf_cv_generator_service.dart';

/// Service für CV-Export und Sharing
class CvExportService {
  static final Logger _log = Logger();
  final PdfCvGeneratorService _pdfGeneratorService;

  CvExportService(this._pdfGeneratorService);

  /// Exportiert CV als PDF-Datei
  Future<String> exportCvAsPdf({
    required UserProfile profile,
    required CvTemplate template,
    String? customFileName,
  }) async {
    try {
      _log.i('🔥 CV-Export: Starte PDF-Export für Template ${template.name}');

      // Generiere PDF
      final pdfBytes = await _pdfGeneratorService.generateCvPdf(
        profile: profile,
        template: template,
      );

      // Erstelle Dateinamen
      final fileName = customFileName ?? _generateFileName(profile, template);

      // Speichere PDF
      final filePath = await _pdfGeneratorService.savePdfToDevice(
        pdfBytes,
        fileName,
      );

      _log.i('✅ CV-Export: PDF erfolgreich exportiert nach $filePath');
      return filePath;
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim PDF-Export',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Teilt CV als PDF
  Future<void> shareCvAsPdf({
    required UserProfile profile,
    required CvTemplate template,
    String? customFileName,
    String? shareText,
  }) async {
    try {
      _log.i('🔥 CV-Export: Starte PDF-Sharing für Template ${template.name}');

      // Generiere PDF
      final pdfBytes = await _pdfGeneratorService.generateCvPdf(
        profile: profile,
        template: template,
      );

      // Erstelle Dateinamen
      final fileName = customFileName ?? _generateFileName(profile, template);

      // Teile PDF
      await _pdfGeneratorService.sharePdf(pdfBytes, fileName);

      _log.i('✅ CV-Export: PDF erfolgreich geteilt');
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim PDF-Sharing',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Sendet CV per E-Mail
  Future<bool> sendCvViaEmail({
    required UserProfile profile,
    required CvTemplate template,
    required String recipientEmail,
    String? subject,
    String? body,
    String? customFileName,
  }) async {
    try {
      _log.i(
        '🔥 CV-Export: Starte E-Mail-Versand für Template ${template.name}',
      );

      // Generiere PDF
      final pdfBytes = await _pdfGeneratorService.generateCvPdf(
        profile: profile,
        template: template,
      );

      // Erstelle temporäre Datei
      final fileName = customFileName ?? _generateFileName(profile, template);
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(pdfBytes);

      // Erstelle E-Mail
      final email = Email(
        recipients: [recipientEmail],
        subject:
            subject ?? 'Bewerbungsunterlagen - ${profile.name ?? 'Lebenslauf'}',
        body: body ?? _generateDefaultEmailBody(profile),
        attachmentPaths: [tempFile.path],
      );

      // Sende E-Mail
      await FlutterEmailSender.send(email);

      // Lösche temporäre Datei nach kurzer Verzögerung
      Future.delayed(const Duration(seconds: 30), () async {
        try {
          if (await tempFile.exists()) {
            await tempFile.delete();
            _log.i('Temporäre CV-Datei gelöscht: ${tempFile.path}');
          }
        } catch (e) {
          _log.w('Fehler beim Löschen der temporären CV-Datei: $e');
        }
      });

      _log.i('✅ CV-Export: E-Mail erfolgreich versendet');
      return true;
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim E-Mail-Versand',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Erstellt Batch-Export für mehrere Templates
  Future<List<String>> batchExportCv({
    required UserProfile profile,
    required List<CvTemplate> templates,
    String? baseFileName,
  }) async {
    try {
      _log.i(
        '🔥 CV-Export: Starte Batch-Export für ${templates.length} Templates',
      );

      final List<String> exportedFiles = [];

      for (final template in templates) {
        try {
          final fileName =
              baseFileName != null
                  ? '${baseFileName}_${template.name.replaceAll(' ', '_')}.pdf'
                  : _generateFileName(profile, template);

          final filePath = await exportCvAsPdf(
            profile: profile,
            template: template,
            customFileName: fileName,
          );

          exportedFiles.add(filePath);
          _log.i('✅ CV-Export: Template ${template.name} exportiert');
        } catch (e) {
          _log.e(
            '❌ CV-Export: Fehler beim Export von Template ${template.name}: $e',
          );
          // Fahre mit nächstem Template fort
        }
      }

      _log.i(
        '✅ CV-Export: Batch-Export abgeschlossen (${exportedFiles.length}/${templates.length} erfolgreich)',
      );
      return exportedFiles;
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim Batch-Export',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Erstellt Vorschau-Thumbnail
  Future<Uint8List?> generatePreviewThumbnail({
    required UserProfile profile,
    required CvTemplate template,
    int? width,
    int? height,
  }) async {
    try {
      _log.i(
        '🔥 CV-Export: Generiere Vorschau-Thumbnail für Template ${template.name}',
      );

      // Generiere PDF
      final pdfBytes = await _pdfGeneratorService.generateCvPdf(
        profile: profile,
        template: template,
      );

      // TODO: PDF zu Bild konvertieren (benötigt zusätzliche Bibliothek wie pdf_render)
      // Für jetzt geben wir die PDF-Bytes zurück
      _log.i('✅ CV-Export: Vorschau-Thumbnail generiert');
      return pdfBytes;
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim Generieren des Vorschau-Thumbnails',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Validiert Export-Parameter
  bool validateExportParameters({
    required UserProfile profile,
    required CvTemplate template,
  }) {
    // Prüfe Mindestanforderungen für Export
    if (profile.name == null || profile.name!.isEmpty) {
      _log.w('⚠️ CV-Export: Name fehlt im Profil');
      return false;
    }

    if (profile.email == null || profile.email!.isEmpty) {
      _log.w('⚠️ CV-Export: E-Mail fehlt im Profil');
      return false;
    }

    // Prüfe ob mindestens eine Sektion mit Inhalt vorhanden ist
    final hasWorkExperience = profile.workExperience?.isNotEmpty == true;
    final hasEducation = profile.education?.isNotEmpty == true;
    final hasSkills = profile.skills?.isNotEmpty == true;

    if (!hasWorkExperience && !hasEducation && !hasSkills) {
      _log.w(
        '⚠️ CV-Export: Keine Inhalte (Berufserfahrung, Ausbildung oder Skills) im Profil',
      );
      return false;
    }

    _log.i('✅ CV-Export: Export-Parameter sind valide');
    return true;
  }

  /// Generiert Standard-Dateinamen
  String _generateFileName(UserProfile profile, CvTemplate template) {
    final userName = profile.name?.replaceAll(' ', '_') ?? 'Lebenslauf';
    final templateName = template.name.replaceAll(' ', '_');
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return '${userName}_CV_${templateName}_$timestamp.pdf';
  }

  /// Generiert Standard-E-Mail-Text
  String _generateDefaultEmailBody(UserProfile profile) {
    final name = profile.name ?? 'Bewerberin/Bewerber';

    return '''
Sehr geehrte Damen und Herren,

anbei sende ich Ihnen meine Bewerbungsunterlagen.

Über eine Rückmeldung würde ich mich sehr freuen.

Mit freundlichen Grüßen
$name

---
Diese E-Mail wurde automatisch mit der KI-Bewerbungsassistent App erstellt.
''';
  }

  /// Gibt unterstützte Export-Formate zurück
  List<String> getSupportedFormats() {
    return ['PDF']; // Aktuell nur PDF unterstützt
  }

  /// Gibt Export-Statistiken zurück
  Future<Map<String, dynamic>> getExportStats() async {
    try {
      // TODO: Implementiere Export-Statistiken aus SharedPreferences oder Datenbank
      return {
        'totalExports': 0,
        'lastExportDate': null,
        'favoriteTemplate': null,
        'exportsByTemplate': <String, int>{},
      };
    } catch (e) {
      _log.e('❌ CV-Export: Fehler beim Laden der Export-Statistiken: $e');
      return {};
    }
  }

  /// Bereinigt temporäre Export-Dateien
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      int deletedCount = 0;
      for (final file in files) {
        if (file is File &&
            file.path.endsWith('.pdf') &&
            file.path.contains('CV_')) {
          try {
            await file.delete();
            deletedCount++;
          } catch (e) {
            _log.w('Fehler beim Löschen der temporären Datei ${file.path}: $e');
          }
        }
      }

      _log.i('✅ CV-Export: $deletedCount temporäre CV-Dateien bereinigt');
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Export: Fehler beim Bereinigen temporärer Dateien',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
}

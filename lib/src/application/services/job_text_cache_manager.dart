import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/entities/extracted_job_text.dart';
import 'text_extraction_service.dart';
import '../../core/logging/app_logger.dart';

/// Erweiterte Cache-Manager für intelligente Deduplizierung
/// Verhindert doppelte Text-Extraktion zwischen Favoriten und beworbenen Jobs
class JobTextCacheManager {
  final SupabaseClient _supabaseClient;
  final TextExtractionService _textExtractionService;
  final AppLogger _log = AppLogger('JobTextCacheManager');
  
  // In-Memory Cache für aktive Extraktionen (verhindert parallele Extraktionen)
  final Map<String, Future<ExtractedJobText?>> _activeExtractions = {};
  
  // Cache für kürzlich extrahierte Texte
  final Map<String, ExtractedJobText> _recentCache = {};
  static const int _maxRecentCacheSize = 50;
  
  // Timer für periodische Bereinigung
  Timer? _cleanupTimer;

  JobTextCacheManager(this._supabaseClient, this._textExtractionService) {
    _startPeriodicCleanup();
  }

  /// Intelligente Text-Extraktion mit Deduplizierung
  /// Verhindert doppelte Extraktion wenn Job sowohl in Favoriten als auch beworbenen Jobs ist
  Future<ExtractedJobText?> getOrExtractJobText(JobEntity job, {
    String? requestSource, // 'favorites' oder 'applied_jobs' für Logging
    String? sourceType, // 'favorite' oder 'applied_job' für Timestamps
    DateTime? sourceTimestamp, // Wann zu Favoriten/beworben hinzugefügt
  }) async {
    final jobId = job.id;
    
    // 1. Prüfe ob bereits eine Extraktion für diesen Job läuft
    if (_activeExtractions.containsKey(jobId)) {
      _log.i('Warte auf laufende Extraktion für Job $jobId (Quelle: $requestSource)');
      return await _activeExtractions[jobId];
    }

    // 2. Prüfe Recent-Cache
    if (_recentCache.containsKey(jobId)) {
      final cachedText = _recentCache[jobId]!;
      _log.i('Job $jobId aus Recent-Cache geladen (Quelle: $requestSource)');
      await _updateLastAccessed(jobId);
      return cachedText;
    }

    // 3. Prüfe Supabase-Cache
    final cachedText = await _getCachedText(jobId);
    if (cachedText != null) {
      _log.i('Job $jobId aus Supabase-Cache geladen (Quelle: $requestSource)');
      _addToRecentCache(cachedText);
      await _updateLastAccessed(jobId);
      return cachedText;
    }

    // 4. Starte neue Extraktion und registriere sie als aktiv
    _log.i('Starte neue Text-Extraktion für Job $jobId (Quelle: $requestSource)');
    final extractionFuture = _performExtraction(job, sourceType: sourceType, sourceTimestamp: sourceTimestamp);
    _activeExtractions[jobId] = extractionFuture;

    try {
      final result = await extractionFuture;
      if (result != null) {
        _addToRecentCache(result);
        _log.i('Text-Extraktion für Job $jobId erfolgreich (${result.getSizeInKB().toStringAsFixed(1)}KB, Quelle: $requestSource)');
      } else {
        _log.w('Text-Extraktion für Job $jobId fehlgeschlagen (Quelle: $requestSource)');
      }
      return result;
    } finally {
      // Entferne aus aktiven Extraktionen
      _activeExtractions.remove(jobId);
    }
  }

  /// Führt die eigentliche Text-Extraktion durch
  Future<ExtractedJobText?> _performExtraction(
    JobEntity job, {
    String? sourceType,
    DateTime? sourceTimestamp,
  }) async {
    return await _textExtractionService.extractAndCacheJobText(
      job,
      sourceType: sourceType,
      sourceTimestamp: sourceTimestamp,
    );
  }

  /// Lädt gecachten Text aus Supabase
  Future<ExtractedJobText?> _getCachedText(String jobId) async {
    try {
      final response = await _supabaseClient
          .from('job_text_cache')
          .select()
          .eq('job_id', jobId)
          .maybeSingle();

      if (response != null) {
        return ExtractedJobText.fromJson(response);
      }
      return null;
    } catch (e) {
      _log.e('Fehler beim Laden des gecachten Texts für Job $jobId: $e');
      return null;
    }
  }

  /// Aktualisiert last_accessed Zeitstempel
  Future<void> _updateLastAccessed(String jobId) async {
    try {
      await _supabaseClient.rpc('update_job_text_cache_access', params: {
        'p_job_id': jobId,
      });
    } catch (e) {
      _log.w('Fehler beim Aktualisieren von last_accessed für Job $jobId: $e');
    }
  }

  /// Fügt Text zum Recent-Cache hinzu
  void _addToRecentCache(ExtractedJobText extractedText) {
    // Entferne älteste Einträge wenn Cache zu groß wird
    if (_recentCache.length >= _maxRecentCacheSize) {
      final oldestKey = _recentCache.keys.first;
      _recentCache.remove(oldestKey);
    }
    
    _recentCache[extractedText.jobId] = extractedText;
  }

  /// Startet periodische Bereinigung
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(hours: 1), (_) {
      _performCleanup();
    });
  }

  /// Führt Bereinigung durch
  Future<void> _performCleanup() async {
    try {
      // 1. Bereinige abgelaufene Einträge in Supabase
      final deletedCount = await _supabaseClient.rpc('cleanup_expired_job_text_cache');
      if (deletedCount > 0) {
        _log.i('$deletedCount abgelaufene Cache-Einträge bereinigt');
      }

      // 2. Bereinige Recent-Cache (entferne Einträge älter als 1 Stunde)
      final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
      final keysToRemove = <String>[];
      
      for (final entry in _recentCache.entries) {
        if (entry.value.lastAccessed != null && 
            entry.value.lastAccessed!.isBefore(oneHourAgo)) {
          keysToRemove.add(entry.key);
        }
      }
      
      for (final key in keysToRemove) {
        _recentCache.remove(key);
      }
      
      if (keysToRemove.isNotEmpty) {
        _log.i('${keysToRemove.length} Einträge aus Recent-Cache entfernt');
      }

      // 3. Bereinige aktive Extraktionen (falls welche hängen geblieben sind)
      final activeKeys = _activeExtractions.keys.toList();
      for (final key in activeKeys) {
        final future = _activeExtractions[key];
        if (future != null) {
          // Prüfe ob Future completed ist durch try-catch
          try {
            await future.timeout(Duration.zero);
            _activeExtractions.remove(key);
          } catch (e) {
            // Future ist noch nicht completed
          }
        }
      }

    } catch (e) {
      _log.e('Fehler bei der Cache-Bereinigung: $e');
    }
  }

  /// Prüft ob ein Job bereits gecacht ist (ohne Extraktion zu starten)
  Future<bool> isJobCached(String jobId) async {
    // Prüfe Recent-Cache
    if (_recentCache.containsKey(jobId)) {
      return true;
    }

    // Prüfe Supabase-Cache
    try {
      final response = await _supabaseClient
          .from('job_text_cache')
          .select('job_id')
          .eq('job_id', jobId)
          .maybeSingle();
      return response != null;
    } catch (e) {
      _log.w('Fehler beim Prüfen des Cache-Status für Job $jobId: $e');
      return false;
    }
  }

  /// Gibt Cache-Statistiken zurück
  Map<String, dynamic> getCacheStats() {
    return {
      'recent_cache_size': _recentCache.length,
      'active_extractions': _activeExtractions.length,
      'max_recent_cache_size': _maxRecentCacheSize,
    };
  }

  /// Leert alle Caches (für Testing)
  void clearAllCaches() {
    _recentCache.clear();
    _activeExtractions.clear();
    _log.i('Alle Caches geleert');
  }

  /// Leert alle Cache-Einträge des aktuellen Benutzers (für signOut)
  Future<void> clearUserCaches() async {
    try {
      // 1. Leere lokale Caches
      _recentCache.clear();
      _activeExtractions.clear();
      
      // 2. Leere Supabase-Cache für den aktuellen Benutzer
      final currentUser = _supabaseClient.auth.currentUser;
      if (currentUser != null) {
        await _supabaseClient.rpc('clear_user_job_text_cache', params: {
          'p_user_id': currentUser.id,
        });
        _log.i('Alle Cache-Einträge für Benutzer ${currentUser.id} geleert');
      } else {
        _log.w('Kein angemeldeter Benutzer gefunden - nur lokale Caches geleert');
      }
    } catch (e) {
      _log.e('Fehler beim Leeren der Benutzer-Caches: $e');
      // Leere trotzdem die lokalen Caches
      _recentCache.clear();
      _activeExtractions.clear();
    }
  }

  /// Dispose-Methode für Cleanup
  void dispose() {
    _cleanupTimer?.cancel();
    clearAllCaches();
    _log.i('JobTextCacheManager disposed');
  }
}

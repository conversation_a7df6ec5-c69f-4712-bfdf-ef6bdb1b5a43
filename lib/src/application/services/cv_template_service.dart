import 'package:logger/logger.dart';
import '../../domain/models/cv_template.dart';

/// Service für CV-Template-Management
class CvTemplateService {
  static final Logger _log = Logger();

  /// Alle verfügbaren CV-Templates - Nur echte, unterschiedliche Layouts
  static final List<CvTemplate> _templates = [
    // 1. CLASSIC TEMPLATE - Einspaltig mit Profilbild links
    CvTemplate(
      id: 'classic_blue',
      name: 'Klassisch',
      description: 'Traditionelles einspaltige Layout mit Profilbild',
      type: CvTemplateType.classic,
      colorScheme: CvColorScheme.blue,
      previewImagePath: 'assets/cv_previews/classic_blue.png',
      isPremium: false,
      layoutConfig: {
        'headerStyle': 'simple',
        'layout': 'single_column',
        'sectionSpacing': 20,
        'fontSize': {'header': 24, 'section': 16, 'body': 12},
      },
    ),

    // 2. MODERN TEMPLATE - Zweispaltig (30%/70%)
    CvTemplate(
      id: 'modern_green',
      name: 'Modern Zweispaltig',
      description: 'Zeitgemäßes zweispaltiges Layout mit Seitenleiste',
      type: CvTemplateType.modern,
      colorScheme: CvColorScheme.green,
      previewImagePath: 'assets/cv_previews/modern_green.png',
      isPremium: false,
      layoutConfig: {
        'headerStyle': 'modern',
        'layout': 'two_column',
        'leftColumnWidth': 0.3,
        'rightColumnWidth': 0.7,
        'sectionSpacing': 25,
        'fontSize': {'header': 28, 'section': 16, 'body': 12},
      },
    ),

    // 3. MINIMALIST TEMPLATE - Clean und einfach
    CvTemplate(
      id: 'minimalist_gray',
      name: 'Minimalistisch',
      description: 'Klares, reduziertes Design ohne Ablenkungen',
      type: CvTemplateType.minimalist,
      colorScheme: CvColorScheme.gray,
      previewImagePath: 'assets/cv_previews/minimalist_gray.png',
      isPremium: false,
      layoutConfig: {
        'headerStyle': 'minimal',
        'layout': 'clean_simple',
        'sectionSpacing': 15,
        'fontSize': {'header': 26, 'section': 16, 'body': 12},
      },
    ),

    // 4. TIMELINE TEMPLATE - Berufserfahrung als vertikale Timeline
    CvTemplate(
      id: 'timeline_navy',
      name: 'Timeline Professional',
      description:
          'Karriereverlauf als elegante Timeline mit professionellen Farben',
      type: CvTemplateType.timeline,
      colorScheme: CvColorScheme.navy,
      previewImagePath: 'assets/cv_previews/timeline_navy.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'timeline_professional',
        'layout': 'timeline_vertical',
        'sectionSpacing': 20,
        'fontSize': {'header': 28, 'section': 16, 'body': 12},
        'timelineElements': true,
        'professionalColors': true,
        'highContrast': true,
      },
    ),

    // 5. EXECUTIVE TEMPLATE - Premium Design für Führungskräfte
    CvTemplate(
      id: 'executive_charcoal',
      name: 'Executive Premium',
      description:
          'Hochwertiges Design für Führungskräfte und Senior-Positionen',
      type: CvTemplateType.executive,
      colorScheme: CvColorScheme.gray,
      previewImagePath: 'assets/cv_previews/executive_charcoal.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'executive_elegant',
        'layout': 'executive_premium',
        'sectionSpacing': 30,
        'fontSize': {'header': 32, 'section': 18, 'body': 12},
        'premiumAccents': true,
        'sophisticatedLayout': true,
        'highContrast': true,
      },
    ),

    // 6. INFOGRAFIK TEMPLATE - Skills als Progress-Bars und visuelle Elemente
    CvTemplate(
      id: 'infographic_blue',
      name: 'Infografik Professional',
      description:
          'Datenvisualisierung mit Progress-Bars und professionellen Charts',
      type: CvTemplateType.infographic,
      colorScheme: CvColorScheme.blue,
      previewImagePath: 'assets/cv_previews/infographic_blue.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'infographic_modern',
        'layout': 'infographic_visual',
        'sectionSpacing': 25,
        'fontSize': {'header': 30, 'section': 16, 'body': 12},
        'progressBars': true,
        'visualElements': true,
        'dataVisualization': true,
        'highContrast': true,
      },
    ),

    // 7. CREATIVE PORTFOLIO TEMPLATE - Künstlerisches Layout
    CvTemplate(
      id: 'creative_purple',
      name: 'Creative Portfolio',
      description: 'Künstlerisches Design für Kreative und Designer',
      type: CvTemplateType.creative,
      colorScheme: CvColorScheme.purple,
      previewImagePath: 'assets/cv_previews/creative_purple.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'creative_artistic',
        'layout': 'creative_portfolio',
        'sectionSpacing': 30,
        'fontSize': {'header': 34, 'section': 18, 'body': 12},
        'artisticElements': true,
        'asymmetricLayout': true,
        'creativeSpacing': true,
      },
    ),

    // 8. GRID TEMPLATE - Strukturiertes Grid-System
    CvTemplate(
      id: 'grid_navy',
      name: 'Corporate Grid',
      description: 'Strukturiertes Grid-Layout für Business-Professionals',
      type: CvTemplateType.grid,
      colorScheme: CvColorScheme.navy,
      previewImagePath: 'assets/cv_previews/grid_navy.png',
      isPremium: false,
      layoutConfig: {
        'headerStyle': 'grid_structured',
        'layout': 'grid_system',
        'sectionSpacing': 20,
        'fontSize': {'header': 28, 'section': 16, 'body': 11},
        'gridLayout': true,
        'structuredSections': true,
        'businessFormal': true,
      },
    ),

    // 9. TECHNICAL SPECS TEMPLATE - IT/Tech-fokussiert
    CvTemplate(
      id: 'technical_green',
      name: 'Technical Specs',
      description: 'Speziell für IT-Professionals und Entwickler',
      type: CvTemplateType.technical,
      colorScheme: CvColorScheme.green,
      previewImagePath: 'assets/cv_previews/technical_green.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'technical_code',
        'layout': 'technical_specs',
        'sectionSpacing': 18,
        'fontSize': {'header': 26, 'section': 15, 'body': 10},
        'codeElements': true,
        'technicalIcons': true,
        'skillBars': true,
        'monospaceAccents': true,
      },
    ),

    // 10. MAGAZINE STYLE TEMPLATE - Editorial-Design
    CvTemplate(
      id: 'magazine_red',
      name: 'Magazine Editorial',
      description: 'Editorial-Design inspiriert von Magazin-Layouts',
      type: CvTemplateType.magazine,
      colorScheme: CvColorScheme.red,
      previewImagePath: 'assets/cv_previews/magazine_red.png',
      isPremium: true,
      layoutConfig: {
        'headerStyle': 'magazine_editorial',
        'layout': 'magazine_columns',
        'sectionSpacing': 25,
        'fontSize': {'header': 32, 'section': 17, 'body': 11},
        'editorialLayout': true,
        'columnSystem': true,
        'magazineSpacing': true,
        'typographyFocus': true,
      },
    ),

    // 11. INFOGRAPHIC TEMPLATE - Datenvisualisierung
    CvTemplate(
      id: 'infographic_cyan',
      name: 'Infographic Pro',
      description: 'Datenreiche Darstellung mit Charts und Progress-Bars',
      type: CvTemplateType.infographic,
      colorScheme: CvColorScheme.cyan,
      previewImagePath: 'assets/cv_previews/infographic_cyan.png',
      isPremium: true,
      isCustomizable: true,
      layoutConfig: {
        'headerStyle': 'infographic_data',
        'layout': 'infographic_visual',
        'sectionSpacing': 22,
        'fontSize': {'header': 30, 'section': 16, 'body': 11},
        'skillCharts': true,
        'progressBars': true,
        'dataVisualization': true,
        'iconIntegration': true,
        'statisticalLayout': true,
      },
    ),

    // 12. ARTISTIC TEMPLATE - Kreatives Design
    CvTemplate(
      id: 'artistic_purple',
      name: 'Creative Artist',
      description: 'Künstlerisches Design für kreative Berufe',
      type: CvTemplateType.artistic,
      colorScheme: CvColorScheme.purple,
      previewImagePath: 'assets/cv_previews/artistic_purple.png',
      isPremium: true,
      isCustomizable: true,
      layoutConfig: {
        'headerStyle': 'artistic_creative',
        'layout': 'artistic_flow',
        'sectionSpacing': 28,
        'fontSize': {'header': 34, 'section': 17, 'body': 12},
        'creativeElements': true,
        'asymmetricalLayout': true,
        'artisticSpacing': true,
        'portfolioFocus': true,
        'colorAccents': true,
      },
    ),

    // 13. STARTUP TEMPLATE - Modern und dynamisch
    CvTemplate(
      id: 'startup_orange',
      name: 'Startup Dynamic',
      description: 'Dynamisches Design für Startup-Umfeld und Innovation',
      type: CvTemplateType.startup,
      colorScheme: CvColorScheme.orange,
      previewImagePath: 'assets/cv_previews/startup_orange.png',
      isPremium: false,
      isCustomizable: true,
      layoutConfig: {
        'headerStyle': 'startup_bold',
        'layout': 'startup_dynamic',
        'sectionSpacing': 24,
        'fontSize': {'header': 32, 'section': 16, 'body': 11},
        'boldAccents': true,
        'modernSpacing': true,
        'innovativeLayout': true,
        'energeticDesign': true,
        'techFriendly': true,
      },
    ),

    // 14. ACADEMIC TEMPLATE - Wissenschaftlich und strukturiert
    CvTemplate(
      id: 'academic_indigo',
      name: 'Academic Scholar',
      description: 'Strukturiertes Design für Wissenschaft und Forschung',
      type: CvTemplateType.academic,
      colorScheme: CvColorScheme.indigo,
      previewImagePath: 'assets/cv_previews/academic_indigo.png',
      isPremium: false,
      isCustomizable: true,
      layoutConfig: {
        'headerStyle': 'academic_formal',
        'layout': 'academic_structured',
        'sectionSpacing': 20,
        'fontSize': {'header': 28, 'section': 15, 'body': 11},
        'publicationFocus': true,
        'researchLayout': true,
        'formalStructure': true,
        'academicSpacing': true,
        'scholarlyDesign': true,
      },
    ),

    // 15. FRESH TEMPLATE - Jung und modern
    CvTemplate(
      id: 'fresh_lime',
      name: 'Fresh Graduate',
      description: 'Frisches Design für Berufseinsteiger und junge Talente',
      type: CvTemplateType.fresh,
      colorScheme: CvColorScheme.lime,
      previewImagePath: 'assets/cv_previews/fresh_lime.png',
      isPremium: false,
      isCustomizable: true,
      layoutConfig: {
        'headerStyle': 'fresh_youthful',
        'layout': 'fresh_modern',
        'sectionSpacing': 26,
        'fontSize': {'header': 30, 'section': 16, 'body': 12},
        'youthfulDesign': true,
        'modernElements': true,
        'freshSpacing': true,
        'graduateFocus': true,
        'energeticLayout': true,
      },
    ),
  ];

  /// Gibt alle verfügbaren Templates zurück
  List<CvTemplate> getAllTemplates() {
    _log.i(
      '🔥 CV-Template-Service: Lade alle Templates (${_templates.length} verfügbar)',
    );
    return List.unmodifiable(_templates);
  }

  /// Gibt kostenlose Templates zurück
  List<CvTemplate> getFreeTemplates() {
    final freeTemplates =
        _templates.where((template) => !template.isPremium).toList();
    _log.i(
      '🔥 CV-Template-Service: Lade kostenlose Templates (${freeTemplates.length} verfügbar)',
    );
    return freeTemplates;
  }

  /// Gibt Premium-Templates zurück
  List<CvTemplate> getPremiumTemplates() {
    final premiumTemplates =
        _templates.where((template) => template.isPremium).toList();
    _log.i(
      '🔥 CV-Template-Service: Lade Premium-Templates (${premiumTemplates.length} verfügbar)',
    );
    return premiumTemplates;
  }

  /// Gibt Templates nach Typ zurück
  List<CvTemplate> getTemplatesByType(CvTemplateType type) {
    final filteredTemplates =
        _templates.where((template) => template.type == type).toList();
    _log.i(
      '🔥 CV-Template-Service: Lade Templates für Typ ${type.name} (${filteredTemplates.length} gefunden)',
    );
    return filteredTemplates;
  }

  /// Gibt Template nach ID zurück
  CvTemplate? getTemplateById(String id) {
    try {
      final template = _templates.firstWhere((template) => template.id == id);
      _log.i('🔥 CV-Template-Service: Template mit ID $id gefunden');
      return template;
    } catch (e) {
      _log.w('⚠️ CV-Template-Service: Template mit ID $id nicht gefunden');
      return null;
    }
  }

  /// Gibt Standard-Template zurück (erstes kostenloses Template)
  CvTemplate getDefaultTemplate() {
    final defaultTemplate = getFreeTemplates().first;
    _log.i(
      '🔥 CV-Template-Service: Standard-Template ${defaultTemplate.name} zurückgegeben',
    );
    return defaultTemplate;
  }

  /// Prüft ob Template verfügbar ist (basierend auf Premium-Status)
  bool isTemplateAvailable(CvTemplate template, bool isPremiumUser) {
    final isAvailable = !template.isPremium || isPremiumUser;
    // Template-Verfügbarkeit geprüft (Debug-Log entfernt für Performance)
    return isAvailable;
  }

  /// Gibt verfügbare Templates für User zurück
  List<CvTemplate> getAvailableTemplatesForUser(bool isPremiumUser) {
    final availableTemplates =
        _templates
            .where((template) => isTemplateAvailable(template, isPremiumUser))
            .toList();

    _log.i(
      '🔥 CV-Template-Service: ${availableTemplates.length} Templates verfügbar für ${isPremiumUser ? 'Premium' : 'Free'}-User',
    );
    return availableTemplates;
  }

  /// Gibt Template-Kategorien zurück
  Map<CvTemplateType, List<CvTemplate>> getTemplatesByCategory() {
    final Map<CvTemplateType, List<CvTemplate>> categorizedTemplates = {};

    for (final type in CvTemplateType.values) {
      categorizedTemplates[type] = getTemplatesByType(type);
    }

    _log.i(
      '🔥 CV-Template-Service: Templates nach Kategorien gruppiert (${categorizedTemplates.length} Kategorien)',
    );
    return categorizedTemplates;
  }
}

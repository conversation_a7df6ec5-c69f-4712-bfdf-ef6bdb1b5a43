import 'dart:convert';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';

import 'package:ki_test/src/domain/models/extracted_cv_data.dart';

// Result-Klasse für Cover Letter Generation
class CoverLetterResult {
  final String coverLetter;
  final String? extractedEmail;
  final String? modelType;

  CoverLetterResult({
    required this.coverLetter,
    this.extractedEmail,
    this.modelType,
  });
}

class SupabaseService {
  final SupabaseClient _client;
  final _log = Logger('SupabaseService');

  SupabaseService(this._client);

  Future<String?> performOcrOnPdf(String storagePath) async {
    _log.info(
      'Rufe Supabase OCR Function "perform-ocr-on-pdf" für Storage-Pfad: $storagePath auf',
    );
    try {
      final response = await _client.functions.invoke(
        'perform-ocr-on-pdf',
        body: {'storagePath': storagePath},
      );

      _log.info(
        'Supabase OCR Function "perform-ocr-on-pdf" Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData.containsKey('extractedText') &&
            responseData['extractedText'] is String) {
          final text = responseData['extractedText'] as String;
          _log.info('OCR erfolgreich. Extrahierter Text Länge: ${text.length}');
          return text;
        } else {
          _log.warning(
            'OCR-Antwort enthält keinen gültigen "extractedText". Data: ${response.data}',
          );
          return null;
        }
      } else {
        _log.severe(
          'Fehler bei der Ausführung der OCR Function "perform-ocr-on-pdf". Status: ${response.status}, Data: ${response.data}',
        );
        return null;
      }
    } catch (e, stackTrace) {
      _log.severe(
        'Exception beim Aufruf der OCR Function "perform-ocr-on-pdf"',
        e,
        stackTrace,
      );
      return null;
    }
  }

  Future<ExtractedCvData?> processCvText(String cvText) async {
    _log.info(
      'Rufe Supabase Textverarbeitungs-Function "process-cv-text" auf...',
    );
    if (cvText.isEmpty) {
      _log.warning('Leerer CV-Text an processCvText übergeben.');
      return null;
    }

    try {
      final response = await _client.functions.invoke(
        'process-cv-text',
        body: {'cvText': cvText},
      );

      _log.info(
        'Supabase Function "process-cv-text" Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        try {
          final data = response.data as Map<String, dynamic>;
          _log.fine(
            'Empfangene Rohdaten von process-cv-text: ${jsonEncode(data)}',
          );
          final extractedData = ExtractedCvData.fromJson(data);
          _log.info('CV-Text erfolgreich verarbeitet und Daten extrahiert.');
          return extractedData;
        } catch (e, stack) {
          _log.severe(
            'Fehler beim Parsen der JSON-Antwort von process-cv-text',
            e,
            stack,
          );
          _log.severe('Rohdaten: ${response.data}');
          return null;
        }
      } else {
        _log.severe(
          'Fehler bei der Ausführung der Function "process-cv-text". Status: ${response.status}, Data: ${response.data}',
        );
        return null;
      }
    } catch (e, stackTrace) {
      _log.severe(
        'Exception beim Aufruf der Function "process-cv-text"',
        e,
        stackTrace,
      );
      return null;
    }
  }

  Future<CoverLetterResult> generateCoverLetter({
    required Map<String, dynamic> userProfile,
    required Map<String, dynamic> jobPosting,
    required String modelType,
  }) async {
    _log.info('Rufe Supabase Function "generate-cover-letter" auf...');
    _log.info('Modell: $modelType');
    _log.info('UserProfile Keys: ${userProfile.keys.toList()}');
    _log.info('JobPosting Keys: ${jobPosting.keys.toList()}');

    try {
      // Bestimme die richtige Edge Function basierend auf dem Modell
      final String functionName = modelType == 'mistral'
          ? 'generate-cover-letter-mistral'
          : 'generate-cover-letter';

      _log.info('Verwende Edge Function: $functionName');

      final response = await _client.functions.invoke(
        functionName,
        body: {
          'userId': _client.auth.currentUser?.id,
          'userProfile': userProfile,
          'jobPosting': jobPosting,
          'stylePreference': 'Professionell',
          'personalizedStylePrompt': null,
        },
      );

      _log.info(
        'Supabase Function "$functionName" Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        final data = Map<String, dynamic>.from(response.data);
        _log.info('Anschreiben erfolgreich generiert.');
        _log.info('Response Keys: ${data.keys.toList()}');

        return CoverLetterResult(
          coverLetter: data['coverLetter'] ?? '',
          extractedEmail: data['extractedEmail'],
          modelType: data['modelType'] ?? modelType,
        );
      } else {
        _log.severe(
          'Fehler bei der Ausführung der Function "$functionName". Status: ${response.status}, Data: ${response.data}',
        );

        throw Exception(
          response.data is Map
              ? (response.data['error'] ??
                  'Unbekannter Fehler bei der Anschreibengenerierung')
              : 'Fehler bei der Anschreibengenerierung',
        );
      }
    } on FunctionException catch (e) {
      _log.severe('[SupabaseService] Function error: ${e.toString()}');
      throw Exception('Function error: ${e.toString()}');
    } catch (e) {
      _log.severe('[SupabaseService] Error: $e');
      throw Exception('Service error: $e');
    }
  }

  /// Generiert Berufsbezeichnungen als Schlüsselwörter basierend auf dem Nutzerprofil
  ///
  /// [userProfile]: Das Profil des Nutzers, das analysiert werden soll
  /// Gibt bei Erfolg eine Liste von Berufsbezeichnungen zurück
  /// Bei Fehlern wird eine leere Liste zurückgegeben
  Future<List<String>> generateJobKeywords(
    Map<String, dynamic> profileData,
  ) async {
    _log.info('Rufe Supabase Function "generate-job-keywords" auf...');
    debugPrint(
      '🔍 [SupabaseService] Rufe generate-job-keywords auf mit Daten: ${jsonEncode(profileData).substring(0, 200)}...',
    );

    try {
      final response = await _client.functions.invoke(
        'generate-job-keywords',
        body: {'profileData': profileData},
      );

      _log.info(
        'Supabase Function "generate-job-keywords" Antwort Status: ${response.status}',
      );
      debugPrint(
        '🔍 [SupabaseService] generate-job-keywords Antwort Status: ${response.status}',
      );

      if (response.status == 200 && response.data != null) {
        debugPrint(
          '🔍 [SupabaseService] generate-job-keywords Antwort Daten: ${jsonEncode(response.data)}',
        );
        final data = response.data as Map<String, dynamic>;

        if (data.containsKey('jobKeywords') && data['jobKeywords'] is List) {
          final jobKeywords =
              (data['jobKeywords'] as List)
                  .map((item) => item.toString())
                  .toList();
          _log.info(
            'Berufsbezeichnungen erfolgreich generiert: ${jobKeywords.join(", ")}',
          );
          debugPrint(
            '🔍 [SupabaseService] Berufsbezeichnungen erfolgreich generiert: ${jobKeywords.join(", ")}',
          );
          return jobKeywords;
        } else {
          _log.warning(
            'Unerwartetes Antwortformat: Keine "jobKeywords" Liste gefunden',
          );
          debugPrint(
            '⚠️ [SupabaseService] Unerwartetes Antwortformat: Keine "jobKeywords" Liste gefunden. Daten: ${jsonEncode(response.data)}',
          );
          return [];
        }
      } else {
        _log.severe(
          'Fehler bei der Ausführung der Function "generate-job-keywords". Status: ${response.status}, Data: ${response.data}',
        );
        debugPrint(
          '❌ [SupabaseService] Fehler bei der Ausführung der Function "generate-job-keywords". Status: ${response.status}, Data: ${response.data}',
        );
        return [];
      }
    } on FunctionException catch (e) {
      _log.severe(
        'FunctionException beim Aufruf von "generate-job-keywords"',
        e,
      );
      debugPrint(
        '❌ [SupabaseService] FunctionException beim Aufruf von "generate-job-keywords": ${e.toString()}',
      );
      return [];
    } catch (e, stackTrace) {
      _log.severe(
        'Fehler beim Aufruf der Function "generate-job-keywords"',
        e,
        stackTrace,
      );
      debugPrint(
        '❌ [SupabaseService] Fehler beim Aufruf der Function "generate-job-keywords": ${e.toString()}',
      );
      return [];
    }
  }


}

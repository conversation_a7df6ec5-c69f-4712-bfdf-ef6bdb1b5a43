import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/models/user_profile.dart';
import '../../domain/models/job_preferences.dart';
import '../../domain/models/extracted_cv_data.dart'; // Import für ExtractedCvData
import 'dart:io'; // Für File
import 'dart:async';
import 'dart:convert'; // Für jsonDecode
import 'package:supabase_flutter/supabase_flutter.dart'; // Supabase importiert
import 'package:flutter/foundation.dart'; // Für mapEquals
// import 'package:flutter_riverpod/flutter_riverpod.dart'; // Bereits oben importiert
import 'package:intl/intl.dart'; // NEU: Import für DateFormat
import 'package:shared_preferences/shared_preferences.dart'; // NEU: Import für SharedPreferences
import '../services/logger.dart';
import '../../infrastructure/services/supabase_service.dart'
    as external_supabase; // Import für externe SupabaseService
import '../../utils/cv_storage_helper.dart'; // Import für CV Storage Helper
import '../../core/utils/device_manager.dart'; // Import für DeviceManager
import '../../core/utils/profile_backup_manager.dart'; // Import für ProfileBackupManager
import '../providers/anti_manipulation_provider.dart';

// Provider für das UserProfile
final userProfileProvider =
    StateNotifierProvider<UserProfileNotifier, AsyncValue<UserProfile>>((ref) {
      final supabaseInstance = Supabase.instance.client;
      // Erstelle den SupabaseService direkt
      final supabaseService = external_supabase.SupabaseService(
        supabaseInstance,
      );
      return UserProfileNotifier(
        ref: ref,
        supabase: supabaseInstance,
        supabaseService: supabaseService,
      );
    });

// StateNotifier Implementierung für das UserProfile
class UserProfileNotifier extends StateNotifier<AsyncValue<UserProfile>> {
  final SupabaseClient supabase;
  final external_supabase.SupabaseService supabaseService;
  final Ref ref;
  final _log = Logger('UserProfileNotifier');

  // Füge isProcessingCv als Klassenvariable hinzu
  final ValueNotifier<bool> isProcessingCv = ValueNotifier(false);

  // Supabase Realtime-Kanal für Profildaten
  RealtimeChannel? _profileChannel;

  UserProfileNotifier({
    required this.ref,
    SupabaseClient? supabase,
    required this.supabaseService,
  }) : supabase = supabase ?? Supabase.instance.client,
       super(const AsyncLoading()) {
    _init();

    // Auth-State-Change Listener hinzufügen
    this.supabase.auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final user = data.session?.user;

      _log.d(
        "[UserProfileNotifier] Auth state changed: $event, User: ${user?.id}",
      );

      if (event == AuthChangeEvent.signedIn && user != null) {
        // Bei Anmeldung oder Benutzerwechsel _init aufrufen
        _log.i(
          "[UserProfileNotifier] User signed in, reinitializing profile for: ${user.id}",
        );
        _init();
      } else if (event == AuthChangeEvent.signedOut) {
        // Bei Abmeldung State zurücksetzen
        _log.i("[UserProfileNotifier] User signed out, resetting state");
        state = const AsyncLoading();
        _profileChannel?.unsubscribe();
        _profileChannel = null;
      }
    });
  }

  @override
  void dispose() {
    _log.d("UserProfileNotifier dispose called");
    // Schließe den Realtime-Kanal, wenn der Provider nicht mehr benötigt wird
    _profileChannel?.unsubscribe();
    isProcessingCv.dispose();
    super.dispose();
  }

  // Methode zum Aktualisieren des Profils
  Future<void> updateProfile(UserProfile updatedProfile) async {
    final currentUserId = supabase.auth.currentUser?.id;
    if (currentUserId == null || updatedProfile.id != currentUserId) {
      final error = "Benutzer nicht angemeldet oder ID-Konflikt beim Update.";
      state = AsyncError(error, StackTrace.current);
      _log.w("[updateProfile] Update abgebrochen: $error");
      return;
    }

    final previousState = state;
    state = const AsyncLoading<UserProfile>().copyWithPrevious(previousState);
    _log.i(
      "[updateProfile] Speichere Profil in Supabase für ID: ${updatedProfile.id}",
    );

    try {
      await supabase.from('profiles').upsert({
        'id': updatedProfile.id,
        'data': updatedProfile.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      state = AsyncData(updatedProfile);
      _log.i(
        "[updateProfile] Profil erfolgreich aktualisiert für ID: ${updatedProfile.id}",
      );
    } catch (e, stack) {
      _log.e("[updateProfile] Fehler: $e", stackTrace: stack);
      state = AsyncError<UserProfile>(e, stack).copyWithPrevious(previousState);
      rethrow; // Fehler weiterwerfen, damit Aufrufer ihn behandeln kann
    }
  }

  // Methode zum Speichern von Profiländerungen im Hintergrund (ohne UI-Update)
  Future<void> saveProfileChangesInBackground(
    UserProfile finalProfileData,
  ) async {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    if (currentUserId == null || finalProfileData.id != currentUserId) {
      final errorMsg =
          "Benutzer nicht angemeldet oder ID-Konflikt beim Speichern.";
      _log.w(
        "[saveProfileChangesInBackground] Speichern abgebrochen: $errorMsg",
      );
      throw Exception(errorMsg);
    }

    try {
      _log.i(
        "[saveProfileChangesInBackground] Speichere Profiländerungen in Supabase für ID: ${finalProfileData.id}...",
      );
      final Map<String, dynamic> profileJson = finalProfileData.toJson();
      profileJson['id'] = finalProfileData.id; // Sicherstellen

      if (!profileJson.containsKey('email') ||
          profileJson['email'] == null ||
          profileJson['email'].toString().isEmpty) {
        profileJson['email'] = supabase.auth.currentUser?.email ?? '';
      }
      _log.d(
        "[saveProfileChangesInBackground] Zu speichernde Daten: ${profileJson.keys}",
      );

      // Hole die aktuelle Profil-Version und Geräte-IDs aus Supabase
      final profileResponse =
          await supabase
              .from('profiles')
              .select('profile_version, device_ids')
              .eq('id', finalProfileData.id!)
              .maybeSingle();

      int profileVersion = 1;
      List<String> deviceIds = [];

      if (profileResponse != null) {
        // Verarbeite die Profil-Version
        if (profileResponse['profile_version'] != null) {
          profileVersion = profileResponse['profile_version'] as int;
        }

        // Verarbeite die Geräte-IDs
        if (profileResponse['device_ids'] != null) {
          if (profileResponse['device_ids'] is List) {
            deviceIds = List<String>.from(profileResponse['device_ids']);
          } else if (profileResponse['device_ids'] is String) {
            try {
              final List<dynamic> parsedIds = jsonDecode(
                profileResponse['device_ids'],
              );
              deviceIds = parsedIds.map((id) => id.toString()).toList();
            } catch (e) {
              _log.w(
                "[saveProfileChangesInBackground] Fehler beim Parsen der Geräte-IDs: $e",
              );
            }
          }
        }
      }

      // Registriere das Gerät für den Benutzer und hole die aktuelle Geräte-ID
      final deviceManager = DeviceManager();
      final registrationResult = await deviceManager.registerDeviceForUser(
        finalProfileData.id!,
      );
      try {
        // UI-State für Anti‑Manipulation aktualisieren
        final anti = ref.read(antiManipulationProvider.notifier);
        anti.setFromServerResult(registrationResult);
      } catch (_) {}
      final deviceId = await deviceManager.getDeviceId();

      // Füge die aktuelle Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
      if (!deviceIds.contains(deviceId)) {
        deviceIds.add(deviceId);
      }

      // Speichere das Profil in Supabase
      await supabase.from('profiles').upsert({
        'id': finalProfileData.id,
        'data': profileJson,
        'profile_version':
            profileVersion + 1, // Inkrementiere die Profil-Version
        'device_ids': deviceIds, // Aktualisiere die Geräte-IDs
        'updated_at': DateTime.now().toIso8601String(),
      });

      // PERFORMANCE FIX: Erstelle sofortiges Backup für Profil-Updates (kritisch)
      final backupManager = ProfileBackupManager();
      await backupManager.backupProfileImmediate(finalProfileData);

      // WICHTIG: Aktualisiere nur die lokalen Daten im State, ohne Loading-State zu setzen
      if (state is AsyncData<UserProfile>) {
        state = AsyncData(finalProfileData);
      }

      _log.i(
        "[saveProfileChangesInBackground] Profiländerungen erfolgreich gespeichert für ID: ${finalProfileData.id}.",
      );
    } catch (e, stack) {
      _log.e(
        "[saveProfileChangesInBackground] Fehler beim Speichern der Profiländerungen für ID ${finalProfileData.id}: $e",
        stackTrace: stack,
      );
      rethrow;
    }
  }

  // Methode zum Speichern von Profiländerungen (mit UI-Loading-State)
  Future<void> saveProfileChanges(UserProfile finalProfileData) async {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    if (currentUserId == null || finalProfileData.id != currentUserId) {
      final errorMsg =
          "Benutzer nicht angemeldet oder ID-Konflikt beim Speichern.";
      final prevState = state;
      state = AsyncError<UserProfile>(
        errorMsg,
        StackTrace.current,
      ).copyWithPrevious(prevState);
      _log.w("[saveProfileChanges] Speichern abgebrochen: $errorMsg");
      throw Exception(errorMsg);
    }

    final prevState = state;
    state = const AsyncLoading<UserProfile>().copyWithPrevious(prevState);

    try {
      _log.i(
        "[saveProfileChanges] Speichere Profiländerungen in Supabase für ID: ${finalProfileData.id}...",
      );
      final Map<String, dynamic> profileJson = finalProfileData.toJson();
      profileJson['id'] = finalProfileData.id; // Sicherstellen

      if (!profileJson.containsKey('email') ||
          profileJson['email'] == null ||
          profileJson['email'].toString().isEmpty) {
        profileJson['email'] = supabase.auth.currentUser?.email ?? '';
      }
      _log.d("[saveProfileChanges] Zu speichernde Daten: ${profileJson.keys}");

      // Hole die aktuelle Profil-Version und Geräte-IDs aus Supabase
      final profileResponse =
          await supabase
              .from('profiles')
              .select('profile_version, device_ids')
              .eq('id', finalProfileData.id!)
              .maybeSingle();

      int profileVersion = 1;
      List<String> deviceIds = [];

      if (profileResponse != null) {
        // Verarbeite die Profil-Version
        if (profileResponse['profile_version'] != null) {
          profileVersion = profileResponse['profile_version'] as int;
        }

        // Verarbeite die Geräte-IDs
        if (profileResponse['device_ids'] != null) {
          if (profileResponse['device_ids'] is List) {
            deviceIds = List<String>.from(profileResponse['device_ids']);
          } else if (profileResponse['device_ids'] is String) {
            try {
              final List<dynamic> parsedIds = jsonDecode(
                profileResponse['device_ids'],
              );
              deviceIds = parsedIds.map((id) => id.toString()).toList();
            } catch (e) {
              _log.w(
                "[saveProfileChanges] Fehler beim Parsen der Geräte-IDs: $e",
              );
            }
          }
        }
      }

      // Registriere das Gerät für den Benutzer und hole die aktuelle Geräte-ID
      final deviceManager = DeviceManager();
      final registrationResult = await deviceManager.registerDeviceForUser(
        finalProfileData.id!,
      );
      try {
        final anti = ref.read(antiManipulationProvider.notifier);
        anti.setFromServerResult(registrationResult);
      } catch (_) {}
      final deviceId = await deviceManager.getDeviceId();

      // Füge die aktuelle Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
      if (!deviceIds.contains(deviceId)) {
        deviceIds.add(deviceId);
      }

      // Speichere das Profil in Supabase
      await supabase.from('profiles').upsert({
        'id': finalProfileData.id,
        'data': profileJson,
        'profile_version':
            profileVersion + 1, // Inkrementiere die Profil-Version
        'device_ids': deviceIds, // Aktualisiere die Geräte-IDs
        'updated_at': DateTime.now().toIso8601String(),
      });

      // PERFORMANCE FIX: Erstelle sofortiges Backup für Profil-Speicherung (kritisch)
      final backupManager = ProfileBackupManager();
      await backupManager.backupProfileImmediate(finalProfileData);

      state = AsyncData(finalProfileData);
      _log.i(
        "[saveProfileChanges] Profiländerungen erfolgreich gespeichert für ID: ${finalProfileData.id}.",
      );
    } catch (e, stack) {
      _log.e(
        "[saveProfileChanges] Fehler beim Speichern der Profiländerungen für ID ${finalProfileData.id}: $e",
        stackTrace: stack,
      );
      state = AsyncError<UserProfile>(e, stack).copyWithPrevious(prevState);
      rethrow;
    }
  }

  /// Aktualisiert die CV-Informationen im State.
  /// Wird aufgerufen, nachdem der CV erfolgreich in den Storage hochgeladen wurde.
  Future<void> updateCvInfo(String downloadUrl, String localPath) async {
    final currentState = state;
    if (currentState is AsyncData<UserProfile>) {
      final currentProfile = currentState.value;
      // Prüfe, ob sich die Daten geändert haben, um unnötige State-Updates zu vermeiden
      if (currentProfile.cvDownloadUrl != downloadUrl ||
          currentProfile.cvFilePath != localPath) {
        _log.i(
          "[updateCvInfo] Updating CV info in state. URL: $downloadUrl, Local path: $localPath",
        );

        final updatedProfile = currentProfile.copyWith(
          cvDownloadUrl: downloadUrl,
          cvFilePath: localPath,
          // cvAnalysisComplete kann hier auch auf false gesetzt werden, wenn ein neuer CV hochgeladen wird
          // und eine neue Analyse impliziert.
          // cvAnalysisComplete: false,
        );
        // Den State direkt zu aktualisieren ist eine Option,
        // aber es wäre konsistenter, saveProfileChanges oder updateProfile zu verwenden.
        // Für eine reine Info-Aktualisierung ohne DB-Speicherung hier ist es ok.
        // Wenn aber auch die DB aktualisiert werden soll:
        try {
          await updateProfile(updatedProfile); // Oder saveProfileChanges
          _log.i(
            "[updateCvInfo] Profil mit CV-Infos via updateProfile gespeichert.",
          );
        } catch (e) {
          _log.e(
            "[updateCvInfo] Fehler beim Speichern des Profils mit CV-Infos: $e",
          );
        }
        // state = AsyncData(updatedProfile); // Wird von updateProfile/saveProfileChanges erledigt
      } else {
        _log.d(
          "[updateCvInfo] CV info is already up-to-date. No state change needed.",
        );
      }
    } else {
      _log.w(
        "[updateCvInfo] Cannot update CV info. Profile state is not AsyncData: ${currentState.runtimeType}",
      );
    }
  }

  /// Aktualisiert den Premium-Status des Benutzers
  Future<void> updatePremiumStatus({
    required bool isPremium,
    DateTime? premiumExpiryDate,
    String? transactionId,
    DateTime? purchaseDate,
    String? planType,
  }) async {
    final currentState = state;
    if (currentState is AsyncLoading<UserProfile>) {
      _log.w(
        '[updatePremiumStatus] Kann Premium-Status nicht aktualisieren, während Profil geladen wird.',
      );
      return;
    }

    final currentUserId = supabase.auth.currentUser?.id;
    if (currentUserId == null) {
      final error = Exception('Nicht eingeloggt');
      _log.e(
        '[updatePremiumStatus] Kann Premium-Status nicht aktualisieren: Nicht eingeloggt.',
      );
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    UserProfile? currentProfile =
        currentState
            .valueOrNull; // Nimmt Wert aus AsyncData oder AsyncError.value

    if (currentProfile == null) {
      final error = Exception('Profil nicht verfügbar für Premium-Update');
      _log.e(
        '[updatePremiumStatus] Kann Premium-Status nicht aktualisieren: Profil nicht verfügbar.',
      );
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    if (currentProfile.id != currentUserId) {
      final error = Exception('ID-Konflikt beim Premium-Update.');
      _log.e('[updatePremiumStatus] ID-Konflikt beim Premium-Update.');
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    // Überprüfen, ob sich der Status tatsächlich ändert
    if (currentProfile.isPremium == isPremium &&
        currentProfile.premiumExpiryDate == premiumExpiryDate &&
        currentProfile.premiumPlanType == planType) {
      _log.i(
        '[updatePremiumStatus] Premium status is already up-to-date. No changes needed.',
      );
      return;
    }
    _log.i(
      "[updatePremiumStatus] Updating premium status to: $isPremium, expiry: $premiumExpiryDate, plan: $planType",
    );

    try {
      final updatedProfile = currentProfile.copyWith(
        isPremium: isPremium,
        premiumExpiryDate: premiumExpiryDate,
        premiumPlanType:
            planType ??
            (isPremium
                ? (currentProfile.premiumPlanType ?? 'premium')
                : 'free'),
        // Ggf. transactionId und purchaseDate hier im UserProfile speichern, wenn das Modell es vorsieht
      );
      await saveProfileChanges(updatedProfile);
      _log.i("[updatePremiumStatus] Premium-Status erfolgreich aktualisiert.");
    } catch (e, stack) {
      _log.e(
        "[updatePremiumStatus] Fehler beim Aktualisieren des Premium-Status: $e",
        stackTrace: stack,
      );
      // Fehler wird von saveProfileChanges behandelt und weitergeworfen
      rethrow;
    }
  }

  /// Aktualisiert das Ablaufdatum des Premium-Status (überarbeitete Version)
  Future<void> updatePremiumExpiryDate(DateTime? expiryDate) async {
    final currentState = state;
    if (currentState.isLoading) {
      _log.w(
        '[updatePremiumExpiryDate] Kann Premium-Ablaufdatum nicht aktualisieren, während Profil geladen wird',
      );
      return;
    }

    final currentUserId = supabase.auth.currentUser?.id;
    if (currentUserId == null) {
      final error = Exception("Nicht eingeloggt für Premium-Update");
      _log.e(
        '[updatePremiumExpiryDate] Kann Premium-Ablaufdatum nicht aktualisieren: $error',
      );
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    UserProfile? currentProfile = currentState.valueOrNull;

    if (currentProfile == null) {
      final error = Exception('Profil nicht verfügbar für Premium-Update');
      _log.e(
        '[updatePremiumExpiryDate] Kann Premium-Ablaufdatum nicht aktualisieren: $error',
      );
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    if (currentProfile.id != currentUserId) {
      final error = Exception('ID-Konflikt beim Premium-Ablaufdatum-Update.');
      _log.e('[updatePremiumExpiryDate] $error');
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(currentState);
      return;
    }

    bool changed = true;
    if (expiryDate == null && currentProfile.premiumExpiryDate == null) {
      changed = false;
    } else if (expiryDate != null && currentProfile.premiumExpiryDate != null) {
      if (expiryDate.isAtSameMomentAs(currentProfile.premiumExpiryDate!)) {
        changed = false;
      }
    }

    if (!changed) {
      _log.i(
        '[updatePremiumExpiryDate] Premium-Ablaufdatum ist bereits auf ${expiryDate?.toIso8601String()} gesetzt oder beide null.',
      );
      return;
    }

    _log.i(
      "[updatePremiumExpiryDate] Aktualisiere Premium-Ablaufdatum auf: ${expiryDate?.toIso8601String()}",
    );

    try {
      // Ladezustand wird in saveProfileChanges gesetzt
      final updatedProfile = currentProfile.copyWith(
        premiumExpiryDate: expiryDate,
      );
      await saveProfileChanges(updatedProfile);
      _log.i(
        '[updatePremiumExpiryDate] Premium-Ablaufdatum erfolgreich aktualisiert via saveProfileChanges.',
      );
    } catch (e, stackTrace) {
      _log.e(
        '[updatePremiumExpiryDate] Fehler beim Aktualisieren des Premium-Ablaufdatums: $e',
        stackTrace: stackTrace,
      );
      // Fehler wird von saveProfileChanges behandelt und weitergeworfen
      rethrow;
    }
  }

  /// Lädt die ausgewählte CV-Datei hoch, ruft die Analyse-Funktionen auf und gibt die extrahierten Daten zurück. (zweite, "echte" Version, überarbeitet)
  /// Gibt ein ExtractedCvData-Objekt zurück, wenn die Analyse erfolgreich war, sonst null.
  Future<ExtractedCvData?> uploadAndAnalyzeCv(
    String localPath,
    String fileName,
  ) async {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    if (currentUserId == null) {
      final error = StateError("Nutzer nicht angemeldet für CV-Upload.");
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(state);
      _log.w(
        "[uploadAndAnalyzeCv] CV Upload abgebrochen: User nicht angemeldet.",
      );
      throw error;
    }

    final previousState = state;
    state = const AsyncLoading<UserProfile>().copyWithPrevious(previousState);
    isProcessingCv.value = true;
    _log.i(
      "[uploadAndAnalyzeCv] Starting CV upload and analysis for user: $currentUserId",
    );

    String? storagePath;

    try {
      final folderName = '${currentUserId}_user';
      final cleanedFileNameForStorage = "lebenslauf.pdf";
      storagePath = '$folderName/$cleanedFileNameForStorage';
      _log.d(
        "[uploadAndAnalyzeCv] Verwende konsistenten Storage-Pfad: $storagePath",
      );
      _log.d(
        "[uploadAndAnalyzeCv] Uploading CV to Supabase Storage: $storagePath",
      );

      try {
        final List<FileObject> existingFiles = await Supabase
            .instance
            .client
            .storage
            .from('cv-backups')
            .list(path: folderName);
        for (final file in existingFiles) {
          if (file.name.toLowerCase().endsWith('.pdf')) {
            _log.d(
              "[uploadAndAnalyzeCv] Deleting existing CV file: ${file.name}",
            );
            await Supabase.instance.client.storage.from('cv-backups').remove([
              '$folderName/${file.name}',
            ]);
          }
        }
      } catch (e) {
        _log.w(
          "[uploadAndAnalyzeCv] No existing files found or error listing files: $e (ignoring)",
        );
      }

      await Supabase.instance.client.storage
          .from('cv-backups')
          .upload(
            storagePath,
            File(localPath),
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true,
              contentType: 'application/pdf',
            ),
          );
      _log.i(
        "[uploadAndAnalyzeCv] CV uploaded successfully to Supabase path: $storagePath",
      );

      _log.d(
        "[uploadAndAnalyzeCv] Invoking Supabase function 'perform-ocr-on-pdf' with path: $storagePath",
      );
      final ocrResponse = await Supabase.instance.client.functions.invoke(
        'perform-ocr-on-pdf',
        body: {'storagePath': storagePath},
      );

      if (ocrResponse.status != 200) {
        _log.e(
          "[uploadAndAnalyzeCv] Supabase OCR function error: Status ${ocrResponse.status}, Data: ${ocrResponse.data}",
        );
        throw Exception(
          'Fehler bei der CV-Textextraktion (Status: ${ocrResponse.status})',
        );
      }

      final ocrData = ocrResponse.data as Map<String, dynamic>?;
      final extractedText = ocrData?['extractedText'] as String?;
      if (extractedText == null || extractedText.isEmpty) {
        _log.w("[uploadAndAnalyzeCv] OCR function returned no text.");
        throw Exception('Aus der PDF konnte kein Text extrahiert werden.');
      }
      _log.i(
        "[uploadAndAnalyzeCv] OCR function successful. Extracted text length: ${extractedText.length}",
      );

      _log.d(
        "[uploadAndAnalyzeCv] Invoking Supabase function 'process-cv-text'...",
      );
      final analysisResponse = await Supabase.instance.client.functions.invoke(
        'process-cv-text',
        body: {'cvText': extractedText},
      );

      if (analysisResponse.status != 200) {
        _log.e(
          "[uploadAndAnalyzeCv] Supabase analysis function error: Status ${analysisResponse.status}, Data: ${analysisResponse.data}",
        );
        throw Exception(
          'Fehler bei der CV-Analyse (Status: ${analysisResponse.status})',
        );
      }
      _log.i(
        "[uploadAndAnalyzeCv] Supabase analysis function executed successfully. Data: ${analysisResponse.data}",
      );

      if (analysisResponse.data is Map<String, dynamic>) {
        final analysisData = analysisResponse.data as Map<String, dynamic>;
        UserProfile? currentProfile =
            state.valueOrNull; // Holen aus dem aktuellen State
        if (currentProfile == null) {
          // Versuche, das Profil aus dem previousState zu laden, falls es ein Ladefehler war
          if (previousState is AsyncData<UserProfile>) {
            currentProfile = previousState.value;
          }
          if (previousState is AsyncError<UserProfile> &&
              previousState.hasValue) {
            currentProfile = previousState.value;
          }

          if (currentProfile == null) {
            throw StateError(
              "Aktuelles Profil konnte nicht geladen werden, um Analysedaten zu integrieren.",
            );
          }
        }

        final localCvPath = await CvStorageHelper.saveCvToLocalStorage(
          localPath,
          currentUserId,
        );
        final newCvDownloadUrl = Supabase.instance.client.storage
            .from('cv-backups')
            .getPublicUrl(storagePath);
        _log.d("[uploadAndAnalyzeCv] Neue CV-URL: $newCvDownloadUrl");

        final profileWithCvInfo = currentProfile.copyWith(
          cvDownloadUrl: newCvDownloadUrl,
          cvFilePath: localCvPath,
          cvAnalysisComplete: false, // Set to false, analysis data is separate
          cvAnalysisTimestamp: null, // Reset timestamp as new CV is uploaded
        );

        // Speichere das Profil mit den neuen CV-Pfaden und URLs
        // saveProfileChanges kümmert sich um den State (AsyncLoading -> AsyncData/Error)
        await saveProfileChanges(profileWithCvInfo);
        _log.i(
          "[uploadAndAnalyzeCv] Profil mit CV-Pfaden aktualisiert via saveProfileChanges.",
        );
        _log.i("[uploadAndAnalyzeCv] Local CV path saved: $localCvPath");

        try {
          final extractedData = ExtractedCvData.fromJson(analysisData);
          _log.i(
            "[uploadAndAnalyzeCv] CV analysis complete. Extracted data ready to return.",
          );
          return extractedData;
        } catch (e, stack) {
          _log.e(
            "[uploadAndAnalyzeCv] Error creating ExtractedCvData from analysis response: $e",
            stackTrace: stack,
          );
          throw Exception('Fehler beim Verarbeiten der Analysedaten: $e');
        }
      } else {
        _log.e(
          "[uploadAndAnalyzeCv] Invalid data format received from Supabase analysis function: ${analysisResponse.data?.runtimeType}",
        );
        throw Exception(
          'Unerwartetes Datenformat von der CV-Analyse erhalten.',
        );
      }
    } catch (e, stack) {
      _log.e(
        "[uploadAndAnalyzeCv] Fehler beim Upload/Analyse des CV: $e",
        stackTrace: stack,
      );
      state = AsyncError<UserProfile>(e, stack).copyWithPrevious(previousState);
      return null;
    } finally {
      isProcessingCv.value = false;
      _log.d(
        "[uploadAndAnalyzeCv] CV-Verarbeitung abgeschlossen (finally Block).",
      );
    }
  }

  // Entfernen des Realtime-Listeners für Profildaten
  void _removeRealtimeListener(String userId) {
    _log.d(
      "[_removeRealtimeListener] Entferne Realtime-Listener für Benutzer $userId",
    );
    try {
      if (_profileChannel != null) {
        _profileChannel!.unsubscribe();
        _log.i(
          "[_removeRealtimeListener] Realtime-Kanal für Benutzer $userId erfolgreich entfernt",
        );
      }
    } catch (e) {
      _log.e(
        "[_removeRealtimeListener] Fehler beim Entfernen des Realtime-Kanals: $e",
      );
    }
  }

  // Einrichten des Realtime-Listeners für Profildaten
  void _setupRealtimeListener(String userId) {
    _profileChannel?.unsubscribe();
    _log.d(
      "[_setupRealtimeListener] Richte Realtime-Listener für Profildaten ein...",
    );
    _profileChannel = supabase
        .channel('profile-updates-$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'profiles',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'id',
            value: userId,
          ),
          callback: (payload) {
            _log.d(
              "[_setupRealtimeListener] Realtime-Update für Profil empfangen: ${payload.newRecord.keys}",
            );
            // Starte asynchrone Verarbeitung ohne auf das Ergebnis zu warten
            _handleProfileUpdate(payload.newRecord).catchError((error) {
              _log.e(
                "[_setupRealtimeListener] Fehler bei der asynchronen Verarbeitung des Realtime-Updates: $error",
              );
            });
          },
        )
        .subscribe((status, error) {
          if (status == RealtimeSubscribeStatus.subscribed) {
            _log.i(
              "[_setupRealtimeListener] Realtime-Kanal für Profildaten erfolgreich abonniert",
            );
          } else if (error != null) {
            _log.e(
              "[_setupRealtimeListener] Fehler beim Abonnieren des Realtime-Kanals: $error",
            );
          }
        });
  }

  // Hilfsmethode zum Konvertieren eines Supabase-Dokuments in ein UserProfile-Objekt
  UserProfile _convertDocToUserProfile(
    Map<String, dynamic> data,
    String userId,
  ) {
    _log.d("[_convertDocToUserProfile] Konvertiere Profildaten: ${data.keys}");
    final Map<String, dynamic> profileData = {}..addAll(data);

    // Stelle sicher, dass die ID gesetzt ist, aber überschreibe keine vorhandene ID
    if (!profileData.containsKey('id') ||
        profileData['id'] == null ||
        profileData['id'].toString().isEmpty) {
      profileData['id'] = userId;
    }

    // Stelle sicher, dass die E-Mail gesetzt ist, aber überschreibe keine vorhandene E-Mail
    if (!profileData.containsKey('email') ||
        profileData['email'] == null ||
        profileData['email'].toString().isEmpty) {
      profileData['email'] = supabase.auth.currentUser?.email ?? '';
    }

    // Stelle sicher, dass der Name gesetzt ist, aber überschreibe keinen vorhandenen Namen
    if (!profileData.containsKey('name') ||
        profileData['name'] == null ||
        profileData['name'].toString().isEmpty) {
      profileData['name'] = data['display_name'] ?? 'Benutzer';
    }

    // additionalCvData - Behalte vorhandene Daten
    if (data.containsKey('additionalCvData') &&
        data['additionalCvData'] is Map &&
        (data['additionalCvData'] as Map).isNotEmpty) {
      try {
        profileData['additionalCvData'] = Map<String, dynamic>.from(
          data['additionalCvData'],
        );
        _log.d(
          "[_convertDocToUserProfile] additionalCvData gefunden und konvertiert",
        );
      } catch (e) {
        _log.w(
          "[_convertDocToUserProfile] Could not convert additionalCvData to Map<String, dynamic>: $e",
        );
        // Behalte vorhandene Daten, setze nur auf null, wenn keine vorhanden sind
        if (!profileData.containsKey('additionalCvData') ||
            profileData['additionalCvData'] == null) {
          profileData['additionalCvData'] = null;
        }
      }
    } else if (!profileData.containsKey('additionalCvData') ||
        profileData['additionalCvData'] == null) {
      // Setze nur auf null, wenn keine vorhandenen Daten vorhanden sind
      profileData['additionalCvData'] = null;
    }

    // jobPreferencesObj - Behalte vorhandene Daten
    if (data.containsKey('jobPreferencesObj') &&
        data['jobPreferencesObj'] != null) {
      if (data['jobPreferencesObj'] is Map &&
          (data['jobPreferencesObj'] as Map).isNotEmpty) {
        try {
          profileData['jobPreferencesObj'] = Map<String, dynamic>.from(
            data['jobPreferencesObj'] as Map,
          );
          _log.d(
            "[_convertDocToUserProfile] jobPreferencesObj gefunden und konvertiert",
          );
        } catch (e) {
          _log.w(
            "[_convertDocToUserProfile] Fehler beim Konvertieren von jobPreferencesObj: $e. Behalte vorhandene Daten.",
          );
          // Behalte vorhandene Daten, setze nur auf leeres Objekt, wenn keine vorhanden sind
          if (!profileData.containsKey('jobPreferencesObj') ||
              profileData['jobPreferencesObj'] == null) {
            profileData['jobPreferencesObj'] = JobPreferences.empty().toJson();
          }
        }
      } else if (data['jobPreferencesObj'] is String &&
          (data['jobPreferencesObj'] as String).isNotEmpty) {
        try {
          profileData['jobPreferencesObj'] = jsonDecode(
            data['jobPreferencesObj'] as String,
          );
          _log.d(
            "[_convertDocToUserProfile] jobPreferencesObj (String) gefunden und konvertiert",
          );
        } catch (e) {
          _log.w(
            "[_convertDocToUserProfile] Fehler beim Parsen von jobPreferencesObj: $e. Behalte vorhandene Daten.",
          );
          // Behalte vorhandene Daten, setze nur auf leeres Objekt, wenn keine vorhanden sind
          if (!profileData.containsKey('jobPreferencesObj') ||
              profileData['jobPreferencesObj'] == null) {
            profileData['jobPreferencesObj'] = JobPreferences.empty().toJson();
          }
        }
      } else {
        _log.w(
          "[_convertDocToUserProfile] Unerwarteter Typ für jobPreferencesObj: ${data['jobPreferencesObj'].runtimeType}. Behalte vorhandene Daten.",
        );
        // Behalte vorhandene Daten, setze nur auf leeres Objekt, wenn keine vorhanden sind
        if (!profileData.containsKey('jobPreferencesObj') ||
            profileData['jobPreferencesObj'] == null) {
          profileData['jobPreferencesObj'] = JobPreferences.empty().toJson();
        }
      }
    } else if (!profileData.containsKey('jobPreferencesObj') ||
        profileData['jobPreferencesObj'] == null) {
      // Setze nur auf leeres Objekt, wenn keine vorhandenen Daten vorhanden sind
      profileData['jobPreferencesObj'] = JobPreferences.empty().toJson();
    }

    // workExperience - Behalte vorhandene Daten
    if (data.containsKey('workExperience') && data['workExperience'] != null) {
      List<Map<String, dynamic>> parsedWorkExperience = _parseListMap(
        data['workExperience'],
        'workExperience',
      );

      // Nur setzen, wenn die Liste nicht leer ist oder keine vorhandenen Daten vorhanden sind
      if (parsedWorkExperience.isNotEmpty ||
          !profileData.containsKey('workExperience') ||
          profileData['workExperience'] == null) {
        profileData['workExperience'] = parsedWorkExperience;
        _log.d(
          "[_convertDocToUserProfile] workExperience gefunden und konvertiert: ${parsedWorkExperience.length} Einträge",
        );
      }
    } else if (!profileData.containsKey('workExperience') ||
        profileData['workExperience'] == null) {
      // Setze nur auf leere Liste, wenn keine vorhandenen Daten vorhanden sind
      profileData['workExperience'] = [];
    }

    // education - Behalte vorhandene Daten
    if (data.containsKey('education') && data['education'] != null) {
      List<Map<String, dynamic>> parsedEducation = _parseListMap(
        data['education'],
        'education',
      );

      // Nur setzen, wenn die Liste nicht leer ist oder keine vorhandenen Daten vorhanden sind
      if (parsedEducation.isNotEmpty ||
          !profileData.containsKey('education') ||
          profileData['education'] == null) {
        profileData['education'] = parsedEducation;
        _log.d(
          "[_convertDocToUserProfile] education gefunden und konvertiert: ${parsedEducation.length} Einträge",
        );
      }
    } else if (!profileData.containsKey('education') ||
        profileData['education'] == null) {
      // Setze nur auf leere Liste, wenn keine vorhandenen Daten vorhanden sind
      profileData['education'] = [];
    }

    // skills - Behalte vorhandene Daten
    if (data.containsKey('skills') && data['skills'] != null) {
      List<String> parsedSkills = _parseListString(data['skills'], 'skills');

      // Nur setzen, wenn die Liste nicht leer ist oder keine vorhandenen Daten vorhanden sind
      if (parsedSkills.isNotEmpty ||
          !profileData.containsKey('skills') ||
          profileData['skills'] == null) {
        profileData['skills'] = parsedSkills;
        _log.d(
          "[_convertDocToUserProfile] skills gefunden und konvertiert: ${parsedSkills.length} Einträge",
        );
      }
    } else if (!profileData.containsKey('skills') ||
        profileData['skills'] == null) {
      // Setze nur auf leere Liste, wenn keine vorhandenen Daten vorhanden sind
      profileData['skills'] = [];
    }

    // jobKeywords - Behalte vorhandene Daten
    if (data.containsKey('jobKeywords') && data['jobKeywords'] != null) {
      List<String> parsedJobKeywords = _parseListString(
        data['jobKeywords'],
        'jobKeywords',
      );

      // Nur setzen, wenn die Liste nicht leer ist oder keine vorhandenen Daten vorhanden sind
      if (parsedJobKeywords.isNotEmpty ||
          !profileData.containsKey('jobKeywords') ||
          profileData['jobKeywords'] == null) {
        profileData['jobKeywords'] = parsedJobKeywords;
        _log.d(
          "[_convertDocToUserProfile] jobKeywords gefunden und konvertiert: ${parsedJobKeywords.length} Einträge",
        );
      }
    } else if (!profileData.containsKey('jobKeywords') ||
        profileData['jobKeywords'] == null) {
      // Setze nur auf leere Liste, wenn keine vorhandenen Daten vorhanden sind
      profileData['jobKeywords'] = [];
    }

    // Setze Standardwerte nur, wenn die Felder nicht vorhanden sind
    if (!profileData.containsKey('preferredWritingStyle') ||
        profileData['preferredWritingStyle'] == null) {
      profileData['preferredWritingStyle'] = 'Professionell';
    }

    if (!profileData.containsKey('includeExperienceInApplication') ||
        profileData['includeExperienceInApplication'] == null) {
      profileData['includeExperienceInApplication'] = true;
    }

    if (!profileData.containsKey('cvAnalysisComplete') ||
        profileData['cvAnalysisComplete'] == null) {
      profileData['cvAnalysisComplete'] = false;
    }

    if (!profileData.containsKey('cvAutoFillEnabled') ||
        profileData['cvAutoFillEnabled'] == null) {
      profileData['cvAutoFillEnabled'] = true;
    }

    if (!profileData.containsKey('isPremium') ||
        profileData['isPremium'] == null) {
      profileData['isPremium'] = false;
    }

    if (!profileData.containsKey('premiumPlanType') ||
        profileData['premiumPlanType'] == null) {
      profileData['premiumPlanType'] = 'free';
    }

    // Stelle sicher, dass premiumExpiryDate gültig ist, aber überschreibe kein vorhandenes Datum
    if (profileData.containsKey('premiumExpiryDate') &&
        profileData['premiumExpiryDate'] != null) {
      if (profileData['premiumExpiryDate'] is String) {
        try {
          DateTime.parse(profileData['premiumExpiryDate'] as String);
          // Datum ist gültig, nichts zu tun
        } catch (e) {
          _log.w(
            "[_convertDocToUserProfile] Ungültiges premiumExpiryDate: ${profileData['premiumExpiryDate']}. Setze Fallback.",
          );
          profileData['premiumExpiryDate'] =
              DateTime.now().add(const Duration(days: 365)).toIso8601String();
        }
      } else {
        // Nicht-String Typen ebenfalls als Fallback behandeln
        _log.w(
          "[_convertDocToUserProfile] Ungültiger Typ für premiumExpiryDate: ${profileData['premiumExpiryDate'].runtimeType}. Setze Fallback.",
        );
        profileData['premiumExpiryDate'] =
            DateTime.now().add(const Duration(days: 365)).toIso8601String();
      }
    } else {
      // Wenn nicht vorhanden oder null, Fallback setzen
      profileData['premiumExpiryDate'] =
          DateTime.now().add(const Duration(days: 365)).toIso8601String();
    }

    _log.d(
      "[_convertDocToUserProfile] Konvertierte Profildaten für UserProfile.fromJson: ${profileData.keys}",
    );
    try {
      return UserProfile.fromJson(profileData);
    } catch (e, stack) {
      _log.e(
        "[_convertDocToUserProfile] Fehler beim UserProfile.fromJson: $e. Erstelle Fallback-Profil.",
        stackTrace: stack,
      );
      return UserProfile(
        id: userId,
        name: profileData['name'] ?? 'Benutzer',
        email: profileData['email'] ?? '',
        jobPreferencesObj: JobPreferences.fromJson(
          profileData['jobPreferencesObj'] ?? JobPreferences.empty().toJson(),
        ),
        education:
            (profileData['education'] as List?)
                ?.map((e) => Education.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        workExperience:
            (profileData['workExperience'] as List?)
                ?.map((e) => WorkExperience.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        skills: List<String>.from(profileData['skills'] ?? []),
        preferredWritingStyle:
            profileData['preferredWritingStyle']?.toString() ?? 'Professionell',
        includeExperienceInApplication:
            profileData['includeExperienceInApplication'] as bool? ?? true,
        isPremium: profileData['isPremium'] as bool? ?? false,
        premiumPlanType: profileData['premiumPlanType']?.toString() ?? 'free',
        premiumExpiryDate:
            DateTime.tryParse(
              profileData['premiumExpiryDate']?.toString() ?? '',
            ) ??
            DateTime.now().add(const Duration(days: 365)),
        cvAnalysisComplete: profileData['cvAnalysisComplete'] as bool? ?? false,
        cvAutoFillEnabled: profileData['cvAutoFillEnabled'] as bool? ?? true,
      );
    }
  }

  List<Map<String, dynamic>> _parseListMap(dynamic data, String fieldName) {
    if (data == null) return [];
    if (data is List) {
      try {
        return data
            .map((item) => Map<String, dynamic>.from(item as Map))
            .toList();
      } catch (e) {
        _log.w(
          "[$fieldName] Fehler beim Konvertieren der Liste von Maps: $e. Gebe leere Liste zurück.",
        );
        return [];
      }
    }
    if (data is String) {
      try {
        final decoded = jsonDecode(data);
        if (decoded is List) {
          return decoded
              .map((item) => Map<String, dynamic>.from(item as Map))
              .toList();
        }
      } catch (e) {
        _log.w(
          "[$fieldName] Fehler beim Parsen des JSON-Strings für Liste von Maps: $e. Gebe leere Liste zurück.",
        );
        return [];
      }
    }
    _log.w(
      "[$fieldName] Unerwarteter Typ: ${data.runtimeType}. Gebe leere Liste zurück.",
    );
    return [];
  }

  List<String> _parseListString(dynamic data, String fieldName) {
    if (data == null) return [];
    if (data is List) {
      try {
        return data.map((item) => item.toString()).toList();
      } catch (e) {
        _log.w(
          "[$fieldName] Fehler beim Konvertieren der Liste von Strings: $e. Gebe leere Liste zurück.",
        );
        return [];
      }
    }
    if (data is String) {
      try {
        final decoded = jsonDecode(data);
        if (decoded is List) {
          return decoded.map((item) => item.toString()).toList();
        }
      } catch (e) {
        _log.w(
          "[$fieldName] Fehler beim Parsen des JSON-Strings für Liste von Strings: $e. Gebe leere Liste zurück.",
        );
        return [];
      }
    }
    _log.w(
      "[$fieldName] Unerwarteter Typ: ${data.runtimeType}. Gebe leere Liste zurück.",
    );
    return [];
  }

  // Verarbeitung von Realtime-Updates für Profildaten
  Future<void> _handleProfileUpdate(Map<String, dynamic> newRecord) async {
    try {
      // Prüfe, ob das Update für den aktuellen Benutzer bestimmt ist
      final recordId = newRecord['id']?.toString();
      final currentUser = supabase.auth.currentUser;

      if (currentUser == null) {
        _log.w("[_handleProfileUpdate] Kein angemeldeter Benutzer gefunden.");
        return;
      }

      // Prüfe, ob die ID des Records mit der ID des angemeldeten Benutzers übereinstimmt
      if (recordId != currentUser.id) {
        _log.w(
          "[_handleProfileUpdate] ID-Konflikt: Record ID $recordId stimmt nicht mit angemeldetem Benutzer ${currentUser.id} überein. Update ignoriert.",
        );
        return;
      }

      // Prüfe, ob die Daten vorhanden sind
      if (!newRecord.containsKey('data') || newRecord['data'] == null) {
        _log.w(
          "[_handleProfileUpdate] Realtime-Update enthält keine 'data' Spalte oder 'data' ist null.",
        );
        return;
      }

      // Prüfe, ob wir einen gültigen State haben
      final currentState = state;
      if (currentState is! AsyncData<UserProfile>) {
        _log.w(
          "[_handleProfileUpdate] Kann Profil nicht aktualisieren, da kein gültiger State vorhanden ist: ${currentState.runtimeType}",
        );
        return;
      }

      final currentProfile = currentState.value;

      // Prüfe, ob die ID des aktuellen Profils mit der ID des angemeldeten Benutzers übereinstimmt
      if (currentProfile.id != currentUser.id) {
        _log.w(
          "[_handleProfileUpdate] ID-Konflikt: Profil ID ${currentProfile.id} stimmt nicht mit angemeldetem Benutzer ${currentUser.id} überein.",
        );

        // Wenn die IDs nicht übereinstimmen, setzen wir den State zurück und laden das Profil neu
        _log.i(
          "[_handleProfileUpdate] Setze State zurück und lade Profil neu für Benutzer ${currentUser.id}",
        );

        // Hole das aktuelle Profil aus Supabase
        try {
          // Verwende den DeviceManager, um das Gerät für den Benutzer zu registrieren
          final deviceManager = DeviceManager();
          final registrationResult = await deviceManager.registerDeviceForUser(
            currentUser.id,
          );
          try {
            final anti = ref.read(antiManipulationProvider.notifier);
            anti.setFromServerResult(registrationResult);
          } catch (_) {}

          // Hole das Profil aus dem lokalen Backup, falls vorhanden
          final backupManager = ProfileBackupManager();
          final hasBackup = await backupManager.hasBackup(currentUser.id);

          if (hasBackup) {
            _log.i(
              "[_handleProfileUpdate] Lokales Backup gefunden für Benutzer ${currentUser.id}",
            );
            var backupProfile = await backupManager.restoreProfile(
              currentUser.id,
            );

            if (backupProfile != null && backupProfile.id == currentUser.id) {
              // Aktualisiere Profilbild-URL aus der Datenbank
              backupProfile = await backupManager.updateProfileImageUrl(
                backupProfile,
              );
              _log.i(
                "[_handleProfileUpdate] Profil aus lokalem Backup wiederhergestellt",
              );
              state = AsyncData(backupProfile);

              // Synchronisiere das wiederhergestellte Profil mit Supabase
              try {
                await saveProfileChanges(backupProfile);
                _log.i(
                  "[_handleProfileUpdate] Wiederhergestelltes Profil mit Supabase synchronisiert",
                );
              } catch (e) {
                _log.w(
                  "[_handleProfileUpdate] Fehler bei der Synchronisierung des wiederhergestellten Profils: $e",
                );
              }

              return;
            }
          }

          // Wenn kein lokales Backup vorhanden ist oder die Wiederherstellung fehlgeschlagen ist,
          // hole das Profil aus Supabase
          final existingProfilesResponse = await supabase
              .from('profiles')
              .select(
                'id, data, device_ids, profile_version, onboarding_complete, updated_at',
              )
              .eq('id', currentUser.id);

          final List<Map<String, dynamic>> existingProfiles =
              List<Map<String, dynamic>>.from(existingProfilesResponse);

          if (existingProfiles.isNotEmpty &&
              existingProfiles[0].containsKey('data') &&
              existingProfiles[0]['data'] != null) {
            final data = existingProfiles[0]['data'];
            Map<String, dynamic> profileData;

            if (data is Map) {
              profileData = Map<String, dynamic>.from(data);
            } else if (data is String) {
              profileData = jsonDecode(data) as Map<String, dynamic>;
            } else {
              _log.e(
                "[_handleProfileUpdate] Unerwarteter Datentyp für 'data': ${data.runtimeType}",
              );
              return;
            }

            final profile = _convertDocToUserProfile(
              profileData,
              currentUser.id,
            );
            state = AsyncData(profile);
            _log.i(
              "[_handleProfileUpdate] Profil aus Supabase neu geladen für Benutzer ${currentUser.id}",
            );

            // KRITISCHER FIX: Verwende sofortiges Backup beim Profil-Laden (wichtig für Datenintegrität)
            await backupManager.backupProfileImmediate(profile);
          } else {
            // Wenn kein Profil gefunden wurde, erstellen wir ein neues
            final newProfile = UserProfile(
              id: currentUser.id,
              name: currentUser.userMetadata?['full_name'] ?? '',
              email: currentUser.email ?? '',
              jobPreferencesObj: JobPreferences.empty(),
            );

            state = AsyncData(newProfile);
            _log.i(
              "[_handleProfileUpdate] Neues Profil erstellt für Benutzer ${currentUser.id}",
            );

            // Speichere das neue Profil in Supabase
            await supabase.from('profiles').upsert({
              'id': currentUser.id,
              'data': newProfile.toJson(),
              'onboarding_complete': false, // Setze auf false für neue Benutzer
              'profile_version': 1,
              'updated_at': DateTime.now().toIso8601String(),
            });

            // PERFORMANCE FIX: Erstelle sofortiges Backup für neues Profil (kritisch)
            await backupManager.backupProfileImmediate(newProfile);
          }
        } catch (e) {
          _log.e(
            "[_handleProfileUpdate] Fehler beim Laden des Profils aus Supabase: $e",
          );

          // Im Fehlerfall erstellen wir ein minimales Profil
          final minimalProfile = UserProfile(
            id: currentUser.id,
            email: currentUser.email ?? '',
          );

          state = AsyncData(minimalProfile);
          _log.i(
            "[_handleProfileUpdate] Minimales Profil erstellt nach Fehler",
          );
        }

        return;
      }

      // Parse die Daten
      Map<String, dynamic> data;
      if (newRecord['data'] is Map) {
        data = Map<String, dynamic>.from(newRecord['data']);
      } else if (newRecord['data'] is String) {
        try {
          data =
              jsonDecode(newRecord['data'] as String) as Map<String, dynamic>;
        } catch (e) {
          _log.e(
            "[_handleProfileUpdate] Fehler beim Parsen der Profildaten aus JSON-String: $e",
          );
          return;
        }
      } else {
        _log.e(
          "[_handleProfileUpdate] Unerwarteter Datentyp für 'data': ${newRecord['data'].runtimeType}",
        );
        return;
      }

      // Prüfe, ob die Daten leer oder fast leer sind (nur ID und E-Mail)
      bool isEmptyProfile =
          data.isEmpty ||
          (data.length <= 2 &&
              data.containsKey('id') &&
              data.containsKey('email'));

      // Erweiterte Prüfung: Prüfe, ob wichtige Daten fehlen
      bool isMissingImportantData = true;

      // Prüfe auf wichtige Daten im neuen Profil
      if ((data.containsKey('name') &&
              data['name'] != null &&
              data['name'].toString().isNotEmpty) ||
          (data.containsKey('skills') &&
              data['skills'] is List &&
              (data['skills'] as List).isNotEmpty) ||
          (data.containsKey('jobKeywords') &&
              data['jobKeywords'] is List &&
              (data['jobKeywords'] as List).isNotEmpty) ||
          (data.containsKey('workExperience') &&
              data['workExperience'] is List &&
              (data['workExperience'] as List).isNotEmpty) ||
          (data.containsKey('education') &&
              data['education'] is List &&
              (data['education'] as List).isNotEmpty) ||
          (data.containsKey('cvDownloadUrl') &&
              data['cvDownloadUrl'] != null &&
              data['cvDownloadUrl'].toString().isNotEmpty) ||
          (data.containsKey('jobPreferencesObj') &&
              data['jobPreferencesObj'] is Map &&
              (data['jobPreferencesObj'] as Map).isNotEmpty)) {
        isMissingImportantData = false;
      }

      if (isEmptyProfile || isMissingImportantData) {
        _log.w(
          "[_handleProfileUpdate] Realtime-Update enthält leeres oder unvollständiges Profil. Update ignoriert.",
        );

        // Prüfe, ob das aktuelle Profil Daten enthält
        bool hasCurrentProfileData =
            (currentProfile.name != null && currentProfile.name!.isNotEmpty) ||
            (currentProfile.skills != null &&
                currentProfile.skills!.isNotEmpty) ||
            (currentProfile.jobKeywords != null &&
                currentProfile.jobKeywords!.isNotEmpty) ||
            (currentProfile.workExperience != null &&
                currentProfile.workExperience!.isNotEmpty) ||
            (currentProfile.education != null &&
                currentProfile.education!.isNotEmpty) ||
            (currentProfile.cvDownloadUrl != null &&
                currentProfile.cvDownloadUrl!.isNotEmpty) ||
            (currentProfile.jobPreferencesObj != null &&
                currentProfile.jobPreferencesObj!.targetPosition != null &&
                currentProfile.jobPreferencesObj!.targetPosition!.isNotEmpty);

        if (hasCurrentProfileData) {
          _log.i(
            "[_handleProfileUpdate] Aktuelles Profil enthält Daten, die erhalten werden sollen.",
          );

          // Wichtig: Hier die vorhandenen Daten zurück in die Datenbank schreiben,
          // um sicherzustellen, dass alle Geräte die gleichen Daten haben
          try {
            // Hole die aktuelle Geräte-ID
            final deviceManager = DeviceManager();
            final deviceId = await deviceManager.getDeviceId();

            // Hole die aktuellen Geräte-IDs aus dem Datensatz
            List<String> deviceIds = [];
            if (newRecord.containsKey('device_ids') &&
                newRecord['device_ids'] != null) {
              if (newRecord['device_ids'] is List) {
                deviceIds = List<String>.from(newRecord['device_ids']);
              } else if (newRecord['device_ids'] is String) {
                try {
                  final List<dynamic> parsedIds = jsonDecode(
                    newRecord['device_ids'],
                  );
                  deviceIds = parsedIds.map((id) => id.toString()).toList();
                } catch (e) {
                  _log.w(
                    "[_handleProfileUpdate] Fehler beim Parsen der Geräte-IDs: $e",
                  );
                }
              }
            }

            // Füge die aktuelle Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
            if (!deviceIds.contains(deviceId)) {
              deviceIds.add(deviceId);
            }

            // Aktualisiere das Profil mit den vorhandenen Daten und der aktuellen Geräte-ID
            await supabase
                .from('profiles')
                .update({
                  'data': currentProfile.toJson(),
                  'device_ids': deviceIds,
                  'updated_at': DateTime.now().toIso8601String(),
                })
                .eq('id', currentUser.id);

            _log.i(
              "[_handleProfileUpdate] Lokales Profil in Supabase gespeichert, um leere Daten zu überschreiben.",
            );

            // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
            final backupManager = ProfileBackupManager();
            await backupManager.backupProfile(currentProfile);
          } catch (e) {
            _log.w(
              "[_handleProfileUpdate] Fehler beim Speichern des lokalen Profils in Supabase: $e",
            );
          }
        } else {
          _log.i(
            "[_handleProfileUpdate] Weder aktuelles noch neues Profil enthält ausreichend Daten.",
          );
        }
        return;
      }

      // Prüfe, ob die ID im Datensatz mit der ID des angemeldeten Benutzers übereinstimmt
      final dataId = data['id']?.toString();
      if (dataId != null && dataId != currentUser.id) {
        _log.w(
          "[_handleProfileUpdate] ID-Konflikt in Daten: Daten ID $dataId stimmt nicht mit angemeldetem Benutzer ${currentUser.id} überein. Update ignoriert.",
        );
        return;
      }

      final updatedProfile = _convertDocToUserProfile(data, currentUser.id);

      // Prüfe, ob sich das Profil geändert hat
      if (_hasProfileChanged(currentProfile, updatedProfile)) {
        // Prüfe, ob die neuen Daten "besser" sind als die vorhandenen Daten
        if (_isProfileDataBetter(currentProfile, updatedProfile)) {
          _log.i(
            "[_handleProfileUpdate] Aktualisiere Profil mit Realtime-Daten für Benutzer ${currentUser.id}.",
          );
          state = AsyncData(updatedProfile);

          // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
          final backupManager = ProfileBackupManager();
          await backupManager.backupProfile(updatedProfile);
        } else {
          _log.w(
            "[_handleProfileUpdate] Neue Daten würden vorhandene Daten überschreiben. Update ignoriert.",
          );

          // Wichtig: Hier die vorhandenen Daten zurück in die Datenbank schreiben,
          // um sicherzustellen, dass alle Geräte die gleichen Daten haben
          try {
            await saveProfileChanges(currentProfile);
            _log.i(
              "[_handleProfileUpdate] Lokales Profil in Supabase gespeichert, um Daten zu erhalten.",
            );

            // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
            final backupManager = ProfileBackupManager();
            await backupManager.backupProfile(currentProfile);
          } catch (e) {
            _log.w(
              "[_handleProfileUpdate] Fehler beim Speichern des lokalen Profils in Supabase: $e",
            );
          }
        }
      } else {
        _log.d(
          "[_handleProfileUpdate] Keine Änderungen am Profil erkannt, Realtime-Update ignoriert.",
        );
      }
    } catch (e, stack) {
      _log.e(
        "[_handleProfileUpdate] Fehler bei der Verarbeitung des Realtime-Updates: $e",
        stackTrace: stack,
      );
    }
  }

  // Prüft, ob sich das Profil geändert hat
  bool _hasProfileChanged(UserProfile oldProfile, UserProfile newProfile) {
    // Einfache Felder
    if (oldProfile.name != newProfile.name ||
        oldProfile.email != newProfile.email ||
        oldProfile.phone != newProfile.phone ||
        oldProfile.address != newProfile.address ||
        oldProfile.preferredWritingStyle != newProfile.preferredWritingStyle ||
        oldProfile.includeExperienceInApplication !=
            newProfile.includeExperienceInApplication ||
        oldProfile.experienceSummary != newProfile.experienceSummary ||
        oldProfile.interests != newProfile.interests ||
        oldProfile.isPremium != newProfile.isPremium ||
        oldProfile.premiumPlanType != newProfile.premiumPlanType ||
        oldProfile.premiumExpiryDate != newProfile.premiumExpiryDate ||
        oldProfile.cvDownloadUrl != newProfile.cvDownloadUrl ||
        oldProfile.cvFilePath != newProfile.cvFilePath ||
        oldProfile.cvAnalysisComplete != newProfile.cvAnalysisComplete ||
        oldProfile.cvAnalysisTimestamp != newProfile.cvAnalysisTimestamp ||
        oldProfile.cvAutoFillEnabled != newProfile.cvAutoFillEnabled) {
      return true;
    }

    // Listen
    if (!_areListsEqual(oldProfile.skills, newProfile.skills)) {
      return true;
    }
    if (!_areListsEqual(oldProfile.jobKeywords, newProfile.jobKeywords)) {
      return true;
    }

    // Komplexe Objekte - Hier aufpassen: JobPreferences, WorkExperience, Education sollten `==` und `hashCode` überschreiben für korrekten Vergleich.
    // Ansonsten wird hier nur auf Referenzgleichheit geprüft.
    // Für einen einfachen Check, ob sich etwas geändert hat:
    if (oldProfile.jobPreferencesObj?.toJson().toString() !=
        newProfile.jobPreferencesObj?.toJson().toString()) {
      return true;
    }

    // Für Listen von komplexen Objekten ist ein tiefer Vergleich aufwändig.
    // Wenn die Modelle `==` nicht überschreiben, vergleiche JSON-Strings als Heuristik (nicht performant für große Listen)
    // oder verlasse dich auf Längenänderungen und Referenzänderungen der Objekte.
    if (!_areListsEqualDeep(
      oldProfile.workExperience,
      newProfile.workExperience,
      (a, b) => a.toJson().toString() == b.toJson().toString(),
    )) {
      return true;
    }
    if (!_areListsEqualDeep(
      oldProfile.education,
      newProfile.education,
      (a, b) => a.toJson().toString() == b.toJson().toString(),
    )) {
      return true;
    }

    // Zusätzliche CV-Daten (Map)
    if (!mapEquals(oldProfile.additionalCvData, newProfile.additionalCvData)) {
      return true;
    }

    return false;
  }

  // Hilfsmethode für den Vergleich von Listen (korrigierte Version)
  bool _areListsEqual<T>(List<T>? list1, List<T>? list2) {
    if (identical(list1, list2)) return true;
    if (list1 == null || list2 == null) return false;
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  // Hilfsmethode für tiefen Vergleich von Listen komplexer Objekte
  bool _areListsEqualDeep<T>(
    List<T>? list1,
    List<T>? list2,
    bool Function(T, T) isEqual,
  ) {
    if (identical(list1, list2)) return true;
    if (list1 == null || list2 == null) return false;
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (!isEqual(list1[i], list2[i])) return false;
    }
    return true;
  }

  // Prüft, ob die neuen Profildaten "besser" sind als die vorhandenen Daten
  // Verhindert, dass vorhandene Daten mit leeren Standardwerten überschrieben werden
  bool _isProfileDataBetter(UserProfile oldProfile, UserProfile newProfile) {
    _log.d(
      "[_isProfileDataBetter] Prüfe, ob neue Profildaten besser sind als vorhandene Daten",
    );

    // Prüfe, ob das neue Profil fast leer ist (nur ID und E-Mail)
    bool isNewProfileAlmostEmpty = true;

    // Prüfe auf nicht-leere Felder im neuen Profil
    if ((newProfile.name != null && newProfile.name!.isNotEmpty) ||
        (newProfile.skills != null && newProfile.skills!.isNotEmpty) ||
        (newProfile.jobKeywords != null &&
            newProfile.jobKeywords!.isNotEmpty) ||
        (newProfile.workExperience != null &&
            newProfile.workExperience!.isNotEmpty) ||
        (newProfile.education != null && newProfile.education!.isNotEmpty) ||
        (newProfile.cvDownloadUrl != null &&
            newProfile.cvDownloadUrl!.isNotEmpty) ||
        (newProfile.cvFilePath != null && newProfile.cvFilePath!.isNotEmpty) ||
        (newProfile.additionalCvData != null &&
            newProfile.additionalCvData!.isNotEmpty) ||
        (newProfile.experienceSummary != null &&
            newProfile.experienceSummary!.isNotEmpty) ||
        (newProfile.jobPreferencesObj != null &&
            newProfile.jobPreferencesObj!.targetPosition != null &&
            newProfile.jobPreferencesObj!.targetPosition!.isNotEmpty)) {
      isNewProfileAlmostEmpty = false;
    }

    // Wenn das neue Profil fast leer ist und das alte Profil Daten enthält, behalte das alte
    if (isNewProfileAlmostEmpty) {
      bool hasOldProfileData =
          (oldProfile.name != null && oldProfile.name!.isNotEmpty) ||
          (oldProfile.skills != null && oldProfile.skills!.isNotEmpty) ||
          (oldProfile.jobKeywords != null &&
              oldProfile.jobKeywords!.isNotEmpty) ||
          (oldProfile.workExperience != null &&
              oldProfile.workExperience!.isNotEmpty) ||
          (oldProfile.education != null && oldProfile.education!.isNotEmpty);

      if (hasOldProfileData) {
        _log.d(
          "[_isProfileDataBetter] Neues Profil ist fast leer, altes enthält Daten -> behalte alte Daten",
        );
        return false;
      }
    }

    // Prüfe Listen: Leere Listen sind nicht besser als vorhandene Listen mit Daten
    if ((oldProfile.skills?.isNotEmpty ?? false) &&
        (newProfile.skills?.isEmpty ?? true)) {
      _log.d(
        "[_isProfileDataBetter] Alte Skills nicht leer, neue leer -> behalte alte",
      );
      return false;
    }

    if ((oldProfile.jobKeywords?.isNotEmpty ?? false) &&
        (newProfile.jobKeywords?.isEmpty ?? true)) {
      _log.d(
        "[_isProfileDataBetter] Alte JobKeywords nicht leer, neue leer -> behalte alte",
      );
      return false;
    }

    if ((oldProfile.workExperience?.isNotEmpty ?? false) &&
        (newProfile.workExperience?.isEmpty ?? true)) {
      _log.d(
        "[_isProfileDataBetter] Alte WorkExperience nicht leer, neue leer -> behalte alte",
      );
      return false;
    }

    if ((oldProfile.education?.isNotEmpty ?? false) &&
        (newProfile.education?.isEmpty ?? true)) {
      _log.d(
        "[_isProfileDataBetter] Alte Education nicht leer, neue leer -> behalte alte",
      );
      return false;
    }

    // Prüfe Name und andere Textfelder
    if ((oldProfile.name != null && oldProfile.name!.isNotEmpty) &&
        (newProfile.name == null || newProfile.name!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alter Name vorhanden, neuer leer -> behalte alte",
      );
      return false;
    }

    if ((oldProfile.phone != null && oldProfile.phone!.isNotEmpty) &&
        (newProfile.phone == null || newProfile.phone!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alte Telefonnummer vorhanden, neue leer -> behalte alte",
      );
      return false;
    }

    if ((oldProfile.address != null && oldProfile.address!.isNotEmpty) &&
        (newProfile.address == null || newProfile.address!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alte Adresse vorhanden, neue leer -> behalte alte",
      );
      return false;
    }

    // Prüfe CV-Daten: Wenn alte Daten vorhanden sind und neue nicht, behalte die alten
    if ((oldProfile.cvAnalysisComplete == true) &&
        (newProfile.cvAnalysisComplete != true)) {
      _log.d(
        "[_isProfileDataBetter] Alte CV-Analyse abgeschlossen, neue nicht -> behalte alte",
      );
      return false;
    }

    if (oldProfile.cvDownloadUrl != null &&
        oldProfile.cvDownloadUrl!.isNotEmpty &&
        (newProfile.cvDownloadUrl == null ||
            newProfile.cvDownloadUrl!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alte CV-URL vorhanden, neue nicht -> behalte alte",
      );
      return false;
    }

    if (oldProfile.cvFilePath != null &&
        oldProfile.cvFilePath!.isNotEmpty &&
        (newProfile.cvFilePath == null || newProfile.cvFilePath!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alter CV-Pfad vorhanden, neuer nicht -> behalte alte",
      );
      return false;
    }

    if (oldProfile.additionalCvData != null &&
        oldProfile.additionalCvData!.isNotEmpty &&
        (newProfile.additionalCvData == null ||
            newProfile.additionalCvData!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alte zusätzliche CV-Daten vorhanden, neue nicht -> behalte alte",
      );
      return false;
    }

    if (oldProfile.experienceSummary != null &&
        oldProfile.experienceSummary!.isNotEmpty &&
        (newProfile.experienceSummary == null ||
            newProfile.experienceSummary!.isEmpty)) {
      _log.d(
        "[_isProfileDataBetter] Alte Erfahrungszusammenfassung vorhanden, neue nicht -> behalte alte",
      );
      return false;
    }

    // Prüfe JobPreferences: Wenn alte Daten vorhanden sind und neue leer, behalte die alten
    if (oldProfile.jobPreferencesObj != null &&
        newProfile.jobPreferencesObj != null &&
        !_isJobPreferencesBetter(
          oldProfile.jobPreferencesObj!,
          newProfile.jobPreferencesObj!,
        )) {
      _log.d(
        "[_isProfileDataBetter] Alte JobPreferences besser als neue -> behalte alte",
      );
      return false;
    }

    _log.d(
      "[_isProfileDataBetter] Neue Profildaten sind besser oder gleich gut wie vorhandene Daten",
    );
    return true;
  }

  // Prüft, ob die neuen JobPreferences "besser" sind als die vorhandenen
  bool _isJobPreferencesBetter(
    JobPreferences oldPrefs,
    JobPreferences newPrefs,
  ) {
    // Prüfe, ob die neuen JobPreferences leere Standardwerte haben, während die alten Daten enthalten
    if ((oldPrefs.targetPosition?.isNotEmpty ?? false) &&
        (newPrefs.targetPosition?.isEmpty ?? true)) {
      return false;
    }

    if ((oldPrefs.desiredSalary != null && oldPrefs.desiredSalary! > 0) &&
        (newPrefs.desiredSalary == null || newPrefs.desiredSalary! <= 0)) {
      return false;
    }

    if ((oldPrefs.locationPreference?.isNotEmpty ?? false) &&
        (newPrefs.locationPreference?.isEmpty ?? true)) {
      return false;
    }

    if ((oldPrefs.industry?.isNotEmpty ?? false) &&
        (newPrefs.industry?.isEmpty ?? true)) {
      return false;
    }

    if ((oldPrefs.employmentType?.isNotEmpty ?? false) &&
        (newPrefs.employmentType?.isEmpty ?? true)) {
      return false;
    }

    return true;
  }

  // `_hasJobPreferencesChanged`, `_hasWorkExperienceChanged`, `_hasEducationChanged` sind jetzt in `_hasProfileChanged` integriert oder durch `_areListsEqualDeep` abgedeckt.

  /// Stellt sicher, dass der Lebenslauf lokal verfügbar ist (erste, beibehaltene Version)
  Future<void> _ensureLocalCvAvailable(UserProfile profile) async {
    if (profile.cvDownloadUrl == null || profile.cvDownloadUrl!.isEmpty) {
      _log.d(
        "[_ensureLocalCvAvailable] Kein Download-URL für Lebenslauf vorhanden.",
      );
      return;
    }

    try {
      _log.d(
        "[_ensureLocalCvAvailable] Stelle sicher, dass Lebenslauf lokal verfügbar ist...",
      );
      final localCvPath = await CvStorageHelper.ensureLocalCvAvailable(
        profile.id,
        profile.cvFilePath,
        profile.cvDownloadUrl,
      );

      if (localCvPath != null && localCvPath != profile.cvFilePath) {
        _log.i(
          "[_ensureLocalCvAvailable] Lebenslauf lokal verfügbar gemacht: $localCvPath",
        );
        final updatedProfile = profile.copyWith(cvFilePath: localCvPath);

        // Wichtig: Der State wird hier aktualisiert. Dies geschieht NACHDEM _init den initialen State gesetzt hat.
        state = AsyncData(updatedProfile);

        if (profile.id != null && profile.id!.isNotEmpty) {
          try {
            await supabase
                .from('profiles')
                .update({'data': updatedProfile.toJson()})
                .eq('id', profile.id!);
            _log.i(
              "[_ensureLocalCvAvailable] Profil mit lokalem Lebenslauf-Pfad in Supabase aktualisiert.",
            );
          } catch (e) {
            _log.w(
              "[_ensureLocalCvAvailable] Fehler beim Aktualisieren des Profils in Supabase: $e (ignoriert für App-Start)",
            );
          }
        }
      } else if (localCvPath == null) {
        _log.w(
          "[_ensureLocalCvAvailable] Lebenslauf konnte nicht lokal verfügbar gemacht werden.",
        );
      } else {
        _log.d(
          "[_ensureLocalCvAvailable] Lebenslauf ist bereits lokal verfügbar: $localCvPath",
        );
      }
    } catch (e) {
      _log.w(
        "[_ensureLocalCvAvailable] Fehler beim Sicherstellen der lokalen Verfügbarkeit des Lebenslaufs: $e (ignoriert für App-Start)",
      );
    }
  }

  // Initialisierung des Profils
  Future<void> _init() async {
    // KRITISCHER DEBUG: Erste Zeile der _init Methode
    debugPrint("🔥 KRITISCHER DEBUG: _init() Methode gestartet!");
    print("🔥 KRITISCHER DEBUG: _init() Methode gestartet!");

    final currentUser = supabase.auth.currentUser;
    if (currentUser == null) {
      debugPrint("🔥 KRITISCHER DEBUG: Kein currentUser gefunden!");
      state = AsyncData(UserProfile.empty());
      _log.i(
        "[_init] Kein Supabase Nutzer eingeloggt, initialisiere mit leerem Profil.",
      );
      return;
    }
    debugPrint("🔥 KRITISCHER DEBUG: CurrentUser gefunden: ${currentUser.id}");
    _log.i(
      "[_init] Supabase Nutzer gefunden: ${currentUser.id}. Lade Supabase Profil...",
    );

    // Initialisiere DeviceManager und ProfileBackupManager
    debugPrint("🔥 KRITISCHER DEBUG: Initialisiere DeviceManager...");
    final deviceManager = DeviceManager();
    debugPrint("🔥 KRITISCHER DEBUG: DeviceManager initialisiert!");

    debugPrint("🔥 KRITISCHER DEBUG: Initialisiere ProfileBackupManager...");
    final backupManager = ProfileBackupManager();
    debugPrint("🔥 KRITISCHER DEBUG: ProfileBackupManager initialisiert!");

    // Speichere die aktuelle Benutzer-ID in SharedPreferences
    // Dies ist wichtig, um zu erkennen, wenn der Benutzer wechselt
    debugPrint("🔥 KRITISCHER DEBUG: Lade SharedPreferences...");
    try {
      final prefs = await SharedPreferences.getInstance();
      debugPrint("🔥 KRITISCHER DEBUG: SharedPreferences geladen!");

      debugPrint("🔥 KRITISCHER DEBUG: Lade previousUserId...");
      final previousUserId = prefs.getString('current_user_id');
      debugPrint(
        "🔥 KRITISCHER DEBUG: PreviousUserId geladen: $previousUserId",
      );

      // Wenn der Benutzer gewechselt hat, setzen wir den State zurück
      debugPrint("🔥 KRITISCHER DEBUG: Prüfe User-Wechsel...");
      if (previousUserId != null && previousUserId != currentUser.id) {
        debugPrint(
          "🔥 KRITISCHER DEBUG: User gewechselt von $previousUserId zu ${currentUser.id}",
        );
        _log.w(
          "[_init] Benutzerwechsel erkannt! Vorheriger Benutzer: $previousUserId, Aktueller Benutzer: ${currentUser.id}",
        );
        // Setze den State zurück, um sicherzustellen, dass keine Daten vom vorherigen Benutzer übernommen werden
        state = AsyncData(UserProfile.empty());

        // Entferne alle Realtime-Listener vom vorherigen Benutzer
        _removeRealtimeListener(previousUserId);

        // Entferne das Gerät aus dem vorherigen Benutzerprofil
        try {
          await deviceManager.unregisterDeviceForUser(previousUserId);
          _log.i(
            "[_init] Gerät vom vorherigen Benutzer abgemeldet: $previousUserId",
          );
        } catch (e) {
          _log.w(
            "[_init] Fehler beim Abmelden des Geräts vom vorherigen Benutzer: $e",
          );
        }
      }
      debugPrint("🔥 KRITISCHER DEBUG: User-Wechsel-Prüfung abgeschlossen");

      // Aktualisiere die gespeicherte Benutzer-ID
      debugPrint("🔥 KRITISCHER DEBUG: Speichere aktuelle User-ID...");
      await prefs.setString('current_user_id', currentUser.id);
      debugPrint("🔥 KRITISCHER DEBUG: User-ID gespeichert!");
    } catch (prefsError, prefsStack) {
      _log.e(
        'Fehler beim Zugriff auf SharedPreferences',
        error: prefsError,
        stackTrace: prefsStack,
      );
      // Nicht kritisch, App kann ohne SharedPreferences fortfahren
    }

    // Registriere das Gerät für den aktuellen Benutzer
    debugPrint("🔥 KRITISCHER DEBUG: Starte Geräteregistrierung...");
    try {
      await deviceManager.registerDeviceForUser(currentUser.id);
      debugPrint("🔥 KRITISCHER DEBUG: Geräteregistrierung erfolgreich!");
      _log.i("[_init] Gerät für Benutzer registriert: ${currentUser.id}");
    } catch (e) {
      debugPrint("🔥 KRITISCHER DEBUG: Geräteregistrierung fehlgeschlagen: $e");
      _log.w("[_init] Fehler bei der Geräteregistrierung: $e");
    }

    // Richte den Realtime-Listener für den aktuellen Benutzer ein (asynchron)
    debugPrint("🔥 KRITISCHER DEBUG: Starte Realtime-Listener Setup...");
    Future.microtask(() {
      _setupRealtimeListener(currentUser.id);
      debugPrint("🔥 KRITISCHER DEBUG: Realtime-Listener Setup abgeschlossen!");
    });

    debugPrint("🔥 KRITISCHER DEBUG: Starte Profil-Lade-Logik...");
    try {
      // Prüfe zuerst, ob wir bereits ein Profil im lokalen State haben
      debugPrint("🔥 KRITISCHER DEBUG: Prüfe lokalen State...");
      final currentState = state;
      UserProfile? localProfile;

      // Nur lokales Profil verwenden, wenn es zum aktuellen Benutzer gehört
      if (currentState is AsyncData<UserProfile> &&
          currentState.value.id == currentUser.id &&
          currentState.value.id != null &&
          currentState.value.id!.isNotEmpty) {
        localProfile = currentState.value;
        _log.d("[_init] Lokales Profil im State gefunden: ${localProfile.id}");
      }

      // Prüfe, ob ein lokales Backup existiert
      debugPrint("🔥 KRITISCHER DEBUG: Prüfe lokales Backup...");
      final hasBackup = await backupManager.hasBackup(currentUser.id);
      debugPrint("🔥 KRITISCHER DEBUG: HasBackup Ergebnis: $hasBackup");
      if (hasBackup) {
        _log.i(
          "[_init] Lokales Backup gefunden für Benutzer: ${currentUser.id}",
        );
        debugPrint("🔥 KRITISCHER DEBUG: Starte Backup-Wiederherstellung...");
        var backupProfile = await backupManager.restoreProfile(currentUser.id);
        if (backupProfile != null && backupProfile.id == currentUser.id) {
          // Aktualisiere Profilbild-URL aus der Datenbank
          backupProfile = await backupManager.updateProfileImageUrl(
            backupProfile,
          );
          _log.i("[_init] Profil aus lokalem Backup wiederhergestellt");

          // Wenn wir bereits ein lokales Profil haben, vergleichen wir es mit dem Backup
          if (localProfile != null) {
            // Verwende das "bessere" Profil
            if (_isProfileDataBetter(localProfile, backupProfile)) {
              _log.d(
                "[_init] Lokales Profil ist besser als Backup, behalte lokales Profil",
              );
            } else {
              _log.d(
                "[_init] Backup ist besser als lokales Profil, verwende Backup",
              );
              localProfile = backupProfile;
            }
          } else {
            localProfile = backupProfile;
          }
        }
      }

      // Prüfe, ob das Gerät für den Benutzer registriert ist
      final isDeviceRegistered = await deviceManager.isDeviceRegisteredForUser(
        currentUser.id,
      );
      _log.d(
        "[_init] Gerät für Benutzer ${currentUser.id} registriert: $isDeviceRegistered",
      );

      final existingProfilesResponse = await supabase
          .from('profiles')
          .select(
            'id, data, device_ids, profile_version, onboarding_complete, updated_at',
          )
          .eq('id', currentUser.id);

      // Konvertiere List<dynamic> zu List<Map<String, dynamic>>
      var existingProfiles = List<Map<String, dynamic>>.from(
        existingProfilesResponse,
      );

      _log.d(
        "[_init] Suche nach existierendem Profil: ${existingProfiles.length} Ergebnisse gefunden für Benutzer ${currentUser.id}",
      );

      // Setze has_existing_account auf true, aber überschreibe nicht den Onboarding-Status
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_existing_account', true);
      } catch (prefsError) {
        _log.w('Fehler beim Setzen von has_existing_account: $prefsError');
      }

      // WICHTIG: Versuche zuerst, ein vorhandenes Profil in Supabase zu finden
      // Wir führen eine zweite Abfrage durch, um sicherzustellen, dass wir das neueste Profil haben
      try {
        _log.i(
          "[_init] Führe zusätzliche Suche nach existierendem Profil durch...",
        );
        final additionalProfileCheck =
            await supabase
                .from('profiles')
                .select('id')
                .eq('id', currentUser.id)
                .maybeSingle();

        // Wenn ein Profil existiert, aber die Daten leer sind, versuchen wir, es aus dem Backup wiederherzustellen
        if (additionalProfileCheck != null) {
          _log.i(
            "[_init] Profil existiert in Supabase, aber möglicherweise ohne Daten. Versuche Wiederherstellung...",
          );

          // Versuche, ein Backup aus der profile_backups-Tabelle zu laden
          try {
            final backupResponse =
                await supabase
                    .from('profile_backups')
                    .select('data')
                    .eq('profile_id', currentUser.id)
                    .order('created_at', ascending: false)
                    .limit(1)
                    .maybeSingle();

            if (backupResponse != null && backupResponse['data'] != null) {
              _log.i(
                "[_init] Backup in Supabase gefunden. Verwende dieses für die Wiederherstellung.",
              );

              // Verwende das Backup, um das Profil wiederherzustellen
              Map<String, dynamic> backupData;
              if (backupResponse['data'] is Map) {
                backupData = Map<String, dynamic>.from(backupResponse['data']);
              } else if (backupResponse['data'] is String) {
                backupData =
                    jsonDecode(backupResponse['data']) as Map<String, dynamic>;
              } else {
                _log.w(
                  "[_init] Unerwarteter Datentyp für Backup-Daten. Fahre mit normaler Initialisierung fort.",
                );
                backupData = {};
              }

              if (backupData.isNotEmpty) {
                // Stelle sicher, dass die ID korrekt ist
                backupData['id'] = currentUser.id;

                // Aktualisiere das Profil mit den Backup-Daten
                await supabase
                    .from('profiles')
                    .update({
                      'data': backupData,
                      'device_ids': [await deviceManager.getDeviceId()],
                      'profile_version': 1,
                      'updated_at': DateTime.now().toIso8601String(),
                    })
                    .eq('id', currentUser.id);

                _log.i("[_init] Profil aus Backup wiederhergestellt.");

                // Aktualisiere die existingProfiles-Variable mit den neuen Daten
                final updatedProfileResponse = await supabase
                    .from('profiles')
                    .select(
                      'id, data, device_ids, profile_version, onboarding_complete, updated_at',
                    )
                    .eq('id', currentUser.id);

                if (updatedProfileResponse.isNotEmpty) {
                  existingProfiles = List<Map<String, dynamic>>.from(
                    updatedProfileResponse,
                  );
                }
              }
            }
          } catch (backupError) {
            _log.w("[_init] Fehler beim Laden des Backups: $backupError");
          }
        }
      } catch (additionalCheckError) {
        _log.w(
          "[_init] Fehler bei der zusätzlichen Profilprüfung: $additionalCheckError",
        );
      }

      // Prüfe, ob der Benutzer bereits ein Profil in Supabase hat
      if (existingProfiles.isEmpty ||
          !existingProfiles[0].containsKey('data') ||
          existingProfiles[0]['data'] == null) {
        // Wenn wir ein lokales Profil haben, verwenden wir dieses anstatt ein neues zu erstellen
        if (localProfile != null) {
          _log.i(
            "[_init] Kein Supabase Profil gefunden, aber lokales Profil vorhanden. Speichere lokales Profil in Supabase.",
          );

          // KRITISCHER FIX: Verwende sofortiges Backup beim Profil-Laden (wichtig für Datenintegrität)
          await backupManager.backupProfileImmediate(localProfile);

          // Speichere das lokale Profil in Supabase
          await supabase.from('profiles').upsert({
            'id': currentUser.id,
            'data': localProfile.toJson(),
            'onboarding_complete': false, // Setze auf false für neue Benutzer
            'profile_version': 1,
            'device_ids': [
              await deviceManager.getDeviceId(),
            ], // Füge die aktuelle Geräte-ID hinzu
            'updated_at': DateTime.now().toIso8601String(),
          });

          _log.i("[_init] Lokales Profil in Supabase gespeichert.");
          state = AsyncData(localProfile);
          _ensureLocalCvAvailable(
            localProfile,
          ); // CV-Check nach Initialisierung
        } else {
          // WICHTIG: Führe eine letzte Prüfung durch, bevor ein neues Profil erstellt wird
          _log.i(
            "[_init] Führe finale Prüfung durch, bevor ein neues Profil erstellt wird...",
          );

          try {
            // Prüfe, ob ein Profil in der profiles-Tabelle existiert, auch wenn die Daten leer sind
            final finalProfileCheck =
                await supabase
                    .from('profiles')
                    .select(
                      'id, data, device_ids, profile_version, onboarding_complete, updated_at',
                    )
                    .eq('id', currentUser.id)
                    .maybeSingle();

            if (finalProfileCheck != null) {
              _log.i(
                "[_init] Profil existiert in Supabase. Versuche, es zu verwenden, anstatt ein neues zu erstellen.",
              );

              // Erstelle ein minimales Profil mit den vorhandenen Daten
              final existingProfile = UserProfile(
                id: currentUser.id,
                name: currentUser.userMetadata?['full_name'] ?? '',
                email: currentUser.email ?? '',
                jobPreferencesObj: JobPreferences.empty(),
              );

              // Aktualisiere das Profil mit den minimalen Daten, aber überschreibe keine vorhandenen Daten
              await supabase
                  .from('profiles')
                  .update({
                    'data': existingProfile.toJson(),
                    'device_ids':
                        finalProfileCheck['device_ids'] is List
                            ? [
                              ...List<String>.from(
                                finalProfileCheck['device_ids'],
                              ),
                              await deviceManager.getDeviceId(),
                            ]
                            : [await deviceManager.getDeviceId()],
                    'updated_at': DateTime.now().toIso8601String(),
                  })
                  .eq('id', currentUser.id);

              _log.i("[_init] Vorhandenes Profil aktualisiert.");
              state = AsyncData(existingProfile);
              _ensureLocalCvAvailable(existingProfile);
              return; // Beende die Methode hier, um die Erstellung eines neuen Profils zu vermeiden
            }
          } catch (finalCheckError) {
            _log.w(
              "[_init] Fehler bei der finalen Profilprüfung: $finalCheckError",
            );
          }

          _log.i(
            "[_init] Kein Supabase Profil gefunden für ${currentUser.id} oder Datenfeld leer. Erstelle neues Profil.",
          );
          final newProfile = UserProfile(
            id: currentUser.id,
            name: currentUser.userMetadata?['full_name'] ?? '',
            email: currentUser.email ?? '',
            jobPreferencesObj: JobPreferences.empty(),
            // ... weitere Standardwerte aus UserProfile.empty() oder explizit
          );

          // PERFORMANCE FIX: Erstelle sofortiges Backup für neues Profil (kritisch)
          await backupManager.backupProfileImmediate(newProfile);

          // Speichere das neue Profil in Supabase
          await supabase.from('profiles').upsert({
            'id': currentUser.id,
            'data': newProfile.toJson(),
            'onboarding_complete': false, // Setze auf false für neue Benutzer
            'profile_version': 1,
            'device_ids': [
              await deviceManager.getDeviceId(),
            ], // Füge die aktuelle Geräte-ID hinzu
            'updated_at': DateTime.now().toIso8601String(),
          });

          _log.i("[_init] Neues Profil in Supabase erstellt.");
          state = AsyncData(newProfile);
          _ensureLocalCvAvailable(newProfile); // CV-Check nach Initialisierung
        }
      } else {
        final response = existingProfiles[0];
        _log.d(
          "[_init] Existierendes Profil gefunden, zuletzt aktualisiert: ${response['updated_at']}",
        );

        // Hole die Profil-Version
        final profileVersion = response['profile_version'] as int? ?? 1;
        _log.d("[_init] Profil-Version: $profileVersion");

        Map<String, dynamic> data;
        if (response['data'] is Map) {
          data = Map<String, dynamic>.from(response['data'] as Map);
        } else if (response['data'] is String) {
          try {
            data =
                jsonDecode(response['data'] as String) as Map<String, dynamic>;
          } catch (e) {
            _log.w(
              "[_init] Fehler beim Parsen der Profildaten aus String: $e. Verwende Fallback.",
            );
            data = {
              'id': currentUser.id,
              'email': currentUser.email,
            }; // Minimaler Fallback
          }
        } else {
          _log.w(
            "[_init] Unerwarteter Datentyp für 'data': ${response['data'].runtimeType}. Verwende Fallback.",
          );
          data = {
            'id': currentUser.id,
            'email': currentUser.email,
          }; // Minimaler Fallback
        }

        // Sicherstellen, dass Kernfelder vorhanden sind, aber KEINE vorhandenen Daten überschreiben
        if (!data.containsKey('id') ||
            data['id'] == null ||
            data['id'].toString().isEmpty) {
          data['id'] = currentUser.id;
        }

        if (!data.containsKey('email') ||
            data['email'] == null ||
            data['email'].toString().isEmpty) {
          data['email'] = currentUser.email ?? '';
        }

        if ((!data.containsKey('name') ||
                data['name'] == null ||
                data['name'].toString().isEmpty) &&
            response.containsKey('display_name') &&
            response['display_name'] != null &&
            response['display_name'].toString().isNotEmpty) {
          data['name'] = response['display_name'];
        }

        // Konvertiere die Daten in ein UserProfile-Objekt
        final profile = _convertDocToUserProfile(data, currentUser.id);
        _log.d(
          "[_init] Profil konvertiert: ${profile.toJson().keys.take(5)}...",
        ); // Log nur einige Keys

        if (profile.id == null || profile.id!.isEmpty) {
          _log.w(
            "[_init] Ungültiges Profil aus Supabase geladen (ID fehlt). Erstelle Fallback-Profil.",
          );
          final fallbackProfile = UserProfile(
            id: currentUser.id,
            email: currentUser.email ?? '',
          );
          // Hier nicht erneut in DB speichern, da dies ein korrupter Zustand sein könnte.
          state = AsyncData(fallbackProfile);

          // KRITISCHER FIX: Verwende sofortiges Backup für Fallback-Profil (wichtig für Datenintegrität)
          await backupManager.backupProfileImmediate(fallbackProfile);
        } else {
          // Prüfe, ob wir bereits ein Profil im lokalen State haben
          final currentState = state;
          if (currentState is AsyncData<UserProfile>) {
            final currentProfile = currentState.value;

            // Prüfe, ob die neuen Daten "besser" sind als die vorhandenen Daten
            if (_isProfileDataBetter(currentProfile, profile)) {
              _log.i("[_init] Profil aus Supabase geladen und State gesetzt.");
              state = AsyncData(profile);

              // KRITISCHER FIX: Verwende sofortiges Backup beim Profil-Laden (wichtig für Datenintegrität)
              await backupManager.backupProfileImmediate(profile);
            } else {
              _log.w(
                "[_init] Lokales Profil enthält mehr Daten als das aus Supabase geladene. Behalte lokales Profil.",
              );
              // Wichtig: Hier die vorhandenen Daten zurück in die Datenbank schreiben,
              // um sicherzustellen, dass alle Geräte die gleichen Daten haben
              try {
                // Aktualisiere die Geräte-IDs im Profil
                List<String> deviceIds = [];
                if (response.containsKey('device_ids') &&
                    response['device_ids'] != null) {
                  if (response['device_ids'] is List) {
                    deviceIds = List<String>.from(response['device_ids']);
                  } else if (response['device_ids'] is String) {
                    try {
                      final List<dynamic> parsedIds = jsonDecode(
                        response['device_ids'],
                      );
                      deviceIds = parsedIds.map((id) => id.toString()).toList();
                    } catch (e) {
                      _log.w("[_init] Fehler beim Parsen der Geräte-IDs: $e");
                    }
                  }
                }

                // Füge die aktuelle Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
                final deviceId = await deviceManager.getDeviceId();
                if (!deviceIds.contains(deviceId)) {
                  deviceIds.add(deviceId);
                }

                await supabase
                    .from('profiles')
                    .update({
                      'data': currentProfile.toJson(),
                      'device_ids': deviceIds,
                      'profile_version': profileVersion + 1,
                      'updated_at': DateTime.now().toIso8601String(),
                    })
                    .eq('id', currentUser.id);

                _log.i(
                  "[_init] Lokales Profil in Supabase gespeichert, um Daten zu erhalten.",
                );

                // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
                await backupManager.backupProfile(currentProfile);
              } catch (e) {
                _log.w(
                  "[_init] Fehler beim Speichern des lokalen Profils in Supabase: $e",
                );
              }
            }
          } else {
            // Wenn wir noch kein Profil im State haben, setze das geladene Profil
            state = AsyncData(profile);
            _log.i("[_init] Profil aus Supabase geladen und State gesetzt.");

            // Aktualisiere die Geräte-IDs im Profil
            try {
              List<String> deviceIds = [];
              if (response.containsKey('device_ids') &&
                  response['device_ids'] != null) {
                if (response['device_ids'] is List) {
                  deviceIds = List<String>.from(response['device_ids']);
                } else if (response['device_ids'] is String) {
                  try {
                    final List<dynamic> parsedIds = jsonDecode(
                      response['device_ids'],
                    );
                    deviceIds = parsedIds.map((id) => id.toString()).toList();
                  } catch (e) {
                    _log.w("[_init] Fehler beim Parsen der Geräte-IDs: $e");
                  }
                }
              }

              // Füge die aktuelle Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
              final deviceId = await deviceManager.getDeviceId();
              if (!deviceIds.contains(deviceId)) {
                deviceIds.add(deviceId);

                await supabase
                    .from('profiles')
                    .update({'device_ids': deviceIds})
                    .eq('id', currentUser.id);

                _log.i("[_init] Geräte-ID zum Profil hinzugefügt: $deviceId");
              }
            } catch (e) {
              _log.w("[_init] Fehler beim Aktualisieren der Geräte-IDs: $e");
            }

            // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
            await backupManager.backupProfile(profile);
          }

          _ensureLocalCvAvailable(profile); // CV-Check nach Initialisierung

          // Wir setzen den Onboarding-Status nicht automatisch auf true,
          // damit der Onboarding-Screen für neue Benutzer angezeigt wird
          _log.i("[_init] Onboarding-Status wird nicht automatisch gesetzt.");
        }
      }
    } catch (e, stack) {
      _log.e("[_init] Schwerwiegender Fehler: $e", stackTrace: stack);
      // Im Fehlerfall versuchen, ein minimales Profil zu erstellen oder den Fehler anzuzeigen
      final minimalProfile = UserProfile(
        id: currentUser.id,
        email: currentUser.email ?? '',
      );
      state = AsyncError<UserProfile>(
        e,
        stack,
      ).copyWithPrevious(AsyncData(minimalProfile));

      // PERFORMANCE FIX: Erstelle optimiertes Backup (mit Debouncing)
      try {
        await backupManager.backupProfile(minimalProfile);
      } catch (backupError) {
        _log.w(
          "[_init] Fehler beim Erstellen des Backup für minimales Profil: $backupError",
        );
      }
    }
  }

  // NEU: Methode zum bedingten Aktualisieren des Profils aus CV-Daten
  Future<bool> conditionallyUpdateProfileFromCv(
    Map<String, dynamic> cvData,
  ) async {
    final currentState = state;
    if (currentState is! AsyncData<UserProfile>) {
      _log.w(
        "[conditionallyUpdateProfileFromCv] Profil nicht bereit für CV-Update. Status: ${currentState.runtimeType}",
      );
      return false;
    }

    final currentProfile = currentState.value;
    bool updated = false;
    UserProfile profileToUpdate = currentProfile;
    Map<String, dynamic> remainingCvData = Map<String, dynamic>.from(cvData);
    _log.d(
      "[conditionallyUpdateProfileFromCv] Empfangene CV-Daten: ${cvData.keys}",
    );

    // Fähigkeiten
    if ((profileToUpdate.skills ?? []).isEmpty &&
        remainingCvData.containsKey('extractedSkills') &&
        remainingCvData['extractedSkills'] is List) {
      final List<String> extractedSkills =
          List<String>.from(
            remainingCvData['extractedSkills'],
          ).where((s) => s.trim().isNotEmpty).toList();
      if (extractedSkills.isNotEmpty) {
        profileToUpdate = profileToUpdate.copyWith(skills: extractedSkills);
        updated = true;
        remainingCvData.remove('extractedSkills');
        _log.d("[conditionallyUpdateProfileFromCv] Fähigkeiten aktualisiert.");
      }
    }

    // Berufserfahrung
    if ((profileToUpdate.workExperience ?? []).isEmpty &&
        remainingCvData.containsKey('extractedExperience') &&
        remainingCvData['extractedExperience'] is List) {
      final List<WorkExperience> extractedExperience =
          (remainingCvData['extractedExperience'] as List)
              .map((expData) {
                if (expData is Map<String, dynamic>) {
                  DateTime? startDate = _parseCvDate(expData['startDate']);
                  DateTime? endDate = _parseCvDate(
                    expData['endDate'],
                    allowPresent: true,
                  );
                  return WorkExperience(
                    position: expData['position'] ?? 'N/A',
                    company: expData['company'] ?? 'N/A',
                    startDate:
                        startDate ??
                        DateTime(
                          1970,
                        ), // Fallback, wenn Modell non-nullable startDate hat
                    endDate: endDate,
                    description: expData['description'] ?? '',
                  );
                }
                return null;
              })
              .whereType<WorkExperience>()
              .toList();
      if (extractedExperience.isNotEmpty) {
        profileToUpdate = profileToUpdate.copyWith(
          workExperience: extractedExperience,
        );
        updated = true;
        remainingCvData.remove('extractedExperience');
        _log.d(
          "[conditionallyUpdateProfileFromCv] Berufserfahrung aktualisiert.",
        );
      }
    }

    // Ausbildung
    if ((profileToUpdate.education ?? []).isEmpty &&
        remainingCvData.containsKey('extractedEducation') &&
        remainingCvData['extractedEducation'] is List) {
      final List<Education> extractedEducation =
          (remainingCvData['extractedEducation'] as List)
              .map((eduData) {
                if (eduData is Map<String, dynamic>) {
                  DateTime? startDate = _parseCvDate(eduData['startDate']);
                  DateTime? endDate = _parseCvDate(
                    eduData['endDate'],
                    allowPresent: true,
                  );
                  return Education(
                    degree: eduData['degree'] ?? 'N/A',
                    institution: eduData['institution'] ?? 'N/A',
                    startDate: startDate ?? DateTime(1970), // Fallback
                    endDate: endDate,
                    fieldOfStudy: eduData['fieldOfStudy'],
                  );
                }
                return null;
              })
              .whereType<Education>()
              .toList();
      if (extractedEducation.isNotEmpty) {
        profileToUpdate = profileToUpdate.copyWith(
          education: extractedEducation,
        );
        updated = true;
        remainingCvData.remove('extractedEducation');
        _log.d("[conditionallyUpdateProfileFromCv] Ausbildung aktualisiert.");
      }
    }

    // Felder, die nicht in additionalCvData landen sollen
    remainingCvData.removeWhere(
      (key, value) =>
          ['fullName', 'email', 'phone', 'extractedSummary'].contains(key),
    );

    if (remainingCvData.isNotEmpty) {
      final Map<String, dynamic> currentAdditionalData =
          profileToUpdate.additionalCvData ?? {};
      final Map<String, dynamic> combinedAdditionalData = {
        ...currentAdditionalData,
        ...remainingCvData,
      };
      if (!mapEquals(
        profileToUpdate.additionalCvData,
        combinedAdditionalData,
      )) {
        profileToUpdate = profileToUpdate.copyWith(
          additionalCvData: combinedAdditionalData,
        );
        updated = true;
        _log.d(
          "[conditionallyUpdateProfileFromCv] Zusätzliche Daten aktualisiert: ${combinedAdditionalData.keys}",
        );
      }
    }

    if (updated) {
      _log.i(
        "[conditionallyUpdateProfileFromCv] Änderungen durch CV-Analyse erkannt. Speichere Profil...",
      );
      try {
        await updateProfile(profileToUpdate); // Oder saveProfileChanges
      } catch (e) {
        _log.e(
          "[conditionallyUpdateProfileFromCv] Fehler beim Speichern des Profils nach CV-Update: $e",
        );
        // Fehler wurde bereits in updateProfile geloggt und State gesetzt.
        return false; // Update fehlgeschlagen
      }
    } else {
      _log.d(
        "[conditionallyUpdateProfileFromCv] Keine leeren oder zusätzlichen Felder zum Aktualisieren gefunden.",
      );
    }
    return updated;
  }

  // Hilfsmethode zum Parsen von Datumsangaben aus CV-Daten
  DateTime? _parseCvDate(dynamic dateValue, {bool allowPresent = false}) {
    if (dateValue == null) return null;
    String dateString = dateValue.toString().trim();

    if (allowPresent &&
        (dateString.toLowerCase() == 'present' ||
            dateString.toLowerCase() == 'heute')) {
      return null; // Aktuell -> null
    }

    List<String> formats = [
      'yyyy-MM-dd',
      'dd.MM.yyyy',
      'MM/dd/yyyy',
      'yyyy-MM',
      'MM.yyyy',
      'yyyy',
    ];
    for (String format in formats) {
      try {
        return DateFormat(format).parseStrict(dateString);
      } catch (_) {
        /* nächstes Format versuchen */
      }
    }
    _log.w("[_parseCvDate] Konnte Datum nicht parsen: $dateString");
    return null;
  }

  // NEU: Methode zum Erzwingen des Updates aus CV-Daten (überschreibt vorhandene Daten)
  Future<bool> forceUpdateProfileFromCv(Map<String, dynamic> cvData) async {
    final currentState = state;
    if (currentState is! AsyncData<UserProfile>) {
      _log.w(
        "[forceUpdateProfileFromCv] Profil nicht bereit für erzwungenes CV-Update. Status: ${currentState.runtimeType}",
      );
      return false;
    }

    final currentProfile = currentState.value;
    UserProfile profileToUpdate = currentProfile;
    Map<String, dynamic> remainingCvData = Map<String, dynamic>.from(cvData);
    _log.d("[forceUpdateProfileFromCv] Empfangene CV-Daten: ${cvData.keys}");

    List<String> extractedSkills = [];
    if (remainingCvData.containsKey('extractedSkills') &&
        remainingCvData['extractedSkills'] is List) {
      extractedSkills =
          List<String>.from(
            remainingCvData['extractedSkills'],
          ).where((s) => s.trim().isNotEmpty).toList();
      remainingCvData.remove('extractedSkills');
    }
    profileToUpdate = profileToUpdate.copyWith(skills: extractedSkills);
    _log.d(
      "[forceUpdateProfileFromCv] Fähigkeiten überschrieben (${extractedSkills.length} Einträge).",
    );

    List<WorkExperience> extractedExperience = [];
    if (remainingCvData.containsKey('extractedExperience') &&
        remainingCvData['extractedExperience'] is List) {
      extractedExperience =
          (remainingCvData['extractedExperience'] as List)
              .map((expData) {
                if (expData is Map<String, dynamic>) {
                  DateTime? startDate = _parseCvDate(expData['startDate']);
                  DateTime? endDate = _parseCvDate(
                    expData['endDate'],
                    allowPresent: true,
                  );
                  return WorkExperience(
                    position: expData['position'] ?? 'N/A',
                    company: expData['company'] ?? 'N/A',
                    startDate: startDate ?? DateTime(1970), // Fallback
                    endDate: endDate,
                    description: expData['description'] ?? '',
                  );
                }
                return null;
              })
              .whereType<WorkExperience>()
              .toList();
      remainingCvData.remove('extractedExperience');
    }
    profileToUpdate = profileToUpdate.copyWith(
      workExperience: extractedExperience,
    );
    _log.d(
      "[forceUpdateProfileFromCv] Berufserfahrung überschrieben (${extractedExperience.length} Einträge).",
    );

    List<Education> extractedEducation = [];
    if (remainingCvData.containsKey('extractedEducation') &&
        remainingCvData['extractedEducation'] is List) {
      extractedEducation =
          (remainingCvData['extractedEducation'] as List)
              .map((eduData) {
                if (eduData is Map<String, dynamic>) {
                  DateTime? startDate = _parseCvDate(eduData['startDate']);
                  DateTime? endDate = _parseCvDate(
                    eduData['endDate'],
                    allowPresent: true,
                  );
                  return Education(
                    degree: eduData['degree'] ?? 'N/A',
                    institution: eduData['institution'] ?? 'N/A',
                    startDate: startDate ?? DateTime(1970), // Fallback
                    endDate: endDate,
                    fieldOfStudy: eduData['fieldOfStudy'],
                  );
                }
                return null;
              })
              .whereType<Education>()
              .toList();
      remainingCvData.remove('extractedEducation');
    }
    profileToUpdate = profileToUpdate.copyWith(education: extractedEducation);
    _log.d(
      "[forceUpdateProfileFromCv] Ausbildung überschrieben (${extractedEducation.length} Einträge).",
    );

    if (remainingCvData.containsKey('extractedSummary') &&
        remainingCvData['extractedSummary'] is String) {
      final String summary =
          remainingCvData['extractedSummary'].toString().trim();
      if (summary.isNotEmpty) {
        profileToUpdate = profileToUpdate.copyWith(experienceSummary: summary);
        _log.d(
          "[forceUpdateProfileFromCv] Erfahrungszusammenfassung aktualisiert.",
        );
      }
      remainingCvData.remove('extractedSummary');
    }

    remainingCvData.removeWhere(
      (key, value) => ['fullName', 'email', 'phone'].contains(key),
    );

    if (remainingCvData.isNotEmpty) {
      if (!mapEquals(profileToUpdate.additionalCvData, remainingCvData)) {
        profileToUpdate = profileToUpdate.copyWith(
          additionalCvData: remainingCvData,
        );
        _log.d(
          "[forceUpdateProfileFromCv] Zusätzliche Daten überschrieben: ${remainingCvData.keys}",
        );
      }
    } else {
      if (profileToUpdate.additionalCvData != null &&
          profileToUpdate.additionalCvData!.isNotEmpty) {
        profileToUpdate = profileToUpdate.copyWith(additionalCvData: {});
        _log.d(
          "[forceUpdateProfileFromCv] Alte zusätzliche Daten gelöscht, da keine neuen vorhanden.",
        );
      }
    }

    profileToUpdate = profileToUpdate.copyWith(
      cvAnalysisComplete: true,
      cvAnalysisTimestamp: DateTime.now(),
    );

    _log.i("[forceUpdateProfileFromCv] Speichere aktualisiertes Profil...");
    try {
      await updateProfile(profileToUpdate); // Oder saveProfileChanges
    } catch (e) {
      _log.e(
        "[forceUpdateProfileFromCv] Fehler beim Speichern des Profils nach erzwungenem CV-Update: $e",
      );
      return false;
    }
    return true;
  }

  // NEU: Funktion zum Zurücksetzen aller Benutzerdaten (auf Standardwerte)
  Future<void> resetProfile() async {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    if (currentUserId == null) {
      final error = "Benutzer nicht angemeldet für Profil-Reset.";
      _log.w("[resetProfile] Reset abgebrochen: $error");
      state = AsyncError<UserProfile>(
        error,
        StackTrace.current,
      ).copyWithPrevious(state);
      return;
    }

    final previousState = state;
    state = const AsyncLoading<UserProfile>().copyWithPrevious(previousState);

    try {
      final email = Supabase.instance.client.auth.currentUser?.email ?? '';
      // Verwende UserProfile.empty() und setze ID und E-Mail
      final emptyProfile = UserProfile.empty().copyWith(
        id: currentUserId,
        email: email,
        // Stelle sicher, dass auch CV-bezogene Felder zurückgesetzt werden
        cvDownloadUrl: null,
        cvFilePath: null,
        cvAnalysisComplete: false,
        cvAnalysisTimestamp: null,
        additionalCvData: null, // explizit null setzen
      );

      await supabase.from('profiles').upsert({
        'id': currentUserId,
        'data': emptyProfile.toJson(),
        'updated_at':
            DateTime.now().toIso8601String(), // Wichtig für Realtime etc.
      });
      state = AsyncData(emptyProfile);
      _log.i(
        "[resetProfile] Profil erfolgreich zurückgesetzt für ID: $currentUserId",
      );
    } catch (e, stack) {
      _log.e(
        "[resetProfile] Fehler beim Zurücksetzen des Profils: $e",
        stackTrace: stack,
      );
      state = AsyncError<UserProfile>(e, stack).copyWithPrevious(previousState);
      rethrow;
    }
  }

  /// Generiert Berufsbezeichnungen als Schlüsselwörter basierend auf dem Nutzerprofil
  Future<List<String>> generateJobKeywordsFromProfile(
    UserProfile userProfile,
  ) async {
    _log.d(
      "[generateJobKeywordsFromProfile] Generiere Berufsbezeichnungen für Profil ${userProfile.id}",
    );
    // try {
    //   final Map<String, dynamic> profileDataForFunction = {
    //     'id': userProfile.id,
    //     'name': userProfile.name,
    //     'email': userProfile.email,
    //     'skills': userProfile.skills,
    //     'experienceSummary': userProfile.experienceSummary,
    //     'workExperience': (userProfile.workExperience ?? []).map((exp) => exp.toJson()).toList(),
    //     'education': (userProfile.education ?? []).map((edu) => edu.toJson()).toList(),
    //   };
    //   _log.d("[generateJobKeywordsFromProfile] Sende Daten an Supabase-Funktion 'generate-job-keywords': ${profileDataForFunction.keys}");
    //   final response = await supabase.functions.invoke('generate-job-keywords', body: profileDataForFunction);
    //   if (response.status == 200 && response.data is Map) {
    //     final List<String> keywords = List<String>.from(response.data['keywords'] ?? []);
    //     _log.i("[generateJobKeywordsFromProfile] Berufsbezeichnungen erfolgreich generiert: ${keywords.join(', ')}");
    //     return keywords;
    //   } else {
    //     _log.w("[generateJobKeywordsFromProfile] Fehler von Supabase-Funktion: Status ${response.status}, Data: ${response.data}");
    //     return [];
    //   }
    // } catch (e, stack) {
    //   _log.e("[generateJobKeywordsFromProfile] Fehler beim Aufruf der Supabase-Funktion: $e", stackTrace: stack);
    //   return [];
    // }

    // Simulierter Aufruf, da die Funktion evtl. nicht existiert / für Testzwecke
    _log.i(
      "[generateJobKeywordsFromProfile] Simuliere Generierung von Berufsbezeichnungen.",
    );
    await Future.delayed(
      const Duration(seconds: 1),
    ); // Simuliere Netzwerk-Latenz
    final List<String> jobKeywords = [
      'Softwareentwickler',
      'App-Entwickler',
      'Flutter-Entwickler',
      'Mobile Developer',
      'Frontend-Entwickler',
      'Projektmanager IT',
    ];
    if ((userProfile.skills ?? []).any(
      (skill) => skill.toLowerCase().contains('projektmanagement'),
    )) {
      jobKeywords.add('IT-Projektmanager');
    }
    _log.i(
      "[generateJobKeywordsFromProfile] Simulierte Berufsbezeichnungen: ${jobKeywords.join(', ')}",
    );
    return jobKeywords;
  }

  /// Aktualisiert die Anzahl der verwendeten Bewerbungen
  Future<void> updateUsedApplications(int usedApplications) async {
    final currentState = state;
    if (currentState is! AsyncData<UserProfile>) {
      _log.w(
        '[updateUsedApplications] Profil nicht bereit für Update. Status: ${currentState.runtimeType}',
      );
      return;
    }
    final currentProfile = currentState.value;
    _log.d(
      "[updateUsedApplications] Aktualisiere verwendete Bewerbungen für Profil ${currentProfile.id} auf $usedApplications",
    );

    try {
      final updatedProfile = currentProfile.copyWith(
        usedApplications: usedApplications,
      );
      await saveProfileChanges(updatedProfile);
      _log.i(
        "[updateUsedApplications] Verwendete Bewerbungen erfolgreich aktualisiert auf: $usedApplications",
      );
    } catch (e, stack) {
      _log.e(
        "[updateUsedApplications] Fehler beim Aktualisieren der verwendeten Bewerbungen: $e",
        stackTrace: stack,
      );
      // Der Fehler wurde bereits von saveProfileChanges behandelt
      rethrow;
    }
  }

  /// Aktualisiert die Berufsbezeichnungen für das aktuelle Profil (zweite, verbesserte Version)
  Future<bool> updateJobKeywords() async {
    final currentState = state;
    if (currentState is! AsyncData<UserProfile>) {
      _log.w(
        "[updateJobKeywords] Profil nicht bereit für Job-Keywords-Update. Status: ${currentState.runtimeType}",
      );
      return false;
    }
    final currentProfile = currentState.value;
    _log.d(
      "[updateJobKeywords] Starte Update der Berufsbezeichnungen für Profil ${currentProfile.id}",
    );

    try {
      final jobKeywords = await generateJobKeywordsFromProfile(currentProfile);
      if (jobKeywords.isNotEmpty) {
        // Prüfen, ob sich die Keywords tatsächlich geändert haben
        if (!_areListsEqual(currentProfile.jobKeywords, jobKeywords)) {
          final updatedProfile = currentProfile.copyWith(
            jobKeywords: jobKeywords,
          );
          await saveProfileChanges(
            updatedProfile,
          ); // Nutzt die zentrale Speichermethode
          _log.i(
            "[updateJobKeywords] Berufsbezeichnungen erfolgreich aktualisiert: ${jobKeywords.join(', ')}",
          );
        } else {
          _log.d(
            "[updateJobKeywords] Generierte Berufsbezeichnungen sind identisch mit den aktuellen. Kein Update nötig.",
          );
        }
        return true;
      } else {
        _log.i(
          "[updateJobKeywords] Keine Berufsbezeichnungen generiert, Profil nicht aktualisiert.",
        );
        // Optional: Bestehende Keywords löschen, wenn keine neuen generiert wurden?
        // if (currentProfile.jobKeywords != null && currentProfile.jobKeywords!.isNotEmpty) {
        //   final updatedProfile = currentProfile.copyWith(jobKeywords: []);
        //   await saveProfileChanges(updatedProfile);
        // }
        return false;
      }
    } catch (e, stack) {
      _log.e(
        "[updateJobKeywords] Fehler beim Aktualisieren der Berufsbezeichnungen: $e",
        stackTrace: stack,
      );
      // Der Fehler wurde bereits von saveProfileChanges behandelt (falls er dort auftrat)
      return false;
    }
  }
}

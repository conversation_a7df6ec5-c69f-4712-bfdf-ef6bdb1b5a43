import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/models/cv_template.dart';
import '../services/cv_template_service.dart';
import 'user_profile_provider.dart';

/// Provider für CV-Template-Service
final cvTemplateServiceProvider = Provider<CvTemplateService>((ref) {
  return CvTemplateService();
});

/// Provider für alle verfügbaren Templates
final allTemplatesProvider = Provider<List<CvTemplate>>((ref) {
  final service = ref.read(cvTemplateServiceProvider);
  return service.getAllTemplates();
});

/// Provider für kostenlose Templates
final freeTemplatesProvider = Provider<List<CvTemplate>>((ref) {
  final service = ref.read(cvTemplateServiceProvider);
  return service.getFreeTemplates();
});

/// Provider für Premium-Templates
final premiumTemplatesProvider = Provider<List<CvTemplate>>((ref) {
  final service = ref.read(cvTemplateServiceProvider);
  return service.getPremiumTemplates();
});

/// Provider für verfügbare Templates basierend auf User-Status
final availableTemplatesProvider = Provider<List<CvTemplate>>((ref) {
  final service = ref.read(cvTemplateServiceProvider);
  final userProfile = ref.watch(userProfileProvider);

  final isPremiumUser = userProfile.when(
    data: (profile) => profile.isPremium ?? false,
    loading: () => false,
    error: (_, __) => false,
  );

  return service.getAvailableTemplatesForUser(isPremiumUser);
});

/// Provider für Templates nach Kategorien
final templatesByCategoryProvider =
    Provider<Map<CvTemplateType, List<CvTemplate>>>((ref) {
      final service = ref.read(cvTemplateServiceProvider);
      return service.getTemplatesByCategory();
    });

/// Provider für aktuell ausgewähltes Template
final selectedTemplateProvider =
    StateNotifierProvider<SelectedTemplateNotifier, CvTemplate?>((ref) {
      final service = ref.read(cvTemplateServiceProvider);
      return SelectedTemplateNotifier(service);
    });

/// Notifier für ausgewähltes Template
class SelectedTemplateNotifier extends StateNotifier<CvTemplate?> {
  static final Logger _log = Logger();
  final CvTemplateService _templateService;

  SelectedTemplateNotifier(this._templateService) : super(null) {
    // Lade gespeichertes Template oder setze Standard-Template
    _loadSavedTemplate();
  }

  /// Lädt gespeichertes Template oder setzt Standard-Template
  Future<void> _loadSavedTemplate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTemplateId = prefs.getString('selected_cv_template_id');

      if (savedTemplateId != null) {
        final savedTemplate = _templateService.getTemplateById(savedTemplateId);
        if (savedTemplate != null) {
          state = savedTemplate;
          _log.i(
            '🔥 CV-Template-Provider: Gespeichertes Template ${savedTemplate.name} geladen',
          );
          return;
        }
      }

      // Fallback auf Standard-Template
      final defaultTemplate = _templateService.getDefaultTemplate();
      state = defaultTemplate;
      _log.i(
        '🔥 CV-Template-Provider: Standard-Template ${defaultTemplate.name} gesetzt',
      );
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Template-Provider: Fehler beim Laden des Templates',
        error: e,
        stackTrace: stackTrace,
      );

      // Fallback auf Standard-Template
      try {
        final defaultTemplate = _templateService.getDefaultTemplate();
        state = defaultTemplate;
      } catch (fallbackError) {
        _log.e(
          '❌ CV-Template-Provider: Auch Standard-Template konnte nicht geladen werden',
          error: fallbackError,
        );
      }
    }
  }

  /// Setzt ausgewähltes Template
  Future<void> selectTemplate(CvTemplate template) async {
    state = template;
    _log.i('🔥 CV-Template-Provider: Template ${template.name} ausgewählt');

    // Speichere Template-Auswahl
    await _saveSelectedTemplate(template.id);
  }

  /// Speichert ausgewähltes Template
  Future<void> _saveSelectedTemplate(String templateId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_cv_template_id', templateId);
      _log.i('🔥 CV-Template-Provider: Template-ID $templateId gespeichert');
    } catch (e, stackTrace) {
      _log.e(
        '❌ CV-Template-Provider: Fehler beim Speichern der Template-Auswahl',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Setzt Template nach ID
  void selectTemplateById(String templateId) {
    final template = _templateService.getTemplateById(templateId);
    if (template != null) {
      selectTemplate(template);
    } else {
      _log.w(
        '⚠️ CV-Template-Provider: Template mit ID $templateId nicht gefunden',
      );
    }
  }

  /// Setzt nächstes Template in der Liste
  void selectNextTemplate(List<CvTemplate> availableTemplates) {
    if (state == null || availableTemplates.isEmpty) return;

    final currentIndex = availableTemplates.indexWhere(
      (t) => t.id == state!.id,
    );
    if (currentIndex == -1) return;

    final nextIndex = (currentIndex + 1) % availableTemplates.length;
    selectTemplate(availableTemplates[nextIndex]);
  }

  /// Setzt vorheriges Template in der Liste
  void selectPreviousTemplate(List<CvTemplate> availableTemplates) {
    if (state == null || availableTemplates.isEmpty) return;

    final currentIndex = availableTemplates.indexWhere(
      (t) => t.id == state!.id,
    );
    if (currentIndex == -1) return;

    final previousIndex =
        currentIndex == 0 ? availableTemplates.length - 1 : currentIndex - 1;
    selectTemplate(availableTemplates[previousIndex]);
  }

  /// Prüft ob aktuelles Template Premium ist
  bool get isCurrentTemplatePremium => state?.isPremium ?? false;

  /// Gibt aktuelles Template oder Standard-Template zurück
  CvTemplate getCurrentTemplateOrDefault() {
    return state ?? _templateService.getDefaultTemplate();
  }
}

/// Provider für Template-Verfügbarkeit
final templateAvailabilityProvider = Provider.family<bool, CvTemplate>((
  ref,
  template,
) {
  final service = ref.read(cvTemplateServiceProvider);
  final userProfile = ref.watch(userProfileProvider);

  final isPremiumUser = userProfile.when(
    data: (profile) => profile.isPremium ?? false,
    loading: () => false,
    error: (_, __) => false,
  );

  return service.isTemplateAvailable(template, isPremiumUser);
});

/// Provider für Template nach Typ
final templatesByTypeProvider =
    Provider.family<List<CvTemplate>, CvTemplateType>((ref, type) {
      final service = ref.read(cvTemplateServiceProvider);
      return service.getTemplatesByType(type);
    });

/// Provider für Template nach ID
final templateByIdProvider = Provider.family<CvTemplate?, String>((ref, id) {
  final service = ref.read(cvTemplateServiceProvider);
  return service.getTemplateById(id);
});

/// Provider für Template-Statistiken
final templateStatsProvider = Provider<Map<String, int>>((ref) {
  final allTemplates = ref.watch(allTemplatesProvider);
  final freeTemplates = ref.watch(freeTemplatesProvider);
  final premiumTemplates = ref.watch(premiumTemplatesProvider);

  return {
    'total': allTemplates.length,
    'free': freeTemplates.length,
    'premium': premiumTemplates.length,
  };
});

/// Provider für Template-Vorschau-Status
final templatePreviewProvider =
    StateNotifierProvider<TemplatePreviewNotifier, Map<String, bool>>((ref) {
      return TemplatePreviewNotifier();
    });

/// Notifier für Template-Vorschau-Status
class TemplatePreviewNotifier extends StateNotifier<Map<String, bool>> {
  static final Logger _log = Logger();

  TemplatePreviewNotifier() : super({});

  /// Setzt Vorschau-Status für Template
  void setPreviewStatus(String templateId, bool isLoading) {
    state = {...state, templateId: isLoading};
    _log.i(
      '🔥 CV-Template-Provider: Vorschau-Status für Template $templateId: ${isLoading ? 'lädt' : 'fertig'}',
    );
  }

  /// Gibt Vorschau-Status für Template zurück
  bool isPreviewLoading(String templateId) {
    return state[templateId] ?? false;
  }

  /// Setzt alle Vorschau-Status zurück
  void resetAllPreviewStatus() {
    state = {};
    _log.i('🔥 CV-Template-Provider: Alle Vorschau-Status zurückgesetzt');
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';

class AntiManipulationState {
  final bool zeroCredit;
  final String? originalEmail;
  final String? reason;
  final bool premiumRequired;

  const AntiManipulationState({
    this.zeroCredit = false,
    this.originalEmail,
    this.reason,
    this.premiumRequired = false,
  });

  AntiManipulationState copyWith({
    bool? zeroCredit,
    String? originalEmail,
    String? reason,
    bool? premiumRequired,
  }) {
    return AntiManipulationState(
      zeroCredit: zeroCredit ?? this.zeroCredit,
      originalEmail: originalEmail ?? this.originalEmail,
      reason: reason ?? this.reason,
      premiumRequired: premiumRequired ?? this.premiumRequired,
    );
  }
}

class AntiManipulationNotifier extends StateNotifier<AntiManipulationState> {
  AntiManipulationNotifier() : super(const AntiManipulationState());

  void setFromServerResult(Map<String, dynamic> result) {
    final zero = result['zero_credit'] == true;
    final email = result['original_user_email'] as String?;
    final reason = result['reason'] as String?;
    final premiumReq = result['premium_required'] == true;
    state = AntiManipulationState(
      zeroCredit: zero,
      originalEmail: email,
      reason: reason,
      premiumRequired: premiumReq,
    );
  }

  void clear() {
    state = const AntiManipulationState();
  }
}

final antiManipulationProvider =
    StateNotifierProvider<AntiManipulationNotifier, AntiManipulationState>(
  (ref) => AntiManipulationNotifier(),
);


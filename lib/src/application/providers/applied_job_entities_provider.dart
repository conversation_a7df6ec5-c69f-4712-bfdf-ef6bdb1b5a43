import 'dart:async';
import 'dart:io';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/application/providers/applied_jobs_provider.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/utils/logging.dart';

// State Notifier to manage the state of applied job entities
class AppliedJobEntitiesNotifier
    extends StateNotifier<AsyncValue<List<JobEntity>>> {
  final Ref _ref;
  List<JobEntity> _cache = [];
  List<String> _lastFetchedIds = [];
  ProviderSubscription<AsyncValue<List<String>>>? _subscription;

  AppliedJobEntitiesNotifier(this._ref) : super(const AsyncValue.loading()) {
    _subscription = _ref.listen<AsyncValue<List<String>>>(appliedJobsProvider, (
      previous,
      next,
    ) {
      next.when(
        data: (ids) => _fetchJobsIfNeeded(ids),
        loading: () => state = const AsyncValue.loading(),
        error: (err, stack) => state = AsyncValue.error(err, stack),
      );
    }, fireImmediately: true);
  }

  // Prüft ob ein Fetch nötig ist und führt ihn nur bei Änderungen durch
  Future<void> _fetchJobsIfNeeded(List<String> ids) async {
    final log = getLogger('AppliedJobEntitiesNotifier');

    // Vergleiche mit letzten IDs um doppelte Aufrufe zu vermeiden
    if (_listEquals(ids, _lastFetchedIds)) {
      log.d('IDs unverändert, überspringe doppelten Fetch');
      return;
    }

    _lastFetchedIds = List.from(ids);
    await _fetchJobs(ids);
  }

  // Hilfsmethode zum Vergleichen von Listen
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  Future<void> _fetchJobs(List<String> ids) async {
    final log = getLogger('AppliedJobEntitiesNotifier');
    if (ids.isEmpty) {
      log.i('No applied jobs, clearing cache and state.');
      _cache = [];
      state = const AsyncValue.data([]);
      return;
    }

    // Return cache immediately to avoid UI flicker
    if (_cache.isNotEmpty) {
      state = AsyncValue.data(_cache);
    } else {
      state = const AsyncValue.loading();
    }

    try {
      log.i('Fetching ${ids.length} applied jobs from Supabase.');
      final supabaseClient = _ref.read(supabaseClientProvider);
      final response = await supabaseClient
          .from('jobs')
          .select()
          .filter('id', 'in', ids);

      // Jobs in der gleichen Reihenfolge wie die IDs sortieren
      final jobsMap = <String, JobEntity>{};
      for (final item in response) {
        final job = JobEntity.fromJson(item);
        jobsMap[job.id] = job;
      }

      // Jobs in der Reihenfolge der IDs (neueste Bewerbungen zuerst)
      final jobs =
          ids
              .map((id) => jobsMap[id])
              .where((job) => job != null)
              .cast<JobEntity>()
              .toList();
      _cache = jobs;
      log.i('Successfully fetched ${jobs.length} jobs.');
      log.i(
        'Erste 3 Jobs in Reihenfolge: ${jobs.take(3).map((j) => j.id).toList()}',
      );
      state = AsyncValue.data(jobs);
    } catch (e, stack) {
      log.e('Error fetching applied jobs', error: e, stackTrace: stack);

      // Benutzerfreundliche Fehlermeldungen für Netzwerkfehler
      String userFriendlyMessage;
      if (e is SocketException) {
        userFriendlyMessage =
            'Keine Internetverbindung verfügbar. Bitte überprüfen Sie Ihre Netzwerkverbindung.';
      } else if (e.toString().toLowerCase().contains('failed host lookup')) {
        userFriendlyMessage =
            'Server nicht erreichbar. Bitte versuchen Sie es später erneut.';
      } else if (e.toString().toLowerCase().contains('timeout')) {
        userFriendlyMessage =
            'Zeitüberschreitung beim Laden der Daten. Bitte versuchen Sie es erneut.';
      } else {
        userFriendlyMessage =
            'Fehler beim Laden der beworbenen Jobs. Bitte versuchen Sie es später erneut.';
      }

      state = AsyncValue.error(userFriendlyMessage, stack);
    }
  }

  @override
  void dispose() {
    _subscription?.close();
    super.dispose();
  }
}

// The new provider
final appliedJobEntitiesProvider = StateNotifierProvider<
  AppliedJobEntitiesNotifier,
  AsyncValue<List<JobEntity>>
>((ref) {
  return AppliedJobEntitiesNotifier(ref);
});

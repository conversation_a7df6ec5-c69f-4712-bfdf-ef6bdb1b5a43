import 'dart:async';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/utils/logging.dart';

import '../../domain/entities/job_entity.dart';
import '../../domain/entities/extracted_job_text.dart'; // NEU: Für Text-Extraktion
import '../services/text_extraction_service.dart'; // NEU: Text-Extraktion Service
import 'services_providers.dart';
// textExtractionServiceProvider ist in services_providers.dart verfügbar

/// Verwaltet den Zustand der Jobs, auf die sich der Benutzer beworben hat.
class AppliedJobsNotifier extends StateNotifier<AsyncValue<List<String>>> {
  final SupabaseClient _supabaseClient;
  final TextExtractionService _textExtractionService; // NEU: Text-Extraktion
  StreamSubscription<AuthState>? _authStateSubscription;
  Timer? _appliedJobsSubscription;
  User? _currentUser;
  final log = getLogger('AppliedJobsNotifier');

  // Konstante für die Supabase-Tabelle und Spalten
  static const String _supabaseTable = 'applied_jobs';
  static const String _jobIdColumn = 'job_id';
  static const String _userIdColumn = 'user_id';

  AppliedJobsNotifier(this._supabaseClient, this._textExtractionService)
    : super(const AsyncValue.loading()) {
    _initializeAuthListener();
  }

  // Initialisiert den Auth-Listener
  void _initializeAuthListener() {
    log.i("Initialisiere Auth-Listener für beworbene Jobs");
    _authStateSubscription = _supabaseClient.auth.onAuthStateChange.listen((
      event,
    ) {
      final authUser = event.session?.user;

      if (authUser != null) {
        // Verhindere doppelte Initialisierung für denselben User
        if (_currentUser?.id != authUser.id) {
          log.i("Benutzer angemeldet: ${authUser.id}");
          _currentUser = authUser;
          _loadAppliedJobs(authUser.id);
          _listenToAppliedJobsChanges(authUser.id);
        } else {
          log.d(
            "User ${authUser.id} bereits initialisiert, überspringe doppelte Initialisierung",
          );
        }
      } else {
        log.i("Benutzer abgemeldet");
        _currentUser = null;
        _appliedJobsSubscription?.cancel();
        state = const AsyncValue.data([]);
      }
    });

    // Prüfe den aktuellen Anmeldestatus
    final currentUser = _supabaseClient.auth.currentUser;
    if (currentUser != null) {
      log.i("Benutzer bereits angemeldet: ${currentUser.id}");
      _currentUser = currentUser;
      _loadAppliedJobs(currentUser.id);
      _listenToAppliedJobsChanges(currentUser.id);
    } else {
      log.i("Kein Benutzer angemeldet");
      state = const AsyncValue.data([]);
    }
  }

  // Lädt die beworbenen Jobs aus Supabase
  Future<void> _loadAppliedJobs(String userId) async {
    // Speichere den aktuellen Zustand, um ihn bei Bedarf wiederherzustellen
    final currentState = state;

    // Setze den Zustand nur auf loading, wenn wir noch keine Daten haben
    if (currentState is! AsyncData<List<String>>) {
      state = const AsyncValue.loading();
    }

    try {
      final response = await _supabaseClient
          .from(_supabaseTable)
          .select(_jobIdColumn)
          .eq(_userIdColumn, userId)
          .order('created_at', ascending: false); // Neueste zuerst

      final List<String> jobIds =
          (response as List<dynamic>)
              .map((item) => item[_jobIdColumn].toString())
              .toList();

      log.i("Beworbene Jobs geladen: ${jobIds.length}");
      log.i("Erste 5 Job-IDs in Reihenfolge: ${jobIds.take(5).toList()}");

      // Prüfe, ob sich die IDs geändert haben
      final hasChanged = currentState.maybeWhen(
        data: (currentIds) {
          // Prüfe, ob die Listen identisch sind
          if (currentIds.length != jobIds.length) return true;
          return !currentIds.every((id) => jobIds.contains(id));
        },
        orElse: () => true, // Bei loading oder error immer aktualisieren
      );

      // Aktualisiere den Zustand nur, wenn sich etwas geändert hat
      if (hasChanged) {
        log.i("Aktualisiere Zustand mit ${jobIds.length} beworbenen Jobs");
        state = AsyncData(jobIds);
      } else {
        log.i("Keine Änderung bei beworbenen Jobs, behalte aktuellen Zustand");
      }
    } catch (e) {
      log.e("Fehler beim Laden der beworbenen Jobs: $e");

      // Bei einem Fehler nur den Zustand aktualisieren, wenn wir keine Daten haben
      if (currentState is! AsyncData<List<String>>) {
        // Benutzerfreundliche Fehlermeldung für Netzwerkfehler
        String userFriendlyMessage;
        if (e.toString().contains('SocketException') ||
            e.toString().contains('Failed host lookup')) {
          userFriendlyMessage =
              'Keine Internetverbindung verfügbar. Bitte überprüfen Sie Ihre Netzwerkeinstellungen.';
        } else if (e.toString().contains('TimeoutException')) {
          userFriendlyMessage =
              'Verbindung zum Server dauert zu lange. Bitte versuchen Sie es später erneut.';
        } else {
          userFriendlyMessage =
              'Fehler beim Laden der beworbenen Jobs. Bitte versuchen Sie es später erneut.';
        }

        state = AsyncValue.error(userFriendlyMessage, StackTrace.current);
      } else {
        // Bei vorhandenen Daten den Fehler nur loggen, aber den Zustand beibehalten
        log.w(
          "Behalte vorherigen Zustand trotz Fehler, um UI-Stabilität zu gewährleisten",
        );
      }
    }
  }

  // Hört auf Änderungen in der Supabase-Tabelle für beworbene Jobs
  void _listenToAppliedJobsChanges(String userId) {
    // Stoppe vorherigen Timer falls vorhanden
    if (_appliedJobsSubscription != null) {
      log.d("Stoppe vorherigen Timer für beworbene Jobs");
      _appliedJobsSubscription?.cancel();
      _appliedJobsSubscription = null;
    }

    // PERFORMANCE FIX: Verwende längeres Intervall und Debouncing
    _appliedJobsSubscription = Timer.periodic(
      const Duration(
        minutes: 2, // Erhöht von 60s auf 2 Minuten für bessere Performance
      ),
      (_) {
        // Verwende Microtask um Main Thread nicht zu blockieren
        scheduleMicrotask(() async {
          // Prüfe, ob wir bereits Daten haben, bevor wir aktualisieren
          if (state is AsyncData<List<String>>) {
            log.i("Hintergrundaktualisierung der beworbenen Jobs");
            await _loadAppliedJobsInBackground(userId);
          } else {
            // Wenn wir noch keine Daten haben, normal laden
            await _loadAppliedJobs(userId);
          }
        });
      },
    );

    log.i("Timer für beworbene Jobs eingerichtet (2min Intervall)");
  }

  // Lädt die beworbenen Jobs im Hintergrund, ohne den UI-Zustand zu beeinflussen
  Future<void> _loadAppliedJobsInBackground(String userId) async {
    try {
      final response = await _supabaseClient
          .from(_supabaseTable)
          .select(_jobIdColumn)
          .eq(_userIdColumn, userId)
          .order('created_at', ascending: false); // Neueste zuerst

      final List<String> jobIds =
          (response as List<dynamic>)
              .map((item) => item[_jobIdColumn].toString())
              .toList();

      log.i("Beworbene Jobs im Hintergrund geladen: ${jobIds.length}");

      // Prüfe, ob sich die IDs geändert haben
      final hasChanged = state.maybeWhen(
        data: (currentIds) {
          // Prüfe, ob die Listen identisch sind
          if (currentIds.length != jobIds.length) return true;
          return !currentIds.every((id) => jobIds.contains(id));
        },
        orElse: () => true, // Bei loading oder error immer aktualisieren
      );

      // Aktualisiere den Zustand nur, wenn sich etwas geändert hat
      if (hasChanged) {
        log.i(
          "Aktualisiere Zustand im Hintergrund mit ${jobIds.length} beworbenen Jobs",
        );
        state = AsyncData(jobIds);
      } else {
        log.i("Keine Änderung bei beworbenen Jobs im Hintergrund");
      }
    } catch (e) {
      // Bei einem Fehler im Hintergrund nur loggen, aber den Zustand nicht ändern
      log.e("Fehler beim Laden der beworbenen Jobs im Hintergrund: $e");
    }
  }

  // Markiert einen Job als beworben und speichert ihn in der jobs-Tabelle
  Future<void> markJobAsApplied(String jobId, {JobEntity? jobEntity}) async {
    if (_currentUser == null) {
      log.w("markJobAsApplied abgebrochen: Kein Supabase User.");
      return;
    }

    final userId = _currentUser!.id;

    try {
      // Prüfe, ob der Job bereits als beworben markiert ist
      final existingEntry =
          await _supabaseClient
              .from(_supabaseTable)
              .select()
              .eq(_userIdColumn, userId)
              .eq(_jobIdColumn, jobId)
              .maybeSingle();

      // Wenn der Eintrag bereits existiert, nichts tun
      if (existingEntry != null) {
        log.i("Job $jobId ist bereits als beworben markiert für User $userId");
        return;
      }

      // NEU: Text-Extraktion im Hintergrund starten (non-blocking)
      if (jobEntity != null) {
        _extractJobTextInBackground(jobEntity, DateTime.now());
      }

      // Wenn ein JobEntity übergeben wurde, speichere es in der jobs-Tabelle
      if (jobEntity != null) {
        try {
          // Prüfe, ob der Job bereits in der jobs-Tabelle existiert
          final existingJob =
              await _supabaseClient
                  .from('jobs')
                  .select()
                  .eq('id', jobId)
                  .maybeSingle();

          // Wenn der Job noch nicht existiert, füge ihn hinzu
          if (existingJob == null) {
            // Konvertiere das JobEntity in ein JSON-Objekt
            final jobData = jobEntity.toJson();

            // Konvertiere publishedDate zu ISO-String für Supabase
            if (jobData['publishedDate'] != null) {
              // Wenn es bereits ein String ist, nichts tun
              if (jobData['publishedDate'] is! String) {
                // Wenn es ein Map ist (ehemaliger Timestamp)
                if (jobData['publishedDate'] is Map) {
                  final timestampMap = jobData['publishedDate'] as Map;
                  if (timestampMap.containsKey('seconds')) {
                    final seconds = timestampMap['seconds'] as int;
                    final nanoseconds =
                        timestampMap['nanoseconds'] as int? ?? 0;
                    final dateTime = DateTime.fromMillisecondsSinceEpoch(
                      seconds * 1000 + (nanoseconds ~/ 1000000),
                    );
                    jobData['publishedDate'] = dateTime.toIso8601String();
                  }
                } else if (jobData['publishedDate'] is DateTime) {
                  // Wenn es ein DateTime ist
                  jobData['publishedDate'] =
                      (jobData['publishedDate'] as DateTime).toIso8601String();
                }
              }
            }

            // Speichere den Job in der jobs-Tabelle
            await _supabaseClient.from('jobs').insert(jobData);
            log.i("Job $jobId in jobs-Tabelle gespeichert");
          }
        } catch (e) {
          log.e("Fehler beim Speichern des Jobs in der jobs-Tabelle: $e");
          // Fahre trotzdem fort, um den Job als beworben zu markieren
        }
      }

      // Füge einen neuen Eintrag hinzu
      await _supabaseClient.from(_supabaseTable).insert({
        _userIdColumn: userId,
        _jobIdColumn: jobId,
        'job_data':
            jobEntity?.toJson() ?? {'id': jobId, 'title': 'Unbekannter Job'},
        'application_date': DateTime.now().toIso8601String(),
      });

      log.i("Job $jobId als beworben markiert für User $userId");

      // Aktualisiere den State sofort, um eine bessere Benutzererfahrung zu bieten
      state.whenData((currentJobIds) {
        if (!currentJobIds.contains(jobId)) {
          final updatedJobIds = List<String>.from(currentJobIds)..add(jobId);
          state = AsyncValue.data(updatedJobIds);

          // Lade die Jobs im Hintergrund neu, ohne die UI zu beeinflussen
          _loadAppliedJobsInBackground(userId);
        }
      });
    } catch (e) {
      log.e("Fehler beim Markieren des Jobs als beworben: $e");

      // Benutzerfreundliche Fehlermeldung für Netzwerkfehler
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Failed host lookup')) {
        // Zeige benutzerfreundliche Nachricht (könnte über einen Snackbar angezeigt werden)
        log.w(
          'Benutzerfreundlicher Fehler: Keine Internetverbindung verfügbar.',
        );
      } else if (e.toString().contains('TimeoutException')) {
        log.w(
          'Benutzerfreundlicher Fehler: Verbindung zum Server dauert zu lange.',
        );
      }
    }
  }

  // Entfernt die Markierung eines Jobs als beworben
  Future<void> unmarkJobAsApplied(String jobId) async {
    if (_currentUser == null) {
      log.w("unmarkJobAsApplied abgebrochen: Kein Supabase User.");
      return;
    }

    final userId = _currentUser!.id;

    try {
      // Lösche den Eintrag
      await _supabaseClient
          .from(_supabaseTable)
          .delete()
          .eq(_userIdColumn, userId)
          .eq(_jobIdColumn, jobId);

      log.i("Bewerbungsmarkierung für Job $jobId entfernt für User $userId");

      // Aktualisiere den State manuell, falls der Realtime-Listener nicht schnell genug ist
      state.whenData((currentJobIds) {
        if (currentJobIds.contains(jobId)) {
          final updatedJobIds = List<String>.from(currentJobIds)..remove(jobId);
          state = AsyncValue.data(updatedJobIds);

          // Lade die Jobs im Hintergrund neu, ohne die UI zu beeinflussen
          _loadAppliedJobsInBackground(userId);
        }
      });
    } catch (e) {
      log.e("Fehler beim Entfernen der Bewerbungsmarkierung: $e");

      // Benutzerfreundliche Fehlermeldung für Netzwerkfehler
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Failed host lookup')) {
        log.w(
          'Benutzerfreundlicher Fehler: Keine Internetverbindung verfügbar.',
        );
      } else if (e.toString().contains('TimeoutException')) {
        log.w(
          'Benutzerfreundlicher Fehler: Verbindung zum Server dauert zu lange.',
        );
      }
    }
  }

  // Stellt einen gelöschten beworbenen Job wieder her
  Future<void> restoreJobAsApplied(JobEntity job) async {
    // Wir verwenden die bestehende markJobAsApplied-Methode, um Code-Duplikation zu vermeiden
    await markJobAsApplied(job.id, jobEntity: job);
    log.i("Job ${job.id} wurde als beworben wiederhergestellt");
  }

  // Prüft, ob ein Job als beworben markiert ist
  bool isJobApplied(String jobId) {
    return state.maybeWhen(
      data: (appliedJobIds) => appliedJobIds.contains(jobId),
      orElse: () => false,
    );
  }

  /// NEU: Extrahiert Job-Text im Hintergrund (non-blocking)
  void _extractJobTextInBackground(JobEntity job, DateTime appliedAt) {
    // Starte Text-Extraktion asynchron ohne auf das Ergebnis zu warten
    _textExtractionService
        .extractAndCacheJobText(
          job,
          sourceType: 'applied_job',
          sourceTimestamp: appliedAt,
        )
        .then((extractedText) {
          if (extractedText != null) {
            log.i(
              "Text für beworbenen Job ${job.id} erfolgreich extrahiert und gecacht (${(extractedText).getSizeInKB().toStringAsFixed(1)}KB)",
            );
          } else {
            log.w(
              "Text-Extraktion für beworbenen Job ${job.id} fehlgeschlagen",
            );
          }
        })
        .catchError((error) {
          log.e(
            "Fehler bei Text-Extraktion für beworbenen Job ${job.id}: $error",
          );
        });
  }

  /// NEU: Lädt extrahierten Text für einen Job (falls verfügbar)
  Future<ExtractedJobText?> getExtractedText(String jobId) async {
    try {
      return await _textExtractionService.extractAndCacheJobText(
        // Erstelle ein minimales JobEntity nur für Cache-Lookup
        JobEntity(
          id: jobId,
          title: '',
          companyName: '',
          location: '',
          publishedDate: DateTime.now(),
          description: '',
        ),
      );
    } catch (e) {
      log.e(
        "Fehler beim Laden des extrahierten Texts für beworbenen Job $jobId: $e",
      );
      return null;
    }
  }

  /// Öffentliche Methode zum manuellen Neuladen der beworbenen Jobs
  Future<void> loadAppliedJobs() async {
    if (_currentUser != null) {
      await _loadAppliedJobs(_currentUser!.id);
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _appliedJobsSubscription?.cancel();
    _textExtractionService.dispose(); // NEU: Service cleanup
    log.i("AppliedJobsNotifier disposed");
    super.dispose();
  }
}

/// Der Provider, der die Instanz von AppliedJobsNotifier bereitstellt
final appliedJobsProvider =
    StateNotifierProvider<AppliedJobsNotifier, AsyncValue<List<String>>>((ref) {
      final supabaseClient = ref.watch(supabaseClientProvider);
      final textExtractionService = ref.watch(textExtractionServiceProvider);
      return AppliedJobsNotifier(supabaseClient, textExtractionService);
    });

import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../domain/models/user_profile.dart';
import '../../domain/models/cv_template.dart';
import '../services/pdf_cv_generator_service.dart';
import 'user_profile_provider.dart';
import 'cv_template_provider.dart';

/// Provider für PDF-CV-Generator-Service
final pdfCvGeneratorServiceProvider = Provider<PdfCvGeneratorService>((ref) {
  return PdfCvGeneratorService();
});

/// Provider für PDF-Generierung
final pdfGenerationProvider =
    StateNotifierProvider<PdfGenerationNotifier, AsyncValue<Uint8List?>>((ref) {
      final service = ref.read(pdfCvGeneratorServiceProvider);
      return PdfGenerationNotifier(service, ref);
    });

/// Provider für automatische PDF-Synchronisation
final pdfAutoSyncProvider = Provider<PdfAutoSyncNotifier>((ref) {
  return PdfAutoSyncNotifier(ref);
});

/// Notifier für automatische PDF-Synchronisation
class PdfAutoSyncNotifier {
  static final Logger _log = Logger();
  final Ref _ref;
  bool _isAutoSyncEnabled = false;
  DateTime? _lastProfileUpdate;
  DateTime? lastPdfGeneration;

  PdfAutoSyncNotifier(this._ref) {
    _setupProfileListener();
  }

  /// Aktiviert/Deaktiviert Auto-Sync
  void setAutoSyncEnabled(bool enabled) {
    _isAutoSyncEnabled = enabled;
    _log.i('🔄 PDF-Auto-Sync: ${enabled ? 'Aktiviert' : 'Deaktiviert'}');

    if (enabled) {
      _checkAndSyncIfNeeded();
    }
  }

  /// Prüft ob Auto-Sync aktiviert ist
  bool get isAutoSyncEnabled => _isAutoSyncEnabled;

  /// Setup Profile-Listener für automatische Synchronisation
  void _setupProfileListener() {
    _ref.listen<AsyncValue<UserProfile?>>(userProfileProvider, (
      previous,
      next,
    ) {
      if (_isAutoSyncEnabled && next.hasValue && next.value != null) {
        _lastProfileUpdate = DateTime.now();
        _log.i('🔄 PDF-Auto-Sync: Profildaten-Änderung erkannt');

        // Verzögerte Synchronisation um mehrere schnelle Änderungen zu vermeiden
        Future.delayed(const Duration(seconds: 2), () {
          _checkAndSyncIfNeeded();
        });
      }
    });
  }

  /// Prüft ob Synchronisation nötig ist und führt sie aus
  Future<void> _checkAndSyncIfNeeded() async {
    if (!_isAutoSyncEnabled) return;

    final selectedTemplate = _ref.read(selectedTemplateProvider);
    if (selectedTemplate == null) {
      _log.i('🔄 PDF-Auto-Sync: Kein Template ausgewählt, überspringe Sync');
      return;
    }

    // Prüfe ob Profildaten neuer sind als letzte PDF-Generierung
    if (_lastProfileUpdate != null &&
        (lastPdfGeneration == null ||
            _lastProfileUpdate!.isAfter(lastPdfGeneration!))) {
      _log.i('🔄 PDF-Auto-Sync: Starte automatische PDF-Regenerierung');

      try {
        await _ref.read(pdfGenerationProvider.notifier).generatePdf();
        lastPdfGeneration = DateTime.now();
        _log.i('✅ PDF-Auto-Sync: Automatische PDF-Regenerierung erfolgreich');
      } catch (e) {
        _log.e(
          '❌ PDF-Auto-Sync: Fehler bei automatischer PDF-Regenerierung: $e',
        );
      }
    }
  }

  /// Manueller Sync-Trigger
  Future<void> forceSyncNow() async {
    _log.i('🔄 PDF-Auto-Sync: Manueller Sync gestartet');
    await _checkAndSyncIfNeeded();
  }
}

/// Notifier für PDF-Generierung
class PdfGenerationNotifier extends StateNotifier<AsyncValue<Uint8List?>> {
  static final Logger _log = Logger();
  final PdfCvGeneratorService _service;
  final Ref _ref;

  PdfGenerationNotifier(this._service, this._ref)
    : super(const AsyncValue.data(null));

  /// Generiert PDF mit aktuellen Profildaten und ausgewähltem Template
  Future<void> generatePdf() async {
    try {
      state = const AsyncValue.loading();
      _log.i('🔥 PDF-Generator-Provider: Starte PDF-Generierung');

      // Hole aktuelle Profildaten
      final userProfile = _ref.read(userProfileProvider);
      final profile = await userProfile.when(
        data: (profile) async => profile,
        loading: () => throw Exception('Profildaten werden noch geladen'),
        error:
            (error, stack) =>
                throw Exception('Fehler beim Laden der Profildaten: $error'),
      );

      // Hole ausgewähltes Template
      final selectedTemplate = _ref.read(selectedTemplateProvider);
      if (selectedTemplate == null) {
        throw Exception('Kein Template ausgewählt');
      }

      _log.i(
        '🔥 PDF-Generator-Provider: Generiere PDF mit Template ${selectedTemplate.name}',
      );

      // Generiere PDF
      final pdfBytes = await _service.generateCvPdf(profile, selectedTemplate);

      state = AsyncValue.data(pdfBytes);
      _log.i('✅ PDF-Generator-Provider: PDF erfolgreich generiert');

      // Benachrichtige Auto-Sync über erfolgreiche Generierung
      try {
        final autoSync = _ref.read(pdfAutoSyncProvider);
        autoSync.lastPdfGeneration = DateTime.now();
      } catch (e) {
        // Ignoriere Fehler beim Auto-Sync-Update
      }
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Generator-Provider: Fehler bei PDF-Generierung',
        error: e,
        stackTrace: stackTrace,
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Generiert PDF mit spezifischem Template
  Future<void> generatePdfWithTemplate(CvTemplate template) async {
    try {
      state = const AsyncValue.loading();
      _log.i(
        '🔥 PDF-Generator-Provider: Starte PDF-Generierung mit Template ${template.name}',
      );

      // Hole aktuelle Profildaten
      final userProfile = _ref.read(userProfileProvider);
      final profile = await userProfile.when(
        data: (profile) async => profile,
        loading: () => throw Exception('Profildaten werden noch geladen'),
        error:
            (error, stack) =>
                throw Exception('Fehler beim Laden der Profildaten: $error'),
      );

      // Generiere PDF
      final pdfBytes = await _service.generateCvPdf(profile, template);

      state = AsyncValue.data(pdfBytes);
      _log.i('✅ PDF-Generator-Provider: PDF erfolgreich generiert');
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Generator-Provider: Fehler bei PDF-Generierung',
        error: e,
        stackTrace: stackTrace,
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Speichert PDF auf dem Gerät
  Future<String?> savePdfToDevice(String fileName) async {
    try {
      final currentState = state;
      if (currentState is AsyncData<Uint8List?> && currentState.value != null) {
        _log.i('🔥 PDF-Generator-Provider: Speichere PDF als $fileName');

        final filePath = await _service.savePdfToDevice(
          currentState.value!,
          fileName,
        );
        _log.i('✅ PDF-Generator-Provider: PDF gespeichert unter $filePath');

        return filePath;
      } else {
        throw Exception('Keine PDF-Daten zum Speichern verfügbar');
      }
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Generator-Provider: Fehler beim Speichern der PDF',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Speichert PDF lokal
  Future<String> savePdfLocally(String fileName) async {
    try {
      final currentState = state;
      if (currentState is AsyncData<Uint8List?> && currentState.value != null) {
        _log.i('🔥 PDF-Generator-Provider: Speichere PDF lokal $fileName');

        final filePath = await _service.savePdfLocally(
          currentState.value!,
          fileName,
        );
        _log.i('✅ PDF-Generator-Provider: PDF lokal gespeichert: $filePath');
        return filePath;
      } else {
        throw Exception('Keine PDF-Daten zum Speichern verfügbar');
      }
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Generator-Provider: Fehler beim lokalen Speichern',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Teilt PDF
  Future<void> sharePdf(String fileName) async {
    try {
      final currentState = state;
      if (currentState is AsyncData<Uint8List?> && currentState.value != null) {
        _log.i('🔥 PDF-Generator-Provider: Teile PDF $fileName');

        await _service.sharePdf(currentState.value!, fileName);
        _log.i('✅ PDF-Generator-Provider: PDF erfolgreich geteilt');
      } else {
        throw Exception('Keine PDF-Daten zum Teilen verfügbar');
      }
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Generator-Provider: Fehler beim Teilen der PDF',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Setzt Status zurück
  void reset() {
    state = const AsyncValue.data(null);
    _log.i('🔥 PDF-Generator-Provider: Status zurückgesetzt');
  }

  /// Prüft ob PDF verfügbar ist
  bool get hasPdf {
    final currentState = state;
    return currentState is AsyncData<Uint8List?> && currentState.value != null;
  }

  /// Gibt PDF-Bytes zurück
  Uint8List? get pdfBytes {
    final currentState = state;
    if (currentState is AsyncData<Uint8List?>) {
      return currentState.value;
    }
    return null;
  }
}

/// Provider für PDF-Vorschau-Generierung
final pdfPreviewProvider = StateNotifierProvider.family<
  PdfPreviewNotifier,
  AsyncValue<Uint8List?>,
  CvTemplate
>((ref, template) {
  final service = ref.read(pdfCvGeneratorServiceProvider);
  return PdfPreviewNotifier(service, ref, template);
});

/// Notifier für PDF-Vorschau
class PdfPreviewNotifier extends StateNotifier<AsyncValue<Uint8List?>> {
  static final Logger _log = Logger();
  final PdfCvGeneratorService _service;
  final Ref _ref;
  final CvTemplate _template;

  PdfPreviewNotifier(this._service, this._ref, this._template)
    : super(const AsyncValue.data(null));

  /// Generiert PDF-Vorschau
  Future<void> generatePreview() async {
    try {
      state = const AsyncValue.loading();
      _log.i(
        '🔥 PDF-Vorschau-Provider: Generiere Vorschau für Template ${_template.name}',
      );

      // Hole aktuelle Profildaten
      final userProfile = _ref.read(userProfileProvider);
      final profile = await userProfile.when(
        data: (profile) async => profile,
        loading: () => throw Exception('Profildaten werden noch geladen'),
        error:
            (error, stack) =>
                throw Exception('Fehler beim Laden der Profildaten: $error'),
      );

      // Generiere PDF-Vorschau
      final pdfBytes = await _service.generateCvPdf(profile, _template);

      state = AsyncValue.data(pdfBytes);
      _log.i('✅ PDF-Vorschau-Provider: Vorschau erfolgreich generiert');
    } catch (e, stackTrace) {
      _log.e(
        '❌ PDF-Vorschau-Provider: Fehler bei Vorschau-Generierung',
        error: e,
        stackTrace: stackTrace,
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// Provider für automatische PDF-Aktualisierung bei Profil-Änderungen
final autoUpdatePdfProvider = Provider<void>((ref) {
  // Überwache Profil-Änderungen
  ref.listen(userProfileProvider, (previous, next) {
    // Wenn sich das Profil ändert und eine PDF generiert wurde, aktualisiere sie
    final pdfNotifier = ref.read(pdfGenerationProvider.notifier);
    if (pdfNotifier.hasPdf) {
      _log.i('🔥 Auto-Update: Profil geändert, aktualisiere PDF');
      pdfNotifier.generatePdf();
    }
  });

  // Überwache Template-Änderungen
  ref.listen(selectedTemplateProvider, (previous, next) {
    if (previous != next && next != null) {
      // Wenn sich das Template ändert, aktualisiere die PDF
      final pdfNotifier = ref.read(pdfGenerationProvider.notifier);
      if (pdfNotifier.hasPdf) {
        _log.i('🔥 Auto-Update: Template geändert, aktualisiere PDF');
        pdfNotifier.generatePdf();
      }
    }
  });
});

/// Provider für PDF-Dateinamen-Generierung
final pdfFileNameProvider = Provider<String>((ref) {
  final userProfile = ref.watch(userProfileProvider);
  final selectedTemplate = ref.watch(selectedTemplateProvider);

  final userName = userProfile.when(
    data: (profile) => profile.name?.replaceAll(' ', '_') ?? 'Lebenslauf',
    loading: () => 'Lebenslauf',
    error: (_, __) => 'Lebenslauf',
  );

  final templateName =
      selectedTemplate?.name.replaceAll(' ', '_') ?? 'Standard';
  final timestamp = DateTime.now().millisecondsSinceEpoch;

  return '${userName}_CV_${templateName}_$timestamp.pdf';
});

/// Logger für PDF-Generator
final Logger _log = Logger();

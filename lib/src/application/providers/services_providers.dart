import 'dart:async';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/infrastructure/services/payment_service.dart';
import 'package:ki_test/src/infrastructure/services/ad_service.dart';
import 'package:ki_test/src/core/network/supabase_retry_client.dart';
import 'package:ki_test/src/infrastructure/services/network_resilience_service.dart';
import 'package:ki_test/src/infrastructure/services/network_status_service.dart';
import 'user_profile_provider.dart'; // Import für UserProfileNotifier
import 'package:ki_test/src/infrastructure/services/supabase_service.dart';
import 'package:ki_test/src/application/services/email_service.dart'; // Import für EmailService
import 'package:ki_test/src/application/services/gmail_service.dart'; // Import für GmailService
import 'package:ki_test/src/application/services/translation_service.dart';
import 'package:ki_test/src/infrastructure/services/in_app_auth_service.dart'; // Import für InAppAuthService
import 'package:ki_test/src/infrastructure/services/application_counter_service.dart'; // Import für ApplicationCounterService
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart'; // Import für SubscriptionManagementService
import 'package:ki_test/src/infrastructure/services/premium_feature_manager.dart'; // Import für PremiumFeatureManager
import 'package:ki_test/src/infrastructure/api/agentur_arbeit_api_client.dart'; // Import für AgenturArbeitApiClient
import 'package:ki_test/src/infrastructure/api/ausbildung_arbeit_api_client.dart'; // Import für AusbildungArbeitApiClient
import 'package:ki_test/src/infrastructure/api/job_api_client.dart'; // Import für JobApiClient
import 'package:ki_test/src/application/services/additional_documents_service.dart'; // Import für AdditionalDocumentsService
import 'package:ki_test/src/application/services/text_extraction_service.dart'; // Import für TextExtractionService
import 'package:ki_test/src/application/services/job_text_cache_manager.dart'; // Import für JobTextCacheManager
import 'package:ki_test/src/infrastructure/services/web_scraping_service.dart'; // Import für WebScrapingService

import 'package:ki_test/src/domain/models/subscription_model.dart'; // SubscriptionModel

/// Provider für den AgenturArbeitApiClient (Jobsuche)
final agenturArbeitApiClientProvider = Provider<JobApiClient>((ref) {
  return AgenturArbeitApiClient();
});

/// Provider für den AusbildungArbeitApiClient (Ausbildungssuche)
final ausbildungArbeitApiClientProvider = Provider<JobApiClient>((ref) {
  return AusbildungArbeitApiClient();
});

/// Provider für den PaymentService.
///
/// Dieser Provider stellt sicher, dass der PaymentService mit dem notwendigen
/// UserProfileNotifier und SupabaseSubscriptionService initialisiert wird.
final paymentServiceProvider = Provider<PaymentService>((ref) {
  final userProfileNotifier = ref.watch(userProfileProvider.notifier);

  // Korrigiere den Konstruktoraufruf: nur die benötigten Parameter übergeben
  final service = PaymentService(InAppPurchase.instance, userProfileNotifier);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider für den AdService.
final adServiceProvider = Provider<AdService>((ref) {
  final adService = AdService();

  ref.onDispose(() {
    adService.dispose();
  });

  return adService;
});

/// Provider für den SupabaseService
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return SupabaseService(client);
});

/// NEU: Provider für den Supabase Client
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

/// Provider für den TranslationService
final translationServiceProvider = Provider<TranslationService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return TranslationService(client);
});

/// Provider für den InAppAuthService
final inAppAuthServiceProvider = Provider<InAppAuthService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return InAppAuthService(client);
});

/// Provider für den Premium-Status des Benutzers
final isPremiumProvider = Provider<bool>((ref) {
  // Lese den Premium-Status aus dem UserProfile
  final userProfileState = ref.watch(userProfileProvider);

  // Wenn das UserProfile geladen ist und der Benutzer Premium ist, gib true zurück
  return userProfileState.maybeWhen(
    data: (userProfile) {
      // Prüfe, ob der Benutzer Premium ist und ob das Ablaufdatum in der Zukunft liegt
      if (userProfile.isPremium ?? false) {
        // Wenn kein Ablaufdatum gesetzt ist oder das Ablaufdatum in der Zukunft liegt
        if (userProfile.premiumExpiryDate == null ||
            userProfile.premiumExpiryDate!.isAfter(DateTime.now())) {
          return true;
        }
      }
      return false;
    },
    // Standardmäßig nicht Premium
    orElse: () => false,
  );
});

/// Provider für den Premium-Override-Status
/// Dieser Provider wird in PremiumFeature-Widgets verwendet, um zu entscheiden,
/// ob Premium-Features angezeigt werden sollen.
/// Standardmäßig gibt er den Wert von isPremiumProvider zurück.
final premiumOverrideProvider = Provider<bool>((ref) {
  // Verwende den isPremiumProvider, um den tatsächlichen Premium-Status zu erhalten
  return ref.watch(isPremiumProvider);
});

// Beispiel: Provider für SupabaseSubscriptionService (falls benötigt)
// Dieser Provider muss *vor* dem paymentServiceProvider definiert sein, wenn er direkt gelesen wird.
/*
final supabaseSubscriptionServiceProvider = Provider<SupabaseSubscriptionService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider); // Oder supabaseServiceProvider
  // Stelle sicher, dass SupabaseSubscriptionService existiert und korrekt importiert wird
  return SupabaseSubscriptionService(supabaseClient);
});
*/

/// Provider für den ApplicationCounterService
final applicationCounterServiceProvider = Provider<ApplicationCounterService>((
  ref,
) {
  final client = ref.watch(supabaseClientProvider);
  return ApplicationCounterService(client, ref);
});

/// Provider für den robusten Supabase Retry Client
final supabaseRetryClientProvider = Provider<SupabaseRetryClient>((ref) {
  return SupabaseRetryClient(Supabase.instance.client);
});

/// Provider für den Network Resilience Service
final networkResilienceServiceProvider = Provider<NetworkResilienceService>((
  ref,
) {
  final retryClient = ref.watch(supabaseRetryClientProvider);
  return NetworkResilienceService(retryClient);
});

/// Provider für den Network Status Service
final networkStatusServiceProvider = Provider<NetworkStatusService>((ref) {
  return NetworkStatusService();
});

/// Provider für den aktuellen Netzwerk-Status
final networkStatusProvider = StreamProvider<NetworkStatus>((ref) {
  final service = ref.watch(networkStatusServiceProvider);

  // Initialisiere den Service beim ersten Zugriff
  service.initialize();

  return service.statusStream;
});

/// Provider für den SubscriptionManagementService
final subscriptionManagementServiceProvider =
    Provider<SubscriptionManagementService>((ref) {
      final client = ref.watch(supabaseClientProvider);
      final service = SubscriptionManagementService(client, ref);
      // Realtime für subscriptions starten
      service.initializeRealtimeSubscriptions();
      ref.onDispose(() {
        service.dispose();
      });
      return service;
    });

/// Provider für die verbleibenden Bewerbungen
/// WICHTIG: Abhängig vom userProfileProvider, damit bei Plan-Änderungen automatisch neu geladen wird
final remainingApplicationsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);

  // KRITISCH: Abhängigkeit vom userProfileProvider hinzufügen
  // Dadurch wird der Provider automatisch neu ausgeführt wenn sich der Plan ändert
  final userProfile = ref.watch(userProfileProvider);

  // Warte auf das UserProfile, damit der Provider bei Plan-Änderungen neu lädt
  await userProfile.when(
    data: (profile) async => profile,
    loading: () async => null,
    error: (_, __) async => null,
  );

  /// Provider für die aktuelle Subscription vom Server (keine Profile/Fallbacks)
  final currentSubscriptionProvider = FutureProvider<SubscriptionModel?>((
    ref,
  ) async {
    final subscriptionService = ref.watch(
      subscriptionManagementServiceProvider,
    );
    return await subscriptionService.getCurrentSubscription();
  });

  return await subscriptionService.getRemainingApplications();
});

/// StateNotifier für Bewerbungszähler mit automatischer Plan-Synchronisation
class RemainingApplicationsNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final SubscriptionManagementService _subscriptionService;
  final Ref _ref;
  static final _log = Logger();
  Timer? _autoRefreshTimer;

  RemainingApplicationsNotifier(this._subscriptionService, this._ref)
    : super(const AsyncValue.loading()) {
    _log.i('🚀 RemainingApplicationsNotifier initialisiert');

    // Initialer Load
    _loadRemainingApplications();

    // KRITISCHER FIX: Auto-Refresh Timer für sofortige DB-Updates
    _startAutoRefreshTimer();

    // Überwache User-Profile-Änderungen
    _ref.listen(userProfileProvider, (previous, next) {
      _log.i(
        '👤 User-Profile-Änderung erkannt - FORCE REFRESH der Bewerbungsdaten',
      );
      _log.d(
        'Previous: ${previous?.value?.premiumPlanType}, Next: ${next.value?.premiumPlanType}',
      );

      // Nach Profilwechsel frische Daten laden
      _loadRemainingApplications();
    });
  }

  /// Startet den Auto-Refresh Timer
  void _startAutoRefreshTimer() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(
      const Duration(
        minutes: 2,
      ), // Alle 2 Minuten prüfen (Performance-Optimierung)
      (_) {
        _log.d('🔄 Auto-Refresh: Lade Bewerbungsdaten neu...');
        _loadRemainingApplications();
      },
    );
    _log.i('⏰ Auto-Refresh Timer gestartet (2min Intervall)');
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    _log.i('🛑 RemainingApplicationsNotifier disposed - Timer gestoppt');
    super.dispose();
  }

  /// Lädt die verbleibenden Bewerbungen neu
  Future<void> _loadRemainingApplications() async {
    try {
      _log.i('🔄 Lade Bewerbungsdaten neu...');
      state = const AsyncValue.loading();

      final data = await _subscriptionService.getRemainingApplications();
      _log.i('✅ Bewerbungsdaten geladen: $data');

      state = AsyncValue.data(data);
      _log.i('📊 StateNotifier State aktualisiert');
    } catch (error, stackTrace) {
      _log.e('❌ Fehler beim Laden der Bewerbungsdaten', error: error);
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Manueller Refresh der Bewerbungsdaten
  Future<void> refresh() async {
    await _loadRemainingApplications();
  }
}

/// Provider für den RemainingApplicationsNotifier
final remainingApplicationsNotifierProvider = StateNotifierProvider<
  RemainingApplicationsNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return RemainingApplicationsNotifier(subscriptionService, ref);
});

/// Provider für die aktuelle Subscription vom Server (keine Profile/Fallbacks)
final currentSubscriptionProvider = FutureProvider<SubscriptionModel?>((
  ref,
) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.getCurrentSubscription();
});

/// Convenience Provider für einfachen Zugriff auf die Bewerbungsdaten
final remainingApplicationsAutoRefreshProvider =
    Provider<AsyncValue<Map<String, dynamic>>>((ref) {
      return ref.watch(remainingApplicationsNotifierProvider);
    });

/// Provider für das nächste Reset-Datum für kostenlose Bewerbungen
final nextFreeResetDateProvider = FutureProvider<DateTime?>((ref) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.getNextFreeResetDate();
});

/// Provider für den Premium-Status (Future-basiert)
final hasPremiumSubscriptionProvider = FutureProvider<bool>((ref) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.hasPremiumSubscription();
});

/// Provider für den PremiumFeatureManager
final premiumFeatureManagerProvider = Provider<PremiumFeatureManager>((ref) {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  final adService = ref.watch(adServiceProvider);
  return PremiumFeatureManager(subscriptionService, adService, ref);
});

/// Provider für den GmailService
final gmailServiceProvider = Provider<GmailService>((ref) {
  return GmailService();
});

/// Provider für den EmailService
final emailServiceProvider = Provider<EmailService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  final gmailService = ref.watch(gmailServiceProvider);
  return EmailService(client, gmailService);
});

/// Provider für den AdditionalDocumentsService
final additionalDocumentsServiceProvider = Provider<AdditionalDocumentsService>(
  (ref) {
    return AdditionalDocumentsService();
  },
);

/// Provider für den TextExtractionService
final textExtractionServiceProvider = Provider<TextExtractionService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  final textExtractionService = TextExtractionService(client);

  ref.onDispose(() {
    textExtractionService.dispose();
  });

  return textExtractionService;
});

/// Provider für den JobTextCacheManager
final jobTextCacheManagerProvider = Provider<JobTextCacheManager>((ref) {
  final client = ref.watch(supabaseClientProvider);
  final textExtractionService = ref.watch(textExtractionServiceProvider);
  final jobTextCacheManager = JobTextCacheManager(
    client,
    textExtractionService,
  );

  ref.onDispose(() {
    jobTextCacheManager.dispose();
  });

  return jobTextCacheManager;
});

/// Provider für den WebScrapingService
final webScrapingServiceProvider = Provider<WebScrapingService>((ref) {
  return WebScrapingService();
});

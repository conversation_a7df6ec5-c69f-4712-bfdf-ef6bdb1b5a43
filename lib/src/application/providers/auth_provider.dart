import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/anti_manipulation_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/core/mixins/error_handling_mixin.dart';
import 'package:ki_test/src/core/utils/auth_error_handler.dart';
import 'package:ki_test/src/core/utils/device_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Provider für die Auth-Funktionalitäten
final authProvider =
    StateNotifierProvider<AuthStateNotifier, AsyncValue<AuthState>>(
      (ref) => AuthStateNotifier(ref),
    );

// Auth State Klasse
class AuthState {
  final User? user;
  final String? errorMessage;
  final bool isLoading;

  AuthState({this.user, this.errorMessage, this.isLoading = false});

  AuthState copyWith({User? user, String? errorMessage, bool? isLoading}) {
    return AuthState(
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// Auth State Notifier
class AuthStateNotifier extends StateNotifier<AsyncValue<AuthState>>
    with ErrorHandlingMixin {
  final Ref _ref;
  final _log = getLogger('AuthStateNotifier');

  @override
  String get componentName => 'AuthStateNotifier';

  AuthStateNotifier(this._ref) : super(const AsyncValue.loading()) {
    // Initialisierung mit dem aktuellen Benutzer
    final client = _ref.read(supabaseClientProvider);
    final currentUser = client.auth.currentUser;

    if (currentUser != null) {
      state = AsyncValue.data(AuthState(user: currentUser));
    } else {
      state = AsyncValue.data(AuthState());
    }

    // Höre auf Auth-Änderungen
    client.auth.onAuthStateChange.listen((event) {
      if (event.event == AuthChangeEvent.signedIn) {
        state = AsyncValue.data(AuthState(user: event.session?.user));
      } else if (event.event == AuthChangeEvent.signedOut) {
        state = AsyncValue.data(AuthState());
      } else if (event.event == AuthChangeEvent.passwordRecovery) {
        state = AsyncValue.data(AuthState(user: event.session?.user));
      }
    });
  }

  // Anmelden mit E-Mail und Passwort
  Future<void> signInWithEmailPassword(
    String email,
    String password, {
    BuildContext? context,
  }) async {
    state = AsyncValue.data(
      AuthState(isLoading: true, user: state.value?.user),
    );

    await safeApiOperation<void>(
      () async {
        final client = _ref.read(supabaseClientProvider);
        await client.auth.signInWithPassword(email: email, password: password);

        // 🔐 ANTI-MANIPULATION: Device-Registrierung nach erfolgreicher Anmeldung
        final currentUser = client.auth.currentUser;
        if (currentUser != null) {
          try {
            final deviceManager = DeviceManager();
            final registrationResult = await deviceManager
                .registerDeviceForUser(currentUser.id);

            if (registrationResult['success'] == true) {
              _log.i(
                '✅ ANTI-MANIPULATION: Device erfolgreich registriert bei Login',
              );

              // Anti‑Manipulation State aktualisieren
              try {
                final anti = _ref.read(antiManipulationProvider.notifier);
                anti.setFromServerResult(registrationResult);
              } catch (_) {}

              final warningMessage = registrationResult['warning_message'];
              if (warningMessage != null) {
                _log.w(
                  '⚠️ ANTI-MANIPULATION: Warnung bei Device-Registrierung: $warningMessage',
                );
              }
            } else {
              _log.w(
                '⚠️ ANTI-MANIPULATION: Device-Registrierung fehlgeschlagen: ${registrationResult['error']}',
              );
            }
          } catch (deviceError) {
            _log.e(
              '❌ ANTI-MANIPULATION: Fehler bei Device-Registrierung: $deviceError',
            );
            // Nicht kritisch - Login kann trotzdem fortgesetzt werden
          }
        }

        state = AsyncValue.data(AuthState(user: client.auth.currentUser));
      },
      context: context,
      errorMessage: 'Fehler bei der Anmeldung',
    ).catchError((e) {
      if (e is AuthException && context != null) {
        AuthErrorHandler.showErrorSnackBar(context, e);
      }
      state = AsyncValue.data(
        AuthState(errorMessage: AuthErrorHandler.getLocalizedErrorMessage(e)),
      );
    });
  }

  // Registrieren mit E-Mail und Passwort
  Future<void> signUpWithEmailPassword(
    String email,
    String password, {
    BuildContext? context,
  }) async {
    state = AsyncValue.data(
      AuthState(isLoading: true, user: state.value?.user),
    );

    await safeApiOperation<void>(
      () async {
        final client = _ref.read(supabaseClientProvider);
        await client.auth.signUp(email: email, password: password);
        state = AsyncValue.data(AuthState(user: client.auth.currentUser));
      },
      context: context,
      errorMessage: 'Fehler bei der Registrierung',
    ).catchError((e) {
      if (e is AuthException && context != null) {
        AuthErrorHandler.showErrorSnackBar(context, e);
      }
      state = AsyncValue.data(
        AuthState(errorMessage: AuthErrorHandler.getLocalizedErrorMessage(e)),
      );
    });
  }

  // Passwort zurücksetzen
  Future<void> sendPasswordResetEmail(
    String email, {
    BuildContext? context,
  }) async {
    state = AsyncValue.data(
      AuthState(isLoading: true, user: state.value?.user),
    );

    await safeApiOperation<void>(
      () async {
        final client = _ref.read(supabaseClientProvider);
        await client.auth.resetPasswordForEmail(email);
        state = AsyncValue.data(AuthState(user: state.value?.user));
      },
      context: context,
      errorMessage: 'Fehler beim Senden der Passwort-Reset-E-Mail',
    ).catchError((e) {
      state = AsyncValue.data(
        AuthState(errorMessage: AuthErrorHandler.getLocalizedErrorMessage(e)),
      );
    });
  }

  // Passwort ändern
  Future<void> updatePassword(String newPassword) async {
    try {
      state = AsyncValue.data(
        AuthState(isLoading: true, user: state.value?.user),
      );
      final client = _ref.read(supabaseClientProvider);
      await client.auth.updateUser(UserAttributes(password: newPassword));
      state = AsyncValue.data(AuthState(user: client.auth.currentUser));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      state = AsyncValue.data(AuthState(errorMessage: e.toString()));
    }
  }

  // Abmelden
  Future<void> signOut() async {
    try {
      state = AsyncValue.data(
        AuthState(isLoading: true, user: state.value?.user),
      );
      final client = _ref.read(supabaseClientProvider);

      // Leere alle benutzerspezifischen Cache-Einträge vor dem Abmelden
      try {
        final jobTextCacheManager = _ref.read(jobTextCacheManagerProvider);
        await jobTextCacheManager.clearUserCaches();
      } catch (cacheError) {
        // Logge den Fehler, aber fahre mit dem Abmelden fort
        print('Fehler beim Leeren der Cache-Einträge: $cacheError');
      }

      await client.auth.signOut();
      state = AsyncValue.data(AuthState());
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      state = AsyncValue.data(AuthState(errorMessage: e.toString()));
    }
  }

  // Konto löschen
  Future<bool> deleteAccount() async {
    try {
      final log = getLogger('AuthStateNotifier');
      log.i("Starte Kontolöschung...");

      state = AsyncValue.data(
        AuthState(isLoading: true, user: state.value?.user),
      );

      final client = _ref.read(supabaseClientProvider);
      final currentUser = client.auth.currentUser;

      if (currentUser == null) {
        log.e("Kein angemeldeter Benutzer gefunden");
        throw Exception("Kein angemeldeter Benutzer gefunden");
      }

      log.i("Benutzer gefunden: ${currentUser.id}");

      // Hole das aktuelle Auth-Token
      final session = client.auth.currentSession;
      if (session == null) {
        log.e("Keine aktive Sitzung gefunden");
        throw Exception("Keine aktive Sitzung gefunden");
      }

      // Versuche zuerst, die Edge Function aufzurufen
      try {
        log.i("Versuche, die Edge Function aufzurufen...");

        final response = await client.functions.invoke(
          'delete-account',
          body: {'userId': currentUser.id, 'auth_token': session.accessToken},
        );

        log.i("Edge Function Antwort: ${response.status}");

        if (response.status == 200) {
          log.i("Konto erfolgreich über Edge Function gelöscht");

          // Lösche den has_existing_account-Flag in den SharedPreferences
          try {
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('has_existing_account', false);
            log.i("has_existing_account in SharedPreferences zurückgesetzt");
          } catch (prefsError) {
            log.w(
              "Fehler beim Zurücksetzen von has_existing_account: $prefsError",
            );
            // Wir fahren trotzdem fort
          }

          // Melde den Benutzer ab
          await client.auth.signOut();
          log.i("Benutzer abgemeldet nach erfolgreicher Edge Function");

          state = AsyncValue.data(AuthState());
          return true;
        }

        // Wenn die Edge Function fehlschlägt, führen wir die Löschung direkt durch
        log.w("Edge Function fehlgeschlagen, führe direkte Löschung durch...");
      } catch (e) {
        log.w("Fehler beim Aufruf der Edge Function: $e");
        log.i("Führe direkte Löschung durch...");
      }

      // Direkte Löschung als Fallback
      try {
        log.i("Lösche Benutzerdaten direkt...");

        // 1. Lösche Profil-Backups
        try {
          await client
              .from('profile_backups')
              .delete()
              .eq('profile_id', currentUser.id);
          log.i("Profil-Backups gelöscht");
        } catch (e) {
          log.w("Fehler beim Löschen der Profil-Backups: $e");
          // Wir fahren trotzdem fort
        }

        // 2. Lösche Abonnements
        try {
          await client
              .from('subscriptions')
              .delete()
              .eq('user_id', currentUser.id);
          log.i("Abonnements gelöscht");
        } catch (e) {
          log.w("Fehler beim Löschen der Abonnements: $e");
          // Wir fahren trotzdem fort
        }

        // 3. Lösche beworbene Jobs
        try {
          await client
              .from('applied_jobs')
              .delete()
              .eq('user_id', currentUser.id);
          log.i("Beworbene Jobs gelöscht");
        } catch (e) {
          log.w("Fehler beim Löschen der beworbenen Jobs: $e");
          // Wir fahren trotzdem fort
        }

        // 4. Lösche Favoriten
        try {
          await client.from('favorites').delete().eq('user_id', currentUser.id);
          log.i("Favoriten gelöscht");
        } catch (e) {
          log.w("Fehler beim Löschen der Favoriten: $e");
          // Wir fahren trotzdem fort
        }

        // 5. Lösche das Profil
        try {
          await client.from('profiles').delete().eq('id', currentUser.id);
          log.i("Profil gelöscht");
        } catch (e) {
          log.w("Fehler beim Löschen des Profils: $e");
          // Wir fahren trotzdem fort
        }

        // Lösche den has_existing_account-Flag in den SharedPreferences
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('has_existing_account', false);
          log.i("has_existing_account in SharedPreferences zurückgesetzt");

          // Lösche auch alle anderen relevanten Daten aus den SharedPreferences
          await prefs.remove('onboarding_complete');
          await prefs.remove('device_id');
          log.i("Weitere relevante Daten aus SharedPreferences gelöscht");
        } catch (prefsError) {
          log.w("Fehler beim Zurücksetzen von SharedPreferences: $prefsError");
          // Wir fahren trotzdem fort
        }

        // 6. Melde den Benutzer ab (da wir ihn nicht direkt löschen können)
        await client.auth.signOut();
        log.i("Benutzer abgemeldet");

        // Benutzer wurde erfolgreich abgemeldet und Daten gelöscht
        state = AsyncValue.data(AuthState());
        return true;
      } catch (functionError) {
        log.e("Fehler beim Löschen der Benutzerdaten: $functionError");
        throw Exception(
          "Fehler beim Löschen der Benutzerdaten: $functionError",
        );
      }
    } catch (e) {
      final log = getLogger('AuthStateNotifier');
      log.e("Allgemeiner Fehler: $e");
      state = AsyncValue.error(e, StackTrace.current);
      state = AsyncValue.data(AuthState(errorMessage: e.toString()));
      return false;
    }
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/utils/logging.dart';

const _onboardingCompleteKey = 'onboarding_complete';

// State Notifier, der den Onboarding-Status verwaltet
class OnboardingNotifier extends StateNotifier<bool> {
  OnboardingNotifier(this._prefs) : super(false) {
    _loadStatus();
  }

  final SharedPreferences _prefs;
  final _log = getLogger('OnboardingNotifier');

  // Lädt den Status aus SharedPreferences beim Initialisieren
  Future<void> _loadStatus() async {
    try {
      // Prüfe, ob der Provider noch mounted ist
      if (!mounted) {
        _log.w("Provider wurde vor Supabase-Abfrage disposed");
        return;
      }

      // Lade den Onboarding-Status aus SharedPreferences
      await _prefs.reload();
      final savedState = _prefs.getBool(_onboardingCompleteKey);

      if (savedState == null) {
        // Kein Wert gespeichert - initialisiere mit false für neue Benutzer (erster Start)
        state = false;
        await _prefs.setBool(_onboardingCompleteKey, false);
        _log.i(
          "Onboarding-Status nicht gefunden, setze auf false (erster Start)",
        );
      } else {
        state = savedState;
        _log.i("Onboarding-Status aus SharedPreferences geladen: $state");
      }
    } catch (e) {
      _log.e("Kritischer Fehler beim Laden des Onboarding-Status", error: e);
      state = false;
      await _prefs.setBool(_onboardingCompleteKey, false);
    }
  }

  // Markiert das Onboarding als abgeschlossen und speichert es
  Future<void> completeOnboarding() async {
    try {
      _log.i("Onboarding wird als abgeschlossen markiert...");
      state = true;

      await _prefs.setBool(_onboardingCompleteKey, true);
      await _prefs.reload();
      _log.i("Onboarding-Status lokal gespeichert.");

      _log.i(
        "Onboarding status saved: $state (Verifiziert: ${_prefs.getBool(_onboardingCompleteKey)})",
      );
    } catch (e) {
      _log.e(
        "Kritischer Fehler beim Speichern des Onboarding-Status",
        error: e,
      );
      state = false;
      await _prefs.setBool(_onboardingCompleteKey, false);
      await _prefs.reload();
    }
  }

  // Setzt den Onboarding-Status zurück (nützlich für Tests)
  Future<void> resetOnboarding() async {
    state = false;
    await _prefs.remove(_onboardingCompleteKey);
    _log.i("Onboarding status reset.");
  }

  @override
  void dispose() {
    _log.d("OnboardingNotifier disposed");
    super.dispose();
  }
}

// Eine einfache Implementierung von SharedPreferences für den Fallback-Fall
class TempPreferences implements SharedPreferences {
  final Map<String, Object> _values = {};

  @override
  Set<String> getKeys() => _values.keys.toSet();

  @override
  Object? get(String key) => _values[key];

  @override
  bool? getBool(String key) => _values[key] as bool?;

  @override
  double? getDouble(String key) => _values[key] as double?;

  @override
  int? getInt(String key) => _values[key] as int?;

  @override
  String? getString(String key) => _values[key] as String?;

  @override
  List<String>? getStringList(String key) => _values[key] as List<String>?;

  @override
  bool containsKey(String key) => _values.containsKey(key);

  @override
  Future<bool> clear() async {
    _values.clear();
    return true;
  }

  @override
  Future<bool> remove(String key) async {
    _values.remove(key);
    return true;
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setString(String key, String value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> commit() async => true;

  @override
  Future<bool> reload() async => true;
}

// Provider, der SharedPreferences bereitstellt (asynchron) mit robustem Error Handling
final sharedPreferencesProvider = FutureProvider<SharedPreferences>((
  ref,
) async {
  final log = getLogger('SharedPreferencesProvider');

  try {
    log.d('Initialisiere SharedPreferences...');
    final prefs = await SharedPreferences.getInstance();
    log.d('SharedPreferences erfolgreich initialisiert');
    return prefs;
  } catch (e, stackTrace) {
    log.e(
      'Fehler beim Initialisieren von SharedPreferences',
      error: e,
      stackTrace: stackTrace,
    );

    // Fallback: Erstelle temporäre In-Memory-Preferences
    log.w('Verwende temporäre In-Memory-Preferences als Fallback');
    return TempPreferences();
  }
});

// Ein Fallback-Provider mit Standardwert
final cachedPrefsProvider = Provider<SharedPreferences?>((ref) {
  final prefsAsync = ref.watch(sharedPreferencesProvider);
  return prefsAsync.when(
    data: (prefs) => prefs,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Provider für den OnboardingNotifier mit sicherem Fallback
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, bool>((
  ref,
) {
  final prefs = ref.watch(cachedPrefsProvider);
  if (prefs == null) {
    // Temporärer In-Memory-Provider mit Standardwert true
    final tempPrefs = TempPreferences();
    tempPrefs.setBool(
      _onboardingCompleteKey,
      true,
    ); // Annahme: Onboarding ist abgeschlossen
    return OnboardingNotifier(tempPrefs);
  }
  return OnboardingNotifier(prefs);
});

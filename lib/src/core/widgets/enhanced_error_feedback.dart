import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Erweiterte Error-Feedback-Komponenten mit actionable Fehlermeldungen
class EnhancedErrorFeedback {
  static final _log = getLogger('EnhancedErrorFeedback');

  /// Zeigt eine erweiterte Error-SnackBar mit Aktionen an
  static void showEnhancedErrorSnackBar(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    String? secondaryActionLabel,
    VoidCallback? onSecondaryAction,
    Duration duration = const Duration(seconds: 6),
    bool canDismiss = true,
    ErrorSeverity severity = ErrorSeverity.error,
  }) {
    if (!context.mounted) return;

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    Color backgroundColor;
    IconData icon;
    
    switch (severity) {
      case ErrorSeverity.info:
        backgroundColor = colorScheme.primary;
        icon = Icons.info_outline;
        break;
      case ErrorSeverity.warning:
        backgroundColor = Colors.orange;
        icon = Icons.warning_outlined;
        break;
      case ErrorSeverity.error:
        backgroundColor = colorScheme.error;
        icon = Icons.error_outline;
        break;
      case ErrorSeverity.critical:
        backgroundColor = Colors.red.shade800;
        icon = Icons.dangerous_outlined;
        break;
    }

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    
    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (severity == ErrorSeverity.critical)
                  const Text(
                    'Bitte kontaktiere den Support falls das Problem weiterhin besteht.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      duration: duration,
      behavior: SnackBarBehavior.floating,
      action: actionLabel != null
          ? SnackBarAction(
              label: actionLabel,
              textColor: Colors.white,
              onPressed: onAction ?? () {},
            )
          : null,
      dismissDirection: canDismiss ? DismissDirection.horizontal : DismissDirection.none,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);

    // Logge die Fehlermeldung
    _log.w('User Error Feedback: $message', context: {
      'severity': severity.name,
      'hasAction': actionLabel != null,
      'canDismiss': canDismiss,
    });
  }

  /// Zeigt einen erweiterten Error-Dialog an
  static Future<ErrorDialogResult?> showEnhancedErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    String? details,
    List<ErrorAction> actions = const [],
    bool barrierDismissible = true,
    ErrorSeverity severity = ErrorSeverity.error,
  }) async {
    if (!context.mounted) return null;

    return showDialog<ErrorDialogResult>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => EnhancedErrorDialog(
        title: title,
        message: message,
        details: details,
        actions: actions,
        severity: severity,
      ),
    );
  }

  /// Zeigt einen Retry-Dialog an
  static Future<bool> showRetryDialog(
    BuildContext context, {
    required String operation,
    String? errorDetails,
    int attemptNumber = 1,
    int maxAttempts = 3,
  }) async {
    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.refresh, size: 48, color: Colors.orange),
        title: Text('$operation fehlgeschlagen'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Versuch $attemptNumber von $maxAttempts'),
            if (errorDetails != null) ...[
              const SizedBox(height: 16),
              const Text('Details:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text(errorDetails, style: const TextStyle(fontSize: 12)),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Abbrechen'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Erneut versuchen'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Zeigt eine Offline-Benachrichtigung an
  static void showOfflineNotification(BuildContext context) {
    showEnhancedErrorSnackBar(
      context,
      'Keine Internetverbindung. Einige Funktionen sind möglicherweise nicht verfügbar.',
      actionLabel: 'Erneut prüfen',
      onAction: () {
        // Implementierung für Netzwerk-Check
      },
      severity: ErrorSeverity.warning,
      duration: const Duration(seconds: 10),
    );
  }

  /// Zeigt eine Wartungs-Benachrichtigung an
  static void showMaintenanceNotification(BuildContext context, {DateTime? estimatedEnd}) {
    final message = estimatedEnd != null
        ? 'Wartungsarbeiten bis ca. ${_formatTime(estimatedEnd)}'
        : 'Wartungsarbeiten im Gange';

    showEnhancedErrorSnackBar(
      context,
      message,
      severity: ErrorSeverity.info,
      duration: const Duration(seconds: 8),
      canDismiss: false,
    );
  }

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Erweiterte Error-Dialog-Komponente
class EnhancedErrorDialog extends StatefulWidget {
  final String title;
  final String message;
  final String? details;
  final List<ErrorAction> actions;
  final ErrorSeverity severity;

  const EnhancedErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.details,
    this.actions = const [],
    this.severity = ErrorSeverity.error,
  });

  @override
  State<EnhancedErrorDialog> createState() => _EnhancedErrorDialogState();
}

class _EnhancedErrorDialogState extends State<EnhancedErrorDialog> {
  bool _showDetails = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    IconData icon;
    Color iconColor;
    
    switch (widget.severity) {
      case ErrorSeverity.info:
        icon = Icons.info_outline;
        iconColor = theme.colorScheme.primary;
        break;
      case ErrorSeverity.warning:
        icon = Icons.warning_outlined;
        iconColor = Colors.orange;
        break;
      case ErrorSeverity.error:
        icon = Icons.error_outline;
        iconColor = theme.colorScheme.error;
        break;
      case ErrorSeverity.critical:
        icon = Icons.dangerous_outlined;
        iconColor = Colors.red.shade800;
        break;
    }

    return AlertDialog(
      icon: Icon(icon, size: 48, color: iconColor),
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.message),
          if (widget.details != null) ...[
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () => setState(() => _showDetails = !_showDetails),
              icon: Icon(_showDetails ? Icons.expand_less : Icons.expand_more),
              label: Text(_showDetails ? 'Details ausblenden' : 'Details anzeigen'),
            ),
            if (_showDetails) ...[
              const SizedBox(height: 8),
              Container(
                width: double.maxFinite,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text('Details:', style: TextStyle(fontWeight: FontWeight.bold)),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            Clipboard.setData(ClipboardData(text: widget.details!));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Details kopiert')),
                            );
                          },
                          icon: const Icon(Icons.copy, size: 16),
                          tooltip: 'Details kopieren',
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.details!,
                      style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
      actions: [
        if (widget.actions.isEmpty)
          TextButton(
            onPressed: () => Navigator.of(context).pop(ErrorDialogResult.dismissed),
            child: const Text('OK'),
          )
        else
          ...widget.actions.map((action) => _buildActionButton(action)),
      ],
    );
  }

  Widget _buildActionButton(ErrorAction action) {
    return action.isPrimary
        ? ElevatedButton(
            onPressed: () {
              action.onPressed?.call();
              Navigator.of(context).pop(action.result);
            },
            child: Text(action.label),
          )
        : TextButton(
            onPressed: () {
              action.onPressed?.call();
              Navigator.of(context).pop(action.result);
            },
            child: Text(action.label),
          );
  }
}

/// Error-Schweregrad
enum ErrorSeverity { info, warning, error, critical }

/// Error-Dialog-Ergebnis
enum ErrorDialogResult { dismissed, retry, cancel, reportBug, contactSupport }

/// Error-Aktion für Dialoge
class ErrorAction {
  final String label;
  final ErrorDialogResult result;
  final VoidCallback? onPressed;
  final bool isPrimary;

  const ErrorAction({
    required this.label,
    required this.result,
    this.onPressed,
    this.isPrimary = false,
  });

  static const retry = ErrorAction(
    label: 'Erneut versuchen',
    result: ErrorDialogResult.retry,
    isPrimary: true,
  );

  static const cancel = ErrorAction(
    label: 'Abbrechen',
    result: ErrorDialogResult.cancel,
  );

  static const reportBug = ErrorAction(
    label: 'Fehler melden',
    result: ErrorDialogResult.reportBug,
  );

  static const contactSupport = ErrorAction(
    label: 'Support kontaktieren',
    result: ErrorDialogResult.contactSupport,
  );
}

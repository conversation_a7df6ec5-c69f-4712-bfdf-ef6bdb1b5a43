import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../utils/logging.dart';

/// Einheitliches Form Validation Framework für die gesamte App
class FormValidationFramework {
  static final _log = getLogger('FormValidationFramework');

  /// Email Validation mit verbesserter Regex
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Bitte gib deine E-Mail-Adresse ein';
    }

    final email = value.trim();
    
    // Verbesserte Email-Regex die auch internationale Domains unterstützt
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
    );

    if (!emailRegex.hasMatch(email)) {
      return 'Bitte gib eine gültige E-Mail-Adresse ein';
    }

    // Zusätzliche Checks
    if (email.length > 254) {
      return 'E-Mail-Adresse ist zu lang';
    }

    if (email.startsWith('.') || email.endsWith('.')) {
      return 'E-Mail-Adresse darf nicht mit einem Punkt beginnen oder enden';
    }

    return null;
  }

  /// Password Validation mit konfigurierbaren Anforderungen
  static String? validatePassword(String? value, {
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = false,
  }) {
    if (value == null || value.isEmpty) {
      return 'Bitte gib ein Passwort ein';
    }

    if (value.length < minLength) {
      return 'Passwort muss mindestens $minLength Zeichen lang sein';
    }

    if (requireUppercase && !value.contains(RegExp(r'[A-Z]'))) {
      return 'Passwort muss mindestens einen Großbuchstaben enthalten';
    }

    if (requireLowercase && !value.contains(RegExp(r'[a-z]'))) {
      return 'Passwort muss mindestens einen Kleinbuchstaben enthalten';
    }

    if (requireNumbers && !value.contains(RegExp(r'[0-9]'))) {
      return 'Passwort muss mindestens eine Zahl enthalten';
    }

    if (requireSpecialChars && !value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Passwort muss mindestens ein Sonderzeichen enthalten';
    }

    return null;
  }

  /// Password Confirmation Validation
  static String? validatePasswordConfirmation(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Bitte bestätige dein Passwort';
    }

    if (value != originalPassword) {
      return 'Die Passwörter stimmen nicht überein';
    }

    return null;
  }

  /// Name Validation
  static String? validateName(String? value, {int minLength = 2, int maxLength = 50}) {
    if (value == null || value.trim().isEmpty) {
      return 'Bitte gib deinen Namen ein';
    }

    final name = value.trim();

    if (name.length < minLength) {
      return 'Name muss mindestens $minLength Zeichen lang sein';
    }

    if (name.length > maxLength) {
      return 'Name darf maximal $maxLength Zeichen lang sein';
    }

    // Prüfe auf gültige Zeichen (Buchstaben, Leerzeichen, Bindestriche, Apostrophe)
    if (!RegExp(r"^[a-zA-ZäöüÄÖÜß\s\-']+$").hasMatch(name)) {
      return 'Name darf nur Buchstaben, Leerzeichen, Bindestriche und Apostrophe enthalten';
    }

    return null;
  }

  /// Phone Number Validation (deutsche Telefonnummern)
  static String? validatePhoneNumber(String? value, {bool required = false}) {
    if (value == null || value.trim().isEmpty) {
      return required ? 'Bitte gib deine Telefonnummer ein' : null;
    }

    final phone = value.trim().replaceAll(RegExp(r'\s+'), '');

    // Deutsche Telefonnummer-Regex
    final phoneRegex = RegExp(r'^(\+49|0)[1-9][0-9]{1,14}$');

    if (!phoneRegex.hasMatch(phone)) {
      return 'Bitte gib eine gültige deutsche Telefonnummer ein';
    }

    return null;
  }

  /// URL Validation
  static String? validateUrl(String? value, {bool required = false}) {
    if (value == null || value.trim().isEmpty) {
      return required ? 'Bitte gib eine URL ein' : null;
    }

    final url = value.trim();

    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return 'URL muss mit http:// oder https:// beginnen';
      }
      if (!uri.hasAuthority) {
        return 'Bitte gib eine gültige URL ein';
      }
    } catch (e) {
      return 'Bitte gib eine gültige URL ein';
    }

    return null;
  }

  /// Generic Text Validation
  static String? validateText(
    String? value, {
    bool required = true,
    int? minLength,
    int? maxLength,
    String? fieldName,
  }) {
    final field = fieldName ?? 'Feld';

    if (value == null || value.trim().isEmpty) {
      return required ? 'Bitte fülle das $field aus' : null;
    }

    final text = value.trim();

    if (minLength != null && text.length < minLength) {
      return '$field muss mindestens $minLength Zeichen lang sein';
    }

    if (maxLength != null && text.length > maxLength) {
      return '$field darf maximal $maxLength Zeichen lang sein';
    }

    return null;
  }

  /// Number Validation
  static String? validateNumber(
    String? value, {
    bool required = true,
    double? min,
    double? max,
    String? fieldName,
  }) {
    final field = fieldName ?? 'Zahl';

    if (value == null || value.trim().isEmpty) {
      return required ? 'Bitte gib eine $field ein' : null;
    }

    final number = double.tryParse(value.trim());
    if (number == null) {
      return 'Bitte gib eine gültige $field ein';
    }

    if (min != null && number < min) {
      return '$field muss mindestens $min sein';
    }

    if (max != null && number > max) {
      return '$field darf maximal $max sein';
    }

    return null;
  }

  /// Date Validation
  static String? validateDate(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      return required ? 'Bitte wähle ein Datum' : null;
    }

    try {
      DateTime.parse(value);
      return null;
    } catch (e) {
      return 'Bitte gib ein gültiges Datum ein';
    }
  }

  /// Kombinierte Validation für mehrere Felder
  static bool validateForm(GlobalKey<FormState> formKey, {VoidCallback? onError}) {
    try {
      final isValid = formKey.currentState?.validate() ?? false;
      if (!isValid && onError != null) {
        onError();
      }
      return isValid;
    } catch (e, stackTrace) {
      _log.e('Fehler bei Form-Validation', error: e, stackTrace: stackTrace);
      if (onError != null) {
        onError();
      }
      return false;
    }
  }

  /// Sichere Text-Bereinigung
  static String sanitizeInput(String? input) {
    if (input == null) return '';
    
    return input
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ') // Mehrfache Leerzeichen durch einzelne ersetzen
        .replaceAll(RegExp(r'[^\w\s@.\-_äöüÄÖÜß]'), ''); // Gefährliche Zeichen entfernen
  }

  /// Error State Management für Forms
  static void handleFormError(BuildContext context, String error) {
    _log.w('Form-Validierungsfehler: $error');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Success State Management für Forms
  static void handleFormSuccess(BuildContext context, String message) {
    _log.i('Form erfolgreich verarbeitet: $message');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// Provider für Form Loading States
final formLoadingProvider = StateProvider.family<bool, String>((ref, formId) => false);

/// Provider für Form Error States
final formErrorProvider = StateProvider.family<String?, String>((ref, formId) => null);

/// Mixin für Form Error Handling
mixin FormErrorHandlingMixin {
  void showFormError(BuildContext context, String error) {
    FormValidationFramework.handleFormError(context, error);
  }

  void showFormSuccess(BuildContext context, String message) {
    FormValidationFramework.handleFormSuccess(context, message);
  }

  void clearFormError(WidgetRef ref, String formId) {
    ref.read(formErrorProvider(formId).notifier).state = null;
  }

  void setFormLoading(WidgetRef ref, String formId, bool loading) {
    ref.read(formLoadingProvider(formId).notifier).state = loading;
  }

  void setFormError(WidgetRef ref, String formId, String error) {
    ref.read(formErrorProvider(formId).notifier).state = error;
  }
}

/// Diese Datei enthält Prompt-Templates für die DeepSeek API.
/// Die Konfiguration ist für den Nutzer nicht zugänglich und dient zur Qualitätsverbesserung.
library;

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;

class DeepSeekPrompts {
  /// Hauptprompt-Template für die Generierung von Bewerbungsanschreiben
  static String generateCoverLetterPrompt({
    required String jobTitle,
    required String jobDescription,
    required String personalizationText,
    required String styleInstructions,
  }) {
    return """
Du bist ein professioneller Bewerbungsexperte mit jahrelanger Erfahrung im HR-Bereich.
Deine Aufgabe:
1. Analysiere den folgenden Text einer Stellenanzeige für '$jobTitle'.
2. Extrahiere die wahrscheinlichste E-Mail-Adresse für eine Bewerbung. Wenn keine E-Mail für Bewerbungen gefunden wird, antworte mit 'EMAIL: nicht gefunden'.
3. <PERSON><PERSON><PERSON> ein überzeugendes Bewerbungsanschreiben unter Beachtung dieser Regeln:
   - Beziehe dich konkret auf die Anforderungen und Aufgaben der Stellenanzeige
   - Stelle gezielt Bezüge zwischen den Qualifikationen des Bewerbers und den Anforderungen her
   - Verwende einen klaren, präzisen Schreibstil ohne Übertreibungen
   - Strukturiere das Anschreiben mit einer überzeugenden Einleitung, relevantem Hauptteil und starkem Abschluss
   - Füge eine formell korrekte Anrede und Grußformel hinzu
   - Halte das Anschreiben auf etwa eine A4-Seite begrenzt
   - Betone Motivation und Mehrwert für das Unternehmen
   - Vermeide Standardfloskeln, Rechtschreibfehler und Passivsätze
   - Füge in der Signatur den Namen des Bewerbers ein, wenn dieser angegeben wurde
4. Beginne niemals die Bewerbung mit "mit großer Interesse" oder ähnlichem
5. Wenn der Bewerber nicht die Fähigkeit hat, die Anforderungen der Stellenanzeige zu erfüllen, schreibe das nicht extra, sondern verschöne diese Lücke // Keep original rule 5 as it might capture nuance
6. Füge KEINE Hinweise, Tipps oder Metakommentare in die Bewerbung ein - der Text soll NUR das fertige Anschreiben sein
7. Erkläre nichts über das Anschreiben selbst und füge keine Kommentare wie "Hier ist dein Anschreiben" hinzu
8. Liefere ausschließlich den Bewerbungstext ohne jegliche Erklärungen, Ratschläge oder Hinweise
9. Schreibe nicht, dass etwas mich reizt, Oder typischen Sätze von Ki // Renumbered from 10
10. WICHTIGE REGEL: Füge KEINE Absender-Kontaktinformationen (E-Mail, Telefon, Adresse) in das Anschreiben ein. Der Name am Ende ist erlaubt. // New rule 10
11. WICHTIGE REGEL: Wenn Qualifikationen fehlen, erwähne dies NICHT direkt. Formuliere stattdessen positiv, z.B. durch Betonung der Lernbereitschaft oder der Übertragbarkeit vorhandener Fähigkeiten auf die neuen Anforderungen. // New rule 11
12. verwende nicht diese formulierung "besonders reizt mich sehr" verwende stattdessen "mich spricht...an" oder andere verariante als "besonders reizt mich sehr" // New rule 12

${personalizationText.isNotEmpty ? 'Persönliche Informationen des Bewerbers:\n$personalizationText\n\n' : ''}
${styleInstructions.isNotEmpty ? 'Gewünschte Stilrichtung:\n$styleInstructions\n\n' : ''}


--- Extrahierter Text der Stellenanzeige ---
$jobDescription
--- Ende Text ---

Antworte NUR im folgenden Format:
EMAIL: [extrahierte E-Mail oder 'nicht gefunden']
ANSCHREIBEN:
[generiertes Anschreiben]
""";
  }

  /// Erweiterte Anweisungen für verschiedene Bewerbungsstile
  static Map<String, String> styleInstructions = {
    'Professionell': """
Schreibe in einem professionellen, formellen Stil mit:
- Sachlicher, klarer Sprache ohne Umschweife
- Angemessener formeller Distanz
- Präzisen Formulierungen und Fachbegriffen
- Höflichem, respektvollem Ton
""",
    'Kreativ': """
Schreibe in einem kreativen, innovativen Stil mit:
- Originellen Formulierungen und bildlicher Sprache
- Persönlicher Note und authentischer Stimme
- Überraschenden Perspektiven und einprägsamen Wendungen
- Dynamischem, enthusiastischem Ausdruck (ohne übertrieben zu wirken)
""",
    'Technisch': """
Schreibe in einem technischen, sachlichen Stil mit:
- Präzisen Fachbegriffen und technischem Vokabular
- Logischem Aufbau und klaren Zusammenhängen
- Faktenbasierter Argumentation und konkreten Beispielen
- Effizienter, zielgerichteter Kommunikation ohne Ausschmückungen
""",
    'Relevant': """
Schreibe eine zielgerichtete Bewerbung mit intelligenter Qualifikationsauswahl:
- Analysiere die Stellenbeschreibung und identifiziere die wichtigsten Anforderungen
- Wähle NUR die passendsten Qualifikationen und Erfahrungen aus dem Lebenslauf aus
- Erwähne andere Qualifikationen nur kurz oder gar nicht, wenn sie nicht relevant sind
- Fokussiere auf die Übereinstimmung zwischen Stellenanforderungen und Bewerberprofil
- Vermeide irrelevante Details oder Qualifikationen, die nicht zur Position passen
- Erstelle eine präzise, relevante Bewerbung ohne überflüssige Informationen
""",
    'Passend zu meinem Stil': """
Passe den Schreibstil an das Profil des Bewerbers an:
- Berücksichtige Bildungsniveau, Sprachkenntnisse und beruflichen Hintergrund
- Verwende einen authentischen, zum Bewerber passenden Ausdruck
- Achte auf angemessene Formalität entsprechend der Qualifikationen
- Stelle die Stärken des Bewerbers in einem natürlichen, zu ihm passenden Stil dar
""",
  };

  /// Optimiert vorhandene personalisierte Informationen für den Prompt
  static String optimizePersonalization(Map<String, String?> userInfo) {
    StringBuffer buffer = StringBuffer();

    if (userInfo['name']?.isNotEmpty == true) {
      buffer.writeln("Name: ${userInfo['name']}");
    }

    if (userInfo['email']?.isNotEmpty == true) {
      buffer.writeln("E-Mail: ${userInfo['email']}");
    }

    if (userInfo['experience']?.isNotEmpty == true) {
      buffer.writeln("Berufliche Erfahrung:");
      buffer.writeln(userInfo['experience']);
    }

    if (userInfo['education']?.isNotEmpty == true) {
      buffer.writeln("Ausbildung und Studium:");
      buffer.writeln(userInfo['education']);
    }

    if (userInfo['skills']?.isNotEmpty == true) {
      buffer.writeln("Kenntnisse und Fähigkeiten:");
      buffer.writeln(userInfo['skills']);
    }

    if (userInfo['interests']?.isNotEmpty == true) {
      buffer.writeln("Interessen und Motivation:");
      buffer.writeln(userInfo['interests']);
    }

    return buffer.toString().trim();
  }

  /// Generiert einen personalisierten Schreibstil-Prompt basierend auf dem Benutzerprofil
  /// Diese Methode wird aufgerufen, wenn der Benutzer "Passend zu meinem Stil" auswählt
  /// Die Methode ruft die Supabase-Funktion 'analyze-profile-style' auf,
  /// die die Profildaten analysiert und einen personalisierten Prompt zurückgibt
  static Future<String> generatePersonalizedStylePrompt({
    required Map<String, dynamic> profileData,
  }) async {
    // Standardprompt als Fallback
    final String defaultPrompt = """
Analysiere das Profil des Bewerbers und erstelle einen personalisierten Schreibstil, der folgende Faktoren berücksichtigt:

1. Sprachniveau: Bestimme anhand des Lebenslaufs, ob die Person wahrscheinlich Deutsch als Muttersprache spricht oder nicht. Passe den Sprachstil entsprechend an (Komplexität der Sätze, Wortschatz, idiomatische Ausdrücke).

2. Bildungsniveau: Berücksichtige den höchsten Bildungsabschluss und passe die Formalität und Komplexität des Schreibstils entsprechend an. Eine Person mit Doktortitel würde anders formulieren als jemand mit Hauptschulabschluss.

3. Berufserfahrung: Analysiere die Art und Dauer der Berufserfahrung. Verwende Fachbegriffe und Ausdrucksweisen, die typisch für die Branche und Position des Bewerbers sind.

4. Persönlichkeit: Leite aus den Hobbys, Interessen und der Berufswahl Persönlichkeitsmerkmale ab und passe den Ton entsprechend an (z.B. dynamisch und enthusiastisch vs. sachlich und zurückhaltend).

5. Kultureller Hintergrund: Berücksichtige mögliche kulturelle Einflüsse auf den Schreibstil, basierend auf Namen, Sprachkenntnissen und internationaler Erfahrung.

Frage dich: "Wie würde diese spezifische Person mit ihrer einzigartigen Kombination aus Bildung, Erfahrung und Hintergrund eine Bewerbung formulieren?" und erstelle einen Schreibstil, der authentisch zu dieser Person passt.
""";

    try {
      // Aufruf der Supabase-Funktion 'analyze-profile-style'
      final response = await Supabase.instance.client.functions.invoke(
        'analyze-profile-style',
        body: {'profileData': profileData},
      );

      // Überprüfen, ob der Aufruf erfolgreich war
      if (response.status == 200 && response.data != null) {
        // Extrahieren des personalisierten Prompts aus der Antwort
        final stylePrompt = response.data['stylePrompt'];

        // Wenn ein Prompt zurückgegeben wurde, verwenden wir diesen
        if (stylePrompt != null && stylePrompt.toString().isNotEmpty) {
          return stylePrompt.toString();
        }
      }

      // Wenn der Aufruf fehlschlägt oder kein Prompt zurückgegeben wird,
      // verwenden wir den Standardprompt
      return defaultPrompt;
    } catch (e) {
      // Bei einem Fehler loggen wir diesen und verwenden den Standardprompt
      debugPrint(
        'Fehler beim Aufruf der Supabase-Funktion "analyze-profile-style": $e',
      );
      return defaultPrompt;
    }
  }
}

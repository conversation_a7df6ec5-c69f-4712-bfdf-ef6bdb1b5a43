// import 'package:flutter_dotenv/flutter_dotenv.dart'; // Entfernt

/// Konfigurationsklasse für die App, geladen aus Compile-Time-Variablen (--dart-define).
class AppConfig {
  final String deepSeekApiKey;
  // final String geminiApiKey; // Entfernt, da nicht benötigt

  const AppConfig({
    required this.deepSeekApiKey,
    // required this.geminiApiKey, // Entfernt
  });

  /// Lädt die Konfiguration aus Compile-Time-Variablen (--dart-define).
  static AppConfig fromEnvironment() {
    const deepSeekKey = String.fromEnvironment('DEEPSEEK_API_KEY');
    // const geminiKey = String.fromEnvironment('GEMINI_API_KEY'); // Beispiel

    if (deepSeekKey.isEmpty) {
      // Wichtiger Hinweis, wenn der Key fehlt!
      // print("WARNUNG: DEEPSEEK_API_KEY wurde nicht über --dart-define übergeben!");
      // Optional: Standardwert oder Fehler werfen?
    }

    return AppConfig(
      deepSeekApiKey: deepSeekKey,
      // geminiApiKey: geminiKey,
    );
  }

  // Hilfsmethode, um zu prüfen, ob ein Key fehlt (optional)
  bool get isDeepSeekKeyMissing => deepSeekApiKey.isEmpty;
  // bool get isGeminiKeyMissing => geminiApiKey.isEmpty; // Entfernt
}

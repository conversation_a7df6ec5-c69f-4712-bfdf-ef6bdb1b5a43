import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../application/services/logger.dart';

/// Erweiterte Memory-Leak-Detection für kritische GC-Probleme
class MemoryLeakDetector {
  static final Logger _log = Logger('MemoryLeakDetector');
  static final Map<String, List<WeakReference<Object>>> _trackedObjects = {};
  static final Map<String, int> _leakCounts = {};
  static Timer? _monitoringTimer;
  static bool _isEnabled = kDebugMode;

  /// Startet kontinuierliches Memory-Monitoring
  static void startMonitoring() {
    if (!_isEnabled) return;

    _log.i('🔍 Memory Leak Detector gestartet');

    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performLeakCheck();
    });
  }

  /// Stoppt Memory-Monitoring
  static void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _log.i('🛑 Memory Leak Detector gestoppt');
  }

  /// Registriert ein Objekt für Memory-Tracking
  static void track(String category, Object object) {
    if (!_isEnabled) return;

    _trackedObjects.putIfAbsent(category, () => []);
    _trackedObjects[category]!.add(WeakReference(object));

    // Bereinige tote Referenzen
    _cleanupDeadReferences(category);
  }

  /// Führt eine umfassende Leak-Prüfung durch
  static void _performLeakCheck() {
    final report = StringBuffer();
    report.writeln('🔍 MEMORY LEAK REPORT - ${DateTime.now()}');
    report.writeln('=' * 50);

    bool hasLeaks = false;

    for (final entry in _trackedObjects.entries) {
      final category = entry.key;
      final references = entry.value;

      // Bereinige tote Referenzen
      _cleanupDeadReferences(category);

      final aliveCount = references.where((ref) => ref.target != null).length;

      if (aliveCount > 0) {
        hasLeaks = true;
        _leakCounts[category] = (_leakCounts[category] ?? 0) + aliveCount;

        report.writeln('⚠️  $category: $aliveCount aktive Objekte');
        report.writeln('   Gesamt-Leaks: ${_leakCounts[category]}');

        // Kritische Warnung bei vielen Leaks
        if (aliveCount > 10) {
          report.writeln('   🚨 KRITISCH: Zu viele aktive Objekte!');
        }
      }
    }

    if (!hasLeaks) {
      report.writeln('✅ Keine Memory-Leaks erkannt');
    } else {
      report.writeln('\n🔧 EMPFOHLENE AKTIONEN:');
      report.writeln('1. Prüfe dispose() Methoden');
      report.writeln('2. Cancel alle Timer und Streams');
      report.writeln('3. Dispose AnimationController');
      report.writeln('4. Cleanup WebView-Instanzen');
    }

    _log.w(report.toString());

    // Bei kritischen Leaks: Force GC
    if (_getTotalLeaks() > 50) {
      _log.e('🚨 KRITISCHE MEMORY-LEAKS ERKANNT - Force GC');
      _forceGarbageCollection();
    }
  }

  /// Bereinigt tote WeakReferences
  static void _cleanupDeadReferences(String category) {
    final references = _trackedObjects[category];
    if (references == null) return;

    references.removeWhere((ref) => ref.target == null);
  }

  /// Zählt Gesamt-Leaks
  static int _getTotalLeaks() {
    return _leakCounts.values.fold(0, (sum, count) => sum + count);
  }

  /// Erzwingt Garbage Collection
  static void _forceGarbageCollection() {
    // Mehrfache GC-Aufrufe für bessere Bereinigung
    for (int i = 0; i < 3; i++) {
      // Hinweis: System.gc() ist nicht verfügbar in Dart
      // Stattdessen verwenden wir indirekte Methoden
      _triggerGC();
    }
  }

  /// Triggert indirekt Garbage Collection
  static void _triggerGC() {
    // Erstelle temporäre große Objekte um GC zu triggern
    final temp = List.generate(1000, (i) => List.filled(100, i));
    temp.clear(); // Sofort freigeben
  }

  /// Erstellt detaillierten Memory-Report
  static String generateDetailedReport() {
    final report = StringBuffer();
    report.writeln('📊 DETAILLIERTER MEMORY LEAK REPORT');
    report.writeln('Generiert: ${DateTime.now()}');
    report.writeln('=' * 60);

    // Kategorien-Übersicht
    report.writeln('\n📋 KATEGORIEN-ÜBERSICHT:');
    for (final entry in _trackedObjects.entries) {
      final category = entry.key;
      final references = entry.value;
      final aliveCount = references.where((ref) => ref.target != null).length;
      final totalCount = references.length;

      report.writeln('$category:');
      report.writeln('  Aktive Objekte: $aliveCount');
      report.writeln('  Gesamt verfolgt: $totalCount');
      report.writeln(
        '  Bereinigungsrate: ${((totalCount - aliveCount) / totalCount * 100).toStringAsFixed(1)}%',
      );
      report.writeln();
    }

    // Leak-Statistiken
    report.writeln('📈 LEAK-STATISTIKEN:');
    final totalLeaks = _getTotalLeaks();
    report.writeln('Gesamt-Leaks: $totalLeaks');

    if (totalLeaks > 0) {
      report.writeln('\nTop Leak-Kategorien:');
      final sortedLeaks =
          _leakCounts.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      for (int i = 0; i < sortedLeaks.length && i < 5; i++) {
        final entry = sortedLeaks[i];
        report.writeln('${i + 1}. ${entry.key}: ${entry.value} Leaks');
      }
    }

    // Empfehlungen
    report.writeln('\n🔧 EMPFEHLUNGEN:');
    if (totalLeaks == 0) {
      report.writeln(
        '✅ Keine Aktionen erforderlich - Memory Management ist optimal',
      );
    } else if (totalLeaks < 10) {
      report.writeln('⚠️  Geringe Leaks - Überwachung fortsetzen');
    } else if (totalLeaks < 50) {
      report.writeln('🔶 Moderate Leaks - dispose() Methoden prüfen');
    } else {
      report.writeln('🚨 KRITISCHE LEAKS - Sofortige Maßnahmen erforderlich!');
      report.writeln('   1. Alle Timer mit cancel() stoppen');
      report.writeln('   2. StreamSubscriptions mit cancel() beenden');
      report.writeln('   3. AnimationController mit dispose() freigeben');
      report.writeln('   4. WebView-Instanzen ordnungsgemäß bereinigen');
    }

    return report.toString();
  }

  /// Reset aller Tracking-Daten
  static void reset() {
    _trackedObjects.clear();
    _leakCounts.clear();
    _log.i('🔄 Memory Leak Detector zurückgesetzt');
  }

  /// Prüft spezifische Objekt-Typen auf Leaks
  static Map<String, int> checkSpecificLeaks() {
    final specificLeaks = <String, int>{};

    // Prüfe auf häufige Leak-Quellen
    final leakSources = [
      'Timer',
      'StreamSubscription',
      'AnimationController',
      'TextEditingController',
      'WebViewController',
      'ScrollController',
      'FocusNode',
      'TabController',
    ];

    for (final source in leakSources) {
      final references = _trackedObjects[source];
      if (references != null) {
        final aliveCount = references.where((ref) => ref.target != null).length;
        if (aliveCount > 0) {
          specificLeaks[source] = aliveCount;
        }
      }
    }

    return specificLeaks;
  }

  /// Aktiviert/Deaktiviert Memory-Tracking
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      stopMonitoring();
      reset();
    }
  }
}

/// Extension für einfaches Object-Tracking
extension MemoryTrackable on Object {
  /// Registriert dieses Objekt für Memory-Tracking
  void trackMemory(String category) {
    MemoryLeakDetector.track(category, this);
  }
}

/// Mixin für automatisches Memory-Tracking in StatefulWidgets
mixin MemoryTrackingMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();
    MemoryLeakDetector.track('StatefulWidget', this);
  }

  @override
  void dispose() {
    // Objekt wird automatisch aus Tracking entfernt wenn dispose() aufgerufen wird
    super.dispose();
  }
}

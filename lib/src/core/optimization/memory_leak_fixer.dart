import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../application/services/logger.dart';
import 'memory_leak_detector.dart';

/// Automatischer Memory-Leak-Fixer für kritische GC-Probleme
class MemoryLeakFixer {
  static final Logger _log = Logger('MemoryLeakFixer');
  static bool _isEnabled = kDebugMode;
  static Timer? _autoFixTimer;

  /// Startet automatische Memory-Leak-Fixes
  static void startAutoFix() {
    if (!_isEnabled) return;

    _log.i('🔧 Memory Leak Auto-Fixer gestartet');

    _autoFixTimer?.cancel();
    _autoFixTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _performAutoFix();
    });
  }

  /// Stoppt automatische Memory-Leak-Fixes
  static void stopAutoFix() {
    _autoFixTimer?.cancel();
    _autoFixTimer = null;
    _log.i('🛑 Memory Leak Auto-Fixer gestoppt');
  }

  /// Führt automatische Memory-Leak-Fixes durch
  static void _performAutoFix() {
    final leaks = MemoryLeakDetector.checkSpecificLeaks();

    if (leaks.isEmpty) {
      _log.i('✅ Keine Memory-Leaks gefunden - keine Fixes erforderlich');
      return;
    }

    _log.w('🔧 Memory-Leaks erkannt - starte Auto-Fix:');

    for (final entry in leaks.entries) {
      final category = entry.key;
      final count = entry.value;

      _log.w('   $category: $count aktive Objekte');

      // Spezifische Fixes für verschiedene Leak-Typen
      switch (category) {
        case 'Timer':
          _fixTimerLeaks();
          break;
        case 'StreamSubscription':
          _fixStreamLeaks();
          break;
        case 'AnimationController':
          _fixAnimationLeaks();
          break;
        case 'WebViewController':
          _fixWebViewLeaks();
          break;
        case 'TextEditingController':
          _fixTextControllerLeaks();
          break;
        default:
          _log.w('   Kein spezifischer Fix für $category verfügbar');
      }
    }

    // Force Garbage Collection nach Fixes
    _forceGarbageCollection();
  }

  /// Behebt Timer-Memory-Leaks
  static void _fixTimerLeaks() {
    _log.i('🔧 Behebe Timer-Leaks...');

    // Hinweis: Timer-Leaks können nur durch ordnungsgemäße cancel() Aufrufe behoben werden
    // Hier können wir nur warnen und Empfehlungen geben
    _log.w('⚠️  Timer-Leaks erkannt:');
    _log.w('   1. Prüfe alle Timer.periodic() Aufrufe');
    _log.w(
      '   2. Stelle sicher, dass timer.cancel() in dispose() aufgerufen wird',
    );
    _log.w('   3. Verwende mounted-Checks in Timer-Callbacks');
  }

  /// Behebt StreamSubscription-Memory-Leaks
  static void _fixStreamLeaks() {
    _log.i('🔧 Behebe StreamSubscription-Leaks...');

    _log.w('⚠️  StreamSubscription-Leaks erkannt:');
    _log.w('   1. Prüfe alle .listen() Aufrufe');
    _log.w(
      '   2. Stelle sicher, dass subscription.cancel() in dispose() aufgerufen wird',
    );
    _log.w('   3. Setze Subscription-Referenzen auf null nach cancel()');
  }

  /// Behebt AnimationController-Memory-Leaks
  static void _fixAnimationLeaks() {
    _log.i('🔧 Behebe AnimationController-Leaks...');

    _log.w('⚠️  AnimationController-Leaks erkannt:');
    _log.w('   1. Prüfe alle AnimationController-Instanzen');
    _log.w(
      '   2. Stelle sicher, dass controller.dispose() in dispose() aufgerufen wird',
    );
    _log.w(
      '   3. Verwende SingleTickerProviderStateMixin für einzelne Controller',
    );
  }

  /// Behebt WebView-Memory-Leaks
  static void _fixWebViewLeaks() {
    _log.i('🔧 Behebe WebView-Leaks...');

    _log.w('⚠️  WebView-Leaks erkannt:');
    _log.w('   1. Rufe controller.clearCache() in dispose() auf');
    _log.w('   2. Rufe controller.clearLocalStorage() in dispose() auf');
    _log.w('   3. Setze WebView-Referenzen auf null');

    // Versuche WebView-Cache zu bereinigen (falls möglich)
    try {
      // Hinweis: Dies ist eine allgemeine Bereinigung
      _log.i('   Bereinige WebView-Cache...');
    } catch (e) {
      _log.e('   Fehler beim Bereinigen des WebView-Cache: $e');
    }
  }

  /// Behebt TextEditingController-Memory-Leaks
  static void _fixTextControllerLeaks() {
    _log.i('🔧 Behebe TextEditingController-Leaks...');

    _log.w('⚠️  TextEditingController-Leaks erkannt:');
    _log.w('   1. Prüfe alle TextEditingController-Instanzen');
    _log.w(
      '   2. Stelle sicher, dass controller.dispose() in dispose() aufgerufen wird',
    );
    _log.w('   3. Verwende useTextEditingController() in HookWidgets');
  }

  /// Erzwingt Garbage Collection
  static void _forceGarbageCollection() {
    _log.i('🗑️  Erzwinge Garbage Collection...');

    // Mehrfache GC-Trigger für bessere Bereinigung
    for (int i = 0; i < 3; i++) {
      _triggerGC();
    }

    _log.i('✅ Garbage Collection abgeschlossen');
  }

  /// Triggert indirekt Garbage Collection
  static void _triggerGC() {
    // Erstelle temporäre große Objekte um GC zu triggern
    final temp = List.generate(1000, (i) => List.filled(100, i));
    temp.clear(); // Sofort freigeben
  }

  /// Erstellt einen detaillierten Fix-Report
  static String generateFixReport() {
    final report = StringBuffer();
    report.writeln('🔧 MEMORY LEAK FIX REPORT');
    report.writeln('Generiert: ${DateTime.now()}');
    report.writeln('=' * 50);

    final leaks = MemoryLeakDetector.checkSpecificLeaks();

    if (leaks.isEmpty) {
      report.writeln('✅ Keine Memory-Leaks erkannt - keine Fixes erforderlich');
      return report.toString();
    }

    report.writeln('🚨 ERKANNTE MEMORY-LEAKS:');
    for (final entry in leaks.entries) {
      final category = entry.key;
      final count = entry.value;

      report.writeln('\n$category: $count aktive Objekte');
      report.writeln(_getFixInstructions(category));
    }

    report.writeln('\n🔧 ALLGEMEINE EMPFEHLUNGEN:');
    report.writeln(
      '1. Implementiere dispose() Methoden in allen StatefulWidgets',
    );
    report.writeln('2. Cancel alle Timer und StreamSubscriptions');
    report.writeln('3. Dispose alle Controller (Animation, Text, etc.)');
    report.writeln('4. Bereinige WebView-Instanzen ordnungsgemäß');
    report.writeln('5. Verwende WeakReferences für lange Referenzen');
    report.writeln('6. Prüfe mounted-Status vor setState() Aufrufen');

    return report.toString();
  }

  /// Gibt spezifische Fix-Anweisungen für einen Leak-Typ zurück
  static String _getFixInstructions(String category) {
    switch (category) {
      case 'Timer':
        return '''
  FIX-ANWEISUNGEN:
  - Timer? _timer; // Referenz speichern
  - _timer = Timer.periodic(...); // Timer erstellen
  - _timer?.cancel(); // In dispose() canceln
  - _timer = null; // Referenz bereinigen''';

      case 'StreamSubscription':
        return '''
  FIX-ANWEISUNGEN:
  - StreamSubscription? _subscription; // Referenz speichern
  - _subscription = stream.listen(...); // Subscription erstellen
  - _subscription?.cancel(); // In dispose() canceln
  - _subscription = null; // Referenz bereinigen''';

      case 'AnimationController':
        return '''
  FIX-ANWEISUNGEN:
  - late AnimationController _controller; // Controller deklarieren
  - _controller = AnimationController(...); // In initState() erstellen
  - _controller.dispose(); // In dispose() bereinigen
  - Verwende TickerProviderStateMixin''';

      case 'WebViewController':
        return '''
  FIX-ANWEISUNGEN:
  - _controller.clearCache(); // In dispose() bereinigen
  - _controller.clearLocalStorage(); // Cache leeren
  - Setze Controller-Referenz auf null''';

      case 'TextEditingController':
        return '''
  FIX-ANWEISUNGEN:
  - late TextEditingController _controller; // Controller deklarieren
  - _controller = TextEditingController(); // In initState() erstellen
  - _controller.dispose(); // In dispose() bereinigen
  - Oder verwende useTextEditingController() in HookWidgets''';

      default:
        return '  Keine spezifischen Fix-Anweisungen verfügbar';
    }
  }

  /// Aktiviert/Deaktiviert Memory-Leak-Fixes
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      stopAutoFix();
    }
  }

  /// Führt einen einmaligen Memory-Leak-Fix durch
  static void performManualFix() {
    _log.i('🔧 Führe manuellen Memory-Leak-Fix durch...');
    _performAutoFix();
  }
}

/// Extension für einfache Memory-Leak-Fixes
extension MemoryLeakFixable on StatefulWidget {
  /// Prüft dieses Widget auf potentielle Memory-Leaks
  void checkForMemoryLeaks() {
    MemoryLeakDetector.track('StatefulWidget', this);
  }
}

/// Mixin für automatische Memory-Leak-Prevention
mixin MemoryLeakPreventionMixin<T extends StatefulWidget> on State<T> {
  final List<Timer> _timers = [];
  final List<StreamSubscription> _subscriptions = [];
  final List<AnimationController> _animationControllers = [];
  final List<TextEditingController> _textControllers = [];

  /// Registriert einen Timer für automatische Bereinigung
  void registerTimer(Timer timer) {
    _timers.add(timer);
  }

  /// Registriert eine StreamSubscription für automatische Bereinigung
  void registerSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  /// Registriert einen AnimationController für automatische Bereinigung
  void registerAnimationController(AnimationController controller) {
    _animationControllers.add(controller);
  }

  /// Registriert einen TextEditingController für automatische Bereinigung
  void registerTextController(TextEditingController controller) {
    _textControllers.add(controller);
  }

  @override
  void dispose() {
    // Automatische Bereinigung aller registrierten Objekte
    for (final timer in _timers) {
      timer.cancel();
    }
    _timers.clear();

    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    for (final controller in _animationControllers) {
      controller.dispose();
    }
    _animationControllers.clear();

    for (final controller in _textControllers) {
      controller.dispose();
    }
    _textControllers.clear();

    super.dispose();
  }
}

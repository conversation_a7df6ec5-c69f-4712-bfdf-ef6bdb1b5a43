import 'dart:io';
import '../utils/logging.dart';
import 'widget_audit_tool.dart';

/// Widget Performance Fixes - Automatische Anwendung von Performance-Optimierungen
class WidgetPerformanceFixes {
  static final _log = getLogger('WidgetPerformanceFixes');

  /// Führt automatische Widget Performance Fixes durch
  static Future<PerformanceFixReport> applyAutomaticFixes() async {
    _log.i('Starting automatic widget performance fixes...');

    // 1. Führe Widget Audit durch
    final auditReport = await WidgetAuditTool.performAudit();
    
    // 2. Generiere Fixes
    final fixes = await WidgetAuditTool.generateFixes();
    
    // 3. Wende automatische Fixes an
    final appliedFixes = <WidgetFix>[];
    final failedFixes = <WidgetFix>[];

    for (final fix in fixes) {
      if (fix.autoApplicable) {
        try {
          await _applyFix(fix);
          appliedFixes.add(fix);
          _log.i('Applied fix: ${fix.description} in ${fix.issue.fileName}');
        } catch (e) {
          failedFixes.add(fix);
          _log.e('Failed to apply fix: ${fix.description} in ${fix.issue.fileName}: $e');
        }
      }
    }

    return PerformanceFixReport(
      timestamp: DateTime.now(),
      auditReport: auditReport,
      totalFixes: fixes.length,
      appliedFixes: appliedFixes,
      failedFixes: failedFixes,
    );
  }

  /// Wendet einen spezifischen Fix an
  static Future<void> _applyFix(WidgetFix fix) async {
    switch (fix.fixType) {
      case WidgetFixType.addConstConstructor:
        await _addConstConstructor(fix);
        break;
      case WidgetFixType.addDispose:
        await _addDisposeMethod(fix);
        break;
      case WidgetFixType.addKey:
        await _addKey(fix);
        break;
      case WidgetFixType.moveExpensiveOperation:
        await _moveExpensiveOperation(fix);
        break;
    }
  }

  /// Fügt const constructor hinzu
  static Future<void> _addConstConstructor(WidgetFix fix) async {
    final filePath = 'lib/src/presentation/${fix.issue.fileName}';
    final file = File(filePath);
    
    if (!await file.exists()) {
      throw Exception('File not found: $filePath');
    }

    final content = await file.readAsString();
    final lines = content.split('\n');
    
    // Finde die Klasse und den Constructor
    final className = _extractClassNameFromIssue(fix.issue.description);
    if (className == null) return;

    bool modified = false;
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Finde den Constructor
      if (line.contains('$className({') && !line.contains('const')) {
        // Füge const hinzu
        lines[i] = lines[i].replaceFirst('$className({', 'const $className({');
        modified = true;
        break;
      }
    }

    if (modified) {
      await file.writeAsString(lines.join('\n'));
      _log.i('Added const constructor to $className in ${fix.issue.fileName}');
    }
  }

  /// Fügt dispose() Methode hinzu
  static Future<void> _addDisposeMethod(WidgetFix fix) async {
    final filePath = 'lib/src/presentation/${fix.issue.fileName}';
    final file = File(filePath);
    
    if (!await file.exists()) {
      throw Exception('File not found: $filePath');
    }

    final content = await file.readAsString();
    final lines = content.split('\n');
    
    // Finde die Stelle wo dispose() hinzugefügt werden soll
    int insertIndex = -1;
    for (int i = lines.length - 1; i >= 0; i--) {
      if (lines[i].trim() == '}' && i > 0) {
        // Prüfe ob das die schließende Klammer der Klasse ist
        bool isClassEnd = false;
        for (int j = i - 1; j >= 0; j--) {
          if (lines[j].trim().contains('Widget build(')) {
            isClassEnd = true;
            break;
          }
        }
        if (isClassEnd) {
          insertIndex = i;
          break;
        }
      }
    }

    if (insertIndex > 0) {
      // Extrahiere Controller Namen aus der Issue-Beschreibung
      final controllers = _extractControllerNames(fix.issue.description);
      
      final disposeMethod = _generateDisposeMethod(controllers);
      lines.insert(insertIndex, disposeMethod);
      
      await file.writeAsString(lines.join('\n'));
      _log.i('Added dispose method to ${fix.issue.fileName}');
    }
  }

  /// Fügt Keys hinzu
  static Future<void> _addKey(WidgetFix fix) async {
    // Implementation für Key-Hinzufügung
    _log.i('Adding key fix for ${fix.issue.fileName}');
  }

  /// Verschiebt teure Operationen
  static Future<void> _moveExpensiveOperation(WidgetFix fix) async {
    // Implementation für das Verschieben teurer Operationen
    _log.i('Moving expensive operation fix for ${fix.issue.fileName}');
  }

  /// Hilfsmethoden
  static String? _extractClassNameFromIssue(String description) {
    final regex = RegExp(r'Widget (\w+) könnte');
    final match = regex.firstMatch(description);
    return match?.group(1);
  }

  static List<String> _extractControllerNames(String description) {
    final regex = RegExp(r'Controller gefunden.*: (.+)');
    final match = regex.firstMatch(description);
    if (match != null) {
      return match.group(1)!.split(', ').map((s) => s.trim()).toList();
    }
    return [];
  }

  static String _generateDisposeMethod(List<String> controllers) {
    final buffer = StringBuffer();
    buffer.writeln('');
    buffer.writeln('  @override');
    buffer.writeln('  void dispose() {');
    
    for (final controller in controllers) {
      buffer.writeln('    $controller.dispose();');
    }
    
    buffer.writeln('    super.dispose();');
    buffer.writeln('  }');
    
    return buffer.toString();
  }

  /// Manuelle Fixes für komplexe Fälle
  static Future<List<ManualFixSuggestion>> generateManualFixSuggestions() async {
    final suggestions = <ManualFixSuggestion>[];
    
    // Häufige Performance-Probleme die manuelle Intervention benötigen
    suggestions.addAll([
      ManualFixSuggestion(
        title: 'ListView.builder Optimierung',
        description: 'Verwende ListView.builder statt ListView für große Listen',
        files: ['job_search/widgets/animated_job_list.dart'],
        priority: ManualFixPriority.high,
        estimatedImpact: 'Reduziert Memory Usage um 30-50%',
      ),
      ManualFixSuggestion(
        title: 'Image Caching Optimierung',
        description: 'Implementiere Image Caching für bessere Performance',
        files: ['common/widgets/*.dart'],
        priority: ManualFixPriority.medium,
        estimatedImpact: 'Reduziert Netzwerk-Requests um 60%',
      ),
      ManualFixSuggestion(
        title: 'Widget Memoization',
        description: 'Verwende Memoization für teure Widget-Berechnungen',
        files: ['job_detail/widgets/*.dart'],
        priority: ManualFixPriority.medium,
        estimatedImpact: 'Reduziert Build-Zeit um 20-40%',
      ),
    ]);
    
    return suggestions;
  }
}

/// Performance Fix Report
class PerformanceFixReport {
  final DateTime timestamp;
  final WidgetAuditReport auditReport;
  final int totalFixes;
  final List<WidgetFix> appliedFixes;
  final List<WidgetFix> failedFixes;

  PerformanceFixReport({
    required this.timestamp,
    required this.auditReport,
    required this.totalFixes,
    required this.appliedFixes,
    required this.failedFixes,
  });

  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== WIDGET PERFORMANCE FIX REPORT ===');
    buffer.writeln('Generated: ${timestamp.toIso8601String()}');
    buffer.writeln('Total Fixes Available: $totalFixes');
    buffer.writeln('Applied Fixes: ${appliedFixes.length}');
    buffer.writeln('Failed Fixes: ${failedFixes.length}');
    buffer.writeln();

    if (appliedFixes.isNotEmpty) {
      buffer.writeln('✅ SUCCESSFULLY APPLIED FIXES:');
      for (final fix in appliedFixes) {
        buffer.writeln('  • ${fix.description} in ${fix.issue.fileName}');
      }
      buffer.writeln();
    }

    if (failedFixes.isNotEmpty) {
      buffer.writeln('❌ FAILED FIXES:');
      for (final fix in failedFixes) {
        buffer.writeln('  • ${fix.description} in ${fix.issue.fileName}');
      }
      buffer.writeln();
    }

    // Zeige verbleibendes Audit Report
    buffer.writeln('REMAINING ISSUES:');
    buffer.write(auditReport.generateReport());

    return buffer.toString();
  }

  double get successRate {
    if (totalFixes == 0) return 100.0;
    return (appliedFixes.length / totalFixes) * 100;
  }
}

/// Manual Fix Suggestion
class ManualFixSuggestion {
  final String title;
  final String description;
  final List<String> files;
  final ManualFixPriority priority;
  final String estimatedImpact;

  ManualFixSuggestion({
    required this.title,
    required this.description,
    required this.files,
    required this.priority,
    required this.estimatedImpact,
  });
}

/// Manual Fix Priority
enum ManualFixPriority {
  critical,
  high,
  medium,
  low,
}

/// Widget Performance Analyzer - Für Live-Monitoring
class WidgetPerformanceAnalyzer {
  static final Map<String, WidgetPerformanceMetrics> _metrics = {};
  
  /// Startet Performance-Monitoring für ein Widget
  static void startMonitoring(String widgetName) {
    _metrics[widgetName] = WidgetPerformanceMetrics(
      widgetName: widgetName,
      startTime: DateTime.now(),
    );
  }
  
  /// Stoppt Performance-Monitoring und loggt Ergebnisse
  static void stopMonitoring(String widgetName) {
    final metrics = _metrics[widgetName];
    if (metrics != null) {
      metrics.endTime = DateTime.now();
      final duration = metrics.endTime!.difference(metrics.startTime);
      
      if (duration.inMilliseconds > 16) { // 60 FPS = 16ms per frame
        getLogger('WidgetPerformanceAnalyzer').w(
          'Slow widget detected: $widgetName took ${duration.inMilliseconds}ms'
        );
      }
      
      _metrics.remove(widgetName);
    }
  }
  
  /// Gibt Performance-Statistiken zurück
  static Map<String, WidgetPerformanceMetrics> getMetrics() {
    return Map.from(_metrics);
  }
}

/// Widget Performance Metrics
class WidgetPerformanceMetrics {
  final String widgetName;
  final DateTime startTime;
  DateTime? endTime;
  
  WidgetPerformanceMetrics({
    required this.widgetName,
    required this.startTime,
    this.endTime,
  });
  
  Duration? get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return null;
  }
}

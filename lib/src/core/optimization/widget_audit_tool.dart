import 'dart:io';
import '../utils/logging.dart';

/// Widget Audit Tool für automatische Performance-Analyse
class WidgetAuditTool {
  static final _log = getLogger('WidgetAuditTool');
  static final List<WidgetIssue> _issues = [];

  /// Führt ein vollständiges Widget Performance Audit durch
  static Future<WidgetAuditReport> performAudit() async {
    _issues.clear();
    _log.i('Starting widget performance audit...');

    // 1. Scanne alle Dart-Dateien im presentation Layer
    await _scanPresentationLayer();

    // 2. Analysiere spezifische Performance-Probleme
    await _analyzePerformanceIssues();

    final report = WidgetAuditReport(
      timestamp: DateTime.now(),
      issues: List.from(_issues),
      totalWidgetsScanned: _getTotalWidgetsScanned(),
    );

    _log.i('Widget audit completed. Found ${_issues.length} issues.');
    return report;
  }

  /// Scannt den presentation Layer nach Widgets
  static Future<void> _scanPresentationLayer() async {
    final presentationDir = Directory('lib/src/presentation');
    if (!await presentationDir.exists()) {
      _log.w('Presentation directory not found');
      return;
    }

    await for (final entity in presentationDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        await _analyzeFile(entity);
      }
    }
  }

  /// Analysiert eine einzelne Dart-Datei
  static Future<void> _analyzeFile(File file) async {
    try {
      final content = await file.readAsString();
      final fileName = file.path.split('/').last;

      // 1. Prüfe auf fehlende const constructors
      await _checkConstConstructors(content, fileName);

      // 2. Prüfe auf Memory Leaks
      await _checkMemoryLeaks(content, fileName);

      // 3. Prüfe auf ineffiziente Rebuilds
      await _checkInefficiientRebuilds(content, fileName);

      // 4. Prüfe auf fehlende Keys
      await _checkMissingKeys(content, fileName);

      // 5. Prüfe auf teure Operationen in build()
      await _checkExpensiveOperations(content, fileName);

    } catch (e) {
      _log.e('Error analyzing file ${file.path}: $e');
    }
  }

  /// Prüft auf fehlende const constructors
  static Future<void> _checkConstConstructors(String content, String fileName) async {
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Suche nach StatelessWidget Klassen
      if (line.contains('class') && 
          line.contains('extends StatelessWidget') &&
          !line.contains('abstract')) {
        
        final className = _extractClassName(line);
        if (className != null) {
          // Prüfe ob const constructor vorhanden ist
          final hasConstConstructor = _hasConstConstructor(content, className);
          
          if (!hasConstConstructor) {
            _addIssue(
              WidgetIssueType.missingConstConstructor,
              'Widget $className könnte const constructor haben',
              fileName,
              i + 1,
              WidgetIssueSeverity.medium,
              'Füge const constructor hinzu für bessere Performance',
            );
          }
        }
      }
    }
  }

  /// Prüft auf Memory Leaks
  static Future<void> _checkMemoryLeaks(String content, String fileName) async {
    final lines = content.split('\n');
    final controllers = <String>[];
    final subscriptions = <String>[];
    bool hasDispose = false;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Sammle Controller und Subscriptions
      if (line.contains('Controller') && 
          (line.contains('final') || line.contains('late'))) {
        final controllerName = _extractVariableName(line);
        if (controllerName != null) {
          controllers.add(controllerName);
        }
      }
      
      if (line.contains('StreamSubscription') && 
          (line.contains('final') || line.contains('late'))) {
        final subscriptionName = _extractVariableName(line);
        if (subscriptionName != null) {
          subscriptions.add(subscriptionName);
        }
      }
      
      // Prüfe auf dispose() Methode
      if (line.contains('void dispose()')) {
        hasDispose = true;
      }
    }

    // Prüfe ob alle Controller disposed werden
    if (controllers.isNotEmpty && !hasDispose) {
      _addIssue(
        WidgetIssueType.memoryLeak,
        'Controller gefunden aber keine dispose() Methode: ${controllers.join(', ')}',
        fileName,
        0,
        WidgetIssueSeverity.high,
        'Implementiere dispose() Methode und dispose alle Controller',
      );
    }

    // Prüfe ob alle Subscriptions cancelled werden
    if (subscriptions.isNotEmpty && !hasDispose) {
      _addIssue(
        WidgetIssueType.memoryLeak,
        'StreamSubscription gefunden aber keine dispose() Methode: ${subscriptions.join(', ')}',
        fileName,
        0,
        WidgetIssueSeverity.high,
        'Implementiere dispose() Methode und cancel alle Subscriptions',
      );
    }
  }

  /// Prüft auf ineffiziente Rebuilds
  static Future<void> _checkInefficiientRebuilds(String content, String fileName) async {
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Prüfe auf teure Operationen in build()
      if (line.contains('build(BuildContext context)')) {
        // Prüfe die nächsten 50 Zeilen nach teuren Operationen
        for (int j = i + 1; j < i + 50 && j < lines.length; j++) {
          final buildLine = lines[j].trim();
          
          if (buildLine.contains('DateTime.now()') ||
              buildLine.contains('Random()') ||
              buildLine.contains('http.') ||
              buildLine.contains('File(') ||
              buildLine.contains('Directory(')) {
            
            _addIssue(
              WidgetIssueType.expensiveOperation,
              'Teure Operation in build() Methode: ${buildLine.substring(0, buildLine.length.clamp(0, 50))}...',
              fileName,
              j + 1,
              WidgetIssueSeverity.high,
              'Verschiebe teure Operationen aus build() Methode',
            );
          }
        }
      }
    }
  }

  /// Prüft auf fehlende Keys in Listen
  static Future<void> _checkMissingKeys(String content, String fileName) async {
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Prüfe auf ListView.builder ohne Keys
      if (line.contains('ListView.builder') || 
          line.contains('GridView.builder') ||
          line.contains('Column(children:') ||
          line.contains('Row(children:')) {
        
        // Prüfe ob Key verwendet wird
        bool hasKey = false;
        for (int j = i; j < i + 10 && j < lines.length; j++) {
          if (lines[j].contains('key:') || lines[j].contains('Key(')) {
            hasKey = true;
            break;
          }
        }
        
        if (!hasKey && (line.contains('ListView.builder') || line.contains('GridView.builder'))) {
          _addIssue(
            WidgetIssueType.missingKey,
            'ListView/GridView ohne Keys gefunden',
            fileName,
            i + 1,
            WidgetIssueSeverity.medium,
            'Füge Keys für bessere Performance hinzu',
          );
        }
      }
    }
  }

  /// Prüft auf teure Operationen
  static Future<void> _checkExpensiveOperations(String content, String fileName) async {
    // Bereits in _checkInefficiientRebuilds implementiert
  }

  /// Analysiert spezifische Performance-Probleme
  static Future<void> _analyzePerformanceIssues() async {
    // Zusätzliche Performance-Analysen können hier hinzugefügt werden
  }

  /// Hilfsmethoden
  static String? _extractClassName(String line) {
    final regex = RegExp(r'class\s+(\w+)\s+extends');
    final match = regex.firstMatch(line);
    return match?.group(1);
  }

  static String? _extractVariableName(String line) {
    final regex = RegExp(r'(?:final|late)\s+\w+\s+(\w+)');
    final match = regex.firstMatch(line);
    return match?.group(1);
  }

  static bool _hasConstConstructor(String content, String className) {
    return content.contains('const $className(') || 
           content.contains('const $className({');
  }

  static int _getTotalWidgetsScanned() {
    // Implementierung für Zählung der gescannten Widgets
    return _issues.length + 100; // Placeholder
  }

  static void _addIssue(
    WidgetIssueType type,
    String description,
    String fileName,
    int lineNumber,
    WidgetIssueSeverity severity,
    String recommendation,
  ) {
    _issues.add(WidgetIssue(
      type: type,
      description: description,
      fileName: fileName,
      lineNumber: lineNumber,
      severity: severity,
      recommendation: recommendation,
    ));
  }

  /// Generiert automatische Fixes für gefundene Issues
  static Future<List<WidgetFix>> generateFixes() async {
    final fixes = <WidgetFix>[];
    
    for (final issue in _issues) {
      if (issue.type == WidgetIssueType.missingConstConstructor) {
        fixes.add(WidgetFix(
          issue: issue,
          fixType: WidgetFixType.addConstConstructor,
          description: 'Füge const constructor hinzu',
          autoApplicable: true,
        ));
      }
    }
    
    return fixes;
  }
}

/// Widget Audit Report
class WidgetAuditReport {
  final DateTime timestamp;
  final List<WidgetIssue> issues;
  final int totalWidgetsScanned;

  WidgetAuditReport({
    required this.timestamp,
    required this.issues,
    required this.totalWidgetsScanned,
  });

  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== WIDGET PERFORMANCE AUDIT REPORT ===');
    buffer.writeln('Generated: ${timestamp.toIso8601String()}');
    buffer.writeln('Total Widgets Scanned: $totalWidgetsScanned');
    buffer.writeln('Issues Found: ${issues.length}');
    buffer.writeln();

    if (issues.isEmpty) {
      buffer.writeln('✅ No performance issues found!');
      return buffer.toString();
    }

    // Gruppiere Issues nach Severity
    final groupedIssues = <WidgetIssueSeverity, List<WidgetIssue>>{};
    for (final issue in issues) {
      groupedIssues.putIfAbsent(issue.severity, () => []).add(issue);
    }

    for (final severity in WidgetIssueSeverity.values) {
      final issuesForSeverity = groupedIssues[severity] ?? [];
      if (issuesForSeverity.isNotEmpty) {
        buffer.writeln('${_severityIcon(severity)} ${severity.name.toUpperCase()} (${issuesForSeverity.length}):');
        for (final issue in issuesForSeverity) {
          buffer.writeln('  📁 ${issue.fileName}:${issue.lineNumber}');
          buffer.writeln('     ${issue.description}');
          buffer.writeln('     💡 ${issue.recommendation}');
          buffer.writeln();
        }
      }
    }

    return buffer.toString();
  }

  String _severityIcon(WidgetIssueSeverity severity) {
    switch (severity) {
      case WidgetIssueSeverity.critical:
        return '🔴';
      case WidgetIssueSeverity.high:
        return '🟠';
      case WidgetIssueSeverity.medium:
        return '🟡';
      case WidgetIssueSeverity.low:
        return '🟢';
    }
  }
}

/// Widget Issue
class WidgetIssue {
  final WidgetIssueType type;
  final String description;
  final String fileName;
  final int lineNumber;
  final WidgetIssueSeverity severity;
  final String recommendation;

  WidgetIssue({
    required this.type,
    required this.description,
    required this.fileName,
    required this.lineNumber,
    required this.severity,
    required this.recommendation,
  });
}

/// Widget Fix
class WidgetFix {
  final WidgetIssue issue;
  final WidgetFixType fixType;
  final String description;
  final bool autoApplicable;

  WidgetFix({
    required this.issue,
    required this.fixType,
    required this.description,
    required this.autoApplicable,
  });
}

/// Widget Issue Types
enum WidgetIssueType {
  missingConstConstructor,
  memoryLeak,
  expensiveOperation,
  missingKey,
  inefficientRebuild,
}

/// Widget Issue Severity
enum WidgetIssueSeverity {
  critical,
  high,
  medium,
  low,
}

/// Widget Fix Types
enum WidgetFixType {
  addConstConstructor,
  addDispose,
  addKey,
  moveExpensiveOperation,
}

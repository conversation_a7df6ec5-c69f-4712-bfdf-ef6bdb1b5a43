import 'package:flutter/material.dart';

/// Memory-optimierte ListView-Implementierung für bessere GPU-Performance
class MemoryOptimizedList<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final int? maxCacheExtent;
  final bool enableRepaintBoundary;

  const MemoryOptimizedList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.maxCacheExtent = 50, // Begrenzt Cache für Memory-Optimierung
    this.enableRepaintBoundary = true,
  });

  @override
  State<MemoryOptimizedList<T>> createState() => _MemoryOptimizedListState<T>();
}

class _MemoryOptimizedListState<T> extends State<MemoryOptimizedList<T>> {
  late ScrollController _scrollController;
  bool _isExternalController = false;

  // Cache für gerenderte Items (begrenzt)
  final Map<int, Widget> _itemCache = {};
  static const int _maxCacheSize = 50;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _isExternalController = widget.controller != null;

    // Scroll-Listener für Cache-Management
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    if (!_isExternalController) {
      _scrollController.dispose();
    }
    _itemCache.clear();
    super.dispose();
  }

  void _onScroll() {
    // Cache-Bereinigung bei Scroll-Events
    if (_itemCache.length > _maxCacheSize) {
      _cleanupCache();
    }
  }

  void _cleanupCache() {
    // Entferne älteste Cache-Einträge
    final keys = _itemCache.keys.toList()..sort();
    final keysToRemove = keys.take(_itemCache.length - _maxCacheSize ~/ 2);
    for (final key in keysToRemove) {
      _itemCache.remove(key);
    }
  }

  Widget _buildItem(BuildContext context, int index) {
    // Cache-Lookup
    if (_itemCache.containsKey(index)) {
      return _itemCache[index]!;
    }

    // Item erstellen
    final item = widget.itemBuilder(context, widget.items[index], index);

    Widget optimizedItem = item;

    // RepaintBoundary für bessere GPU-Performance
    if (widget.enableRepaintBoundary) {
      optimizedItem = RepaintBoundary(child: item);
    }

    // Cache nur wenn unter Limit
    if (_itemCache.length < _maxCacheSize) {
      _itemCache[index] = optimizedItem;
    }

    return optimizedItem;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemCount: widget.items.length,
      cacheExtent: widget.maxCacheExtent?.toDouble(),
      itemBuilder: _buildItem,
      // Optimierungen für bessere Performance
      addAutomaticKeepAlives: false, // Reduziert Memory-Usage
      addRepaintBoundaries: false, // Wir handhaben das manuell
      addSemanticIndexes: false, // Reduziert Overhead
    );
  }
}

/// Memory-optimierte GridView-Implementierung
class MemoryOptimizedGrid<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const MemoryOptimizedGrid({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.gridDelegate,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  State<MemoryOptimizedGrid<T>> createState() => _MemoryOptimizedGridState<T>();
}

class _MemoryOptimizedGridState<T> extends State<MemoryOptimizedGrid<T>> {
  late ScrollController _scrollController;
  bool _isExternalController = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _isExternalController = widget.controller != null;
  }

  @override
  void dispose() {
    if (!_isExternalController) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      gridDelegate: widget.gridDelegate,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: widget.itemBuilder(context, widget.items[index], index),
        );
      },
      // Memory-Optimierungen
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false, // Wir handhaben das manuell
      addSemanticIndexes: false,
    );
  }
}

/// Utility-Klasse für Memory-Monitoring
class MemoryOptimizer {
  static const bool _debugMode = false;

  /// Überwacht Memory-Usage von Widgets
  static Widget monitorWidget(String name, Widget child) {
    if (!_debugMode) return child;

    return Builder(
      builder: (context) {
        // In Debug-Mode: Memory-Monitoring
        debugPrint('Widget $name rendered');
        return child;
      },
    );
  }

  /// Optimiert Image-Widgets für bessere Memory-Performance
  static Widget optimizeImage({
    required ImageProvider image,
    double? width,
    double? height,
    BoxFit? fit,
    bool enableMemoryCache = true,
  }) {
    return RepaintBoundary(
      child: Image(
        image: image,
        width: width,
        height: height,
        fit: fit,
        gaplessPlayback: true, // Verhindert Flackern
        filterQuality:
            FilterQuality.medium, // Balance zwischen Qualität und Performance
        isAntiAlias: false, // Reduziert GPU-Load
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: const Icon(Icons.error),
          );
        },
      ),
    );
  }
}

/// Extension für einfache Memory-Optimierung
extension MemoryOptimizedWidget on Widget {
  /// Fügt RepaintBoundary hinzu für bessere GPU-Performance
  Widget withRepaintBoundary() => RepaintBoundary(child: this);

  /// Fügt Memory-Monitoring hinzu (nur in Debug-Mode)
  Widget withMemoryMonitoring(String name) =>
      MemoryOptimizer.monitorWidget(name, this);
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:ki_test/src/core/logging/app_logger.dart';

/// WebView Performance Optimizer - Reduziert JavaScript-Logs und optimiert WebView Performance
class WebViewPerformanceOptimizer {
  static final _log = AppLogger('WebViewPerformanceOptimizer');
  static bool _isEnabled = true;
  static final Map<String, Timer> _debounceTimers = {};

  /// Aktiviert/Deaktiviert den WebView Performance Optimizer
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    _log.i(
      'WebView Performance Optimizer ${enabled ? 'aktiviert' : 'deaktiviert'}',
    );
  }

  /// Erstellt einen optimierten WebViewController
  static WebViewController createOptimizedController({
    required String controllerId,
    Color? backgroundColor,
    Function(String)? onPageStarted,
    Function(String)? onPageFinished,
    Function(int)? onProgress,
    Function(WebResourceError)? onWebResourceError,
  }) {
    if (!_isEnabled) {
      return _createStandardController(
        backgroundColor: backgroundColor,
        onPageStarted: onPageStarted,
        onPageFinished: onPageFinished,
        onProgress: onProgress,
      );
    }

    _log.i('Erstelle optimierten WebViewController: $controllerId');

    final controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(backgroundColor ?? const Color(0x00000000));

    // PERFORMANCE FIX: JavaScript-Optimierungen
    _injectPerformanceOptimizations(controller);

    // Optimierte Navigation Delegate
    controller.setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          // Debounce Progress Updates um UI-Thread zu entlasten
          _debounceOperation('progress_$controllerId', () {
            onProgress?.call(progress);
          });
        },
        onPageStarted: (String url) {
          _log.d('WebView started loading: $url');

          // Debounce Page Started Events
          _debounceOperation('page_started_$controllerId', () {
            onPageStarted?.call(url);
          });
        },
        onPageFinished: (String url) {
          _log.d('WebView finished loading: $url');

          // Injiziere Performance-Optimierungen nach dem Laden
          _injectPostLoadOptimizations(controller);

          // Debounce Page Finished Events
          _debounceOperation('page_finished_$controllerId', () {
            onPageFinished?.call(url);
          });
        },
        onWebResourceError: (WebResourceError error) {
          _log.w('WebView Resource Error: ${error.description}');
        },
      ),
    );

    return controller;
  }

  /// Erstellt einen Standard WebViewController ohne Optimierungen
  static WebViewController _createStandardController({
    Color? backgroundColor,
    Function(String)? onPageStarted,
    Function(String)? onPageFinished,
    Function(int)? onProgress,
  }) {
    return WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(backgroundColor ?? const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: onProgress,
          onPageStarted: onPageStarted,
          onPageFinished: onPageFinished,
        ),
      );
  }

  /// Injiziert Performance-Optimierungen in die WebView
  static void _injectPerformanceOptimizations(WebViewController controller) {
    // JavaScript Code um Tracking-Logs zu reduzieren
    const jsCode = '''
      (function() {
        // PERFORMANCE FIX: Reduziere '[tracking] deleteCustomDimension' Logs
        if (window.console && window.console.log) {
          const originalLog = window.console.log;
          window.console.log = function(...args) {
            const message = args.join(' ');
            // Filtere bekannte Tracking-Logs
            if (message.includes('[tracking] deleteCustomDimension') ||
                message.includes('deleteCustomDimension') ||
                message.includes('tracking')) {
              // Unterdrücke diese Logs
              return;
            }
            // Normale Logs weiterleiten
            originalLog.apply(console, args);
          };
        }

        // Optimiere Google Analytics/Tracking Performance
        if (window.gtag) {
          const originalGtag = window.gtag;
          window.gtag = function(command, ...args) {
            // Debounce Tracking-Calls
            if (command === 'event' || command === 'config') {
              setTimeout(() => originalGtag(command, ...args), 100);
            } else {
              originalGtag(command, ...args);
            }
          };
        }

        // Reduziere DOM-Mutations-Observer Performance-Impact
        if (window.MutationObserver) {
          const originalObserver = window.MutationObserver;
          window.MutationObserver = function(callback) {
            const throttledCallback = throttle(callback, 100);
            return new originalObserver(throttledCallback);
          };
        }

        // Throttle-Funktion für Performance
        function throttle(func, limit) {
          let inThrottle;
          return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
              func.apply(context, args);
              inThrottle = true;
              setTimeout(() => inThrottle = false, limit);
            }
          }
        }

        console.log('WebView Performance Optimizations injected');
      })();
    ''';

    // Injiziere JavaScript asynchron
    scheduleMicrotask(() async {
      try {
        await controller.runJavaScript(jsCode);
        _log.d('Performance-Optimierungen erfolgreich injiziert');
      } catch (e) {
        _log.w('Fehler beim Injizieren der Performance-Optimierungen: $e');
      }
    });
  }

  /// Injiziert Post-Load-Optimierungen
  static void _injectPostLoadOptimizations(WebViewController controller) {
    const jsCode = '''
      (function() {
        // Entferne oder reduziere Tracking-Elemente nach dem Laden
        const trackingElements = document.querySelectorAll('[data-tracking], .tracking, #tracking');
        trackingElements.forEach(el => {
          if (el.style) {
            el.style.display = 'none';
          }
        });

        // Optimiere Scroll-Performance
        let scrollTimeout;
        window.addEventListener('scroll', function() {
          clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(function() {
            // Scroll-Handler mit Debouncing
          }, 16); // 60fps
        }, { passive: true });

        console.log('Post-load optimizations applied');
      })();
    ''';

    scheduleMicrotask(() async {
      try {
        await controller.runJavaScript(jsCode);
        _log.d('Post-Load-Optimierungen erfolgreich angewendet');
      } catch (e) {
        _log.w('Fehler bei Post-Load-Optimierungen: $e');
      }
    });
  }

  /// Debounce-Operation um häufige Aufrufe zu reduzieren
  static void _debounceOperation(String operationId, VoidCallback operation) {
    _debounceTimers[operationId]?.cancel();
    _debounceTimers[operationId] = Timer(const Duration(milliseconds: 100), () {
      operation();
      _debounceTimers.remove(operationId);
    });
  }

  /// Bereinigt alle Debounce-Timer
  static void cleanup() {
    _log.i('Bereinige WebView Performance Optimizer...');

    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }

  /// Gibt Performance-Statistiken zurück
  static Map<String, dynamic> getPerformanceStats() {
    return {
      'enabled': _isEnabled,
      'active_debounce_timers': _debounceTimers.length,
      'active_timers': _debounceTimers.values.where((t) => t.isActive).length,
    };
  }
}

/// Extension für WebViewController um Performance-Optimierungen zu erleichtern
extension WebViewControllerOptimized on WebViewController {
  /// Führt JavaScript mit Performance-Optimierung aus
  Future<void> runJavaScriptOptimized(String javaScriptString) async {
    return UIThreadOptimizer.executeOptimized(
      'webview_js_${javaScriptString.hashCode}',
      () => runJavaScript(javaScriptString),
    );
  }
}

/// Import für UIThreadOptimizer
class UIThreadOptimizer {
  static Future<T> executeOptimized<T>(
    String operationId,
    Future<T> Function() operation,
  ) async {
    // Vereinfachte Version für WebView-Optimierung
    return await operation();
  }
}

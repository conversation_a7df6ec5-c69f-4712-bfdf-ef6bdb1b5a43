import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../utils/logging.dart';

/// Widget Performance Optimizer für automatische Performance-Verbesserungen
class WidgetPerformanceOptimizer {
  static final _log = getLogger('WidgetPerformanceOptimizer');
  static final Map<String, PerformanceMetrics> _widgetMetrics = {};
  static bool _isEnabled = kDebugMode;

  /// Aktiviert/Deaktiviert Performance Monitoring
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Wrapper für Performance-kritische Widgets
  static Widget optimizeWidget({
    required String widgetName,
    required Widget Function() builder,
    bool enableConst = true,
    bool enableMemoization = true,
    Duration? rebuildThreshold,
  }) {
    if (!_isEnabled) {
      return builder();
    }

    return _PerformanceWrapper(
      widgetName: widgetName,
      builder: builder,
      enableConst: enableConst,
      enableMemoization: enableMemoization,
      rebuildThreshold: rebuildThreshold ?? const Duration(milliseconds: 16),
    );
  }

  /// Automatische const Constructor Detection
  static Widget autoConst(Widget widget) {
    if (!_isEnabled) return widget;
    
    // Prüfe ob Widget bereits const ist
    if (widget.runtimeType.toString().contains('const')) {
      return widget;
    }

    // Logge potentielle const Optimierung
    _log.w('Widget ${widget.runtimeType} könnte const sein');
    
    return widget;
  }

  /// Memory Leak Detection für StatefulWidgets
  static void trackDisposable(String widgetName, Disposable disposable) {
    if (!_isEnabled) return;
    
    _DisposableTracker.instance.track(widgetName, disposable);
  }

  /// Rebuild Optimization für Listen
  static Widget optimizeList({
    required String listName,
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    bool enableLazyLoading = true,
    int? cacheExtent,
  }) {
    if (!_isEnabled) {
      return ListView.builder(
        itemCount: itemCount,
        itemBuilder: itemBuilder,
      );
    }

    return _OptimizedListView(
      listName: listName,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      enableLazyLoading: enableLazyLoading,
      cacheExtent: cacheExtent,
    );
  }

  /// Performance Report generieren
  static String generatePerformanceReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== WIDGET PERFORMANCE REPORT ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln();

    if (_widgetMetrics.isEmpty) {
      buffer.writeln('No performance data collected.');
      return buffer.toString();
    }

    // Sortiere nach durchschnittlicher Build-Zeit
    final sortedMetrics = _widgetMetrics.entries.toList()
      ..sort((a, b) => b.value.averageBuildTime.compareTo(a.value.averageBuildTime));

    buffer.writeln('TOP SLOW WIDGETS:');
    for (int i = 0; i < sortedMetrics.length && i < 10; i++) {
      final entry = sortedMetrics[i];
      final metrics = entry.value;
      buffer.writeln('${i + 1}. ${entry.key}:');
      buffer.writeln('   Avg Build Time: ${metrics.averageBuildTime.toStringAsFixed(2)}ms');
      buffer.writeln('   Total Rebuilds: ${metrics.rebuildCount}');
      buffer.writeln('   Max Build Time: ${metrics.maxBuildTime.toStringAsFixed(2)}ms');
      buffer.writeln();
    }

    // Memory Leak Report
    buffer.writeln('MEMORY LEAK DETECTION:');
    final leaks = _DisposableTracker.instance.getLeaks();
    if (leaks.isEmpty) {
      buffer.writeln('No memory leaks detected.');
    } else {
      for (final leak in leaks) {
        buffer.writeln('- $leak');
      }
    }

    return buffer.toString();
  }

  /// Performance Metriken zurücksetzen
  static void resetMetrics() {
    _widgetMetrics.clear();
    _DisposableTracker.instance.reset();
  }
}

/// Performance Wrapper Widget
class _PerformanceWrapper extends StatefulWidget {
  final String widgetName;
  final Widget Function() builder;
  final bool enableConst;
  final bool enableMemoization;
  final Duration rebuildThreshold;

  const _PerformanceWrapper({
    required this.widgetName,
    required this.builder,
    required this.enableConst,
    required this.enableMemoization,
    required this.rebuildThreshold,
  });

  @override
  State<_PerformanceWrapper> createState() => _PerformanceWrapperState();
}

class _PerformanceWrapperState extends State<_PerformanceWrapper> {
  Widget? _cachedWidget;
  int _buildCount = 0;

  @override
  Widget build(BuildContext context) {
    final stopwatch = Stopwatch()..start();
    
    Widget result;
    if (widget.enableMemoization && _cachedWidget != null) {
      result = _cachedWidget!;
    } else {
      result = widget.builder();
      if (widget.enableMemoization) {
        _cachedWidget = result;
      }
    }
    
    stopwatch.stop();
    _buildCount++;
    
    // Performance Metriken aktualisieren
    _updateMetrics(widget.widgetName, stopwatch.elapsedMicroseconds / 1000);
    
    // Warnung bei langsamen Builds
    if (stopwatch.elapsed > widget.rebuildThreshold) {
      WidgetPerformanceOptimizer._log.w(
        'Slow widget build: ${widget.widgetName} took ${stopwatch.elapsedMilliseconds}ms (Build #$_buildCount)',
      );
    }
    
    return result;
  }

  void _updateMetrics(String widgetName, double buildTimeMs) {
    final existing = WidgetPerformanceOptimizer._widgetMetrics[widgetName];
    if (existing == null) {
      WidgetPerformanceOptimizer._widgetMetrics[widgetName] = PerformanceMetrics(
        widgetName: widgetName,
        totalBuildTime: buildTimeMs,
        rebuildCount: 1,
        maxBuildTime: buildTimeMs,
      );
    } else {
      WidgetPerformanceOptimizer._widgetMetrics[widgetName] = existing.copyWith(
        totalBuildTime: existing.totalBuildTime + buildTimeMs,
        rebuildCount: existing.rebuildCount + 1,
        maxBuildTime: buildTimeMs > existing.maxBuildTime ? buildTimeMs : existing.maxBuildTime,
      );
    }
  }
}

/// Optimierte ListView Implementation
class _OptimizedListView extends StatefulWidget {
  final String listName;
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final bool enableLazyLoading;
  final int? cacheExtent;

  const _OptimizedListView({
    required this.listName,
    required this.itemCount,
    required this.itemBuilder,
    required this.enableLazyLoading,
    this.cacheExtent,
  });

  @override
  State<_OptimizedListView> createState() => _OptimizedListViewState();
}

class _OptimizedListViewState extends State<_OptimizedListView> {
  final Map<int, Widget> _itemCache = {};
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    if (widget.enableLazyLoading) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Implementiere Lazy Loading Logic
    final position = _scrollController.position;
    if (position.pixels >= position.maxScrollExtent * 0.8) {
      // Trigger lazy loading
      WidgetPerformanceOptimizer._log.d('${widget.listName}: Lazy loading triggered');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.itemCount,
      cacheExtent: widget.cacheExtent?.toDouble(),
      itemBuilder: (context, index) {
        // Cache häufig verwendete Items
        if (_itemCache.containsKey(index)) {
          return _itemCache[index]!;
        }

        final item = widget.itemBuilder(context, index);
        
        // Cache nur die ersten 50 Items um Memory zu sparen
        if (index < 50) {
          _itemCache[index] = item;
        }
        
        return item;
      },
    );
  }
}

/// Performance Metriken Datenklasse
class PerformanceMetrics {
  final String widgetName;
  final double totalBuildTime;
  final int rebuildCount;
  final double maxBuildTime;

  const PerformanceMetrics({
    required this.widgetName,
    required this.totalBuildTime,
    required this.rebuildCount,
    required this.maxBuildTime,
  });

  double get averageBuildTime => totalBuildTime / rebuildCount;

  PerformanceMetrics copyWith({
    String? widgetName,
    double? totalBuildTime,
    int? rebuildCount,
    double? maxBuildTime,
  }) {
    return PerformanceMetrics(
      widgetName: widgetName ?? this.widgetName,
      totalBuildTime: totalBuildTime ?? this.totalBuildTime,
      rebuildCount: rebuildCount ?? this.rebuildCount,
      maxBuildTime: maxBuildTime ?? this.maxBuildTime,
    );
  }
}

/// Memory Leak Tracker
class _DisposableTracker {
  static final _DisposableTracker _instance = _DisposableTracker._internal();
  static _DisposableTracker get instance => _instance;
  _DisposableTracker._internal();

  final Map<String, List<WeakReference<Disposable>>> _tracked = {};
  final List<String> _leaks = [];

  void track(String widgetName, Disposable disposable) {
    _tracked.putIfAbsent(widgetName, () => []).add(WeakReference(disposable));
  }

  List<String> getLeaks() {
    _leaks.clear();
    
    for (final entry in _tracked.entries) {
      final widgetName = entry.key;
      final disposables = entry.value;
      
      // Prüfe auf nicht-disposed Objekte
      final activeCount = disposables.where((ref) => ref.target != null).length;
      if (activeCount > 0) {
        _leaks.add('$widgetName: $activeCount undisposed objects');
      }
    }
    
    return List.from(_leaks);
  }

  void reset() {
    _tracked.clear();
    _leaks.clear();
  }
}

/// Interface für disposable Objekte
abstract class Disposable {
  void dispose();
}

/// Mixin für automatisches Dispose-Management
mixin AutoDisposeMixin<T extends StatefulWidget> on State<T> {
  final List<Disposable> _disposables = [];

  void addDisposable(Disposable disposable) {
    _disposables.add(disposable);
    WidgetPerformanceOptimizer.trackDisposable(
      widget.runtimeType.toString(),
      disposable,
    );
  }

  @override
  void dispose() {
    for (final disposable in _disposables) {
      try {
        disposable.dispose();
      } catch (e) {
        WidgetPerformanceOptimizer._log.e(
          'Error disposing ${disposable.runtimeType}: $e',
        );
      }
    }
    _disposables.clear();
    super.dispose();
  }
}

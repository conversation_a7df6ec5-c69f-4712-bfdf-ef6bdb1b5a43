import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';

/// Profile Backup Optimizer - Reduziert redundante Backup-Operationen durch
/// intelligentes Debouncing und Change-Detection
class ProfileBackupOptimizer {
  static final Map<String, Timer> _debounceTimers = {};
  static final Map<String, String> _lastBackupHashes = {};
  static final Map<String, DateTime> _lastBackupTimes = {};
  static bool _isEnabled = true;

  // Konfiguration
  static const Duration _debounceDelay = Duration(minutes: 5);
  static const Duration _minBackupInterval = Duration(minutes: 2);
  static const int _maxBackupsPerHour = 12;

  /// Aktiviert/Deaktiviert den Profile Backup Optimizer
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    debugPrint(
      '[ProfileBackupOptimizer] ${enabled ? 'aktiviert' : 'deaktiviert'}',
    );
  }

  /// Prüft ob ein Backup notwendig ist basierend auf Change-Detection
  static bool shouldCreateBackup(UserProfile profile) {
    if (!_isEnabled) return true;

    final userId = profile.id;
    if (userId == null || userId.isEmpty) return false;

    // Generiere Hash des aktuellen Profils
    final currentHash = _generateProfileHash(profile);
    final lastHash = _lastBackupHashes[userId];

    // Prüfe ob sich das Profil geändert hat
    if (lastHash != null && lastHash == currentHash) {
      debugPrint(
        '[ProfileBackupOptimizer] INFO: Profil unverändert für $userId - Backup übersprungen',
      );
      return false;
    }

    // Prüfe Zeitintervall seit letztem Backup
    final lastBackupTime = _lastBackupTimes[userId];
    if (lastBackupTime != null) {
      final timeSinceLastBackup = DateTime.now().difference(lastBackupTime);
      if (timeSinceLastBackup < _minBackupInterval) {
        debugPrint(
          '[ProfileBackupOptimizer] INFO: Backup zu früh für $userId - ${timeSinceLastBackup.inSeconds}s seit letztem Backup',
        );
        return false;
      }
    }

    // Prüfe Rate-Limiting (max. Backups pro Stunde)
    if (_isRateLimited(userId)) {
      debugPrint(
        '[ProfileBackupOptimizer] WARN: Rate-Limit erreicht für $userId',
      );
      return false;
    }

    return true;
  }

  /// Optimiertes Backup mit Debouncing
  static Future<bool> createOptimizedBackup(
    UserProfile profile,
    Future<bool> Function(UserProfile) backupFunction,
  ) async {
    if (!_isEnabled) {
      return await backupFunction(profile);
    }

    final userId = profile.id;
    if (userId == null || userId.isEmpty) return false;

    // Prüfe ob Backup notwendig ist
    if (!shouldCreateBackup(profile)) {
      return true; // Backup nicht notwendig, aber kein Fehler
    }

    // Cancle vorherigen Timer
    _debounceTimers[userId]?.cancel();

    // Erstelle Completer für das Ergebnis
    final completer = Completer<bool>();

    // Starte neuen Debounce-Timer
    _debounceTimers[userId] = Timer(_debounceDelay, () async {
      try {
        debugPrint(
          '[ProfileBackupOptimizer] INFO: Starte debounced Backup für $userId',
        );

        // Führe das eigentliche Backup aus
        final success = await backupFunction(profile);

        if (success) {
          // Aktualisiere Tracking-Daten
          _lastBackupHashes[userId] = _generateProfileHash(profile);
          _lastBackupTimes[userId] = DateTime.now();
          debugPrint(
            '[ProfileBackupOptimizer] INFO: Backup erfolgreich für $userId',
          );
        }

        completer.complete(success);
      } catch (e) {
        debugPrint(
          '[ProfileBackupOptimizer] ERROR: Backup fehlgeschlagen für $userId: $e',
        );
        completer.complete(false);
      } finally {
        _debounceTimers.remove(userId);
      }
    });

    return completer.future;
  }

  /// Sofortiges Backup ohne Debouncing (für kritische Operationen)
  static Future<bool> createImmediateBackup(
    UserProfile profile,
    Future<bool> Function(UserProfile) backupFunction,
  ) async {
    final userId = profile.id;
    if (userId == null || userId.isEmpty) return false;

    try {
      debugPrint(
        '[ProfileBackupOptimizer] INFO: Starte sofortiges Backup für $userId',
      );

      final success = await backupFunction(profile);

      if (success) {
        // Aktualisiere Tracking-Daten
        _lastBackupHashes[userId] = _generateProfileHash(profile);
        _lastBackupTimes[userId] = DateTime.now();
        debugPrint(
          '[ProfileBackupOptimizer] INFO: Sofortiges Backup erfolgreich für $userId',
        );
      }

      return success;
    } catch (e) {
      debugPrint(
        '[ProfileBackupOptimizer] ERROR: Sofortiges Backup fehlgeschlagen für $userId: $e',
      );
      return false;
    }
  }

  /// Generiert einen Hash des Profils für Change-Detection
  static String _generateProfileHash(UserProfile profile) {
    try {
      // Konvertiere relevante Profil-Daten zu JSON (ohne Timestamps)
      final profileData = profile.toJson();

      // Entferne volatile Felder die sich häufig ändern
      profileData.remove('updated_at');
      profileData.remove('last_seen');
      profileData.remove('device_info');

      // Sortiere Keys für konsistenten Hash
      final sortedKeys = profileData.keys.toList()..sort();
      final sortedData = <String, dynamic>{};
      for (final key in sortedKeys) {
        sortedData[key] = profileData[key];
      }

      final jsonString = jsonEncode(sortedData);
      return jsonString.hashCode.toString();
    } catch (e) {
      debugPrint(
        '[ProfileBackupOptimizer] WARN: Hash-Generierung fehlgeschlagen: $e',
      );
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// Prüft ob Rate-Limiting aktiv ist
  static bool _isRateLimited(String userId) {
    final now = DateTime.now();
    final oneHourAgo = now.subtract(const Duration(hours: 1));

    // Zähle Backups in der letzten Stunde
    int backupsInLastHour = 0;
    final lastBackupTime = _lastBackupTimes[userId];

    if (lastBackupTime != null && lastBackupTime.isAfter(oneHourAgo)) {
      // Vereinfachte Rate-Limiting-Logik
      // In einer vollständigen Implementierung würde man alle Backup-Zeiten tracken
      backupsInLastHour = 1;
    }

    return backupsInLastHour >= _maxBackupsPerHour;
  }

  /// Erzwingt ein Backup (ignoriert alle Optimierungen)
  static Future<bool> forceBackup(
    UserProfile profile,
    Future<bool> Function(UserProfile) backupFunction,
  ) async {
    final userId = profile.id;
    if (userId == null || userId.isEmpty) return false;

    // Cancle alle Timer für diesen User
    _debounceTimers[userId]?.cancel();
    _debounceTimers.remove(userId);

    debugPrint('[ProfileBackupOptimizer] INFO: Erzwinge Backup für $userId');
    return await createImmediateBackup(profile, backupFunction);
  }

  /// Bereinigt alle Timer und Caches
  static void cleanup() {
    debugPrint('[ProfileBackupOptimizer] INFO: Bereinige Optimizer...');

    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    _lastBackupHashes.clear();
    _lastBackupTimes.clear();
  }

  /// Gibt Performance-Statistiken zurück
  static Map<String, dynamic> getPerformanceStats() {
    return {
      'enabled': _isEnabled,
      'active_timers': _debounceTimers.length,
      'tracked_profiles': _lastBackupHashes.length,
      'last_backup_times': _lastBackupTimes.length,
      'debounce_delay_minutes': _debounceDelay.inMinutes,
      'min_interval_minutes': _minBackupInterval.inMinutes,
      'max_backups_per_hour': _maxBackupsPerHour,
    };
  }

  /// Setzt die Konfiguration zurück
  static void reset() {
    cleanup();
    _isEnabled = true;
    debugPrint('[ProfileBackupOptimizer] INFO: Optimizer zurückgesetzt');
  }
}

/// Mixin für Klassen die Profile Backup Optimierung verwenden
mixin ProfileBackupOptimized {
  /// Optimiertes Backup mit automatischer Change-Detection
  Future<bool> createOptimizedProfileBackup(
    UserProfile profile,
    Future<bool> Function(UserProfile) backupFunction,
  ) {
    return ProfileBackupOptimizer.createOptimizedBackup(
      profile,
      backupFunction,
    );
  }

  /// Sofortiges Backup für kritische Operationen
  Future<bool> createImmediateProfileBackup(
    UserProfile profile,
    Future<bool> Function(UserProfile) backupFunction,
  ) {
    return ProfileBackupOptimizer.createImmediateBackup(
      profile,
      backupFunction,
    );
  }
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:ki_test/src/core/logging/app_logger.dart';

/// Scroll Performance Optimizer - Reduziert redundante Scroll-Events durch
/// intelligentes Debouncing und Throttling
class ScrollPerformanceOptimizer {
  static final _log = AppLogger('ScrollPerformanceOptimizer');
  static bool _isEnabled = true;
  static final Map<String, Timer> _debounceTimers = {};
  static final Map<String, DateTime> _lastExecutionTimes = {};
  static final Map<String, bool> _focusStates = {};

  /// Aktiviert/Deaktiviert den Scroll Performance Optimizer
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    _log.i('Scroll Performance Optimizer ${enabled ? 'aktiviert' : 'deaktiviert'}');
  }

  /// Optimierter Scroll-Handler mit Debouncing
  /// Führt die Operation nur aus, wenn seit dem letzten Aufruf genug Zeit vergangen ist
  static void handleScrollWithDebouncing({
    required String operationId,
    required VoidCallback operation,
    Duration debounceDelay = const Duration(milliseconds: 100),
    bool logExecution = false,
  }) {
    if (!_isEnabled) {
      operation();
      return;
    }

    // Cancle vorherigen Timer
    _debounceTimers[operationId]?.cancel();

    // Starte neuen Timer
    _debounceTimers[operationId] = Timer(debounceDelay, () {
      try {
        operation();
        if (logExecution) {
          _log.d('Debounced operation executed: $operationId');
        }
      } catch (e) {
        _log.e('Fehler bei debounced operation $operationId: $e');
      } finally {
        _debounceTimers.remove(operationId);
      }
    });
  }

  /// Throttling für häufige Operationen
  /// Führt die Operation maximal einmal pro Zeitintervall aus
  static void handleScrollWithThrottling({
    required String operationId,
    required VoidCallback operation,
    Duration throttleInterval = const Duration(milliseconds: 500),
    bool logExecution = false,
  }) {
    if (!_isEnabled) {
      operation();
      return;
    }

    final now = DateTime.now();
    final lastExecution = _lastExecutionTimes[operationId];

    // Prüfe ob genug Zeit seit der letzten Ausführung vergangen ist
    if (lastExecution == null || 
        now.difference(lastExecution) >= throttleInterval) {
      
      _lastExecutionTimes[operationId] = now;
      
      try {
        operation();
        if (logExecution) {
          _log.d('Throttled operation executed: $operationId');
        }
      } catch (e) {
        _log.e('Fehler bei throttled operation $operationId: $e');
      }
    } else {
      if (logExecution) {
        _log.d('Throttled operation skipped: $operationId (too frequent)');
      }
    }
  }

  /// Intelligente Fokus-Verwaltung mit State-Tracking
  /// Entfernt Fokus nur wenn nötig und verhindert redundante Aufrufe
  static void handleFocusRemovalOptimized({
    required String widgetId,
    Duration debounceDelay = const Duration(milliseconds: 150),
    bool logExecution = false,
  }) {
    if (!_isEnabled) {
      _removeFocusImmediate();
      return;
    }

    // Prüfe aktuellen Fokus-Status
    final hasFocus = FocusManager.instance.primaryFocus?.hasFocus ?? false;
    final lastFocusState = _focusStates[widgetId] ?? false;

    // Nur handeln wenn sich der Fokus-Status geändert hat oder Fokus vorhanden ist
    if (hasFocus || lastFocusState != hasFocus) {
      handleScrollWithDebouncing(
        operationId: 'focus_removal_$widgetId',
        operation: () {
          _removeFocusImmediate();
          _focusStates[widgetId] = false;
          if (logExecution) {
            _log.d('Focus removed for widget: $widgetId');
          }
        },
        debounceDelay: debounceDelay,
        logExecution: logExecution,
      );
      
      _focusStates[widgetId] = hasFocus;
    }
  }

  /// Sofortige Fokus-Entfernung ohne Optimierung
  static void _removeFocusImmediate() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  /// Optimierter ScrollController-Listener
  /// Kombiniert mehrere Scroll-Operationen in einem optimierten Handler
  static VoidCallback createOptimizedScrollListener({
    required String listenerId,
    VoidCallback? onScroll,
    VoidCallback? onFocusRemoval,
    VoidCallback? onEndReached,
    ScrollController? scrollController,
    double endReachedThreshold = 0.9,
    Duration scrollDebounce = const Duration(milliseconds: 100),
    Duration focusDebounce = const Duration(milliseconds: 150),
    Duration endReachedThrottle = const Duration(milliseconds: 1000),
  }) {
    return () {
      if (!_isEnabled) {
        onScroll?.call();
        onFocusRemoval?.call();
        _checkEndReached(scrollController, onEndReached, endReachedThreshold);
        return;
      }

      // Scroll-Handler mit Debouncing
      if (onScroll != null) {
        handleScrollWithDebouncing(
          operationId: 'scroll_$listenerId',
          operation: onScroll,
          debounceDelay: scrollDebounce,
        );
      }

      // Fokus-Entfernung mit intelligenter Optimierung
      if (onFocusRemoval != null) {
        handleFocusRemovalOptimized(
          widgetId: listenerId,
          debounceDelay: focusDebounce,
        );
      }

      // End-Reached-Detection mit Throttling
      if (onEndReached != null && scrollController != null) {
        handleScrollWithThrottling(
          operationId: 'end_reached_$listenerId',
          operation: () => _checkEndReached(
            scrollController, 
            onEndReached, 
            endReachedThreshold
          ),
          throttleInterval: endReachedThrottle,
        );
      }
    };
  }

  /// Prüft ob das Ende der Liste erreicht wurde
  static void _checkEndReached(
    ScrollController? controller,
    VoidCallback? onEndReached,
    double threshold,
  ) {
    if (controller == null || onEndReached == null) return;
    
    if (controller.hasClients &&
        controller.position.pixels >= 
        controller.position.maxScrollExtent * threshold) {
      onEndReached();
    }
  }

  /// Bereinigt alle Timer und States
  static void cleanup() {
    _log.i('Bereinige Scroll Performance Optimizer...');
    
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    _lastExecutionTimes.clear();
    _focusStates.clear();
  }

  /// Gibt Performance-Statistiken zurück
  static Map<String, dynamic> getPerformanceStats() {
    return {
      'enabled': _isEnabled,
      'active_debounce_timers': _debounceTimers.length,
      'tracked_focus_states': _focusStates.length,
      'last_execution_times': _lastExecutionTimes.length,
      'active_timers': _debounceTimers.values.where((t) => t.isActive).length,
    };
  }
}

/// Mixin für Widgets um Scroll Performance Optimierung zu verwenden
mixin ScrollPerformanceOptimizedWidget {
  /// Optimierter Scroll-Handler
  void handleOptimizedScroll({
    required String operationId,
    required VoidCallback operation,
    Duration debounceDelay = const Duration(milliseconds: 100),
  }) {
    ScrollPerformanceOptimizer.handleScrollWithDebouncing(
      operationId: '${runtimeType}_$operationId',
      operation: operation,
      debounceDelay: debounceDelay,
    );
  }

  /// Optimierte Fokus-Entfernung
  void handleOptimizedFocusRemoval({
    Duration debounceDelay = const Duration(milliseconds: 150),
  }) {
    ScrollPerformanceOptimizer.handleFocusRemovalOptimized(
      widgetId: runtimeType.toString(),
      debounceDelay: debounceDelay,
    );
  }
}

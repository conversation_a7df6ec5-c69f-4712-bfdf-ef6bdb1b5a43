import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../../application/providers/services_providers.dart';
import '../../../application/providers/user_profile_provider.dart';

/// Optimierte Version des RemainingApplicationsWidget mit Timer-basierter Zeit-Updates
/// Verhindert DateTime.now() Aufrufe in build() Methode
class OptimizedRemainingApplicationsWidget extends ConsumerStatefulWidget {
  final bool showLabel;
  final bool showIcon;
  final bool compact;
  final bool showDetailed;
  final VoidCallback? onUpgradePressed;

  const OptimizedRemainingApplicationsWidget({
    super.key,
    this.showLabel = true,
    this.showIcon = true,
    this.compact = false,
    this.showDetailed = false,
    this.onUpgradePressed,
  });

  @override
  ConsumerState<OptimizedRemainingApplicationsWidget> createState() =>
      _OptimizedRemainingApplicationsWidgetState();
}

class _OptimizedRemainingApplicationsWidgetState
    extends ConsumerState<OptimizedRemainingApplicationsWidget> {
  Timer? _timer;
  DateTime _currentTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Timer für Zeit-Updates alle Minute
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentTime = DateTime.now();
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final remainingApplicationsState = ref.watch(remainingApplicationsProvider);
    final isPremiumState = ref.watch(isPremiumProvider);

    return remainingApplicationsState.when(
      data: (remainingApplications) {
        if (isPremiumState) {
          return _buildPremiumWidget(context);
        }

        if (remainingApplications > 0) {
          return _buildRemainingApplicationsWidget(
            context,
            remainingApplications,
          );
        } else {
          return widget.showDetailed
              ? _buildDetailedNoApplicationsWidget(context)
              : _buildSimpleNoApplicationsWidget(context);
        }
      },
      loading: () => _buildLoadingWidget(),
      error: (error, stackTrace) => _buildErrorWidget(context, error),
    );
  }

  Widget _buildPremiumWidget(BuildContext context) {
    if (widget.compact) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showIcon) ...[
              const Icon(Icons.star, color: Colors.white, size: 16),
              const SizedBox(width: 4),
            ],
            if (widget.showLabel)
              const Text(
                'Premium',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          if (widget.showIcon) ...[
            const Icon(Icons.star, color: Colors.white, size: 24),
            const SizedBox(width: 12),
          ],
          if (widget.showLabel)
            const Expanded(
              child: Text(
                'Premium - Unbegrenzte Bewerbungen',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRemainingApplicationsWidget(
    BuildContext context,
    int remainingApplications,
  ) {
    final theme = Theme.of(context);
    final color = remainingApplications <= 2
        ? theme.colorScheme.error
        : theme.colorScheme.primary;

    if (widget.compact) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showIcon) ...[
              Icon(Icons.send, color: color, size: 16),
              const SizedBox(width: 4),
            ],
            if (widget.showLabel)
              Text(
                '$remainingApplications',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          if (widget.showIcon) ...[
            Icon(Icons.send, color: color, size: 24),
            const SizedBox(width: 12),
          ],
          if (widget.showLabel)
            Expanded(
              child: Text(
                'Verbleibende Bewerbungen: $remainingApplications',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSimpleNoApplicationsWidget(BuildContext context) {
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        if (nextResetDate == null) {
          return _buildStandardNoApplicationsWidget(context);
        }

        // Verwende gecachte Zeit statt DateTime.now()
        final difference = nextResetDate.difference(_currentTime);

        if (difference.isNegative) {
          return _buildStandardNoApplicationsWidget(context);
        }

        final days = difference.inDays;
        final hours = difference.inHours % 24;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.error.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.hourglass_empty,
                    color: Theme.of(context).colorScheme.error,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Keine Bewerbungen mehr verfügbar',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Nächste kostenlose Bewerbungen in: ${days}d ${hours}h',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onUpgradePressed ??
                      () => context.push('/premium'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                  child: const Text('Jetzt Premium werden'),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => _buildLoadingWidget(),
      error: (error, stackTrace) => _buildStandardNoApplicationsWidget(context),
    );
  }

  Widget _buildDetailedNoApplicationsWidget(BuildContext context) {
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        if (nextResetDate == null) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        // Verwende gecachte Zeit statt DateTime.now()
        final difference = nextResetDate.difference(_currentTime);

        if (difference.isNegative) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        final days = difference.inDays;
        final hours = difference.inHours % 24;
        final minutes = difference.inMinutes % 60;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.error.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.hourglass_empty,
                    color: Theme.of(context).colorScheme.error,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Bewerbungslimit erreicht',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Du hast dein tägliches Limit für kostenlose Bewerbungen erreicht.',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Nächste Bewerbungen in: ${days}d ${hours}h ${minutes}m',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: widget.onUpgradePressed ??
                      () => context.push('/premium'),
                  icon: const Icon(Icons.star),
                  label: const Text('Premium werden - Unbegrenzte Bewerbungen'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => _buildLoadingWidget(),
      error: (error, stackTrace) =>
          _buildStandardDetailedNoApplicationsWidget(context),
    );
  }

  Widget _buildStandardNoApplicationsWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Text('Keine Bewerbungen mehr verfügbar'),
    );
  }

  Widget _buildStandardDetailedNoApplicationsWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Text('Bewerbungslimit erreicht'),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, Object error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'Fehler beim Laden: $error',
        style: TextStyle(
          color: Theme.of(context).colorScheme.onErrorContainer,
        ),
      ),
    );
  }
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// System Error Handler - Behandelt Android System-Fehler wie callGcSupression
/// und handleResized abandoned Warnungen
class SystemErrorHandler {
  // Einfaches Logging ohne externe Abhängigkeiten
  static void _log_i(String message) =>
      debugPrint('[SystemErrorHandler] INFO: $message');
  static void _log_w(String message) =>
      debugPrint('[SystemErrorHandler] WARN: $message');
  static void _log_d(String message) =>
      debugPrint('[SystemErrorHandler] DEBUG: $message');
  static bool _isInitialized = false;
  static final Map<String, int> _errorCounts = {};
  static Timer? _cleanupTimer;

  /// Initialisiert den System Error Handler
  static void initialize() {
    if (_isInitialized) return;

    _isInitialized = true;
    _log_i('System Error Handler initialisiert');

    // Installiere globalen Error Handler
    _installGlobalErrorHandler();

    // Starte periodische Bereinigung
    _startPeriodicCleanup();

    // Installiere Platform-spezifische Handler
    if (Platform.isAndroid) {
      _installAndroidSpecificHandlers();
    }
  }

  /// Installiert globalen Error Handler
  static void _installGlobalErrorHandler() {
    // Flutter Error Handler
    FlutterError.onError = (FlutterErrorDetails details) {
      final errorString = details.toString();

      // Filtere bekannte System-Fehler
      if (_isKnownSystemError(errorString)) {
        _handleKnownSystemError(errorString, details);
      } else {
        // Normale Fehlerbehandlung
        FlutterError.presentError(details);
      }
    };

    // Platform Dispatcher Error Handler
    PlatformDispatcher.instance.onError = (error, stack) {
      final errorString = error.toString();

      if (_isKnownSystemError(errorString)) {
        _handleKnownSystemError(errorString, null);
        return true; // Fehler als behandelt markieren
      }

      return false; // Normale Fehlerbehandlung
    };
  }

  /// Prüft ob es sich um einen bekannten System-Fehler handelt
  static bool _isKnownSystemError(String errorString) {
    final knownErrors = [
      'callGcSupression',
      'handleResized abandoned',
      'NullPointerException',
      'ClassLoaderContext',
      'performTraversals',
      'VRI[MainActivity]',
      'DynamicFramerate',
      'FilePhenotypeFlags',
    ];

    return knownErrors.any((error) => errorString.contains(error));
  }

  /// Behandelt bekannte System-Fehler
  static void _handleKnownSystemError(
    String errorString,
    FlutterErrorDetails? details,
  ) {
    // Zähle Fehler-Häufigkeit
    final errorType = _getErrorType(errorString);
    _errorCounts[errorType] = (_errorCounts[errorType] ?? 0) + 1;

    // Logge nur bei ersten paar Vorkommen um Spam zu vermeiden
    final count = _errorCounts[errorType]!;
    if (count <= 3) {
      _log_w('System-Fehler erkannt ($count. Mal): $errorType');
      if (details != null && count == 1) {
        _log_d('Fehler-Details: ${details.summary}');
      }
    } else if (count == 10) {
      _log_w(
        'System-Fehler $errorType tritt häufig auf (${count}x) - weitere Logs werden unterdrückt',
      );
    }

    // Spezifische Behandlung je nach Fehlertyp
    _handleSpecificError(errorType, errorString);
  }

  /// Ermittelt den Fehlertyp aus dem Error-String
  static String _getErrorType(String errorString) {
    if (errorString.contains('callGcSupression')) return 'callGcSupression';
    if (errorString.contains('handleResized abandoned')) return 'handleResized';
    if (errorString.contains('performTraversals')) return 'performTraversals';
    if (errorString.contains('ClassLoaderContext')) return 'ClassLoaderContext';
    if (errorString.contains('DynamicFramerate')) return 'DynamicFramerate';
    if (errorString.contains('FilePhenotypeFlags')) return 'FilePhenotypeFlags';
    return 'unknown_system_error';
  }

  /// Behandelt spezifische Fehlertypen
  static void _handleSpecificError(String errorType, String errorString) {
    switch (errorType) {
      case 'callGcSupression':
        _handleCallGcSupressionError();
        break;
      case 'handleResized':
        _handleResizedAbandonedError();
        break;
      case 'performTraversals':
        _handlePerformTraversalsError();
        break;
      case 'ClassLoaderContext':
        _handleClassLoaderError();
        break;
      default:
        // Keine spezielle Behandlung nötig
        break;
    }
  }

  /// Behandelt callGcSupression NullPointerException
  static void _handleCallGcSupressionError() {
    // Diese Fehler sind Android-System-intern und können ignoriert werden
    // Führe sanfte Garbage Collection durch um Memory-Druck zu reduzieren
    scheduleMicrotask(() {
      // Trigger GC indirekt durch kleine Memory-Allocation
      final dummy = List.generate(100, (i) => i);
      dummy.clear();
    });
  }

  /// Behandelt handleResized abandoned Warnungen
  static void _handleResizedAbandonedError() {
    // Diese Warnungen entstehen durch schnelle UI-Änderungen
    // Reduziere UI-Updates durch Debouncing
    scheduleMicrotask(() {
      // Kurze Pause um UI-Thread zu entlasten
      Future.delayed(const Duration(milliseconds: 16));
    });
  }

  /// Behandelt performTraversals Warnungen
  static void _handlePerformTraversalsError() {
    // Diese Warnungen entstehen durch UI-Thread-Blocking
    // Bereits durch UIThreadOptimizer behandelt
  }

  /// Behandelt ClassLoader Fehler
  static void _handleClassLoaderError() {
    // Diese Fehler sind Android-Build-System-bedingt und können ignoriert werden
  }

  /// Installiert Android-spezifische Error Handler
  static void _installAndroidSpecificHandlers() {
    // Zusätzliche Android-spezifische Behandlung könnte hier implementiert werden
    _log_d('Android-spezifische Error Handler installiert');
  }

  /// Startet periodische Bereinigung der Error-Counts
  static void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      // Reduziere Error-Counts um Spam-Detection zu resetten
      final keysToReduce = _errorCounts.keys.toList();
      for (final key in keysToReduce) {
        final currentCount = _errorCounts[key]!;
        if (currentCount > 10) {
          _errorCounts[key] = (currentCount * 0.8).round();
        }
      }
    });
  }

  /// Gibt Error-Statistiken zurück
  static Map<String, dynamic> getErrorStats() {
    return {
      'initialized': _isInitialized,
      'error_counts': Map.from(_errorCounts),
      'total_errors': _errorCounts.values.fold(0, (sum, count) => sum + count),
    };
  }

  /// Bereinigt den Error Handler
  static void dispose() {
    _log_i('System Error Handler wird bereinigt...');
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    _errorCounts.clear();
    _isInitialized = false;
  }
}

/// Mixin für Widgets um System Error Handling zu verwenden
mixin SystemErrorHandledWidget {
  /// Führt eine Operation mit System Error Handling aus
  Future<T?> executeWithSystemErrorHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    try {
      return await operation();
    } catch (e) {
      final errorString = e.toString();

      if (SystemErrorHandler._isKnownSystemError(errorString)) {
        SystemErrorHandler._handleKnownSystemError(errorString, null);
        return null; // Bekannte System-Fehler ignorieren
      }

      // Unbekannte Fehler weiterwerfen
      rethrow;
    }
  }
}

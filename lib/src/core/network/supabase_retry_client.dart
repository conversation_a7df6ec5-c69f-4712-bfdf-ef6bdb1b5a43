import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

/// Robuster Supabase-Client mit automatischer Retry-Logic
/// Behandelt Netzwerkfehler, Timeouts und temporäre Ausfälle
class SupabaseRetryClient {
  final SupabaseClient _client;
  final Logger _log = Logger();

  // Retry-Konfiguration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);
  static const Duration _maxDelay = Duration(seconds: 10);
  static const Duration _timeout = Duration(seconds: 30);

  SupabaseRetryClient(this._client);

  /// Führt eine Supabase-Operation mit Retry-Logic aus
  Future<T> executeWithRetry<T>(
    String operationName,
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    Duration baseDelay = _baseDelay,
    bool enableLogging = true,
  }) async {
    int attempt = 0;
    Duration currentDelay = baseDelay;

    while (attempt <= maxRetries) {
      try {
        if (enableLogging && attempt > 0) {
          _log.i(
            '🔄 $operationName - Versuch ${attempt + 1}/${maxRetries + 1}',
          );
        }

        // Operation mit Timeout ausführen
        final result = await operation().timeout(_timeout);

        if (enableLogging && attempt > 0) {
          _log.i('✅ $operationName erfolgreich nach ${attempt + 1} Versuchen');
        }

        return result;
      } catch (e) {
        attempt++;

        // Prüfe ob es sich um einen wiederholbaren Fehler handelt
        final isRetryable = _isRetryableError(e);
        final hasRetriesLeft = attempt <= maxRetries;

        if (enableLogging) {
          _log.w('⚠️ $operationName Fehler (Versuch $attempt): $e');
          _log.d('Wiederholbar: $isRetryable, Versuche übrig: $hasRetriesLeft');
        }

        if (!isRetryable || !hasRetriesLeft) {
          if (enableLogging) {
            _log.e(
              '❌ $operationName endgültig fehlgeschlagen nach $attempt Versuchen',
            );
          }
          rethrow;
        }

        // Exponential backoff mit Jitter
        final jitter = Duration(
          milliseconds: (currentDelay.inMilliseconds * 0.1).round(),
        );
        final delayWithJitter = currentDelay + jitter;
        final actualDelay =
            delayWithJitter > _maxDelay ? _maxDelay : delayWithJitter;

        if (enableLogging) {
          _log.i('⏳ Warte ${actualDelay.inSeconds}s vor nächstem Versuch...');
        }

        await Future.delayed(actualDelay);
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * 1.5).round(),
        );
      }
    }

    throw Exception(
      '$operationName: Maximale Anzahl Wiederholungsversuche erreicht',
    );
  }

  /// Prüft ob ein Fehler wiederholbar ist
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Netzwerk-bezogene Fehler
    if (errorString.contains('socketexception') ||
        errorString.contains('failed host lookup') ||
        errorString.contains('network is unreachable') ||
        errorString.contains('connection refused') ||
        errorString.contains('connection reset') ||
        errorString.contains('connection timed out')) {
      return true;
    }

    // Timeout-Fehler
    if (errorString.contains('timeoutexception') ||
        errorString.contains('timeout')) {
      return true;
    }

    // HTTP-Status-Codes die wiederholbar sind
    if (error is PostgrestException) {
      // 5xx Server-Fehler sind wiederholbar
      if (error.code != null && error.code!.startsWith('5')) {
        return true;
      }
      // 429 Too Many Requests
      if (error.code == '429') {
        return true;
      }
    }

    // Supabase-spezifische temporäre Fehler
    if (errorString.contains('temporary failure') ||
        errorString.contains('service unavailable') ||
        errorString.contains('internal server error')) {
      return true;
    }

    return false;
  }

  /// Sichere Supabase-Abfrage mit Retry-Logic
  Future<List<Map<String, dynamic>>> safeSelect(
    String table,
    String operationName, {
    String? select,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    return executeWithRetry<List<Map<String, dynamic>>>(
      'SELECT $operationName',
      () async {
        // Baue die Query in einem Zug auf
        var query = _client.from(table).select(select ?? '*');

        // Filter anwenden
        if (filters != null) {
          for (final entry in filters.entries) {
            query = query.eq(entry.key, entry.value);
          }
        }

        // Führe die Query aus und wende dann Sortierung/Limit an
        if (orderBy != null && limit != null) {
          return await query.order(orderBy, ascending: ascending).limit(limit);
        } else if (orderBy != null) {
          return await query.order(orderBy, ascending: ascending);
        } else if (limit != null) {
          return await query.limit(limit);
        } else {
          return await query;
        }
      },
    );
  }

  /// Sichere Supabase-Einfügung mit Retry-Logic
  Future<List<Map<String, dynamic>>> safeInsert(
    String table,
    String operationName,
    Map<String, dynamic> data,
  ) async {
    return executeWithRetry<List<Map<String, dynamic>>>(
      'INSERT $operationName',
      () async {
        return await _client.from(table).insert(data).select();
      },
    );
  }

  /// Sichere Supabase-Aktualisierung mit Retry-Logic
  Future<List<Map<String, dynamic>>> safeUpdate(
    String table,
    String operationName,
    Map<String, dynamic> data,
    Map<String, dynamic> filters,
  ) async {
    return executeWithRetry<List<Map<String, dynamic>>>(
      'UPDATE $operationName',
      () async {
        var query = _client.from(table).update(data);

        // Filter anwenden
        for (final entry in filters.entries) {
          query = query.eq(entry.key, entry.value);
        }

        return await query.select();
      },
    );
  }

  /// Sichere Supabase-Löschung mit Retry-Logic
  Future<List<Map<String, dynamic>>> safeDelete(
    String table,
    String operationName,
    Map<String, dynamic> filters,
  ) async {
    return executeWithRetry<List<Map<String, dynamic>>>(
      'DELETE $operationName',
      () async {
        var query = _client.from(table).delete();

        // Filter anwenden
        for (final entry in filters.entries) {
          query = query.eq(entry.key, entry.value);
        }

        return await query.select();
      },
    );
  }

  /// Sichere RPC-Aufrufe mit Retry-Logic
  Future<T> safeRpc<T>(
    String functionName,
    String operationName, {
    Map<String, dynamic>? params,
  }) async {
    return executeWithRetry<T>('RPC $operationName', () async {
      return await _client.rpc(functionName, params: params);
    });
  }

  /// Sichere Edge Function-Aufrufe mit Retry-Logic
  Future<FunctionResponse> safeFunction(
    String functionName,
    String operationName, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    return executeWithRetry<FunctionResponse>(
      'FUNCTION $operationName',
      () async {
        return await _client.functions.invoke(
          functionName,
          body: body,
          headers: headers,
        );
      },
    );
  }
}

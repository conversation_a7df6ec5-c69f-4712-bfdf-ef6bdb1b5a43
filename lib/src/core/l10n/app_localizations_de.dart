// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'Bewerbung KI';

  @override
  String get jobSearchTitle => 'Jobsuche';

  @override
  String get favoritesTitle => 'Favoriten';

  @override
  String get profileTitle => 'Profil';

  @override
  String get settingsTitle => 'Einstellungen';

  @override
  String get loginTitle => 'Anmelden';

  @override
  String get signupTitle => 'Registrieren';

  @override
  String get forgotPasswordTitle => 'Passwort vergessen';

  @override
  String get email => 'E-Mail';

  @override
  String get password => 'Passwort';

  @override
  String get login => 'Anmelden';

  @override
  String get signup => 'Registrieren';

  @override
  String get forgotPassword => 'Passwort vergessen?';

  @override
  String get resetPassword => 'Passwort zurücksetzen';

  @override
  String get search => 'Suchen';

  @override
  String get filter => 'Filter';

  @override
  String get location => 'Standort';

  @override
  String get distance => 'Entfernung';

  @override
  String get jobTitle => 'Jobtitel';

  @override
  String get company => 'Firma';

  @override
  String get apply => 'Bewerben';

  @override
  String get save => 'Speichern';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get delete => 'Löschen';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get addToFavorites => 'Zu Favoriten hinzufügen';

  @override
  String get removeFromFavorites => 'Von Favoriten entfernen';

  @override
  String get noResults => 'Keine Ergebnisse gefunden';

  @override
  String get loading => 'Wird geladen...';

  @override
  String get error => 'Fehler';

  @override
  String get retry => 'Erneut versuchen';

  @override
  String get logout => 'Abmelden';

  @override
  String get accountSettings => 'Konto';

  @override
  String get appSettings => 'App';

  @override
  String get securitySettings => 'Sicherheit';

  @override
  String get infoHelp => 'Info & Hilfe';

  @override
  String get editProfile => 'Profil bearbeiten';

  @override
  String get changePassword => 'Passwort ändern';

  @override
  String get premiumBenefits => 'Premium-Vorteile';

  @override
  String get appearance => 'Darstellung (Theme)';

  @override
  String get clearCache => 'Cache leeren';

  @override
  String get language => 'Sprache';

  @override
  String get aboutApp => 'Über diese App';

  @override
  String get helpFeedback => 'Hilfe & Feedback';

  @override
  String get privacyPolicy => 'Datenschutzerklärung';

  @override
  String get translateContent => 'Inhalt übersetzen';

  @override
  String get originalContent => 'Originalinhalt';

  @override
  String get translationError => 'Fehler bei der Übersetzung';

  @override
  String get translationSuccess => 'Übersetzung erfolgreich';

  @override
  String get selectLanguage => 'Sprache auswählen';

  @override
  String get german => 'Deutsch';

  @override
  String get english => 'Englisch';

  @override
  String get french => 'Französisch';

  @override
  String get spanish => 'Spanisch';

  @override
  String get italian => 'Italienisch';

  @override
  String get languageChanged => 'Sprache geändert';

  @override
  String get restartApp => 'App neu starten';

  @override
  String get restartRequired => 'Neustart erforderlich';

  @override
  String get restartAppMessage => 'Bitte starte die App neu, um die Sprachänderung zu übernehmen.';

  @override
  String get detectedLanguage => 'Erkannte Sprache';

  @override
  String get translatingTo => 'Übersetze nach';

  @override
  String get translationInProgress => 'Übersetzung läuft...';

  @override
  String get welcome => 'Willkommen bei';

  @override
  String get findJobWithAI => 'Finden Sie Ihren nächsten Job mit Hilfe von KI.';

  @override
  String get setupProfile => 'Lass uns dein Profil einrichten, damit die KI dir optimal helfen kann, passende Jobs zu finden und überzeugende Bewerbungen zu erstellen.';

  @override
  String get aboutYou => 'Erzähl uns etwas über dich';

  @override
  String get profileHelpInfo => 'Diese Informationen helfen der KI, deine Bewerbungen zu personalisieren.';

  @override
  String get fullName => 'Vollständiger Name';

  @override
  String get emailFromLogin => 'E-Mail (aus Anmeldung)';

  @override
  String get phoneOptional => 'Telefonnummer (Optional)';

  @override
  String get yourSkills => 'Deine wichtigsten Fähigkeiten';

  @override
  String get skillsDescription => 'Nenne uns einige deiner Kernkompetenzen (z.B. Programmiersprachen, Software, Soft Skills).';

  @override
  String get skill => 'Fähigkeit';

  @override
  String get removeSkill => 'Fähigkeit entfernen';

  @override
  String get addSkill => 'Fähigkeit hinzufügen';

  @override
  String get minSkillRequired => 'Mindestens eine Fähigkeit benötigt.';

  @override
  String get lastPosition => 'Deine letzte Position (Optional)';

  @override
  String get experienceDescription => 'Gib deine aktuellste oder relevanteste Berufserfahrung an. Mehr Details kannst du später im Profil hinzufügen.';

  @override
  String get position => 'Position';

  @override
  String get firm => 'Firma';

  @override
  String get startDate => 'Startdatum';

  @override
  String get endDate => 'Enddatum';

  @override
  String get currentlyWorking => 'Ich arbeite aktuell hier';

  @override
  String get backToLogin => 'Zurück zum Login';

  @override
  String get generateAICoverLetter => 'KI-Anschreiben generieren';

  @override
  String get generatingCoverLetter => 'Generiere Anschreiben...';

  @override
  String get coverLetterGenerated => 'Anschreiben generiert ✓';

  @override
  String get solveCaptcha => 'Bitte CAPTCHA lösen';

  @override
  String get loadingAd => 'Lade Werbung...';

  @override
  String get confirm => 'Bestätigen';

  @override
  String get ok => 'OK';

  @override
  String get termsAndPrivacyNotice => 'Mit deiner Registrierung akzeptierst du unsere Nutzungsbedingungen und Datenschutzerklärung.';

  @override
  String get apiCallsDisabled => 'Direkte API-Aufrufe wurden aus Sicherheitsgründen deaktiviert. Die Daten wurden lokal gespeichert.';

  @override
  String get aiTest => 'AI-Test';

  @override
  String get aiChangeActive => 'AI-Änderung aktiv!';

  @override
  String get personalData => 'Persönliche Daten';

  @override
  String get resume => 'Lebenslauf (PDF)';

  @override
  String get skills => 'Fähigkeiten';

  @override
  String get workExperience => 'Berufserfahrung';

  @override
  String get education => 'Ausbildung';

  @override
  String get aiPersonalization => 'KI Personalisierung';

  @override
  String get address => 'Adresse (Optional)';

  @override
  String get localSelectionRemoved => 'Lokale Auswahl entfernt.';

  @override
  String get generateJobTitles => 'Berufsbezeichnungen generieren';

  @override
  String get generatingJobTitles => 'Generiere Berufsbezeichnungen...';

  @override
  String get jobTitlesGenerated => 'Berufsbezeichnungen erfolgreich generiert!';

  @override
  String get jobTitlesError => 'Fehler beim Generieren der Berufsbezeichnungen.';

  @override
  String get deleteSkill => 'Fähigkeit löschen';

  @override
  String get deleteWorkExperience => 'Berufserfahrung wirklich löschen?';

  @override
  String get deleteEducation => 'Ausbildung wirklich löschen?';

  @override
  String get generatedJobTitles => 'Generierte Berufsbezeichnungen:';

  @override
  String get name => 'Name';

  @override
  String get phone => 'Telefon';

  @override
  String get desiredPosition => 'Gewünschte Position';

  @override
  String get salaryClaim => 'Gehaltsvorstellung';

  @override
  String get preferredLocation => 'Bevorzugter Ort';

  @override
  String get notSpecified => 'Nicht angegeben';

  @override
  String get noWorkExperience => 'Keine Berufserfahrung hinzugefügt.';

  @override
  String get description => 'Beschreibung';

  @override
  String get deleteEntry => 'Eintrag löschen';

  @override
  String get startDateError => 'Start darf nicht nach Ende liegen.';

  @override
  String get noEducation => 'Keine Ausbildung hinzugefügt.';

  @override
  String get institution => 'Institution';

  @override
  String get degree => 'Abschluss';

  @override
  String get fieldOfStudy => 'Fachrichtung (Optional)';

  @override
  String get noSkills => 'Keine Fähigkeiten hinzugefügt.';

  @override
  String get enterSkill => 'Fähigkeit eingeben...';

  @override
  String get selectPdf => 'PDF auswählen';

  @override
  String get selectNewPdf => 'Neue PDF auswählen';

  @override
  String get fileSelectionError => 'Fehler beim Auswählen der Datei:';

  @override
  String get cannotOpenResume => 'Konnte Lebenslauf nicht öffnen: Ungültige URL';

  @override
  String get removeSelection => 'Auswahl aufheben';

  @override
  String get relevance => 'Relevanz';

  @override
  String get date => 'Datum';

  @override
  String get enterKeyword => 'Stichwort eingeben';

  @override
  String get radius => 'Umkreis';

  @override
  String get km => 'km';

  @override
  String get yourProfessions => 'Deine Berufe';

  @override
  String get includeWorkExperience => 'Berufserfahrung einbeziehen';

  @override
  String get preferredWritingStyle => 'Bevorzugter Schreibstil';

  @override
  String get writingStyleProfessional => 'Professionell';

  @override
  String get writingStyleCasual => 'Informell';

  @override
  String get writingStyleCreative => 'Kreativ';

  @override
  String get writingStyleDirect => 'Direkt';

  @override
  String get interestingHobbies => 'Interessante Hobbys';

  @override
  String get currentPassword => 'Aktuelles Passwort';

  @override
  String get newPassword => 'Neues Passwort';

  @override
  String get confirmPassword => 'Passwort bestätigen';

  @override
  String get passwordChanged => 'Passwort erfolgreich geändert!';

  @override
  String get passwordError => 'Fehler beim Ändern des Passworts:';

  @override
  String get passwordMismatch => 'Die Passwörter stimmen nicht überein';

  @override
  String get passwordTooShort => 'Passwort muss mindestens 6 Zeichen lang sein';

  @override
  String get withoutExperience => 'Ohne Vorerfahrung';

  @override
  String get noJobsFound => 'Keine Jobs gefunden.';

  @override
  String get experienceSummary => 'Zusammenfassung deiner Erfahrung';

  @override
  String get aiPersonalizationHint => 'Hinweis: Diese Informationen helfen der KI, personalisierte Anschreiben für dich zu erstellen.';

  @override
  String get savePassword => 'Passwort speichern';

  @override
  String get enterNewPassword => 'Bitte gib ein neues Passwort ein';

  @override
  String get passwordMinLength => 'Das Passwort muss mindestens 8 Zeichen lang sein';

  @override
  String get passwordRequireUppercase => 'Das Passwort muss mindestens einen Großbuchstaben enthalten';

  @override
  String get passwordRequireNumber => 'Das Passwort muss mindestens eine Zahl enthalten';

  @override
  String get confirmNewPassword => 'Bitte bestätige das neue Passwort';

  @override
  String get noUserLoggedIn => 'Fehler: Kein Benutzer angemeldet.';

  @override
  String passwordUpdateError(String message) {
    return 'Fehler beim Ändern des Passworts: $message';
  }

  @override
  String get unexpectedError => 'Ein unerwarteter Fehler ist aufgetreten.';

  @override
  String get passwordUpdatedSuccess => 'Passwort erfolgreich geändert!';

  @override
  String get selectDate => 'Datum auswählen';

  @override
  String get deleteDate => 'Datum löschen';

  @override
  String resumeFileName(Object name, Object timestamp) {
    return 'Lebenslauf_${timestamp}_$name';
  }

  @override
  String get autofillFromResume => 'Automatisch aus Lebenslauf ausfüllen';

  @override
  String get enabled => 'Aktiviert';

  @override
  String get disabled => 'Deaktiviert';

  @override
  String get skillsInApplication => 'Fähigkeiten in Bewerbungen';

  @override
  String get workExperienceInApplication => 'Berufserfahrung in Bewerbungen';

  @override
  String get educationInApplication => 'Bildung in Anschreiben';

  @override
  String get dateFormat => 'dd.MM.yyyy';

  @override
  String get present => 'heute';

  @override
  String get personalStyle => 'Persönlicher Stil';
}

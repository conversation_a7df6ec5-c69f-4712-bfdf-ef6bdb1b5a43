{"@@locale": "de", "appTitle": "Bewerbung KI", "@appTitle": {"description": "Der Titel der Anwendung"}, "jobSearchTitle": "Jobsuche", "@jobSearchTitle": {"description": "Titel des Jobsuche-Tabs"}, "favoritesTitle": "<PERSON><PERSON>", "@favoritesTitle": {"description": "Titel des Favoriten-Tabs"}, "profileTitle": "Profil", "@profileTitle": {"description": "Titel des Profil-Tabs"}, "settingsTitle": "Einstellungen", "@settingsTitle": {"description": "Titel des Einstellungen-Tabs"}, "loginTitle": "Anmelden", "@loginTitle": {"description": "Titel des Anmelde-Bildschirms"}, "signupTitle": "Registrieren", "@signupTitle": {"description": "Titel des Registrierungs-Bildschirms"}, "forgotPasswordTitle": "Passwort vergessen", "@forgotPasswordTitle": {"description": "Titel des Passwort-vergessen-Bildschirms"}, "email": "E-Mail", "@email": {"description": "Bezeichnung für E-Mail-Felder"}, "password": "Passwort", "@password": {"description": "Bezeichnung für Passwort-Felder"}, "login": "Anmelden", "@login": {"description": "Text für den Anmelde-<PERSON>ton"}, "signup": "Registrieren", "@signup": {"description": "Text für den Registrierungs-<PERSON><PERSON>"}, "forgotPassword": "Passwort vergessen?", "@forgotPassword": {"description": "Text für den Passwort-vergessen-Link"}, "resetPassword": "Passwort zurücksetzen", "@resetPassword": {"description": "Text für den Passwort-zurücksetzen-Button"}, "search": "<PERSON><PERSON>", "@search": {"description": "Text für den Such-<PERSON>"}, "filter": "Filter", "@filter": {"description": "Text für den Filter-<PERSON><PERSON>"}, "location": "<PERSON><PERSON>", "@location": {"description": "Bezeichnung für Standort-Felder"}, "distance": "Entfernung", "@distance": {"description": "Bezeichnung für die Entfernung bei der Sortierung"}, "jobTitle": "Jobtitel", "@jobTitle": {"description": "Bezeichnung für Jobtitel-Felder"}, "company": "Firma", "@company": {"description": "Bezeichnung für die Firma in der Berufserfahrung"}, "apply": "Bewerben", "@apply": {"description": "Text für den Bewerben-<PERSON><PERSON>"}, "save": "Speichern", "@save": {"description": "Text für den Speichern-<PERSON><PERSON>"}, "cancel": "Abbrechen", "@cancel": {"description": "Text für den Abbrechen-Button"}, "delete": "Löschen", "@delete": {"description": "Text für den Löschen-Button"}, "edit": "<PERSON><PERSON><PERSON>", "@edit": {"description": "Text für den Bearbeiten-<PERSON><PERSON>"}, "addToFavorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "@addToFavorites": {"description": "Text für den Zu-Favoriten-hinzufügen-Button"}, "removeFromFavorites": "<PERSON>", "@removeFromFavorites": {"description": "Text für den Von-Favoriten-entfernen-Button"}, "noResults": "<PERSON><PERSON> gefunden", "@noResults": {"description": "Text, wenn keine Suchergebnisse gefunden wurden"}, "loading": "Wird geladen...", "@loading": {"description": "Text während des Ladevorgangs"}, "error": "<PERSON><PERSON>", "@error": {"description": "Allgemeiner Fehlertext"}, "retry": "<PERSON><PERSON><PERSON> versuchen", "@retry": {"description": "Text für den Erneut-versuchen-Button"}, "logout": "Abmelden", "@logout": {"description": "Text für den Abmelde-<PERSON>ton"}, "accountSettings": "Ko<PERSON>", "@accountSettings": {"description": "Titel für den Konto-Abschnitt in den Einstellungen"}, "appSettings": "App", "@appSettings": {"description": "Titel für den App-Abschnitt in den Einstellungen"}, "securitySettings": "Sicherheit", "@securitySettings": {"description": "Titel für den Sicherheits-Abschnitt in den Einstellungen"}, "infoHelp": "Info & Hilfe", "@infoHelp": {"description": "Titel für den Info-und-Hilfe-Abschnitt in den Einstellungen"}, "editProfile": "<PERSON><PERSON>", "@editProfile": {"description": "Text für den Profil-bearbeiten-<PERSON>ton"}, "changePassword": "Passwort ändern", "@changePassword": {"description": "Titel der Passwort-Änderungsseite"}, "premiumBenefits": "Premium-Vorteile", "@premiumBenefits": {"description": "Bezeichnung für die Premium-Vorteile"}, "appearance": "Dar<PERSON><PERSON><PERSON> (Theme)", "@appearance": {"description": "Text für den Darstellungs-Button in den Einstellungen"}, "clearCache": "<PERSON><PERSON> le<PERSON>n", "@clearCache": {"description": "Text für den Cache-leeren-<PERSON><PERSON>"}, "language": "<PERSON><PERSON><PERSON>", "@language": {"description": "Text für den Sprach-Button in den Einstellungen"}, "aboutApp": "Über diese App", "@aboutApp": {"description": "Text für den Über-diese-<PERSON><PERSON>-<PERSON><PERSON>"}, "helpFeedback": "Hilfe & Feedback", "@helpFeedback": {"description": "Text für den Hilfe-und-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, "privacyPolicy": "Datenschutzerklärung", "@privacyPolicy": {"description": "Text für den Datenschutzerklärung-<PERSON>ton"}, "translateContent": "Inhalt übersetzen", "@translateContent": {"description": "Text für den Inhalt-übersetzen-Button"}, "originalContent": "<PERSON><PERSON><PERSON>", "@originalContent": {"description": "Text für den Originalinhalt-<PERSON><PERSON>"}, "translationError": "Fehler bei der Übersetzung", "@translationError": {"description": "Fehlermeldung bei Übersetzungsfehlern"}, "translationSuccess": "Übersetzung erfolgreich", "@translationSuccess": {"description": "Erfolgsmeldung bei erfolgreicher Übersetzung"}, "selectLanguage": "Sprache auswählen", "@selectLanguage": {"description": "Titel für den Sprachauswahl-Dialog"}, "german": "De<PERSON>ch", "@german": {"description": "Name der deutschen Sprache"}, "english": "<PERSON><PERSON><PERSON>", "@english": {"description": "Name der englischen Sprache"}, "french": "Franzö<PERSON><PERSON>", "@french": {"description": "Name der französischen Sprache"}, "spanish": "Spanisch", "@spanish": {"description": "Name der spanischen Sprache"}, "italian": "Italienisch", "@italian": {"description": "Name der italienischen Sprache"}, "languageChanged": "Sprache geändert", "@languageChanged": {"description": "Meldung nach Änderung der Sprache"}, "restartApp": "App neu starten", "@restartApp": {"description": "Aufforderung zum Neustart der App"}, "restartRequired": "<PERSON><PERSON><PERSON><PERSON>", "@restartRequired": {"description": "<PERSON><PERSON><PERSON><PERSON>, dass ein Neustart erforderlich ist"}, "restartAppMessage": "Bitte starte die App neu, um die Sprachänderung zu übernehmen.", "@restartAppMessage": {"description": "<PERSON><PERSON><PERSON><PERSON>, dass die App neu gestartet werden muss"}, "detectedLanguage": "Erkannte Sprache", "@detectedLanguage": {"description": "Text für die erkannte Sprache"}, "translatingTo": "Übersetze nach", "@translatingTo": {"description": "Text für die Zielsprache der Übersetzung"}, "translationInProgress": "Übersetzung läuft...", "@translationInProgress": {"description": "Text während der Übersetzung"}, "welcome": "Will<PERSON>mmen bei", "@welcome": {"description": "Willkommenstext"}, "findJobWithAI": "Finden Sie Ihren nächsten Job mit Hilfe von KI.", "@findJobWithAI": {"description": "Beschreibung der App-Funktionalität"}, "setupProfile": "Lass uns dein Profil einrich<PERSON>, damit die KI dir optimal helfen kann, passende Jobs zu finden und überzeugende Bewerbungen zu erstellen.", "@setupProfile": {"description": "Beschreibung des Onboarding-Prozesses"}, "aboutYou": "<PERSON><PERSON><PERSON><PERSON> uns etwas über dich", "@aboutYou": {"description": "Überschrift für persönliche Informationen"}, "profileHelpInfo": "Diese Informationen helfen der KI, deine Bewerbungen zu personalisieren.", "@profileHelpInfo": {"description": "Erklärung zur Verwendung der Profilinformationen"}, "fullName": "Vollständiger Name", "@fullName": {"description": "Bezeichnung für das Namensfeld"}, "emailFromLogin": "E-Mail (aus Anmeldung)", "@emailFromLogin": {"description": "Bezeichnung für das E-Mail-Feld im Onboarding"}, "phoneOptional": "Telefonnummer (Optional)", "@phoneOptional": {"description": "Bezeichnung für das Telefonnummernfeld"}, "yourSkills": "Deine wichtigsten Fähigkeiten", "@yourSkills": {"description": "Überschrift für den Fähigkeiten-Abschnitt"}, "skillsDescription": "<PERSON>enne uns einige deiner Kernkompetenzen (z.B. Programmiersprachen, Software, Soft Skills).", "@skillsDescription": {"description": "Beschreibung für den Fähigkeiten-Abschnitt"}, "skill": "Fähigkeit", "@skill": {"description": "Bezeichnung für ein Fähigkeitsfeld"}, "removeSkill": "Fähigkeit entfernen", "@removeSkill": {"description": "Tooltip für den Fähigkeit-entfernen-Button"}, "addSkill": "Fähigkeit hinzufügen", "@addSkill": {"description": "Text für den Button zum Hinzufügen einer Fähigkeit"}, "minSkillRequired": "Mindestens eine Fähigkeit benötigt.", "@minSkillRequired": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn keine Fähigkeit angegeben wurde"}, "lastPosition": "<PERSON><PERSON> letzte Position (Optional)", "@lastPosition": {"description": "Überschrift für den Berufserfahrungs-Abschnitt"}, "experienceDescription": "Gib deine aktuellste oder relevanteste Berufserfahrung an. Mehr Details kannst du später im Profil hinzufügen.", "@experienceDescription": {"description": "Beschreibung für den Berufserfahrungs-Abschnitt"}, "position": "Position", "@position": {"description": "Bezeichnung für die Position in der Berufserfahrung"}, "firm": "Firma", "@firm": {"description": "Bezeichnung für das Firmenfeld"}, "startDate": "Startdatum", "@startDate": {"description": "Bezeichnung für das Startdatum"}, "endDate": "Enddatum", "@endDate": {"description": "Bezeichnung für das Enddatum"}, "currentlyWorking": "Ich arbeite aktuell hier", "@currentlyWorking": {"description": "Text für die Checkbox 'Aktuell beschäftigt'"}, "backToLogin": "<PERSON><PERSON><PERSON> zum Login", "@backToLogin": {"description": "Text für den Zurück-zum-Login-<PERSON><PERSON>"}, "generateAICoverLetter": "KI-Anschreiben generieren", "@generateAICoverLetter": {"description": "Text für den KI-Anschreiben-generieren-Button"}, "generatingCoverLetter": "Generiere Anschreiben...", "@generatingCoverLetter": {"description": "Text während der Anschreibengenerierung"}, "coverLetterGenerated": "Anschreiben generiert ✓", "@coverLetterGenerated": {"description": "Text nach erfolgreicher Anschreibengenerierung"}, "solveCaptcha": "Bitte CAPTCHA lösen", "@solveCaptcha": {"description": "Aufforderung zum Lösen eines CAPTCHAs"}, "loadingAd": "Lade Werbung...", "@loadingAd": {"description": "Text während des Ladens einer Werbeanzeige"}, "confirm": "Bestätigen", "@confirm": {"description": "Text für den Bestätigen-Button in Dialogen"}, "ok": "OK", "@ok": {"description": "Text für den OK-Button in Dialogen"}, "termsAndPrivacyNotice": "Mit deiner Registrierung akzeptierst du unsere Nutzungsbedingungen und Datenschutzerklärung.", "@termsAndPrivacyNotice": {"description": "<PERSON>n<PERSON><PERSON> zu Nutzungsbedingungen und Datenschutz bei der Registrierung"}, "apiCallsDisabled": "Direkte API-Aufrufe wurden aus Sicherheitsgründen deaktiviert. Die Daten wurden lokal gespeichert.", "@apiCallsDisabled": {"description": "<PERSON><PERSON><PERSON><PERSON>, dass direkte API-Aufrufe deaktiviert wurden"}, "aiTest": "AI-Test", "@aiTest": {"description": "Titel für den AI-Test-Dialog"}, "aiChangeActive": "AI-Änderung aktiv!", "@aiChangeActive": {"description": "<PERSON><PERSON><PERSON>, dass die AI-Änderung aktiv ist"}, "personalData": "Persönliche Daten", "@personalData": {"description": "Titel für den Abschnitt mit persönlichen Daten"}, "resume": "Lebenslauf (PDF)", "@resume": {"description": "Titel für den Lebenslauf-Abschnitt"}, "skills": "Fähigkeiten", "@skills": {"description": "Titel für den Fähigkeiten-Abschnitt"}, "workExperience": "Berufserfahrung", "@workExperience": {"description": "Titel für den Berufserfahrungs-Abschnitt"}, "education": "Ausbildung", "@education": {"description": "Titel für den Ausbildungs-Abschnitt"}, "aiPersonalization": "KI Personalisierung", "@aiPersonalization": {"description": "Bezeichnung für die KI-Personalisierung"}, "address": "<PERSON><PERSON><PERSON> (Optional)", "@address": {"description": "Bezeichnung für das Adressfeld"}, "localSelectionRemoved": "Lokale Auswahl entfernt.", "@localSelectionRemoved": {"description": "<PERSON><PERSON><PERSON>, dass die lokale Auswahl entfernt wurde"}, "generateJobTitles": "Berufsbezeichnungen generieren", "@generateJobTitles": {"description": "Text für den Button zum Generieren von Berufsbezeichnungen"}, "generatingJobTitles": "Generiere Berufsbezeichnungen...", "@generatingJobTitles": {"description": "Text während der Generierung von Berufsbezeichnungen"}, "jobTitlesGenerated": "Berufsbezeichnungen erfolgreich generiert!", "@jobTitlesGenerated": {"description": "Meldung nach erfolgreicher Generierung von Berufsbezeichnungen"}, "jobTitlesError": "Fehler beim Generieren der Berufsbezeichnungen.", "@jobTitlesError": {"description": "Fehlermeldung bei der Generierung von Berufsbezeichnungen"}, "deleteSkill": "Fähigkeit löschen", "@deleteSkill": {"description": "Text für den Button zum Löschen einer Fähigkeit"}, "deleteWorkExperience": "Berufserfahrung wirklich löschen?", "@deleteWorkExperience": {"description": "Bestätigungsdialog zum Löschen einer Berufserfahrung"}, "deleteEducation": "Ausbildung wirklich löschen?", "@deleteEducation": {"description": "Bestätigungsdialog zum Löschen einer Ausbildung"}, "generatedJobTitles": "Generierte Berufsbezeichnungen:", "@generatedJobTitles": {"description": "Überschrift für die generierten Berufsbezeichnungen"}, "name": "Name", "@name": {"description": "Bezeichnung für das Namensfeld"}, "phone": "Telefon", "@phone": {"description": "Bezeichnung für das Telefonfeld"}, "desiredPosition": "Gewünschte Position", "@desiredPosition": {"description": "Bezeichnung für das Feld der gewünschten Position"}, "salaryClaim": "Gehaltsvorstellung", "@salaryClaim": {"description": "Bezeichnung für das Gehaltsvorstellungsfeld"}, "preferredLocation": "Bevorzugter Ort", "@preferredLocation": {"description": "Bezeichnung für das Feld des bevorzugten Ortes"}, "notSpecified": "Nicht angegeben", "@notSpecified": {"description": "Text für nicht angegebene Werte"}, "noWorkExperience": "<PERSON><PERSON> Berufserfahrung hinzugefügt.", "@noWorkExperience": {"description": "Text, wenn keine Berufserfahrung hinzugefügt wurde"}, "description": "Beschreibung", "@description": {"description": "Bezeichnung für die Beschreibung in der Berufserfahrung"}, "deleteEntry": "Eintrag löschen", "@deleteEntry": {"description": "Text für den Button zum Löschen eines Eintrags"}, "startDateError": "Start darf nicht nach Ende liegen.", "@startDateError": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn das Startdatum nach dem Enddatum liegt"}, "noEducation": "<PERSON><PERSON>sbildung hinzugefügt.", "@noEducation": {"description": "Text, wenn keine Ausbildung hinzugefügt wurde"}, "institution": "Institution", "@institution": {"description": "Bezeichnung für die Institution in der Ausbildung"}, "degree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@degree": {"description": "Bezeichnung für den Abschluss in der Ausbildung"}, "fieldOfStudy": "Fachrichtung (Optional)", "@fieldOfStudy": {"description": "Bezeichnung für die Fachrichtung in der Ausbildung"}, "noSkills": "<PERSON><PERSON> Fähigkeiten hinzugefügt.", "@noSkills": {"description": "Text, wenn keine Fähigkeiten hinzugefügt wurden"}, "enterSkill": "Fähigkeit eingeben...", "@enterSkill": {"description": "Platzhaltertext für das Eingabefeld für Fähigkeiten"}, "selectPdf": "PDF auswählen", "@selectPdf": {"description": "Text für den Button zum Auswählen einer PDF-Datei"}, "selectNewPdf": "Neue PDF auswählen", "@selectNewPdf": {"description": "Text für den Button zum Auswählen einer neuen PDF-Datei"}, "fileSelectionError": "Fehler beim Auswählen der Datei:", "@fileSelectionError": {"description": "Fehlermeldung bei der Dateiauswahl"}, "cannotOpenResume": "Konnte Lebenslauf nicht öffnen: Ungültige URL", "@cannotOpenResume": {"description": "<PERSON><PERSON><PERSON><PERSON>ng, wenn der Lebenslauf nicht geöffnet werden kann"}, "removeSelection": "Auswahl aufheben", "@removeSelection": {"description": "Text für den Button zum Aufheben einer Auswahl"}, "relevance": "<PERSON><PERSON><PERSON><PERSON>", "@relevance": {"description": "Bezeichnung für die Relevanz bei der Sortierung"}, "date": "Datum", "@date": {"description": "Bezeichnung für das Datum bei der Sortierung"}, "enterKeyword": "Stichwort eingeben", "@enterKeyword": {"description": "Platzhaltertext für das Eingabefeld für Stichwörter"}, "radius": "Umkreis", "@radius": {"description": "Bezeichnung für den Umkreis bei der Suche"}, "km": "km", "@km": {"description": "Abkürzung für Kilometer"}, "yourProfessions": "<PERSON><PERSON>", "@yourProfessions": {"description": "Bezeichnung für die Berufe des Benutzers"}, "includeWorkExperience": "Berufserfahrung einbeziehen", "@includeWorkExperience": {"description": "Text für die Option, Berufserfahrung einzubeziehen"}, "preferredWritingStyle": "Bevorzugter Schreibstil", "@preferredWritingStyle": {"description": "Bezeichnung für den bevorzugten Schreibstil"}, "writingStyleProfessional": "Professionell", "@writingStyleProfessional": {"description": "Professioneller <PERSON><PERSON><PERSON>bstil Option"}, "writingStyleCasual": "Informell", "@writingStyleCasual": {"description": "Informeller Schreibstil Option"}, "writingStyleCreative": "K<PERSON><PERSON>v", "@writingStyleCreative": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "writingStyleDirect": "Direkt", "@writingStyleDirect": {"description": "Direkter Schreibstil Option"}, "interestingHobbies": "Interessante <PERSON>s", "@interestingHobbies": {"description": "Bezeichnung für interessante Hobbys"}, "currentPassword": "Aktuelles Passwort", "@currentPassword": {"description": "Bezeichnung für das aktuelle Passwort"}, "newPassword": "Neues Passwort", "@newPassword": {"description": "Bezeichnung für das neue Passwort"}, "confirmPassword": "Passwort bestätigen", "@confirmPassword": {"description": "Bezeichnung für die Passwortbestätigung"}, "passwordChanged": "Passwort erfolgreich geändert!", "@passwordChanged": {"description": "Erfolgsmeldung nach dem Ändern des Passworts"}, "passwordError": "Fehler beim Ändern des Passworts:", "@passwordError": {"description": "Fehlermeldung beim Ändern des Passworts"}, "passwordMismatch": "Die Passwörter stimmen nicht überein", "@passwordMismatch": {"description": "Fehlermeldung, wenn Passwörter nicht übereinstimmen"}, "passwordTooShort": "Passwort muss mindestens 6 <PERSON>eichen lang sein", "@passwordTooShort": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn das Passwort zu kurz ist"}, "withoutExperience": "<PERSON><PERSON>", "@withoutExperience": {"description": "Filter-Option für Jobs ohne Vorerfahrung"}, "noJobsFound": "<PERSON><PERSON> gefunden.", "@noJobsFound": {"description": "<PERSON><PERSON><PERSON>, wenn keine Jobs gefunden wurden"}, "experienceSummary": "Zusammenfassung deiner Erfahrung", "@experienceSummary": {"description": "Beschriftung für das Feld zur Zusammenfassung der Berufserfahrung"}, "aiPersonalizationHint": "Hinweis: Diese Informationen helfen der KI, personalisierte Anschreiben für dich zu erstellen.", "@aiPersonalizationHint": {"description": "Hinweis zur KI-Personalisierung"}, "savePassword": "Passwort speichern", "@savePassword": {"description": "Button zum Speichern des Passworts"}, "enterNewPassword": "Bitte gib ein neues Passwort ein", "@enterNewPassword": {"description": "Aufforderung zur Eingabe eines neuen Passworts"}, "passwordMinLength": "Das Passwort muss mindestens 8 Zeichen lang sein", "@passwordMinLength": {"description": "Hinweis zur Mindestlänge des Passworts"}, "passwordRequireUppercase": "Das Passwort muss mindestens einen Großbuchstaben enthalten", "@passwordRequireUppercase": {"description": "Hinweis zur Anforderung eines Großbuchstabens im Passwort"}, "passwordRequireNumber": "Das Passwort muss mindestens eine Zahl enthalten", "@passwordRequireNumber": {"description": "Hinweis zur Anforderung einer Zahl im Passwort"}, "confirmNewPassword": "Bitte bestätige das neue Passwort", "@confirmNewPassword": {"description": "Aufforderung zur Bestätigung des neuen Passworts"}, "noUserLoggedIn": "Fehler: <PERSON><PERSON>.", "@noUserLoggedIn": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn kein Benutzer angemeldet ist"}, "passwordUpdateError": "Fehler beim Ändern des Passworts: {message}", "@passwordUpdateError": {"description": "Fehlermeldung beim Ändern des Passworts", "placeholders": {"message": {"type": "String", "example": "Ungültiges Passwort"}}}, "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten.", "@unexpectedError": {"description": "Allgemeine Fehlermeldung bei unerwarteten Fehlern"}, "passwordUpdatedSuccess": "Passwort erfolgreich geändert!", "@passwordUpdatedSuccess": {"description": "Erfolgsmeldung nach Passwortänderung"}, "selectDate": "Da<PERSON> ausw<PERSON>en", "@selectDate": {"description": "Aufforderung zum Auswählen eines Datums"}, "deleteDate": "<PERSON><PERSON>", "@deleteDate": {"description": "Tooltip für das Löschen eines Datums"}, "resumeFileName": "Lebenslauf_{timestamp}_{name}", "@resumeFileName": {"description": "Dateiname für den Lebenslauf"}, "autofillFromResume": "Automatisch aus Lebenslauf ausfüllen", "@autofillFromResume": {"description": "Text für die Option zum automatischen Ausfüllen aus dem Lebenslauf"}, "enabled": "Aktiviert", "@enabled": {"description": "Text für aktivierten Status"}, "disabled": "Deaktiviert", "@disabled": {"description": "Text für deaktivierten Status"}, "skillsInApplication": "Fähigkeiten in Bewerbungen", "@skillsInApplication": {"description": "Überschrift für Fähigkeiten in Bewerbungen"}, "workExperienceInApplication": "Berufserfahrung in Bewerbungen", "@workExperienceInApplication": {"description": "Überschrift für Berufserfahrung in Bewerbungen"}, "educationInApplication": "Bildung in Anschreiben", "@educationInApplication": {"description": "<PERSON>n<PERSON><PERSON> für die Option, die Bildung in Anschreiben zu verwenden"}, "dateFormat": "dd.<PERSON><PERSON><PERSON>yyyy", "@dateFormat": {"description": "Format für die Anzeige von Datumswerten"}, "present": "heute", "@present": {"description": "Anzeige für aktuelles Datum als Endedatum"}, "personalStyle": "Persönlicher Stil", "@personalStyle": {"description": "Bezeichnung für den persönlichen Stil"}}
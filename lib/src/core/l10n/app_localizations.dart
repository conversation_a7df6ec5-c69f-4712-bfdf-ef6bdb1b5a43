import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart' deferred as app_localizations_de;

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('de')
  ];

  /// Der Titel der Anwendung
  ///
  /// In de, this message translates to:
  /// **'Bewerbung KI'**
  String get appTitle;

  /// Titel des Jobsuche-Tabs
  ///
  /// In de, this message translates to:
  /// **'Jobsuche'**
  String get jobSearchTitle;

  /// Titel des Favoriten-Tabs
  ///
  /// In de, this message translates to:
  /// **'Favoriten'**
  String get favoritesTitle;

  /// Titel des Profil-Tabs
  ///
  /// In de, this message translates to:
  /// **'Profil'**
  String get profileTitle;

  /// Titel des Einstellungen-Tabs
  ///
  /// In de, this message translates to:
  /// **'Einstellungen'**
  String get settingsTitle;

  /// Titel des Anmelde-Bildschirms
  ///
  /// In de, this message translates to:
  /// **'Anmelden'**
  String get loginTitle;

  /// Titel des Registrierungs-Bildschirms
  ///
  /// In de, this message translates to:
  /// **'Registrieren'**
  String get signupTitle;

  /// Titel des Passwort-vergessen-Bildschirms
  ///
  /// In de, this message translates to:
  /// **'Passwort vergessen'**
  String get forgotPasswordTitle;

  /// Bezeichnung für E-Mail-Felder
  ///
  /// In de, this message translates to:
  /// **'E-Mail'**
  String get email;

  /// Bezeichnung für Passwort-Felder
  ///
  /// In de, this message translates to:
  /// **'Passwort'**
  String get password;

  /// Text für den Anmelde-Button
  ///
  /// In de, this message translates to:
  /// **'Anmelden'**
  String get login;

  /// Text für den Registrierungs-Button
  ///
  /// In de, this message translates to:
  /// **'Registrieren'**
  String get signup;

  /// Text für den Passwort-vergessen-Link
  ///
  /// In de, this message translates to:
  /// **'Passwort vergessen?'**
  String get forgotPassword;

  /// Text für den Passwort-zurücksetzen-Button
  ///
  /// In de, this message translates to:
  /// **'Passwort zurücksetzen'**
  String get resetPassword;

  /// Text für den Such-Button
  ///
  /// In de, this message translates to:
  /// **'Suchen'**
  String get search;

  /// Text für den Filter-Button
  ///
  /// In de, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Bezeichnung für Standort-Felder
  ///
  /// In de, this message translates to:
  /// **'Standort'**
  String get location;

  /// Bezeichnung für die Entfernung bei der Sortierung
  ///
  /// In de, this message translates to:
  /// **'Entfernung'**
  String get distance;

  /// Bezeichnung für Jobtitel-Felder
  ///
  /// In de, this message translates to:
  /// **'Jobtitel'**
  String get jobTitle;

  /// Bezeichnung für die Firma in der Berufserfahrung
  ///
  /// In de, this message translates to:
  /// **'Firma'**
  String get company;

  /// Text für den Bewerben-Button
  ///
  /// In de, this message translates to:
  /// **'Bewerben'**
  String get apply;

  /// Text für den Speichern-Button
  ///
  /// In de, this message translates to:
  /// **'Speichern'**
  String get save;

  /// Text für den Abbrechen-Button
  ///
  /// In de, this message translates to:
  /// **'Abbrechen'**
  String get cancel;

  /// Text für den Löschen-Button
  ///
  /// In de, this message translates to:
  /// **'Löschen'**
  String get delete;

  /// Text für den Bearbeiten-Button
  ///
  /// In de, this message translates to:
  /// **'Bearbeiten'**
  String get edit;

  /// Text für den Zu-Favoriten-hinzufügen-Button
  ///
  /// In de, this message translates to:
  /// **'Zu Favoriten hinzufügen'**
  String get addToFavorites;

  /// Text für den Von-Favoriten-entfernen-Button
  ///
  /// In de, this message translates to:
  /// **'Von Favoriten entfernen'**
  String get removeFromFavorites;

  /// Text, wenn keine Suchergebnisse gefunden wurden
  ///
  /// In de, this message translates to:
  /// **'Keine Ergebnisse gefunden'**
  String get noResults;

  /// Text während des Ladevorgangs
  ///
  /// In de, this message translates to:
  /// **'Wird geladen...'**
  String get loading;

  /// Allgemeiner Fehlertext
  ///
  /// In de, this message translates to:
  /// **'Fehler'**
  String get error;

  /// Text für den Erneut-versuchen-Button
  ///
  /// In de, this message translates to:
  /// **'Erneut versuchen'**
  String get retry;

  /// Text für den Abmelde-Button
  ///
  /// In de, this message translates to:
  /// **'Abmelden'**
  String get logout;

  /// Titel für den Konto-Abschnitt in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'Konto'**
  String get accountSettings;

  /// Titel für den App-Abschnitt in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'App'**
  String get appSettings;

  /// Titel für den Sicherheits-Abschnitt in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'Sicherheit'**
  String get securitySettings;

  /// Titel für den Info-und-Hilfe-Abschnitt in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'Info & Hilfe'**
  String get infoHelp;

  /// Text für den Profil-bearbeiten-Button
  ///
  /// In de, this message translates to:
  /// **'Profil bearbeiten'**
  String get editProfile;

  /// Titel der Passwort-Änderungsseite
  ///
  /// In de, this message translates to:
  /// **'Passwort ändern'**
  String get changePassword;

  /// Bezeichnung für die Premium-Vorteile
  ///
  /// In de, this message translates to:
  /// **'Premium-Vorteile'**
  String get premiumBenefits;

  /// Text für den Darstellungs-Button in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'Darstellung (Theme)'**
  String get appearance;

  /// Text für den Cache-leeren-Button
  ///
  /// In de, this message translates to:
  /// **'Cache leeren'**
  String get clearCache;

  /// Text für den Sprach-Button in den Einstellungen
  ///
  /// In de, this message translates to:
  /// **'Sprache'**
  String get language;

  /// Text für den Über-diese-App-Button
  ///
  /// In de, this message translates to:
  /// **'Über diese App'**
  String get aboutApp;

  /// Text für den Hilfe-und-Feedback-Button
  ///
  /// In de, this message translates to:
  /// **'Hilfe & Feedback'**
  String get helpFeedback;

  /// Text für den Datenschutzerklärung-Button
  ///
  /// In de, this message translates to:
  /// **'Datenschutzerklärung'**
  String get privacyPolicy;

  /// Text für den Inhalt-übersetzen-Button
  ///
  /// In de, this message translates to:
  /// **'Inhalt übersetzen'**
  String get translateContent;

  /// Text für den Originalinhalt-Button
  ///
  /// In de, this message translates to:
  /// **'Originalinhalt'**
  String get originalContent;

  /// Fehlermeldung bei Übersetzungsfehlern
  ///
  /// In de, this message translates to:
  /// **'Fehler bei der Übersetzung'**
  String get translationError;

  /// Erfolgsmeldung bei erfolgreicher Übersetzung
  ///
  /// In de, this message translates to:
  /// **'Übersetzung erfolgreich'**
  String get translationSuccess;

  /// Titel für den Sprachauswahl-Dialog
  ///
  /// In de, this message translates to:
  /// **'Sprache auswählen'**
  String get selectLanguage;

  /// Name der deutschen Sprache
  ///
  /// In de, this message translates to:
  /// **'Deutsch'**
  String get german;

  /// Name der englischen Sprache
  ///
  /// In de, this message translates to:
  /// **'Englisch'**
  String get english;

  /// Name der französischen Sprache
  ///
  /// In de, this message translates to:
  /// **'Französisch'**
  String get french;

  /// Name der spanischen Sprache
  ///
  /// In de, this message translates to:
  /// **'Spanisch'**
  String get spanish;

  /// Name der italienischen Sprache
  ///
  /// In de, this message translates to:
  /// **'Italienisch'**
  String get italian;

  /// Meldung nach Änderung der Sprache
  ///
  /// In de, this message translates to:
  /// **'Sprache geändert'**
  String get languageChanged;

  /// Aufforderung zum Neustart der App
  ///
  /// In de, this message translates to:
  /// **'App neu starten'**
  String get restartApp;

  /// Hinweis, dass ein Neustart erforderlich ist
  ///
  /// In de, this message translates to:
  /// **'Neustart erforderlich'**
  String get restartRequired;

  /// Nachricht, dass die App neu gestartet werden muss
  ///
  /// In de, this message translates to:
  /// **'Bitte starte die App neu, um die Sprachänderung zu übernehmen.'**
  String get restartAppMessage;

  /// Text für die erkannte Sprache
  ///
  /// In de, this message translates to:
  /// **'Erkannte Sprache'**
  String get detectedLanguage;

  /// Text für die Zielsprache der Übersetzung
  ///
  /// In de, this message translates to:
  /// **'Übersetze nach'**
  String get translatingTo;

  /// Text während der Übersetzung
  ///
  /// In de, this message translates to:
  /// **'Übersetzung läuft...'**
  String get translationInProgress;

  /// Willkommenstext
  ///
  /// In de, this message translates to:
  /// **'Willkommen bei'**
  String get welcome;

  /// Beschreibung der App-Funktionalität
  ///
  /// In de, this message translates to:
  /// **'Finden Sie Ihren nächsten Job mit Hilfe von KI.'**
  String get findJobWithAI;

  /// Beschreibung des Onboarding-Prozesses
  ///
  /// In de, this message translates to:
  /// **'Lass uns dein Profil einrichten, damit die KI dir optimal helfen kann, passende Jobs zu finden und überzeugende Bewerbungen zu erstellen.'**
  String get setupProfile;

  /// Überschrift für persönliche Informationen
  ///
  /// In de, this message translates to:
  /// **'Erzähl uns etwas über dich'**
  String get aboutYou;

  /// Erklärung zur Verwendung der Profilinformationen
  ///
  /// In de, this message translates to:
  /// **'Diese Informationen helfen der KI, deine Bewerbungen zu personalisieren.'**
  String get profileHelpInfo;

  /// Bezeichnung für das Namensfeld
  ///
  /// In de, this message translates to:
  /// **'Vollständiger Name'**
  String get fullName;

  /// Bezeichnung für das E-Mail-Feld im Onboarding
  ///
  /// In de, this message translates to:
  /// **'E-Mail (aus Anmeldung)'**
  String get emailFromLogin;

  /// Bezeichnung für das Telefonnummernfeld
  ///
  /// In de, this message translates to:
  /// **'Telefonnummer (Optional)'**
  String get phoneOptional;

  /// Überschrift für den Fähigkeiten-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Deine wichtigsten Fähigkeiten'**
  String get yourSkills;

  /// Beschreibung für den Fähigkeiten-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Nenne uns einige deiner Kernkompetenzen (z.B. Programmiersprachen, Software, Soft Skills).'**
  String get skillsDescription;

  /// Bezeichnung für ein Fähigkeitsfeld
  ///
  /// In de, this message translates to:
  /// **'Fähigkeit'**
  String get skill;

  /// Tooltip für den Fähigkeit-entfernen-Button
  ///
  /// In de, this message translates to:
  /// **'Fähigkeit entfernen'**
  String get removeSkill;

  /// Text für den Button zum Hinzufügen einer Fähigkeit
  ///
  /// In de, this message translates to:
  /// **'Fähigkeit hinzufügen'**
  String get addSkill;

  /// Fehlermeldung, wenn keine Fähigkeit angegeben wurde
  ///
  /// In de, this message translates to:
  /// **'Mindestens eine Fähigkeit benötigt.'**
  String get minSkillRequired;

  /// Überschrift für den Berufserfahrungs-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Deine letzte Position (Optional)'**
  String get lastPosition;

  /// Beschreibung für den Berufserfahrungs-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Gib deine aktuellste oder relevanteste Berufserfahrung an. Mehr Details kannst du später im Profil hinzufügen.'**
  String get experienceDescription;

  /// Bezeichnung für die Position in der Berufserfahrung
  ///
  /// In de, this message translates to:
  /// **'Position'**
  String get position;

  /// Bezeichnung für das Firmenfeld
  ///
  /// In de, this message translates to:
  /// **'Firma'**
  String get firm;

  /// Bezeichnung für das Startdatum
  ///
  /// In de, this message translates to:
  /// **'Startdatum'**
  String get startDate;

  /// Bezeichnung für das Enddatum
  ///
  /// In de, this message translates to:
  /// **'Enddatum'**
  String get endDate;

  /// Text für die Checkbox 'Aktuell beschäftigt'
  ///
  /// In de, this message translates to:
  /// **'Ich arbeite aktuell hier'**
  String get currentlyWorking;

  /// Text für den Zurück-zum-Login-Button
  ///
  /// In de, this message translates to:
  /// **'Zurück zum Login'**
  String get backToLogin;

  /// Text für den KI-Anschreiben-generieren-Button
  ///
  /// In de, this message translates to:
  /// **'KI-Anschreiben generieren'**
  String get generateAICoverLetter;

  /// Text während der Anschreibengenerierung
  ///
  /// In de, this message translates to:
  /// **'Generiere Anschreiben...'**
  String get generatingCoverLetter;

  /// Text nach erfolgreicher Anschreibengenerierung
  ///
  /// In de, this message translates to:
  /// **'Anschreiben generiert ✓'**
  String get coverLetterGenerated;

  /// Aufforderung zum Lösen eines CAPTCHAs
  ///
  /// In de, this message translates to:
  /// **'Bitte CAPTCHA lösen'**
  String get solveCaptcha;

  /// Text während des Ladens einer Werbeanzeige
  ///
  /// In de, this message translates to:
  /// **'Lade Werbung...'**
  String get loadingAd;

  /// Text für den Bestätigen-Button in Dialogen
  ///
  /// In de, this message translates to:
  /// **'Bestätigen'**
  String get confirm;

  /// Text für den OK-Button in Dialogen
  ///
  /// In de, this message translates to:
  /// **'OK'**
  String get ok;

  /// Hinweis zu Nutzungsbedingungen und Datenschutz bei der Registrierung
  ///
  /// In de, this message translates to:
  /// **'Mit deiner Registrierung akzeptierst du unsere Nutzungsbedingungen und Datenschutzerklärung.'**
  String get termsAndPrivacyNotice;

  /// Hinweis, dass direkte API-Aufrufe deaktiviert wurden
  ///
  /// In de, this message translates to:
  /// **'Direkte API-Aufrufe wurden aus Sicherheitsgründen deaktiviert. Die Daten wurden lokal gespeichert.'**
  String get apiCallsDisabled;

  /// Titel für den AI-Test-Dialog
  ///
  /// In de, this message translates to:
  /// **'AI-Test'**
  String get aiTest;

  /// Meldung, dass die AI-Änderung aktiv ist
  ///
  /// In de, this message translates to:
  /// **'AI-Änderung aktiv!'**
  String get aiChangeActive;

  /// Titel für den Abschnitt mit persönlichen Daten
  ///
  /// In de, this message translates to:
  /// **'Persönliche Daten'**
  String get personalData;

  /// Titel für den Lebenslauf-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Lebenslauf (PDF)'**
  String get resume;

  /// Titel für den Fähigkeiten-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Fähigkeiten'**
  String get skills;

  /// Titel für den Berufserfahrungs-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Berufserfahrung'**
  String get workExperience;

  /// Titel für den Ausbildungs-Abschnitt
  ///
  /// In de, this message translates to:
  /// **'Ausbildung'**
  String get education;

  /// Bezeichnung für die KI-Personalisierung
  ///
  /// In de, this message translates to:
  /// **'KI Personalisierung'**
  String get aiPersonalization;

  /// Bezeichnung für das Adressfeld
  ///
  /// In de, this message translates to:
  /// **'Adresse (Optional)'**
  String get address;

  /// Meldung, dass die lokale Auswahl entfernt wurde
  ///
  /// In de, this message translates to:
  /// **'Lokale Auswahl entfernt.'**
  String get localSelectionRemoved;

  /// Text für den Button zum Generieren von Berufsbezeichnungen
  ///
  /// In de, this message translates to:
  /// **'Berufsbezeichnungen generieren'**
  String get generateJobTitles;

  /// Text während der Generierung von Berufsbezeichnungen
  ///
  /// In de, this message translates to:
  /// **'Generiere Berufsbezeichnungen...'**
  String get generatingJobTitles;

  /// Meldung nach erfolgreicher Generierung von Berufsbezeichnungen
  ///
  /// In de, this message translates to:
  /// **'Berufsbezeichnungen erfolgreich generiert!'**
  String get jobTitlesGenerated;

  /// Fehlermeldung bei der Generierung von Berufsbezeichnungen
  ///
  /// In de, this message translates to:
  /// **'Fehler beim Generieren der Berufsbezeichnungen.'**
  String get jobTitlesError;

  /// Text für den Button zum Löschen einer Fähigkeit
  ///
  /// In de, this message translates to:
  /// **'Fähigkeit löschen'**
  String get deleteSkill;

  /// Bestätigungsdialog zum Löschen einer Berufserfahrung
  ///
  /// In de, this message translates to:
  /// **'Berufserfahrung wirklich löschen?'**
  String get deleteWorkExperience;

  /// Bestätigungsdialog zum Löschen einer Ausbildung
  ///
  /// In de, this message translates to:
  /// **'Ausbildung wirklich löschen?'**
  String get deleteEducation;

  /// Überschrift für die generierten Berufsbezeichnungen
  ///
  /// In de, this message translates to:
  /// **'Generierte Berufsbezeichnungen:'**
  String get generatedJobTitles;

  /// Bezeichnung für das Namensfeld
  ///
  /// In de, this message translates to:
  /// **'Name'**
  String get name;

  /// Bezeichnung für das Telefonfeld
  ///
  /// In de, this message translates to:
  /// **'Telefon'**
  String get phone;

  /// Bezeichnung für das Feld der gewünschten Position
  ///
  /// In de, this message translates to:
  /// **'Gewünschte Position'**
  String get desiredPosition;

  /// Bezeichnung für das Gehaltsvorstellungsfeld
  ///
  /// In de, this message translates to:
  /// **'Gehaltsvorstellung'**
  String get salaryClaim;

  /// Bezeichnung für das Feld des bevorzugten Ortes
  ///
  /// In de, this message translates to:
  /// **'Bevorzugter Ort'**
  String get preferredLocation;

  /// Text für nicht angegebene Werte
  ///
  /// In de, this message translates to:
  /// **'Nicht angegeben'**
  String get notSpecified;

  /// Text, wenn keine Berufserfahrung hinzugefügt wurde
  ///
  /// In de, this message translates to:
  /// **'Keine Berufserfahrung hinzugefügt.'**
  String get noWorkExperience;

  /// Bezeichnung für die Beschreibung in der Berufserfahrung
  ///
  /// In de, this message translates to:
  /// **'Beschreibung'**
  String get description;

  /// Text für den Button zum Löschen eines Eintrags
  ///
  /// In de, this message translates to:
  /// **'Eintrag löschen'**
  String get deleteEntry;

  /// Fehlermeldung, wenn das Startdatum nach dem Enddatum liegt
  ///
  /// In de, this message translates to:
  /// **'Start darf nicht nach Ende liegen.'**
  String get startDateError;

  /// Text, wenn keine Ausbildung hinzugefügt wurde
  ///
  /// In de, this message translates to:
  /// **'Keine Ausbildung hinzugefügt.'**
  String get noEducation;

  /// Bezeichnung für die Institution in der Ausbildung
  ///
  /// In de, this message translates to:
  /// **'Institution'**
  String get institution;

  /// Bezeichnung für den Abschluss in der Ausbildung
  ///
  /// In de, this message translates to:
  /// **'Abschluss'**
  String get degree;

  /// Bezeichnung für die Fachrichtung in der Ausbildung
  ///
  /// In de, this message translates to:
  /// **'Fachrichtung (Optional)'**
  String get fieldOfStudy;

  /// Text, wenn keine Fähigkeiten hinzugefügt wurden
  ///
  /// In de, this message translates to:
  /// **'Keine Fähigkeiten hinzugefügt.'**
  String get noSkills;

  /// Platzhaltertext für das Eingabefeld für Fähigkeiten
  ///
  /// In de, this message translates to:
  /// **'Fähigkeit eingeben...'**
  String get enterSkill;

  /// Text für den Button zum Auswählen einer PDF-Datei
  ///
  /// In de, this message translates to:
  /// **'PDF auswählen'**
  String get selectPdf;

  /// Text für den Button zum Auswählen einer neuen PDF-Datei
  ///
  /// In de, this message translates to:
  /// **'Neue PDF auswählen'**
  String get selectNewPdf;

  /// Fehlermeldung bei der Dateiauswahl
  ///
  /// In de, this message translates to:
  /// **'Fehler beim Auswählen der Datei:'**
  String get fileSelectionError;

  /// Fehlermeldung, wenn der Lebenslauf nicht geöffnet werden kann
  ///
  /// In de, this message translates to:
  /// **'Konnte Lebenslauf nicht öffnen: Ungültige URL'**
  String get cannotOpenResume;

  /// Text für den Button zum Aufheben einer Auswahl
  ///
  /// In de, this message translates to:
  /// **'Auswahl aufheben'**
  String get removeSelection;

  /// Bezeichnung für die Relevanz bei der Sortierung
  ///
  /// In de, this message translates to:
  /// **'Relevanz'**
  String get relevance;

  /// Bezeichnung für das Datum bei der Sortierung
  ///
  /// In de, this message translates to:
  /// **'Datum'**
  String get date;

  /// Platzhaltertext für das Eingabefeld für Stichwörter
  ///
  /// In de, this message translates to:
  /// **'Stichwort eingeben'**
  String get enterKeyword;

  /// Bezeichnung für den Umkreis bei der Suche
  ///
  /// In de, this message translates to:
  /// **'Umkreis'**
  String get radius;

  /// Abkürzung für Kilometer
  ///
  /// In de, this message translates to:
  /// **'km'**
  String get km;

  /// Bezeichnung für die Berufe des Benutzers
  ///
  /// In de, this message translates to:
  /// **'Deine Berufe'**
  String get yourProfessions;

  /// Text für die Option, Berufserfahrung einzubeziehen
  ///
  /// In de, this message translates to:
  /// **'Berufserfahrung einbeziehen'**
  String get includeWorkExperience;

  /// Bezeichnung für den bevorzugten Schreibstil
  ///
  /// In de, this message translates to:
  /// **'Bevorzugter Schreibstil'**
  String get preferredWritingStyle;

  /// Professioneller Schreibstil Option
  ///
  /// In de, this message translates to:
  /// **'Professionell'**
  String get writingStyleProfessional;

  /// Informeller Schreibstil Option
  ///
  /// In de, this message translates to:
  /// **'Informell'**
  String get writingStyleCasual;

  /// Kreativer Schreibstil Option
  ///
  /// In de, this message translates to:
  /// **'Kreativ'**
  String get writingStyleCreative;

  /// Direkter Schreibstil Option
  ///
  /// In de, this message translates to:
  /// **'Direkt'**
  String get writingStyleDirect;

  /// Bezeichnung für interessante Hobbys
  ///
  /// In de, this message translates to:
  /// **'Interessante Hobbys'**
  String get interestingHobbies;

  /// Bezeichnung für das aktuelle Passwort
  ///
  /// In de, this message translates to:
  /// **'Aktuelles Passwort'**
  String get currentPassword;

  /// Bezeichnung für das neue Passwort
  ///
  /// In de, this message translates to:
  /// **'Neues Passwort'**
  String get newPassword;

  /// Bezeichnung für die Passwortbestätigung
  ///
  /// In de, this message translates to:
  /// **'Passwort bestätigen'**
  String get confirmPassword;

  /// Erfolgsmeldung nach dem Ändern des Passworts
  ///
  /// In de, this message translates to:
  /// **'Passwort erfolgreich geändert!'**
  String get passwordChanged;

  /// Fehlermeldung beim Ändern des Passworts
  ///
  /// In de, this message translates to:
  /// **'Fehler beim Ändern des Passworts:'**
  String get passwordError;

  /// Fehlermeldung, wenn Passwörter nicht übereinstimmen
  ///
  /// In de, this message translates to:
  /// **'Die Passwörter stimmen nicht überein'**
  String get passwordMismatch;

  /// Fehlermeldung, wenn das Passwort zu kurz ist
  ///
  /// In de, this message translates to:
  /// **'Passwort muss mindestens 6 Zeichen lang sein'**
  String get passwordTooShort;

  /// Filter-Option für Jobs ohne Vorerfahrung
  ///
  /// In de, this message translates to:
  /// **'Ohne Vorerfahrung'**
  String get withoutExperience;

  /// Meldung, wenn keine Jobs gefunden wurden
  ///
  /// In de, this message translates to:
  /// **'Keine Jobs gefunden.'**
  String get noJobsFound;

  /// Beschriftung für das Feld zur Zusammenfassung der Berufserfahrung
  ///
  /// In de, this message translates to:
  /// **'Zusammenfassung deiner Erfahrung'**
  String get experienceSummary;

  /// Hinweis zur KI-Personalisierung
  ///
  /// In de, this message translates to:
  /// **'Hinweis: Diese Informationen helfen der KI, personalisierte Anschreiben für dich zu erstellen.'**
  String get aiPersonalizationHint;

  /// Button zum Speichern des Passworts
  ///
  /// In de, this message translates to:
  /// **'Passwort speichern'**
  String get savePassword;

  /// Aufforderung zur Eingabe eines neuen Passworts
  ///
  /// In de, this message translates to:
  /// **'Bitte gib ein neues Passwort ein'**
  String get enterNewPassword;

  /// Hinweis zur Mindestlänge des Passworts
  ///
  /// In de, this message translates to:
  /// **'Das Passwort muss mindestens 8 Zeichen lang sein'**
  String get passwordMinLength;

  /// Hinweis zur Anforderung eines Großbuchstabens im Passwort
  ///
  /// In de, this message translates to:
  /// **'Das Passwort muss mindestens einen Großbuchstaben enthalten'**
  String get passwordRequireUppercase;

  /// Hinweis zur Anforderung einer Zahl im Passwort
  ///
  /// In de, this message translates to:
  /// **'Das Passwort muss mindestens eine Zahl enthalten'**
  String get passwordRequireNumber;

  /// Aufforderung zur Bestätigung des neuen Passworts
  ///
  /// In de, this message translates to:
  /// **'Bitte bestätige das neue Passwort'**
  String get confirmNewPassword;

  /// Fehlermeldung, wenn kein Benutzer angemeldet ist
  ///
  /// In de, this message translates to:
  /// **'Fehler: Kein Benutzer angemeldet.'**
  String get noUserLoggedIn;

  /// Fehlermeldung beim Ändern des Passworts
  ///
  /// In de, this message translates to:
  /// **'Fehler beim Ändern des Passworts: {message}'**
  String passwordUpdateError(String message);

  /// Allgemeine Fehlermeldung bei unerwarteten Fehlern
  ///
  /// In de, this message translates to:
  /// **'Ein unerwarteter Fehler ist aufgetreten.'**
  String get unexpectedError;

  /// Erfolgsmeldung nach Passwortänderung
  ///
  /// In de, this message translates to:
  /// **'Passwort erfolgreich geändert!'**
  String get passwordUpdatedSuccess;

  /// Aufforderung zum Auswählen eines Datums
  ///
  /// In de, this message translates to:
  /// **'Datum auswählen'**
  String get selectDate;

  /// Tooltip für das Löschen eines Datums
  ///
  /// In de, this message translates to:
  /// **'Datum löschen'**
  String get deleteDate;

  /// Dateiname für den Lebenslauf
  ///
  /// In de, this message translates to:
  /// **'Lebenslauf_{timestamp}_{name}'**
  String resumeFileName(Object name, Object timestamp);

  /// Text für die Option zum automatischen Ausfüllen aus dem Lebenslauf
  ///
  /// In de, this message translates to:
  /// **'Automatisch aus Lebenslauf ausfüllen'**
  String get autofillFromResume;

  /// Text für aktivierten Status
  ///
  /// In de, this message translates to:
  /// **'Aktiviert'**
  String get enabled;

  /// Text für deaktivierten Status
  ///
  /// In de, this message translates to:
  /// **'Deaktiviert'**
  String get disabled;

  /// Überschrift für Fähigkeiten in Bewerbungen
  ///
  /// In de, this message translates to:
  /// **'Fähigkeiten in Bewerbungen'**
  String get skillsInApplication;

  /// Überschrift für Berufserfahrung in Bewerbungen
  ///
  /// In de, this message translates to:
  /// **'Berufserfahrung in Bewerbungen'**
  String get workExperienceInApplication;

  /// Hinweis für die Option, die Bildung in Anschreiben zu verwenden
  ///
  /// In de, this message translates to:
  /// **'Bildung in Anschreiben'**
  String get educationInApplication;

  /// Format für die Anzeige von Datumswerten
  ///
  /// In de, this message translates to:
  /// **'dd.MM.yyyy'**
  String get dateFormat;

  /// Anzeige für aktuelles Datum als Endedatum
  ///
  /// In de, this message translates to:
  /// **'heute'**
  String get present;

  /// Bezeichnung für den persönlichen Stil
  ///
  /// In de, this message translates to:
  /// **'Persönlicher Stil'**
  String get personalStyle;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return lookupAppLocalizations(locale);
  }

  @override
  bool isSupported(Locale locale) => <String>['de'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

Future<AppLocalizations> lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de': return app_localizations_de.loadLibrary().then((dynamic _) => app_localizations_de.AppLocalizationsDe());
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

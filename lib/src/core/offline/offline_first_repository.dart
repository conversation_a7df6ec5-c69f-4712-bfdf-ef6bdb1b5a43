import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logging.dart';

/// Offline-First Repository Pattern für robuste Datenverarbeitung
abstract class OfflineFirstRepository<T> {
  final _log = getLogger('OfflineFirstRepository');
  final Connectivity _connectivity = Connectivity();
  
  /// Cache-Schlüssel für lokale Speicherung
  String get cacheKey;
  
  /// Serialisierung für lokale Speicherung
  Map<String, dynamic> toJson(T item);
  T fromJson(Map<String, dynamic> json);
  List<T> fromJsonList(List<dynamic> jsonList);
  
  /// Remote Data Source (API)
  Future<List<T>> fetchFromRemote(Map<String, dynamic> params);
  Future<T?> fetchSingleFromRemote(String id);
  
  /// Cache-Strategien
  Duration get cacheExpiration => const Duration(hours: 1);
  bool get enableOfflineMode => true;
  
  /// Hauptmethode: Daten abrufen mit Offline-First Strategie
  Future<List<T>> getData({
    Map<String, dynamic> params = const {},
    bool forceRefresh = false,
    bool fallbackToCache = true,
  }) async {
    try {
      // 1. Prüfe Internetverbindung
      final isOnline = await _isConnected();
      
      // 2. Cache-Daten laden
      final cachedData = await _getCachedData(params);
      final isCacheValid = _isCacheValid(params);
      
      // 3. Entscheidungslogik
      if (forceRefresh || (!isCacheValid && isOnline)) {
        // Remote-Daten laden
        return await _fetchAndCache(params, fallbackToCache ? cachedData : null);
      } else if (cachedData.isNotEmpty) {
        // Cache verwenden
        _log.d('Using cached data for $cacheKey');
        
        // Background-Refresh wenn online
        if (isOnline && !isCacheValid) {
          _backgroundRefresh(params);
        }
        
        return cachedData;
      } else if (isOnline) {
        // Keine Cache-Daten, aber online
        return await _fetchAndCache(params, null);
      } else {
        // Offline und keine Cache-Daten
        _log.w('No cached data available and offline for $cacheKey');
        return <T>[];
      }
    } catch (e, stackTrace) {
      _log.e('Error in getData for $cacheKey', error: e, stackTrace: stackTrace);
      
      // Fallback zu Cache bei Fehlern
      if (fallbackToCache) {
        final cachedData = await _getCachedData(params);
        if (cachedData.isNotEmpty) {
          _log.i('Falling back to cached data for $cacheKey');
          return cachedData;
        }
      }
      
      rethrow;
    }
  }
  
  /// Einzelnes Item abrufen
  Future<T?> getSingleItem(String id, {bool forceRefresh = false}) async {
    try {
      final isOnline = await _isConnected();
      
      // Cache-Schlüssel für einzelnes Item
      final itemCacheKey = '${cacheKey}_item_$id';
      final cachedItem = await _getCachedSingleItem(itemCacheKey);
      final isCacheValid = _isSingleItemCacheValid(itemCacheKey);
      
      if (forceRefresh || (!isCacheValid && isOnline)) {
        final remoteItem = await fetchSingleFromRemote(id);
        if (remoteItem != null) {
          await _cacheSingleItem(itemCacheKey, remoteItem);
        }
        return remoteItem;
      } else if (cachedItem != null) {
        // Background-Refresh wenn online
        if (isOnline && !isCacheValid) {
          _backgroundRefreshSingle(id, itemCacheKey);
        }
        return cachedItem;
      } else if (isOnline) {
        final remoteItem = await fetchSingleFromRemote(id);
        if (remoteItem != null) {
          await _cacheSingleItem(itemCacheKey, remoteItem);
        }
        return remoteItem;
      } else {
        return null;
      }
    } catch (e, stackTrace) {
      _log.e('Error in getSingleItem for $id', error: e, stackTrace: stackTrace);
      
      // Fallback zu Cache
      final itemCacheKey = '${cacheKey}_item_$id';
      return await _getCachedSingleItem(itemCacheKey);
    }
  }
  
  /// Daten invalidieren
  Future<void> invalidateCache([Map<String, dynamic>? params]) async {
    final prefs = await SharedPreferences.getInstance();
    
    if (params != null) {
      final specificKey = _buildCacheKey(params);
      await prefs.remove(specificKey);
      await prefs.remove('${specificKey}_timestamp');
    } else {
      // Alle Cache-Einträge für dieses Repository löschen
      final keys = prefs.getKeys().where((key) => key.startsWith(cacheKey));
      for (final key in keys) {
        await prefs.remove(key);
      }
    }
    
    _log.i('Cache invalidated for $cacheKey');
  }
  
  /// Private Hilfsmethoden
  Future<bool> _isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
  
  Future<List<T>> _fetchAndCache(
    Map<String, dynamic> params,
    List<T>? fallbackData,
  ) async {
    try {
      _log.d('Fetching remote data for $cacheKey');
      final remoteData = await fetchFromRemote(params);
      
      // Cache speichern
      await _cacheData(params, remoteData);
      
      return remoteData;
    } catch (e) {
      _log.e('Failed to fetch remote data for $cacheKey: $e');
      
      if (fallbackData != null) {
        _log.i('Using fallback cached data for $cacheKey');
        return fallbackData;
      }
      
      rethrow;
    }
  }
  
  Future<void> _cacheData(Map<String, dynamic> params, List<T> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKeyWithParams = _buildCacheKey(params);
      
      final jsonData = data.map((item) => toJson(item)).toList();
      await prefs.setString(cacheKeyWithParams, json.encode(jsonData));
      await prefs.setInt('${cacheKeyWithParams}_timestamp', DateTime.now().millisecondsSinceEpoch);
      
      _log.d('Cached ${data.length} items for $cacheKeyWithParams');
    } catch (e) {
      _log.e('Failed to cache data for $cacheKey: $e');
    }
  }
  
  Future<List<T>> _getCachedData(Map<String, dynamic> params) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKeyWithParams = _buildCacheKey(params);
      
      final cachedJson = prefs.getString(cacheKeyWithParams);
      if (cachedJson == null) return <T>[];
      
      final List<dynamic> jsonList = json.decode(cachedJson);
      return fromJsonList(jsonList);
    } catch (e) {
      _log.e('Failed to load cached data for $cacheKey: $e');
      return <T>[];
    }
  }
  
  bool _isCacheValid(Map<String, dynamic> params) {
    try {
      final prefs = SharedPreferences.getInstance();
      return prefs.then((p) {
        final cacheKeyWithParams = _buildCacheKey(params);
        final timestamp = p.getInt('${cacheKeyWithParams}_timestamp');
        
        if (timestamp == null) return false;
        
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final now = DateTime.now();
        
        return now.difference(cacheTime) < cacheExpiration;
      }) as bool;
    } catch (e) {
      return false;
    }
  }
  
  Future<void> _cacheSingleItem(String itemCacheKey, T item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(itemCacheKey, json.encode(toJson(item)));
      await prefs.setInt('${itemCacheKey}_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      _log.e('Failed to cache single item: $e');
    }
  }
  
  Future<T?> _getCachedSingleItem(String itemCacheKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(itemCacheKey);
      
      if (cachedJson == null) return null;
      
      final Map<String, dynamic> jsonData = json.decode(cachedJson);
      return fromJson(jsonData);
    } catch (e) {
      _log.e('Failed to load cached single item: $e');
      return null;
    }
  }
  
  bool _isSingleItemCacheValid(String itemCacheKey) {
    try {
      final prefs = SharedPreferences.getInstance();
      return prefs.then((p) {
        final timestamp = p.getInt('${itemCacheKey}_timestamp');
        
        if (timestamp == null) return false;
        
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final now = DateTime.now();
        
        return now.difference(cacheTime) < cacheExpiration;
      }) as bool;
    } catch (e) {
      return false;
    }
  }
  
  String _buildCacheKey(Map<String, dynamic> params) {
    if (params.isEmpty) return cacheKey;
    
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );
    
    final paramString = sortedParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    
    return '${cacheKey}_$paramString';
  }
  
  void _backgroundRefresh(Map<String, dynamic> params) {
    Timer(const Duration(milliseconds: 100), () async {
      try {
        await _fetchAndCache(params, null);
      } catch (e) {
        _log.d('Background refresh failed for $cacheKey: $e');
      }
    });
  }
  
  void _backgroundRefreshSingle(String id, String itemCacheKey) {
    Timer(const Duration(milliseconds: 100), () async {
      try {
        final remoteItem = await fetchSingleFromRemote(id);
        if (remoteItem != null) {
          await _cacheSingleItem(itemCacheKey, remoteItem);
        }
      } catch (e) {
        _log.d('Background refresh failed for single item $id: $e');
      }
    });
  }
}

/// Offline-Status Provider
class OfflineStatusProvider {
  static final _instance = OfflineStatusProvider._internal();
  factory OfflineStatusProvider() => _instance;
  OfflineStatusProvider._internal();
  
  final _connectivity = Connectivity();
  final _controller = StreamController<bool>.broadcast();
  bool _isOnline = true;
  
  Stream<bool> get onlineStatusStream => _controller.stream;
  bool get isOnline => _isOnline;
  
  void initialize() {
    _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (wasOnline != _isOnline) {
        _controller.add(_isOnline);
      }
    });
  }
  
  void dispose() {
    _controller.close();
  }
}

import '../utils/logging.dart';

/// Input Sanitizer für XSS-Schutz und sichere Datenverarbeitung
class InputSanitizer {
  static final _log = getLogger('InputSanitizer');

  // Gefährliche HTML-Tags und Attribute
  static const List<String> _dangerousTags = [
    'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
    'select', 'textarea', 'link', 'meta', 'style', 'base', 'applet',
    'body', 'html', 'head', 'title', 'frame', 'frameset', 'noframes',
  ];

  static const List<String> _dangerousAttributes = [
    'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
    'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
    'javascript:', 'vbscript:', 'data:', 'file:', 'ftp:',
  ];

  static const List<String> _sqlKeywords = [
    'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
    'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', 'DECLARE', 'CAST', 'CONVERT',
    '--', '/*', '*/', ';', 'xp_', 'sp_', 'INFORMATION_SCHEMA',
  ];

  /// Sanitisiert User Input für sichere Verarbeitung
  static String sanitizeUserInput(String input) {
    if (input.isEmpty) return input;

    try {
      String sanitized = input;

      // 1. Entferne gefährliche HTML-Tags
      sanitized = _removeHtmlTags(sanitized);

      // 2. Escape HTML-Entities
      sanitized = _escapeHtmlEntities(sanitized);

      // 3. Entferne JavaScript-Code
      sanitized = _removeJavaScript(sanitized);

      // 4. Entferne SQL-Injection-Versuche
      sanitized = _removeSqlInjection(sanitized);

      // 5. Normalisiere Whitespace
      sanitized = _normalizeWhitespace(sanitized);

      // 6. Begrenze Länge
      sanitized = _limitLength(sanitized, 10000);

      _log.d('Input sanitized: ${input.length} -> ${sanitized.length} chars');
      return sanitized;
    } catch (e) {
      _log.e('Fehler beim Sanitisieren des Inputs', error: e);
      // Im Fehlerfall: Leeren String zurückgeben für Sicherheit
      return '';
    }
  }

  /// Sanitisiert E-Mail-Adressen
  static String sanitizeEmail(String email) {
    if (email.isEmpty) return email;

    try {
      String sanitized = email.trim().toLowerCase();

      // Entferne gefährliche Zeichen
      sanitized = sanitized.replaceAll(RegExp(r'[<>"\(\)\[\]{}\\]'), '');

      // Validiere E-Mail-Format
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(sanitized)) {
        _log.w('Ungültige E-Mail-Adresse erkannt: $email');
        return '';
      }

      return sanitized;
    } catch (e) {
      _log.e('Fehler beim Sanitisieren der E-Mail', error: e);
      return '';
    }
  }

  /// Sanitisiert URLs
  static String sanitizeUrl(String url) {
    if (url.isEmpty) return url;

    try {
      String sanitized = url.trim();

      // Entferne gefährliche Protokolle
      final dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'ftp:'];
      for (final protocol in dangerousProtocols) {
        if (sanitized.toLowerCase().startsWith(protocol)) {
          _log.w('Gefährliches Protokoll in URL erkannt: $url');
          return '';
        }
      }

      // Stelle sicher, dass URL mit http:// oder https:// beginnt
      if (!sanitized.startsWith('http://') && !sanitized.startsWith('https://')) {
        sanitized = 'https://$sanitized';
      }

      // Validiere URL-Format
      try {
        final uri = Uri.parse(sanitized);
        if (!uri.hasScheme || !uri.hasAuthority) {
          _log.w('Ungültige URL erkannt: $url');
          return '';
        }
      } catch (e) {
        _log.w('Ungültige URL-Syntax: $url');
        return '';
      }

      return sanitized;
    } catch (e) {
      _log.e('Fehler beim Sanitisieren der URL', error: e);
      return '';
    }
  }

  /// Sanitisiert Dateinamen
  static String sanitizeFileName(String fileName) {
    if (fileName.isEmpty) return fileName;

    try {
      String sanitized = fileName.trim();

      // Entferne gefährliche Zeichen
      sanitized = sanitized.replaceAll(RegExp(r'[<>:"/\\|?*\x00-\x1f]'), '');

      // Entferne führende/trailing Punkte und Leerzeichen
      sanitized = sanitized.replaceAll(RegExp(r'^[.\s]+|[.\s]+$'), '');

      // Verhindere reservierte Windows-Namen
      final reservedNames = [
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5',
        'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4',
        'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
      ];

      final nameWithoutExt = sanitized.split('.').first.toUpperCase();
      if (reservedNames.contains(nameWithoutExt)) {
        sanitized = 'file_$sanitized';
      }

      // Begrenze Länge
      if (sanitized.length > 255) {
        final extension = sanitized.contains('.') ? sanitized.split('.').last : '';
        final nameLength = 255 - extension.length - 1;
        sanitized = '${sanitized.substring(0, nameLength)}.$extension';
      }

      return sanitized;
    } catch (e) {
      _log.e('Fehler beim Sanitisieren des Dateinamens', error: e);
      return 'sanitized_file';
    }
  }

  /// Sanitisiert JSON-Daten
  static Map<String, dynamic> sanitizeJsonData(Map<String, dynamic> data) {
    try {
      final sanitized = <String, dynamic>{};

      for (final entry in data.entries) {
        final key = sanitizeUserInput(entry.key);
        final value = entry.value;

        if (value is String) {
          sanitized[key] = sanitizeUserInput(value);
        } else if (value is Map<String, dynamic>) {
          sanitized[key] = sanitizeJsonData(value);
        } else if (value is List) {
          sanitized[key] = _sanitizeList(value);
        } else {
          sanitized[key] = value;
        }
      }

      return sanitized;
    } catch (e) {
      _log.e('Fehler beim Sanitisieren der JSON-Daten', error: e);
      return {};
    }
  }

  /// Private Hilfsmethoden
  static String _removeHtmlTags(String input) {
    String result = input;

    // Entferne gefährliche HTML-Tags
    for (final tag in _dangerousTags) {
      result = result.replaceAll(RegExp('<$tag[^>]*>', caseSensitive: false), '');
      result = result.replaceAll(RegExp('</$tag>', caseSensitive: false), '');
    }

    // Entferne alle HTML-Tags
    result = result.replaceAll(RegExp(r'<[^>]*>'), '');

    return result;
  }

  static String _escapeHtmlEntities(String input) {
    return input
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('/', '&#x2F;');
  }

  static String _removeJavaScript(String input) {
    String result = input;

    // Entferne JavaScript-Event-Handler
    for (final attr in _dangerousAttributes) {
      result = result.replaceAll(RegExp(attr, caseSensitive: false), '');
    }

    // Entferne JavaScript-URLs
    result = result.replaceAll(RegExp(r'javascript:[^"]*', caseSensitive: false), '');

    return result;
  }

  static String _removeSqlInjection(String input) {
    String result = input;

    // Entferne SQL-Keywords (nur wenn sie verdächtig aussehen)
    for (final keyword in _sqlKeywords) {
      // Entferne nur wenn das Keyword von Leerzeichen oder Sonderzeichen umgeben ist
      result = result.replaceAll(
        RegExp(r'\b' + RegExp.escape(keyword) + r'\b', caseSensitive: false),
        '',
      );
    }

    return result;
  }

  static String _normalizeWhitespace(String input) {
    return input
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  static String _limitLength(String input, int maxLength) {
    if (input.length <= maxLength) return input;
    return input.substring(0, maxLength);
  }

  static List<dynamic> _sanitizeList(List<dynamic> list) {
    return list.map((item) {
      if (item is String) {
        return sanitizeUserInput(item);
      } else if (item is Map<String, dynamic>) {
        return sanitizeJsonData(item);
      } else if (item is List) {
        return _sanitizeList(item);
      } else {
        return item;
      }
    }).toList();
  }

  /// Validiert ob Input sicher ist
  static bool isInputSafe(String input) {
    try {
      // Prüfe auf gefährliche Patterns
      final dangerousPatterns = [
        RegExp(r'<script[^>]*>', caseSensitive: false),
        RegExp(r'javascript:', caseSensitive: false),
        RegExp(r'on\w+\s*=', caseSensitive: false),
        RegExp(r'(union|select|insert|update|delete|drop)\s+', caseSensitive: false),
        RegExp(r'--|\*\/|\*'),
      ];

      for (final pattern in dangerousPatterns) {
        if (pattern.hasMatch(input)) {
          _log.w('Gefährliches Pattern in Input erkannt: ${pattern.pattern}');
          return false;
        }
      }

      return true;
    } catch (e) {
      _log.e('Fehler bei der Input-Validierung', error: e);
      return false;
    }
  }

  /// Loggt verdächtige Inputs für Security Monitoring
  static void logSuspiciousInput(String input, String source) {
    _log.w('SECURITY: Verdächtiger Input erkannt von $source: ${input.substring(0, input.length.clamp(0, 100))}...');
  }
}

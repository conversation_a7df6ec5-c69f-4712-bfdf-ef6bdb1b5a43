import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Eine Klasse zur Implementierung von SSL-Pinning für HTTP-Anfragen
/// 
/// SSL-Pinning ist eine Sicherheitsmaßnahme, die sicherstellt, dass die App nur mit
/// vertrauenswürdigen Servern kommuniziert, indem die Zertifikate oder öffentlichen Schlüssel
/// der Server überprüft werden.
class SecureHttpClient extends http.BaseClient {
  final _log = getLogger('SecureHttpClient');
  final http.Client _inner;
  
  // Singleton-Instanz
  static final SecureHttpClient _instance = SecureHttpClient._internal();
  
  // Factory-Konstruktor
  factory SecureHttpClient() {
    return _instance;
  }
  
  // Privater Konstruktor für Singleton
  SecureHttpClient._internal() : _inner = _createClient();
  
  // Erstellt einen sicheren HTTP-Client mit SSL-Pinning
  static http.Client _createClient() {
    final HttpClient httpClient = HttpClient()
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        return _validateCertificate(cert, host);
      };
    
    return IOClient(httpClient);
  }
  
  // Validiert ein Zertifikat anhand der gepinnten Zertifikate
  static bool _validateCertificate(X509Certificate cert, String host) {
    // Deaktiviere SSL-Pinning im Debug-Modus für einfachere Entwicklung
    if (kDebugMode) {
      return true;
    }
    
    // Gepinnte Zertifikate für verschiedene Domains
    final Map<String, List<String>> pinnedCertificates = {
      // Supabase
      'vpttdxibvjrfjzbtktqg.supabase.co': [
        // SHA-256 Fingerabdruck des Zertifikats
        // Beispiel: "5E:1E:F2:D5:2D:4D:B5:5E:A9:6E:7A:F2:A1:C3:8A:DE:C3:D3:F3:A8:A3:AA:D0:1A:4F:D9:8A:E2:B6:4F:D5:F5"
        // Hinweis: Diese Fingerabdrücke müssen mit den tatsächlichen Zertifikaten aktualisiert werden
        "REPLACE_WITH_ACTUAL_FINGERPRINT_1",
        "REPLACE_WITH_ACTUAL_FINGERPRINT_2", // Backup-Zertifikat
      ],
      // DeepSeek API
      'api.deepseek.com': [
        "REPLACE_WITH_ACTUAL_FINGERPRINT_1",
        "REPLACE_WITH_ACTUAL_FINGERPRINT_2",
      ],
      // Agentur für Arbeit API
      'rest.arbeitsagentur.de': [
        "REPLACE_WITH_ACTUAL_FINGERPRINT_1",
        "REPLACE_WITH_ACTUAL_FINGERPRINT_2",
      ],
      // Groq API
      'api.groq.com': [
        "REPLACE_WITH_ACTUAL_FINGERPRINT_1",
        "REPLACE_WITH_ACTUAL_FINGERPRINT_2",
      ],
      // Mistral API
      'api.mistral.ai': [
        "REPLACE_WITH_ACTUAL_FINGERPRINT_1",
        "REPLACE_WITH_ACTUAL_FINGERPRINT_2",
      ],
    };
    
    // Überprüfe, ob der Host in der Liste der gepinnten Zertifikate ist
    if (!pinnedCertificates.containsKey(host)) {
      // Wenn der Host nicht in der Liste ist, akzeptiere das Zertifikat
      // Dies kann je nach Sicherheitsanforderungen angepasst werden
      return true;
    }
    
    // Extrahiere den SHA-256 Fingerabdruck des Zertifikats
    final fingerprint = _getFingerprint(cert);

    // Überprüfe, ob der Fingerabdruck in der Liste der gepinnten Zertifikate ist
    return pinnedCertificates[host]!.contains(fingerprint);
  }

  // Extrahiert den SHA-256 Fingerabdruck eines Zertifikats
  static String _getFingerprint(X509Certificate cert) {
    // In einer realen Implementierung würde hier der SHA-256 Fingerabdruck
    // des Zertifikats extrahiert werden
    // Dies ist eine vereinfachte Implementierung
    return cert.sha1.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join(':').toUpperCase();
  }
  
  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    _log.d('Sending request to ${request.url}');
    return _inner.send(request);
  }
  
  @override
  void close() {
    _inner.close();
    super.close();
  }
}

/// Hilfsfunktion zum Erstellen eines sicheren HTTP-Clients
http.Client createSecureHttpClient() {
  return SecureHttpClient();
}

/// Hilfsfunktion zum Senden einer sicheren GET-Anfrage
Future<http.Response> secureGet(String url, {Map<String, String>? headers}) {
  return SecureHttpClient().get(Uri.parse(url), headers: headers);
}

/// Hilfsfunktion zum Senden einer sicheren POST-Anfrage
Future<http.Response> securePost(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
  return SecureHttpClient().post(Uri.parse(url), headers: headers, body: body, encoding: encoding);
}

/// Hilfsfunktion zum Senden einer sicheren PUT-Anfrage
Future<http.Response> securePut(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
  return SecureHttpClient().put(Uri.parse(url), headers: headers, body: body, encoding: encoding);
}

/// Hilfsfunktion zum Senden einer sicheren DELETE-Anfrage
Future<http.Response> secureDelete(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
  return SecureHttpClient().delete(Uri.parse(url), headers: headers, body: body, encoding: encoding);
}

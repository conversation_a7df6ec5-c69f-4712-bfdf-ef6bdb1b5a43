import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logging.dart';

/// Sicherer Datenmanager für sensitive Informationen
/// Ersetzt unverschlüsselte SharedPreferences-Speicherung
class SecureDataManager {
  static final _log = getLogger('SecureDataManager');
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'secure_app_prefs',
      preferencesKeyPrefix: 'secure_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.example.app.secure',
      accountName: 'SecureAppData',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Keys für verschiedene Datentypen
  static const String _userEmailKey = 'user_email_encrypted';
  static const String _userNameKey = 'user_name_encrypted';
  static const String _userExperienceKey = 'user_experience_encrypted';
  static const String _userSkillsKey = 'user_skills_encrypted';
  static const String _userInterestsKey = 'user_interests_encrypted';
  static const String _passwordResetEmailKey = 'password_reset_email_encrypted';
  static const String _cvFileNameKey = 'cv_filename_encrypted';
  static const String _applicationDataKey = 'application_data_encrypted';

  /// Speichert User-E-Mail sicher verschlüsselt
  static Future<void> saveUserEmail(String email) async {
    try {
      final encryptedEmail = _encryptData(email);
      await _secureStorage.write(key: _userEmailKey, value: encryptedEmail);
      _log.i('User-E-Mail sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der User-E-Mail', error: e);
      rethrow;
    }
  }

  /// Lädt User-E-Mail sicher entschlüsselt
  static Future<String?> getUserEmail() async {
    try {
      final encryptedEmail = await _secureStorage.read(key: _userEmailKey);
      if (encryptedEmail == null) return null;
      
      return _decryptData(encryptedEmail);
    } catch (e) {
      _log.e('Fehler beim Laden der User-E-Mail', error: e);
      return null;
    }
  }

  /// Speichert User-Name sicher verschlüsselt
  static Future<void> saveUserName(String name) async {
    try {
      final encryptedName = _encryptData(name);
      await _secureStorage.write(key: _userNameKey, value: encryptedName);
      _log.i('User-Name sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des User-Names', error: e);
      rethrow;
    }
  }

  /// Lädt User-Name sicher entschlüsselt
  static Future<String?> getUserName() async {
    try {
      final encryptedName = await _secureStorage.read(key: _userNameKey);
      if (encryptedName == null) return null;
      
      return _decryptData(encryptedName);
    } catch (e) {
      _log.e('Fehler beim Laden des User-Names', error: e);
      return null;
    }
  }

  /// Speichert User-Experience sicher verschlüsselt
  static Future<void> saveUserExperience(String experience) async {
    try {
      final encryptedExperience = _encryptData(experience);
      await _secureStorage.write(key: _userExperienceKey, value: encryptedExperience);
      _log.i('User-Experience sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der User-Experience', error: e);
      rethrow;
    }
  }

  /// Lädt User-Experience sicher entschlüsselt
  static Future<String?> getUserExperience() async {
    try {
      final encryptedExperience = await _secureStorage.read(key: _userExperienceKey);
      if (encryptedExperience == null) return null;
      
      return _decryptData(encryptedExperience);
    } catch (e) {
      _log.e('Fehler beim Laden der User-Experience', error: e);
      return null;
    }
  }

  /// Speichert User-Skills sicher verschlüsselt
  static Future<void> saveUserSkills(String skills) async {
    try {
      final encryptedSkills = _encryptData(skills);
      await _secureStorage.write(key: _userSkillsKey, value: encryptedSkills);
      _log.i('User-Skills sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der User-Skills', error: e);
      rethrow;
    }
  }

  /// Lädt User-Skills sicher entschlüsselt
  static Future<String?> getUserSkills() async {
    try {
      final encryptedSkills = await _secureStorage.read(key: _userSkillsKey);
      if (encryptedSkills == null) return null;
      
      return _decryptData(encryptedSkills);
    } catch (e) {
      _log.e('Fehler beim Laden der User-Skills', error: e);
      return null;
    }
  }

  /// Speichert User-Interests sicher verschlüsselt
  static Future<void> saveUserInterests(String interests) async {
    try {
      final encryptedInterests = _encryptData(interests);
      await _secureStorage.write(key: _userInterestsKey, value: encryptedInterests);
      _log.i('User-Interests sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der User-Interests', error: e);
      rethrow;
    }
  }

  /// Lädt User-Interests sicher entschlüsselt
  static Future<String?> getUserInterests() async {
    try {
      final encryptedInterests = await _secureStorage.read(key: _userInterestsKey);
      if (encryptedInterests == null) return null;
      
      return _decryptData(encryptedInterests);
    } catch (e) {
      _log.e('Fehler beim Laden der User-Interests', error: e);
      return null;
    }
  }

  /// Speichert Password-Reset-E-Mail sicher verschlüsselt
  static Future<void> savePasswordResetEmail(String email) async {
    try {
      final encryptedEmail = _encryptData(email);
      await _secureStorage.write(key: _passwordResetEmailKey, value: encryptedEmail);
      _log.i('Password-Reset-E-Mail sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der Password-Reset-E-Mail', error: e);
      rethrow;
    }
  }

  /// Lädt Password-Reset-E-Mail sicher entschlüsselt
  static Future<String?> getPasswordResetEmail() async {
    try {
      final encryptedEmail = await _secureStorage.read(key: _passwordResetEmailKey);
      if (encryptedEmail == null) return null;
      
      return _decryptData(encryptedEmail);
    } catch (e) {
      _log.e('Fehler beim Laden der Password-Reset-E-Mail', error: e);
      return null;
    }
  }

  /// Speichert CV-Dateiname sicher verschlüsselt
  static Future<void> saveCvFileName(String fileName) async {
    try {
      final encryptedFileName = _encryptData(fileName);
      await _secureStorage.write(key: _cvFileNameKey, value: encryptedFileName);
      _log.i('CV-Dateiname sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des CV-Dateinamens', error: e);
      rethrow;
    }
  }

  /// Lädt CV-Dateiname sicher entschlüsselt
  static Future<String?> getCvFileName() async {
    try {
      final encryptedFileName = await _secureStorage.read(key: _cvFileNameKey);
      if (encryptedFileName == null) return null;
      
      return _decryptData(encryptedFileName);
    } catch (e) {
      _log.e('Fehler beim Laden des CV-Dateinamens', error: e);
      return null;
    }
  }

  /// Speichert Bewerbungsdaten sicher verschlüsselt
  static Future<void> saveApplicationData(String jobRefnr, Map<String, dynamic> applicationData) async {
    try {
      final dataJson = jsonEncode(applicationData);
      final encryptedData = _encryptData(dataJson);
      final key = '${_applicationDataKey}_$jobRefnr';
      
      await _secureStorage.write(key: key, value: encryptedData);
      _log.i('Bewerbungsdaten für Job $jobRefnr sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der Bewerbungsdaten für Job $jobRefnr', error: e);
      rethrow;
    }
  }

  /// Lädt Bewerbungsdaten sicher entschlüsselt
  static Future<Map<String, dynamic>?> getApplicationData(String jobRefnr) async {
    try {
      final key = '${_applicationDataKey}_$jobRefnr';
      final encryptedData = await _secureStorage.read(key: key);
      if (encryptedData == null) return null;
      
      final decryptedJson = _decryptData(encryptedData);
      return jsonDecode(decryptedJson) as Map<String, dynamic>;
    } catch (e) {
      _log.e('Fehler beim Laden der Bewerbungsdaten für Job $jobRefnr', error: e);
      return null;
    }
  }

  /// Löscht alle gespeicherten User-Daten
  static Future<void> clearAllUserData() async {
    try {
      await _secureStorage.deleteAll();
      _log.i('Alle User-Daten sicher gelöscht');
    } catch (e) {
      _log.e('Fehler beim Löschen der User-Daten', error: e);
      rethrow;
    }
  }

  /// Migriert unverschlüsselte Daten von SharedPreferences zu sicherer Speicherung
  static Future<void> migrateFromSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Migriere E-Mail
      final email = prefs.getString('email');
      if (email != null && email.isNotEmpty) {
        await saveUserEmail(email);
        await prefs.remove('email');
        _log.i('E-Mail von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere Name
      final name = prefs.getString('name');
      if (name != null && name.isNotEmpty) {
        await saveUserName(name);
        await prefs.remove('name');
        _log.i('Name von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere Experience
      final experience = prefs.getString('experience');
      if (experience != null && experience.isNotEmpty) {
        await saveUserExperience(experience);
        await prefs.remove('experience');
        _log.i('Experience von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere Skills
      final skills = prefs.getString('skills');
      if (skills != null && skills.isNotEmpty) {
        await saveUserSkills(skills);
        await prefs.remove('skills');
        _log.i('Skills von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere Interests
      final interests = prefs.getString('interests');
      if (interests != null && interests.isNotEmpty) {
        await saveUserInterests(interests);
        await prefs.remove('interests');
        _log.i('Interests von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere Password Reset E-Mail
      final passwordResetEmail = prefs.getString('password_reset_email');
      if (passwordResetEmail != null && passwordResetEmail.isNotEmpty) {
        await savePasswordResetEmail(passwordResetEmail);
        await prefs.remove('password_reset_email');
        _log.i('Password Reset E-Mail von SharedPreferences zu sicherer Speicherung migriert');
      }

      // Migriere CV-Dateiname
      final cvFileName = prefs.getString('uploadedCVFileName');
      if (cvFileName != null && cvFileName.isNotEmpty) {
        await saveCvFileName(cvFileName);
        await prefs.remove('uploadedCVFileName');
        _log.i('CV-Dateiname von SharedPreferences zu sicherer Speicherung migriert');
      }

      _log.i('Migration von SharedPreferences zu sicherer Speicherung abgeschlossen');
    } catch (e) {
      _log.e('Fehler bei der Migration von SharedPreferences', error: e);
      rethrow;
    }
  }

  /// Verschlüsselt Daten mit SHA-256 Hash
  static String _encryptData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    
    // Einfache Verschlüsselung - in Production sollte AES verwendet werden
    final encrypted = base64.encode(utf8.encode(data));
    return encrypted;
  }

  /// Entschlüsselt Daten
  static String _decryptData(String encryptedData) {
    try {
      final decrypted = utf8.decode(base64.decode(encryptedData));
      return decrypted;
    } catch (e) {
      throw Exception('Fehler beim Entschlüsseln der Daten: $e');
    }
  }

  /// Validiert ob Daten korrekt verschlüsselt sind
  static bool isDataEncrypted(String data) {
    try {
      base64.decode(data);
      return true;
    } catch (e) {
      return false;
    }
  }
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logging.dart';

/// Security Audit Tool für automatische Sicherheitsüberprüfungen
class SecurityAuditTool {
  static final _log = getLogger('SecurityAuditTool');
  static final List<SecurityViolation> _violations = [];

  /// Führt eine vollständige Sicherheitsüberprüfung durch
  static Future<SecurityAuditReport> performFullAudit() async {
    _violations.clear();
    
    _log.i('Starting comprehensive security audit...');
    
    // 1. API Key Security Audit
    await _auditApiKeySecurity();
    
    // 2. Data Storage Security Audit
    await _auditDataStorageSecurity();
    
    // 3. Input Validation Audit
    await _auditInputValidation();
    
    // 4. Network Security Audit
    await _auditNetworkSecurity();
    
    // 5. Privacy Compliance Audit
    await _auditPrivacyCompliance();
    
    // 6. Code Security Audit
    await _auditCodeSecurity();
    
    final report = SecurityAuditReport(
      timestamp: DateTime.now(),
      violations: List.from(_violations),
      overallScore: _calculateSecurityScore(),
    );
    
    _log.i('Security audit completed. Score: ${report.overallScore}/100');
    
    return report;
  }

  /// API Key Security Audit
  static Future<void> _auditApiKeySecurity() async {
    _log.d('Auditing API key security...');
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      // Prüfe auf unverschlüsselte API Keys in SharedPreferences
      for (final key in keys) {
        if (key.toLowerCase().contains('api') || 
            key.toLowerCase().contains('key') ||
            key.toLowerCase().contains('token')) {
          
          final value = prefs.getString(key);
          if (value != null && _looksLikeApiKey(value)) {
            _addViolation(
              SecurityViolationType.unencryptedApiKey,
              'Unverschlüsselter API Key in SharedPreferences: $key',
              SecuritySeverity.critical,
              'Verwende flutter_secure_storage für API Keys',
            );
          }
        }
      }
      
      // Prüfe auf hardcoded API Keys (simuliert)
      await _checkForHardcodedKeys();
      
    } catch (e) {
      _log.e('Error during API key audit: $e');
    }
  }

  /// Data Storage Security Audit
  static Future<void> _auditDataStorageSecurity() async {
    _log.d('Auditing data storage security...');
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      // Prüfe auf sensitive Daten in unverschlüsseltem Storage
      for (final key in keys) {
        final value = prefs.getString(key);
        if (value != null && _containsSensitiveData(value)) {
          _addViolation(
            SecurityViolationType.unencryptedSensitiveData,
            'Sensitive Daten unverschlüsselt gespeichert: $key',
            SecuritySeverity.high,
            'Verschlüssele sensitive Daten vor der Speicherung',
          );
        }
      }
      
      // Prüfe Dateiberechtigungen
      await _checkFilePermissions();
      
    } catch (e) {
      _log.e('Error during data storage audit: $e');
    }
  }

  /// Input Validation Audit
  static Future<void> _auditInputValidation() async {
    _log.d('Auditing input validation...');
    
    // Simuliere Input Validation Checks
    // In einer echten Implementierung würde hier der Code gescannt
    
    final commonVulnerabilities = [
      'SQL Injection Risiko in Datenbankabfragen',
      'XSS Risiko durch unvalidierte User Inputs',
      'Path Traversal Risiko in File Operations',
      'Command Injection Risiko in System Calls',
    ];
    
    // Simuliere gefundene Vulnerabilities (in echter App durch Code-Analyse)
    if (kDebugMode) {
      for (final vuln in commonVulnerabilities.take(1)) {
        _addViolation(
          SecurityViolationType.inputValidation,
          vuln,
          SecuritySeverity.medium,
          'Implementiere proper Input Validation und Sanitization',
        );
      }
    }
  }

  /// Network Security Audit
  static Future<void> _auditNetworkSecurity() async {
    _log.d('Auditing network security...');
    
    // Prüfe SSL/TLS Konfiguration
    await _checkSslConfiguration();
    
    // Prüfe Certificate Pinning
    await _checkCertificatePinning();
    
    // Prüfe auf unsichere HTTP Verbindungen
    await _checkForInsecureConnections();
  }

  /// Privacy Compliance Audit
  static Future<void> _auditPrivacyCompliance() async {
    _log.d('Auditing privacy compliance...');
    
    // GDPR Compliance Checks
    await _checkGdprCompliance();
    
    // Data Minimization Checks
    await _checkDataMinimization();
    
    // Consent Management Checks
    await _checkConsentManagement();
  }

  /// Code Security Audit
  static Future<void> _auditCodeSecurity() async {
    _log.d('Auditing code security...');
    
    // Debug-Modus in Production
    if (kDebugMode) {
      _addViolation(
        SecurityViolationType.debugMode,
        'Debug-Modus in Production Build aktiv',
        SecuritySeverity.medium,
        'Deaktiviere Debug-Modus für Production Builds',
      );
    }
    
    // Logging von sensitiven Daten
    await _checkForSensitiveLogging();
    
    // Unsichere Kryptographie
    await _checkCryptographyUsage();
  }

  /// Hilfsmethoden
  static bool _looksLikeApiKey(String value) {
    // Einfache Heuristik für API Key Erkennung
    return value.length > 20 && 
           RegExp(r'^[a-zA-Z0-9_\-\.]+$').hasMatch(value) &&
           !value.contains(' ');
  }

  static bool _containsSensitiveData(String value) {
    final sensitivePatterns = [
      r'\b\d{4}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{4}\b', // Kreditkarte
      r'\b\d{3}[\s\-]?\d{2}[\s\-]?\d{4}\b', // SSN
      r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', // Email
      r'\b(?:\+49|0)[1-9][0-9]{1,14}\b', // Telefonnummer
    ];
    
    for (final pattern in sensitivePatterns) {
      if (RegExp(pattern).hasMatch(value)) {
        return true;
      }
    }
    
    return false;
  }

  static Future<void> _checkForHardcodedKeys() async {
    // Simuliere Hardcoded Key Detection
    // In echter Implementierung: Code-Scanning
    
    final suspiciousPatterns = [
      'sk_live_',
      'pk_live_',
      'AIza',
      'AKIA',
    ];
    
    // Simuliere Fund (in Debug-Modus)
    if (kDebugMode && suspiciousPatterns.isNotEmpty) {
      _addViolation(
        SecurityViolationType.hardcodedCredentials,
        'Potentiell hardcoded API Key gefunden',
        SecuritySeverity.critical,
        'Entferne hardcoded Credentials aus dem Code',
      );
    }
  }

  static Future<void> _checkFilePermissions() async {
    // Prüfe App-Verzeichnis Berechtigungen
    try {
      final appDir = Directory.current;
      final stat = await appDir.stat();
      
      // Vereinfachte Berechtigungsprüfung
      if (stat.mode & 0x002 != 0) { // World writable
        _addViolation(
          SecurityViolationType.insecureFilePermissions,
          'App-Verzeichnis ist world-writable',
          SecuritySeverity.high,
          'Korrigiere Dateiberechtigungen',
        );
      }
    } catch (e) {
      _log.d('Could not check file permissions: $e');
    }
  }

  static Future<void> _checkSslConfiguration() async {
    // SSL/TLS Konfigurationsprüfung
    _addViolation(
      SecurityViolationType.weakSslConfiguration,
      'SSL Certificate Pinning nicht vollständig implementiert',
      SecuritySeverity.medium,
      'Implementiere vollständiges SSL Certificate Pinning',
    );
  }

  static Future<void> _checkCertificatePinning() async {
    // Certificate Pinning Prüfung
    // Implementierung abhängig von verwendeter HTTP-Library
  }

  static Future<void> _checkForInsecureConnections() async {
    // Prüfe auf HTTP statt HTTPS
    _addViolation(
      SecurityViolationType.insecureConnection,
      'Potentielle HTTP-Verbindungen ohne Verschlüsselung',
      SecuritySeverity.high,
      'Verwende ausschließlich HTTPS für alle Verbindungen',
    );
  }

  static Future<void> _checkGdprCompliance() async {
    // GDPR Compliance Prüfung
    _addViolation(
      SecurityViolationType.privacyCompliance,
      'Fehlende explizite Nutzereinwilligung für Datenverarbeitung',
      SecuritySeverity.medium,
      'Implementiere GDPR-konforme Einwilligungsmechanismen',
    );
  }

  static Future<void> _checkDataMinimization() async {
    // Data Minimization Prüfung
  }

  static Future<void> _checkConsentManagement() async {
    // Consent Management Prüfung
  }

  static Future<void> _checkForSensitiveLogging() async {
    // Prüfe auf Logging von sensitiven Daten
    _addViolation(
      SecurityViolationType.sensitiveDataLogging,
      'Potentielles Logging von sensitiven Daten',
      SecuritySeverity.low,
      'Entferne sensitive Daten aus Log-Ausgaben',
    );
  }

  static Future<void> _checkCryptographyUsage() async {
    // Kryptographie-Nutzung prüfen
  }

  static void _addViolation(
    SecurityViolationType type,
    String description,
    SecuritySeverity severity,
    String recommendation,
  ) {
    _violations.add(SecurityViolation(
      type: type,
      description: description,
      severity: severity,
      recommendation: recommendation,
      timestamp: DateTime.now(),
    ));
  }

  static int _calculateSecurityScore() {
    if (_violations.isEmpty) return 100;
    
    int deductions = 0;
    for (final violation in _violations) {
      switch (violation.severity) {
        case SecuritySeverity.critical:
          deductions += 25;
          break;
        case SecuritySeverity.high:
          deductions += 15;
          break;
        case SecuritySeverity.medium:
          deductions += 8;
          break;
        case SecuritySeverity.low:
          deductions += 3;
          break;
      }
    }
    
    return (100 - deductions).clamp(0, 100);
  }
}

/// Security Audit Report
class SecurityAuditReport {
  final DateTime timestamp;
  final List<SecurityViolation> violations;
  final int overallScore;

  SecurityAuditReport({
    required this.timestamp,
    required this.violations,
    required this.overallScore,
  });

  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== SECURITY AUDIT REPORT ===');
    buffer.writeln('Generated: ${timestamp.toIso8601String()}');
    buffer.writeln('Overall Security Score: $overallScore/100');
    buffer.writeln();

    if (violations.isEmpty) {
      buffer.writeln('✅ No security violations found!');
      return buffer.toString();
    }

    buffer.writeln('Security Violations Found: ${violations.length}');
    buffer.writeln();

    final groupedViolations = <SecuritySeverity, List<SecurityViolation>>{};
    for (final violation in violations) {
      groupedViolations.putIfAbsent(violation.severity, () => []).add(violation);
    }

    for (final severity in SecuritySeverity.values) {
      final violationsForSeverity = groupedViolations[severity] ?? [];
      if (violationsForSeverity.isNotEmpty) {
        buffer.writeln('${_severityIcon(severity)} ${severity.name.toUpperCase()} (${violationsForSeverity.length}):');
        for (final violation in violationsForSeverity) {
          buffer.writeln('  • ${violation.description}');
          buffer.writeln('    💡 ${violation.recommendation}');
          buffer.writeln();
        }
      }
    }

    return buffer.toString();
  }

  String _severityIcon(SecuritySeverity severity) {
    switch (severity) {
      case SecuritySeverity.critical:
        return '🔴';
      case SecuritySeverity.high:
        return '🟠';
      case SecuritySeverity.medium:
        return '🟡';
      case SecuritySeverity.low:
        return '🟢';
    }
  }
}

/// Security Violation
class SecurityViolation {
  final SecurityViolationType type;
  final String description;
  final SecuritySeverity severity;
  final String recommendation;
  final DateTime timestamp;

  SecurityViolation({
    required this.type,
    required this.description,
    required this.severity,
    required this.recommendation,
    required this.timestamp,
  });
}

/// Security Violation Types
enum SecurityViolationType {
  unencryptedApiKey,
  unencryptedSensitiveData,
  hardcodedCredentials,
  inputValidation,
  insecureConnection,
  weakSslConfiguration,
  insecureFilePermissions,
  debugMode,
  sensitiveDataLogging,
  privacyCompliance,
}

/// Security Severity Levels
enum SecuritySeverity {
  critical,
  high,
  medium,
  low,
}

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Zentrale Klasse für sichere File-Operationen mit Error-Handling
class FileErrorHandler {
  static final _log = getLogger('FileErrorHandler');

  /// Sichere Datei-Lese-Operation
  static Future<String?> safeReadFile(
    String filePath, {
    BuildContext? context,
    String? errorMessage,
    String? fallbackContent,
  }) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        _log.w('Datei existiert nicht: $filePath');
        _showErrorIfContext(context, errorMessage ?? 'Datei nicht gefunden');
        return fallbackContent;
      }

      final content = await file.readAsString();
      _log.d('Datei erfolgreich gelesen: $filePath (${content.length} Zeichen)');
      return content;
    } catch (e, stackTrace) {
      _log.e('Fe<PERSON> beim Lesen der Datei $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Lesen der Datei');
      return fallbackContent;
    }
  }

  /// Sichere Datei-Schreib-Operation
  static Future<bool> safeWriteFile(
    String filePath,
    String content, {
    BuildContext? context,
    String? errorMessage,
    bool createDirectories = true,
  }) async {
    try {
      final file = File(filePath);
      
      if (createDirectories) {
        await file.parent.create(recursive: true);
      }

      await file.writeAsString(content);
      _log.d('Datei erfolgreich geschrieben: $filePath (${content.length} Zeichen)');
      return true;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Schreiben der Datei $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Speichern der Datei');
      return false;
    }
  }

  /// Sichere Binärdatei-Lese-Operation
  static Future<Uint8List?> safeReadBytes(
    String filePath, {
    BuildContext? context,
    String? errorMessage,
  }) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        _log.w('Datei existiert nicht: $filePath');
        _showErrorIfContext(context, errorMessage ?? 'Datei nicht gefunden');
        return null;
      }

      final bytes = await file.readAsBytes();
      _log.d('Binärdatei erfolgreich gelesen: $filePath (${bytes.length} Bytes)');
      return bytes;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Lesen der Binärdatei $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Lesen der Datei');
      return null;
    }
  }

  /// Sichere Binärdatei-Schreib-Operation
  static Future<bool> safeWriteBytes(
    String filePath,
    Uint8List bytes, {
    BuildContext? context,
    String? errorMessage,
    bool createDirectories = true,
  }) async {
    try {
      final file = File(filePath);
      
      if (createDirectories) {
        await file.parent.create(recursive: true);
      }

      await file.writeAsBytes(bytes);
      _log.d('Binärdatei erfolgreich geschrieben: $filePath (${bytes.length} Bytes)');
      return true;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Schreiben der Binärdatei $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Speichern der Datei');
      return false;
    }
  }

  /// Sichere Datei-Lösch-Operation
  static Future<bool> safeDeleteFile(
    String filePath, {
    BuildContext? context,
    String? errorMessage,
    bool ignoreNotFound = true,
  }) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        if (ignoreNotFound) {
          _log.d('Datei existiert bereits nicht: $filePath');
          return true;
        } else {
          _log.w('Datei zum Löschen nicht gefunden: $filePath');
          _showErrorIfContext(context, errorMessage ?? 'Datei nicht gefunden');
          return false;
        }
      }

      await file.delete();
      _log.d('Datei erfolgreich gelöscht: $filePath');
      return true;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Löschen der Datei $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Löschen der Datei');
      return false;
    }
  }

  /// Sichere Verzeichnis-Erstellung
  static Future<Directory?> safeCreateDirectory(
    String dirPath, {
    BuildContext? context,
    String? errorMessage,
    bool recursive = true,
  }) async {
    try {
      final directory = Directory(dirPath);
      
      if (await directory.exists()) {
        _log.d('Verzeichnis existiert bereits: $dirPath');
        return directory;
      }

      final createdDir = await directory.create(recursive: recursive);
      _log.d('Verzeichnis erfolgreich erstellt: $dirPath');
      return createdDir;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Erstellen des Verzeichnisses $dirPath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Erstellen des Verzeichnisses');
      return null;
    }
  }

  /// Sichere Datei-Kopier-Operation
  static Future<bool> safeCopyFile(
    String sourcePath,
    String destinationPath, {
    BuildContext? context,
    String? errorMessage,
    bool createDirectories = true,
  }) async {
    try {
      final sourceFile = File(sourcePath);
      
      if (!await sourceFile.exists()) {
        _log.w('Quelldatei existiert nicht: $sourcePath');
        _showErrorIfContext(context, errorMessage ?? 'Quelldatei nicht gefunden');
        return false;
      }

      final destinationFile = File(destinationPath);
      
      if (createDirectories) {
        await destinationFile.parent.create(recursive: true);
      }

      await sourceFile.copy(destinationPath);
      _log.d('Datei erfolgreich kopiert: $sourcePath -> $destinationPath');
      return true;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Kopieren der Datei $sourcePath -> $destinationPath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Kopieren der Datei');
      return false;
    }
  }

  /// Sichere Datei-Größen-Prüfung
  static Future<int?> safeGetFileSize(
    String filePath, {
    BuildContext? context,
    String? errorMessage,
  }) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        _log.w('Datei existiert nicht: $filePath');
        return null;
      }

      final size = await file.length();
      _log.d('Dateigröße ermittelt: $filePath = $size Bytes');
      return size;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Ermitteln der Dateigröße $filePath: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Prüfen der Dateigröße');
      return null;
    }
  }

  /// Sichere Temp-Verzeichnis-Ermittlung
  static Future<Directory?> safeGetTemporaryDirectory({
    BuildContext? context,
    String? errorMessage,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      _log.d('Temp-Verzeichnis ermittelt: ${tempDir.path}');
      return tempDir;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Ermitteln des Temp-Verzeichnisses: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Zugriff auf temporäre Dateien');
      return null;
    }
  }

  /// Sichere App-Dokumente-Verzeichnis-Ermittlung
  static Future<Directory?> safeGetApplicationDocumentsDirectory({
    BuildContext? context,
    String? errorMessage,
  }) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _log.d('App-Dokumente-Verzeichnis ermittelt: ${appDir.path}');
      return appDir;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Ermitteln des App-Dokumente-Verzeichnisses: $e', stackTrace: stackTrace);
      _showErrorIfContext(context, errorMessage ?? 'Fehler beim Zugriff auf App-Dateien');
      return null;
    }
  }

  /// Validiert Dateipfad und -name
  static bool isValidFilePath(String filePath) {
    try {
      final file = File(filePath);
      final path = file.path;
      
      // Prüfe auf ungültige Zeichen
      final invalidChars = RegExp(r'[<>:"|?*]');
      if (invalidChars.hasMatch(path)) {
        return false;
      }
      
      // Prüfe auf zu lange Pfade (Windows-Limit)
      if (path.length > 260) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Bereinigt Dateinamen von ungültigen Zeichen
  static String sanitizeFileName(String fileName) {
    // Entferne oder ersetze ungültige Zeichen
    return fileName
        .replaceAll(RegExp(r'[<>:"|?*\\\/]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .trim();
  }

  /// Hilfsmethode zum Anzeigen von Fehlern
  static void _showErrorIfContext(BuildContext? context, String message) {
    if (context != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }
}

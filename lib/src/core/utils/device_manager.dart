import 'dart:io';
import 'dart:convert';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async' show unawaited;

/// DeviceManager ist verantwortlich für die Verwaltung der Geräte-ID und
/// die Zuordnung von Geräten zu Benutzerprofilen.
class DeviceManager {
  static final DeviceManager _instance = DeviceManager._internal();
  final Logger _log = Logger('DeviceManager');

  factory DeviceManager() => _instance;

  DeviceManager._internal();

  static const String _deviceIdKey = 'device_id';
  static const String _lastUserIdKey = 'last_user_id';

  /// Generiert oder holt eine eindeutige Geräte-ID
  Future<String> getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString(_deviceIdKey);

      if (deviceId == null) {
        deviceId = await _generateDeviceId();
        await prefs.setString(_deviceIdKey, deviceId);
        _log.info('Neue Geräte-ID generiert: $deviceId');
      } else {
        _log.fine('Bestehende Geräte-ID verwendet: $deviceId');
      }

      return deviceId;
    } catch (e, stackTrace) {
      _log.severe('Fehler beim Abrufen der Geräte-ID: $e', e, stackTrace);
      // Fallback: Generiere eine temporäre ID
      final fallbackId = await _generateDeviceId();
      _log.warning('Verwende Fallback-Geräte-ID: $fallbackId');
      return fallbackId;
    }
  }

  /// Generiert eine eindeutige Geräte-ID basierend auf Geräteinformationen
  Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();
    final uuid = const Uuid();
    String deviceData = '';

    try {
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        deviceData =
            '${webInfo.browserName}-${webInfo.platform}-${webInfo.userAgent}';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceData =
            '${androidInfo.brand}-${androidInfo.model}-${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceData =
            '${iosInfo.model}-${iosInfo.systemName}-${iosInfo.identifierForVendor}';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        deviceData = '${windowsInfo.computerName}-${windowsInfo.productId}';
      } else if (Platform.isMacOS) {
        final macOsInfo = await deviceInfo.macOsInfo;
        deviceData = '${macOsInfo.computerName}-${macOsInfo.model}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        deviceData = '${linuxInfo.name}-${linuxInfo.machineId}';
      }
    } catch (e) {
      _log.warning('Fehler beim Abrufen von Geräteinformationen: $e');
      deviceData = 'unknown-device-${DateTime.now().millisecondsSinceEpoch}';
    }

    // Generiere eine UUID basierend auf den Gerätedaten
    // Verwende URL-Namespace (Konstante aus Uuid-Paket)
    return uuid.v5('6ba7b811-9dad-11d1-80b4-00c04fd430c8', deviceData);
  }

  /// Sammelt detaillierte Geräteinformationen für Anti-Manipulation-System
  Future<Map<String, dynamic>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    Map<String, dynamic> info = {
      'platform': 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        info.addAll({
          'platform': 'web',
          'browser_name': webInfo.browserName.name,
          'platform_name': webInfo.platform,
          'user_agent': webInfo.userAgent,
          'language': webInfo.language,
          'vendor': webInfo.vendor,
        });
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info.addAll({
          'platform': 'android',
          'brand': androidInfo.brand,
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'product': androidInfo.product,
          'device': androidInfo.device,
          'android_id': androidInfo.id,
          'version_release': androidInfo.version.release,
          'version_sdk_int': androidInfo.version.sdkInt,
          'is_physical_device': androidInfo.isPhysicalDevice,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info.addAll({
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'system_name': iosInfo.systemName,
          'system_version': iosInfo.systemVersion,
          'identifier_for_vendor': iosInfo.identifierForVendor,
          'is_physical_device': iosInfo.isPhysicalDevice,
          'machine': iosInfo.utsname.machine,
        });
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        info.addAll({
          'platform': 'windows',
          'computer_name': windowsInfo.computerName,
          'product_id': windowsInfo.productId,
          'product_name': windowsInfo.productName,
          'registered_owner': windowsInfo.registeredOwner,
          'release_id': windowsInfo.releaseId,
        });
      } else if (Platform.isMacOS) {
        final macOsInfo = await deviceInfo.macOsInfo;
        info.addAll({
          'platform': 'macos',
          'computer_name': macOsInfo.computerName,
          'model': macOsInfo.model,
          'kernel_version': macOsInfo.kernelVersion,
          'os_release': macOsInfo.osRelease,
          'major_version': macOsInfo.majorVersion,
          'minor_version': macOsInfo.minorVersion,
        });
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        info.addAll({
          'platform': 'linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'machine_id': linuxInfo.machineId,
          'variant': linuxInfo.variant,
        });
      }
    } catch (e) {
      _log.warning('Fehler beim Sammeln von Geräteinformationen: $e');
      info['error'] = e.toString();
    }

    return info;
  }

  /// Speichert die Benutzer-ID des zuletzt angemeldeten Benutzers
  Future<void> saveLastUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastUserIdKey, userId);
    _log.fine('Letzte Benutzer-ID gespeichert: $userId');
  }

  /// Holt die Benutzer-ID des zuletzt angemeldeten Benutzers
  Future<String?> getLastUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString(_lastUserIdKey);
    _log.fine('Letzte Benutzer-ID abgerufen: $userId');
    return userId;
  }

  /// Registriert das aktuelle Gerät für einen Benutzer in Supabase (NEUES ANTI-MANIPULATION-SYSTEM)
  Future<Map<String, dynamic>> registerDeviceForUser(String userId) async {
    try {
      final deviceId = await getDeviceId();
      final deviceInfo = await getDeviceInfo();
      final client = Supabase.instance.client;

      // Prüfe, ob der Benutzer angemeldet ist
      if (client.auth.currentUser?.id != userId) {
        _log.warning('Benutzer nicht angemeldet oder ID-Konflikt');
        return {
          'success': false,
          'error': 'Benutzer nicht angemeldet',
          'device_valid': false,
        };
      }

      _log.info(
        '🔐 ANTI-MANIPULATION: Registriere Device $deviceId für User $userId',
      );

      // Verwende die neue register_user_device RPC-Funktion
      // WICHTIG: Parameter-Reihenfolge beachten: (p_user_id, p_device_id, p_device_info)
      final response = await client.rpc(
        'register_user_device',
        params: {
          'p_user_id': userId,
          'p_device_id': deviceId,
          'p_device_info': deviceInfo,
        },
      );

      // RPC-Funktion gibt JSONB-Objekt zurück, nicht Liste
      if (response != null) {
        Map<String, dynamic> result;

        // Handle sowohl direkte Map als auch Liste mit einem Element
        if (response is Map<String, dynamic>) {
          result = response;
        } else if (response is List && response.isNotEmpty) {
          result = response.first as Map<String, dynamic>;
        } else {
          _log.severe(
            '❌ ANTI-MANIPULATION: Unerwartete Antwort-Struktur: $response',
          );
          return {
            'success': false,
            'error': 'Unerwartete Server-Antwort',
            'device_valid': false,
          };
        }

        final success = result['success'] ?? false;
        final action = result['action'] ?? '';
        final message = result['message'] ?? '';
        final reason = result['reason'] ?? '';
        final originalUserEmail = result['original_user_email'];
        final originalUserId = result['original_user_id'];
        final zeroCredit = result['zero_credit'] ?? false;
        final premiumRequired = result['premium_required'] ?? false;

        if (success) {
          if (action == 'zero_credit') {
            _log.warning(
              '⚠️ ANTI-MANIPULATION: GUTHABEN AUF 0 GESETZT - $message',
            );
            _log.warning(
              '💡 ANTI-MANIPULATION: Original-Account: $originalUserEmail',
            );
            _log.warning(
              '🚫 ANTI-MANIPULATION: Premium erforderlich für weitere Bewerbungen',
            );
          } else {
            _log.info(
              '✅ ANTI-MANIPULATION: Device erfolgreich registriert - $action: $message',
            );
          }

          // Speichere die Benutzer-ID lokal (asynchron für Performance)
          unawaited(saveLastUserId(userId));

          return {
            'success': true,
            'action': action,
            'message': message,
            'device_valid': true,
            'reason': reason,
            'original_user_email': originalUserEmail,
            'original_user_id': originalUserId,
            'zero_credit': zeroCredit,
            'premium_required': premiumRequired,
          };
        } else {
          // Duplicate-Key als Erfolg behandeln (bereits registriert)
          final msg = (message ?? '').toString().toLowerCase();
          if (msg.contains('duplicate key') ||
              msg.contains('unique constraint')) {
            _log.info(
              '🔁 ANTI-MANIPULATION: Device bereits registriert (Duplicate Key)',
            );
            return {
              'success': true,
              'action': action,
              'message': message,
              'device_valid': true,
              'reason': reason,
              'original_user_email': originalUserEmail,
              'original_user_id': originalUserId,
              'zero_credit': zeroCredit,
              'premium_required': premiumRequired,
            };
          }

          _log.severe(
            '❌ ANTI-MANIPULATION: Device-Registrierung fehlgeschlagen - $action: $message',
          );
          return {
            'success': false,
            'error': message,
            'device_valid': false,
            'reason': reason,
          };
        }
      }

      _log.severe(
        '❌ ANTI-MANIPULATION: Unerwartete Antwort von register_user_device',
      );
      return {
        'success': false,
        'error': 'Unerwartete Server-Antwort',
        'device_valid': false,
      };
    } catch (e, stackTrace) {
      final msg = e.toString();
      if (msg.contains('duplicate key') || msg.contains('unique constraint')) {
        _log.info(
          '🔁 ANTI-MANIPULATION: Device bereits registriert (Duplicate Key)',
        );
        return {
          'success': true,
          'error': null,
          'device_valid': true,
          'reason': null,
        };
      }

      _log.severe(
        '❌ ANTI-MANIPULATION: Fehler bei Device-Registrierung: $e',
        e,
        stackTrace,
      );
      return {
        'success': false,
        'error': 'Fehler bei der Geräteregistrierung: $e',
        'device_valid': false,
      };
    }
  }

  /// Validiert das aktuelle Gerät für Bewerbungen (ANTI-MANIPULATION-SYSTEM)
  Future<Map<String, dynamic>> validateDeviceForApplications(
    String userId,
  ) async {
    try {
      final deviceId = await getDeviceId();
      final client = Supabase.instance.client;

      _log.info(
        '🔍 ANTI-MANIPULATION: Validiere Device $deviceId für User $userId',
      );

      // Verwende die neue validate_device_for_applications RPC-Funktion
      final response = await client.rpc(
        'validate_device_for_applications',
        params: {'p_user_id': userId, 'p_device_id': deviceId},
      );

      // RPC-Funktion gibt JSONB-Objekt zurück, nicht Liste
      if (response != null) {
        Map<String, dynamic> result;

        // Handle sowohl direkte Map als auch Liste mit einem Element
        if (response is Map<String, dynamic>) {
          result = response;
        } else if (response is List && response.isNotEmpty) {
          result = response.first as Map<String, dynamic>;
        } else {
          _log.severe(
            '❌ ANTI-MANIPULATION: Unerwartete Antwort-Struktur: $response',
          );
          return {'is_valid': false, 'reason': 'Unerwartete Server-Antwort'};
        }

        // Akzeptiere sowohl 'valid' als auch 'is_valid' vom Server
        final isValid = (result['valid'] ?? result['is_valid']) == true;
        final reason = result['reason'] ?? 'Unbekannter Fehler';
        final message = result['message'] ?? '';
        final blockedUntil = result['blocked_until'];

        if (isValid) {
          _log.info(
            '✅ ANTI-MANIPULATION: Device validiert für Bewerbungen - $message',
          );
          return {'is_valid': true, 'reason': reason, 'blocked_until': null};
        } else {
          _log.warning(
            '⚠️ ANTI-MANIPULATION: Device nicht valid - $reason: $message',
          );
          return {
            'is_valid': false,
            'reason': reason,
            'blocked_until': blockedUntil,
          };
        }
      }

      _log.severe(
        '❌ ANTI-MANIPULATION: Unerwartete Antwort von validate_device_for_applications',
      );
      return {
        'is_valid': false,
        'reason': 'Unerwartete Server-Antwort',
        'blocked_until': null,
      };
    } catch (e, stackTrace) {
      _log.severe(
        '❌ ANTI-MANIPULATION: Fehler bei Device-Validation: $e',
        e,
        stackTrace,
      );
      return {
        'is_valid': false,
        'reason': 'Fehler bei der Device-Validation: $e',
        'blocked_until': null,
      };
    }
  }

  /// Prüft, ob das aktuelle Gerät für einen Benutzer registriert ist (LEGACY - für Kompatibilität)
  Future<bool> isDeviceRegisteredForUser(String userId) async {
    try {
      final validation = await validateDeviceForApplications(userId);
      return validation['is_valid'] ?? false;
    } catch (e) {
      _log.severe('Fehler bei der Prüfung der Geräteregistrierung: $e');
      return false;
    }
  }

  /// Entfernt das aktuelle Gerät aus der Liste der registrierten Geräte eines Benutzers
  Future<void> unregisterDeviceForUser(String userId) async {
    try {
      final deviceId = await getDeviceId();
      final client = Supabase.instance.client;

      // Hole die aktuellen Geräte-IDs des Benutzers
      final response =
          await client
              .from('profiles')
              .select('device_ids')
              .eq('id', userId)
              .maybeSingle();

      if (response != null && response['device_ids'] != null) {
        List<String> deviceIds = [];
        if (response['device_ids'] is List) {
          deviceIds = List<String>.from(response['device_ids']);
        } else if (response['device_ids'] is String) {
          // Versuche, JSON zu parsen, falls es als String gespeichert ist
          try {
            final List<dynamic> parsedIds = jsonDecode(response['device_ids']);
            deviceIds = parsedIds.map((id) => id.toString()).toList();
          } catch (e) {
            _log.warning('Fehler beim Parsen der Geräte-IDs: $e');
            return;
          }
        }

        // Entferne die Geräte-ID, wenn sie vorhanden ist
        if (deviceIds.contains(deviceId)) {
          deviceIds.remove(deviceId);

          // Aktualisiere die Geräte-IDs in Supabase
          await client
              .from('profiles')
              .update({'device_ids': deviceIds})
              .eq('id', userId);

          _log.info('Gerät für Benutzer $userId entfernt: $deviceId');
        }
      }
    } catch (e) {
      _log.severe('Fehler bei der Geräteabmeldung: $e');
    }
  }
}

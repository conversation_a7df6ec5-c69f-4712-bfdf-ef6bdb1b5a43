import 'package:flutter/material.dart';

/// Eine Utility-Klasse für responsive Design-Funktionen
class ResponsiveUtils {
  /// Singleton-Instanz
  static final ResponsiveUtils _instance = ResponsiveUtils._internal();
  factory ResponsiveUtils() => _instance;
  ResponsiveUtils._internal();

  /// Bildschirmbreiten-Breakpoints
  static const double mobileBreakpoint = 480;
  static const double tabletBreakpoint = 768;
  static const double desktopBreakpoint = 1024;

  /// Minimale Containerbreite gemäß Style Guide
  static const double minContainerWidth = 320;

  /// Bestimmt den Gerätetyp basierend auf der Bildschirmbreite
  DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// Gibt einen Wert basierend auf dem Gerätetyp zurück
  T getValueForDeviceType<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// Gibt einen Wert basierend auf der Bildschirmbreite zurück
  double getResponsiveValue({
    required BuildContext context,
    required double defaultValue,
    double? multiplier,
    double? maxValue,
    double? minValue,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final actualMultiplier = multiplier ?? 1.0;
    
    // Berechne den Wert basierend auf der Bildschirmbreite
    double value = defaultValue * (screenWidth / 375) * actualMultiplier;
    
    // Begrenze den Wert, falls nötig
    if (maxValue != null && value > maxValue) {
      value = maxValue;
    }
    if (minValue != null && value < minValue) {
      value = minValue;
    }
    
    return value;
  }

  /// Gibt die Bildschirmgröße zurück
  Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// Gibt die Bildschirmbreite zurück
  double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Gibt die Bildschirmhöhe zurück
  double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Gibt die Höhe der Statusleiste zurück
  double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// Gibt die Höhe der Navigationsleiste zurück
  double getNavigationBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// Gibt die Höhe der Tastatur zurück
  double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Prüft, ob die Tastatur sichtbar ist
  bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Gibt die verfügbare Höhe zurück (Bildschirmhöhe - Statusleiste - Navigationsleiste)
  double getAvailableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  /// Gibt die verfügbare Breite zurück (Bildschirmbreite - horizontale Paddings)
  double getAvailableWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }

  /// Gibt einen Padding-Wert zurück, der die Navigationsleiste berücksichtigt
  EdgeInsets getSafeAreaPadding(BuildContext context, {
    double left = 0, 
    double top = 0, 
    double right = 0, 
    double bottom = 0,
    bool includeNavigationBar = true,
  }) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      left: left + mediaQuery.padding.left,
      top: top + mediaQuery.padding.top,
      right: right + mediaQuery.padding.right,
      bottom: bottom + (includeNavigationBar ? mediaQuery.padding.bottom : 0),
    );
  }
}

/// Enum für Gerätetypen
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// Extension-Methoden für BuildContext
extension ResponsiveContext on BuildContext {
  /// Gibt die ResponsiveUtils-Instanz zurück
  ResponsiveUtils get responsive => ResponsiveUtils();
  
  /// Gibt den Gerätetyp zurück
  DeviceType get deviceType => ResponsiveUtils().getDeviceType(this);
  
  /// Gibt die Bildschirmgröße zurück
  Size get screenSize => ResponsiveUtils().getScreenSize(this);
  
  /// Gibt die Bildschirmbreite zurück
  double get screenWidth => ResponsiveUtils().getScreenWidth(this);
  
  /// Gibt die Bildschirmhöhe zurück
  double get screenHeight => ResponsiveUtils().getScreenHeight(this);
  
  /// Gibt die Höhe der Statusleiste zurück
  double get statusBarHeight => ResponsiveUtils().getStatusBarHeight(this);
  
  /// Gibt die Höhe der Navigationsleiste zurück
  double get navigationBarHeight => ResponsiveUtils().getNavigationBarHeight(this);
  
  /// Gibt die Höhe der Tastatur zurück
  double get keyboardHeight => ResponsiveUtils().getKeyboardHeight(this);
  
  /// Prüft, ob die Tastatur sichtbar ist
  bool get isKeyboardVisible => ResponsiveUtils().isKeyboardVisible(this);
  
  /// Gibt die verfügbare Höhe zurück
  double get availableHeight => ResponsiveUtils().getAvailableHeight(this);
  
  /// Gibt die verfügbare Breite zurück
  double get availableWidth => ResponsiveUtils().getAvailableWidth(this);
  
  /// Prüft, ob das Gerät ein Mobiltelefon ist
  bool get isMobile => deviceType == DeviceType.mobile;
  
  /// Prüft, ob das Gerät ein Tablet ist
  bool get isTablet => deviceType == DeviceType.tablet;
  
  /// Prüft, ob das Gerät ein Desktop ist
  bool get isDesktop => deviceType == DeviceType.desktop;
}

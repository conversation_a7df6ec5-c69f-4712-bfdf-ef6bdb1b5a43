import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

/// Erweiterte Logger-Klasse mit strukturiertem Logging und Error-Tracking
class EnhancedLogger {
  static final Map<String, EnhancedLogger> _loggers = {};
  static late Directory _logDirectory;
  static bool _initialized = false;
  
  final String _component;
  final Logger _logger;
  
  EnhancedLogger._(this._component) : _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  /// Initialisiert das Logging-System
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _logDirectory = Directory('${appDir.path}/logs');
      await _logDirectory.create(recursive: true);
      _initialized = true;
      
      // Bereinige alte Log-Dateien (älter als 7 Tage)
      await _cleanupOldLogs();
    } catch (e) {
      debugPrint('Fehler beim Initialisieren des Logging-Systems: $e');
    }
  }

  /// Erstellt oder gibt einen Logger für eine Komponente zurück
  static EnhancedLogger getLogger(String component) {
    return _loggers.putIfAbsent(component, () => EnhancedLogger._(component));
  }

  /// Loggt eine Debug-Nachricht
  void d(String message, {Map<String, dynamic>? context}) {
    _logWithContext(Level.debug, message, context: context);
  }

  /// Loggt eine Info-Nachricht
  void i(String message, {Map<String, dynamic>? context}) {
    _logWithContext(Level.info, message, context: context);
  }

  /// Loggt eine Warnung
  void w(String message, {dynamic error, Map<String, dynamic>? context}) {
    _logWithContext(Level.warning, message, error: error, context: context);
  }

  /// Loggt einen Fehler
  void e(String message, {dynamic error, StackTrace? stackTrace, Map<String, dynamic>? context}) {
    _logWithContext(Level.error, message, error: error, stackTrace: stackTrace, context: context);
    _writeErrorToFile(message, error, stackTrace, context);
  }

  /// Loggt einen kritischen Fehler
  void f(String message, {dynamic error, StackTrace? stackTrace, Map<String, dynamic>? context}) {
    _logWithContext(Level.fatal, message, error: error, stackTrace: stackTrace, context: context);
    _writeErrorToFile(message, error, stackTrace, context, isFatal: true);
  }

  /// Loggt Performance-Metriken
  void performance(String operation, Duration duration, {Map<String, dynamic>? context}) {
    final perfContext = {
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
      'duration_readable': '${duration.inMilliseconds}ms',
      ...?context,
    };
    
    if (duration.inMilliseconds > 1000) {
      w('Langsame Operation erkannt: $operation', context: perfContext);
    } else {
      d('Performance: $operation', context: perfContext);
    }
  }

  /// Loggt User-Aktionen für Analytics
  void userAction(String action, {Map<String, dynamic>? parameters}) {
    final actionContext = {
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      'component': _component,
      ...?parameters,
    };
    
    i('User Action: $action', context: actionContext);
    _writeUserActionToFile(action, actionContext);
  }

  /// Loggt API-Aufrufe
  void apiCall(String endpoint, String method, {
    int? statusCode,
    Duration? duration,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
    dynamic error,
  }) {
    final apiContext = {
      'endpoint': endpoint,
      'method': method,
      'timestamp': DateTime.now().toIso8601String(),
      if (statusCode != null) 'status_code': statusCode,
      if (duration != null) 'duration_ms': duration.inMilliseconds,
      if (requestData != null) 'request_data': requestData,
      if (responseData != null) 'response_data': responseData,
    };

    if (error != null || (statusCode != null && statusCode >= 400)) {
      e('API Error: $method $endpoint', error: error, context: apiContext);
    } else {
      i('API Call: $method $endpoint', context: apiContext);
    }
  }

  /// Interne Methode für strukturiertes Logging
  void _logWithContext(Level level, String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    final structuredContext = {
      'component': _component,
      'timestamp': DateTime.now().toIso8601String(),
      'level': level.name,
      ...?context,
    };

    final contextString = context != null ? ' | Context: ${jsonEncode(structuredContext)}' : '';
    final fullMessage = '[$_component] $message$contextString';

    switch (level) {
      case Level.debug:
        _logger.d(fullMessage);
        break;
      case Level.info:
        _logger.i(fullMessage);
        break;
      case Level.warning:
        _logger.w(fullMessage, error: error);
        break;
      case Level.error:
        _logger.e(fullMessage, error: error, stackTrace: stackTrace);
        break;
      case Level.fatal:
        _logger.f(fullMessage, error: error, stackTrace: stackTrace);
        break;
      default:
        _logger.i(fullMessage);
    }
  }

  /// Schreibt Fehler in eine separate Datei
  Future<void> _writeErrorToFile(String message, dynamic error, StackTrace? stackTrace, 
      Map<String, dynamic>? context, {bool isFatal = false}) async {
    if (!_initialized) return;

    try {
      final errorData = {
        'timestamp': DateTime.now().toIso8601String(),
        'component': _component,
        'level': isFatal ? 'FATAL' : 'ERROR',
        'message': message,
        'error': error?.toString(),
        'stackTrace': stackTrace?.toString(),
        'context': context,
      };

      final fileName = 'errors_${DateTime.now().toIso8601String().split('T')[0]}.log';
      final file = File('${_logDirectory.path}/$fileName');
      
      await file.writeAsString(
        '${jsonEncode(errorData)}\n',
        mode: FileMode.append,
      );
    } catch (e) {
      debugPrint('Fehler beim Schreiben der Error-Log-Datei: $e');
    }
  }

  /// Schreibt User-Aktionen in eine separate Datei
  Future<void> _writeUserActionToFile(String action, Map<String, dynamic> context) async {
    if (!_initialized) return;

    try {
      final fileName = 'user_actions_${DateTime.now().toIso8601String().split('T')[0]}.log';
      final file = File('${_logDirectory.path}/$fileName');
      
      await file.writeAsString(
        '${jsonEncode(context)}\n',
        mode: FileMode.append,
      );
    } catch (e) {
      debugPrint('Fehler beim Schreiben der User-Action-Log-Datei: $e');
    }
  }

  /// Bereinigt alte Log-Dateien
  static Future<void> _cleanupOldLogs() async {
    try {
      final files = await _logDirectory.list().toList();
      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            debugPrint('Alte Log-Datei gelöscht: ${file.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Fehler beim Bereinigen alter Log-Dateien: $e');
    }
  }

  /// Exportiert Logs für Support-Zwecke
  static Future<List<File>> exportLogs() async {
    if (!_initialized) return [];

    try {
      final files = await _logDirectory.list().toList();
      return files.whereType<File>().toList();
    } catch (e) {
      debugPrint('Fehler beim Exportieren der Logs: $e');
      return [];
    }
  }

  /// Erstellt einen Crash-Report
  static Future<void> createCrashReport(dynamic error, StackTrace stackTrace, {
    Map<String, dynamic>? additionalContext,
  }) async {
    if (!_initialized) return;

    try {
      final crashData = {
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'CRASH',
        'error': error.toString(),
        'stackTrace': stackTrace.toString(),
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'context': additionalContext,
      };

      final fileName = 'crash_${DateTime.now().millisecondsSinceEpoch}.log';
      final file = File('${_logDirectory.path}/$fileName');
      
      await file.writeAsString(jsonEncode(crashData));
      debugPrint('Crash-Report erstellt: ${file.path}');
    } catch (e) {
      debugPrint('Fehler beim Erstellen des Crash-Reports: $e');
    }
  }

  /// Misst die Ausführungszeit einer Operation
  static Future<T> measureTime<T>(String operation, Future<T> Function() function, {
    String? component,
    Map<String, dynamic>? context,
  }) async {
    final stopwatch = Stopwatch()..start();
    final logger = component != null ? getLogger(component) : getLogger('Performance');
    
    try {
      final result = await function();
      stopwatch.stop();
      logger.performance(operation, stopwatch.elapsed, context: context);
      return result;
    } catch (e, stackTrace) {
      stopwatch.stop();
      logger.e('Operation failed: $operation', error: e, stackTrace: stackTrace, context: {
        'duration_ms': stopwatch.elapsedMilliseconds,
        ...?context,
      });
      rethrow;
    }
  }
}

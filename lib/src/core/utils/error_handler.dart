import 'package:flutter/material.dart';

/// Einheitliche Error Handling Utilities für die gesamte App
class ErrorHandler {

  /// Zeigt eine benutzerfreundliche Fehlermeldung als SnackBar an
  static void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        duration: duration,
        action: action,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Zeigt eine Warnung als SnackBar an
  static void showWarningSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning_amber_rounded, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange.shade600,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Zeigt eine Erfolgsmeldung als SnackBar an
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Loggt einen Fehler mit einheitlichem Format
  static void logError(
    String component,
    String message,
    dynamic error, [
    StackTrace? stackTrace,
  ]) {
    debugPrint('ERROR [$component] $message: $error');
    if (stackTrace != null) {
      debugPrint('StackTrace: $stackTrace');
    }
  }

  /// Loggt eine Warnung mit einheitlichem Format
  static void logWarning(
    String component,
    String message, [
    dynamic error,
  ]) {
    debugPrint('WARNING [$component] $message${error != null ? ': $error' : ''}');
  }

  /// Loggt eine Info mit einheitlichem Format
  static void logInfo(
    String component,
    String message,
  ) {
    debugPrint('INFO [$component] $message');
  }

  /// Behandelt SharedPreferences Fehler einheitlich
  static void handleSharedPreferencesError(
    String component,
    dynamic error,
    StackTrace stackTrace, {
    BuildContext? context,
    String? userMessage,
  }) {
    logError(
      component,
      'Fehler beim Zugriff auf SharedPreferences',
      error,
      stackTrace,
    );

    if (context != null && userMessage != null) {
      showWarningSnackBar(context, userMessage);
    }
  }

  /// Behandelt Netzwerkfehler einheitlich
  static void handleNetworkError(
    String component,
    dynamic error,
    StackTrace stackTrace, {
    BuildContext? context,
    String? userMessage,
  }) {
    logError(
      component,
      'Netzwerkfehler',
      error,
      stackTrace,
    );

    if (context != null) {
      showErrorSnackBar(
        context,
        userMessage ?? 'Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung.',
        action: SnackBarAction(
          label: 'Erneut versuchen',
          onPressed: () {
            // Kann von der aufrufenden Komponente überschrieben werden
          },
        ),
      );
    }
  }

  /// Behandelt File I/O Fehler einheitlich
  static void handleFileError(
    String component,
    dynamic error,
    StackTrace stackTrace, {
    BuildContext? context,
    String? userMessage,
  }) {
    logError(
      component,
      'Dateifehler',
      error,
      stackTrace,
    );

    if (context != null) {
      showErrorSnackBar(
        context,
        userMessage ?? 'Fehler beim Zugriff auf Dateien.',
      );
    }
  }

  /// Behandelt JSON parsing Fehler einheitlich
  static void handleJsonError(
    String component,
    dynamic error,
    StackTrace stackTrace, {
    BuildContext? context,
    String? userMessage,
  }) {
    logError(
      component,
      'JSON-Parsing-Fehler',
      error,
      stackTrace,
    );

    if (context != null && userMessage != null) {
      showErrorSnackBar(context, userMessage);
    }
  }

  /// Behandelt API-Fehler einheitlich
  static void handleApiError(
    String component,
    dynamic error,
    StackTrace stackTrace, {
    BuildContext? context,
    String? userMessage,
  }) {
    logError(
      component,
      'API-Fehler',
      error,
      stackTrace,
    );

    if (context != null) {
      showErrorSnackBar(
        context,
        userMessage ?? 'Fehler bei der Kommunikation mit dem Server.',
      );
    }
  }

  /// Konvertiert technische Fehlermeldungen in benutzerfreundliche Nachrichten
  static String getReadableErrorMessage(dynamic error) {
    if (error == null) return 'Unbekannter Fehler';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Netzwerkproblem. Bitte überprüfen Sie Ihre Internetverbindung.';
    }

    if (errorString.contains('timeout')) {
      return 'Zeitüberschreitung. Bitte versuchen Sie es erneut.';
    }

    if (errorString.contains('permission')) {
      return 'Berechtigung verweigert. Bitte überprüfen Sie die App-Berechtigungen.';
    }

    if (errorString.contains('file not found') || errorString.contains('no such file')) {
      return 'Datei nicht gefunden.';
    }

    if (errorString.contains('json') || errorString.contains('format')) {
      return 'Datenformat-Fehler. Bitte versuchen Sie es erneut.';
    }

    if (errorString.contains('auth') || errorString.contains('unauthorized')) {
      return 'Anmeldung erforderlich. Bitte melden Sie sich erneut an.';
    }

    // Fallback auf originale Fehlermeldung (gekürzt)
    final originalMessage = error.toString();
    return originalMessage.length > 100 
        ? '${originalMessage.substring(0, 100)}...'
        : originalMessage;
  }
}

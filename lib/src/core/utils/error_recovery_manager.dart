import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Manager für automatische Error-Recovery-Mechanismen
class ErrorRecoveryManager {
  static final _log = getLogger('ErrorRecoveryManager');
  static final Map<String, int> _errorCounts = {};
  static final Map<String, DateTime> _lastErrorTimes = {};
  static const int _maxRetries = 3;
  static const Duration _cooldownPeriod = Duration(minutes: 5);

  /// Führt eine Operation mit automatischem Recovery aus
  static Future<T?> executeWithRecovery<T>(
    String operationId,
    Future<T> Function() operation, {
    Future<T> Function()? fallbackOperation,
    T? fallbackValue,
    BuildContext? context,
    String? userMessage,
    bool showUserFeedback = true,
  }) async {
    try {
      final result = await operation();
      _resetErrorCount(operationId);
      return result;
    } catch (e, stackTrace) {
      _log.e('Operation $operationId fehlgeschlagen: $e', stackTrace: stackTrace);
      
      final errorCount = _incrementErrorCount(operationId);
      
      if (errorCount <= _maxRetries && !_isInCooldown(operationId)) {
        _log.i('Versuche Recovery für $operationId (Versuch $errorCount/$_maxRetries)');
        
        // Warte kurz vor dem nächsten Versuch
        await Future.delayed(Duration(seconds: errorCount * 2));
        
        return executeWithRecovery<T>(
          operationId,
          operation,
          fallbackOperation: fallbackOperation,
          fallbackValue: fallbackValue,
          context: context,
          userMessage: userMessage,
          showUserFeedback: false, // Verhindere mehrfache User-Nachrichten
        );
      }
      
      // Maximale Versuche erreicht oder in Cooldown
      if (fallbackOperation != null) {
        try {
          _log.i('Führe Fallback-Operation für $operationId aus');
          final result = await fallbackOperation();
          _resetErrorCount(operationId);
          return result;
        } catch (fallbackError) {
          _log.e('Auch Fallback-Operation fehlgeschlagen: $fallbackError');
        }
      }
      
      _setCooldown(operationId);
      
      if (showUserFeedback && context != null) {
        _showRecoveryMessage(context, operationId, userMessage);
      }
      
      return fallbackValue;
    }
  }

  /// Führt eine Netzwerk-Operation mit speziellem Recovery aus
  static Future<T?> executeNetworkOperationWithRecovery<T>(
    String operationId,
    Future<T> Function() operation, {
    T? fallbackValue,
    BuildContext? context,
    bool useOfflineCache = true,
  }) async {
    return executeWithRecovery<T>(
      operationId,
      operation,
      fallbackOperation: useOfflineCache ? () => _loadFromOfflineCache<T>(operationId) : null,
      fallbackValue: fallbackValue,
      context: context,
      userMessage: 'Netzwerkfehler. Versuche es später erneut.',
    );
  }

  /// Führt eine Datei-Operation mit Recovery aus
  static Future<T?> executeFileOperationWithRecovery<T>(
    String operationId,
    Future<T> Function() operation, {
    Future<T> Function()? backupOperation,
    T? fallbackValue,
    BuildContext? context,
  }) async {
    return executeWithRecovery<T>(
      operationId,
      operation,
      fallbackOperation: backupOperation ?? () => _restoreFromBackup<T>(operationId),
      fallbackValue: fallbackValue,
      context: context,
      userMessage: 'Dateifehler. Versuche Wiederherstellung.',
    );
  }

  /// Führt eine Datenbank-Operation mit Recovery aus
  static Future<T?> executeDatabaseOperationWithRecovery<T>(
    String operationId,
    Future<T> Function() operation, {
    T? fallbackValue,
    BuildContext? context,
  }) async {
    return executeWithRecovery<T>(
      operationId,
      operation,
      fallbackOperation: () => _loadFromLocalCache<T>(operationId),
      fallbackValue: fallbackValue,
      context: context,
      userMessage: 'Datenbankfehler. Lade lokale Daten.',
    );
  }

  /// Automatische App-Recovery bei kritischen Fehlern
  static Future<void> performAppRecovery({
    BuildContext? context,
    bool clearCache = false,
    bool resetSettings = false,
  }) async {
    _log.i('Führe App-Recovery durch...');
    
    try {
      if (clearCache) {
        await _clearAppCache();
        _log.i('App-Cache geleert');
      }
      
      if (resetSettings) {
        await _resetAppSettings();
        _log.i('App-Einstellungen zurückgesetzt');
      }
      
      // Bereinige temporäre Dateien
      await _cleanupTempFiles();
      _log.i('Temporäre Dateien bereinigt');
      
      // Setze Error-Zähler zurück
      _errorCounts.clear();
      _lastErrorTimes.clear();
      _log.i('Error-Zähler zurückgesetzt');
      
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App-Recovery abgeschlossen'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e, stackTrace) {
      _log.e('Fehler bei App-Recovery: $e', stackTrace: stackTrace);
      
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App-Recovery fehlgeschlagen'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Prüft den Gesundheitsstatus der App
  static Future<AppHealthStatus> checkAppHealth() async {
    final issues = <String>[];
    
    try {
      // Prüfe Speicherplatz
      final freeSpace = await _getAvailableSpace();
      if (freeSpace < 100 * 1024 * 1024) { // 100MB
        issues.add('Wenig Speicherplatz verfügbar');
      }
      
      // Prüfe Error-Rate
      final errorRate = _calculateErrorRate();
      if (errorRate > 0.1) { // 10% Error-Rate
        issues.add('Hohe Fehlerrate erkannt');
      }
      
      // Prüfe Netzwerkverbindung
      final hasNetwork = await _checkNetworkConnectivity();
      if (!hasNetwork) {
        issues.add('Keine Netzwerkverbindung');
      }
      
      return AppHealthStatus(
        isHealthy: issues.isEmpty,
        issues: issues,
        lastCheck: DateTime.now(),
      );
    } catch (e) {
      _log.e('Fehler bei Gesundheitsprüfung: $e');
      return AppHealthStatus(
        isHealthy: false,
        issues: ['Gesundheitsprüfung fehlgeschlagen'],
        lastCheck: DateTime.now(),
      );
    }
  }

  // Private Hilfsmethoden
  static int _incrementErrorCount(String operationId) {
    _errorCounts[operationId] = (_errorCounts[operationId] ?? 0) + 1;
    _lastErrorTimes[operationId] = DateTime.now();
    return _errorCounts[operationId]!;
  }

  static void _resetErrorCount(String operationId) {
    _errorCounts.remove(operationId);
    _lastErrorTimes.remove(operationId);
  }

  static bool _isInCooldown(String operationId) {
    final lastError = _lastErrorTimes[operationId];
    if (lastError == null) return false;
    
    return DateTime.now().difference(lastError) < _cooldownPeriod;
  }

  static void _setCooldown(String operationId) {
    _lastErrorTimes[operationId] = DateTime.now();
  }

  static void _showRecoveryMessage(BuildContext context, String operationId, String? userMessage) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(userMessage ?? 'Operation $operationId fehlgeschlagen'),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'Erneut versuchen',
          onPressed: () {
            _resetErrorCount(operationId);
          },
        ),
      ),
    );
  }

  static Future<T> _loadFromOfflineCache<T>(String operationId) async {
    // Implementierung für Offline-Cache
    throw UnimplementedError('Offline-Cache für $operationId nicht implementiert');
  }

  static Future<T> _restoreFromBackup<T>(String operationId) async {
    // Implementierung für Backup-Wiederherstellung
    throw UnimplementedError('Backup-Wiederherstellung für $operationId nicht implementiert');
  }

  static Future<T> _loadFromLocalCache<T>(String operationId) async {
    // Implementierung für lokalen Cache
    throw UnimplementedError('Lokaler Cache für $operationId nicht implementiert');
  }

  static Future<void> _clearAppCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      _log.e('Fehler beim Leeren des Caches: $e');
    }
  }

  static Future<void> _resetAppSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('setting_')).toList();
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      _log.e('Fehler beim Zurücksetzen der Einstellungen: $e');
    }
  }

  static Future<void> _cleanupTempFiles() async {
    // Implementierung für Temp-File-Cleanup
  }

  static Future<int> _getAvailableSpace() async {
    // Implementierung für Speicherplatz-Prüfung
    return 1024 * 1024 * 1024; // 1GB als Dummy
  }

  static double _calculateErrorRate() {
    if (_errorCounts.isEmpty) return 0.0;
    
    final totalErrors = _errorCounts.values.reduce((a, b) => a + b);
    final totalOperations = _errorCounts.length * _maxRetries;
    
    return totalErrors / totalOperations;
  }

  static Future<bool> _checkNetworkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}

/// Status der App-Gesundheit
class AppHealthStatus {
  final bool isHealthy;
  final List<String> issues;
  final DateTime lastCheck;

  AppHealthStatus({
    required this.isHealthy,
    required this.issues,
    required this.lastCheck,
  });
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Zentrale Klasse für sichere Navigation mit Error-Handling
class NavigationErrorHandler {
  static final _log = getLogger('NavigationErrorHandler');

  /// Sichere Navigation mit Error-Handling
  static Future<bool> safeGo(
    BuildContext context,
    String location, {
    Object? extra,
    String? fallbackRoute,
    bool showErrorSnackBar = true,
  }) async {
    if (!context.mounted) {
      _log.w('Context nicht mehr mounted, Navigation abgebrochen');
      return false;
    }

    try {
      context.go(location, extra: extra);
      _log.d('Navigation erfolgreich: $location');
      return true;
    } catch (e, stackTrace) {
      _log.e('Navigationsfehler zu $location: $e', stackTrace: stackTrace);
      
      if (showErrorSnackBar && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Navigation fehlgeschlagen: ${_getReadableError(e)}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
            ),
          ),
        );
      }

      // Fallback-Navigation versuchen
      if (fallbackRoute != null && context.mounted) {
        try {
          context.go(fallbackRoute);
          _log.i('Fallback-Navigation erfolgreich: $fallbackRoute');
          return true;
        } catch (fallbackError) {
          _log.e('Auch Fallback-Navigation fehlgeschlagen: $fallbackError');
        }
      }

      return false;
    }
  }

  /// Sichere Push-Navigation mit Error-Handling
  static Future<T?> safePush<T extends Object?>(
    BuildContext context,
    String location, {
    Object? extra,
    String? fallbackRoute,
    bool showErrorSnackBar = true,
  }) async {
    if (!context.mounted) {
      _log.w('Context nicht mehr mounted, Push-Navigation abgebrochen');
      return null;
    }

    try {
      final result = await context.push<T>(location, extra: extra);
      _log.d('Push-Navigation erfolgreich: $location');
      return result;
    } catch (e, stackTrace) {
      _log.e('Push-Navigationsfehler zu $location: $e', stackTrace: stackTrace);
      
      if (showErrorSnackBar && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Navigation fehlgeschlagen: ${_getReadableError(e)}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Fallback-Navigation versuchen
      if (fallbackRoute != null && context.mounted) {
        try {
          return await context.push<T>(fallbackRoute);
        } catch (fallbackError) {
          _log.e('Auch Fallback-Push-Navigation fehlgeschlagen: $fallbackError');
        }
      }

      return null;
    }
  }

  /// Sichere Replace-Navigation mit Error-Handling
  static Future<bool> safeGoReplacement(
    BuildContext context,
    String location, {
    Object? extra,
    String? fallbackRoute,
    bool showErrorSnackBar = true,
  }) async {
    if (!context.mounted) {
      _log.w('Context nicht mehr mounted, Replacement-Navigation abgebrochen');
      return false;
    }

    try {
      context.pushReplacement(location, extra: extra);
      _log.d('Replacement-Navigation erfolgreich: $location');
      return true;
    } catch (e, stackTrace) {
      _log.e('Replacement-Navigationsfehler zu $location: $e', stackTrace: stackTrace);
      
      if (showErrorSnackBar && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Navigation fehlgeschlagen: ${_getReadableError(e)}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Fallback-Navigation versuchen
      if (fallbackRoute != null && context.mounted) {
        try {
          context.pushReplacement(fallbackRoute);
          _log.i('Fallback-Replacement-Navigation erfolgreich: $fallbackRoute');
          return true;
        } catch (fallbackError) {
          _log.e('Auch Fallback-Replacement-Navigation fehlgeschlagen: $fallbackError');
        }
      }

      return false;
    }
  }

  /// Sichere Pop-Navigation mit Error-Handling
  static bool safePop<T extends Object?>(
    BuildContext context, [
    T? result,
  ]) {
    if (!context.mounted) {
      _log.w('Context nicht mehr mounted, Pop-Navigation abgebrochen');
      return false;
    }

    try {
      if (context.canPop()) {
        context.pop(result);
        _log.d('Pop-Navigation erfolgreich');
        return true;
      } else {
        _log.w('Kann nicht zurück navigieren - kein vorheriger Screen');
        return false;
      }
    } catch (e, stackTrace) {
      _log.e('Pop-Navigationsfehler: $e', stackTrace: stackTrace);
      return false;
    }
  }

  /// Sichere Navigation mit Validierung der Route
  static Future<bool> safeGoWithValidation(
    BuildContext context,
    String location, {
    Object? extra,
    String? fallbackRoute,
    bool showErrorSnackBar = true,
    bool Function(String)? routeValidator,
  }) async {
    // Validiere Route falls Validator vorhanden
    if (routeValidator != null && !routeValidator(location)) {
      _log.w('Route-Validierung fehlgeschlagen für: $location');
      if (fallbackRoute != null) {
        return safeGo(context, fallbackRoute, showErrorSnackBar: showErrorSnackBar);
      }
      return false;
    }

    return safeGo(
      context,
      location,
      extra: extra,
      fallbackRoute: fallbackRoute,
      showErrorSnackBar: showErrorSnackBar,
    );
  }

  /// Deep Link Error Handling
  static Future<void> handleDeepLinkError(
    BuildContext context,
    Uri uri,
    dynamic error, {
    String? fallbackRoute = '/login',
  }) async {
    _log.e('Deep Link Fehler für URI $uri: $error');
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Deep Link konnte nicht verarbeitet werden'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 5),
        ),
      );

      if (fallbackRoute != null) {
        await safeGo(context, fallbackRoute, showErrorSnackBar: false);
      }
    }
  }

  /// Konvertiert technische Fehler in benutzerfreundliche Nachrichten
  static String _getReadableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('route') || errorString.contains('path')) {
      return 'Seite nicht gefunden';
    }
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Netzwerkfehler';
    }
    
    if (errorString.contains('timeout')) {
      return 'Zeitüberschreitung';
    }
    
    return 'Unbekannter Navigationsfehler';
  }

  /// Prüft ob eine Route gültig ist
  static bool isValidRoute(String route) {
    try {
      Uri.parse(route);
      return route.startsWith('/') && route.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Erstellt sichere Route-Parameter
  static Map<String, String> createSafeQueryParameters(Map<String, dynamic> params) {
    final safeParams = <String, String>{};
    
    for (final entry in params.entries) {
      try {
        final value = entry.value?.toString() ?? '';
        if (value.isNotEmpty) {
          safeParams[entry.key] = Uri.encodeComponent(value);
        }
      } catch (e) {
        _log.w('Parameter ${entry.key} konnte nicht encodiert werden: $e');
      }
    }
    
    return safeParams;
  }
}

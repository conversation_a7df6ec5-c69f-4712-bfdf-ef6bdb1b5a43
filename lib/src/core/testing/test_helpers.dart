import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/logging.dart';

/// Test-Hilfsfunktionen für einheitliche Tests in der gesamten App
class TestHelpers {
  static final _log = getLogger('TestHelpers');

  /// Erstellt einen ProviderScope für Tests mit Mock-Providern
  static Widget createTestApp({
    required Widget child,
    List<Override>? overrides,
    Map<String, dynamic>? sharedPrefsValues,
  }) {
    return ProviderScope(
      overrides: [
        ...?overrides,
        if (sharedPrefsValues != null)
          sharedPreferencesProvider.overrideWithValue(
            AsyncValue.data(_createMockSharedPreferences(sharedPrefsValues)),
          ),
      ],
      child: MaterialApp(
        home: child,
        theme: ThemeData.light(),
      ),
    );
  }

  /// Erstellt Mock SharedPreferences für Tests
  static SharedPreferences _createMockSharedPreferences(Map<String, dynamic> values) {
    SharedPreferences.setMockInitialValues(values);
    return SharedPreferences.getInstance() as SharedPreferences;
  }

  /// Wartet auf alle Async-Operationen in Tests
  static Future<void> pumpAndSettle(WidgetTester tester, {Duration? duration}) async {
    await tester.pumpAndSettle(duration ?? const Duration(seconds: 5));
  }

  /// Findet Widget mit Retry-Logik für flaky Tests
  static Future<Finder> findWidgetWithRetry(
    WidgetTester tester,
    Finder finder, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      await tester.pump(delay);
      if (tester.any(finder)) {
        return finder;
      }
    }
    throw TestFailure('Widget nicht gefunden nach $maxRetries Versuchen');
  }

  /// Simuliert Netzwerkfehler für Tests
  static Exception createNetworkError([String? message]) {
    return Exception(message ?? 'Simulated network error for testing');
  }

  /// Simuliert API-Fehler für Tests
  static Exception createApiError(int statusCode, [String? message]) {
    return Exception('API Error $statusCode: ${message ?? 'Simulated API error'}');
  }

  /// Erstellt Mock User für Auth-Tests
  static User createMockUser({
    String? id,
    String? email,
    Map<String, dynamic>? userMetadata,
  }) {
    return User(
      id: id ?? 'test-user-id',
      appMetadata: {},
      userMetadata: userMetadata ?? {'full_name': 'Test User'},
      aud: 'authenticated',
      createdAt: DateTime.now().toIso8601String(),
      email: email ?? '<EMAIL>',
    );
  }

  /// Wartet auf Provider State Changes
  static Future<T> waitForProviderState<T>(
    WidgetTester tester,
    ProviderListenable<T> provider,
    bool Function(T) condition, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    final completer = Completer<T>();
    late final ProviderSubscription subscription;
    
    subscription = ProviderScope.containerOf(
      tester.element(find.byType(ProviderScope)),
    ).listen(
      provider,
      (previous, next) {
        if (condition(next)) {
          subscription.close();
          completer.complete(next);
        }
      },
    );

    return completer.future.timeout(timeout, onTimeout: () {
      subscription.close();
      throw TimeoutException('Provider state condition not met', timeout);
    });
  }

  /// Testet Error Handling in Widgets
  static Future<void> testErrorHandling(
    WidgetTester tester,
    Widget widget,
    VoidCallback triggerError, {
    String? expectedErrorText,
  }) async {
    await tester.pumpWidget(widget);
    
    // Trigger den Fehler
    triggerError();
    await pumpAndSettle(tester);
    
    // Prüfe ob Error-UI angezeigt wird
    if (expectedErrorText != null) {
      expect(find.text(expectedErrorText), findsOneWidget);
    } else {
      // Suche nach generischen Error-Indikatoren
      expect(
        find.byType(SnackBar).or(find.textContaining('Fehler')),
        findsAtLeastNWidgets(1),
      );
    }
  }

  /// Testet Loading States in Widgets
  static Future<void> testLoadingState(
    WidgetTester tester,
    Widget widget,
    Future<void> Function() asyncOperation,
  ) async {
    await tester.pumpWidget(widget);
    
    // Starte async Operation
    final future = asyncOperation();
    await tester.pump();
    
    // Prüfe Loading-Indikator
    expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    
    // Warte auf Completion
    await future;
    await pumpAndSettle(tester);
    
    // Loading-Indikator sollte verschwunden sein
    expect(find.byType(CircularProgressIndicator), findsNothing);
  }

  /// Simuliert User Input mit Validation
  static Future<void> enterTextWithValidation(
    WidgetTester tester,
    Finder textFieldFinder,
    String text, {
    bool expectValidationError = false,
    String? expectedErrorText,
  }) async {
    await tester.enterText(textFieldFinder, text);
    await tester.pump();
    
    if (expectValidationError) {
      if (expectedErrorText != null) {
        expect(find.text(expectedErrorText), findsOneWidget);
      } else {
        expect(find.textContaining('Fehler'), findsAtLeastNWidgets(1));
      }
    }
  }

  /// Testet Navigation zwischen Screens
  static Future<void> testNavigation(
    WidgetTester tester,
    Widget startWidget,
    VoidCallback triggerNavigation,
    Type expectedScreenType,
  ) async {
    await tester.pumpWidget(startWidget);
    
    triggerNavigation();
    await pumpAndSettle(tester);
    
    expect(find.byType(expectedScreenType), findsOneWidget);
  }

  /// Erstellt Mock HTTP Response für Tests
  static String createMockHttpResponse(Map<String, dynamic> data) {
    return '''
    {
      "status": "success",
      "data": ${data.toString().replaceAll("'", '"')}
    }
    ''';
  }

  /// Loggt Test-Informationen
  static void logTestInfo(String testName, String info) {
    _log.d('TEST [$testName]: $info');
  }

  /// Erstellt Test-Daten für verschiedene Szenarien
  static Map<String, dynamic> createTestUserProfile({
    String? name,
    String? email,
    bool isComplete = true,
  }) {
    return {
      'id': 'test-user-id',
      'name': name ?? 'Test User',
      'email': email ?? '<EMAIL>',
      'onboarding_complete': isComplete,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Erstellt Test-Job-Daten
  static Map<String, dynamic> createTestJobData({
    String? id,
    String? title,
    String? company,
    String? location,
  }) {
    return {
      'refnr': id ?? 'test-job-id',
      'beruf': title ?? 'Test Job',
      'arbeitgeber': company ?? 'Test Company',
      'arbeitsort': {'ort': location ?? 'Test Location'},
      'modifikationsTimestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Cleanup nach Tests
  static Future<void> cleanup() async {
    // Reset SharedPreferences
    SharedPreferences.setMockInitialValues({});
    
    // Clear Logs
    _log.i('Test cleanup completed');
  }
}

/// Custom Matcher für bessere Test-Assertions
class CustomMatchers {
  /// Matcher für AsyncValue.loading
  static Matcher isAsyncLoading() => const TypeMatcher<AsyncLoading>();
  
  /// Matcher für AsyncValue.error
  static Matcher isAsyncError() => const TypeMatcher<AsyncError>();
  
  /// Matcher für AsyncValue.data
  static Matcher isAsyncData() => const TypeMatcher<AsyncData>();
  
  /// Matcher für spezifische Error-Types
  static Matcher isErrorOfType<T>() => isA<T>();
  
  /// Matcher für Email-Validation
  static Matcher isValidEmail() => matches(
    r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
  );
}

/// Test-Konfiguration für verschiedene Umgebungen
class TestConfig {
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration shortTimeout = Duration(seconds: 5);
  static const Duration longTimeout = Duration(minutes: 2);
  
  static const String testApiBaseUrl = 'https://test-api.example.com';
  static const String testSupabaseUrl = 'https://test.supabase.co';
  static const String testSupabaseAnonKey = 'test-anon-key';
  
  /// Konfiguration für verschiedene Test-Szenarien
  static Map<String, dynamic> getTestConfig(String scenario) {
    switch (scenario) {
      case 'offline':
        return {'hasNetwork': false, 'useCache': true};
      case 'slow_network':
        return {'networkDelay': 5000, 'hasNetwork': true};
      case 'api_error':
        return {'simulateApiError': true, 'errorCode': 500};
      default:
        return {'hasNetwork': true, 'useCache': false};
    }
  }
}

/// Provider-Overrides für Tests
final sharedPreferencesProvider = FutureProvider<SharedPreferences>((ref) async {
  return SharedPreferences.getInstance();
});

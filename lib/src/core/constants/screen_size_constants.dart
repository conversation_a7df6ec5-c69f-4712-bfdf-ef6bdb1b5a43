/// Konstanten für Bildschirmgrößen und Breakpoints
class ScreenSizeConstants {
  /// Singleton-Instanz
  static final ScreenSizeConstants _instance = ScreenSizeConstants._internal();
  factory ScreenSizeConstants() => _instance;
  ScreenSizeConstants._internal();

  /// Bildschirmbreiten-Breakpoints
  static const double mobileSmallBreakpoint = 320;
  static const double mobileMediumBreakpoint = 375;
  static const double mobileLargeBreakpoint = 425;
  static const double tabletBreakpoint = 768;
  static const double laptopBreakpoint = 1024;
  static const double laptopLargeBreakpoint = 1440;
  static const double desktopBreakpoint = 2560;

  /// Minimale Containerbreite gemäß Style Guide
  static const double minContainerWidth = 320;
  
  /// Maximale Containerbreite für verschiedene Gerätetypen
  static const double maxMobileContainerWidth = 425;
  static const double maxTabletContainerWidth = 768;
  static const double maxDesktopContainerWidth = 1200;
  
  /// Standardabstände für verschiedene Gerätetypen
  static const double mobileHorizontalPadding = 16.0;
  static const double tabletHorizontalPadding = 24.0;
  static const double desktopHorizontalPadding = 32.0;
  
  static const double mobileVerticalPadding = 16.0;
  static const double tabletVerticalPadding = 24.0;
  static const double desktopVerticalPadding = 32.0;
  
  /// Standardabstände zwischen Elementen
  static const double mobileItemSpacing = 8.0;
  static const double tabletItemSpacing = 12.0;
  static const double desktopItemSpacing = 16.0;
  
  /// Standardgrößen für Text
  static const double mobileHeadlineSize = 20.0;
  static const double tabletHeadlineSize = 24.0;
  static const double desktopHeadlineSize = 28.0;
  
  static const double mobileBodySize = 14.0;
  static const double tabletBodySize = 16.0;
  static const double desktopBodySize = 18.0;
  
  /// Standardgrößen für Icons
  static const double mobileIconSize = 20.0;
  static const double tabletIconSize = 24.0;
  static const double desktopIconSize = 28.0;
  
  /// Standardgrößen für Buttons
  static const double mobileButtonHeight = 40.0;
  static const double tabletButtonHeight = 48.0;
  static const double desktopButtonHeight = 56.0;
  
  static const double mobileButtonWidth = 120.0;
  static const double tabletButtonWidth = 160.0;
  static const double desktopButtonWidth = 200.0;
  
  /// Standardgrößen für Input-Felder
  static const double mobileInputHeight = 40.0;
  static const double tabletInputHeight = 48.0;
  static const double desktopInputHeight = 56.0;
  
  /// Standardgrößen für Karten
  static const double mobileCardPadding = 12.0;
  static const double tabletCardPadding = 16.0;
  static const double desktopCardPadding = 20.0;
  
  /// Standardgrößen für Border-Radius
  static const double mobileCardRadius = 8.0;
  static const double tabletCardRadius = 12.0;
  static const double desktopCardRadius = 16.0;
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Prevent instantiation
  AppTheme._();

  // Primäre Farbpalette
  static const Color primaryColor = Color(0xFF1A237E); // Indigo 900
  static const Color primaryLightColor = Color(0xFF5C6BC0); // Indigo 300
  static const Color primaryDarkColor = Color(0xFF000051); // Darker Indigo

  // Sekundäre Farbpalette
  static const Color secondaryColor = Color(0xFFFF6F00); // Amber 800
  static const Color secondaryLightColor = Color(0xFFFFC107); // Amber 500
  static const Color secondaryDarkColor = Color(0xFFC43E00); // Darker Amber

  // Funktionale Farben
  static const Color successColor = Color(0xFF388E3C); // Green 700
  static const Color errorColor = Color(0xFFD32F2F); // Red 700
  static const Color warningColor = Color(0xFFFFA000); // Amber 700
  static const Color infoColor = Color(0xFF1976D2); // Blue 700

  // Neutrale Farben - Basis für Light/Dark Mode
  static const Color backgroundLightColor = Color(0xFFFFFFFF); // White
  static const Color backgroundDarkColor = Color(0xFF121212); // Standard Dark
  static const Color surfaceLightColor = Color(0xFFF5F5F5); // Slightly off-white
  static const Color surfaceDarkColor = Color(0xFF1E1E1E); // Slightly lighter dark
  static const Color appBarDarkColor = Color(0xFF1E1E1E); // Consistent with surface

  // Text-Farben
  static const Color textPrimaryLightColor = Color(0xFF1F1F1F); // Very dark grey
  static const Color textSecondaryLightColor = Color(0xFF5F5F5F); // Medium dark grey
  static const Color textPrimaryDarkColor = Color(0xFFE0E0E0); // Light Grey
  static const Color textSecondaryDarkColor = Color(0xFFA0A0A0); // Medium Light Grey

  // Border-Farben
  static const Color borderLightColor = Color(0xFFE0E0E0);
  static const Color borderDarkColor = Color(0xFF3A3A3A);

  // Neue Farbpaletten mit Schattierungen
  static const Map<int, Color> primaryColors = {
    50: Color(0xFFE3F2FD), // Blue 50
    70: Color(0xFF64B5F6), // Blue 300 (for input borders, lighter)
    100: Color(0xFFBBDEFB), // Blue 100
    200: Color(0xFF90CAF9), // Blue 200
    300: Color(0xFF64B5F6), // Blue 300
    400: Color(0xFF42A5F5), // Blue 400 (primaryLightColor)
    500: Color(0xFF2196F3), // Blue 500
    600: Color(0xFF1E88E5), // Blue 600
    700: Color(0xFF1976D2), // Blue 700 (infoColor)
    800: Color(0xFF1565C0), // Blue 800
    900: Color(0xFF0D47A1), // Blue 900 (primaryColor)
  };

  static const Map<int, Color> secondaryColors = {
    50: Color(0xFFFFF8E1), // Amber 50
    100: Color(0xFFFFECB3), // Amber 100
    200: Color(0xFFFFE082), // Amber 200
    300: Color(0xFFFFD54F), // Amber 300
    400: Color(0xFFFFCA28), // Amber 400 (secondaryLightColor)
    500: Color(0xFFFFC107), // Amber 500
    600: Color(0xFFFFB300), // Amber 600 (warningColor)
    700: Color(0xFFFFA000), // Amber 700
    800: Color(0xFFFF8F00), // Amber 800 (secondaryColor)
    900: Color(0xFFFF6F00), // Amber 900
  };

  static const Map<int, Color> neutralColors = {
    50: Color(0xFFFAFAFA),
    100: Color(0xFFF5F5F5),
    200: Color(0xFFEEEEEE),
    300: Color(0xFFE0E0E0),
    400: Color(0xFFBDBDBD),
    500: Color(0xFF9E9E9E),
    600: Color(0xFF757575),
    700: Color(0xFF616161),
    800: Color(0xFF424242),
    900: Color(0xFF212121),
  };

  // Funktionale Farben
  static const Map<int, Color> successColors = {
    50: Color(0xFFE8F5E8), // Green 50
    100: Color(0xFFC8E6C9), // Green 100
    200: Color(0xFFA5D6A7), // Green 200
    300: Color(0xFF81C784), // Green 300
    400: Color(0xFF66BB6A), // Green 400
    500: Color(0xFF4CAF50), // Green 500
    600: Color(0xFF43A047), // Green 600
    700: Color(0xFF388E3C), // Green 700 (successColor)
    800: Color(0xFF2E7D32), // Green 800
    900: Color(0xFF1B5E20), // Green 900
  };

  static const Map<int, Color> errorColors = {
    50: Color(0xFFFFEBEE), // Red 50
    100: Color(0xFFFFCDD2), // Red 100
    200: Color(0xFFEF9A9A), // Red 200
    300: Color(0xFFE57373), // Red 300
    400: Color(0xFFEF5350), // Red 400
    500: Color(0xFFF44336), // Red 500
    600: Color(0xFFE53935), // Red 600
    700: Color(0xFFD32F2F), // Red 700 (errorColor)
    800: Color(0xFFC62828), // Red 800
    900: Color(0xFFB71C1C), // Red 900
  };

  static const Map<int, Color> warningColors = {
    50: Color(0xFFFFF8E1), // Amber 50
    100: Color(0xFFFFECB3), // Amber 100
    200: Color(0xFFFFE082), // Amber 200
    300: Color(0xFFFFD54F), // Amber 300
    400: Color(0xFFFFCA28), // Amber 400
    500: Color(0xFFFFC107), // Amber 500
    600: Color(0xFFFFB300), // Amber 600
    700: Color(0xFFFFA000), // Amber 700 (warningColor)
    800: Color(0xFFFF8F00), // Amber 800
    900: Color(0xFFFF6F00), // Amber 900
  };

  static const Map<int, Color> infoColors = {
    50: Color(0xFFE3F2FD), // Blue 50
    100: Color(0xFFBBDEFB), // Blue 100
    200: Color(0xFF90CAF9), // Blue 200
    300: Color(0xFF64B5F6), // Blue 300
    400: Color(0xFF42A5F5), // Blue 400
    500: Color(0xFF2196F3), // Blue 500
    600: Color(0xFF1E88E5), // Blue 600
    700: Color(0xFF1976D2), // Blue 700 (infoColor)
    800: Color(0xFF1565C0), // Blue 800
    900: Color(0xFF0D47A1), // Blue 900
  };

  static const _FunctionalColors functionalColors = _FunctionalColors(
    success: Color(0xFF388E3C), // Green 700
    danger: Color(0xFFD32F2F), // Red 700
    warning: Color(0xFFFFA000), // Amber 700
    info: Color(0xFF1976D2), // Blue 700
  );

  // Border-Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 20.0;
  static const double borderRadiusCircular = 100.0;

  // Abstände
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 40.0;
  static const double spacingXXXLarge = 48.0;
  static const double spacingExtraLarge = 32.0;

  // Schatten
  static final List<BoxShadow> lightShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.04),
      blurRadius: 6.0,
      offset: const Offset(0, 2),
    ),
  ];

  static final List<BoxShadow> mediumShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 8.0,
      offset: const Offset(0, 4),
    ),
  ];

  static final List<BoxShadow> strongShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.12),
      blurRadius: 12.0,
      offset: const Offset(0, 6),
    ),
  ];

  // Highlight Color (NEU für Animationen)
  static const Color highlightColor = Color(0xFF66BB6A); // Helles Grün

  // NEU: Premium Farbe
  static const Color premiumColor = Color(0xFFFFD700); // Gold als Beispiel

  // Card elevation
  static const double cardElevation = 1.0;

  // InputDecoration Factory Methode
  static InputDecoration inputDecoration(String labelText, {Widget? icon}) {
    return InputDecoration(
      labelText: labelText,
      prefixIcon: icon,
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      filled: false,
      contentPadding: EdgeInsets.zero,
    );
  }

  // ThemeData für Light Mode
  static ThemeData lightTheme() {
    final base = ThemeData.light(useMaterial3: true);
    final textTheme = GoogleFonts.poppinsTextTheme(base.textTheme);

    return base.copyWith(
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: backgroundLightColor,
        surfaceContainerHighest: surfaceLightColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimaryLightColor,
        onError: Colors.white,
        brightness: Brightness.light,
      ),
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceLightColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide(color: borderLightColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide(color: borderLightColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor, width: 2.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 1.0),
        ),
        floatingLabelStyle: const TextStyle(color: primaryColor),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingMedium,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: spacingMedium,
            horizontal: spacingLarge,
          ),
          elevation: 2,
          minimumSize: const Size(88, 48),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: spacingMedium,
            horizontal: spacingLarge,
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(
            vertical: spacingSmall,
            horizontal: spacingMedium,
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: surfaceLightColor,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        margin: const EdgeInsets.all(spacingSmall),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: backgroundLightColor,
        disabledColor: borderLightColor,
        selectedColor: primaryLightColor,
        secondarySelectedColor: secondaryLightColor,
        padding: const EdgeInsets.all(spacingSmall),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusCircular),
        ),
        labelStyle: textTheme.bodyMedium,
        secondaryLabelStyle: textTheme.bodyMedium?.copyWith(
          color: Colors.white,
        ),
        brightness: Brightness.light,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceLightColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryLightColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      dividerTheme: const DividerThemeData(
        color: borderLightColor,
        thickness: 1,
        space: spacingMedium,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) return primaryColor;
          return Colors.transparent;
        }),
        side: const BorderSide(color: borderLightColor, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusSmall),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) return primaryColor;
          return borderLightColor;
        }),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) return primaryColor;
          return Colors.white;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.5);
          }
          return borderLightColor;
        }),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: primaryColor,
        circularTrackColor: borderLightColor,
        linearTrackColor: borderLightColor,
        refreshBackgroundColor: surfaceLightColor,
      ),
    );
  }

  // ThemeData für Dark Mode
  static ThemeData darkTheme() {
    final base = ThemeData.dark(useMaterial3: true);
    final textTheme = GoogleFonts.poppinsTextTheme(base.textTheme).apply(
      bodyColor: textPrimaryDarkColor,
      displayColor: textPrimaryDarkColor,
    );

    return base.copyWith(
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        primary: primaryLightColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: backgroundDarkColor,
        surfaceContainerHighest: surfaceDarkColor,
        onPrimary: textPrimaryDarkColor,
        onSecondary: textPrimaryDarkColor,
        onSurface: textPrimaryDarkColor,
        onError: textPrimaryDarkColor,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: backgroundDarkColor,
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: appBarDarkColor,
        foregroundColor: textPrimaryDarkColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryDarkColor,
        ),
      ),
      cardTheme: CardTheme(
        elevation: cardElevation,
        color: const Color(0xFF1E1E1E),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
          side: const BorderSide(color: Colors.transparent, width: 0),
        ),
        margin: const EdgeInsets.only(bottom: spacingMedium),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFF2B2B2F),
        hintStyle: TextStyle(color: textSecondaryDarkColor.withValues(alpha: 0.6)),
        labelStyle: const TextStyle(color: textSecondaryDarkColor),
        prefixIconColor: textSecondaryDarkColor.withValues(alpha: 0.8),
        suffixIconColor: textSecondaryDarkColor.withValues(alpha: 0.8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide(
            color: borderDarkColor.withValues(alpha: 0.5),
            width: 0.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide(
            color: borderDarkColor.withValues(alpha: 0.5),
            width: 0.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryLightColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 1.0),
        ),
        floatingLabelStyle: const TextStyle(color: primaryLightColor),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingMedium,
        ),
        isDense: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryLightColor,
          foregroundColor: textPrimaryDarkColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: spacingMedium,
            horizontal: spacingLarge,
          ),
          elevation: 2,
          minimumSize: const Size(88, 48),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryLightColor,
          side: const BorderSide(color: primaryLightColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: spacingMedium,
            horizontal: spacingLarge,
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryLightColor,
          padding: const EdgeInsets.symmetric(
            vertical: spacingSmall,
            horizontal: spacingMedium,
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: primaryLightColor.withValues(alpha: 0.1),
        disabledColor: Colors.grey.withValues(alpha: 0.5),
        selectedColor: primaryLightColor,
        secondarySelectedColor: primaryLightColor.withValues(alpha: 0.8),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        labelStyle: TextStyle(
          color: textPrimaryDarkColor.withValues(alpha: 0.8),
          fontSize: 12,
        ),
        secondaryLabelStyle: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        brightness: Brightness.dark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          side: BorderSide(color: primaryLightColor.withValues(alpha: 0.3)),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: borderDarkColor.withValues(alpha: 0.3),
        thickness: 0.5,
        space: spacingMedium * 1.5,
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: surfaceDarkColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(borderRadiusSmall),
        ),
        textStyle: textTheme.bodySmall?.copyWith(color: textPrimaryDarkColor),
      ),
      dialogTheme: DialogTheme(
        backgroundColor: surfaceDarkColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
        titleTextStyle: textTheme.titleMedium?.copyWith(
          color: textPrimaryDarkColor,
        ),
        contentTextStyle: textTheme.bodyMedium?.copyWith(
          color: textPrimaryDarkColor,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: surfaceDarkColor,
        contentTextStyle: textTheme.bodyMedium?.copyWith(
          color: textPrimaryDarkColor,
        ),
        actionTextColor: primaryLightColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: surfaceDarkColor,
        selectedItemColor: primaryLightColor,
        unselectedItemColor: textSecondaryDarkColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) return primaryLightColor;
          return Colors.transparent;
        }),
        side: BorderSide(color: borderDarkColor, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusSmall),
        ),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryLightColor,
        circularTrackColor: borderDarkColor,
        linearTrackColor: borderDarkColor,
        refreshBackgroundColor: surfaceDarkColor,
      ),
    );
  }

  // Methode für benutzerdefiniertes InputDecorationTheme
  static InputDecorationTheme inputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        borderSide: BorderSide(color: colorScheme.error, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        borderSide: BorderSide(color: colorScheme.error, width: 2.0),
      ),
      contentPadding: const EdgeInsets.symmetric(
        vertical: 14.0,
        horizontal: 16.0,
      ),
      hintStyle: TextStyle(
        color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
      ),
      prefixIconColor: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
      suffixIconColor: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
    );
  }

  // Statische Hilfsmethode für Standard-InputDecoration
}

class _FunctionalColors {
  final Color success;
  final Color danger;
  final Color warning;
  final Color info;

  const _FunctionalColors({
    required this.success,
    required this.danger,
    required this.warning,
    required this.info,
  });
}

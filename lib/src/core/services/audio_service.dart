import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:async';

/// Ein Service für die Audiowiedergabe in der App
class AudioService {
  final AudioPlayer _audioPlayer;
  bool _isInitialized = false;
  int _currentPage = 0;
  StreamSubscription<Duration>? _positionSubscription;
  bool _isTransitioning = false;

  // Pfade zu den Audiodateien für jede Seite
  final List<String> _pageSoundPaths = [
    'assets/audio/Onboarding-Screen/p1.mp3',
    'assets/audio/Onboarding-Screen/p2.mp3',
    'assets/audio/Onboarding-Screen/p3.mp3',
    'assets/audio/Onboarding-Screen/p4.mp3',
    '', // Leerer String für Seite 5 (keine Musik)
    'assets/audio/Onboarding-Screen/p5.mp3', // Spezielle Musik für die letzte Seite (Seite 6)
  ];

  AudioService() : _audioPlayer = AudioPlayer();

  /// Initialisiert den AudioPlayer für die erste Seite
  Future<void> initialize(String assetPath) async {
    // Ignoriere den übergebenen Pfad und verwende stattdessen die seitenspezifischen Audiodateien
    await _initializeForPage(0);
  }

  /// Initialisiert den AudioPlayer für eine bestimmte Seite
  Future<void> _initializeForPage(int pageIndex) async {
    if (_isTransitioning) return;
    _isTransitioning = true;

    if (_isInitialized) {
      await _audioPlayer.stop();
      _positionSubscription?.cancel();
      _positionSubscription = null;
    }

    if (pageIndex < 0 || pageIndex >= _pageSoundPaths.length) {
      debugPrint('Ungültiger Seitenindex: $pageIndex');
      _isTransitioning = false;
      return;
    }

    _currentPage = pageIndex;
    final assetPath = _pageSoundPaths[pageIndex];

    try {
      // Versuche die Audiodatei für die aktuelle Seite zu laden
      await _audioPlayer.setAsset(assetPath);

      // Keine Schleife für alle Seiten
      await _audioPlayer.setLoopMode(LoopMode.off);

      // Für Seite mit leerem Pfad keine Musik abspielen
      if (assetPath.isEmpty) {
        _isInitialized = false;
        _isTransitioning = false;
        return;
      }

      await _audioPlayer.setVolume(0.5); // Reduzierte Lautstärke
      _isInitialized = true;

      // Für die letzte Seite (p5) einen Timer einrichten, der die Mindestanzeigedauer sicherstellt
      if (pageIndex == _pageSoundPaths.length - 1) {
        _positionSubscription = _audioPlayer.positionStream.listen((position) {
          // Nichts tun, nur den Stream aktiv halten
        });
      }

      debugPrint(
        'AudioPlayer erfolgreich initialisiert für Seite $pageIndex mit: $assetPath',
      );
    } catch (e) {
      debugPrint(
        'Fehler beim Initialisieren des AudioPlayers für Seite $pageIndex: $e',
      );
      _isInitialized = false;
    }

    _isTransitioning = false;
  }

  /// Startet die Audiowiedergabe
  Future<void> play() async {
    if (!_isInitialized) {
      debugPrint('AudioPlayer ist nicht initialisiert');
      return;
    }

    try {
      await _audioPlayer.play();
    } catch (e) {
      debugPrint('Fehler beim Abspielen der Audio: $e');
    }
  }

  /// Pausiert die Audiowiedergabe
  Future<void> pause() async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer.pause();
    } catch (e) {
      debugPrint('Fehler beim Pausieren der Audio: $e');
    }
  }

  /// Stoppt die Audiowiedergabe
  Future<void> stop() async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer.stop();
    } catch (e) {
      debugPrint('Fehler beim Stoppen der Audio: $e');
    }
  }

  /// Setzt die Lautstärke (0.0 bis 1.0)
  Future<void> setVolume(double volume) async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer.setVolume(volume);
    } catch (e) {
      debugPrint('Fehler beim Setzen der Lautstärke: $e');
    }
  }

  /// Wechselt zur Audiodatei für die angegebene Seite
  Future<void> switchToPage(int pageIndex) async {
    if (_currentPage == pageIndex) return;

    await _initializeForPage(pageIndex);
    await play();
  }

  /// Prüft, ob die aktuelle Seite die letzte Seite ist
  bool isLastPage() {
    return _currentPage == _pageSoundPaths.length - 1;
  }

  /// Gibt die Gesamtdauer der aktuellen Audiodatei zurück
  Future<Duration?> getDuration() async {
    if (!_isInitialized) return null;
    return _audioPlayer.duration;
  }

  /// Gibt die aktuelle Position der Audiowiedergabe zurück
  Future<Duration?> getPosition() async {
    if (!_isInitialized) return null;
    return _audioPlayer.position;
  }

  /// Gibt zurück, ob die Audiowiedergabe abgeschlossen ist
  Future<bool> isComplete() async {
    if (!_isInitialized) return false;

    final position = await getPosition();
    final duration = await getDuration();

    if (position == null || duration == null) return false;

    // Betrachte die Wiedergabe als abgeschlossen, wenn weniger als 500ms verbleiben
    return (duration - position).inMilliseconds < 500;
  }

  /// Gibt Ressourcen frei
  Future<void> dispose() async {
    try {
      _positionSubscription?.cancel();
      _positionSubscription = null;
      await _audioPlayer.dispose();
      _isInitialized = false;
    } catch (e) {
      debugPrint('Fehler beim Freigeben des AudioPlayers: $e');
    }
  }
}

/// Provider für den AudioService
final audioServiceProvider = Provider<AudioService>((ref) {
  final audioService = AudioService();

  ref.onDispose(() {
    audioService.dispose();
  });

  return audioService;
});

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Ein Service für die sichere Speicherung sensibler Daten
/// 
/// Verwendet flutter_secure_storage, um Daten verschlüsselt zu speichern:
/// - Auf Android: AES-Verschlüsselung im Android Keystore
/// - Auf iOS: Keychain mit Zugriffskontrollen
class SecureStorageService {
  final FlutterSecureStorage _secureStorage;
  final _log = getLogger('SecureStorageService');

  // Schlüssel für gespeicherte Werte
  static const String _apiKeyKey = 'api_key';
  static const String _userTokenKey = 'user_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _premiumStatusKey = 'premium_status';

  // Singleton-Instanz
  static final SecureStorageService _instance = SecureStorageService._internal();

  // Factory-Konstruktor
  factory SecureStorageService() {
    return _instance;
  }

  // Privater Konstruktor für Singleton
  SecureStorageService._internal()
      : _secureStorage = const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true, // Verwendet EncryptedSharedPreferences
            resetOnError: true, // Setzt den Speicher zurück, wenn ein Fehler auftritt
            keyCipherAlgorithm: 
                KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
            storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
          ),
          iOptions: IOSOptions(
            accessibility: KeychainAccessibility.first_unlock, // Verfügbar nach dem ersten Entsperren
            synchronizable: false, // Nicht über iCloud synchronisieren
          ),
        );

  /// Speichert einen API-Schlüssel sicher
  Future<void> saveApiKey(String apiKey) async {
    try {
      await _secureStorage.write(key: _apiKeyKey, value: apiKey);
      _log.i('API-Schlüssel sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des API-Schlüssels', error: e);
      rethrow;
    }
  }

  /// Liest den gespeicherten API-Schlüssel
  Future<String?> getApiKey() async {
    try {
      return await _secureStorage.read(key: _apiKeyKey);
    } catch (e) {
      _log.e('Fehler beim Lesen des API-Schlüssels', error: e);
      return null;
    }
  }

  /// Speichert einen Benutzer-Token sicher
  Future<void> saveUserToken(String token) async {
    try {
      await _secureStorage.write(key: _userTokenKey, value: token);
      _log.i('Benutzer-Token sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des Benutzer-Tokens', error: e);
      rethrow;
    }
  }

  /// Liest den gespeicherten Benutzer-Token
  Future<String?> getUserToken() async {
    try {
      return await _secureStorage.read(key: _userTokenKey);
    } catch (e) {
      _log.e('Fehler beim Lesen des Benutzer-Tokens', error: e);
      return null;
    }
  }

  /// Speichert einen Refresh-Token sicher
  Future<void> saveRefreshToken(String token) async {
    try {
      await _secureStorage.write(key: _refreshTokenKey, value: token);
      _log.i('Refresh-Token sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des Refresh-Tokens', error: e);
      rethrow;
    }
  }

  /// Liest den gespeicherten Refresh-Token
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      _log.e('Fehler beim Lesen des Refresh-Tokens', error: e);
      return null;
    }
  }

  /// Speichert eine Benutzer-ID sicher
  Future<void> saveUserId(String userId) async {
    try {
      await _secureStorage.write(key: _userIdKey, value: userId);
      _log.i('Benutzer-ID sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern der Benutzer-ID', error: e);
      rethrow;
    }
  }

  /// Liest die gespeicherte Benutzer-ID
  Future<String?> getUserId() async {
    try {
      return await _secureStorage.read(key: _userIdKey);
    } catch (e) {
      _log.e('Fehler beim Lesen der Benutzer-ID', error: e);
      return null;
    }
  }

  /// Speichert den Premium-Status sicher
  Future<void> savePremiumStatus(bool isPremium) async {
    try {
      await _secureStorage.write(
        key: _premiumStatusKey, 
        value: isPremium.toString(),
      );
      _log.i('Premium-Status sicher gespeichert: $isPremium');
    } catch (e) {
      _log.e('Fehler beim Speichern des Premium-Status', error: e);
      rethrow;
    }
  }

  /// Liest den gespeicherten Premium-Status
  Future<bool?> getPremiumStatus() async {
    try {
      final value = await _secureStorage.read(key: _premiumStatusKey);
      return value != null ? value.toLowerCase() == 'true' : null;
    } catch (e) {
      _log.e('Fehler beim Lesen des Premium-Status', error: e);
      return null;
    }
  }

  /// Speichert einen beliebigen Wert sicher
  Future<void> saveSecureValue(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      _log.i('Wert für Schlüssel "$key" sicher gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des Werts für Schlüssel "$key"', error: e);
      rethrow;
    }
  }

  /// Liest einen beliebigen gespeicherten Wert
  Future<String?> getSecureValue(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      _log.e('Fehler beim Lesen des Werts für Schlüssel "$key"', error: e);
      return null;
    }
  }

  /// Löscht einen gespeicherten Wert
  Future<void> deleteSecureValue(String key) async {
    try {
      await _secureStorage.delete(key: key);
      _log.i('Wert für Schlüssel "$key" gelöscht');
    } catch (e) {
      _log.e('Fehler beim Löschen des Werts für Schlüssel "$key"', error: e);
      rethrow;
    }
  }

  /// Löscht alle gespeicherten Werte
  Future<void> deleteAllSecureValues() async {
    try {
      await _secureStorage.deleteAll();
      _log.i('Alle sicheren Werte gelöscht');
    } catch (e) {
      _log.e('Fehler beim Löschen aller sicheren Werte', error: e);
      rethrow;
    }
  }
}

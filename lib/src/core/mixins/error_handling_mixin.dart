import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../utils/error_handler.dart';

/// Mixin für einheitliche Error Handling Patterns
mixin ErrorHandlingMixin {
  /// Komponenten-Name für Logging (muss von der implementierenden Klasse überschrieben werden)
  String get componentName;

  /// Sicherer SharedPreferences Zugriff mit Error Handling
  Future<T?> safeSharedPreferences<T>(
    Future<T> Function(SharedPreferences prefs) operation, {
    BuildContext? context,
    String? errorMessage,
    T? fallbackValue,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await operation(prefs);
    } catch (e, stackTrace) {
      ErrorHandler.handleSharedPreferencesError(
        componentName,
        e,
        stackTrace,
        context: context?.mounted == true ? context : null,
        userMessage: errorMessage,
      );
      return fallbackValue;
    }
  }

  /// Sicheres JSON Parsing mit Error Handling
  Map<String, dynamic>? safeJsonDecode(
    String jsonString, {
    BuildContext? context,
    String? errorMessage,
  }) {
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e, stackTrace) {
      ErrorHandler.handleJsonError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return null;
    }
  }

  /// Sicheres JSON Encoding mit Error Handling
  String? safeJsonEncode(
    Object? object, {
    BuildContext? context,
    String? errorMessage,
  }) {
    try {
      return jsonEncode(object);
    } catch (e, stackTrace) {
      ErrorHandler.handleJsonError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return null;
    }
  }

  /// Sicheres JSON List Parsing mit Error Handling
  List<T>? safeJsonDecodeList<T>(
    String jsonString,
    T Function(Map<String, dynamic>) fromJson, {
    BuildContext? context,
    String? errorMessage,
  }) {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonString) as List<dynamic>;
      return jsonList
          .map((item) => fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e, stackTrace) {
      ErrorHandler.handleJsonError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return null;
    }
  }

  /// Sicheres Object-zu-JSON Parsing mit Error Handling
  Map<String, dynamic>? safeToJson<T>(
    T object,
    Map<String, dynamic> Function(T) toJson, {
    BuildContext? context,
    String? errorMessage,
  }) {
    try {
      return toJson(object);
    } catch (e, stackTrace) {
      ErrorHandler.handleJsonError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return null;
    }
  }

  /// Sichere File Operation mit Error Handling
  Future<T?> safeFileOperation<T>(
    Future<T> Function() operation, {
    BuildContext? context,
    String? errorMessage,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      ErrorHandler.handleFileError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return fallbackValue;
    }
  }

  /// Sichere API Operation mit Error Handling
  Future<T?> safeApiOperation<T>(
    Future<T> Function() operation, {
    BuildContext? context,
    String? errorMessage,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      ErrorHandler.handleApiError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return fallbackValue;
    }
  }

  /// Sichere Netzwerk Operation mit Error Handling
  Future<T?> safeNetworkOperation<T>(
    Future<T> Function() operation, {
    BuildContext? context,
    String? errorMessage,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      ErrorHandler.handleNetworkError(
        componentName,
        e,
        stackTrace,
        context: context,
        userMessage: errorMessage,
      );
      return fallbackValue;
    }
  }

  /// Zeigt eine Fehlermeldung an
  void showError(BuildContext context, String message) {
    ErrorHandler.showErrorSnackBar(context, message);
  }

  /// Zeigt eine Warnung an
  void showWarning(BuildContext context, String message) {
    ErrorHandler.showWarningSnackBar(context, message);
  }

  /// Zeigt eine Erfolgsmeldung an
  void showSuccess(BuildContext context, String message) {
    ErrorHandler.showSuccessSnackBar(context, message);
  }

  /// Loggt einen Fehler
  void logError(String message, dynamic error, [StackTrace? stackTrace]) {
    ErrorHandler.logError(componentName, message, error, stackTrace);
  }

  /// Loggt eine Warnung
  void logWarning(String message, [dynamic error]) {
    ErrorHandler.logWarning(componentName, message, error);
  }

  /// Loggt eine Info
  void logInfo(String message) {
    ErrorHandler.logInfo(componentName, message);
  }

  /// Behandelt einen allgemeinen Fehler mit Fallback
  Future<T?> handleErrorWithFallback<T>(
    Future<T> Function() operation,
    T? fallbackValue, {
    BuildContext? context,
    String? errorMessage,
    bool showUserError = true,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      logError('Operation fehlgeschlagen', e, stackTrace);
      
      if (context?.mounted == true && showUserError) {
        final userMsg = errorMessage ?? ErrorHandler.getReadableErrorMessage(e);
        showError(context!, userMsg);
      }
      
      return fallbackValue;
    }
  }

  /// Führt eine Operation mit Retry-Logik aus
  Future<T?> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    BuildContext? context,
    String? errorMessage,
  }) async {
    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        if (attempt == maxRetries) {
          // Letzter Versuch fehlgeschlagen
          logError('Operation nach $maxRetries Versuchen fehlgeschlagen', e, stackTrace);
          
          if (context?.mounted == true) {
            final userMsg = errorMessage ?? ErrorHandler.getReadableErrorMessage(e);
            showError(context!, userMsg);
          }
          
          return null;
        } else {
          // Warte vor dem nächsten Versuch
          logWarning('Versuch ${attempt + 1} fehlgeschlagen, versuche erneut', e);
          await Future.delayed(delay * (attempt + 1));
        }
      }
    }
    return null;
  }

  /// Validiert eine Eingabe und zeigt Fehler an
  bool validateInput(
    BuildContext context,
    bool Function() validator,
    String errorMessage,
  ) {
    try {
      final isValid = validator();
      if (!isValid && context.mounted) {
        showError(context, errorMessage);
      }
      return isValid;
    } catch (e, stackTrace) {
      logError('Validierungsfehler', e, stackTrace);
      if (context.mounted) {
        showError(context, 'Fehler bei der Eingabevalidierung');
      }
      return false;
    }
  }

  /// Sichere Ausführung mit Loading State
  Future<T?> executeWithLoading<T>(
    Future<T> Function() operation,
    void Function(bool loading) setLoading, {
    BuildContext? context,
    String? errorMessage,
  }) async {
    try {
      setLoading(true);
      return await operation();
    } catch (e, stackTrace) {
      logError('Operation mit Loading fehlgeschlagen', e, stackTrace);
      
      if (context != null) {
        final userMsg = errorMessage ?? ErrorHandler.getReadableErrorMessage(e);
        showError(context, userMsg);
      }
      
      return null;
    } finally {
      setLoading(false);
    }
  }
}

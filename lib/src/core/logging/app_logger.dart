/// Simple logging utility for the app
class AppLogger {
  final String _name;
  
  AppLogger(this._name);
  
  void i(String message) {
    print('[$_name] INFO: $message');
  }
  
  void d(String message) {
    print('[$_name] DEBUG: $message');
  }
  
  void w(String message) {
    print('[$_name] WARNING: $message');
  }
  
  void e(String message, [StackTrace? stackTrace]) {
    print('[$_name] ERROR: $message');
    if (stackTrace != null) {
      print('[$_name] STACK: $stackTrace');
    }
  }
}

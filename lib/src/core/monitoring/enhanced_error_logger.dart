import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logging.dart';

/// Erweiterte Error Logging Klasse mit strukturiertem Logging und Kategorisierung
class EnhancedErrorLogger {
  static final _log = getLogger('EnhancedErrorLogger');
  static final List<ErrorLogEntry> _errorHistory = [];
  static const int _maxHistorySize = 1000;
  static const String _errorHistoryKey = 'error_history';

  /// Kategorien für verschiedene Error-Types
  enum ErrorCategory {
    network,
    api,
    database,
    authentication,
    validation,
    ui,
    system,
    unknown,
  }

  /// Severity Levels für Errors
  enum ErrorSeverity {
    low,
    medium,
    high,
    critical,
  }

  /// Loggt einen strukturierten Error
  static Future<void> logError({
    required dynamic error,
    StackTrace? stackTrace,
    ErrorCategory category = ErrorCategory.unknown,
    ErrorSeverity severity = ErrorSeverity.medium,
    String? component,
    String? operation,
    Map<String, dynamic>? context,
    String? userMessage,
    BuildContext? buildContext,
  }) async {
    final entry = ErrorLogEntry(
      timestamp: DateTime.now(),
      error: error,
      stackTrace: stackTrace,
      category: category,
      severity: severity,
      component: component,
      operation: operation,
      context: context,
      userMessage: userMessage,
      deviceInfo: await _getDeviceInfo(),
      appVersion: await _getAppVersion(),
    );

    // Füge zur History hinzu
    _addToHistory(entry);

    // Logge basierend auf Severity
    switch (severity) {
      case ErrorSeverity.low:
        _log.d(_formatErrorMessage(entry));
        break;
      case ErrorSeverity.medium:
        _log.w(_formatErrorMessage(entry));
        break;
      case ErrorSeverity.high:
        _log.e(_formatErrorMessage(entry), error: error, stackTrace: stackTrace);
        break;
      case ErrorSeverity.critical:
        _log.e(_formatErrorMessage(entry), error: error, stackTrace: stackTrace);
        await _handleCriticalError(entry, buildContext);
        break;
    }

    // Speichere Error History persistent
    await _saveErrorHistory();

    // Sende Error an Remote Logging Service (falls konfiguriert)
    await _sendToRemoteLogging(entry);
  }

  /// Loggt Network-Errors
  static Future<void> logNetworkError({
    required dynamic error,
    StackTrace? stackTrace,
    String? url,
    String? method,
    int? statusCode,
    Map<String, String>? headers,
    String? requestBody,
    String? responseBody,
    BuildContext? context,
  }) async {
    await logError(
      error: error,
      stackTrace: stackTrace,
      category: ErrorCategory.network,
      severity: _determineNetworkErrorSeverity(statusCode),
      component: 'NetworkClient',
      operation: '$method $url',
      context: {
        'url': url,
        'method': method,
        'statusCode': statusCode,
        'headers': headers,
        'requestBody': requestBody,
        'responseBody': responseBody,
      },
      buildContext: context,
    );
  }

  /// Loggt API-Errors
  static Future<void> logApiError({
    required dynamic error,
    StackTrace? stackTrace,
    String? apiName,
    String? endpoint,
    String? operation,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
    BuildContext? context,
  }) async {
    await logError(
      error: error,
      stackTrace: stackTrace,
      category: ErrorCategory.api,
      severity: ErrorSeverity.high,
      component: apiName ?? 'ApiClient',
      operation: operation ?? endpoint,
      context: {
        'endpoint': endpoint,
        'requestData': requestData,
        'responseData': responseData,
      },
      buildContext: context,
    );
  }

  /// Loggt Authentication-Errors
  static Future<void> logAuthError({
    required dynamic error,
    StackTrace? stackTrace,
    String? operation,
    String? userId,
    Map<String, dynamic>? authContext,
    BuildContext? context,
  }) async {
    await logError(
      error: error,
      stackTrace: stackTrace,
      category: ErrorCategory.authentication,
      severity: ErrorSeverity.high,
      component: 'AuthService',
      operation: operation,
      context: {
        'userId': userId,
        'authContext': authContext,
      },
      buildContext: context,
    );
  }

  /// Loggt Validation-Errors
  static Future<void> logValidationError({
    required dynamic error,
    String? fieldName,
    dynamic fieldValue,
    String? validationRule,
    BuildContext? context,
  }) async {
    await logError(
      error: error,
      category: ErrorCategory.validation,
      severity: ErrorSeverity.low,
      component: 'FormValidation',
      operation: 'validateField',
      context: {
        'fieldName': fieldName,
        'fieldValue': fieldValue,
        'validationRule': validationRule,
      },
      buildContext: context,
    );
  }

  /// Loggt UI-Errors
  static Future<void> logUiError({
    required dynamic error,
    StackTrace? stackTrace,
    String? widgetName,
    String? operation,
    Map<String, dynamic>? widgetState,
    BuildContext? context,
  }) async {
    await logError(
      error: error,
      stackTrace: stackTrace,
      category: ErrorCategory.ui,
      severity: ErrorSeverity.medium,
      component: widgetName ?? 'UIComponent',
      operation: operation,
      context: widgetState,
      buildContext: context,
    );
  }

  /// Gibt Error History zurück
  static List<ErrorLogEntry> getErrorHistory({
    ErrorCategory? category,
    ErrorSeverity? severity,
    DateTime? since,
    int? limit,
  }) {
    var filtered = _errorHistory.where((entry) {
      if (category != null && entry.category != category) return false;
      if (severity != null && entry.severity != severity) return false;
      if (since != null && entry.timestamp.isBefore(since)) return false;
      return true;
    }).toList();

    filtered.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && filtered.length > limit) {
      filtered = filtered.take(limit).toList();
    }

    return filtered;
  }

  /// Exportiert Error History als JSON
  static String exportErrorHistory() {
    final data = _errorHistory.map((entry) => entry.toJson()).toList();
    return json.encode(data);
  }

  /// Löscht Error History
  static Future<void> clearErrorHistory() async {
    _errorHistory.clear();
    await _saveErrorHistory();
  }

  /// Lädt Error History beim App-Start
  static Future<void> loadErrorHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_errorHistoryKey);
      
      if (historyJson != null) {
        final List<dynamic> data = json.decode(historyJson);
        _errorHistory.clear();
        _errorHistory.addAll(
          data.map((item) => ErrorLogEntry.fromJson(item)).toList(),
        );
      }
    } catch (e) {
      _log.w('Fehler beim Laden der Error History: $e');
    }
  }

  /// Private Hilfsmethoden
  static void _addToHistory(ErrorLogEntry entry) {
    _errorHistory.add(entry);
    
    // Begrenze History-Größe
    if (_errorHistory.length > _maxHistorySize) {
      _errorHistory.removeRange(0, _errorHistory.length - _maxHistorySize);
    }
  }

  static String _formatErrorMessage(ErrorLogEntry entry) {
    final buffer = StringBuffer();
    buffer.writeln('ERROR [${entry.category.name.toUpperCase()}] ${entry.severity.name.toUpperCase()}');
    buffer.writeln('Component: ${entry.component ?? 'Unknown'}');
    buffer.writeln('Operation: ${entry.operation ?? 'Unknown'}');
    buffer.writeln('Error: ${entry.error}');
    
    if (entry.context != null && entry.context!.isNotEmpty) {
      buffer.writeln('Context: ${json.encode(entry.context)}');
    }
    
    return buffer.toString();
  }

  static ErrorSeverity _determineNetworkErrorSeverity(int? statusCode) {
    if (statusCode == null) return ErrorSeverity.high;
    
    if (statusCode >= 500) return ErrorSeverity.critical;
    if (statusCode >= 400) return ErrorSeverity.high;
    if (statusCode >= 300) return ErrorSeverity.medium;
    return ErrorSeverity.low;
  }

  static Future<void> _handleCriticalError(ErrorLogEntry entry, BuildContext? context) async {
    // Bei kritischen Fehlern zusätzliche Maßnahmen ergreifen
    _log.e('CRITICAL ERROR DETECTED: ${entry.error}');
    
    if (context != null && context.mounted) {
      // Zeige Critical Error Dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Kritischer Fehler'),
          content: Text(entry.userMessage ?? 'Ein kritischer Fehler ist aufgetreten. Die App wird möglicherweise instabil.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'isDebug': kDebugMode,
    };
  }

  static Future<String> _getAppVersion() async {
    // TODO: Implementiere Package Info für App Version
    return '1.0.0';
  }

  static Future<void> _saveErrorHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = json.encode(_errorHistory.map((e) => e.toJson()).toList());
      await prefs.setString(_errorHistoryKey, historyJson);
    } catch (e) {
      _log.w('Fehler beim Speichern der Error History: $e');
    }
  }

  static Future<void> _sendToRemoteLogging(ErrorLogEntry entry) async {
    // TODO: Implementiere Remote Logging Service
    // Beispiel: Sentry, Firebase Crashlytics, etc.
  }
}

/// Error Log Entry Datenklasse
class ErrorLogEntry {
  final DateTime timestamp;
  final dynamic error;
  final StackTrace? stackTrace;
  final EnhancedErrorLogger.ErrorCategory category;
  final EnhancedErrorLogger.ErrorSeverity severity;
  final String? component;
  final String? operation;
  final Map<String, dynamic>? context;
  final String? userMessage;
  final Map<String, dynamic>? deviceInfo;
  final String? appVersion;

  ErrorLogEntry({
    required this.timestamp,
    required this.error,
    this.stackTrace,
    required this.category,
    required this.severity,
    this.component,
    this.operation,
    this.context,
    this.userMessage,
    this.deviceInfo,
    this.appVersion,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'error': error.toString(),
      'stackTrace': stackTrace?.toString(),
      'category': category.name,
      'severity': severity.name,
      'component': component,
      'operation': operation,
      'context': context,
      'userMessage': userMessage,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  factory ErrorLogEntry.fromJson(Map<String, dynamic> json) {
    return ErrorLogEntry(
      timestamp: DateTime.parse(json['timestamp']),
      error: json['error'],
      stackTrace: json['stackTrace'] != null ? StackTrace.fromString(json['stackTrace']) : null,
      category: EnhancedErrorLogger.ErrorCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => EnhancedErrorLogger.ErrorCategory.unknown,
      ),
      severity: EnhancedErrorLogger.ErrorSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => EnhancedErrorLogger.ErrorSeverity.medium,
      ),
      component: json['component'],
      operation: json['operation'],
      context: json['context'],
      userMessage: json['userMessage'],
      deviceInfo: json['deviceInfo'],
      appVersion: json['appVersion'],
    );
  }
}

import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logging.dart';

/// Performance Monitoring System für die App
class PerformanceMonitor {
  static final _log = getLogger('PerformanceMonitor');
  static final Map<String, Stopwatch> _activeTimers = {};
  static final Queue<PerformanceMetric> _metrics = Queue();
  static const int _maxMetricsHistory = 500;
  static const String _metricsKey = 'performance_metrics';

  /// Startet Performance-Messung für eine Operation
  static void startTimer(String operationId, {Map<String, dynamic>? context}) {
    if (_activeTimers.containsKey(operationId)) {
      _log.w('Timer für $operationId läuft bereits');
      return;
    }

    final stopwatch = Stopwatch()..start();
    _activeTimers[operationId] = stopwatch;
    
    _log.d('Performance Timer gestartet: $operationId');
  }

  /// Stoppt Performance-Messung und loggt Ergebnis
  static Duration? stopTimer(
    String operationId, {
    Map<String, dynamic>? context,
    PerformanceCategory category = PerformanceCategory.general,
    bool logResult = true,
  }) {
    final stopwatch = _activeTimers.remove(operationId);
    if (stopwatch == null) {
      _log.w('Kein aktiver Timer für $operationId gefunden');
      return null;
    }

    stopwatch.stop();
    final duration = stopwatch.elapsed;

    final metric = PerformanceMetric(
      operationId: operationId,
      duration: duration,
      timestamp: DateTime.now(),
      category: category,
      context: context,
    );

    _addMetric(metric);

    if (logResult) {
      _logPerformanceResult(metric);
    }

    return duration;
  }

  /// Misst die Performance einer async Operation
  static Future<T> measureAsync<T>(
    String operationId,
    Future<T> Function() operation, {
    Map<String, dynamic>? context,
    PerformanceCategory category = PerformanceCategory.general,
    Duration? warningThreshold,
    Duration? errorThreshold,
  }) async {
    startTimer(operationId, context: context);
    
    try {
      final result = await operation();
      final duration = stopTimer(operationId, context: context, category: category);
      
      _checkThresholds(operationId, duration, warningThreshold, errorThreshold);
      
      return result;
    } catch (e) {
      stopTimer(operationId, context: context, category: category);
      rethrow;
    }
  }

  /// Misst die Performance einer synchronen Operation
  static T measureSync<T>(
    String operationId,
    T Function() operation, {
    Map<String, dynamic>? context,
    PerformanceCategory category = PerformanceCategory.general,
    Duration? warningThreshold,
    Duration? errorThreshold,
  }) {
    startTimer(operationId, context: context);
    
    try {
      final result = operation();
      final duration = stopTimer(operationId, context: context, category: category);
      
      _checkThresholds(operationId, duration, warningThreshold, errorThreshold);
      
      return result;
    } catch (e) {
      stopTimer(operationId, context: context, category: category);
      rethrow;
    }
  }

  /// Misst Widget Build Performance
  static Widget measureWidgetBuild(
    String widgetName,
    Widget Function() builder, {
    Duration? warningThreshold,
  }) {
    return Builder(
      builder: (context) {
        return measureSync(
          'widget_build_$widgetName',
          builder,
          category: PerformanceCategory.ui,
          warningThreshold: warningThreshold ?? const Duration(milliseconds: 16),
        );
      },
    );
  }

  /// Misst API-Call Performance
  static Future<T> measureApiCall<T>(
    String apiName,
    String endpoint,
    Future<T> Function() apiCall, {
    Map<String, dynamic>? requestContext,
  }) async {
    return measureAsync(
      'api_${apiName}_$endpoint',
      apiCall,
      context: {
        'apiName': apiName,
        'endpoint': endpoint,
        ...?requestContext,
      },
      category: PerformanceCategory.network,
      warningThreshold: const Duration(seconds: 5),
      errorThreshold: const Duration(seconds: 30),
    );
  }

  /// Misst Database Operation Performance
  static Future<T> measureDatabaseOperation<T>(
    String operation,
    Future<T> Function() dbOperation, {
    Map<String, dynamic>? context,
  }) async {
    return measureAsync(
      'db_$operation',
      dbOperation,
      context: context,
      category: PerformanceCategory.database,
      warningThreshold: const Duration(seconds: 2),
      errorThreshold: const Duration(seconds: 10),
    );
  }

  /// Gibt Performance-Statistiken zurück
  static PerformanceStats getStats({
    PerformanceCategory? category,
    Duration? since,
  }) {
    final filteredMetrics = _metrics.where((metric) {
      if (category != null && metric.category != category) return false;
      if (since != null && metric.timestamp.isBefore(DateTime.now().subtract(since))) return false;
      return true;
    }).toList();

    if (filteredMetrics.isEmpty) {
      return PerformanceStats.empty();
    }

    final durations = filteredMetrics.map((m) => m.duration).toList();
    durations.sort();

    return PerformanceStats(
      totalOperations: filteredMetrics.length,
      averageDuration: Duration(
        microseconds: durations.map((d) => d.inMicroseconds).reduce((a, b) => a + b) ~/ durations.length,
      ),
      minDuration: durations.first,
      maxDuration: durations.last,
      medianDuration: durations[durations.length ~/ 2],
      p95Duration: durations[(durations.length * 0.95).floor()],
      slowestOperations: _getSlowestOperations(filteredMetrics, 10),
    );
  }

  /// Exportiert Performance-Daten als JSON
  static String exportMetrics() {
    final data = _metrics.map((metric) => metric.toJson()).toList();
    return json.encode(data);
  }

  /// Löscht Performance-Daten
  static Future<void> clearMetrics() async {
    _metrics.clear();
    await _saveMetrics();
  }

  /// Lädt Performance-Daten beim App-Start
  static Future<void> loadMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_metricsKey);
      
      if (metricsJson != null) {
        final List<dynamic> data = json.decode(metricsJson);
        _metrics.clear();
        _metrics.addAll(
          data.map((item) => PerformanceMetric.fromJson(item)).toList(),
        );
      }
    } catch (e) {
      _log.w('Fehler beim Laden der Performance-Metriken: $e');
    }
  }

  /// Private Hilfsmethoden
  static void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // Begrenze History-Größe
    while (_metrics.length > _maxMetricsHistory) {
      _metrics.removeFirst();
    }
    
    // Speichere Metriken persistent (async)
    _saveMetrics();
  }

  static void _logPerformanceResult(PerformanceMetric metric) {
    final duration = metric.duration;
    final operationId = metric.operationId;
    
    if (duration.inMilliseconds > 1000) {
      _log.w('SLOW OPERATION: $operationId took ${duration.inMilliseconds}ms');
    } else if (duration.inMilliseconds > 100) {
      _log.i('Performance: $operationId took ${duration.inMilliseconds}ms');
    } else {
      _log.d('Performance: $operationId took ${duration.inMilliseconds}ms');
    }
  }

  static void _checkThresholds(
    String operationId,
    Duration? duration,
    Duration? warningThreshold,
    Duration? errorThreshold,
  ) {
    if (duration == null) return;

    if (errorThreshold != null && duration > errorThreshold) {
      _log.e('PERFORMANCE ERROR: $operationId exceeded error threshold (${duration.inMilliseconds}ms > ${errorThreshold.inMilliseconds}ms)');
    } else if (warningThreshold != null && duration > warningThreshold) {
      _log.w('PERFORMANCE WARNING: $operationId exceeded warning threshold (${duration.inMilliseconds}ms > ${warningThreshold.inMilliseconds}ms)');
    }
  }

  static List<PerformanceMetric> _getSlowestOperations(List<PerformanceMetric> metrics, int count) {
    final sorted = List<PerformanceMetric>.from(metrics);
    sorted.sort((a, b) => b.duration.compareTo(a.duration));
    return sorted.take(count).toList();
  }

  static Future<void> _saveMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = json.encode(_metrics.map((m) => m.toJson()).toList());
      await prefs.setString(_metricsKey, metricsJson);
    } catch (e) {
      _log.w('Fehler beim Speichern der Performance-Metriken: $e');
    }
  }
}

/// Performance-Kategorien
enum PerformanceCategory {
  general,
  ui,
  network,
  database,
  computation,
  fileIO,
}

/// Performance-Metrik Datenklasse
class PerformanceMetric {
  final String operationId;
  final Duration duration;
  final DateTime timestamp;
  final PerformanceCategory category;
  final Map<String, dynamic>? context;

  PerformanceMetric({
    required this.operationId,
    required this.duration,
    required this.timestamp,
    required this.category,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'operationId': operationId,
      'duration': duration.inMicroseconds,
      'timestamp': timestamp.toIso8601String(),
      'category': category.name,
      'context': context,
    };
  }

  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      operationId: json['operationId'],
      duration: Duration(microseconds: json['duration']),
      timestamp: DateTime.parse(json['timestamp']),
      category: PerformanceCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PerformanceCategory.general,
      ),
      context: json['context'],
    );
  }
}

/// Performance-Statistiken
class PerformanceStats {
  final int totalOperations;
  final Duration averageDuration;
  final Duration minDuration;
  final Duration maxDuration;
  final Duration medianDuration;
  final Duration p95Duration;
  final List<PerformanceMetric> slowestOperations;

  PerformanceStats({
    required this.totalOperations,
    required this.averageDuration,
    required this.minDuration,
    required this.maxDuration,
    required this.medianDuration,
    required this.p95Duration,
    required this.slowestOperations,
  });

  factory PerformanceStats.empty() {
    return PerformanceStats(
      totalOperations: 0,
      averageDuration: Duration.zero,
      minDuration: Duration.zero,
      maxDuration: Duration.zero,
      medianDuration: Duration.zero,
      p95Duration: Duration.zero,
      slowestOperations: [],
    );
  }

  @override
  String toString() {
    return '''
Performance Stats:
- Total Operations: $totalOperations
- Average Duration: ${averageDuration.inMilliseconds}ms
- Min Duration: ${minDuration.inMilliseconds}ms
- Max Duration: ${maxDuration.inMilliseconds}ms
- Median Duration: ${medianDuration.inMilliseconds}ms
- 95th Percentile: ${p95Duration.inMilliseconds}ms
- Slowest Operations: ${slowestOperations.length}
''';
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../utils/logging.dart';
import '../mixins/error_handling_mixin.dart';

/// Basis-Klasse für alle API-Clients mit einheitlichem Error Handling
abstract class BaseApiClient with ErrorHandlingMixin {
  final _log = getLogger('BaseApiClient');

  @override
  String get componentName => runtimeType.toString();

  /// Standard HTTP Headers für API-Requests
  Map<String, String> get defaultHeaders => {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'User-Agent': 'EinsteinAI-App/1.0',
  };

  /// Standard Timeout für API-Requests
  Duration get defaultTimeout => const Duration(seconds: 30);

  /// Maximale Anzahl von Retry-Versuchen
  int get maxRetries => 3;

  /// Delay zwischen Retry-Versuchen
  Duration get retryDelay => const Duration(seconds: 2);

  /// Führt eine sichere HTTP GET-Anfrage durch
  Future<http.Response> safeGet(
    Uri uri, {
    Map<String, String>? headers,
    Duration? timeout,
    BuildContext? context,
  }) async {
    return await safeApiOperation<http.Response>(
      () async => _makeHttpRequest(
        () => http.get(uri, headers: _mergeHeaders(headers)),
        timeout: timeout ?? defaultTimeout,
      ),
      context: context,
      errorMessage: 'HTTP GET-Anfrage fehlgeschlagen',
    ) ?? _createErrorResponse(500, 'Request failed');
  }

  /// Führt eine sichere HTTP POST-Anfrage durch
  Future<http.Response> safePost(
    Uri uri, {
    Map<String, String>? headers,
    Object? body,
    Duration? timeout,
    BuildContext? context,
  }) async {
    return await safeApiOperation<http.Response>(
      () async => _makeHttpRequest(
        () => http.post(uri, headers: _mergeHeaders(headers), body: body),
        timeout: timeout ?? defaultTimeout,
      ),
      context: context,
      errorMessage: 'HTTP POST-Anfrage fehlgeschlagen',
    ) ?? _createErrorResponse(500, 'Request failed');
  }

  /// Führt eine sichere HTTP PUT-Anfrage durch
  Future<http.Response> safePut(
    Uri uri, {
    Map<String, String>? headers,
    Object? body,
    Duration? timeout,
    BuildContext? context,
  }) async {
    return await safeApiOperation<http.Response>(
      () async => _makeHttpRequest(
        () => http.put(uri, headers: _mergeHeaders(headers), body: body),
        timeout: timeout ?? defaultTimeout,
      ),
      context: context,
      errorMessage: 'HTTP PUT-Anfrage fehlgeschlagen',
    ) ?? _createErrorResponse(500, 'Request failed');
  }

  /// Führt eine sichere HTTP DELETE-Anfrage durch
  Future<http.Response> safeDelete(
    Uri uri, {
    Map<String, String>? headers,
    Duration? timeout,
    BuildContext? context,
  }) async {
    return await safeApiOperation<http.Response>(
      () async => _makeHttpRequest(
        () => http.delete(uri, headers: _mergeHeaders(headers)),
        timeout: timeout ?? defaultTimeout,
      ),
      context: context,
      errorMessage: 'HTTP DELETE-Anfrage fehlgeschlagen',
    ) ?? _createErrorResponse(500, 'Request failed');
  }

  /// Interne Methode für HTTP-Requests mit Timeout und Error Handling
  Future<http.Response> _makeHttpRequest(
    Future<http.Response> Function() request, {
    required Duration timeout,
  }) async {
    try {
      final response = await request().timeout(
        timeout,
        onTimeout: () => throw TimeoutException(
          'HTTP-Anfrage dauerte zu lange (>${timeout.inSeconds}s)',
          timeout,
        ),
      );
      
      _logResponse(response);
      return response;
    } on SocketException catch (e) {
      _log.e('Netzwerkfehler bei HTTP-Anfrage', error: e);
      throw Exception('Keine Internetverbindung verfügbar. Bitte überprüfen Sie Ihre Netzwerkeinstellungen.');
    } on TimeoutException catch (e) {
      _log.e('Timeout bei HTTP-Anfrage', error: e);
      throw Exception('Die Anfrage dauerte zu lange. Bitte versuchen Sie es erneut.');
    } on FormatException catch (e) {
      _log.e('Format-Fehler bei HTTP-Anfrage', error: e);
      throw Exception('Ungültige Antwort vom Server erhalten.');
    }
  }

  /// Führt eine Operation mit Retry-Logik durch
  @override
  Future<T?> executeWithRetry<T>(
    Future<T> Function() operation, {
    int? maxRetries,
    Duration? retryDelay,
    String? errorMessage,
  }) async {
    final maxAttempts = maxRetries ?? this.maxRetries;
    final delay = retryDelay ?? this.retryDelay;
    
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (e) {
        if (attempt == maxAttempts) {
          _log.e('Operation nach $maxAttempts Versuchen fehlgeschlagen: $e');
          throw Exception(errorMessage ?? 'Operation fehlgeschlagen nach mehreren Versuchen: $e');
        }
        
        if (_shouldRetry(e)) {
          _log.w('Versuch $attempt/$maxAttempts fehlgeschlagen, wiederhole in ${delay.inSeconds}s: $e');
          await Future.delayed(delay * attempt);
        } else {
          _log.e('Nicht-wiederholbarer Fehler: $e');
          rethrow;
        }
      }
    }
    
    return null;
  }

  /// Prüft ob ein Fehler einen Retry rechtfertigt
  bool _shouldRetry(dynamic error) {
    if (error is SocketException) return true;
    if (error is TimeoutException) return true;
    if (error is HttpException) {
      final statusCode = error.message.contains('500') || 
                        error.message.contains('502') || 
                        error.message.contains('503') || 
                        error.message.contains('504') ||
                        error.message.contains('429');
      return statusCode;
    }
    return false;
  }

  /// Merged Standard-Headers mit benutzerdefinierten Headers
  Map<String, String> _mergeHeaders(Map<String, String>? customHeaders) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }
    return headers;
  }

  /// Loggt HTTP-Response Details
  void _logResponse(http.Response response) {
    _log.d('HTTP ${response.request?.method} ${response.request?.url} -> ${response.statusCode}');
    if (response.statusCode >= 400) {
      _log.w('HTTP Error Response Body: ${response.body}');
    }
  }

  /// Erstellt eine Error-Response für Fallback-Fälle
  http.Response _createErrorResponse(int statusCode, String message) {
    return http.Response(
      json.encode({'error': message}),
      statusCode,
      headers: {'content-type': 'application/json'},
    );
  }

  /// Parst JSON-Response sicher
  Map<String, dynamic> parseJsonResponse(http.Response response) {
    try {
      if (response.body.isEmpty) {
        throw FormatException('Leere Response erhalten');
      }
      
      final decoded = json.decode(utf8.decode(response.bodyBytes));
      if (decoded is! Map<String, dynamic>) {
        throw FormatException('Unerwartetes Response-Format: ${decoded.runtimeType}');
      }
      
      return decoded;
    } catch (e) {
      _log.e('JSON-Parsing-Fehler: ${response.body}', error: e);
      throw FormatException('Fehler beim Parsen der Server-Antwort: $e');
    }
  }

  /// Behandelt HTTP-Status-Codes einheitlich
  void handleHttpStatusCode(http.Response response, {String? operation}) {
    final op = operation ?? 'HTTP-Operation';
    
    switch (response.statusCode) {
      case 200:
      case 201:
      case 204:
        // Erfolg - nichts zu tun
        break;
      case 400:
        throw Exception('Ungültige Anfrage ($op): ${_extractErrorMessage(response)}');
      case 401:
        throw Exception('Nicht autorisiert ($op): Bitte melden Sie sich erneut an');
      case 403:
        throw Exception('Zugriff verweigert ($op): Keine Berechtigung für diese Aktion');
      case 404:
        throw Exception('Nicht gefunden ($op): Die angeforderte Ressource existiert nicht');
      case 429:
        throw Exception('Zu viele Anfragen ($op): Bitte warten Sie einen Moment');
      case 500:
      case 502:
      case 503:
      case 504:
        throw Exception('Server-Fehler ($op): Bitte versuchen Sie es später erneut');
      default:
        throw Exception('HTTP-Fehler $op (${response.statusCode}): ${_extractErrorMessage(response)}');
    }
  }

  /// Extrahiert Fehlermeldung aus HTTP-Response
  String _extractErrorMessage(http.Response response) {
    try {
      final body = response.body;
      if (body.isNotEmpty) {
        final data = json.decode(body);
        if (data is Map && data.containsKey('message')) {
          return data['message'].toString();
        }
        if (data is Map && data.containsKey('error')) {
          return data['error'].toString();
        }
      }
    } catch (e) {
      // Ignore JSON parsing errors
    }
    return 'Unbekannter Server-Fehler';
  }

  /// Validiert Input-Parameter
  @override
  void validateInput(Map<String, dynamic> params) {
    for (final entry in params.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value == null) {
        throw ArgumentError('Parameter "$key" darf nicht null sein');
      }
      
      if (value is String && value.trim().isEmpty) {
        throw ArgumentError('Parameter "$key" darf nicht leer sein');
      }
      
      if (value is int && value < 0) {
        throw ArgumentError('Parameter "$key" muss positiv sein');
      }
    }
  }

  /// Bereinigt Input-Strings
  String sanitizeInput(String input) {
    return input.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
}

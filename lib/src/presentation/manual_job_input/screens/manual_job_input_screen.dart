import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../application/providers/user_profile_provider.dart';
import '../../../application/providers/services_providers.dart';
import '../../../domain/models/user_profile.dart';
import '../../shared/widgets/model_selection_chip.dart';

class ManualJobInputScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic>? extraData;

  const ManualJobInputScreen({super.key, this.extraData});

  @override
  ConsumerState<ManualJobInputScreen> createState() =>
      _ManualJobInputScreenState();
}

class _ManualJobInputScreenState extends ConsumerState<ManualJobInputScreen>
    with TickerProviderStateMixin {
  final Logger _log = Logger();

  // Controllers
  final TextEditingController _jobTitleController = TextEditingController();
  final TextEditingController _companyController = TextEditingController();
  final TextEditingController _jobDescriptionController =
      TextEditingController();
  final TextEditingController _additionalHintsController =
      TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // State
  bool _isGenerating = false;
  String _selectedModelType = 'mistral';
  String? _generatedApplication;
  String? _sourceUrl;
  bool _isFromSharedUrl = false;

  // Animation Controllers
  late AnimationController _buttonAnimationController;
  late AnimationController _gradientSweepController;
  late Animation<double> _buttonAnimation;
  late Animation<double> _gradientSweepAnimation;

  @override
  void initState() {
    super.initState();

    // Animation Controllers initialisieren
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _gradientSweepController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _buttonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _buttonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _gradientSweepAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _gradientSweepController, curve: Curves.linear),
    );

    // Prüfe auf vorausgefüllte Daten
    _checkForPrefillData();
  }

  void _checkForPrefillData() {
    if (widget.extraData != null) {
      _log.i('ExtraData empfangen: ${widget.extraData}');

      final sourceUrl = widget.extraData!['sourceUrl'] as String?;
      final prefillData =
          widget.extraData!['prefillData'] as Map<String, dynamic>?;

      if (sourceUrl != null) {
        _sourceUrl = sourceUrl;
        _isFromSharedUrl = true;
        _log.i('URL-Quelle: $_sourceUrl');
      }

      if (prefillData != null) {
        _log.i('Fülle Felder mit extrahierten Daten vor...');

        // Fülle die Felder mit den extrahierten Daten
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (prefillData['jobTitle'] != null &&
              prefillData['jobTitle'].toString().isNotEmpty) {
            _jobTitleController.text = prefillData['jobTitle'].toString();
          }

          if (prefillData['companyName'] != null &&
              prefillData['companyName'].toString().isNotEmpty) {
            _companyController.text = prefillData['companyName'].toString();
          }

          if (prefillData['jobDescription'] != null &&
              prefillData['jobDescription'].toString().isNotEmpty) {
            _jobDescriptionController.text =
                prefillData['jobDescription'].toString();
          }

          // Zusätzliche Informationen in Hinweise einfügen
          final additionalInfo =
              prefillData['additionalInfo'] as Map<String, String>?;
          if (additionalInfo != null && additionalInfo.isNotEmpty) {
            final hintsText = additionalInfo.entries
                .map((entry) => '${entry.key}: ${entry.value}')
                .join('\n');
            _additionalHintsController.text = hintsText;
          }

          _log.i('Felder erfolgreich vorausgefüllt');

          // Zeige Erfolgs-Snackbar
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Stellenanzeige erfolgreich aus geteilter URL extrahiert!',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        });
      }
    }
  }

  @override
  void dispose() {
    _jobTitleController.dispose();
    _companyController.dispose();
    _jobDescriptionController.dispose();
    _additionalHintsController.dispose();
    _scrollController.dispose();
    _buttonAnimationController.dispose();
    _gradientSweepController.dispose();
    super.dispose();
  }

  Future<void> _generateApplication() async {
    if (_jobDescriptionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bitte geben Sie eine Jobbeschreibung ein.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Prüfe vor Start, ob die gewählte Qualitätsstufe erlaubt ist
    final userProfileState = ref.read(userProfileProvider);
    final planType =
        userProfileState.asData?.value.premiumPlanType?.toLowerCase() ??
        'basic';
    final canUseHighQuality = planType == 'pro' || planType == 'unlimited';
    if (_selectedModelType == 'deepseek' && !canUseHighQuality) {
      await showDialog(
        context: context,
        builder:
            (ctx) => AlertDialog(
              title: const Text('Nicht verfügbar'),
              content: const Text(
                'Diese Qualitätsstufe ist nur für Pro oder Unlimited Mitglieder verfügbar.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(ctx).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
      );
      return;
    }

    setState(() {
      _isGenerating = true;
      _generatedApplication = null;
    });

    // Animationen starten
    _buttonAnimationController.repeat();
    _gradientSweepController.repeat();

    try {
      final result = await _generateApplicationWithAI();

      if (result['generatedText'] != null && result['error'] == null) {
        setState(() {
          _generatedApplication = result['generatedText'] as String;
        });

        // Automatisch zur generierten Bewerbung scrollen
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bewerbung erfolgreich generiert!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['error'] ?? 'Fehler bei der Generierung'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      _log.e('Fehler bei der Bewerbungsgenerierung: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Fehler bei der Generierung der Bewerbung.'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isGenerating = false;
      });

      // Animationen stoppen
      _buttonAnimationController.stop();
      _buttonAnimationController.reset();
      _gradientSweepController.stop();
      _gradientSweepController.reset();
    }
  }

  Future<Map<String, dynamic>> _generateApplicationWithAI() async {
    _log.i('Starte KI-Anschreiben-Generierung für manuellen Job...');

    // User Profil holen
    final userProfileState = ref.read(userProfileProvider);
    final UserProfile? userProfile = userProfileState.asData?.value;

    if (userProfile == null) {
      _log.e('User-Profil nicht verfügbar für KI-Generierung.');
      return {'error': 'Benutzerprofil konnte nicht geladen werden.'};
    }

    try {
      // Erstelle Job-Daten aus den Eingabefeldern
      final Map<String, dynamic> jobPostingDataForFunction = {
        'title':
            _jobTitleController.text.trim().isNotEmpty
                ? _jobTitleController.text.trim()
                : 'Stellenausschreibung',
        'company':
            _companyController.text.trim().isNotEmpty
                ? _companyController.text.trim()
                : 'Unbekanntes Unternehmen',
        'description': _jobDescriptionController.text.trim(),
      };

      // User-Profil für die Funktion vorbereiten
      final Map<String, dynamic> userProfileDataForFunction =
          userProfile.toJson();

      // Zusätzliche Hinweise hinzufügen falls vorhanden
      if (_additionalHintsController.text.trim().isNotEmpty) {
        userProfileDataForFunction['additionalHints'] =
            _additionalHintsController.text.trim();
      }

      _log.i('Rufe SupabaseService.generateCoverLetter auf...');
      _log.i('Modell (gewählt): $_selectedModelType');

      // Durchsetzen: Free darf die hohe Qualitätsstufe nicht nutzen
      final userProfileState = ref.read(userProfileProvider);
      final planType =
          userProfileState.asData?.value.premiumPlanType?.toLowerCase() ??
          'basic';
      final canUseHighQuality = planType == 'pro' || planType == 'unlimited';
      if (_selectedModelType == 'deepseek' && !canUseHighQuality) {
        _log.w('Qualitätsstufe gesperrt für Free. Vorgang abgebrochen.');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Diese Qualitätsstufe ist nur für Pro/Unlimited verfügbar.',
            ),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
        return {'error': 'Nicht erlaubt'};
      }

      final supabaseService = ref.read(supabaseServiceProvider);

      // Timeout für die Anfrage setzen (2 Minuten)
      final result = await Future.any([
        // Hauptanfrage
        supabaseService.generateCoverLetter(
          userProfile: userProfileDataForFunction,
          jobPosting: jobPostingDataForFunction,
          modelType: _selectedModelType,
        ),
        // Timeout-Future
        Future.delayed(const Duration(minutes: 2)).then(
          (_) =>
              throw TimeoutException(
                'Die Anfrage hat zu lange gedauert. Bitte versuchen Sie es erneut.',
                const Duration(minutes: 2),
              ),
        ),
      ]);

      _log.i('KI-Anschreiben erfolgreich generiert');
      return {
        'generatedText': result.coverLetter,
        'extractedEmail': result.extractedEmail,
        'modelType': result.modelType,
      };
    } catch (e, stack) {
      _log.e('Fehler bei der KI-Generierung: $e\n$stack');
      return {'error': 'Fehler bei der Generierung: ${e.toString()}'};
    }
  }

  void _copyToClipboard() {
    if (_generatedApplication != null) {
      Clipboard.setData(ClipboardData(text: _generatedApplication!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bewerbung in Zwischenablage kopiert!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Bewerbung erstellen',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Info-Banner für geteilte URLs
            if (_isFromSharedUrl) ...[
              _buildSharedUrlInfoBanner(),
              const SizedBox(height: 16),
            ],

            // Info-Text für URL-Sharing-Funktion
            _buildUrlSharingInfoCard(),
            const SizedBox(height: 24),

            // Jobtitel
            _buildTextField(
              controller: _jobTitleController,
              label: 'Jobtitel (optional)',
              hint: 'z.B. Software Entwickler',
            ),
            const SizedBox(height: 16),

            // Unternehmen
            _buildTextField(
              controller: _companyController,
              label: 'Unternehmen (optional)',
              hint: 'z.B. Tech GmbH',
            ),
            const SizedBox(height: 16),

            // Jobbeschreibung
            _buildTextField(
              controller: _jobDescriptionController,
              label: 'Jobbeschreibung *',
              hint: 'Fügen Sie hier die komplette Jobbeschreibung ein...',
              maxLines: 8,
              required: true,
            ),
            const SizedBox(height: 16),

            // Zusätzliche Hinweise
            _buildTextField(
              controller: _additionalHintsController,
              label: 'Zusätzliche Hinweise (optional)',
              hint:
                  'Besondere Punkte, die in der Bewerbung erwähnt werden sollen...',
              maxLines: 3,
            ),
            const SizedBox(height: 24),

            // Modellauswahl
            ModelSelectionChip(
              selectedModel: _selectedModelType,
              canUseDeepseek:
                  (() {
                    final userProfileState = ref.watch(userProfileProvider);
                    final planType =
                        userProfileState.asData?.value.premiumPlanType
                            ?.toLowerCase() ??
                        'basic';
                    return planType == 'pro' || planType == 'unlimited';
                  })(),
              onModelChanged: (model) {
                setState(() {
                  _selectedModelType = model;
                });
              },
            ),
            const SizedBox(height: 24),

            // Generieren Button
            _buildGenerateButton(),

            // Generierte Bewerbung anzeigen
            if (_generatedApplication != null) ...[
              const SizedBox(height: 24),
              _buildGeneratedApplicationCard(),
              const SizedBox(
                height: 50,
              ), // Extra Platz am Ende für bessere Sichtbarkeit
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    bool required = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: required ? Colors.orange : Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[600]),
            filled: true,
            fillColor: Colors.grey[900],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.blue, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenerateButton() {
    return AnimatedBuilder(
      animation: _buttonAnimation,
      builder: (context, child) {
        return Container(
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(28),
            gradient:
                _isGenerating
                    ? LinearGradient(
                      colors: [
                        Colors.blue.withValues(alpha: 0.3),
                        Colors.purple.withValues(alpha: 0.3),
                        Colors.blue.withValues(alpha: 0.3),
                      ],
                      stops: [0.0, _gradientSweepAnimation.value, 1.0],
                    )
                    : const LinearGradient(
                      colors: [Colors.blue, Colors.purple],
                    ),
          ),
          child: ElevatedButton(
            onPressed: _isGenerating ? null : _generateApplication,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
            ),
            child:
                _isGenerating
                    ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Generiere Bewerbung...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                    : const Text(
                      'Bewerbung generieren',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
          ),
        );
      },
    );
  }

  Widget _buildGeneratedApplicationCard() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Generierte Bewerbung',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy, color: Colors.blue),
                  tooltip: 'In Zwischenablage kopieren',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[700]!),
              ),
              child: SelectableText(
                _generatedApplication!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Banner für geteilte URLs
  Widget _buildSharedUrlInfoBanner() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.share, color: Colors.green, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Aus geteilter URL extrahiert',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (_sourceUrl != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _sourceUrl!,
                    style: TextStyle(
                      color: Colors.green.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Info-Karte für URL-Sharing-Funktion
  Widget _buildUrlSharingInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'Tipp: URL-Sharing',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Sie können Stellenanzeigen direkt aus dem Browser mit dieser App teilen. '
            'Die App extrahiert automatisch die relevanten Informationen und füllt die Felder vor.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.share,
                color: Colors.blue.withValues(alpha: 0.7),
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                'Browser → Teilen → ki_test',
                style: TextStyle(
                  color: Colors.blue.withValues(alpha: 0.8),
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message';
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../application/providers/favorites_provider.dart';

import '../widgets/favorite_job_list_item.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import '../../../core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import '../../common/widgets/job_search_filter.dart';
import '../../../domain/entities/job_entity.dart';
import '../../shared/widgets/error_display_widget.dart';

import '../../offline_texts/screens/offline_texts_screen.dart';

class FavoritesScreen extends HookConsumerWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favoritesState = ref.watch(favoritesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizationsWrapper.of(context).favoritesTitle),
        elevation: 0,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          // Button für Offline-Texte anzeigen
          IconButton(
            icon: const Icon(Icons.cloud_off),
            tooltip: 'Offline-Texte anzeigen',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const OfflineTextsScreen(),
                ),
              );
            },
          ),
        ],
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: favoritesState.when(
        data: (favoriteJobs) {
          if (favoriteJobs.isEmpty) {
            return _buildEmptyView(context);
          } else {
            return _FavoritesWithFilter(favoriteJobs: favoriteJobs, ref: ref);
          }
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => ErrorDisplayWidget(
              error: error.toString(),
              customTitle:
                  'Fehler beim Laden der ${AppLocalizationsWrapper.of(context).favoritesTitle}',
              onRetry: () {
                ref.read(favoritesProvider.notifier).loadFavorites();
              },
            ),
      ),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 80, color: Colors.grey[400]),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Keine ${AppLocalizationsWrapper.of(context).favoritesTitle} gespeichert',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
            ),
            child: Text(
              'Tippe auf das Herz-Symbol bei Jobs, um sie zu ${AppLocalizationsWrapper.of(context).addToFavorites}.',
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class _FavoritesWithFilter extends HookWidget {
  final List<JobEntity> favoriteJobs;
  final WidgetRef ref;

  const _FavoritesWithFilter({required this.favoriteJobs, required this.ref});

  @override
  Widget build(BuildContext context) {
    final filteredJobs = useState<List<JobEntity>>(favoriteJobs);

    // Aktualisiere gefilterte Jobs wenn sich die Favoriten ändern
    useEffect(() {
      filteredJobs.value = favoriteJobs;
      return null;
    }, [favoriteJobs]);

    return Column(
      children: [
        // Suchfilter
        JobSearchFilter(
          originalJobs: favoriteJobs,
          onFilteredJobsChanged: (filtered) {
            filteredJobs.value = filtered;
          },
          hintText: 'Favoriten durchsuchen...',
        ),

        // Ergebnisanzahl anzeigen
        if (filteredJobs.value.length != favoriteJobs.length)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
              vertical: AppTheme.spacingXSmall,
            ),
            child: Text(
              '${filteredJobs.value.length} von ${favoriteJobs.length} Favoriten',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),

        // Job-Liste
        Expanded(
          child:
              filteredJobs.value.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),
                        Text(
                          'Keine Favoriten gefunden',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          'Versuche andere Suchbegriffe',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    // Verringerter Abstand zur Navigation
                    padding: const EdgeInsets.only(
                      left: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                      right: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                      top: AppTheme.spacingSmall,
                      bottom: kBottomNavigationBarHeight - 32,
                    ),
                    itemCount: filteredJobs.value.length,
                    itemBuilder: (context, index) {
                      final job = filteredJobs.value[index];
                      return FavoriteJobListItem(
                        job: job,
                        onTap: () {
                          // Snackbar ausblenden, wenn zu einer anderen Seite navigiert wird
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => JobDetailScreen(
                                    jobRefnr: job.id,
                                    jobTitle: job.title,
                                    sourceUrl: job.sourceUrl,
                                    jobEntity: job,
                                  ),
                            ),
                          );
                        },
                        onDismissed: () {
                          // Job speichern, bevor er entfernt wird, um ihn ggf. wiederherstellen zu können
                          final deletedJob = job;
                          ref
                              .read(favoritesProvider.notifier)
                              .removeFavorite(job.id);

                          // Snackbar mit Wiederherstellungsoption für 5 Sekunden anzeigen
                          ScaffoldMessenger.of(context).clearSnackBars();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: Colors.red[700],
                              content: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Aus Favoriten entfernt',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      // Job wiederherstellen
                                      ref
                                          .read(favoritesProvider.notifier)
                                          .restoreFavorite(deletedJob);
                                      ScaffoldMessenger.of(
                                        context,
                                      ).hideCurrentSnackBar();
                                    },
                                    child: Text(
                                      'Wiederherstellen',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 13,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              duration: const Duration(seconds: 5),
                              behavior: SnackBarBehavior.fixed,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              elevation: 4,
                            ),
                          );
                        },
                      );
                    },
                  ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import '../../../domain/entities/job_entity.dart';
import '../../job_search/widgets/job_list_item.dart'; // Wir verwenden die Basiskomponenten wieder

class FavoriteJobListItem extends StatelessWidget {
  final JobEntity job;
  final VoidCallback onTap;
  final VoidCallback onDismissed;

  const FavoriteJobListItem({
    super.key,
    required this.job,
    required this.onTap,
    required this.onDismissed,
  });

  @override
  Widget build(BuildContext context) {
    // Verwende Dismissible, um Wischen zum Entfernen zu ermöglichen
    return Dismissible(
      key: Key(job.id), // Eindeutiger Key für jedes Element
      direction: DismissDirection.endToStart, // Nur Wischen von rechts nach links
      onDismissed: (direction) {
        onDismissed(); // Callback aufrufen, wenn gewischt wurde
      },
      background: Container(
        color: Colors.redAccent.withValues(alpha: 0.8),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: const Row(
           mainAxisSize: MainAxisSize.min,
           children: [
             Text('Entfernen', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
             SizedBox(width: 8),
             Icon(Icons.delete_sweep_outlined, color: Colors.white),
           ],
        ),
      ),
      child: JobListItem( // Verwende das normale JobListItem für die Anzeige
        job: job,
        onTap: onTap,
        // Das Herz-Icon wird hier nicht direkt gesteuert, da es ein Favorit ist
        // Optional: Man könnte hier ein immer gefülltes Herz anzeigen
      ),
    );
  }
} 
import 'package:flutter/material.dart';
import '../../../domain/models/cv_template.dart';

/// Widget für CV-Template-Karte
class CvTemplateCard extends StatelessWidget {
  final CvTemplate template;
  final bool isSelected;
  final bool isAvailable;
  final VoidCallback onTap;
  final VoidCallback onPreview;
  final VoidCallback? onCustomize;

  const CvTemplateCard({
    super.key,
    required this.template,
    required this.isSelected,
    required this.isAvailable,
    required this.onTap,
    required this.onPreview,
    this.onCustomize,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isSelected
                ? BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                )
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Template-Vorschau
                Expanded(
                  flex: 3,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      color: template.colorScheme.secondaryColor,
                    ),
                    child: Stack(
                      children: [
                        // Placeholder für Template-Vorschau
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _getTemplateIcon(),
                                size: 48,
                                color: template.colorScheme.primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                template.type.displayName,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: template.colorScheme.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Action-Buttons
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Column(
                            children: [
                              // Vorschau-Button
                              IconButton(
                                onPressed: onPreview,
                                icon: const Icon(Icons.visibility),
                                iconSize: 20,
                                style: IconButton.styleFrom(
                                  backgroundColor: Colors.white.withValues(
                                    alpha: 0.9,
                                  ),
                                  foregroundColor:
                                      template.colorScheme.primaryColor,
                                ),
                              ),
                              // Anpassen-Button (nur für editierbare Templates)
                              if (template.isCustomizable &&
                                  onCustomize != null) ...[
                                const SizedBox(height: 4),
                                IconButton(
                                  onPressed: onCustomize,
                                  icon: const Icon(Icons.tune),
                                  iconSize: 20,
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.white.withValues(
                                      alpha: 0.9,
                                    ),
                                    foregroundColor:
                                        template.colorScheme.primaryColor,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Template-Info
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Template-Name
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                template.name,
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.bold),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (template.isPremium) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.star,
                                size: 16,
                                color: Colors.amber[600],
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Template-Beschreibung
                        Expanded(
                          child: Text(
                            template.description,
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color:
                                  Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // Farbschema-Indikator
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: template.colorScheme.primaryColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              template.colorScheme.displayName,
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                fontSize: 10,
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Ausgewählt-Indikator
            if (isSelected)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),

            // Nicht verfügbar Overlay
            if (!isAvailable)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.black.withValues(alpha: 0.6),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.lock, size: 32, color: Colors.white),
                      const SizedBox(height: 8),
                      Text(
                        'Premium\nerforderlich',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Gibt Icon für Template-Typ zurück
  IconData _getTemplateIcon() {
    switch (template.type) {
      case CvTemplateType.classic:
        return Icons.description;
      case CvTemplateType.modern:
        return Icons.auto_awesome;
      case CvTemplateType.creative:
        return Icons.palette;
      case CvTemplateType.minimalist:
        return Icons.minimize;
      case CvTemplateType.professional:
        return Icons.business;
      case CvTemplateType.elegant:
        return Icons.diamond;
      case CvTemplateType.tech:
        return Icons.computer;
      case CvTemplateType.executive:
        return Icons.corporate_fare;
      case CvTemplateType.simple:
        return Icons.minimize;
      case CvTemplateType.luxury:
        return Icons.diamond;
      case CvTemplateType.corporate:
        return Icons.business;
      case CvTemplateType.artistic:
        return Icons.palette;
      case CvTemplateType.startup:
        return Icons.rocket_launch;
      case CvTemplateType.academic:
        return Icons.school;
      case CvTemplateType.fresh:
        return Icons.refresh;
      case CvTemplateType.eco:
        return Icons.eco;
      case CvTemplateType.traditional:
        return Icons.account_balance;
      case CvTemplateType.timeline:
        return Icons.timeline;
      case CvTemplateType.infographic:
        return Icons.bar_chart;
      case CvTemplateType.grid:
        return Icons.grid_view;
      case CvTemplateType.technical:
        return Icons.code;
      case CvTemplateType.magazine:
        return Icons.article;
    }
  }
}

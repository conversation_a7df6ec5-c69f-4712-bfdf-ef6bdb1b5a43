import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import '../../../domain/models/cv_template.dart';
import '../../../application/providers/pdf_cv_generator_provider.dart';
import '../../../application/providers/cv_template_provider.dart';

/// Dialog für CV-Template-Vorschau
class CvTemplatePreviewDialog extends ConsumerStatefulWidget {
  final CvTemplate template;

  const CvTemplatePreviewDialog({super.key, required this.template});

  @override
  ConsumerState<CvTemplatePreviewDialog> createState() =>
      _CvTemplatePreviewDialogState();
}

class _CvTemplatePreviewDialogState
    extends ConsumerState<CvTemplatePreviewDialog> {
  @override
  void initState() {
    super.initState();
    // Generiere Vorschau beim <PERSON>nen des Dialogs
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pdfPreviewProvider(widget.template).notifier).generatePreview();
    });
  }

  @override
  Widget build(BuildContext context) {
    final previewState = ref.watch(pdfPreviewProvider(widget.template));

    return Dialog(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.template.colorScheme.primaryColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.template.name,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: widget.template.colorScheme.textColor,
                          ),
                        ),
                        Text(
                          widget.template.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: widget.template.colorScheme.textColor
                                .withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: widget.template.colorScheme.textColor,
                    ),
                  ),
                ],
              ),
            ),

            // Vorschau-Bereich
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: previewState.when(
                  data: (pdfBytes) {
                    if (pdfBytes != null) {
                      return _buildPdfPreview(pdfBytes);
                    } else {
                      return _buildEmptyState();
                    }
                  },
                  loading: () => _buildLoadingState(),
                  error: (error, stack) => _buildErrorState(error.toString()),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  // Template-Info
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: widget.template.colorScheme.primaryColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                widget.template.colorScheme.displayName,
                                style: Theme.of(context).textTheme.bodySmall,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (widget.template.isPremium) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Colors.amber[600],
                              ),
                              const SizedBox(width: 2),
                              Text(
                                'Premium',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: Colors.amber[600],
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),
                        Text(
                          widget.template.type.displayName,
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Buttons - Smaller Size Fixed Layout
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          height: 32,
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                              ),
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                            child: const Text('Schließen'),
                          ),
                        ),
                        const SizedBox(width: 6),
                        SizedBox(
                          height: 32,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop();
                              _selectTemplate();
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                            icon: const Icon(Icons.check, size: 16),
                            label: const Text('Auswählen'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Erstellt PDF-Vorschau Widget
  Widget _buildPdfPreview(dynamic pdfBytes) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GestureDetector(
          onTap: () => _openFullscreenPreview(),
          child: ref
              .watch(pdfPreviewProvider(widget.template))
              .when(
                data: (pdfBytes) {
                  if (pdfBytes != null) {
                    return FutureBuilder<String>(
                      future: _createTempPdfFile(pdfBytes),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Stack(
                              children: [
                                PDFView(
                                  filePath: snapshot.data!,
                                  enableSwipe: true,
                                  swipeHorizontal: false,
                                  autoSpacing: false,
                                  pageFling: false,
                                  pageSnap: true,
                                  defaultPage: 0,
                                  fitPolicy: FitPolicy.BOTH,
                                  preventLinkNavigation: true,
                                  onRender: (pages) {
                                    // PDF erfolgreich gerendert
                                  },
                                  onError: (error) {
                                    print('PDF-Vorschau Fehler: $error');
                                  },
                                  onPageError: (page, error) {
                                    print('PDF-Seite $page Fehler: $error');
                                  },
                                ),
                                // Vollbild-Hinweis
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.black54,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: const Icon(
                                      Icons.fullscreen,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        } else if (snapshot.hasError) {
                          return _buildErrorPlaceholder(
                            'PDF-Vorschau Fehler: ${snapshot.error}',
                          );
                        } else {
                          return _buildLoadingPlaceholder();
                        }
                      },
                    );
                  } else {
                    return _buildLoadingPlaceholder();
                  }
                },
                loading: () => _buildLoadingPlaceholder(),
                error:
                    (error, stack) => _buildErrorPlaceholder(error.toString()),
              ),
        ),
      ),
    );
  }

  /// Öffnet Vollbild-Ansicht der PDF-Vorschau
  void _openFullscreenPreview() {
    final previewState = ref.read(pdfPreviewProvider(widget.template));

    previewState.whenData((pdfBytes) {
      if (pdfBytes != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => _FullscreenPdfViewer(
                  pdfBytes: pdfBytes,
                  template: widget.template,
                ),
          ),
        );
      }
    });
  }

  /// Erstellt Loading-State
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: widget.template.colorScheme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Generiere Vorschau...',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ],
      ),
    );
  }

  /// Erstellt Error-State
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Fehler beim Laden der Vorschau',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Erstellt Empty-State
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.preview,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'Keine Vorschau verfügbar',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ],
      ),
    );
  }

  /// Wählt Template aus
  Future<void> _selectTemplate() async {
    try {
      // Template auswählen und persistieren
      await ref
          .read(selectedTemplateProvider.notifier)
          .selectTemplate(widget.template);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Vorlage "${widget.template.name}" ausgewählt'),
            backgroundColor: Colors.green,
          ),
        );

        // Dialog schließen
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Fehler beim Auswählen: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Erstellt temporäre PDF-Datei für Vorschau
  Future<String> _createTempPdfFile(Uint8List pdfBytes) async {
    final tempDir = await getTemporaryDirectory();
    final fileName = 'cv_preview_${widget.template.id}.pdf';
    final file = File('${tempDir.path}/$fileName');

    await file.writeAsBytes(pdfBytes);
    return file.path;
  }

  /// Teilt PDF
  Future<void> _sharePdf(Uint8List pdfBytes) async {
    try {
      // Temporäre Datei erstellen
      final tempDir = await getTemporaryDirectory();
      final fileName =
          'lebenslauf_${widget.template.name.toLowerCase().replaceAll(' ', '_')}.pdf';
      final file = File('${tempDir.path}/$fileName');

      // PDF-Bytes in Datei schreiben
      await file.writeAsBytes(pdfBytes);

      // PDF teilen
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Mein Lebenslauf erstellt mit ${widget.template.name} Vorlage',
        subject: 'Lebenslauf - ${widget.template.name}',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ PDF erfolgreich geteilt'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Fehler beim Teilen: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Erstellt Loading-Placeholder
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: widget.template.colorScheme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Generiere Vorschau...',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: widget.template.colorScheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Erstellt Error-Placeholder
  Widget _buildErrorPlaceholder(String error) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Fehler beim Laden',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// Vollbild-PDF-Viewer
class _FullscreenPdfViewer extends StatefulWidget {
  final Uint8List pdfBytes;
  final CvTemplate template;

  const _FullscreenPdfViewer({required this.pdfBytes, required this.template});

  @override
  State<_FullscreenPdfViewer> createState() => _FullscreenPdfViewerState();
}

class _FullscreenPdfViewerState extends State<_FullscreenPdfViewer> {
  String? _pdfPath;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _createTempPdfFile();
  }

  Future<void> _createTempPdfFile() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = 'cv_fullscreen_${widget.template.id}.pdf';
      final file = File('${tempDir.path}/$fileName');

      await file.writeAsBytes(widget.pdfBytes);

      if (mounted) {
        setState(() {
          _pdfPath = file.path;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          widget.template.name,
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: _sharePdf,
            icon: const Icon(Icons.share, color: Colors.white),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.white),
              )
              : _pdfPath != null
              ? PDFView(
                filePath: _pdfPath!,
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: true,
                pageFling: true,
                pageSnap: true,
                defaultPage: 0,
                fitPolicy: FitPolicy.BOTH,
                preventLinkNavigation: true,
                backgroundColor: Colors.black,
                onRender: (pages) {
                  print('✅ Vollbild-PDF: $pages Seiten gerendert');
                },
                onError: (error) {
                  print('❌ Vollbild-PDF Fehler: $error');
                },
                onPageError: (page, error) {
                  print('❌ Vollbild-PDF Seite $page Fehler: $error');
                },
              )
              : const Center(
                child: Text(
                  'Fehler beim Laden der PDF',
                  style: TextStyle(color: Colors.white),
                ),
              ),
    );
  }

  Future<void> _sharePdf() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName =
          'lebenslauf_${widget.template.name.toLowerCase().replaceAll(' ', '_')}.pdf';
      final file = File('${tempDir.path}/$fileName');

      await file.writeAsBytes(widget.pdfBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Mein Lebenslauf erstellt mit ${widget.template.name} Vorlage',
        subject: 'Lebenslauf - ${widget.template.name}',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ PDF erfolgreich geteilt'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Fehler beim Teilen: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../domain/models/cv_template.dart';
import '../../../application/providers/cv_template_provider.dart';
import '../../../application/providers/pdf_cv_generator_provider.dart';
import '../../../application/providers/user_profile_provider.dart';

import '../widgets/cv_template_card.dart';
import '../widgets/cv_template_preview_dialog.dart';
import 'cv_template_editor_screen.dart';

/// Hauptseite für den CV-Generator in den Einstellungen
class CvGeneratorMainScreen extends ConsumerStatefulWidget {
  const CvGeneratorMainScreen({super.key});

  @override
  ConsumerState<CvGeneratorMainScreen> createState() =>
      _CvGeneratorMainScreenState();
}

class _CvGeneratorMainScreenState extends ConsumerState<CvGeneratorMainScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProfile = ref.watch(userProfileProvider);
    final availableTemplates = ref.watch(availableTemplatesProvider);
    final freeTemplates = ref.watch(freeTemplatesProvider);
    final premiumTemplates = ref.watch(premiumTemplatesProvider);
    final selectedTemplate = ref.watch(selectedTemplateProvider);
    final pdfGenerationState = ref.watch(pdfGenerationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Lebenslauf-Generator'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          // Auto-Sync Toggle
          Consumer(
            builder: (context, ref, child) {
              final autoSync = ref.watch(pdfAutoSyncProvider);
              return IconButton(
                onPressed: () => _toggleAutoSync(ref),
                icon: Icon(
                  autoSync.isAutoSyncEnabled ? Icons.sync : Icons.sync_disabled,
                  color:
                      autoSync.isAutoSyncEnabled
                          ? Theme.of(context).colorScheme.primary
                          : null,
                ),
                tooltip:
                    autoSync.isAutoSyncEnabled
                        ? 'Auto-Sync deaktivieren'
                        : 'Auto-Sync aktivieren',
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'Kostenlos (${freeTemplates.length})',
              icon: const Icon(Icons.free_breakfast),
            ),
            Tab(
              text: 'Premium (${premiumTemplates.length})',
              icon: const Icon(Icons.star),
            ),
          ],
        ),
      ),
      body: userProfile.when(
        data:
            (profile) => _buildMainContent(
              profile,
              freeTemplates,
              premiumTemplates,
              selectedTemplate,
              pdfGenerationState,
            ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildNetworkErrorState(error.toString()),
      ),

      // Bottom Action Bar
      bottomNavigationBar:
          selectedTemplate != null
              ? Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: Row(
                    children: [
                      // Template Info
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Ausgewählt:',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            Text(
                              selectedTemplate.name,
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),

                      // PDF Generieren Button
                      ElevatedButton.icon(
                        onPressed:
                            pdfGenerationState.isLoading ? null : _generatePdf,
                        icon:
                            pdfGenerationState.isLoading
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.picture_as_pdf),
                        label: Text(
                          pdfGenerationState.isLoading
                              ? 'Generiere...'
                              : 'PDF erstellen',
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              : null,
    );
  }

  /// Erstellt den Hauptinhalt
  Widget _buildMainContent(
    dynamic profile,
    List<CvTemplate> freeTemplates,
    List<CvTemplate> premiumTemplates,
    CvTemplate? selectedTemplate,
    AsyncValue pdfGenerationState,
  ) {
    // Prüfe ob Profildaten vorhanden sind
    final hasBasicProfile =
        profile.name != null &&
        profile.email != null &&
        (profile.workExperience?.isNotEmpty == true ||
            profile.education?.isNotEmpty == true ||
            profile.skills?.isNotEmpty == true);

    if (!hasBasicProfile) {
      return _buildIncompleteProfileState();
    }

    return Column(
      children: [
        // Info-Banner
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Row(
            children: [
              Icon(
                Icons.picture_as_pdf,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Professionelle Lebensläufe erstellen',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Wähle eine Vorlage und erstelle automatisch einen PDF-Lebenslauf mit deinen Profildaten.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Aktuell ausgewählte Vorlage
        if (selectedTemplate != null) ...[
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: selectedTemplate.colorScheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Ausgewählte Vorlage:',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        selectedTemplate.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                if (selectedTemplate.isPremium)
                  Icon(Icons.star, color: Colors.amber[600], size: 20),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () => _showTemplatePreview(selectedTemplate),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('Vorschau'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        // Template-Liste
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Kostenlose Templates
              _buildTemplateGrid(freeTemplates, 'free'),

              // Premium Templates
              _buildTemplateGrid(premiumTemplates, 'premium'),
            ],
          ),
        ),
      ],
    );
  }

  /// Erstellt Template-Grid
  Widget _buildTemplateGrid(List<CvTemplate> templates, String category) {
    if (templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              category == 'premium'
                  ? Icons.star_outline
                  : Icons.free_breakfast_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              category == 'premium'
                  ? 'Keine Premium-Vorlagen verfügbar'
                  : 'Keine kostenlosen Vorlagen verfügbar',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        final isSelected =
            ref.watch(selectedTemplateProvider)?.id == template.id;
        final isAvailable = ref.watch(templateAvailabilityProvider(template));

        return CvTemplateCard(
          template: template,
          isSelected: isSelected,
          isAvailable: isAvailable,
          onTap: () => _selectTemplate(template),
          onPreview: () => _showTemplatePreview(template),
          onCustomize:
              template.isCustomizable && isAvailable
                  ? () => _showTemplateEditor(template)
                  : null,
        );
      },
    );
  }

  /// Erstellt Zustand für unvollständiges Profil
  Widget _buildIncompleteProfileState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_outline,
              size: 80,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 24),
            Text(
              'Profil vervollständigen',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Um einen Lebenslauf zu erstellen, benötigst du mindestens:\n\n'
              '• Name und E-Mail-Adresse\n'
              '• Berufserfahrung, Ausbildung oder Fähigkeiten\n\n'
              'Vervollständige dein Profil, um fortzufahren.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => context.go('/profile'),
              icon: const Icon(Icons.edit),
              label: const Text('Profil bearbeiten'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Erstellt Error-State
  /// Erstellt Netzwerk-Error-State mit Resilience
  Widget _buildNetworkErrorState(String error) {
    final isNetworkError =
        error.contains('SocketException') ||
        error.contains('ClientException') ||
        error.contains('Failed host lookup');

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isNetworkError ? Icons.wifi_off : Icons.error_outline,
              size: 80,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 24),
            Text(
              isNetworkError ? 'Netzwerkfehler' : 'Fehler beim Laden',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              isNetworkError
                  ? 'Keine Internetverbindung verfügbar.\nBitte überprüfe deine Netzwerkverbindung.'
                  : 'Ein Fehler ist aufgetreten:\n$error',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () => ref.refresh(userProfileProvider),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Erneut versuchen'),
                ),
                if (isNetworkError) ...[
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    onPressed: () => _tryOfflineMode(),
                    icon: const Icon(Icons.offline_bolt),
                    label: const Text('Offline-Modus'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Versucht Offline-Modus zu aktivieren
  void _tryOfflineMode() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Offline-Modus wird aktiviert...'),
        duration: Duration(seconds: 2),
      ),
    );

    // Versuche Cache-Daten zu laden
    Future.delayed(const Duration(seconds: 1), () {
      ref.refresh(userProfileProvider);
    });
  }

  /// Togglet Auto-Sync Ein/Aus
  void _toggleAutoSync(WidgetRef ref) {
    final autoSync = ref.read(pdfAutoSyncProvider);
    final newState = !autoSync.isAutoSyncEnabled;

    autoSync.setAutoSyncEnabled(newState);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          newState
              ? 'Auto-Sync aktiviert - PDF wird automatisch bei Profil-Änderungen aktualisiert'
              : 'Auto-Sync deaktiviert',
        ),
        duration: const Duration(seconds: 3),
        action:
            newState
                ? SnackBarAction(
                  label: 'Jetzt synchronisieren',
                  onPressed: () => autoSync.forceSyncNow(),
                )
                : null,
      ),
    );
  }

  /// Wählt Template aus
  void _selectTemplate(CvTemplate template) {
    final isAvailable = ref.read(templateAvailabilityProvider(template));

    if (!isAvailable) {
      _showPremiumRequiredDialog(template);
      return;
    }

    ref.read(selectedTemplateProvider.notifier).selectTemplate(template);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Vorlage "${template.name}" ausgewählt'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(label: 'PDF erstellen', onPressed: _generatePdf),
      ),
    );
  }

  /// Zeigt Template-Vorschau
  void _showTemplatePreview(CvTemplate template) {
    showDialog(
      context: context,
      builder: (context) => CvTemplatePreviewDialog(template: template),
    );
  }

  /// Zeigt Template-Editor
  void _showTemplateEditor(CvTemplate template) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CvTemplateEditorScreen(template: template),
      ),
    );
  }

  /// Zeigt Premium-Required Dialog
  void _showPremiumRequiredDialog(CvTemplate template) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Premium erforderlich'),
            content: Text(
              'Die Vorlage "${template.name}" ist nur für Premium-Nutzer verfügbar. '
              'Upgrade auf Premium, um Zugang zu allen Vorlagen zu erhalten.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Abbrechen'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.go('/premium-management');
                },
                child: const Text('Upgrade'),
              ),
            ],
          ),
    );
  }

  /// Generiert PDF
  Future<void> _generatePdf() async {
    try {
      await ref.read(pdfGenerationProvider.notifier).generatePdf();

      final pdfState = ref.read(pdfGenerationProvider);
      pdfState.when(
        data: (pdfBytes) {
          if (pdfBytes != null) {
            _showPdfGeneratedDialog();
          }
        },
        loading: () {},
        error: (error, stack) {
          _showErrorDialog('Fehler bei PDF-Generierung: $error');
        },
      );
    } catch (e) {
      _showErrorDialog('Unerwarteter Fehler: $e');
    }
  }

  /// Zeigt PDF-Generated Dialog
  void _showPdfGeneratedDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('PDF erstellt'),
            content: const Text(
              'Dein Lebenslauf wurde erfolgreich als PDF erstellt.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _savePdfLocally();
                },
                icon: const Icon(Icons.download),
                label: const Text('Speichern'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _sharePdf();
                },
                icon: const Icon(Icons.share),
                label: const Text('Teilen'),
              ),
            ],
          ),
    );
  }

  /// Speichert PDF lokal
  Future<void> _savePdfLocally() async {
    try {
      final fileName = ref.read(pdfFileNameProvider);
      final filePath = await ref
          .read(pdfGenerationProvider.notifier)
          .savePdfLocally(fileName);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF gespeichert: $filePath'),
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'OK',
              onPressed:
                  () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
            ),
          ),
        );
      }
    } catch (e) {
      _showErrorDialog('Fehler beim Speichern: $e');
    }
  }

  /// Teilt PDF
  Future<void> _sharePdf() async {
    try {
      final fileName = ref.read(pdfFileNameProvider);
      await ref.read(pdfGenerationProvider.notifier).sharePdf(fileName);
    } catch (e) {
      _showErrorDialog('Fehler beim Teilen: $e');
    }
  }

  /// Zeigt Fehler-Dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Fehler'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../domain/models/cv_template.dart';
import '../../../application/providers/cv_template_provider.dart';
import '../../../application/providers/pdf_cv_generator_provider.dart';
import '../widgets/cv_template_card.dart';
import '../widgets/cv_template_preview_dialog.dart';
import 'cv_template_editor_screen.dart';

/// Screen für CV-Template-Auswahl
class CvTemplateSelectionScreen extends ConsumerStatefulWidget {
  const CvTemplateSelectionScreen({super.key});

  @override
  ConsumerState<CvTemplateSelectionScreen> createState() =>
      _CvTemplateSelectionScreenState();
}

class _CvTemplateSelectionScreenState
    extends ConsumerState<CvTemplateSelectionScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final availableTemplates = ref.watch(availableTemplatesProvider);
    final freeTemplates = ref.watch(freeTemplatesProvider);
    final premiumTemplates = ref.watch(premiumTemplatesProvider);
    final selectedTemplate = ref.watch(selectedTemplateProvider);
    final pdfGenerationState = ref.watch(pdfGenerationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Lebenslauf-Vorlage wählen'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'Kostenlos (${freeTemplates.length})',
              icon: const Icon(Icons.free_breakfast),
            ),
            Tab(
              text: 'Premium (${premiumTemplates.length})',
              icon: const Icon(Icons.star),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Info-Banner
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.primaryContainer,
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Wähle eine Vorlage für deinen Lebenslauf. Du kannst jederzeit zwischen den Vorlagen wechseln.',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Template-Liste
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Kostenlose Templates
                _buildTemplateGrid(freeTemplates, 'free'),

                // Premium Templates
                _buildTemplateGrid(premiumTemplates, 'premium'),
              ],
            ),
          ),
        ],
      ),

      // Bottom Action Bar
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Ausgewähltes Template Info
            if (selectedTemplate != null) ...[
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ausgewählt:',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      selectedTemplate.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
            ],

            // Vorschau Button
            OutlinedButton.icon(
              onPressed:
                  selectedTemplate != null
                      ? () => _showTemplatePreview(selectedTemplate)
                      : null,
              icon: const Icon(Icons.preview),
              label: const Text('Vorschau'),
            ),
            const SizedBox(width: 12),

            // PDF Generieren Button
            ElevatedButton.icon(
              onPressed:
                  selectedTemplate != null && !pdfGenerationState.isLoading
                      ? _generatePdf
                      : null,
              icon:
                  pdfGenerationState.isLoading
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.picture_as_pdf),
              label: Text(
                pdfGenerationState.isLoading ? 'Generiere...' : 'PDF erstellen',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Erstellt Template-Grid
  Widget _buildTemplateGrid(List<CvTemplate> templates, String category) {
    if (templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              category == 'premium'
                  ? Icons.star_outline
                  : Icons.free_breakfast_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              category == 'premium'
                  ? 'Keine Premium-Vorlagen verfügbar'
                  : 'Keine kostenlosen Vorlagen verfügbar',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        final isSelected =
            ref.watch(selectedTemplateProvider)?.id == template.id;
        final isAvailable = ref.watch(templateAvailabilityProvider(template));

        return CvTemplateCard(
          template: template,
          isSelected: isSelected,
          isAvailable: isAvailable,
          onTap: () => _selectTemplate(template),
          onPreview: () => _showTemplatePreview(template),
          onCustomize:
              template.isCustomizable && isAvailable
                  ? () => _showTemplateEditor(template)
                  : null,
        );
      },
    );
  }

  /// Wählt Template aus
  void _selectTemplate(CvTemplate template) {
    final isAvailable = ref.read(templateAvailabilityProvider(template));

    if (!isAvailable) {
      _showPremiumRequiredDialog(template);
      return;
    }

    ref.read(selectedTemplateProvider.notifier).selectTemplate(template);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Vorlage "${template.name}" ausgewählt'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Zeigt Template-Vorschau
  void _showTemplatePreview(CvTemplate template) {
    showDialog(
      context: context,
      builder: (context) => CvTemplatePreviewDialog(template: template),
    );
  }

  /// Zeigt Template-Editor
  void _showTemplateEditor(CvTemplate template) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CvTemplateEditorScreen(template: template),
      ),
    );
  }

  /// Zeigt Premium-Required Dialog
  void _showPremiumRequiredDialog(CvTemplate template) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Premium erforderlich'),
            content: Text(
              'Die Vorlage "${template.name}" ist nur für Premium-Nutzer verfügbar. '
              'Upgrade auf Premium, um Zugang zu allen Vorlagen zu erhalten.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Abbrechen'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Navigation zu Premium-Upgrade
                },
                child: const Text('Upgrade'),
              ),
            ],
          ),
    );
  }

  /// Generiert PDF
  Future<void> _generatePdf() async {
    try {
      await ref.read(pdfGenerationProvider.notifier).generatePdf();

      final pdfState = ref.read(pdfGenerationProvider);
      pdfState.when(
        data: (pdfBytes) {
          if (pdfBytes != null) {
            _showPdfGeneratedDialog();
          }
        },
        loading: () {},
        error: (error, stack) {
          _showErrorDialog('Fehler bei PDF-Generierung: $error');
        },
      );
    } catch (e) {
      _showErrorDialog('Unerwarteter Fehler: $e');
    }
  }

  /// Zeigt PDF-Generated Dialog
  void _showPdfGeneratedDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('PDF erstellt'),
            content: const Text(
              'Dein Lebenslauf wurde erfolgreich als PDF erstellt.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _sharePdf();
                },
                child: const Text('Teilen'),
              ),
            ],
          ),
    );
  }

  /// Teilt PDF
  Future<void> _sharePdf() async {
    try {
      final fileName = ref.read(pdfFileNameProvider);
      await ref.read(pdfGenerationProvider.notifier).sharePdf(fileName);
    } catch (e) {
      _showErrorDialog('Fehler beim Teilen: $e');
    }
  }

  /// Zeigt Fehler-Dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Fehler'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}

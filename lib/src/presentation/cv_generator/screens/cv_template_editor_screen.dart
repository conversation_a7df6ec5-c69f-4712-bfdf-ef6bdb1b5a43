import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../domain/models/cv_template.dart';
import '../../../application/providers/cv_template_provider.dart';
import '../widgets/cv_template_preview_dialog.dart';

/// Screen für Template-Anpassungen
class CvTemplateEditorScreen extends ConsumerStatefulWidget {
  final CvTemplate template;

  const CvTemplateEditorScreen({
    super.key,
    required this.template,
  });

  @override
  ConsumerState<CvTemplateEditorScreen> createState() =>
      _CvTemplateEditorScreenState();
}

class _CvTemplateEditorScreenState
    extends ConsumerState<CvTemplateEditorScreen> {
  late CvTemplate _editedTemplate;
  late CvTemplateCustomization _customization;

  @override
  void initState() {
    super.initState();
    _editedTemplate = widget.template;
    _customization = widget.template.customization ?? 
        const CvTemplateCustomization();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.template.name} anpassen'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          // Vorschau-Button
          IconButton(
            onPressed: _showPreview,
            icon: const Icon(Icons.visibility),
            tooltip: 'Vorschau',
          ),
          // Speichern-Button
          IconButton(
            onPressed: _saveCustomization,
            icon: const Icon(Icons.save),
            tooltip: 'Speichern',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Template-Info
            _buildTemplateInfo(),
            const SizedBox(height: 24),

            // Farbschema-Anpassung
            _buildColorSchemeSection(),
            const SizedBox(height: 24),

            // Schriftgrößen-Anpassung
            _buildFontSizeSection(),
            const SizedBox(height: 24),

            // Layout-Optionen
            _buildLayoutOptionsSection(),
            const SizedBox(height: 24),

            // Abstände-Anpassung
            _buildSpacingSection(),
            const SizedBox(height: 32),

            // Aktions-Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Template-Info-Bereich
  Widget _buildTemplateInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: widget.template.colorScheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.template.name,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                if (widget.template.isPremium) ...[
                  const SizedBox(width: 8),
                  Icon(Icons.star, color: Colors.amber[600], size: 20),
                ],
              ],
            ),
            const SizedBox(height: 8),
            Text(
              widget.template.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Farbschema-Anpassung
  Widget _buildColorSchemeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Farbschema',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CvColorScheme.values.map((colorScheme) {
                final isSelected = (_customization.customColorScheme ?? 
                    widget.template.colorScheme) == colorScheme;
                
                return GestureDetector(
                  onTap: () => _updateColorScheme(colorScheme),
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: colorScheme.primaryColor,
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected
                          ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 3,
                            )
                          : null,
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                          )
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// Schriftgrößen-Anpassung
  Widget _buildFontSizeSection() {
    final currentFontSizes = _customization.customFontSizes ?? 
        Map<String, int>.from(widget.template.layoutConfig['fontSize'] ?? {});

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Schriftgrößen',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...currentFontSizes.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getFontSizeLabel(entry.key),
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    Slider(
                      value: entry.value.toDouble(),
                      min: 8,
                      max: 48,
                      divisions: 40,
                      label: '${entry.value}pt',
                      onChanged: (value) => _updateFontSize(entry.key, value.round()),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// Layout-Optionen
  Widget _buildLayoutOptionsSection() {
    final layoutOptions = _customization.customLayoutOptions ?? {};
    final availableOptions = _getAvailableLayoutOptions();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Layout-Optionen',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...availableOptions.map((option) {
              final isEnabled = layoutOptions[option] ?? 
                  (widget.template.layoutConfig[option] as bool? ?? false);
              
              return SwitchListTile(
                title: Text(_getLayoutOptionLabel(option)),
                subtitle: Text(_getLayoutOptionDescription(option)),
                value: isEnabled,
                onChanged: (value) => _updateLayoutOption(option, value),
                contentPadding: EdgeInsets.zero,
              );
            }),
          ],
        ),
      ),
    );
  }

  /// Abstände-Anpassung
  Widget _buildSpacingSection() {
    final currentSpacing = _customization.customSectionSpacing ?? 
        (widget.template.layoutConfig['sectionSpacing'] as int?)?.toDouble() ?? 20.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Abschnitt-Abstände',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'Abstand zwischen Abschnitten: ${currentSpacing.round()}px',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Slider(
              value: currentSpacing,
              min: 10,
              max: 50,
              divisions: 40,
              label: '${currentSpacing.round()}px',
              onChanged: _updateSectionSpacing,
            ),
          ],
        ),
      ),
    );
  }

  /// Aktions-Buttons
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _resetToDefault,
            icon: const Icon(Icons.refresh),
            label: const Text('Zurücksetzen'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _saveCustomization,
            icon: const Icon(Icons.save),
            label: const Text('Speichern'),
          ),
        ),
      ],
    );
  }

  /// Aktualisiert das Farbschema
  void _updateColorScheme(CvColorScheme colorScheme) {
    setState(() {
      _customization = _customization.copyWith(
        customColorScheme: colorScheme,
      );
      _editedTemplate = _editedTemplate.copyWith(
        colorScheme: colorScheme,
        customization: _customization,
      );
    });
  }

  /// Aktualisiert eine Schriftgröße
  void _updateFontSize(String key, int size) {
    final currentFontSizes = Map<String, int>.from(
      _customization.customFontSizes ?? 
      widget.template.layoutConfig['fontSize'] ?? {},
    );
    currentFontSizes[key] = size;

    setState(() {
      _customization = _customization.copyWith(
        customFontSizes: currentFontSizes,
      );
      _editedTemplate = _editedTemplate.copyWith(
        customization: _customization,
      );
    });
  }

  /// Aktualisiert eine Layout-Option
  void _updateLayoutOption(String option, bool value) {
    final currentOptions = Map<String, bool>.from(
      _customization.customLayoutOptions ?? {},
    );
    currentOptions[option] = value;

    setState(() {
      _customization = _customization.copyWith(
        customLayoutOptions: currentOptions,
      );
      _editedTemplate = _editedTemplate.copyWith(
        customization: _customization,
      );
    });
  }

  /// Aktualisiert den Abschnitt-Abstand
  void _updateSectionSpacing(double spacing) {
    setState(() {
      _customization = _customization.copyWith(
        customSectionSpacing: spacing,
      );
      _editedTemplate = _editedTemplate.copyWith(
        customization: _customization,
      );
    });
  }

  /// Zeigt Vorschau des angepassten Templates
  void _showPreview() {
    showDialog(
      context: context,
      builder: (context) => CvTemplatePreviewDialog(template: _editedTemplate),
    );
  }

  /// Speichert die Anpassungen
  void _saveCustomization() {
    ref.read(selectedTemplateProvider.notifier).selectTemplate(_editedTemplate);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Template-Anpassungen gespeichert'),
        duration: Duration(seconds: 2),
      ),
    );
    
    Navigator.of(context).pop();
  }

  /// Setzt Template auf Standard zurück
  void _resetToDefault() {
    setState(() {
      _customization = const CvTemplateCustomization();
      _editedTemplate = widget.template.copyWith(
        customization: null,
      );
    });
  }

  /// Hilfsmethoden
  String _getFontSizeLabel(String key) {
    switch (key) {
      case 'header': return 'Überschrift';
      case 'section': return 'Abschnitt';
      case 'body': return 'Fließtext';
      default: return key;
    }
  }

  List<String> _getAvailableLayoutOptions() {
    final config = widget.template.layoutConfig;
    return config.keys
        .where((key) => config[key] is bool)
        .toList();
  }

  String _getLayoutOptionLabel(String option) {
    switch (option) {
      case 'skillCharts': return 'Skill-Charts';
      case 'progressBars': return 'Progress-Bars';
      case 'creativeElements': return 'Kreative Elemente';
      case 'boldAccents': return 'Fette Akzente';
      case 'modernSpacing': return 'Moderner Abstand';
      default: return option;
    }
  }

  String _getLayoutOptionDescription(String option) {
    switch (option) {
      case 'skillCharts': return 'Zeigt Skills als visuelle Charts';
      case 'progressBars': return 'Verwendet Progress-Bars für Skills';
      case 'creativeElements': return 'Fügt kreative Design-Elemente hinzu';
      case 'boldAccents': return 'Verwendet fette Akzent-Farben';
      case 'modernSpacing': return 'Verwendet moderne Abstände';
      default: return 'Layout-Option aktivieren/deaktivieren';
    }
  }
}

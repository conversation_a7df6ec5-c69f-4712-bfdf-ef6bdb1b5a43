import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/extensions/flutter_extensions.dart';

/// Screen für das Setzen eines neuen Passworts nach Passwort-Reset
class ResetPasswordScreen extends HookConsumerWidget {
  final String? code;
  final String? errorMessage;

  const ResetPasswordScreen({
    super.key,
    this.code,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Extract parameters from URL if not provided directly
    final uri = GoRouterState.of(context).uri;
    final urlToken = uri.queryParameters['token'];
    final urlError = uri.queryParameters['error'];
    final urlEmail = uri.queryParameters['email']; // Extract email from URL if available
    final finalToken = code ?? urlToken;
    final finalErrorMessage = errorMessage ?? urlError;

    final formKey = GlobalKey<FormState>();
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final isNewPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isSubmitting = useState(false);
    final errorState = useState<String?>(finalErrorMessage);
    final supabase = Supabase.instance.client;
    final sessionCreated = useState(false);

    // Prüfe automatisch erstellte Session von Supabase
    useEffect(() {
      Future<void> checkSession() async {
        try {
          final currentSession = supabase.auth.currentSession;
          if (currentSession != null) {
            sessionCreated.value = true;
            errorState.value = null;
          } else if (finalErrorMessage != null) {
            errorState.value = finalErrorMessage;
            sessionCreated.value = false;
          }
        } catch (e) {
          errorState.value = 'Fehler beim Verarbeiten des Reset-Links. Bitte versuchen Sie es erneut.';
          sessionCreated.value = false;
        }
      }

      checkSession();
      return null;
    }, [finalToken, finalErrorMessage]);

    Future<void> resetPassword() async {
      if (!formKey.currentState!.validate()) return;

      isSubmitting.value = true;
      errorState.value = null;

      try {
        // CRITICAL SECURITY: Verify session exists before password update
        final currentSession = supabase.auth.currentSession;
        if (currentSession == null) {
          throw Exception('Keine gültige Session für Passwort-Reset gefunden');
        }

        // CRITICAL SECURITY: Verify session is not expired
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        if (currentSession.expiresAt != null && currentSession.expiresAt! <= now) {
          throw Exception('Session ist abgelaufen - bitte fordern Sie einen neuen Reset-Link an');
        }

        // Password already validated by form validator, but double-check for security
        final password = newPasswordController.text;
        if (password.length < 8) {
          throw Exception('Passwort muss mindestens 8 Zeichen lang sein');
        }

        // Update password with validated session
        await supabase.auth.updateUser(
          UserAttributes(password: password),
        );

        // CRITICAL SECURITY: Force sign out after password change
        await supabase.auth.signOut();

        if (context.mounted) {
          context.showSuccessSnackBar(
            'Passwort erfolgreich geändert! Sie können sich jetzt mit dem neuen Passwort anmelden.',
          );

          // Zur Login-Seite weiterleiten
          context.go('/login');
        }
      } on AuthException catch (e) {
        if (e.message.contains('session_not_found') ||
            e.message.contains('invalid') ||
            e.message.contains('expired') ||
            e.message.contains('Auth session missing')) {
          errorState.value = 'Der Passwort-Reset-Link ist abgelaufen oder ungültig. Bitte fordern Sie einen neuen Link an.';
        } else if (e.message.contains('password')) {
          errorState.value = 'Das Passwort entspricht nicht den Sicherheitsanforderungen.';
        } else {
          errorState.value = 'Fehler beim Ändern des Passworts. Bitte versuchen Sie es erneut.';
        }
      } catch (e) {

        String userFriendlyError = 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';

        if (e.toString().contains('Session')) {
          userFriendlyError = 'Der Reset-Link ist ungültig oder abgelaufen. Bitte fordern Sie einen neuen Link an.';
        } else if (e.toString().contains('Passwort')) {
          userFriendlyError = e.toString().replaceAll('Exception: ', '');
        }

        errorState.value = userFriendlyError;
      } finally {
        if (context.mounted) {
          isSubmitting.value = false;
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Neues Passwort setzen'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Info-Text
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                margin: const EdgeInsets.only(bottom: AppTheme.spacingLarge),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: AppTheme.spacingSmall),
                    const Expanded(
                      child: Text(
                        'Geben Sie Ihr neues Passwort ein. Nach dem Speichern können Sie sich mit dem neuen Passwort anmelden.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),

              // Fehleranzeige
              if (errorState.value != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          errorState.value!,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Neues Passwort
              TextFormField(
                controller: newPasswordController,
                obscureText: !isNewPasswordVisible.value,
                decoration: InputDecoration(
                  labelText: 'Neues Passwort',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isNewPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () => isNewPasswordVisible.value = !isNewPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Bitte geben Sie ein neues Passwort ein';
                  }
                  if (value.length < 8) {
                    return 'Das Passwort muss mindestens 8 Zeichen lang sein';
                  }
                  if (!RegExp(r'[A-Z]').hasMatch(value)) {
                    return 'Das Passwort muss mindestens einen Großbuchstaben enthalten';
                  }
                  if (!RegExp(r'[0-9]').hasMatch(value)) {
                    return 'Das Passwort muss mindestens eine Zahl enthalten';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingMedium),

              // Neues Passwort bestätigen
              TextFormField(
                controller: confirmPasswordController,
                obscureText: !isConfirmPasswordVisible.value,
                decoration: InputDecoration(
                  labelText: 'Passwort bestätigen',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isConfirmPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () => isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Bitte bestätigen Sie das neue Passwort';
                  }
                  if (value != newPasswordController.text) {
                    return 'Die Passwörter stimmen nicht überein';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingXLarge),

              // Speicherbutton
              ElevatedButton(
                onPressed: isSubmitting.value ? null : resetPassword,
                child: isSubmitting.value
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(color: Colors.white),
                      )
                    : const Text('Passwort speichern'),
              ),

              const SizedBox(height: AppTheme.spacingMedium),

              // Link zu "Neuen Reset-Link anfordern"
              TextButton(
                onPressed: () {
                  context.go('/forgot-password');
                },
                child: const Text('Neuen Reset-Link anfordern'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

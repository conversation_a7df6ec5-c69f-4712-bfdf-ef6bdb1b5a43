import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';



/// Screen der den Deep Link für Passwort-Reset verarbeitet
class PasswordResetCallbackScreen extends HookConsumerWidget {
  const PasswordResetCallbackScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isProcessing = useState(true);
    final errorMessage = useState<String?>(null);

    useEffect(() {
      // Warte auf das passwordRecovery Event vom Auth State Change Listener
      // Dieser Screen wird nur noch als Fallback verwendet
      Future.delayed(const Duration(seconds: 3), () {
        if (context.mounted && isProcessing.value) {
          // Wenn nach 3 Sekunden nichts passiert ist, navigiere zur Login-Seite
          errorMessage.value = 'Passwort-Reset konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.';
          isProcessing.value = false;
        }
      });
      return null;
    }, []);

    return Scaffold(
      appBar: AppBar(
         title: const Text('Passwort-Reset'),
       ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
             if (isProcessing.value) ...[
               const CircularProgressIndicator(),
               const SizedBox(height: 16),
               Text(
                 'Passwort-Reset wird verarbeitet...',
                 style: Theme.of(context).textTheme.bodyLarge,
               ),
             ] else if (errorMessage.value != null) ...[
               Icon(
                 Icons.error_outline,
                 size: 64,
                 color: Theme.of(context).colorScheme.error,
               ),
               const SizedBox(height: 16),
               Text(
                 errorMessage.value!,
                 style: TextStyle(
                   color: Theme.of(context).colorScheme.error,
                   fontSize: 16,
                 ),
                 textAlign: TextAlign.center,
               ),
               const SizedBox(height: 24),
               ElevatedButton(
                 onPressed: () => context.go('/login'),
                 child: const Text('Zur Anmeldung'),
               ),
             ],
          ],
        ),
      ),
    );
  }
}

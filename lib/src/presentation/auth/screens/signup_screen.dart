import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/l10n/app_localizations.dart';

// Datenklasse für Registrierungs-Anmeldedaten
class SignupCredentials {
  final String name;
  final String email;
  final String password;

  SignupCredentials({
    required this.name,
    required this.email,
    required this.password,
  });
}

// Provider für Ladezustand
final isSubmittingSignupProvider = StateProvider<bool>((ref) => false);

class SignupScreen extends HookConsumerWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());

    final isPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isSubmitting = ref.watch(isSubmittingSignupProvider);

    // --- KEINE Firebase Auth Instanz mehr nötig ---

    // --- Supabase Sign Up Logik (Innerhalb der build-Methode) ---
    Future<void> signUp() async {
      if (!formKey.currentState!.validate()) return;

      if (passwordController.text != confirmPasswordController.text) {
        showErrorSnackBar(context, 'Die Passwörter stimmen nicht überein.');
        return;
      }

      ref.read(isSubmittingSignupProvider.notifier).state =
          true; // Ladezustand setzen
      final supabase = Supabase.instance.client;

      try {
        final email = emailController.text.trim();
        final password = passwordController.text.trim();

        // Erst prüfen, ob der Benutzer bereits existiert über Edge Function
        debugPrint("[SignupScreen] Prüfe, ob E-Mail bereits existiert: $email");

        try {
          // Edge Function aufrufen, die prüft, ob E-Mail existiert
          final response = await supabase.functions.invoke(
            'check-email-exists',
            body: {'email': email},
          );

          if (response.data != null && response.data['exists'] == true) {
            // E-Mail existiert bereits
            debugPrint("[SignupScreen] E-Mail existiert bereits laut Server");
            if (!context.mounted) return;
            await showInfoDialog(
              context,
              'E-Mail bereits registriert',
              'Diese E-Mail-Adresse ist bereits registriert. Bitte melde dich mit deinem bestehenden Konto an oder verwende "Passwort vergessen", falls du dein Passwort nicht mehr weißt.',
            );
            if (context.mounted) {
              context.go('/login');
            }
            return;
          }

          debugPrint("[SignupScreen] E-Mail existiert nicht laut Server");
        } catch (functionError) {
          debugPrint("[SignupScreen] Fehler bei E-Mail-Prüfung: $functionError");
          // Bei Fehler trotzdem mit Registrierung fortfahren
        }

        // E-Mail existiert nicht - normale Registrierung durchführen
        debugPrint("[SignupScreen] Führe normale Registrierung durch");
        final AuthResponse res = await supabase.auth.signUp(
          email: email,
          password: password,
          emailRedirectTo: 'com.einsteinai.app://confirm-signup',
        );
        debugPrint(
          "[SignupScreen] Registrierung initiiert für: ${res.user?.email}. Session: ${res.session}",
        );

        if (res.user != null && res.session == null) {
          // Neue Registrierung - E-Mail-Bestätigung erforderlich
          if (!context.mounted) return;
          await showInfoDialog(
            context,
            'Registrierung erfolgreich!',
            'Bitte prüfe dein E-Mail-Postfach und klicke auf den Bestätigungslink, um dein Konto zu aktivieren.',
          );
          if (context.mounted) {
            context.go('/login');
          }
        } else if (res.user != null && res.session != null) {
          debugPrint(
            "[SignupScreen] Registrierung erfolgreich und User direkt eingeloggt.",
          );
          // Direkt zur Hauptseite navigieren wenn bereits eingeloggt
          if (context.mounted) {
            context.go('/');
          }
        } else {
          // Unerwarteter Fall - kein User zurückgegeben
          if (!context.mounted) return;
          showErrorSnackBar(context, 'Unerwarteter Fehler bei der Registrierung.');
        }
      } on AuthException catch (e) {
        debugPrint("[SignupScreen] Fehler bei Registrierung: ${e.message}");
        if (!context.mounted) return; // Check mounted state

        // Spezifische Behandlung für bereits existierende E-Mail
        if (e.message.contains('User already registered') ||
            e.message.contains('already registered') ||
            e.message.contains('email already exists')) {
          await showInfoDialog(
            context,
            'E-Mail bereits registriert',
            'Diese E-Mail-Adresse ist bereits registriert. Bitte melde dich mit deinem bestehenden Konto an oder verwende "Passwort vergessen", falls du dein Passwort nicht mehr weißt.',
          );
          if (context.mounted) {
            context.go('/login');
          }
        } else {
          showErrorSnackBar(context, 'Registrierungsfehler: ${e.message}');
        }
      } catch (e) {
        debugPrint("[SignupScreen] Unerwarteter Fehler bei Registrierung: $e");
        if (!context.mounted) return; // Check mounted state
        showErrorSnackBar(context, 'Ein unerwarteter Fehler ist aufgetreten.');
      } finally {
        // Korrekter Zugriff auf ref innerhalb der build-Methode
        ref.read(isSubmittingSignupProvider.notifier).state = false;
      }
    }
    // --- Ende Supabase Sign Up Logik ---

    return Scaffold(
      appBar: AppBar(
        title: const Text('Registrieren'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppTheme.primaryColor,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            child: Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header Text
                  const Text(
                    'Erstelle dein Konto',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  const Text(
                    'Finde mit KI den perfekten Job für dich',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryLightColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXLarge),

                  // Name Feld
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Vollständiger Name',
                      hintText: 'Max Mustermann',
                      prefixIcon: Icon(Icons.person_outline),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte gib deinen Namen ein';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // E-Mail Feld
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'E-Mail',
                      hintText: '<EMAIL>',
                      prefixIcon: Icon(Icons.email_outlined),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte gib deine E-Mail ein';
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'Bitte gib eine gültige E-Mail ein';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // Passwort Feld
                  TextFormField(
                    controller: passwordController,
                    obscureText: !isPasswordVisible.value,
                    decoration: InputDecoration(
                      labelText: 'Passwort',
                      hintText: '••••••••',
                      prefixIcon: const Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isPasswordVisible.value
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: Colors.grey,
                        ),
                        onPressed:
                            () =>
                                isPasswordVisible.value =
                                    !isPasswordVisible.value,
                      ),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte gib ein Passwort ein';
                      }
                      if (value.length < 8) {
                        return 'Das Passwort muss mindestens 8 Zeichen lang sein';
                      }
                      if (!RegExp(r'[A-Z]').hasMatch(value)) {
                        return 'Das Passwort muss mindestens einen Großbuchstaben enthalten';
                      }
                      if (!RegExp(r'[0-9]').hasMatch(value)) {
                        return 'Das Passwort muss mindestens eine Zahl enthalten';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // Passwort-Bestätigung Feld
                  TextFormField(
                    controller: confirmPasswordController,
                    obscureText: !isConfirmPasswordVisible.value,
                    decoration: InputDecoration(
                      labelText: 'Passwort bestätigen',
                      hintText: '••••••••',
                      prefixIcon: const Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isConfirmPasswordVisible.value
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: Colors.grey,
                        ),
                        onPressed:
                            () =>
                                isConfirmPasswordVisible.value =
                                    !isConfirmPasswordVisible.value,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte bestätige dein Passwort';
                      }
                      if (value != passwordController.text) {
                        return 'Die Passwörter stimmen nicht überein';
                      }
                      return null;
                    },
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) {
                      if (formKey.currentState!.validate() && !isSubmitting) {
                        signUp();
                      }
                    },
                  ),

                  const SizedBox(height: AppTheme.spacingXLarge),

                  // Registrieren Button
                  ElevatedButton(
                    onPressed:
                        isSubmitting
                            ? null
                            : () {
                              if (formKey.currentState!.validate()) {
                                signUp();
                              }
                            },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        isSubmitting
                            ? const SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Text('Registrieren'),
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // Datenschutzerklärung und AGB-Hinweis
                  Text(
                    AppLocalizations.of(context).termsAndPrivacyNotice,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryLightColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// --- Hilfsfunktionen für Dialoge und Snackbars (Könnten in eine Utils-Datei) ---
void showErrorSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Theme.of(context).colorScheme.error,
    ),
  );
}

Future<void> showInfoDialog(BuildContext context, String title, String content) async {
  if (!context.mounted) return;
  await showDialog(
    context: context,
    builder:
        (context) => AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).ok),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
  );
}

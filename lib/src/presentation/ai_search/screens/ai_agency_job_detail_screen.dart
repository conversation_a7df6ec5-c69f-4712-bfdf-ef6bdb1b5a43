// Für jsonDecode
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/services.dart';

import '../../../core/theme/app_theme.dart';

class AiAgencyJobDetailScreen extends HookWidget {
  final String jobTitle;
  final String location;
  final String jobId;
  final String? description;

  const AiAgencyJobDetailScreen({
    super.key,
    required this.jobTitle,
    required this.location,
    required this.jobId,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    // State-Variablen mit Hooks
    final isLoadingWebView = useState(true);
    final hasError = useState(false);
    final captchaDetected = useState(false);
    final extractedContent = useState<Map<String, String>?>(null);



    // WebView Controller für die Jobdetails
    late WebViewController controller;
    controller = useMemoized(() {
      return WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.white)
        ..setNavigationDelegate(
          NavigationDelegate(
            onNavigationRequest: (request) {
              // Erlaube Navigation innerhalb der WebView
              return NavigationDecision.navigate;
            },
            onPageStarted: (_) {
              isLoadingWebView.value = true;
              hasError.value = false;
            },
            onPageFinished: (url) {
              isLoadingWebView.value = false;
              // Hervorhebung des externen Links und Navigation History
              controller.runJavaScript('''
                // Hervorhebung des externen Links
                const externalLinks = document.querySelectorAll('a[href*="externe-seite"], a:contains("externe Seite"), button:contains("externe Seite")');
                externalLinks.forEach(link => {
                  link.style.backgroundColor = "#FFEB3B";
                  link.style.border = "2px solid #FFC107";
                  link.style.padding = "8px 16px";
                  link.style.borderRadius = "4px";
                  link.scrollIntoView({behavior: 'smooth'});
                });
              ''');
            },
            onWebResourceError: (error) {
              print('WebView Fehler: ${error.description}');
              isLoadingWebView.value = false;
              hasError.value = true;
            },
          ),
        )
        ..loadRequest(
          Uri.parse('https://www.arbeitsagentur.de/jobsuche/jobdetail/$jobId'),
        );
    }, const []);

    return Scaffold(
      appBar: AppBar(
        title: Text(jobTitle, overflow: TextOverflow.ellipsis),
        leading: Builder(
          builder:
              (context) => IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () async {
                  if (await controller.canGoBack()) {
                    await controller.goBack();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
              ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Neu laden',
            onPressed: () {
              controller.reload();
              extractedContent.value = null;
              captchaDetected.value = false;
            },
          ),
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            tooltip: 'Im Browser öffnen',
            onPressed:
                () => _launchUrl(
                  'https://www.arbeitsagentur.de/jobsuche/jobdetail/$jobId',
                  context,
                ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Gegebene Infos immer anzeigen
          _buildJobInfoCard(context),

          // Extrahierte Beschreibung ODER WebView
          if (extractedContent.value != null)
            Expanded(
              child: _buildJobDetailsCard(
                context,
                extractedContent.value!,
                captchaDetected.value,
              ),
            )
          else
            Expanded(
              child: Stack(
                children: [
                  WebViewWidget(controller: controller),
                  if (isLoadingWebView.value)
                    const Center(child: CircularProgressIndicator()),
                  if (hasError.value)
                    const Center(
                      child: Text(
                        'Fehler beim Laden der Stellendetails',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
      persistentFooterButtons: [
        if (extractedContent.value != null &&
            extractedContent.value!.containsKey('email') &&
            extractedContent.value!['email']!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
            ),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.email),
              label: const Text('Bewerbung per E-Mail senden'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                final email = extractedContent.value!['email'] ?? '';
                if (email.isNotEmpty) {
                  _launchMailto(
                    to: email,
                    subject: 'Bewerbung für $jobTitle',
                    context: context,
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Keine E-Mail-Adresse gefunden'),
                    ),
                  );
                }
              },
            ),
          ),
      ],
    );
  }

  // Zeigt allgemeine Job-Informationen in einem Card-Widget an
  Widget _buildJobInfoCard(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              jobTitle,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Row(
              children: [
                const Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    location,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            if (jobId.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Row(
                children: [
                  const Icon(Icons.numbers, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Job-ID: $jobId',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Zeigt die extrahierten Jobdetails in einem Card-Widget an
  Widget _buildJobDetailsCard(
    BuildContext context,
    Map<String, String> content,
    bool captchaDetected,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMedium),
      elevation: 1,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Warnung wenn keine Beschreibung vorhanden
            if ((!content.containsKey('description') ||
                    content['description']!.isEmpty) &&
                (!content.containsKey('requirements') ||
                    content['requirements']!.isEmpty))
              Container(
                margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                padding: const EdgeInsets.all(AppTheme.spacingSmall),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue.shade300),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade700),
                    const SizedBox(width: AppTheme.spacingSmall),
                    const Expanded(
                      child: Text(
                        'Für diesen Job sind keine Beschreibungen verfügbar. Bitte öffne die externe Seite für weitere Informationen.',
                        style: TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),

            // CAPTCHA-Warnung, falls erkannt
            if (captchaDetected)
              Container(
                margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                padding: const EdgeInsets.all(AppTheme.spacingSmall),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange.shade300),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange.shade700,
                    ),
                    const SizedBox(width: AppTheme.spacingSmall),
                    const Expanded(
                      child: Text(
                        'Ein CAPTCHA wurde erkannt. Möglicherweise werden nicht alle Informationen angezeigt. Bitte löse das CAPTCHA in der WebView und versuche es erneut.',
                        style: TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),

            // Beschreibung
            if (content.containsKey('description') &&
                content['description']!.isNotEmpty) ...[
              Text(
                'Stellenbeschreibung',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              SelectableText(content['description']!),
              const SizedBox(height: AppTheme.spacingMedium),
              const Divider(),
            ],

            // Anforderungen
            if (content.containsKey('requirements') &&
                content['requirements']!.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                'Anforderungen',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              SelectableText(content['requirements']!),
              const SizedBox(height: AppTheme.spacingMedium),
              const Divider(),
            ],

            // Arbeitgeber
            if (content.containsKey('company') &&
                content['company']!.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                'Arbeitgeber',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              SelectableText(content['company']!),
              const SizedBox(height: AppTheme.spacingMedium),
            ],

            // Kontakt
            if (content.containsKey('contact') &&
                content['contact']!.isNotEmpty) ...[
              Text('Kontakt', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: AppTheme.spacingSmall),
              SelectableText(content['contact']!),
              if (content.containsKey('email') &&
                  content['email']!.isNotEmpty) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                InkWell(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: content['email']!));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('E-Mail-Adresse kopiert')),
                    );
                  },
                  child: Row(
                    children: [
                      const Icon(Icons.email, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        content['email']!,
                        style: const TextStyle(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.copy, size: 14, color: Colors.grey),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }



  // Öffnet eine URL im Browser
  Future<void> _launchUrl(String urlString, BuildContext context) async {
    final uri = Uri.parse(urlString);
    try {
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        _showSnackBar(context, 'Konnte URL nicht öffnen: $urlString');
      }
    } catch (e) {
      _showSnackBar(context, 'Fehler beim Öffnen der URL: $e');
    }
  }

  // Öffnet eine E-Mail mit vorausgefüllten Daten
  Future<void> _launchMailto({
    required String to,
    String? subject,
    String? body,
    required BuildContext context,
  }) async {
    String? encodedSubject =
        subject != null ? Uri.encodeComponent(subject) : null;
    String? encodedBody =
        body?.replaceAll(' ', '%20').replaceAll('\n', '%0D%0A');

    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];

    if (encodedSubject != null) {
      queryParts.add('subject=$encodedSubject');
    }
    if (encodedBody != null) {
      queryParts.add('body=$encodedBody');
    }

    if (queryParts.isNotEmpty) {
      mailtoLink += '?${queryParts.join('&')}';
    }

    final Uri mailUri = Uri.parse(mailtoLink);
    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _showSnackBar(context, 'E-Mail-App konnte nicht geöffnet werden');
      }
    } catch (e) {
      _showSnackBar(context, 'Fehler beim Öffnen der E-Mail-App: $e');
    }
  }

  // Zeigt eine Snackbar mit einer Nachricht an
  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/validation/form_validation_framework.dart';

/// Erweiterte TextFormField-Widget mit eingebauter Validation und Error Handling
class ValidatedTextField extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final bool obscureText;
  final bool enabled;
  final int? maxLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final String? helperText;
  final bool showCharacterCount;
  final EdgeInsetsGeometry? contentPadding;

  const ValidatedTextField({
    super.key,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.helperText,
    this.showCharacterCount = false,
    this.contentPadding,
  });

  @override
  State<ValidatedTextField> createState() => _ValidatedTextFieldState();
}

class _ValidatedTextFieldState extends State<ValidatedTextField> {
  bool _hasError = false;
  String? _errorText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: widget.controller,
          decoration: InputDecoration(
            labelText: widget.labelText,
            hintText: widget.hintText,
            prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
            suffixIcon: widget.suffixIcon,
            helperText: widget.helperText,
            counterText: widget.showCharacterCount ? null : '',
            contentPadding: widget.contentPadding,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: _hasError 
                    ? Theme.of(context).colorScheme.error 
                    : Theme.of(context).colorScheme.outline,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: _hasError 
                    ? Theme.of(context).colorScheme.error 
                    : Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 2,
              ),
            ),
          ),
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          obscureText: widget.obscureText,
          enabled: widget.enabled,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          inputFormatters: widget.inputFormatters,
          readOnly: widget.readOnly,
          onTap: widget.onTap,
          onChanged: (value) {
            // Validierung bei jeder Änderung
            if (widget.validator != null) {
              final error = widget.validator!(value);
              setState(() {
                _hasError = error != null;
                _errorText = error;
              });
            }
            widget.onChanged?.call(value);
          },
          validator: (value) {
            final error = widget.validator?.call(value);
            setState(() {
              _hasError = error != null;
              _errorText = error;
            });
            return error;
          },
        ),
        if (_hasError && _errorText != null) ...[
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              _errorText!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Spezialisierte Email TextField
class EmailTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final bool enabled;
  final void Function(String)? onChanged;

  const EmailTextField({
    super.key,
    required this.controller,
    this.labelText = 'E-Mail',
    this.hintText = '<EMAIL>',
    this.enabled = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ValidatedTextField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: Icons.email_outlined,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      enabled: enabled,
      validator: FormValidationFramework.validateEmail,
      onChanged: onChanged,
    );
  }
}

/// Spezialisierte Password TextField
class PasswordTextField extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final bool enabled;
  final void Function(String)? onChanged;
  final bool requireStrong;

  const PasswordTextField({
    super.key,
    required this.controller,
    this.labelText = 'Passwort',
    this.hintText,
    this.enabled = true,
    this.onChanged,
    this.requireStrong = true,
  });

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _isVisible = false;

  @override
  Widget build(BuildContext context) {
    return ValidatedTextField(
      controller: widget.controller,
      labelText: widget.labelText,
      hintText: widget.hintText,
      prefixIcon: Icons.lock_outline,
      suffixIcon: IconButton(
        icon: Icon(_isVisible ? Icons.visibility_off : Icons.visibility),
        onPressed: () {
          setState(() {
            _isVisible = !_isVisible;
          });
        },
      ),
      obscureText: !_isVisible,
      enabled: widget.enabled,
      validator: (value) => FormValidationFramework.validatePassword(
        value,
        requireUppercase: widget.requireStrong,
        requireNumbers: widget.requireStrong,
        requireSpecialChars: false,
      ),
      onChanged: widget.onChanged,
    );
  }
}

/// Spezialisierte Name TextField
class NameTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final bool enabled;
  final void Function(String)? onChanged;

  const NameTextField({
    super.key,
    required this.controller,
    this.labelText = 'Name',
    this.hintText = 'Max Mustermann',
    this.enabled = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ValidatedTextField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: Icons.person_outline,
      textInputAction: TextInputAction.next,
      enabled: enabled,
      validator: FormValidationFramework.validateName,
      onChanged: onChanged,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r"[a-zA-ZäöüÄÖÜß\s\-']")),
      ],
    );
  }
}

/// Spezialisierte Phone TextField
class PhoneTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final bool enabled;
  final bool required;
  final void Function(String)? onChanged;

  const PhoneTextField({
    super.key,
    required this.controller,
    this.labelText = 'Telefonnummer',
    this.hintText = '+49 123 456789',
    this.enabled = true,
    this.required = false,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ValidatedTextField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: Icons.phone_outlined,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      enabled: enabled,
      validator: (value) => FormValidationFramework.validatePhoneNumber(
        value,
        required: required,
      ),
      onChanged: onChanged,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s\-()]')),
      ],
    );
  }
}

/// Loading State für Forms
class FormLoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? loadingText;

  const FormLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withOpacity(0.3),
            child: Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      if (loadingText != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          loadingText!,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

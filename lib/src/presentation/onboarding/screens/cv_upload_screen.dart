import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:file_picker/file_picker.dart';

import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/profile/services/profile_cv_service.dart';

/// CV-Upload Screen der nach dem Onboarding angezeigt wird
class CvUploadScreen extends ConsumerStatefulWidget {
  const CvUploadScreen({super.key});

  @override
  ConsumerState<CvUploadScreen> createState() => _CvUploadScreenState();
}

class _CvUploadScreenState extends ConsumerState<CvUploadScreen>
    with TickerProviderStateMixin {
  bool _isUploading = false;
  String? _uploadedFileName;
  bool _isAnalyzing = false;
  File? _uploadedFile;

  // Animation Controllers
  late AnimationController _scanAnimationController;
  late AnimationController _progressAnimationController;
  late Animation<double> _scanAnimation;
  late Animation<double> _progressAnimation;

  // Fortschritts-Status
  final List<String> _extractionSteps = [
    'Dokument wird gelesen...',
    'Berufserfahrungen werden extrahiert...',
    'Ausbildung wird extrahiert...',
    'Fähigkeiten werden extrahiert...',
    'Persönliche Daten werden extrahiert...',
    'Analyse wird abgeschlossen...',
  ];
  int _currentStep = 0;
  bool _analysisComplete = false;

  @override
  void initState() {
    super.initState();
    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _scanAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _scanAnimationController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundDarkColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.description,
                    color: AppTheme.primaryLightColor,
                    size: 28,
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Text(
                    'Lebenslauf hochladen',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingXLarge),

              // Hauptinhalt
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(60),
                      border: Border.all(
                        color: AppTheme.primaryLightColor.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.upload_file,
                      size: 60,
                      color: AppTheme.primaryLightColor,
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingXLarge),

                  // Titel mit besserem Kontrast
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusMedium,
                      ),
                      border: Border.all(
                        color: AppTheme.primaryLightColor.withOpacity(0.5),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.5),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      'Lade deinen Lebenslauf hoch',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // Erklärung warum wichtig - verbesserte Lesbarkeit
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingLarge),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.85),
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusMedium,
                      ),
                      border: Border.all(
                        color: AppTheme.primaryLightColor.withOpacity(0.6),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.6),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Warum ist das wichtig?',
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),
                        ..._buildBenefitsList(),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingXLarge),

                  // CV-Vorschau und Analyse-Animation
                  if (_uploadedFileName != null && _isAnalyzing)
                    _buildCvAnalysisWidget(),

                  // Upload Status (nur wenn nicht analysiert wird)
                  if (_uploadedFileName != null && !_isAnalyzing)
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMedium),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMedium,
                        ),
                        border: Border.all(
                          color: Colors.green.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 24,
                          ),
                          const SizedBox(width: AppTheme.spacingSmall),
                          Expanded(
                            child: Text(
                              'Datei hochgeladen: $_uploadedFileName',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Upload und Skip Buttons nur anzeigen wenn nicht analysiert wird
                  if (!_isAnalyzing && !_analysisComplete) ...[
                    const SizedBox(height: AppTheme.spacingLarge),

                    // Upload Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isUploading ? null : _uploadCV,
                        icon:
                            _isUploading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : const Icon(Icons.upload_file),
                        label: Text(
                          _isUploading
                              ? 'Wird hochgeladen...'
                              : (_uploadedFileName != null
                                  ? 'Andere Datei wählen'
                                  : 'Lebenslauf hochladen'),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryLightColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            vertical: AppTheme.spacingMedium,
                          ),
                          textStyle: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: AppTheme.spacingMedium),

                    // Überspringen Button mit besserem Kontrast
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMedium,
                        ),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: TextButton(
                        onPressed: _skipUpload,
                        child: const Text(
                          'Später hochladen',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: AppTheme.spacingXLarge),

              // Footer mit Weiter-Button
              if (_analysisComplete)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _continueToApp,
                    icon: const Icon(Icons.arrow_forward),
                    label: const Text('Weiter zur App'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: AppTheme.spacingMedium,
                      ),
                      textStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildBenefitsList() {
    final benefits = [
      {
        'icon': Icons.auto_awesome,
        'text': 'KI analysiert deine Qualifikationen automatisch',
      },
      {'icon': Icons.speed, 'text': 'Bewerbungen werden in Sekunden erstellt'},
      {
        'icon': Icons.trending_up,
        'text': 'Höhere Erfolgschancen durch personalisierte Anschreiben',
      },
    ];

    return benefits
        .map(
          (benefit) => Padding(
            padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
            child: Row(
              children: [
                Icon(
                  benefit['icon'] as IconData,
                  color: AppTheme.primaryLightColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Expanded(
                  child: Text(
                    benefit['text'] as String,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        )
        .toList();
  }

  Future<void> _uploadCV() async {
    setState(() {
      _isUploading = true;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          // Setze den Dateinamen für die UI und starte Animation
          setState(() {
            _uploadedFileName = file.name;
            _uploadedFile = File(filePath);
            _isAnalyzing = true;
            _currentStep = 0;
            _analysisComplete = false;
          });

          // Starte Scan-Animation
          _startAnalysisAnimation();

          // Verwende die echte CV-Upload-Funktion aus dem ProfileCvService
          final isUploadingCv = ValueNotifier<bool>(true);

          try {
            // Rufe die bestehende CV-Upload-Funktion auf
            await ProfileCvService.uploadAndProcessCv(
              context,
              ref,
              File(filePath),
              isUploadingCv,
            );

            // Zeige Erfolgs-Snackbar
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Text(
                        '${file.name} erfolgreich hochgeladen und analysiert!',
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          } catch (uploadError) {
            debugPrint('Fehler bei CV-Upload und Analyse: $uploadError');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Fehler bei der CV-Analyse'),
                    ],
                  ),
                  backgroundColor: Colors.orange,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Fehler beim Datei-Auswahl-Prozess: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Fehler beim Hochladen der Datei'),
              ],
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _isAnalyzing = false;
        });
      }
    }
  }

  void _skipUpload() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: Colors.black.withOpacity(0.95),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              side: BorderSide(
                color: AppTheme.primaryLightColor.withOpacity(0.5),
                width: 2,
              ),
            ),
            title: const Text(
              'Lebenslauf später hochladen?',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            content: const Text(
              'Du kannst deinen Lebenslauf jederzeit in den Einstellungen hochladen. Ohne Lebenslauf kann die KI jedoch keine personalisierten Bewerbungen erstellen.',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.grey.withOpacity(0.3),
                ),
                child: const Text(
                  'Abbrechen',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _continueToApp();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryLightColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text(
                  'Trotzdem fortfahren',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
    );
  }

  void _continueToApp() {
    context.go('/');
  }

  // CV-Analyse-Widget mit Vorschau und Animation
  Widget _buildCvAnalysisWidget() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.9),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: AppTheme.primaryLightColor.withOpacity(0.5),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.6),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Titel
          Text(
            'CV wird analysiert',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),

          // CV-Vorschau mit Scan-Animation
          _buildCvPreviewWithScan(),

          const SizedBox(height: AppTheme.spacingLarge),

          // Fortschritts-Anzeige
          _buildProgressIndicator(),

          if (_analysisComplete) ...[
            const SizedBox(height: AppTheme.spacingLarge),
            _buildCompletionMessage(),
          ],
        ],
      ),
    );
  }

  // CV-Vorschau mit Scan-Animation
  Widget _buildCvPreviewWithScan() {
    return Container(
      width: 200,
      height: 250,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(
          color: AppTheme.primaryLightColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // CV-Inhalt (vereinfacht)
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 8),
                // Linien für Text
                ...List.generate(
                  8,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Container(
                      width: index % 3 == 0 ? 120 : (index % 2 == 0 ? 100 : 80),
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Scan-Linie Animation
          AnimatedBuilder(
            animation: _scanAnimation,
            builder: (context, child) {
              return Positioned(
                top: _scanAnimation.value * 250,
                left: 0,
                right: 0,
                child: Container(
                  height: 3,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        AppTheme.primaryLightColor.withOpacity(0.8),
                        AppTheme.primaryLightColor,
                        AppTheme.primaryLightColor.withOpacity(0.8),
                        Colors.transparent,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryLightColor.withOpacity(0.6),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Dateiname
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _uploadedFileName ?? '',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Fortschritts-Anzeige
  Widget _buildProgressIndicator() {
    return Column(
      children: [
        // Aktueller Schritt
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            border: Border.all(
              color: AppTheme.primaryLightColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryLightColor,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Text(
                  _currentStep < _extractionSteps.length
                      ? _extractionSteps[_currentStep]
                      : 'Analyse abgeschlossen',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        // Fortschrittsbalken
        Container(
          width: double.infinity,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(4),
          ),
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              double progress =
                  (_currentStep + _progressAnimation.value) /
                  _extractionSteps.length;
              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                ),
                child: LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryLightColor,
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: AppTheme.spacingSmall),

        // Prozent-Anzeige
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            double progress =
                (_currentStep + _progressAnimation.value) /
                _extractionSteps.length;
            int percentage = (progress * 100).clamp(0, 100).round();
            return Text(
              '$percentage% abgeschlossen',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            );
          },
        ),
      ],
    );
  }

  // Abschluss-Nachricht
  Widget _buildCompletionMessage() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(color: Colors.green.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 48),
          const SizedBox(height: AppTheme.spacingMedium),
          const Text(
            'Fertig!',
            style: TextStyle(
              color: Colors.green,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          const Text(
            'Dein Lebenslauf wurde erfolgreich analysiert.\nDu kannst jetzt loslegen!',
            style: TextStyle(color: Colors.white, fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _continueToApp,
              icon: const Icon(Icons.rocket_launch),
              label: const Text('Jetzt loslegen!'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingMedium,
                ),
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Analyse-Animation starten
  void _startAnalysisAnimation() {
    _scanAnimationController.repeat();
    _simulateAnalysisProgress();
  }

  // Simuliere Analyse-Fortschritt
  void _simulateAnalysisProgress() async {
    for (int i = 0; i < _extractionSteps.length; i++) {
      if (!mounted || !_isAnalyzing) break;

      setState(() {
        _currentStep = i;
      });

      _progressAnimationController.reset();
      await _progressAnimationController.forward();

      // Warte zwischen den Schritten (15 Sekunden gesamt / 6 Schritte = 2,5 Sekunden pro Schritt)
      await Future.delayed(const Duration(milliseconds: 2500));
    }

    if (mounted && _isAnalyzing) {
      _scanAnimationController.stop();
      setState(() {
        _analysisComplete = true;
      });
    }
  }
}

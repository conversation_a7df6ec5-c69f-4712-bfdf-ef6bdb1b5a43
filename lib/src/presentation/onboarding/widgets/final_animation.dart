import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

/// Ein Widget, das eine hochwertige Animation für die letzte Seite des Onboarding-Screens darstellt
/// Inspiriert von einem Bild mit Schloss/Burg, Mond, Sternen und Heißluftballons
class FinalAnimation extends StatefulWidget {
  final VoidCallback onAnimationComplete;
  final int durationInSeconds;

  const FinalAnimation({
    super.key,
    required this.onAnimationComplete,
    this.durationInSeconds = 18, // Standardmäßig 18 Sekunden
  });

  @override
  State<FinalAnimation> createState() => _FinalAnimationState();
}

class _FinalAnimationState extends State<FinalAnimation>
    with TickerProviderStateMixin {
  // Animation Controller
  late final AnimationController _backgroundController;
  late final AnimationController _moonController;
  late final AnimationController _starsController;
  late final AnimationController _cloudsController;
  late final AnimationController _balloonController;
  late final AnimationController _castleController;
  late final AnimationController _textController;

  // Animationen
  late final Animation<double> _moonPositionAnimation;
  late final Animation<double> _cloudOpeningAnimation;
  late final Animation<double> _castleOpacityAnimation;
  late final Animation<double> _textOpacityAnimation;
  late final Animation<double> _textScaleAnimation;

  // Timer für die Gesamtdauer
  Timer? _completionTimer;
  Timer? _textAnimationTimer; // KRITISCHER FIX: Timer-Referenz speichern
  bool _isAnimating = true;

  // Inspirational texts to show during the animation
  final List<String> _inspirationalTexts = [
    'Willkommen in deiner Zukunft',
    'Deine Reise beginnt jetzt',
    'Entdecke neue Möglichkeiten',
    'Dein Traumjob wartet auf dich',
    'Bereit für den nächsten Schritt',
  ];

  int _currentTextIndex = 0;

  // Zufallsgenerator für Sterne
  final Random _random = Random();

  @override
  void initState() {
    super.initState();

    // Hintergrund-Animation
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );

    // Mond-Animation
    _moonController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    // Sterne-Animation
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );

    // Wolken-Animation
    _cloudsController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    );

    // Heißluftballon-Animation
    _balloonController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 12),
    );

    // Schloss/Burg-Animation
    _castleController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    );

    // Text-Animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    // Mond-Positions-Animation
    _moonPositionAnimation = Tween<double>(begin: -0.1, end: 0.0).animate(
      CurvedAnimation(parent: _moonController, curve: Curves.easeInOut),
    );

    // Wolken-Öffnungs-Animation
    _cloudOpeningAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _cloudsController, curve: Curves.easeOut),
    );

    // Schloss/Burg-Opazitäts-Animation
    _castleOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _castleController, curve: Curves.easeIn));

    // Text-Opazitäts-Animation
    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    // Text-Skalierungs-Animation
    _textScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOutBack),
    );

    // Starte die Animationen
    _startAnimations();

    // Timer für den Abschluss der Animation
    _completionTimer = Timer(Duration(seconds: widget.durationInSeconds), () {
      if (mounted) {
        setState(() {
          _isAnimating = false;
        });
        widget.onAnimationComplete();
      }
    });
  }

  void _startAnimations() {
    // Starte die Hintergrund-Animation
    _backgroundController.repeat(reverse: true);

    // Starte die Mond-Animation
    _moonController.forward();

    // Starte die Sterne-Animation
    _starsController.repeat(reverse: true);

    // Starte die Wolken-Animation
    _cloudsController.forward();

    // Starte die Heißluftballon-Animation
    _balloonController.repeat(reverse: true);

    // Starte die Schloss/Burg-Animation nach einer Verzögerung
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _castleController.forward();
      }
    });

    // Starte die Text-Animation und wechsle den Text alle 3 Sekunden
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _textController.forward();

        // KRITISCHER FIX: Timer-Referenz speichern für ordnungsgemäße Bereinigung
        _textAnimationTimer = Timer.periodic(const Duration(seconds: 3), (
          timer,
        ) {
          if (!mounted || !_isAnimating) {
            timer.cancel();
            _textAnimationTimer = null; // Referenz bereinigen
            return;
          }

          _textController.reset();
          _textController.forward();

          setState(() {
            _currentTextIndex =
                (_currentTextIndex + 1) % _inspirationalTexts.length;
          });
        });
      }
    });
  }

  @override
  void dispose() {
    // KRITISCHER FIX: Alle Timer ordnungsgemäß canceln
    _completionTimer?.cancel();
    _textAnimationTimer?.cancel();

    // Animation Controller dispose
    _backgroundController.dispose();
    _moonController.dispose();
    _starsController.dispose();
    _cloudsController.dispose();
    _balloonController.dispose();
    _castleController.dispose();
    _textController.dispose();

    // State bereinigen
    _isAnimating = false;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Stack(
      fit: StackFit.expand,
      children: [
        // Hintergrund mit Farbverlauf (Nachthimmel mit Sonnenaufgang)
        AnimatedBuilder(
          animation: _backgroundController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF1A1B41), // Dunkles Blau/Violett (oben)
                    const Color(0xFF3A1E54), // Mittleres Violett
                    const Color(0xFF6A0D83), // Dunkles Violett/Magenta
                    const Color(0xFFAA336A), // Magenta/Rosa
                    const Color(
                      0xFFE14594,
                    ), // Helles Rosa (unten, Sonnenaufgang)
                  ],
                  stops: [
                    0.0,
                    0.3,
                    0.5 + (_backgroundController.value * 0.1),
                    0.7 + (_backgroundController.value * 0.05),
                    1.0,
                  ],
                ),
              ),
            );
          },
        ),

        // Sterne
        AnimatedBuilder(
          animation: _starsController,
          builder: (context, child) {
            return CustomPaint(
              painter: StarsPainter(
                opacity: 0.5 + (_starsController.value * 0.5),
                random: _random,
              ),
              size: size,
            );
          },
        ),

        // Mond
        AnimatedBuilder(
          animation: _moonController,
          builder: (context, child) {
            // Sonne/Mond im Stil des Referenzbildes
            return Positioned(
              bottom: size.height * 0.4,
              right: size.width * (0.1 + _moonPositionAnimation.value),
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      const Color(0xFFFFF3B0), // Helles Gelb im Zentrum
                      const Color(0xFFFFD700), // Gold
                      const Color(0xFFFF9500), // Orange
                      const Color(
                        0xFFFF5252,
                      ).withAlpha(179), // Rot mit Transparenz (0.7 * 255 = 179)
                    ],
                    stops: const [0.0, 0.3, 0.6, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(
                        0xFFFF5252,
                      ).withAlpha(128), // 0.5 * 255 = 128
                      blurRadius: 30,
                      spreadRadius: 10,
                    ),
                  ],
                ),
              ),
            );
          },
        ),

        // Berge/Hügel im Hintergrund
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: size.height * 0.4,
          child: CustomPaint(
            painter: MountainsPainter(),
            size: Size(size.width, size.height * 0.4),
          ),
        ),

        // Schloss/Burg
        AnimatedBuilder(
          animation: _castleController,
          builder: (context, child) {
            return Positioned(
              bottom: size.height * 0.2,
              left: 0,
              right: 0,
              child: Opacity(
                opacity: _castleOpacityAnimation.value,
                child: Center(child: CastleWidget(size: size)),
              ),
            );
          },
        ),

        // Heißluftballons
        AnimatedBuilder(
          animation: _balloonController,
          builder: (context, child) {
            return Stack(
              children: [
                // Linker Ballon
                Positioned(
                  top: size.height * (0.15 + _balloonController.value * 0.05),
                  left: size.width * 0.15,
                  child: const HotAirBalloon(
                    size: 50,
                    color: Color(0xFF8E44AD), // Dunkles Violett
                  ),
                ),
                // Mittlerer Ballon (weiter hinten)
                Positioned(
                  top: size.height * (0.1 - _balloonController.value * 0.05),
                  left: size.width * 0.4,
                  child: const HotAirBalloon(
                    size: 40,
                    color: Color(0xFF9B59B6), // Mittleres Violett
                  ),
                ),
                // Rechter Ballon
                Positioned(
                  top: size.height * (0.18 + _balloonController.value * 0.05),
                  right: size.width * 0.2,
                  child: const HotAirBalloon(
                    size: 55,
                    color: Color(0xFFD35400), // Orange/Rot
                  ),
                ),
                // Zusätzlicher kleiner Ballon (ganz links)
                Positioned(
                  top: size.height * (0.25 - _balloonController.value * 0.03),
                  left: size.width * 0.05,
                  child: const HotAirBalloon(
                    size: 30,
                    color: Color(0xFFE74C3C), // Rot
                  ),
                ),
              ],
            );
          },
        ),

        // Wolken-Animation (Öffnungseffekt)
        AnimatedBuilder(
          animation: _cloudsController,
          builder: (context, child) {
            return Stack(
              children: [
                // Linke Wolke
                Positioned(
                  top: 0,
                  left:
                      -size.width * (0.5 - 0.5 * _cloudOpeningAnimation.value),
                  width: size.width,
                  height: size.height,
                  child: const CloudWidget(isLeft: true),
                ),
                // Rechte Wolke
                Positioned(
                  top: 0,
                  right:
                      -size.width * (0.5 - 0.5 * _cloudOpeningAnimation.value),
                  width: size.width,
                  height: size.height,
                  child: const CloudWidget(isLeft: false),
                ),
              ],
            );
          },
        ),

        // Inspirierender Text mit Animation
        Positioned(
          bottom: size.height * 0.1,
          left: 20,
          right: 20,
          child: AnimatedBuilder(
            animation: _textController,
            builder: (context, child) {
              return Opacity(
                opacity: _textOpacityAnimation.value,
                child: Transform.scale(
                  scale: _textScaleAnimation.value,
                  child: Text(
                    _inspirationalTexts[_currentTextIndex],
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(128), // 0.5 * 255 = 128
                          blurRadius: 5,
                          offset: const Offset(2, 2),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

// Maler für die Sterne
class StarsPainter extends CustomPainter {
  final double opacity;
  final Random random;
  final List<Offset> _starPositions = [];
  final List<double> _starSizes = [];

  StarsPainter({required this.opacity, required this.random}) {
    // Generiere zufällige Sternpositionen, wenn sie noch nicht generiert wurden
    if (_starPositions.isEmpty) {
      for (int i = 0; i < 100; i++) {
        _starPositions.add(
          Offset(random.nextDouble() * 1000, random.nextDouble() * 500),
        );
        _starSizes.add(random.nextDouble() * 2 + 1);
      }
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < _starPositions.length; i++) {
      final position = _starPositions[i];
      final starSize = _starSizes[i];
      final x = position.dx % size.width;
      final y = position.dy % size.height;

      // Verwende unterschiedliche Opazitäten für ein natürlicheres Aussehen
      final starOpacity = 0.3 + (random.nextDouble() * 0.7 * opacity);

      // Zeichne einen Stern mit Glühen
      final paint =
          Paint()
            ..color = Colors.white.withAlpha((starOpacity * 255).toInt())
            ..style = PaintingStyle.fill;

      // Zeichne den Stern
      if (i % 5 == 0) {
        // Für einige Sterne zeichne einen echten Stern statt eines Kreises
        drawStar(canvas, x, y, starSize, paint);
      } else {
        // Für die meisten Sterne zeichne einen Kreis mit Glühen
        canvas.drawCircle(Offset(x, y), starSize, paint);

        // Füge ein Glühen hinzu
        final glowPaint =
            Paint()
              ..color = Colors.white.withAlpha(
                ((starOpacity * 0.3) * 255).toInt(),
              )
              ..style = PaintingStyle.fill
              ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

        canvas.drawCircle(Offset(x, y), starSize * 1.5, glowPaint);
      }
    }
  }

  // Hilfsmethode zum Zeichnen eines Sterns
  void drawStar(Canvas canvas, double x, double y, double size, Paint paint) {
    final path = Path();
    final outerRadius = size * 2;
    final innerRadius = size * 0.8;

    for (int i = 0; i < 5; i++) {
      final outerAngle = 2 * pi * i / 5 - pi / 2;
      final innerAngle = 2 * pi * (i + 0.5) / 5 - pi / 2;

      final outerX = x + outerRadius * cos(outerAngle);
      final outerY = y + outerRadius * sin(outerAngle);
      final innerX = x + innerRadius * cos(innerAngle);
      final innerY = y + innerRadius * sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

// Maler für die Berge/Hügel
class MountainsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Hinterste Bergkette (am dunkelsten)
    final backPaint =
        Paint()
          ..color = const Color(0xFF1A0E33) // Sehr dunkles Violett
          ..style = PaintingStyle.fill;

    final backPath = Path();
    backPath.moveTo(0, size.height);

    // Hinterste Bergkette mit spitzeren Gipfeln
    backPath.lineTo(0, size.height * 0.65);

    // Mehrere Berge mit unterschiedlichen Höhen
    backPath.lineTo(size.width * 0.05, size.height * 0.6);
    backPath.lineTo(size.width * 0.1, size.height * 0.7);
    backPath.lineTo(size.width * 0.15, size.height * 0.55);
    backPath.lineTo(size.width * 0.2, size.height * 0.65);
    backPath.lineTo(size.width * 0.25, size.height * 0.5);
    backPath.lineTo(size.width * 0.3, size.height * 0.6);
    backPath.lineTo(size.width * 0.35, size.height * 0.55);
    backPath.lineTo(size.width * 0.4, size.height * 0.45);
    backPath.lineTo(size.width * 0.45, size.height * 0.5);
    backPath.lineTo(size.width * 0.5, size.height * 0.4);
    backPath.lineTo(size.width * 0.55, size.height * 0.5);
    backPath.lineTo(size.width * 0.6, size.height * 0.45);
    backPath.lineTo(size.width * 0.65, size.height * 0.55);
    backPath.lineTo(size.width * 0.7, size.height * 0.5);
    backPath.lineTo(size.width * 0.75, size.height * 0.6);
    backPath.lineTo(size.width * 0.8, size.height * 0.55);
    backPath.lineTo(size.width * 0.85, size.height * 0.65);
    backPath.lineTo(size.width * 0.9, size.height * 0.6);
    backPath.lineTo(size.width * 0.95, size.height * 0.7);
    backPath.lineTo(size.width, size.height * 0.65);

    backPath.lineTo(size.width, size.height);
    backPath.close();

    canvas.drawPath(backPath, backPaint);

    // Mittlere Bergkette
    final midPaint =
        Paint()
          ..color = const Color(0xFF2D1B4E) // Dunkles Violett
          ..style = PaintingStyle.fill;

    final midPath = Path();
    midPath.moveTo(0, size.height);
    midPath.lineTo(0, size.height * 0.75);

    // Sanftere Berge für die mittlere Kette
    midPath.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.65,
      size.width * 0.2,
      size.height * 0.7,
    );
    midPath.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.75,
      size.width * 0.4,
      size.height * 0.65,
    );
    midPath.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.55,
      size.width * 0.6,
      size.height * 0.6,
    );
    midPath.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.65,
      size.width * 0.8,
      size.height * 0.7,
    );
    midPath.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.75,
      size.width,
      size.height * 0.7,
    );

    midPath.lineTo(size.width, size.height);
    midPath.close();

    canvas.drawPath(midPath, midPaint);

    // Vorderste Bergkette (am hellsten)
    final frontPaint =
        Paint()
          ..color = const Color(0xFF3A1E54) // Etwas helleres Violett
          ..style = PaintingStyle.fill;

    final frontPath = Path();
    frontPath.moveTo(0, size.height);
    frontPath.lineTo(0, size.height * 0.85);

    // Detailliertere vordere Bergkette mit Bäumen/Vegetation
    frontPath.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.8,
      size.width * 0.1,
      size.height * 0.85,
    );

    // Kleine Erhebungen für Bäume/Vegetation
    for (int i = 0; i < 20; i++) {
      final x = size.width * (0.1 + i * 0.045);
      final y = size.height * (0.85 - 0.05 * (i % 3) / 3);
      frontPath.lineTo(x, y);
    }

    frontPath.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.8,
      size.width,
      size.height * 0.85,
    );

    frontPath.lineTo(size.width, size.height);
    frontPath.close();

    canvas.drawPath(frontPath, frontPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Widget für das Schloss/die Burg
class CastleWidget extends StatelessWidget {
  final Size size;

  const CastleWidget({super.key, required this.size});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: CastlePainter(),
      size: Size(size.width * 0.6, size.height * 0.3),
    );
  }
}

// Maler für das Schloss/die Burg
class CastlePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = const Color(
            0xFF2D1B4E,
          ) // Dunkles Violett für die Silhouette
          ..style = PaintingStyle.fill;

    // Hauptpfad für die Burg/Schloss-Silhouette
    final castlePath = Path();

    // Basis/Fundament
    castlePath.moveTo(size.width * 0.1, size.height);
    castlePath.lineTo(size.width * 0.9, size.height);

    // Rechte Seite der Burg
    castlePath.lineTo(size.width * 0.9, size.height * 0.7);
    castlePath.lineTo(size.width * 0.85, size.height * 0.7);
    castlePath.lineTo(size.width * 0.85, size.height * 0.5);
    castlePath.lineTo(size.width * 0.8, size.height * 0.5);
    castlePath.lineTo(size.width * 0.8, size.height * 0.4);

    // Rechter Turm mit Zinnen
    castlePath.lineTo(size.width * 0.75, size.height * 0.4);
    castlePath.lineTo(size.width * 0.75, size.height * 0.3);
    castlePath.lineTo(size.width * 0.73, size.height * 0.3);
    castlePath.lineTo(size.width * 0.73, size.height * 0.35);
    castlePath.lineTo(size.width * 0.71, size.height * 0.35);
    castlePath.lineTo(size.width * 0.71, size.height * 0.3);
    castlePath.lineTo(size.width * 0.69, size.height * 0.3);
    castlePath.lineTo(size.width * 0.69, size.height * 0.35);
    castlePath.lineTo(size.width * 0.67, size.height * 0.35);
    castlePath.lineTo(size.width * 0.67, size.height * 0.3);
    castlePath.lineTo(size.width * 0.65, size.height * 0.3);

    // Mittlerer Teil
    castlePath.lineTo(size.width * 0.65, size.height * 0.4);

    // Hauptturm (Mitte)
    castlePath.lineTo(size.width * 0.6, size.height * 0.4);
    castlePath.lineTo(size.width * 0.6, size.height * 0.2);
    castlePath.lineTo(size.width * 0.58, size.height * 0.2);
    castlePath.lineTo(size.width * 0.58, size.height * 0.25);
    castlePath.lineTo(size.width * 0.56, size.height * 0.25);
    castlePath.lineTo(size.width * 0.56, size.height * 0.2);
    castlePath.lineTo(size.width * 0.54, size.height * 0.2);
    castlePath.lineTo(size.width * 0.54, size.height * 0.25);
    castlePath.lineTo(size.width * 0.52, size.height * 0.25);
    castlePath.lineTo(size.width * 0.52, size.height * 0.2);
    castlePath.lineTo(size.width * 0.5, size.height * 0.2);
    castlePath.lineTo(size.width * 0.5, size.height * 0.15);
    castlePath.lineTo(size.width * 0.48, size.height * 0.15);
    castlePath.lineTo(size.width * 0.48, size.height * 0.2);
    castlePath.lineTo(size.width * 0.46, size.height * 0.2);
    castlePath.lineTo(size.width * 0.46, size.height * 0.25);
    castlePath.lineTo(size.width * 0.44, size.height * 0.25);
    castlePath.lineTo(size.width * 0.44, size.height * 0.2);
    castlePath.lineTo(size.width * 0.42, size.height * 0.2);
    castlePath.lineTo(size.width * 0.42, size.height * 0.25);
    castlePath.lineTo(size.width * 0.4, size.height * 0.25);
    castlePath.lineTo(size.width * 0.4, size.height * 0.2);
    castlePath.lineTo(size.width * 0.38, size.height * 0.2);

    // Linker Teil
    castlePath.lineTo(size.width * 0.38, size.height * 0.4);
    castlePath.lineTo(size.width * 0.35, size.height * 0.4);

    // Linker Turm mit Zinnen
    castlePath.lineTo(size.width * 0.35, size.height * 0.3);
    castlePath.lineTo(size.width * 0.33, size.height * 0.3);
    castlePath.lineTo(size.width * 0.33, size.height * 0.35);
    castlePath.lineTo(size.width * 0.31, size.height * 0.35);
    castlePath.lineTo(size.width * 0.31, size.height * 0.3);
    castlePath.lineTo(size.width * 0.29, size.height * 0.3);
    castlePath.lineTo(size.width * 0.29, size.height * 0.35);
    castlePath.lineTo(size.width * 0.27, size.height * 0.35);
    castlePath.lineTo(size.width * 0.27, size.height * 0.3);
    castlePath.lineTo(size.width * 0.25, size.height * 0.3);

    // Linke Seite der Burg
    castlePath.lineTo(size.width * 0.25, size.height * 0.4);
    castlePath.lineTo(size.width * 0.2, size.height * 0.4);
    castlePath.lineTo(size.width * 0.2, size.height * 0.5);
    castlePath.lineTo(size.width * 0.15, size.height * 0.5);
    castlePath.lineTo(size.width * 0.15, size.height * 0.7);
    castlePath.lineTo(size.width * 0.1, size.height * 0.7);

    // Schließe den Pfad
    castlePath.close();

    // Zeichne die Burg
    canvas.drawPath(castlePath, paint);

    // Zeichne Fenster und Details
    final detailPaint =
        Paint()
          ..color = const Color(0xFFFFD700) // Gold für Lichter/Fenster
          ..style = PaintingStyle.fill;

    // Fenster im Hauptturm
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.3),
      size.width * 0.015,
      detailPaint,
    );

    // Fenster in den Seitentürmen
    canvas.drawCircle(
      Offset(size.width * 0.3, size.height * 0.45),
      size.width * 0.01,
      detailPaint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.7, size.height * 0.45),
      size.width * 0.01,
      detailPaint,
    );

    // Fenster in der Basis
    for (int i = 0; i < 5; i++) {
      canvas.drawCircle(
        Offset(size.width * (0.25 + i * 0.125), size.height * 0.6),
        size.width * 0.008,
        detailPaint,
      );
    }

    // Flagge auf dem Hauptturm
    final flagPaint =
        Paint()
          ..color = const Color(0xFFFF5252) // Rot für die Flagge
          ..style = PaintingStyle.fill;

    final flagPath = Path();
    flagPath.moveTo(size.width * 0.5, size.height * 0.15);
    flagPath.lineTo(size.width * 0.5, size.height * 0.1);
    flagPath.lineTo(size.width * 0.55, size.height * 0.125);
    flagPath.lineTo(size.width * 0.5, size.height * 0.15);
    flagPath.close();

    canvas.drawPath(flagPath, flagPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Widget für die Wolken
class CloudWidget extends StatelessWidget {
  final bool isLeft;

  const CloudWidget({super.key, required this.isLeft});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: CloudPainter(isLeft: isLeft),
      size: MediaQuery.of(context).size,
    );
  }
}

// Maler für die Wolken
class CloudPainter extends CustomPainter {
  final bool isLeft;

  CloudPainter({required this.isLeft});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withAlpha(179) // 0.7 * 255 = 179
          ..style = PaintingStyle.fill;

    final path = Path();

    if (isLeft) {
      // Linke Wolke
      path.moveTo(0, 0);
      path.lineTo(0, size.height);
      path.lineTo(size.width * 0.5, size.height);
      path.quadraticBezierTo(
        size.width * 0.3,
        size.height * 0.7,
        size.width * 0.5,
        size.height * 0.5,
      );
      path.quadraticBezierTo(
        size.width * 0.7,
        size.height * 0.3,
        size.width * 0.5,
        0,
      );
      path.close();
    } else {
      // Rechte Wolke
      path.moveTo(size.width, 0);
      path.lineTo(size.width, size.height);
      path.lineTo(size.width * 0.5, size.height);
      path.quadraticBezierTo(
        size.width * 0.7,
        size.height * 0.7,
        size.width * 0.5,
        size.height * 0.5,
      );
      path.quadraticBezierTo(
        size.width * 0.3,
        size.height * 0.3,
        size.width * 0.5,
        0,
      );
      path.close();
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Widget für den Heißluftballon
class HotAirBalloon extends StatelessWidget {
  final double size;
  final Color color;

  const HotAirBalloon({super.key, required this.size, required this.color});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size * 1.5,
      child: CustomPaint(
        painter: HotAirBalloonPainter(color: color),
        size: Size(size, size * 1.5),
      ),
    );
  }
}

// Maler für den Heißluftballon
class HotAirBalloonPainter extends CustomPainter {
  final Color color;

  HotAirBalloonPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    // Ballon
    final balloonPaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final balloonPath = Path();
    balloonPath.addOval(Rect.fromLTWH(0, 0, size.width, size.width));

    // Korb
    final basketPaint =
        Paint()
          ..color = const Color(0xFF8D6E63) // Braun
          ..style = PaintingStyle.fill;

    final basketPath = Path();
    basketPath.addRect(
      Rect.fromLTWH(
        size.width * 0.3,
        size.width * 1.1,
        size.width * 0.4,
        size.width * 0.3,
      ),
    );

    // Seile
    final ropePaint =
        Paint()
          ..color = const Color(0xFFBDBDBD) // Grau
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    final leftRopePath = Path();
    leftRopePath.moveTo(size.width * 0.3, size.width * 0.8);
    leftRopePath.lineTo(size.width * 0.3, size.width * 1.1);

    final rightRopePath = Path();
    rightRopePath.moveTo(size.width * 0.7, size.width * 0.8);
    rightRopePath.lineTo(size.width * 0.7, size.width * 1.1);

    // Zeichne alle Teile
    canvas.drawPath(balloonPath, balloonPaint);
    canvas.drawPath(basketPath, basketPaint);
    canvas.drawPath(leftRopePath, ropePaint);
    canvas.drawPath(rightRopePath, ropePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

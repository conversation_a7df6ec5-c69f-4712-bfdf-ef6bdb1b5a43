import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein Widget, das einen animierten Hintergrund für den Onboarding-Screen darstellt
class AnimatedBackground extends StatefulWidget {
  final Widget child;

  const AnimatedBackground({super.key, required this.child});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late final List<AnimationController> _controllers;
  late final List<Animation<double>> _animations;
  final List<Offset> _positions = [];
  final List<double> _sizes = [];
  final List<Color> _colors = [];
  final int _numElements = 8; // Reduziert für bessere GPU-Performance

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    final random = math.Random();

    // Erstelle Animationscontroller mit unterschiedlichen Geschwindigkeiten
    _controllers = List.generate(
      _numElements,
      (index) => AnimationController(
        duration: Duration(
          milliseconds: 20000 + random.nextInt(20000),
        ), // 20-40 Sekunden für langsamere, entspanntere Bewegungen
        vsync: this,
      ),
    );

    // Verwende verschiedene Kurven für unterschiedliche Bewegungsmuster
    final curves = [
      Curves.easeInOut,
      Curves.easeInOutSine,
      Curves.easeInOutQuad,
      Curves.easeInOutCubic,
      Curves.easeInOutQuart,
    ];

    _animations = List.generate(
      _numElements,
      (index) => CurvedAnimation(
        parent: _controllers[index],
        curve: curves[random.nextInt(curves.length)],
      ),
    );

    // Zufällige Startpositionen, Größen und Farben
    for (int i = 0; i < _numElements; i++) {
      // Positioniere Elemente gleichmäßiger über den Bildschirm verteilt
      _positions.add(
        Offset(
          0.1 + 0.8 * random.nextDouble(), // Vermeide Ränder (10%-90%)
          0.1 + 0.8 * random.nextDouble(),
        ),
      );

      // Variiere die Größen stärker für mehr Tiefenwirkung
      // Kleinere Elemente für subtilere Hintergrundeffekte
      _sizes.add(30.0 + random.nextDouble() * 180.0);

      // Erweiterte Farbpalette mit mehr Variationen
      List<Color> colorPalette = [
        AppTheme.primaryColor,
        AppTheme.primaryLightColor,
        AppTheme.secondaryColor,
        AppTheme.primaryDarkColor,
        AppTheme.secondaryLightColor,
      ];

      final baseColor = colorPalette[random.nextInt(colorPalette.length)];

      // Einfache Variante mit geringer Opazität
      final color = baseColor.withAlpha(
        15 + random.nextInt(15),
      ); // 6-12% Opazität
      _colors.add(color);

      // Gestaffelte Animation mit unterschiedlichen Verzögerungen
      // für einen natürlicheren Effekt
      Future.delayed(
        Duration(milliseconds: i * 300 + random.nextInt(2000)),
        () {
          if (mounted) {
            // Verschiedene Animationsmodi für Abwechslung
            if (random.nextBool()) {
              _controllers[i].repeat(reverse: true);
            } else {
              _controllers[i].repeat();
            }
          }
        },
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Hintergrundfarbe
        Container(
          // Einfacher dunkler Hintergrund
          color: AppTheme.backgroundDarkColor,
        ),

        // Animierte Elemente
        ..._buildAnimatedElements(),

        // Inhalt
        widget.child,
      ],
    );
  }

  List<Widget> _buildAnimatedElements() {
    final List<Widget> elements = [];
    final size = MediaQuery.of(context).size;

    for (int i = 0; i < _numElements; i++) {
      elements.add(
        AnimatedBuilder(
          animation: _animations[i],
          builder: (context, child) {
            final value = _animations[i].value;

            // Berechne die aktuelle Position mit einer sanften Bewegung
            final dx = _positions[i].dx + 0.1 * math.sin(value * math.pi * 2);
            final dy = _positions[i].dy + 0.1 * math.cos(value * math.pi * 2);

            return Positioned(
              left: dx * size.width - _sizes[i] / 2,
              top: dy * size.height - _sizes[i] / 2,
              child: RepaintBoundary(
                child: Opacity(
                  opacity: 0.3 + 0.2 * math.sin(value * math.pi),
                  child: Container(
                    width: _sizes[i],
                    height: _sizes[i],
                    decoration: BoxDecoration(
                      color: _colors[i],
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          // Reduzierte Schatten für bessere GPU-Performance
                          color: _colors[i].withValues(alpha: 0.3),
                          blurRadius: 10, // Reduziert von 20
                          spreadRadius: 2, // Reduziert von 5
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      );
    }

    return elements;
  }
}

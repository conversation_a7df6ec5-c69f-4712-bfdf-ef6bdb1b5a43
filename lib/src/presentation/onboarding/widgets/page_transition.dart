import 'package:flutter/material.dart';

/// Eine benutzerdefinierte Übergangsanimation für den PageView im Onboarding
class SmoothPageTransition extends PageRouteBuilder {
  final Widget page;

  SmoothPageTransition({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          var offsetAnimation = animation.drive(tween);

          return SlideTransition(
            position: offsetAnimation,
            child: FadeTransition(opacity: animation, child: child),
          );
        },
      );
}

/// Ein benutzerdefinierter PageView mit glatten Übergängen
class SmoothPageView extends StatefulWidget {
  final List<Widget> pages;
  final PageController controller;
  final ValueChanged<int>? onPageChanged;

  const SmoothPageView({
    super.key,
    required this.pages,
    required this.controller,
    this.onPageChanged,
  });

  @override
  State<SmoothPageView> createState() => _SmoothPageViewState();
}

class _SmoothPageViewState extends State<SmoothPageView> {
  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: widget.controller,
      onPageChanged: widget.onPageChanged,
      // Verwende NeverScrollableScrollPhysics für die letzte Seite, um Überspringen zu verhindern
      physics: const BouncingScrollPhysics(),
      itemCount: widget.pages.length,
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: widget.controller,
          builder: (context, child) {
            double value = 1.0;

            if (widget.controller.position.haveDimensions) {
              value = (widget.controller.page! - index).abs();
              value = (1 - (value.clamp(0.0, 1.0))).toDouble();
            }

            // Verbesserte Animation mit mehreren Transformationen
            return AnimatedBuilder(
              animation: widget.controller,
              builder: (context, child) {
                // Berechne die Transformationswerte basierend auf der Seitenposition
                final position = widget.controller.position;
                final pageOffset =
                    position.haveDimensions
                        ? (widget.controller.page ?? 0) - index
                        : 0.0;
                final absPageOffset = pageOffset.abs();

                // Verschiedene Kurven für verschiedene Effekte
                final scaleValue = Curves.easeOutQuart.transform(
                  (1 - absPageOffset.clamp(0.0, 1.0)).toDouble(),
                );
                final opacityValue = Curves.easeInOut.transform(
                  (1 - absPageOffset.clamp(0.0, 1.0)).toDouble(),
                );

                // Berechne die Verschiebung für einen 3D-Effekt
                final translateX = pageOffset * 30.0;

                // Verbesserte Transformationen für einen noch hochwertigeren Effekt
                return Transform(
                  transform:
                      Matrix4.identity()
                        ..setEntry(
                          3,
                          2,
                          0.002,
                        ) // Stärkere Perspektive für mehr 3D-Effekt
                        ..translate(translateX)
                        ..rotateY(
                          pageOffset * 0.05,
                        ) // Leichte Rotation um die Y-Achse
                        ..scale(
                          0.85 + scaleValue * 0.15,
                        ), // Skalierung zwischen 0.85 und 1.0
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(
                            alpha: 0.1 * opacityValue,
                          ),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Opacity(
                      opacity:
                          0.6 +
                          opacityValue * 0.4, // Opazität zwischen 0.6 und 1.0
                      child: widget.pages[index],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}

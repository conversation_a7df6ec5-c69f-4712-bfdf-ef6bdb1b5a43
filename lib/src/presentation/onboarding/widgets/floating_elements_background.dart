import 'dart:math';
import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein Widget, das einen animierten Hintergrund mit fliegenden Elementen darstellt
class FloatingElementsBackground extends StatefulWidget {
  final Widget child;
  final PageController?
  pageController; // Optional: PageController für Seitenübergänge

  const FloatingElementsBackground({
    super.key,
    required this.child,
    this.pageController,
  });

  @override
  State<FloatingElementsBackground> createState() =>
      FloatingElementsBackgroundState();
}

class FloatingElementsBackgroundState extends State<FloatingElementsBackground>
    with TickerProviderStateMixin {
  // Controller für verschiedene Animationen
  late final AnimationController _mainController;
  late final AnimationController _secondaryController;
  late final AnimationController _tertiaryController;

  // Controller für Farbwechsel
  late final AnimationController _colorController;

  // Zufallsgenerator für die Positionierung der Elemente
  final Random _random = Random();

  // Liste der fliegenden Elemente
  final List<FloatingElement> _elements = [];

  // Aktuelle Farbpalette und nächste Farbpalette
  List<Color> _currentColors = [];
  List<Color> _nextColors = [];

  // Aktuelle Seite für Farbwechsel
  double _currentPage = 0;

  // Für Exit-Animation
  bool _isExiting = false;
  Offset _exitDirection = const Offset(-1.0, 0.5); // Nach links unten

  @override
  void initState() {
    super.initState();

    // Hauptcontroller für die primäre Animation
    _mainController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 80), // Schnellere Animation
    );

    // Sekundärer Controller für zusätzliche Animationen
    _secondaryController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60), // Noch schnellere Animation
    );

    // Tertiärer Controller für weitere Variationen
    _tertiaryController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 40), // Sehr schnelle Animation
    );

    // Controller für Farbwechsel
    _colorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Starte alle Animationen
    _mainController.repeat(reverse: false);
    _secondaryController.repeat(reverse: false);
    _tertiaryController.repeat(reverse: false);

    // Initialisiere Farbpaletten
    _initializeColorPalettes();

    // Generiere die fliegenden Elemente
    _generateElements();

    // Listener für PageController
    if (widget.pageController != null) {
      widget.pageController!.addListener(_onPageChanged);
    }
  }

  void _initializeColorPalettes() {
    // Basis-Farbpaletten
    final basePalettes = [
      // Palette 1: Blau/Lila
      [
        AppTheme.primaryColor.withAlpha(51), // 0.2 * 255 = 51
        AppTheme.primaryLightColor.withAlpha(38), // 0.15 * 255 = 38
        Colors.purple.withAlpha(51), // 0.2 * 255 = 51
        Colors.white.withAlpha(26), // 0.1 * 255 = 26
        Colors.purple.withAlpha(38), // 0.15 * 255 = 38
      ],
      // Palette 2: Grün/Blau
      [
        Colors.teal.withAlpha(51),
        Colors.cyan.withAlpha(38),
        Colors.blue.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.lightBlue.withAlpha(38),
      ],
      // Palette 3: Rot/Orange
      [
        Colors.deepOrange.withAlpha(51),
        Colors.orange.withAlpha(38),
        Colors.amber.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.red.withAlpha(38),
      ],
      // Palette 4: Lila/Pink
      [
        Colors.deepPurple.withAlpha(51),
        Colors.purple.withAlpha(38),
        Colors.pink.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.indigo.withAlpha(38),
      ],
      // Palette 5: Grün/Gelb
      [
        Colors.green.withAlpha(51),
        Colors.lightGreen.withAlpha(38),
        Colors.lime.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.teal.withAlpha(38),
      ],
    ];

    // Setze initiale Paletten
    _currentColors = List.from(basePalettes[0]);
    _nextColors = List.from(basePalettes[1]);
  }

  void _onPageChanged() {
    if (widget.pageController == null || !widget.pageController!.hasClients) {
      return;
    }

    final newPage = widget.pageController!.page ?? 0;

    // Wenn wir die Seite gewechselt haben
    if ((newPage.round() != _currentPage.round()) &&
        _colorController.status != AnimationStatus.forward) {
      _currentPage = newPage;

      // Wähle neue Farbpalette
      final paletteIndex = _currentPage.round() % 5;
      _updateColorPalette(paletteIndex);

      // Starte Farbübergangsanimation
      _colorController.forward(from: 0.0);
    }
  }

  void _updateColorPalette(int paletteIndex) {
    // Basis-Farbpaletten
    final basePalettes = [
      // Palette 1: Blau/Lila
      [
        AppTheme.primaryColor.withAlpha(51),
        AppTheme.primaryLightColor.withAlpha(38),
        Colors.purple.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.purple.withAlpha(38),
      ],
      // Palette 2: Grün/Blau
      [
        Colors.teal.withAlpha(51),
        Colors.cyan.withAlpha(38),
        Colors.blue.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.lightBlue.withAlpha(38),
      ],
      // Palette 3: Rot/Orange
      [
        Colors.deepOrange.withAlpha(51),
        Colors.orange.withAlpha(38),
        Colors.amber.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.red.withAlpha(38),
      ],
      // Palette 4: Lila/Pink
      [
        Colors.deepPurple.withAlpha(51),
        Colors.purple.withAlpha(38),
        Colors.pink.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.indigo.withAlpha(38),
      ],
      // Palette 5: Grün/Gelb
      [
        Colors.green.withAlpha(51),
        Colors.lightGreen.withAlpha(38),
        Colors.lime.withAlpha(51),
        Colors.white.withAlpha(26),
        Colors.teal.withAlpha(38),
      ],
    ];

    // Aktuelle Palette wird zur vorherigen
    _currentColors = List.from(_nextColors);

    // Wähle nächste Palette
    final nextPaletteIndex = (paletteIndex + 1) % basePalettes.length;
    _nextColors = List.from(basePalettes[nextPaletteIndex]);
  }

  // Methode zum Starten der Exit-Animation
  void startExitAnimation(Offset direction) {
    setState(() {
      _isExiting = true;
      _exitDirection = direction;
    });
  }

  @override
  void dispose() {
    _mainController.dispose();
    _secondaryController.dispose();
    _tertiaryController.dispose();
    _colorController.dispose();

    if (widget.pageController != null) {
      widget.pageController!.removeListener(_onPageChanged);
    }

    super.dispose();
  }

  // Generiert die fliegenden Elemente mit zufälligen Eigenschaften
  void _generateElements() {
    // Verschiedene Formen für die Elemente
    final shapes = [
      FloatingElementShape.circle,
      FloatingElementShape.square,
      FloatingElementShape.triangle,
      FloatingElementShape.star,
    ];

    // Verschiedene Größen für die Elemente
    final sizes = [8.0, 12.0, 16.0, 20.0, 24.0, 28.0];

    // Generiere 35 zufällige Elemente für mehr Dichte
    for (int i = 0; i < 35; i++) {
      final shape = shapes[_random.nextInt(shapes.length)];
      final size = sizes[_random.nextInt(sizes.length)];

      // Verwende die aktuelle Farbpalette
      final colorIndex = _random.nextInt(_currentColors.length);
      final color = _currentColors[colorIndex];

      // Zufällige Startposition
      final startX = _random.nextDouble();
      final startY = _random.nextDouble();

      // Zufällige Bewegungsrichtung mit höherer Geschwindigkeit
      final moveX = (_random.nextDouble() * 2 - 1) * 0.5; // -0.5 bis 0.5
      final moveY = (_random.nextDouble() * 2 - 1) * 0.5; // -0.5 bis 0.5

      // Zufällige Rotationsgeschwindigkeit
      final rotationSpeed = _random.nextDouble() * 2 * pi; // 0 bis 2π

      // Zufällige Verzögerung für den Start der Animation
      final delay = _random.nextDouble();

      // Zufällige Kurvenart für nicht-lineare Bewegung
      final curveType = _random.nextInt(4); // 0-3 für verschiedene Kurven

      // Zufällige Geschwindigkeit
      final speed = _random.nextDouble() * 0.5 + 0.3; // 0.3 bis 0.8

      // Füge das Element zur Liste hinzu
      _elements.add(
        FloatingElement(
          shape: shape,
          size: size,
          color: color,
          startX: startX,
          startY: startY,
          moveX: moveX,
          moveY: moveY,
          rotationSpeed: rotationSpeed,
          delay: delay,
          colorIndex: colorIndex,
          curveType: curveType,
          speed: speed,
        ),
      );
    }
  }

  // Berechnet die aktuelle Farbe eines Elements basierend auf der Farbübergangsanimation
  Color _getCurrentElementColor(FloatingElement element) {
    if (_colorController.value == 0) {
      return element.color;
    }

    // Interpoliere zwischen aktueller und nächster Farbe
    final currentColor = _currentColors[element.colorIndex];
    final nextColor = _nextColors[element.colorIndex];

    return Color.lerp(currentColor, nextColor, _colorController.value) ??
        currentColor;
  }

  // Berechnet die Position basierend auf der Kurvenart
  double _applyCurve(double value, int curveType) {
    switch (curveType) {
      case 0:
        return Curves.easeInOut.transform(value);
      case 1:
        return Curves.easeInOutBack.transform(value);
      case 2:
        return Curves.elasticOut.transform(value);
      case 3:
        return Curves.bounceOut.transform(value);
      default:
        return value;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Berechne den Seitenversatz für die Hintergrundanimation
    double pageOffset = 0.0;
    if (widget.pageController != null && widget.pageController!.hasClients) {
      final position = widget.pageController!.position;
      if (position.haveDimensions) {
        pageOffset = widget.pageController!.page ?? 0;
      }
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        // Hintergrundfarbe mit Farbverlauf
        AnimatedBuilder(
          animation: _colorController,
          builder: (context, child) {
            // Interpoliere zwischen aktuellen und nächsten Farben für den Hintergrund
            final currentBgColor = AppTheme.backgroundDarkColor;
            final currentAccentColor =
                Color.lerp(
                  AppTheme.backgroundDarkColor,
                  AppTheme.primaryColor,
                  0.2,
                ) ??
                AppTheme.backgroundDarkColor;

            // Nächste Farben für den Hintergrund
            final nextBgColor = AppTheme.backgroundDarkColor;
            final nextAccentColor =
                Color.lerp(AppTheme.backgroundDarkColor, _nextColors[0], 0.3) ??
                AppTheme.backgroundDarkColor;

            // Interpoliere zwischen aktuellen und nächsten Farben
            final bgColor =
                Color.lerp(
                  currentBgColor,
                  nextBgColor,
                  _colorController.value,
                ) ??
                currentBgColor;
            final accentColor =
                Color.lerp(
                  currentAccentColor,
                  nextAccentColor,
                  _colorController.value,
                ) ??
                currentAccentColor;

            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [bgColor, accentColor],
                ),
              ),
            );
          },
        ),

        // Fliegende Elemente
        ...List.generate(_elements.length, (index) {
          final element = _elements[index];

          // Wähle den Controller basierend auf dem Index und der Geschwindigkeit
          final controller =
              index % 3 == 0
                  ? _mainController
                  : (index % 3 == 1
                      ? _secondaryController
                      : _tertiaryController);

          return AnimatedBuilder(
            animation: Listenable.merge([controller, _colorController]),
            builder: (context, child) {
              // Berechne die aktuelle Position basierend auf der Animation
              final baseAnimationValue =
                  (controller.value + element.delay) % 1.0;

              // Wende die Kurve an für nicht-lineare Bewegung
              final animationValue = _applyCurve(
                baseAnimationValue,
                element.curveType,
              );

              // Berechne die aktuelle Position mit Seitenversatz
              double currentX =
                  (element.startX +
                      element.moveX * animationValue * element.speed) %
                  1.0;
              double currentY =
                  (element.startY +
                      element.moveY * animationValue * element.speed) %
                  1.0;

              // Füge Seitenversatz hinzu, wenn PageController vorhanden ist
              if (widget.pageController != null &&
                  widget.pageController!.hasClients) {
                // Berechne den Versatz basierend auf der aktuellen Seite
                final fractionalPage = pageOffset - pageOffset.floor();

                // Verschiebe die Elemente mit der Seite
                currentX = (currentX - fractionalPage * 0.5) % 1.0;
                if (currentX < 0) currentX += 1.0;
              }

              // Wenn Exit-Animation aktiv ist, bewege alle Elemente in die Exit-Richtung
              if (_isExiting) {
                currentX += _exitDirection.dx * 0.05;
                currentY += _exitDirection.dy * 0.05;

                // Wenn Element außerhalb des Bildschirms ist, entferne es
                if (currentX < -0.2 ||
                    currentX > 1.2 ||
                    currentY < -0.2 ||
                    currentY > 1.2) {
                  return const SizedBox.shrink();
                }
              }

              // Berechne die aktuelle Rotation
              final currentRotation = element.rotationSpeed * animationValue;

              // Aktuelle Farbe mit Übergang
              final currentColor = _getCurrentElementColor(element);

              return Positioned(
                left: MediaQuery.of(context).size.width * currentX,
                top: MediaQuery.of(context).size.height * currentY,
                child: Transform.rotate(
                  angle: currentRotation,
                  child: _buildElementWidget(element, currentColor),
                ),
              );
            },
          );
        }),

        // Inhalt
        widget.child,
      ],
    );
  }

  // Baut das Widget für ein Element basierend auf seiner Form
  Widget _buildElementWidget(FloatingElement element, Color color) {
    switch (element.shape) {
      case FloatingElementShape.circle:
        return Container(
          width: element.size,
          height: element.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            boxShadow: [
              BoxShadow(
                color: color.withAlpha(30),
                blurRadius: 5,
                spreadRadius: 1,
              ),
            ],
          ),
        );
      case FloatingElementShape.square:
        return Container(
          width: element.size,
          height: element.size,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: color,
            boxShadow: [
              BoxShadow(
                color: color.withAlpha(30),
                blurRadius: 5,
                spreadRadius: 1,
              ),
            ],
          ),
        );
      case FloatingElementShape.triangle:
        return CustomPaint(
          size: Size(element.size, element.size),
          painter: TrianglePainter(color: color),
        );
      case FloatingElementShape.star:
        return CustomPaint(
          size: Size(element.size, element.size),
          painter: StarPainter(color: color),
        );
    }
  }
}

// Formen für die fliegenden Elemente
enum FloatingElementShape { circle, square, triangle, star }

// Klasse für ein fliegendes Element
class FloatingElement {
  final FloatingElementShape shape;
  final double size;
  final Color color;
  final double startX;
  final double startY;
  final double moveX;
  final double moveY;
  final double rotationSpeed;
  final double delay;
  final int colorIndex;
  final int curveType;
  final double speed;

  FloatingElement({
    required this.shape,
    required this.size,
    required this.color,
    required this.startX,
    required this.startY,
    required this.moveX,
    required this.moveY,
    required this.rotationSpeed,
    required this.delay,
    required this.colorIndex,
    required this.curveType,
    required this.speed,
  });
}

// Maler für ein Dreieck
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path =
        Path()
          ..moveTo(size.width / 2, 0)
          ..lineTo(size.width, size.height)
          ..lineTo(0, size.height)
          ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Maler für einen Stern
class StarPainter extends CustomPainter {
  final Color color;

  StarPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final outerRadius = size.width / 2;
    final innerRadius = size.width / 5;

    for (int i = 0; i < 5; i++) {
      final outerAngle = 2 * pi * i / 5 - pi / 2;
      final innerAngle = 2 * pi * (i + 0.5) / 5 - pi / 2;

      final outerX = centerX + outerRadius * cos(outerAngle);
      final outerY = centerY + outerRadius * sin(outerAngle);
      final innerX = centerX + innerRadius * cos(innerAngle);
      final innerY = centerY + innerRadius * sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

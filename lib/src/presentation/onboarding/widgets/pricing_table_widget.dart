import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Widget zur Anzeige der Preistabelle im Onboarding
class PricingTableWidget extends StatelessWidget {
  const PricingTableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMedium,
        vertical: AppTheme.spacingLarge,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.borderDarkColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Überschrift
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Row(
              children: [
                const Icon(
                  Icons.local_offer,
                  color: AppTheme.primaryLightColor,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  'Empfehlung für die Preisstruktur:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Tabelle
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: WidgetStateProperty.all(
                AppTheme.primaryDarkColor.withValues(alpha: 0.7),
              ),
              dataRowColor: WidgetStateProperty.all(Colors.transparent),
              columnSpacing: 16,
              headingTextStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              dataTextStyle: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
              ),
              columns: const [
                DataColumn(label: Text('Plan')),
                DataColumn(label: Text('Bewerbungen/Monat')),
                DataColumn(label: Text('Preis (€)')),
                DataColumn(label: Text('Vorteile')),
              ],
              rows: [
                _buildDataRow(
                  'Basic',
                  '30',
                  '6,99 €',
                  '30 Bewerbungen/Monat, mit Werbung',
                ),
                _buildDataRow(
                  'Pro',
                  '150',
                  '14,99 €',
                  '150 Bewerbungen/Monat, ohne Werbung',
                ),
                _buildDataRow(
                  'Unlimited',
                  'Unbegrenzt',
                  '29,99 €',
                  'Unbegrenzt, 1-Klick, alle Funktionen frei',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  DataRow _buildDataRow(
    String plan,
    String bewerbungen,
    String preis,
    String vorteile,
  ) {
    Color rowColor = Colors.transparent;

    // Hervorhebung für den Pro-Plan
    if (plan == 'Pro') {
      rowColor = AppTheme.primaryColor.withValues(alpha: 0.15);
    }

    return DataRow(
      color: WidgetStateProperty.all(rowColor),
      cells: [
        DataCell(
          Text(
            plan,
            style: TextStyle(
              fontWeight: plan == 'Pro' ? FontWeight.bold : FontWeight.normal,
              color:
                  plan == 'Pro'
                      ? AppTheme.primaryLightColor
                      : Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ),
        DataCell(Text(bewerbungen)),
        DataCell(
          Text(
            preis,
            style: TextStyle(
              fontWeight: plan == 'Pro' ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        DataCell(Text(vorteile)),
      ],
    );
  }
}

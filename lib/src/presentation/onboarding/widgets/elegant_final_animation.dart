import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'floating_elements_background.dart';

/// Ein elegantes Widget für die letzte Seite des Onboarding-Screens
/// Mit minimalistischem Design und subtilen Animationen
class ElegantFinalAnimation extends StatefulWidget {
  final VoidCallback onAnimationComplete;
  final int durationInSeconds;

  const ElegantFinalAnimation({
    super.key,
    required this.onAnimationComplete,
    this.durationInSeconds = 5, // Kürzere Dauer: 5 Sekunden
  });

  @override
  State<ElegantFinalAnimation> createState() => ElegantFinalAnimationState();
}

/// Klasse für einen Partikel
class Particle {
  Offset position;
  final double size;
  final Color color;
  final double speed;
  final double angle;
  final double opacity;
  final bool blur; // Neues Attribut für Blur-Effekt

  Particle({
    required this.position,
    required this.size,
    required this.color,
    required this.speed,
    required this.angle,
    required this.opacity,
    this.blur = false, // Standardmäßig kein Blur
  });
}

/// Maler für die animierten Partikel
class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final double animation;
  final bool isExiting;
  final double exitProgress;

  ParticlesPainter({
    required this.particles,
    required this.animation,
    this.isExiting = false,
    this.exitProgress = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    for (final particle in particles) {
      // Berechne eine wirklich kontinuierliche Position für jeden Partikel
      // Verwende die aktuelle Zeit für eine endlose Animation ohne Wiederholung
      final particleTime = DateTime.now().millisecondsSinceEpoch / 1000.0;
      final particleSpeed = particle.speed;

      // Individuelle Geschwindigkeit für jeden Partikel basierend auf seiner ID
      final individualOffset = particleTime * particleSpeed * 30;

      // Berechne die Position mit einer Sinusfunktion für sanfte Übergänge
      // Verwende verschiedene Frequenzen für x und y, um natürlichere Bewegungen zu erzeugen
      final dx =
          math.cos(particle.angle) *
          particleSpeed *
          (math.sin(individualOffset * 0.3) + 1) *
          100;
      final dy =
          math.sin(particle.angle) *
          particleSpeed *
          (math.cos(individualOffset * 0.2) + 1) *
          100;

      // Wenn ein Partikel den Bildschirm verlässt, erscheint er auf der anderen Seite wieder
      // Dies sorgt für eine endlose Animation ohne sichtbare Wiederholungen
      final basePosition = particle.position;

      // Wenn die Exit-Animation aktiv ist, bewege alle Partikel nach links unten
      Offset currentPosition;
      if (isExiting) {
        final exitOffset = Offset(
          -size.width * exitProgress * 0.5,
          size.height * exitProgress * 0.5,
        );
        currentPosition = center + basePosition + Offset(dx, dy) + exitOffset;
      } else {
        currentPosition = center + basePosition + Offset(dx, dy);
      }

      // Zeichne den Partikel
      final paint =
          Paint()
            ..color = particle.color.withAlpha((particle.opacity * 255).toInt())
            ..style = PaintingStyle.fill;

      // Füge Blur-Effekt hinzu, wenn aktiviert
      if (particle.blur) {
        paint.maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);
      }

      // Zeichne den Partikel als Kreis
      canvas.drawCircle(currentPosition, particle.size, paint);

      // Für einige Partikel einen "Schweif" zeichnen (Bewegungseffekt)
      if (particle.blur && particle.size > 3.0) {
        // Mehrere Schweif-Segmente für flüssigeren Effekt
        for (int i = 1; i <= 3; i++) {
          final trailOpacity = particle.opacity * (1 - i * 0.2);
          if (trailOpacity <= 0) continue;

          final trailPaint =
              Paint()
                ..color = particle.color.withAlpha((trailOpacity * 100).toInt())
                ..style = PaintingStyle.fill
                ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

          // Zeichne mehrere Kreise hinter dem Partikel für einen längeren Schweif
          final trailOffset = Offset(
            -math.cos(particle.angle) * particle.speed * (5 * i),
            -math.sin(particle.angle) * particle.speed * (5 * i),
          );

          canvas.drawCircle(
            currentPosition + trailOffset,
            particle.size * (1 - i * 0.15),
            trailPaint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(ParticlesPainter oldDelegate) => true;
}

/// Der öffentliche State für die ElegantFinalAnimation
/// Ermöglicht den Zugriff auf die startExitAnimation-Methode von außen
class ElegantFinalAnimationState extends State<ElegantFinalAnimation>
    with TickerProviderStateMixin {
  // Animation Controller
  late final AnimationController backgroundController;
  late final AnimationController cardController;
  late final AnimationController iconController;
  late final AnimationController textController;
  late final AnimationController particlesController;
  late final AnimationController exitAnimationController;

  // Animationen
  late final Animation<double> cardScaleAnimation;
  late final Animation<double> iconScaleAnimation;
  late final Animation<double> textOpacityAnimation;
  late final Animation<double> textSlideAnimation;
  late final Animation<double> particlesAnimation;

  // Exit-Animationen
  late final Animation<Offset> exitOffsetAnimation;
  late final Animation<double> exitOpacityAnimation;

  // Timer für die Gesamtdauer
  Timer? completionTimer;

  // Zufallsgenerator für Partikel
  final math.Random random = math.Random();
  final List<Particle> particles = [];

  // Status für die Exit-Animation
  bool isExiting = false;

  @override
  void initState() {
    super.initState();

    // Hintergrund-Animation
    backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    // Karten-Animation
    cardController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Icon-Animation
    iconController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Text-Animation
    textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Partikel-Animation
    particlesController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );

    // Exit-Animation Controller
    exitAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Karten-Skalierungs-Animation
    cardScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: cardController, curve: Curves.easeOutQuint),
    );

    // Icon-Skalierungs-Animation
    iconScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: iconController, curve: Curves.elasticOut),
    );

    // Text-Opazitäts-Animation
    textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: textController, curve: Curves.easeInOut));

    // Text-Slide-Animation
    textSlideAnimation = Tween<double>(begin: 30.0, end: 0.0).animate(
      CurvedAnimation(parent: textController, curve: Curves.easeOutQuint),
    );

    // Partikel-Animation
    particlesAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: particlesController, curve: Curves.linear),
    );

    // Exit-Animationen
    exitOffsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-1.5, 1.0), // Nach links unten
    ).animate(
      CurvedAnimation(
        parent: exitAnimationController,
        curve: Curves.easeInQuint,
      ),
    );

    exitOpacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: exitAnimationController, curve: Curves.easeOut),
    );

    // Generiere Partikel
    generateParticles();

    // Starte die Animationen
    startAnimations();

    // Sofort die Navigation freigeben, ohne Timer
    if (mounted) {
      widget.onAnimationComplete();
    }

    // Listener für die Exit-Animation
    exitAnimationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Wenn die Exit-Animation abgeschlossen ist, rufe onAnimationComplete auf
        if (mounted) {
          widget.onAnimationComplete();
        }
      }
    });
  }

  void generateParticles() {
    // Mehr Partikel für eine dichtere Animation
    for (int i = 0; i < 40; i++) {
      // Verschiedene Partikeltypen für mehr Vielfalt
      final particleType = random.nextInt(3); // 0: normal, 1: schnell, 2: groß

      double speed;
      double size;
      double opacity;

      switch (particleType) {
        case 0: // Normale Partikel
          speed = random.nextDouble() * 0.4 + 0.2; // Etwas schneller
          size = random.nextDouble() * 5 + 2;
          opacity = random.nextDouble() * 0.5 + 0.2;
          break;
        case 1: // Schnelle Partikel
          speed = random.nextDouble() * 0.6 + 0.3;
          size = random.nextDouble() * 3 + 1;
          opacity = random.nextDouble() * 0.3 + 0.1;
          break;
        case 2: // Große Partikel
          speed = random.nextDouble() * 0.3 + 0.1;
          size = random.nextDouble() * 8 + 4;
          opacity = random.nextDouble() * 0.4 + 0.1;
          break;
        default:
          speed = random.nextDouble() * 0.4 + 0.2;
          size = random.nextDouble() * 5 + 2;
          opacity = random.nextDouble() * 0.5 + 0.2;
      }

      // Partikel hauptsächlich von unten nach oben für Raketen-Effekt
      double angle;
      if (random.nextDouble() < 0.7) {
        // 70% der Partikel bewegen sich nach oben (Gegenrichtung zur Rakete)
        angle =
            random.nextDouble() * math.pi +
            math.pi; // Bereich: π bis 2π (untere Hälfte)
      } else {
        // 30% der Partikel bewegen sich in zufällige Richtungen
        angle = random.nextDouble() * 2 * math.pi;
      }

      particles.add(
        Particle(
          position: Offset(
            random.nextDouble() * 400 - 200,
            random.nextDouble() * 400 - 200,
          ),
          size: size,
          color: getRandomColor(),
          speed: speed,
          angle: angle,
          opacity: opacity,
          blur: random.nextDouble() < 0.3, // 30% der Partikel haben Blur-Effekt
        ),
      );
    }
  }

  Color getRandomColor() {
    // Mehr Farbvariationen
    final baseColors = [
      Colors.white,
      Colors.purple.shade200,
      Colors.purple.shade300,
      Colors.deepPurple.shade200,
      Colors.deepPurple.shade300,
      Colors.indigo.shade200,
    ];

    // Zufällige Farbe mit zufälliger Transparenz für mehr Variation
    final baseColor = baseColors[random.nextInt(baseColors.length)];
    final alpha =
        (random.nextDouble() * 80 + 40).toInt(); // Alpha zwischen 40 und 120

    return baseColor.withAlpha(alpha);
  }

  void startAnimations() {
    // Starte die Hintergrund-Animation
    backgroundController.repeat(reverse: true);

    // Starte die Partikel-Animation
    particlesController.repeat();

    // Starte die Karten-Animation nach einer kurzen Verzögerung
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        cardController.forward();
      }
    });

    // Starte die Icon-Animation nach der Karten-Animation
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        iconController.forward();
      }
    });

    // Starte die Text-Animation nach der Icon-Animation
    Future.delayed(const Duration(milliseconds: 900), () {
      if (mounted) {
        textController.forward();
      }
    });
  }

  // Methode zum Starten der Exit-Animation
  void startExitAnimation() {
    if (!isExiting) {
      setState(() {
        isExiting = true;
      });

      // Stoppe den Timer, da wir jetzt die Exit-Animation verwenden
      completionTimer?.cancel();

      // Starte die Exit-Animation
      exitAnimationController.forward();

      // Starte auch die Exit-Animation im FloatingElementsBackground
      final FloatingElementsBackgroundState? backgroundState =
          _findFloatingElementsBackgroundState(context);
      if (backgroundState != null) {
        backgroundState.startExitAnimation(
          const Offset(-1.0, 0.5),
        ); // Nach links unten
      }
    }
  }

  // Findet den FloatingElementsBackground im Widget-Baum
  FloatingElementsBackgroundState? _findFloatingElementsBackgroundState(
    BuildContext context,
  ) {
    FloatingElementsBackgroundState? result;

    // Durchsuche den Widget-Baum nach dem FloatingElementsBackground
    void visitor(Element element) {
      if (element.widget is FloatingElementsBackground) {
        // Wenn wir den Background gefunden haben, speichere den State
        final statefulElement = element as StatefulElement;
        final state = statefulElement.state;
        if (state is FloatingElementsBackgroundState) {
          result = state;
        }
      }

      // Wenn wir noch keinen State gefunden haben, besuche alle Kinder
      if (result == null) {
        element.visitChildren(visitor);
      }
    }

    // Starte die Suche beim Root-Element
    final rootElement = context.findRootAncestorStateOfType<State>()?.context;
    if (rootElement != null) {
      rootElement.visitChildElements(visitor);
    }

    return result;
  }

  @override
  void dispose() {
    backgroundController.dispose();
    cardController.dispose();
    iconController.dispose();
    textController.dispose();
    particlesController.dispose();
    exitAnimationController.dispose();
    completionTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    // Wende die Exit-Animation auf den gesamten Inhalt an
    return AnimatedBuilder(
      animation: exitAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: exitOffsetAnimation.value,
          child: Opacity(opacity: exitOpacityAnimation.value, child: child),
        );
      },
      child: FloatingElementsBackground(
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Animierte Partikel für den Raketen-Effekt
            AnimatedBuilder(
              animation: particlesController,
              builder: (context, child) {
                return CustomPaint(
                  painter: ParticlesPainter(
                    particles: particles,
                    animation: particlesAnimation.value,
                    isExiting: isExiting,
                    exitProgress: exitAnimationController.value,
                  ),
                  size: Size.infinite,
                );
              },
            ),

            // Hauptinhalt
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Animierter Titel
                  AnimatedBuilder(
                    animation: textController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: textOpacityAnimation.value,
                        child: Transform.translate(
                          offset: Offset(0, -textSlideAnimation.value),
                          child: Text(
                            'Alles bereit!',
                            textAlign: TextAlign.center,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withAlpha(100),
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 30),

                  // Animierte Rakete mit Flugeffekt
                  AnimatedBuilder(
                    animation: Listenable.merge([
                      iconController,
                      backgroundController,
                    ]),
                    builder: (context, child) {
                      // Leichte Bewegung für Flugeffekt
                      final hoverOffset =
                          math.sin(backgroundController.value * math.pi * 2) *
                          5.0;

                      return Transform.translate(
                        offset: Offset(
                          0,
                          hoverOffset,
                        ), // Leichte Auf- und Abbewegung
                        child: Transform.scale(
                          scale: iconScaleAnimation.value,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(30),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withAlpha(100),
                                width: 2,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryColor.withAlpha(100),
                                  blurRadius: 15,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Leichter Glüh-Effekt hinter der Rakete
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: RadialGradient(
                                      colors: [
                                        Colors.white.withAlpha(100),
                                        Colors.transparent,
                                      ],
                                      stops: const [0.3, 1.0],
                                    ),
                                  ),
                                ),
                                // Raketen-Icon
                                Icon(
                                  Icons.rocket_launch_rounded,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 30),

                  // Animierte Karte mit Text
                  AnimatedBuilder(
                    animation: cardController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: cardScaleAnimation.value,
                        child: Opacity(
                          opacity: cardScaleAnimation.value,
                          child: Container(
                            width: size.width * 0.85,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 20,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(20),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.white.withAlpha(40),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(30),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Text(
                              'Du kannst jetzt mit der Jobsuche beginnen und deine ersten Bewerbungen erstellen.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                height: 1.4,
                                letterSpacing: 0.2,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Widget zur Darstellung der Vorteile der App im Onboarding
class BenefitsWidget extends StatelessWidget {
  const BenefitsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.borderDarkColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Warum du deinen Lebenslauf hochladen solltest:',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),

          // Vorteile als Liste
          ..._buildBenefitItems(context),

          const SizedBox(height: AppTheme.spacingLarge),

          // Zusammenfassung
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              border: Border.all(
                color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              'Mit nur drei Klicks ist deine Bewerbung abgeschickt – spare Zeit und erhöhe deine Chancen!',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBenefitItems(BuildContext context) {
    final benefits = [
      {
        'icon': Icons.speed,
        'title': 'Schneller Bewerbungsprozess',
        'description':
            'Dein Lebenslauf wird automatisch mit jeder Bewerbung abgeschickt, ohne dass du ihn jedes Mal neu hochladen musst.',
      },
      {
        'icon': Icons.auto_awesome,
        'title': 'Personalisierte Anschreiben',
        'description':
            'Die KI analysiert deinen Lebenslauf und erstellt maßgeschneiderte Anschreiben, die perfekt zu deinen Qualifikationen passen.',
      },
      {
        'icon': Icons.search,
        'title': 'Bessere Jobvorschläge',
        'description':
            'Basierend auf deinen Fähigkeiten und Erfahrungen werden dir relevantere Stellenangebote angezeigt.',
      },
      {
        'icon': Icons.trending_up,
        'title': 'Höhere Erfolgschancen',
        'description':
            'Mit einem vollständig analysierten Profil steigen deine Chancen auf positive Rückmeldungen von Arbeitgebern.',
      },
    ];

    return benefits.map((benefit) {
      return Padding(
        padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingSmall),
              decoration: BoxDecoration(
                color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(
                  AppTheme.borderRadiusMedium,
                ),
              ),
              child: Icon(
                benefit['icon'] as IconData,
                color: AppTheme.primaryLightColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    benefit['title'] as String,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXSmall),
                  Text(
                    benefit['description'] as String,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }).toList();
  }
}

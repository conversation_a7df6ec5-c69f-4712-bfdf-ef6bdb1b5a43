import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/application/providers/onboarding_state_provider.dart';
import 'package:lottie/lottie.dart';

/// Ein verbessertes Widget für den Lebenslauf-Upload im Onboarding
class ResumeUploadWidget extends ConsumerStatefulWidget {
  final VoidCallback? onUploadComplete;

  const ResumeUploadWidget({super.key, this.onUploadComplete});

  @override
  ConsumerState<ResumeUploadWidget> createState() => _ResumeUploadWidgetState();
}

class _ResumeUploadWidgetState extends ConsumerState<ResumeUploadWidget>
    with SingleTickerProviderStateMixin {
  bool _isUploading = false;
  late AnimationController _animationController;
  late Animation<double> _uploadAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _uploadAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickAndUploadFile() async {
    setState(() {
      _isUploading = true;
    });

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          // Animiere den Upload-Prozess
          _animationController.forward();

          // Simuliere einen Ladevorgang
          await Future.delayed(const Duration(seconds: 2));

          // Aktualisiere den Pfad im Onboarding-State
          ref.read(onboardingStateProvider.notifier).updateCvPath(filePath);

          // Benachrichtige über den abgeschlossenen Upload
          if (widget.onUploadComplete != null) {
            widget.onUploadComplete!();
          }
        }
      }
    } catch (e) {
      debugPrint('Fehler beim Datei-Upload: $e');
      // Zeige einen Fehler-Dialog an
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Fehler beim Hochladen des Lebenslaufs'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
        // Setze die Animation zurück
        _animationController.reset();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cvPath = ref.watch(
      onboardingStateProvider.select((state) => state.cvFilePath),
    );
    final hasFile = cvPath != null && cvPath.isNotEmpty;
    final fileName = hasFile ? path.basename(cvPath) : null;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color:
              hasFile
                  ? AppTheme.successColor.withValues(alpha: 0.5)
                  : AppTheme.borderDarkColor.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon und Titel
          if (!hasFile && !_isUploading)
            Column(
              children: [
                const Icon(
                  Icons.upload_file,
                  color: AppTheme.primaryLightColor,
                  size: 48,
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Lebenslauf hochladen',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  'Lade deinen Lebenslauf hoch, um die besten Ergebnisse zu erzielen',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),

          // Ladeanimation
          if (_isUploading)
            Column(
              children: [
                SizedBox(
                  height: 120,
                  width: 120,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Lottie-Animation könnte hier verwendet werden
                      CircularProgressIndicator(
                        value: _uploadAnimation.value,
                        strokeWidth: 4,
                        color: AppTheme.primaryLightColor,
                        backgroundColor: Colors.grey.withValues(alpha: 0.3),
                      ),
                      Icon(
                        Icons.upload_file,
                        size: 40,
                        color: AppTheme.primaryLightColor.withOpacity(
                          _uploadAnimation.value,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Lebenslauf wird hochgeladen...',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.white),
                ),
              ],
            ),

          // Erfolgreicher Upload
          if (hasFile && !_isUploading)
            Column(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppTheme.successColor,
                  size: 48,
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Lebenslauf hochgeladen',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  fileName ?? '',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),

          const SizedBox(height: AppTheme.spacingLarge),

          // Upload-Button
          ElevatedButton.icon(
            onPressed: _isUploading ? null : _pickAndUploadFile,
            icon: Icon(hasFile ? Icons.refresh : Icons.upload_file, size: 20),
            label: Text(
              hasFile ? 'Lebenslauf aktualisieren' : 'Lebenslauf auswählen',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryLightColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingLarge,
                vertical: AppTheme.spacingMedium,
              ),
              minimumSize: const Size(200, 50),
            ),
          ),

          if (!hasFile)
            TextButton(
              onPressed: () {
                // Hier könnte eine Aktion für "Später hochladen" implementiert werden
              },
              child: Text(
                'Später hochladen',
                style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
              ),
            ),
        ],
      ),
    );
  }
}

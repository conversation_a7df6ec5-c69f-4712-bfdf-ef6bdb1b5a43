import 'dart:async';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:location/location.dart' as loc;
import 'package:geocoding/geocoding.dart';

import '../../../application/providers/job_search_provider.dart';
import '../../../core/theme/app_theme.dart';
// import '../../../core/widgets/error_display.dart'; // Auskommentiert, da nicht gefunden
// Widget für Filter/Sortierung
import '../widgets/search_bar.dart'
    as CustomSearchBar; // Präfix verwenden, um Konflikt zu lösen
import '../widgets/animated_job_list.dart'; // Neue animierte Jobliste
import '../../../core/l10n/app_localizations_wrapper.dart';
import '../../shared/widgets/error_display_widget.dart';
// import '../widgets/job_list_item.dart'; // Entfernt (war Zeile 12 - Duplikat)
// import '../widgets/search_bar_widget.dart'; // Entfernt (war Zeile 13 - Falsch/Unbenutzt)
// VERSUCH: Absoluter Package-Import (wieder aktiviert) - ENTFERNT, da Datei gelöscht
// import 'package:ai_job_assistant_new/src/presentation/settings/screens/ki_optimization_screen.dart';
// VERSUCH: Absoluter Package-Import mit neuem Dateinamen (BEIBEHALTEN) - ENTFERNT, da Datei verschoben
// import 'package:ai_job_assistant_new/src/presentation/settings/screens/ki_settings_page.dart';

// NEUER VERSUCH: Import vom neuen Ort (direkt unter presentation) - ENTFERNT, da Build fehlschlägt
// import 'package:ai_job_assistant_new/src/presentation/ki_settings_page.dart';

// Import für die Einstellungsseite (angenommen sie ist im settings-Ordner)
// import '../../settings/screens/ki_settings_screen.dart';

import '../../../application/providers/user_profile_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import '../../../domain/models/job_search_settings.dart'; // Doppelt entfernt
// import '../widgets/job_card.dart'; // Doppelt entfernt
// <-- Korrigierter Pfad angenommen
import './location_input_dialog.dart'; // Import für den neuen Dialog
import '../../../core/utils/logging.dart'; // Logging importieren
// import '../../../application/providers/ai_search_provider.dart'; // Entfernt: AI Search Provider
import '../../../presentation/common/widgets/remaining_applications_widget.dart'; // Import für den Bewerbungszähler
import '../../../presentation/common/widgets/anti_manipulation_warning_widget.dart'; // Import für Anti-Manipulation-Warnung
import 'package:ki_test/src/core/optimization/scroll_performance_optimizer.dart';

class JobSearchScreen extends HookConsumerWidget {
  const JobSearchScreen({super.key});

  static final log = getLogger('JobSearchScreen');

  // Keys für SharedPreferences
  static const String _prefsKeyLocation = 'last_search_location';
  static const String _prefsKeyDistance = 'last_search_distance';
  static const String _prefsKeyLocationTimestamp =
      'last_location_timestamp'; // NEU

  // --- NEUE HILFSFUNKTIONEN ---
  // Refined Load
  Future<Map<String, dynamic>> _loadLastSearchSettings() async {
    // Rückgabe auf dynamic geändert
    try {
      final prefs = await SharedPreferences.getInstance();
      // Explicitly reload to try and avoid stale data
      await prefs.reload();
      final location = prefs.getString(_prefsKeyLocation);
      final distance = prefs.getString(_prefsKeyDistance);
      final timestamp = prefs.getInt(
        _prefsKeyLocationTimestamp,
      ); // Lade Timestamp
      print(
        "### [Prefs Load] Success: Location='$location', Distance='$distance', Timestamp='$timestamp' ###",
      );
      return {
        'location': location,
        'distance': distance,
        'timestamp': timestamp,
      };
    } catch (e, stackTrace) {
      log.e(
        "Fehler beim Laden der SharedPreferences",
        error: e,
        stackTrace: stackTrace,
      );
      // Fallback-Werte zurückgeben
      return {'location': null, 'distance': null, 'timestamp': null};
    }
  }

  // Refined Save
  Future<void> _saveLastSearchSettings(
    String? location,
    String? distance, {
    bool updateTimestamp = true,
  }) async {
    // Flag für Timestamp
    try {
      final prefs = await SharedPreferences.getInstance();
      bool locOpSuccess = false;
      bool distOpSuccess = false;
      bool timeOpSuccess = false;

      if (location != null && location.isNotEmpty) {
        locOpSuccess = await prefs.setString(_prefsKeyLocation, location);
        print(
          "### [Prefs Save] Attempting to save Location '$location': ${locOpSuccess ? 'Success' : 'Failed'} ###",
        );
        if (updateTimestamp) {
          // Speichere Timestamp nur, wenn Flag gesetzt ist
          final now = DateTime.now().millisecondsSinceEpoch;
          timeOpSuccess = await prefs.setInt(_prefsKeyLocationTimestamp, now);
          print(
            "### [Prefs Save] Attempting to save Timestamp '$now': ${timeOpSuccess ? 'Success' : 'Failed'} ###",
          );
        }
      } else {
        locOpSuccess = await prefs.remove(_prefsKeyLocation);
        // Entferne auch den Timestamp, wenn der Ort entfernt wird
        timeOpSuccess = await prefs.remove(_prefsKeyLocationTimestamp);
        print(
          "### [Prefs Save] Attempting to remove Location: ${locOpSuccess ? 'Success' : 'Failed'} ###",
        );
        print(
          "### [Prefs Save] Attempting to remove Timestamp: ${timeOpSuccess ? 'Success' : 'Failed'} ###",
        );
      }

      if (distance != null) {
        distOpSuccess = await prefs.setString(_prefsKeyDistance, distance);
        print(
          "### [Prefs Save] Attempting to save Distance '$distance': ${distOpSuccess ? 'Success' : 'Failed'} ###",
        );
      } else {
        distOpSuccess = await prefs.remove(_prefsKeyDistance);
        print(
          "### [Prefs Save] Attempting to remove Distance: ${distOpSuccess ? 'Success' : 'Failed'} ###",
        );
      }
    } catch (e, stackTrace) {
      log.e(
        "Fehler beim Speichern der SharedPreferences",
        error: e,
        stackTrace: stackTrace,
      );
      // Bei Speicherfehlern können wir nicht viel tun, aber wir loggen es proper
    }
  }
  // --- ENDE NEUE HILFSFUNKTIONEN ---

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('JobSearchScreen.build');
    log.d("Building JobSearchScreen");
    final searchState = ref.watch(jobSearchProvider);
    final searchNotifier = ref.read(jobSearchProvider.notifier);

    // Verwende useMemoized für den ScrollController, um sicherzustellen, dass er nur einmal erstellt wird
    final scrollController = useMemoized(() => ScrollController(), []);

    // Stelle sicher, dass der Controller entsorgt wird, wenn das Widget entfernt wird
    useEffect(() {
      return () {
        scrollController.dispose();
      };
    }, [scrollController]);

    final textEditingController = useTextEditingController(
      text: searchState.currentKeywords ?? '',
    );

    // Benutzer-Profil für Berufsbezeichnungen laden
    final userProfileAsync = ref.watch(userProfileProvider);
    final userProfile = userProfileAsync.valueOrNull;

    // State für das Ein-/Ausklappen des Filterfensters - jetzt über Provider
    // Wird vom Standortfeld-Pfeil und anderen Elementen gemeinsam genutzt
    final isFilterPanelCollapsed = ref.watch(jobSearchFilterPanelStateProvider);

    // Animation Controller für flüssige Animation
    final animationController = useAnimationController(
      duration: const Duration(
        milliseconds: 500,
      ), // Längere Animation für flüssigeren Effekt
      initialValue: 1.0, // Anfangs ausgeklappt
    );

    // Effekt, um den AnimationController mit dem isFilterPanelCollapsed-State zu synchronisieren
    useEffect(() {
      if (isFilterPanelCollapsed) {
        animationController.reverse(); // Von 1.0 zu 0.0 animieren (einklappen)
      } else {
        animationController.forward(); // Von 0.0 zu 1.0 animieren (ausklappen)
      }
      return null;
    }, [isFilterPanelCollapsed]);

    // Hooks sollten *immer* den aktuellen State widerspiegeln
    final noExperienceFilterActive = useState(
      searchState.currentFilters['noExperienceJobs'] == 'true',
    );
    useEffect(() {
      noExperienceFilterActive.value =
          searchState.currentFilters['noExperienceJobs'] == 'true';
      return null;
    }, [searchState.currentFilters['noExperienceJobs']]);

    final sortByProfileActive = useState(
      searchState.currentSortOption == 'profil',
    );
    useEffect(() {
      sortByProfileActive.value = searchState.currentSortOption == 'profil';
      return null;
    }, [searchState.currentSortOption]);

    // Aktiver Berufsfilter
    final activeJobKeywordFilter = useState<String?>(null);
    useEffect(() {
      // Wenn die Suche zurückgesetzt wird, setze auch den aktiven Berufsfilter zurück
      if (searchState.currentKeywords == null ||
          searchState.currentKeywords!.isEmpty) {
        activeJobKeywordFilter.value = null;
      }
      return null;
    }, [searchState.currentKeywords]);

    final initialDistance =
        double.tryParse(searchState.currentFilters['distance'] ?? '25') ?? 25.0;
    final clampedInitialDistance = initialDistance.clamp(1.0, 100.0);
    final distanceValue = useState<double>(clampedInitialDistance);
    useEffect(() {
      final loadedDistance =
          double.tryParse(searchState.currentFilters['distance'] ?? '25') ??
          25.0;
      if ((loadedDistance.clamp(1.0, 100.0) - distanceValue.value).abs() >
          0.1) {
        distanceValue.value = loadedDistance.clamp(1.0, 100.0);
      }
      return null;
    }, [searchState.currentFilters['distance']]);

    // Effekt für Scroll Listener & einmalige/periodische Standortermittlung
    useEffect(() {
      log.d("Initialer useEffect für Standort & Scroll Listener");

      Future<void> initializeLocationAndSearch() async {
        final prefsData = await _loadLastSearchSettings();
        final String? savedLocation = prefsData['location'];
        final int? savedTimestamp = prefsData['timestamp'];
        final String? savedDistance =
            prefsData['distance']; // Distanz auch laden
        Map<String, String> initialFilters = {};
        bool locationNeedsUpdate = false;

        log.d(
          "Geladene Prefs: Location=$savedLocation, Timestamp=$savedTimestamp, Distance=$savedDistance",
        );

        // SOFORTIGE SUCHE MIT GESPEICHERTEM STANDORT (falls vorhanden)
        if (savedLocation != null && savedLocation.isNotEmpty) {
          initialFilters['location'] = savedLocation;
          if (savedDistance != null) {
            initialFilters['distance'] = savedDistance;
          }

          // Sofort Jobs mit gespeichertem Standort laden
          log.i(
            "Starte sofortige Suche mit gespeichertem Standort: $savedLocation",
          );
          searchNotifier.triggerSearch(filters: initialFilters);

          // Prüfe ob Standort-Update nötig ist
          if (savedTimestamp != null) {
            final lastUpdate = DateTime.fromMillisecondsSinceEpoch(
              savedTimestamp,
            );
            final threeDaysAgo = DateTime.now().subtract(
              const Duration(days: 3),
            );
            if (lastUpdate.isBefore(threeDaysAgo)) {
              log.i(
                "Gespeicherter Standort ist älter als 3 Tage. Update im Hintergrund...",
              );
              locationNeedsUpdate = true;
            } else {
              log.i(
                "Gespeicherter Standort ist aktuell genug. Kein Update nötig.",
              );
            }
          } else {
            log.w(
              "Kein Zeitstempel für gespeicherten Standort gefunden. Update im Hintergrund...",
            );
            locationNeedsUpdate = true;
          }

          // HINTERGRUND-UPDATE (falls nötig)
          if (locationNeedsUpdate) {
            log.d("Starte Standort-Update im Hintergrund...");
            _updateLocationInBackground(
              context,
              searchNotifier,
              initialFilters,
            );
          }
        } else {
          // Kein gespeicherter Standort - erstmalige Ermittlung erforderlich
          log.i("Kein Standort gespeichert. Starte erstmalige Ermittlung...");
          if (!context.mounted) return;
          final detectedLocation = await _determineLocationInBackground(
            context,
            searchNotifier,
          );

          if (!context.mounted) return;
          // Nur suchen, wenn noch kein Standort in den Filtern vorhanden ist
          if (initialFilters['location'] == null ||
              initialFilters['location']!.isEmpty) {
            if (detectedLocation != null) {
              log.i("Erstmaliger Standort erkannt: $detectedLocation");
              initialFilters['location'] = detectedLocation;
              log.i("Löse initiale Suche aus mit Filtern: $initialFilters");
              searchNotifier.triggerSearch(filters: initialFilters);
            } else {
              log.w("Standortermittlung fehlgeschlagen. Suche ohne Standort.");
              searchNotifier.triggerSearch(filters: initialFilters);
            }
          } else {
            log.d("Standort bereits vorhanden, überspringe doppelte Suche.");
          }
        }
      }

      // Führe die Initialisierungslogik nach dem ersten Frame aus
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          initializeLocationAndSearch();
        }
      });

      // PERFORMANCE FIX: Optimierter Scroll Listener mit Throttling
      final optimizedScrollListener =
          ScrollPerformanceOptimizer.createOptimizedScrollListener(
            listenerId: 'job_search_screen',
            onEndReached: () {
              final currentState = ref.read(jobSearchProvider);
              if (!currentState.isLoadingMore && !currentState.hasReachedMax) {
                log.d("Reached end of list, loading more jobs...");
                searchNotifier.loadMoreJobs();
              }
            },
            scrollController: scrollController,
            endReachedThreshold: 0.9,
            endReachedThrottle: const Duration(milliseconds: 1000),
          );

      scrollController.addListener(optimizedScrollListener);
      return () => scrollController.removeListener(optimizedScrollListener);
    }, [scrollController]); // Abhängigkeit nur vom ScrollController

    // === Ladezustand & Fehlerbehandlung für den initialen Screen-Aufbau ===
    // Zeige den Ladeindikator nur beim allerersten Laden der App, bevor irgendwelche UI-Elemente angezeigt werden
    // Dies ist nur für den Fall, dass die App gerade gestartet wurde und noch keine UI aufgebaut wurde
    final bool isVeryFirstLoading =
        searchState.isLoading &&
        searchState.jobs.isEmpty &&
        searchState.error == null &&
        searchState.currentKeywords ==
            null && // Wichtig: Nur beim ersten Start, nicht bei Suchen
        searchState
            .currentFilters
            .isEmpty; // Wichtig: Nur beim ersten Start, nicht bei Suchen

    if (isVeryFirstLoading) {
      log.d("Build: Very first initial loading state...");
      // Nur beim allerersten Laden der App zeigen wir einen Vollbild-Ladeindikator
      return Scaffold(
        appBar: AppBar(
          title: Text(AppLocalizationsWrapper.of(context).jobSearchTitle),
          elevation: 0,
          foregroundColor: Colors.white, // Beispiel Annahme
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ),
        body: Center(child: CircularProgressIndicator()),
      );
    }

    // Fehleranzeige wird jetzt in der AnimatedJobList angezeigt, nicht mehr als eigene Seite
    // Dadurch bleibt die Tastatur geöffnet, wenn ein Fehler auftritt
    // Die Fehlerbehandlung erfolgt jetzt in der AnimatedJobList-Klasse

    return GestureDetector(
      // PERFORMANCE FIX: Optimierte Gesture-Handler mit Debouncing
      onTap: () {
        ScrollPerformanceOptimizer.handleScrollWithDebouncing(
          operationId: 'job_search_screen_tap',
          operation: () {
            FocusManager.instance.primaryFocus?.unfocus();
            log.d("Tap auf Bildschirm erkannt - Fokus wird entfernt");
          },
          debounceDelay: const Duration(milliseconds: 100),
        );
      },
      onVerticalDragStart: (_) {
        ScrollPerformanceOptimizer.handleScrollWithDebouncing(
          operationId: 'job_search_screen_drag',
          operation: () {
            FocusManager.instance.primaryFocus?.unfocus();
            log.d("Vertikale Drag-Geste erkannt - Fokus wird entfernt");
          },
          debounceDelay: const Duration(milliseconds: 150),
        );
      },
      child: Scaffold(
        // Setze resizeToAvoidBottomInset auf true, damit die Tastatur die Anzeige nicht überlappt
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Text(AppLocalizationsWrapper.of(context).jobSearchTitle),
          elevation: 0,
          foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
          actions: [
            // Bewerbungszähler nur anzeigen, wenn der Benutzer kein Premium hat
            Consumer(
              builder: (context, ref, child) {
                final userProfileState = ref.watch(userProfileProvider);
                return userProfileState.when(
                  data: (userProfile) {
                    final planType = userProfile.premiumPlanType ?? 'basic';
                    // Nur für Basic und Pro Pläne den Bewerbungszähler anzeigen
                    if (planType != 'premium' && planType != 'unlimited') {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: Center(
                          child: RemainingApplicationsWidget(
                            compact: true,
                            showLabel: false,
                          ),
                        ),
                      );
                    } else {
                      // Bei Premium oder Unlimited keinen Bewerbungszähler anzeigen
                      return const SizedBox.shrink();
                    }
                  },
                  loading: () => const SizedBox.shrink(),
                  error: (_, __) => const SizedBox.shrink(),
                );
              },
            ),
          ],
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ),
        // Verwende SingleChildScrollView, um sicherzustellen, dass die Inhalte scrollbar sind
        body: SafeArea(
          // SafeArea verhindert Überlappungen mit Systemleisten
          child: Column(
            children: [
              // Standortanzeige mittig unter "Jobsuche"
              Padding(
                padding: const EdgeInsets.only(
                  top: AppTheme.spacingXSmall,
                  bottom: AppTheme.spacingXSmall,
                ),
                child: Center(
                  child: _buildLocationDisplay(
                    context,
                    ref,
                    searchState,
                    searchNotifier,
                  ),
                ),
              ),

              // Toggle zwischen Jobsuche und Ausbildungssuche
              Padding(
                padding: const EdgeInsets.only(
                  left: AppTheme.spacingMedium,
                  right: AppTheme.spacingMedium,
                  bottom: AppTheme.spacingSmall,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: Theme.of(context).colorScheme.surface,
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withAlpha(128),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (searchState.isAusbildungMode) {
                              searchNotifier.toggleAusbildungMode(
                                isAusbildungMode: false,
                              );
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.horizontal(
                                left: Radius.circular(25),
                              ),
                              color:
                                  !searchState.isAusbildungMode
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.transparent,
                            ),
                            child: Text(
                              'Jobs',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color:
                                    !searchState.isAusbildungMode
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.onPrimary
                                        : Theme.of(
                                          context,
                                        ).colorScheme.onSurface,
                                fontWeight:
                                    !searchState.isAusbildungMode
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (!searchState.isAusbildungMode) {
                              searchNotifier.toggleAusbildungMode(
                                isAusbildungMode: true,
                              );
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.horizontal(
                                right: Radius.circular(25),
                              ),
                              color:
                                  searchState.isAusbildungMode
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.transparent,
                            ),
                            child: Text(
                              'Ausbildung',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color:
                                    searchState.isAusbildungMode
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.onPrimary
                                        : Theme.of(
                                          context,
                                        ).colorScheme.onSurface,
                                fontWeight:
                                    searchState.isAusbildungMode
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Suchleiste - immer sichtbar
              Padding(
                padding: const EdgeInsets.only(
                  left: AppTheme.spacingMedium,
                  right: AppTheme.spacingMedium,
                  bottom: AppTheme.spacingSmall,
                ),
                child: CustomSearchBar.SearchBar(
                  hintText: AppLocalizationsWrapper.of(context).enterKeyword,
                  initialValue: searchState.currentKeywords,
                  onSubmitted: (query) {
                    log.d("Search submitted: $query");
                    // Keine zusätzliche Suche nötig, da bereits durch onChanged ausgelöst
                  },
                  onChanged: (query) {
                    log.d("Search query changed: $query");
                    // Suche *immer* auslösen, aber mit Debounce
                    // Die Suchleiste verhindert bereits doppelte Aufrufe mit demselben Wert

                    // Wichtig: Wir verwenden hier triggerSearch statt performSearch,
                    // damit die bestehenden Jobs während der Suche angezeigt bleiben
                    searchNotifier.triggerSearch(keywords: query);
                  },
                ),
              ),

              // Berufsbezeichnungen als Filter anzeigen, wenn vorhanden
              if (userProfile != null &&
                  (userProfile.jobKeywords ?? []).isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(
                    left: AppTheme.spacingMedium,
                    right: AppTheme.spacingMedium,
                    bottom: AppTheme.spacingSmall,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          '${AppLocalizationsWrapper.of(context).yourProfessions}:',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ),
                      SizedBox(
                        height:
                            36, // Etwas kleinere Höhe für die horizontale Liste
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: (userProfile.jobKeywords ?? []).length,
                          itemBuilder: (context, index) {
                            final keyword =
                                (userProfile.jobKeywords ?? [])[index];
                            final isSelected =
                                activeJobKeywordFilter.value == keyword;
                            return Padding(
                              padding: EdgeInsets.only(
                                right: 8.0,
                                left: index == 0 ? 0.0 : 0.0,
                              ),
                              child: FilterChip(
                                label: Text(keyword),
                                selected: isSelected,
                                onSelected: (selected) {
                                  // Aktuelle Filter beibehalten
                                  final currentFilters =
                                      Map<String, String>.from(
                                        searchState.currentFilters,
                                      );

                                  // Wenn bereits ausgewählt, deaktiviere den Filter
                                  if (isSelected) {
                                    activeJobKeywordFilter.value = null;
                                    // Suche ohne Schlüsselwort, aber mit aktuellen Filtern
                                    searchNotifier.triggerSearch(
                                      keywords: '',
                                      filters: currentFilters,
                                      sortOption:
                                          'profil', // Behalte Profil-Sortierung bei
                                    );
                                  } else {
                                    // Neuen Filter aktivieren
                                    activeJobKeywordFilter.value = keyword;
                                    // Suche mit dem Schlüsselwort und aktuellen Filtern
                                    searchNotifier.triggerSearch(
                                      keywords: keyword,
                                      filters: currentFilters,
                                      sortOption:
                                          'profil', // Aktiviere Profil-Sortierung
                                    );
                                  }
                                },
                                labelStyle: TextStyle(
                                  color:
                                      isSelected
                                          ? Theme.of(
                                            context,
                                          ).colorScheme.onSecondary
                                          : Theme.of(
                                            context,
                                          ).colorScheme.onSurface,
                                ),
                                backgroundColor:
                                    Theme.of(context).colorScheme.surface,
                                selectedColor:
                                    Theme.of(context).colorScheme.secondary,
                                checkmarkColor:
                                    Theme.of(context).colorScheme.onSecondary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16.0),
                                  side: BorderSide(
                                    color:
                                        isSelected
                                            ? Theme.of(
                                              context,
                                            ).colorScheme.secondary
                                            : Theme.of(context)
                                                .colorScheme
                                                .outline
                                                .withAlpha(128),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

              // Pfeil zum Ein-/Ausklappen des Filterfensters
              GestureDetector(
                onTap: () {
                  // Verwende den globalen Provider
                  final currentState = ref.read(
                    jobSearchFilterPanelStateProvider,
                  );
                  ref.read(jobSearchFilterPanelStateProvider.notifier).state =
                      !currentState;
                },
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: Icon(
                    ref.watch(jobSearchFilterPanelStateProvider)
                        ? Icons.keyboard_arrow_down
                        : Icons.keyboard_arrow_up,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),

              // Animiertes Filterfenster mit verbesserter Animation
              AnimatedSwitcher(
                duration: const Duration(
                  milliseconds: 600,
                ), // Noch langsamere Animation für mehr Flüssigkeit
                switchInCurve:
                    Curves
                        .easeInOutCubic, // Noch sanftere Kurve für flüssigere Bewegung
                switchOutCurve:
                    Curves
                        .easeInOutCubic, // Noch sanftere Kurve für flüssigere Bewegung
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: CurvedAnimation(
                      parent: animation,
                      curve: Interval(0.0, 1.0, curve: Curves.easeInOutCubic),
                    ),
                    child: SizeTransition(
                      sizeFactor: animation,
                      axisAlignment: -1.0, // Von oben nach unten
                      child: child,
                    ),
                  );
                },
                child:
                    isFilterPanelCollapsed
                        ? const SizedBox.shrink() // Leeres Widget wenn eingeklappt
                        : Container(
                          key: const ValueKey<String>(
                            'expanded_filter_panel',
                          ), // Wichtig für Animation
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            border: Border(
                              bottom: BorderSide(
                                color: Theme.of(
                                  context,
                                ).colorScheme.outline.withAlpha(25),
                                width: 1.0,
                              ),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingMedium,
                              vertical: AppTheme.spacingXSmall,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Distanz-Slider (nur wenn Standort aktiv)
                                if (searchState.currentFilters.containsKey(
                                  'location',
                                )) ...[
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Überschrift mit aktuellem Wert
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              '${AppLocalizationsWrapper.of(context).radius}:',
                                              style:
                                                  Theme.of(
                                                    context,
                                                  ).textTheme.titleSmall,
                                            ),
                                            Text(
                                              '${distanceValue.value.round()} ${AppLocalizationsWrapper.of(context).km}',
                                              style: Theme.of(
                                                context,
                                              ).textTheme.titleSmall?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        // Slider für die Distanz
                                        SliderTheme(
                                          data: SliderTheme.of(
                                            context,
                                          ).copyWith(
                                            activeTrackColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            inactiveTrackColor: Theme.of(
                                              context,
                                            ).colorScheme.primary.withAlpha(50),
                                            thumbColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            overlayColor: Theme.of(
                                              context,
                                            ).colorScheme.primary.withAlpha(32),
                                            valueIndicatorColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            valueIndicatorTextStyle: TextStyle(
                                              color:
                                                  Theme.of(
                                                    context,
                                                  ).colorScheme.onPrimary,
                                            ),
                                            trackHeight: 4.0,
                                          ),
                                          child: Slider(
                                            value: distanceValue.value,
                                            min: 1.0,
                                            max: 100.0,
                                            divisions:
                                                99, // Für Schritte von 1 km
                                            label:
                                                '${distanceValue.value.round()} ${AppLocalizationsWrapper.of(context).km}',
                                            onChanged: (value) {
                                              // Aktualisiere nur den angezeigten Wert
                                              distanceValue.value = value;
                                            },
                                            onChangeEnd: (value) {
                                              // Aktualisiere die Suche, wenn der Slider losgelassen wird
                                              final roundedValue =
                                                  value.round();
                                              final currentDistance =
                                                  int.tryParse(
                                                    searchState
                                                            .currentFilters['distance'] ??
                                                        '25',
                                                  ) ??
                                                  25;

                                              if (roundedValue !=
                                                  currentDistance) {
                                                final currentFilters =
                                                    Map<String, String>.from(
                                                      searchState
                                                          .currentFilters,
                                                    );
                                                currentFilters['distance'] =
                                                    roundedValue.toString();

                                                // Debug-Ausgabe
                                                final log = getLogger(
                                                  'JobSearchScreen.Slider',
                                                );
                                                final currentSortOption =
                                                    searchState
                                                        .currentSortOption;
                                                log.d(
                                                  "Umkreis geändert auf: $roundedValue km (Sortierung: $currentSortOption)",
                                                );

                                                // Direkt die Suche auslösen ohne Dialog
                                                searchNotifier.triggerSearch(
                                                  filters: currentFilters,
                                                  sortOption: currentSortOption,
                                                );
                                              }
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],

                                // Filter und Sortieroptionen in einer übersichtlichen Anordnung
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Filter-Überschrift
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        bottom: 4.0,
                                      ),
                                      child: Text(
                                        '${AppLocalizationsWrapper.of(context).filter}:',
                                        style:
                                            Theme.of(
                                              context,
                                            ).textTheme.titleSmall,
                                      ),
                                    ),

                                    // Filter-Chips in einer Row
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        bottom: 8.0,
                                      ),
                                      child: Wrap(
                                        spacing: 8.0,
                                        children: [
                                          // Filter: Ohne Vorerfahrung
                                          FilterChip(
                                            label: Text(
                                              AppLocalizationsWrapper.of(
                                                context,
                                              ).withoutExperience,
                                            ),
                                            selected:
                                                noExperienceFilterActive.value,
                                            onSelected: (selected) {
                                              noExperienceFilterActive.value =
                                                  selected;
                                              final currentFilters =
                                                  Map<String, String>.from(
                                                    searchState.currentFilters,
                                                  );
                                              if (selected) {
                                                currentFilters['noExperienceJobs'] =
                                                    'true';
                                              } else {
                                                currentFilters.remove(
                                                  'noExperienceJobs',
                                                );
                                              }
                                              searchNotifier.triggerSearch(
                                                filters: currentFilters,
                                              );
                                            },
                                            labelStyle: TextStyle(
                                              color:
                                                  noExperienceFilterActive.value
                                                      ? Theme.of(
                                                        context,
                                                      ).colorScheme.onSecondary
                                                      : Theme.of(
                                                        context,
                                                      ).colorScheme.onSurface,
                                            ),
                                            backgroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.surface,
                                            selectedColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.secondary,
                                            checkmarkColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.onSecondary,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(16.0),
                                              side: BorderSide(
                                                color:
                                                    noExperienceFilterActive
                                                            .value
                                                        ? Theme.of(
                                                          context,
                                                        ).colorScheme.secondary
                                                        : Theme.of(context)
                                                            .colorScheme
                                                            .outline
                                                            .withAlpha(128),
                                              ),
                                            ),
                                          ),

                                          // Filter: Relevanz (nach Profil)
                                          FilterChip(
                                            label: Text(
                                              AppLocalizationsWrapper.of(
                                                context,
                                              ).relevance,
                                            ),
                                            selected:
                                                searchState.currentSortOption ==
                                                'profil',
                                            onSelected: (selected) {
                                              // Wenn ausgewählt, setze Sortierung auf 'profil', sonst auf 'entfernung'
                                              final sortOption =
                                                  selected
                                                      ? 'profil'
                                                      : 'entfernung';

                                              // Behalte alle aktuellen Filter bei
                                              final currentFilters =
                                                  Map<String, String>.from(
                                                    searchState.currentFilters,
                                                  );

                                              // Löse die Suche mit der neuen Sortierung aus
                                              searchNotifier.triggerSearch(
                                                filters: currentFilters,
                                                sortOption: sortOption,
                                              );
                                            },
                                            labelStyle: TextStyle(
                                              color:
                                                  searchState.currentSortOption ==
                                                          'profil'
                                                      ? Theme.of(
                                                        context,
                                                      ).colorScheme.onSecondary
                                                      : Theme.of(
                                                        context,
                                                      ).colorScheme.onSurface,
                                            ),
                                            backgroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.surface,
                                            selectedColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.secondary,
                                            checkmarkColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.onSecondary,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(16.0),
                                              side: BorderSide(
                                                color:
                                                    searchState.currentSortOption ==
                                                            'profil'
                                                        ? Theme.of(
                                                          context,
                                                        ).colorScheme.secondary
                                                        : Theme.of(context)
                                                            .colorScheme
                                                            .outline
                                                            .withAlpha(128),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Doppelte Anzeige der Berufe entfernt - bereits oben angezeigt
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
              ),

              Expanded(
                child: _buildJobList(
                  ref.watch(jobSearchFilterPanelStateProvider),
                  context,
                  ref,
                  searchState,
                  scrollController,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // --- HILFSMETHODE FÜR Standort-Anzeige ---
  Widget _buildLocationDisplay(
    BuildContext context,
    WidgetRef ref,
    JobSearchState state,
    JobSearchNotifier notifier,
  ) {
    final log = getLogger('JobSearchScreen._buildLocationDisplay');
    final location = state.currentFilters['location'];
    final displayLocation =
        location != null && location.isNotEmpty ? location : 'Ort wählen';
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    // Haupt-Container für Text und Icon-Button
    return Container(
      // Begrenzte Breite für zentrierte Anzeige
      width:
          MediaQuery.of(context).size.width * 0.6, // 60% der Bildschirmbreite
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMedium,
        vertical: AppTheme.spacingXSmall,
      ),
      decoration: BoxDecoration(
        color: colorScheme.primary.withAlpha(20), // 0.08 * 255 = ~20
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: colorScheme.primary.withAlpha(40),
          width: 1.0,
        ),
      ),
      child: InkWell(
        onTap: () async {
          // Manuelle Eingabe Logik
          final currentFilters = state.currentFilters;
          final currentLocation = currentFilters['location'];
          final currentDistance = currentFilters['distance'] ?? '25';

          final String? newLocation = await showDialog<String>(
            context: context,
            builder:
                (_) => LocationInputDialog(
                  initialLocation: currentLocation,
                  showLocationButton:
                      true, // Zeige den Standort-Button im Dialog
                ),
          );

          if (newLocation != null &&
              newLocation.isNotEmpty &&
              newLocation != currentLocation) {
            log.i("Neuer Standort gewählt: $newLocation");
            final newFilterMap = {'location': newLocation};
            if (currentDistance.isNotEmpty) {
              newFilterMap['distance'] = currentDistance;
            }
            notifier.triggerSearch(
              filters: newFilterMap,
            ); // Trigger Suche mit neuem Standort
            // Speichere neuen Standort UND neuen Zeitstempel
            await _saveLastSearchSettings(
              newLocation,
              currentDistance,
              updateTimestamp: true,
            );
          } else if (newLocation != null &&
              newLocation.isEmpty &&
              currentLocation != null) {
            log.i("Standortfilter entfernt.");
            final newFilterMap = Map<String, String>.from(currentFilters);
            newFilterMap.remove('location');
            notifier.triggerSearch(
              filters: newFilterMap,
            ); // Trigger Suche ohne Standort
            // Entferne Standort UND Zeitstempel
            await _saveLastSearchSettings(
              null,
              currentDistance,
              updateTimestamp: true,
            ); // Ruft remove für beides auf
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center, // Zentriere den Inhalt
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.location_on_outlined,
              size: 20,
              color: colorScheme.primary,
            ),
            const SizedBox(width: AppTheme.spacingXSmall),
            Text(
              displayLocation,
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // --- HILFSMETHODE FÜR JOB LISTE ---
  Widget _buildJobList(
    bool isFilterPanelCollapsed,
    BuildContext context,
    WidgetRef ref,
    JobSearchState state,
    ScrollController scrollController,
  ) {
    final log = getLogger('JobSearchScreen._buildJobList');

    // PERFORMANCE FIX: Optimierter Panel-Collapse-Listener mit Throttling
    final panelCollapseListener =
        ScrollPerformanceOptimizer.createOptimizedScrollListener(
          listenerId: 'filter_panel_collapse',
          onScroll: () {
            if (scrollController.position.pixels > 10 &&
                !isFilterPanelCollapsed) {
              ref.read(jobSearchFilterPanelStateProvider.notifier).state = true;
            }
          },
          scrollController: scrollController,
          scrollDebounce: const Duration(milliseconds: 200),
        );

    scrollController.addListener(panelCollapseListener);
    final searchNotifier = ref.read(jobSearchProvider.notifier);
    log.d(
      "Building job list. State: isLoading=${state.isLoading}, isLoadingMore=${state.isLoadingMore}, jobs=${state.jobs.length}, error=${state.error}, hasReachedMax=${state.hasReachedMax}",
    );

    final bool isFilterOrSortActive =
        state.currentFilters['noExperienceJobs'] == 'true' ||
        state.currentFilters['onlyHelperJobs'] == 'true' ||
        state.currentSortOption == 'profil';

    // Bedingung für den "Umkreis erweitern" Button
    final bool showExpandSearchButton =
        !state.isLoadingMore &&
        state.hasReachedMax &&
        state.jobs.isNotEmpty &&
        isFilterOrSortActive &&
        state.currentFilters.containsKey(
          'location',
        ); // Nur anzeigen, wenn Standort aktiv ist

    // Zeige "Keine Jobs gefunden", wenn keine Jobs da sind, nicht geladen wird und kein Fehler vorliegt.
    // Diese Bedingung wird jetzt nur erreicht, wenn die Suche abgeschlossen ist und 0 Ergebnisse lieferte.
    // Wichtig: Wir zeigen diese Meldung nur an, wenn wir NICHT mehr laden
    if (state.jobs.isEmpty &&
        !state.isLoading &&
        !state.isLoadingMore &&
        state.error == null) {
      log.i("No results found or list is empty after search completed.");
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.search_off, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                AppLocalizationsWrapper.of(context).noJobsFound,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Versuchen Sie es mit anderen Suchbegriffen oder Filtern.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Fehler anzeigen, WENN keine Jobs da sind
    if (state.error != null && state.jobs.isEmpty) {
      log.e("Error state and no jobs: ${state.error}");
      return ErrorDisplayWidget(
        error: state.error!,
        customTitle: 'Fehler beim Laden der Jobs',
        customIcon: Icons.cloud_off,
        onRetry: () {
          searchNotifier.performSearch(
            keywords: state.currentKeywords,
            filters: state.currentFilters,
            sortOption: state.currentSortOption,
          );
        },
      );
    }

    // Berechne den nächsten Suchradius für den "Umkreis erweitern" Button
    final currentDistance =
        int.tryParse(state.currentFilters['distance'] ?? '25') ?? 25;
    final nextDistance = (currentDistance + 10).clamp(
      10,
      150,
    ); // Erhöhe um 10, max 150km

    // Verwende die neue animierte Jobliste mit Anti-Manipulation-Warnung
    return Column(
      children: [
        // Anti-Manipulation-Warnung (falls erforderlich)
        const AntiManipulationWarningWidget(),

        // Hauptinhalt
        Expanded(
          child: AnimatedJobList(
            jobs: state.jobs,
            isLoading: state.isLoading,
            isLoadingMore: state.isLoadingMore,
            isSearching:
                state
                    .isSearching, // Übergebe isSearching an die AnimatedJobList
            error: state.error, // Übergebe den Fehler an die AnimatedJobList
            showExpandSearchButton: showExpandSearchButton,
            currentDistance: currentDistance,
            nextDistance: nextDistance,
            scrollController: scrollController,
            // Callback, wenn ein Job angeklickt wird - klappe das Filterfenster ein
            onJobTap: () {
              // Klappe das Filterfenster ein, wenn es nicht bereits eingeklappt ist
              if (!isFilterPanelCollapsed) {
                ref.read(jobSearchFilterPanelStateProvider.notifier).state =
                    true;
              }
            },
            onExpandSearchPressed: () {
              final newFilters = Map<String, String>.from(state.currentFilters);
              newFilters['distance'] = nextDistance.toString();
              log.i("Expanding search radius to $nextDistance km.");

              // Behalte die aktuelle Sortierung bei (wichtig für "Nach Profil sortieren")
              final currentSortOption = state.currentSortOption;
              searchNotifier.triggerSearch(
                filters: newFilters,
                sortOption:
                    currentSortOption, // Behalte die aktuelle Sortierung bei
              );
            },
            onRetry: () {
              // Suche erneut ausführen
              searchNotifier.performSearch(
                keywords: state.currentKeywords,
                filters: state.currentFilters,
                sortOption: state.currentSortOption,
              );
            },
          ),
        ),
      ],
    );
  }

  // --- NEU: HILFSMETHODE FÜR Standortberechtigung und -abfrage ---
  Future<String?> _requestLocationPermission(BuildContext context) async {
    final log = getLogger('JobSearchScreen._requestLocationPermission');
    loc.Location location = loc.Location();

    bool serviceEnabled;
    loc.PermissionStatus permissionGranted;
    loc.LocationData locationData;

    log.i("Prüfe Standortdienste...");
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      log.w("Standortdienste sind deaktiviert. Fordere Aktivierung an...");
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        log.e("Standortdienste wurden nicht aktiviert.");
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Bitte aktiviere die Standortdienste.'),
            ),
          );
        }
        return null;
      }
    }

    log.i("Prüfe Standortberechtigung...");
    permissionGranted = await location.hasPermission();
    if (permissionGranted == loc.PermissionStatus.denied) {
      log.w("Standortberechtigung nicht erteilt. Fordere Berechtigung an...");
      permissionGranted = await location.requestPermission();
      if (permissionGranted != loc.PermissionStatus.granted) {
        log.e("Standortberechtigung wurde nicht erteilt.");
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Standortberechtigung erforderlich.')),
          );
        }
        return null;
      }
    }

    if (permissionGranted == loc.PermissionStatus.granted) {
      try {
        log.i("Berechtigung erteilt. Ermittle Standort...");
        // Setze Genauigkeit für bessere Ergebnisse (optional)
        // location.changeSettings(accuracy: loc.LocationAccuracy.high);
        locationData = await location.getLocation();
        log.i(
          "Standortdaten erhalten: Lat=${locationData.latitude}, Lon=${locationData.longitude}",
        );

        if (locationData.latitude != null && locationData.longitude != null) {
          // Konvertiere Koordinaten in Ortsnamen (Reverse Geocoding)
          List<Placemark> placemarks = await placemarkFromCoordinates(
            locationData.latitude!,
            locationData.longitude!,
          );
          if (placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final city = placemark.locality;
            log.i("Ort ermittelt: $city");
            return city;
          } else {
            log.w("Kein Ortsname für Koordinaten gefunden.");
            return null;
          }
        } else {
          log.w("Ungültige Standortdaten erhalten.");
          return null;
        }
      } catch (e, stacktrace) {
        log.e("Fehler bei der Standortermittlung: $e", stackTrace: stacktrace);
        return null;
      }
    } else {
      log.e("Unbekannter Berechtigungsstatus: $permissionGranted");
      return null;
    }
  }

  // Neue Hilfsmethode zur Standortermittlung im Hintergrund
  // Gibt den erkannten Standort zurück oder null bei Fehler/Ablehnung
  Future<String?> _determineLocationInBackground(
    BuildContext context,
    JobSearchNotifier searchNotifier,
  ) async {
    final log = getLogger('JobSearchScreen._determineLocationInBackground');
    String? detectedLocation;
    // Map<String, String> initialFilters = {}; // Wird hier nicht mehr benötigt

    // Kurze Verzögerung, damit die UI aufgebaut werden kann
    // await Future.delayed(const Duration(milliseconds: 200)); // Nicht mehr nötig

    // if (!context.mounted) { // Prüfung erfolgt jetzt im Aufrufer
    //   log.w("Context nicht mehr gültig, breche Hintergrund-Standortermittlung ab.");
    //   return null;
    // }

    try {
      log.i("Standortbestimmung im Hintergrund gestartet");
      detectedLocation = await _requestLocationPermission(context);

      if (!context.mounted) {
        log.w("Context nach Standortermittlung nicht mehr gültig.");
        return null;
      }

      if (detectedLocation != null) {
        log.i("Standort im Hintergrund erkannt: $detectedLocation");

        // Speichere den neuen Standort UND den Zeitstempel
        // final currentDistance = searchNotifier.state.currentFilters['distance']; // Distanz wird nicht geändert
        // ÄNDERUNG: Lade die aktuell gespeicherte Distanz, um sie nicht zu überschreiben
        final prefsData = await _loadLastSearchSettings();
        final savedDistance = prefsData['distance'];
        await _saveLastSearchSettings(
          detectedLocation,
          savedDistance,
          updateTimestamp: true,
        ); // updateTimestamp explizit true

        // Meldung anzeigen
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Standort automatisch auf "$detectedLocation" gesetzt',
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return detectedLocation; // Gib den erkannten Standort zurück
      } else {
        log.w(
          "Standortermittlung im Hintergrund nicht erfolgreich oder kein Ort gefunden.",
        );
        return null;
      }
    } catch (e) {
      log.e("Fehler bei der Hintergrund-Standortermittlung: $e.");
      return null;
    }
    // finally { // Suche wird jetzt vom Aufrufer ausgelöst
    // LÖSE JETZT IMMER DIE SUCHE AUS - mit oder ohne Standortfilter
    // log.i("Löse initiale Suche aus mit Filtern: $initialFilters");
    // Wichtig: Prüfen ob der Notifier noch existiert / das Widget gemountet ist?
    // Da wir den Notifier übergeben haben, sollte er existieren.
    // searchNotifier.triggerSearch(filters: initialFilters);
    // })
  }

  // Neue Methode für Hintergrund-Updates
  Future<void> _updateLocationInBackground(
    BuildContext context,
    JobSearchNotifier searchNotifier,
    Map<String, String> currentFilters,
  ) async {
    final log = getLogger('JobSearchScreen._updateLocationInBackground');
    try {
      if (!context.mounted) return;
      final detectedLocation = await _determineLocationInBackground(
        context,
        searchNotifier,
      );

      if (!context.mounted) return;
      if (detectedLocation != null &&
          detectedLocation != currentFilters['location']) {
        log.i(
          "Standort im Hintergrund aktualisiert: ${currentFilters['location']} -> $detectedLocation",
        );
        // Aktualisiere Filter mit neuem Standort
        final updatedFilters = Map<String, String>.from(currentFilters);
        updatedFilters['location'] = detectedLocation;
        // Neue Suche mit aktualisiertem Standort
        searchNotifier.triggerSearch(filters: updatedFilters);
      } else {
        log.d(
          "Standort-Update im Hintergrund: Kein neuer Standort oder gleicher Standort",
        );
      }
    } catch (e) {
      log.e("Fehler beim Standort-Update im Hintergrund: $e");
    }
  }
}

// --- NEU: Provider für ScrollController ---
final jobSearchScreenScrollControllerProvider =
    Provider.autoDispose<ScrollController>((ref) {
      final controller = ScrollController();
      ref.onDispose(() {
        // Logger für Dispose-Ereignis
        final log = getLogger('JobSearchScreen.ScrollController');
        log.d("Disposing JobSearchScreen ScrollController");
        controller.dispose();
      });
      return controller;
    });

// Provider für den Zustand des Filter-Panels (eingeklappt/ausgeklappt)
final jobSearchFilterPanelStateProvider = StateProvider<bool>(
  (ref) => true,
); // Standardmäßig eingeklappt

import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart'; // Für Styling
import '../../../core/utils/search_history_manager.dart'; // Für Suchverlauf
import '../../../core/utils/logging.dart'; // Für Logging

/// Ein Suchfeld-Widget mit Autovervollständigung.
class SearchBar extends StatefulWidget {
  final Function(String) onSubmitted;
  final Function(String)? onChanged;
  final String? initialValue;
  final String? hintText;

  const SearchBar({
    super.key,
    required this.onSubmitted,
    this.onChanged,
    this.initialValue,
    this.hintText,
  });

  @override
  State<SearchBar> createState() => _SearchBarState();
}

class _SearchBarState extends State<SearchBar> {
  late final TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;
  List<String> _suggestions = [];
  List<String> _searchHistory = []; // Suchverlauf
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  String?
  _lastSearchValue; // Speichert den letzten Suchwert, um Duplikate zu vermeiden
  final _log = getLogger('SearchBar');

  // Erweiterte Liste häufiger Jobsuchen als Vorschläge
  final List<String> _commonJobSearches = [
    // Einfache Jobs und Hilfstätigkeiten
    'Verkäufer',
    'Lagerhelfer',
    'Bäckerei',
    'Pflegehelfer',
    'Reinigungskraft',
    'Servicekraft',
    'Kassierer',
    'Fahrer',
    'Büro',
    'Küchenhilfe',
    'Aushilfe',
    'Minijob',
    'Teilzeit',
    'Hausmeister',
    'Hotelfachkraft',
    'Security',
    'Kellner',
    'Produktionshelfer',
    'Bauhelfer',
    'Callcenter',
    'Zimmermädchen',
    'Babysitter',
    'Nachhilfe',
    'Promotion',
    'Lieferfahrer',
    'Lagerist',
    'Koch',
    'Barkeeper',
    'Einzelhandel',
    'Supermarkt',
    'Bäcker',
    'Konditor',
    'Metzger',
    'Empfang',
    'Rezeption',
    'Verkauf',
    'Gastgewerbe',
    'Tierpfleger',
    'Altenpfleger',
    'Krankenpfleger',
    'Sprachlehrer',
    'Fitnesstrainer',
    'Tennislehrer',
    'Schwimmlehrer',
    'Friseur',
    'Kosmetiker',
    'Buchhalter',
    'Transporthelfer',
    'Lkw-Fahrer',
    'Bürokaufmann',
    'Telefonist',

    // IT und Technologie
    'Softwareentwickler',
    'Webentwickler',
    'Frontend-Entwickler',
    'Backend-Entwickler',
    'Full-Stack-Entwickler',
    'App-Entwickler',
    'Systemadministrator',
    'Netzwerktechniker',
    'IT-Support',
    'Datenbankadministrator',
    'DevOps-Engineer',
    'Cloud-Architekt',
    'IT-Projektmanager',
    'Scrum Master',
    'Product Owner',
    'UX/UI-Designer',
    'Grafikdesigner',
    'Mediengestalter',
    'Webdesigner',
    'SEO-Manager',
    'Social Media Manager',
    'Content Manager',
    'Online-Marketing-Manager',

    // Kaufmännische Berufe
    'Kaufmann im Einzelhandel',
    'Kaufmann im Großhandel',
    'Industriekaufmann',
    'Bürokaufmann',
    'Bankkaufmann',
    'Versicherungskaufmann',
    'Immobilienkaufmann',
    'Speditionskaufmann',
    'Reiseverkehrskaufmann',
    'Personalreferent',
    'Buchhalter',
    'Controller',
    'Steuerberater',
    'Wirtschaftsprüfer',
    'Unternehmensberater',

    // Handwerk
    'Elektriker',
    'Elektroniker',
    'Mechatroniker',
    'Anlagenmechaniker',
    'Industriemechaniker',
    'KFZ-Mechatroniker',
    'Tischler',
    'Schreiner',
    'Zimmermann',
    'Maurer',
    'Maler',
    'Lackierer',
    'Fliesenleger',
    'Dachdecker',
    'Sanitärinstallateur',
    'Heizungsinstallateur',
    'Klempner',
    'Schweißer',
    'Metallbauer',

    // Gesundheit und Soziales
    'Arzt',
    'Zahnarzt',
    'Tierarzt',
    'Apotheker',
    'Physiotherapeut',
    'Ergotherapeut',
    'Logopäde',
    'Psychologe',
    'Sozialarbeiter',
    'Erzieher',
    'Kindergärtner',
    'Lehrer',
    'Grundschullehrer',
    'Gymnasiallehrer',
    'Berufsschullehrer',
    'Hochschullehrer',
    'Dozent',
    'Professor',

    // Ingenieurwesen
    'Maschinenbauingenieur',
    'Elektroingenieur',
    'Bauingenieur',
    'Chemieingenieur',
    'Verfahrenstechniker',
    'Umweltingenieur',
    'Wirtschaftsingenieur',
    'Fahrzeugtechniker',
    'Luft- und Raumfahrtingenieur',
    'Qualitätsmanager',

    // Naturwissenschaften
    'Chemiker',
    'Biologe',
    'Physiker',
    'Mathematiker',
    'Laborant',
    'Chemielaborant',
    'Biologielaborant',
    'Pharmazeutisch-technischer Assistent',

    // Medien und Kreativberufe
    'Journalist',
    'Redakteur',
    'Autor',
    'Übersetzer',
    'Dolmetscher',
    'Fotograf',
    'Kameramann',
    'Tontechniker',
    'Cutter',
    'Regisseur',
    'Schauspieler',
    'Musiker',
    'Sänger',
    'Tänzer',

    // Management und Führung
    'Geschäftsführer',
    'CEO',
    'CFO',
    'CTO',
    'COO',
    'Abteilungsleiter',
    'Teamleiter',
    'Filialleiter',
    'Vertriebsleiter',
    'Marketingleiter',
    'Personalleiter',
    'HR-Manager',

    // Vertrieb und Kundenservice
    'Vertriebsmitarbeiter',
    'Account Manager',
    'Key Account Manager',
    'Sales Manager',
    'Kundenberater',
    'Kundendienstmitarbeiter',
    'Außendienstmitarbeiter',
    'Handelsvertreter',

    // Transport und Logistik
    'Logistiker',
    'Disponent',
    'Lagerlogistiker',
    'Speditionskaufmann',
    'Berufskraftfahrer',
    'Kurier',
    'Postbote',
    'Paketzusteller',
    'Pilot',
    'Flugbegleiter',
    'Lokführer',
    'Schifffahrtskaufmann',
    'Hafenarbeiter',

    // Gastronomie und Hotellerie
    'Restaurantfachmann',
    'Hotelfachmann',
    'Hotelkaufmann',
    'Rezeptionist',
    'Concierge',
    'Sommelier',
    'Barista',
    'Küchenchef',
    'Sous-Chef',
    'Patissier',

    // Landwirtschaft und Umwelt
    'Landwirt',
    'Gärtner',
    'Florist',
    'Forstwirt',
    'Winzer',
    'Umwelttechniker',
    'Umweltschutztechniker',

    // Sicherheit und Recht
    'Polizist',
    'Feuerwehrmann',
    'Rettungssanitäter',
    'Notfallsanitäter',
    'Wachmann',
    'Detektiv',
    'Rechtsanwalt',
    'Notar',
    'Richter',
    'Staatsanwalt',
    'Justizfachangestellter',
    'Rechtsanwaltsfachangestellter',

    // Finanzen und Versicherung
    'Finanzberater',
    'Anlageberater',
    'Vermögensberater',
    'Versicherungsmakler',
    'Versicherungsvertreter',
    'Bankberater',
    'Kreditberater',
    'Immobilienmakler',

    // Ausbildung und Praktika
    'Ausbildung',
    'Praktikum',
    'Trainee',
    'Werkstudent',
    'Duales Studium',
    'Volontariat',
    'Referendariat',

    // Arbeitsmodelle
    'Homeoffice',
    'Remote',
    'Teilzeit',
    'Vollzeit',
    'Schichtarbeit',
    'Nachtarbeit',
    'Wochenendarbeit',
    'Saisonarbeit',
    'Befristet',
    'Unbefristet',
    'Festanstellung',
    'Selbständig',
    'Freiberuflich',
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);

    // Lade den Suchverlauf beim Start
    _loadSearchHistory();

    // Verbesserte Focus-Listener-Logik
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _log.d("Suchfeld fokussiert - lade Suchverlauf neu");

        // Wenn das Suchfeld fokussiert wird, lade immer zuerst den Suchverlauf neu
        _loadSearchHistory().then((_) {
          // Wenn das Suchfeld leer ist, zeige den Suchverlauf an
          if (_controller.text.isEmpty) {
            _showSearchHistory();
          } else {
            // Sonst zeige normale Vorschläge
            _updateSuggestions();
          }

          // Nur Overlay anzeigen, wenn Vorschläge oder Verlauf vorhanden sind
          if (_suggestions.isNotEmpty) {
            _showOverlay();
          }
        });
      } else {
        _hideOverlay();
        _log.d("Suchfeld hat Fokus verloren - Overlay wird geschlossen");
      }
    });

    // Initialisiere Vorschläge, falls ein Initialwert vorhanden ist
    if (widget.initialValue != null && widget.initialValue!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateSuggestions();
      });
    }
  }

  // Lade den Suchverlauf aus SharedPreferences
  Future<void> _loadSearchHistory() async {
    try {
      final history = await SearchHistoryManager.getSearchHistory();
      setState(() {
        _searchHistory = history;
      });
      _log.d("Suchverlauf geladen: $_searchHistory");
    } catch (e) {
      _log.e("Fehler beim Laden des Suchverlaufs: $e");
    }
  }

  // Zeige den Suchverlauf als Vorschläge an
  void _showSearchHistory() {
    setState(() {
      if (_searchHistory.isNotEmpty) {
        _log.d("Zeige Suchverlauf an: $_searchHistory");
        _suggestions = List.from(_searchHistory);
        _showSuggestions = true;
      } else {
        _log.d("Kein Suchverlauf vorhanden, zeige Top-Vorschläge");
        // Wenn kein Verlauf vorhanden ist, zeige die häufigsten Berufe
        _suggestions = _getTopSuggestions(30);
        _showSuggestions = true;
      }
    });
  }

  @override
  void dispose() {
    _hideOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _showOverlay() {
    // Entferne das alte Overlay, falls vorhanden
    _hideOverlay();

    // Stelle sicher, dass der Kontext noch gültig ist
    if (!mounted) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            // Transparenter Hintergrund zum Abfangen von Klicks
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _hideOverlay();
                  // Entferne den Fokus vom Suchfeld und schließe die Tastatur
                  _focusNode.unfocus();
                  _log.d(
                    "Klick außerhalb des Suchfelds erkannt - Overlay wird geschlossen und Fokus entfernt",
                  );
                },
                child: Container(color: Colors.transparent),
              ),
            ),

            // Die eigentliche Vorschlagsliste
            Positioned(
              width: size.width,
              left: renderBox.localToGlobal(Offset.zero).dx,
              top: renderBox.localToGlobal(Offset.zero).dy + size.height + 5.0,
              child: Material(
                elevation: 4.0,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                clipBehavior: Clip.antiAlias,
                child:
                    _suggestions.isEmpty
                        ? const SizedBox.shrink()
                        : Container(
                          constraints: BoxConstraints(
                            maxHeight:
                                200, // Reduzierte Höhe für weniger Vorschläge
                            minWidth: size.width,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusLarge,
                            ),
                            color: Theme.of(context).colorScheme.surface,
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            itemCount: _suggestions.length,
                            itemBuilder: (BuildContext context, int index) {
                              final suggestion = _suggestions[index];
                              final query = _controller.text.toLowerCase();

                              // Prüfe, ob es sich um einen Eintrag aus dem Suchverlauf handelt
                              final bool isHistoryItem =
                                  _controller.text.isEmpty &&
                                  _searchHistory.contains(suggestion);

                              // Wenn es ein Verlaufseintrag ist, zeige ein Uhr-Symbol
                              if (isHistoryItem) {
                                return ListTile(
                                  dense: true,
                                  leading: Icon(
                                    Icons.history,
                                    size: 18,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                  title: Text(
                                    suggestion,
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                  trailing: IconButton(
                                    icon: Icon(
                                      Icons.close,
                                      size: 16,
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant,
                                    ),
                                    onPressed: () async {
                                      // Entferne den Eintrag aus dem Verlauf
                                      await SearchHistoryManager.removeSearchTerm(
                                        suggestion,
                                      );
                                      // Aktualisiere die Anzeige
                                      _loadSearchHistory();
                                      _showSearchHistory();
                                    },
                                  ),
                                  onTap: () => _selectSuggestion(suggestion),
                                );
                              }

                              // Hervorheben des Suchbegriffs in den Vorschlägen
                              final int highlightStart = suggestion
                                  .toLowerCase()
                                  .indexOf(query);
                              if (highlightStart == -1) {
                                return ListTile(
                                  dense: true,
                                  title: Text(suggestion),
                                  onTap: () => _selectSuggestion(suggestion),
                                );
                              }

                              final int highlightEnd =
                                  highlightStart + query.length;
                              return ListTile(
                                dense: true,
                                title: RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: suggestion.substring(
                                          0,
                                          highlightStart,
                                        ),
                                        style: TextStyle(
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                      TextSpan(
                                        text: suggestion.substring(
                                          highlightStart,
                                          highlightEnd,
                                        ),
                                        style: TextStyle(
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      TextSpan(
                                        text: suggestion.substring(
                                          highlightEnd,
                                        ),
                                        style: TextStyle(
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                onTap: () => _selectSuggestion(suggestion),
                              );
                            },
                          ),
                        ),
              ),
            ),
          ],
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // Hilfsmethode zum Auswählen einer Vorschlag
  void _selectSuggestion(String suggestion) {
    _controller.text = suggestion;
    _hideOverlay();

    _log.d("Vorschlag ausgewählt: $suggestion");

    // Speichere den Suchbegriff im Verlauf
    _saveSearchTerm(suggestion);

    widget.onSubmitted(_controller.text);
    widget.onChanged?.call(_controller.text);

    // Behalte den Fokus, damit die Tastatur nicht verschwindet
    _focusNode.requestFocus();
  }

  // Speichere den Suchbegriff im Verlauf
  Future<void> _saveSearchTerm(String term) async {
    if (term.isEmpty) return;

    // Speichere den Begriff im Verlauf
    final success = await SearchHistoryManager.addSearchTerm(term);

    if (success) {
      // Aktualisiere den lokalen Verlauf
      await _loadSearchHistory();

      // Debug-Ausgabe
      _log.i(
        "Suchbegriff '$term' zum Verlauf hinzugefügt. Aktueller Verlauf: $_searchHistory",
      );
    } else {
      _log.e("Fehler beim Hinzufügen des Suchbegriffs '$term' zum Verlauf");
    }
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateSuggestions() {
    if (_controller.text.isEmpty) {
      // Bei leerem Suchfeld zeigen wir den Suchverlauf an
      _showSearchHistory();

      if (_suggestions.isNotEmpty && _focusNode.hasFocus) {
        _hideOverlay();
        _showOverlay();
      } else {
        _hideOverlay();
      }
      return;
    }

    final String query = _controller.text.toLowerCase().trim();

    // Beginne Vorschläge bereits ab 1 Zeichen zu zeigen
    if (query.isNotEmpty) {
      // Verbesserte Filterlogik mit mehreren Prioritätsstufen

      // 1. Höchste Priorität: Vorschläge, die exakt mit dem Suchbegriff beginnen
      final List<String> exactStartsWithSuggestions =
          _commonJobSearches
              .where((job) => job.toLowerCase().startsWith(query))
              .toList();

      // 2. Zweite Priorität: Vorschläge, bei denen ein Wort mit dem Suchbegriff beginnt
      final List<String> wordStartsWithSuggestions =
          _commonJobSearches.where((job) {
            if (exactStartsWithSuggestions.contains(job)) return false;

            final words = job.toLowerCase().split(' ');
            for (final word in words) {
              if (word.startsWith(query)) return true;
            }
            return false;
          }).toList();

      // 3. Dritte Priorität: Vorschläge, die den Suchbegriff irgendwo enthalten
      final List<String> containsSuggestions =
          _commonJobSearches
              .where(
                (job) =>
                    !exactStartsWithSuggestions.contains(job) &&
                    !wordStartsWithSuggestions.contains(job) &&
                    job.toLowerCase().contains(query),
              )
              .toList();

      // 4. Vierte Priorität: Vorschläge, die ähnliche Wörter enthalten (z.B. für Tippfehler)
      final List<String> similarSuggestions =
          _commonJobSearches
              .where(
                (job) =>
                    !exactStartsWithSuggestions.contains(job) &&
                    !wordStartsWithSuggestions.contains(job) &&
                    !containsSuggestions.contains(job) &&
                    _isSimilarTo(job.toLowerCase(), query),
              )
              .toList();

      // Kombiniere die Listen mit absteigender Priorität
      final List<String> matchingSuggestions = [
        ...exactStartsWithSuggestions,
        ...wordStartsWithSuggestions,
        ...containsSuggestions,
        ...similarSuggestions,
      ];

      // Begrenze die Anzahl der Vorschläge auf maximal 30 (statt 15)
      final int suggestionsCount = matchingSuggestions.length;
      final List<String> limitedSuggestions =
          suggestionsCount > 30
              ? matchingSuggestions.sublist(0, 30)
              : matchingSuggestions;

      setState(() {
        _suggestions = limitedSuggestions;
        _showSuggestions = limitedSuggestions.isNotEmpty;
      });

      if (_suggestions.isNotEmpty && _focusNode.hasFocus) {
        // Stelle sicher, dass das Overlay aktualisiert wird
        _hideOverlay(); // Entferne das alte Overlay
        _showOverlay(); // Zeige das neue Overlay mit aktualisierten Vorschlägen
      } else {
        _hideOverlay();
      }

      // Sofortige Suche auslösen bei Eingabe, aber nur wenn sich der Text geändert hat
      // Hinweis: Die eigentliche Suche wird jetzt in onChanged ausgelöst, nicht hier
    }
  }

  // Hilfsmethode, um zu prüfen, ob ein Job ähnlich zum Suchbegriff ist (für Tippfehler)
  bool _isSimilarTo(String job, String query) {
    // Einfache Ähnlichkeitsprüfung: Prüfe, ob mindestens 70% der Zeichen übereinstimmen
    if (query.length < 3) return false; // Zu kurze Queries ignorieren

    // Prüfe jedes Wort im Job
    final jobWords = job.split(' ');
    for (final word in jobWords) {
      if (word.length < 3) continue; // Zu kurze Wörter ignorieren

      // Berechne Levenshtein-Distanz (vereinfacht)
      int matches = 0;
      for (int i = 0; i < query.length && i < word.length; i++) {
        if (query[i] == word[i]) matches++;
      }

      // Wenn mindestens 70% der Zeichen übereinstimmen, gilt es als ähnlich
      if (matches >= query.length * 0.7) return true;
    }

    return false;
  }

  // Hilfsmethode, um die häufigsten Berufe als Vorschläge zu erhalten
  List<String> _getTopSuggestions(int count) {
    // Hier könnten wir eine vordefinierte Liste der häufigsten Berufe zurückgeben
    // Für jetzt nehmen wir einfach die ersten 'count' Einträge aus der Liste
    return _commonJobSearches.take(count).toList();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: widget.hintText ?? 'Stichwort, Firma, Ort...',
          prefixIcon: const Icon(Icons.search, size: 20),
          suffixIcon:
              _controller.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear, size: 20),
                    onPressed: () {
                      _controller.clear();
                      widget.onSubmitted('');
                      widget.onChanged?.call('');
                      setState(() {
                        _suggestions = [];
                        _showSuggestions = false;
                      });
                      _hideOverlay();
                    },
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Theme.of(
            context,
          ).colorScheme.surfaceContainerHighest.withAlpha(128),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 10,
            horizontal: AppTheme.spacingMedium,
          ),
          isDense: true,
        ),
        textAlignVertical: TextAlignVertical.center,
        onSubmitted: (value) {
          _hideOverlay();

          // Speichere den Suchbegriff im Verlauf, wenn er nicht leer ist
          if (value.isNotEmpty) {
            _log.d("Suchbegriff eingegeben: $value");
            _saveSearchTerm(value);
          }

          widget.onSubmitted(value);

          // Behalte den Fokus, damit die Tastatur nicht verschwindet
          Future.microtask(() => _focusNode.requestFocus());
        },
        onChanged: (value) {
          // Sofort Vorschläge aktualisieren bei jeder Eingabe
          _updateSuggestions();

          // Stelle sicher, dass das Overlay angezeigt wird, wenn Vorschläge vorhanden sind
          if (_suggestions.isNotEmpty && _focusNode.hasFocus) {
            _showOverlay();
          } else {
            _hideOverlay();
          }

          // Nur wenn sich der Text tatsächlich geändert hat
          if (value != _lastSearchValue) {
            _lastSearchValue = value;

            // Stelle sicher, dass der Fokus erhalten bleibt
            if (!_focusNode.hasFocus) {
              _focusNode.requestFocus();
            }

            // Rufe onChanged nur auf, wenn der Wert sich geändert hat
            widget.onChanged?.call(value);
          }
        },
        textInputAction: TextInputAction.search,
      ),
    );
  }
}

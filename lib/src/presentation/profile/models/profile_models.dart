import 'package:flutter/material.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';

/// Helper-K<PERSON><PERSON> für das Profile-Management
/// Diese Datei enthält alle Model-Klassen für die Profil-Bearbeitung

// --- Profile Controllers ---

/// Controller-Klasse für alle Textfelder im Profil
class ProfileControllers {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  // cvFilePath wird jetzt nur noch für die *lokale* Auswahl verwendet, nicht zum Speichern
  // String? cvFilePath;
  TextEditingController targetPositionController = TextEditingController();
  TextEditingController industryController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController salaryController = TextEditingController();
  TextEditingController employmentTypeController = TextEditingController();
  TextEditingController globalAiHintsController = TextEditingController();
  TextEditingController preferredWritingStyleController = TextEditingController(
    // Standardwert wird später durch lokalisierten Text ersetzt
    text: '',
  );
  TextEditingController applicationLengthController = TextEditingController(
    text: 'Standard',
  );
  bool includeExperience = true;

  // Tracking für Profil-ID um unnötige Re-Initialisierung zu vermeiden
  String? lastProfileId;

  void dispose() {
    debugPrint("### ProfileControllers.dispose() called ###");
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    addressController.dispose();
    targetPositionController.dispose();
    industryController.dispose();
    locationController.dispose();
    salaryController.dispose();
    employmentTypeController.dispose();
    globalAiHintsController.dispose();
    preferredWritingStyleController.dispose();
    applicationLengthController.dispose();
    debugPrint("### ProfileControllers.dispose() finished ###");
  }
}

// --- Editable Models ---

/// Helper-Klasse für editierbare Berufserfahrung
class EditableWorkExperience {
  final TextEditingController positionController;
  final TextEditingController companyController;
  final TextEditingController descriptionController;
  final DateTime startDate;
  final DateTime? endDate;

  EditableWorkExperience({
    required this.positionController,
    required this.companyController,
    required this.descriptionController,
    required this.startDate,
    this.endDate,
  });

  factory EditableWorkExperience.empty() => EditableWorkExperience(
    positionController: TextEditingController(),
    companyController: TextEditingController(),
    descriptionController: TextEditingController(),
    startDate: DateTime.now(),
    endDate: null,
  );

  factory EditableWorkExperience.fromWorkExperience(WorkExperience exp) {
    // Korrigiere ungültige Datumsangaben (vor 1980)
    DateTime validStartDate = exp.startDate;
    if (validStartDate.isBefore(DateTime(1980))) {
      validStartDate = DateTime.now();
    }

    DateTime? validEndDate = exp.endDate;
    if (validEndDate != null && validEndDate.isBefore(DateTime(1980))) {
      validEndDate = null; // Setze auf null für "aktuell"
    }

    return EditableWorkExperience(
      positionController: TextEditingController(text: exp.position),
      companyController: TextEditingController(text: exp.company),
      descriptionController: TextEditingController(text: exp.description),
      startDate: validStartDate,
      endDate: validEndDate,
    );
  }

  EditableWorkExperience copyWith({
    DateTime? startDate,
    DateTime? endDate,
    bool clearEndDate = false,
  }) {
    return EditableWorkExperience(
      positionController: positionController,
      companyController: companyController,
      descriptionController: descriptionController,
      startDate: startDate ?? this.startDate,
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
    );
  }

  void dispose() {
    positionController.dispose();
    companyController.dispose();
    descriptionController.dispose();
  }
}

/// Helper-Klasse für editierbare Ausbildung
class EditableEducation {
  final TextEditingController institutionController;
  final TextEditingController degreeController;
  final TextEditingController fieldOfStudyController;
  final DateTime startDate;
  final DateTime? endDate;

  EditableEducation({
    required this.institutionController,
    required this.degreeController,
    required this.fieldOfStudyController,
    required this.startDate,
    this.endDate,
  });

  factory EditableEducation.empty() => EditableEducation(
    institutionController: TextEditingController(),
    degreeController: TextEditingController(),
    fieldOfStudyController: TextEditingController(),
    startDate: DateTime.now(),
    endDate: null,
  );

  factory EditableEducation.fromEducation(Education edu) {
    // Korrigiere ungültige Datumsangaben (vor 1980)
    DateTime validStartDate = edu.startDate;
    if (validStartDate.isBefore(DateTime(1980))) {
      validStartDate = DateTime.now();
    }

    DateTime? validEndDate = edu.endDate;
    if (validEndDate != null && validEndDate.isBefore(DateTime(1980))) {
      validEndDate = null; // Setze auf null für "aktuell"
    }

    return EditableEducation(
      institutionController: TextEditingController(text: edu.institution),
      degreeController: TextEditingController(text: edu.degree),
      fieldOfStudyController: TextEditingController(text: edu.fieldOfStudy),
      startDate: validStartDate,
      endDate: validEndDate,
    );
  }

  EditableEducation copyWith({
    DateTime? startDate,
    DateTime? endDate,
    bool clearEndDate = false,
  }) {
    return EditableEducation(
      institutionController: institutionController,
      degreeController: degreeController,
      fieldOfStudyController: fieldOfStudyController,
      startDate: startDate ?? this.startDate,
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
    );
  }

  void dispose() {
    institutionController.dispose();
    degreeController.dispose();
    fieldOfStudyController.dispose();
  }
}

/// Helper-Klasse für editierbare Skills
class EditableSkill {
  final TextEditingController skillController;
  final GlobalKey key;

  EditableSkill({required this.skillController, required this.key});

  factory EditableSkill.empty() =>
      EditableSkill(skillController: TextEditingController(), key: GlobalKey());

  factory EditableSkill.fromString(String skill) => EditableSkill(
    skillController: TextEditingController(text: skill),
    key: GlobalKey(),
  );

  void dispose() {
    skillController.dispose();
  }
}

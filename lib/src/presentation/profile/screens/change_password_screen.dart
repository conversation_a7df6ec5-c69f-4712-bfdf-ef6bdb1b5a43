import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/theme/app_theme.dart';
import '../../../extensions/flutter_extensions.dart';
import '../../../core/l10n/app_localizations_wrapper.dart';
import '../../../core/utils/logging.dart';

class ChangePasswordScreen extends HookConsumerWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('ChangePasswordScreen');
    
    // Extrahiere Parameter aus der URL
    final uri = GoRouterState.of(context).uri;
    final source = uri.queryParameters['source']; // 'auth_event', 'deep_link', 'initial_session'
    final email = uri.queryParameters['email'];
    final token = uri.queryParameters['token'];
    
    log.i('ChangePasswordScreen geöffnet mit:');
    log.i('  - Source: $source');
    log.i('  - Email: $email');
    log.i('  - Token vorhanden: ${token != null}');
    
    // Zeige Informationen über die Quelle des Passwort-Resets
    final sourceInfo = useMemoized(() {
      switch (source) {
        case 'auth_event':
          return 'Passwort-Reset über Authentifizierungsereignis';
        case 'deep_link':
          return 'Passwort-Reset über Deep Link';
        case 'initial_session':
          return 'Passwort-Reset beim App-Start';
        default:
          return 'Passwort ändern';
      }
    }, [source]);
    // final currentPasswordController = useTextEditingController(); // Vorerst nicht benötigt für Supabase
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // final isCurrentPasswordVisible = useState(false); // Vorerst nicht benötigt
    final isNewPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isSubmitting = useState(false);
    final errorState = useState<String?>(null);

    Future<void> changePassword() async {
      if (!(formKey.currentState?.validate() ?? false)) {
        log.w('Formularvalidierung fehlgeschlagen');
        return;
      }

      // Prüfung auf Übereinstimmung
      if (newPasswordController.text != confirmPasswordController.text) {
        log.w('Passwörter stimmen nicht überein');
        errorState.value = AppLocalizationsWrapper.of(context).passwordMismatch;
        return;
      }

      errorState.value = null;
      isSubmitting.value = true;
      
      log.i('Starte Passwort-Update...');
      log.i('  - Source: $source');
      log.i('  - Email: $email');

      try {
        final supabase = Supabase.instance.client;
        final currentUser = supabase.auth.currentUser;

        if (currentUser == null) {
          log.e('Kein Benutzer angemeldet');
          errorState.value = 'Kein Benutzer angemeldet. Bitte melden Sie sich erneut an.';
          return;
        }

        log.i('Aktueller Benutzer: ${currentUser.email}');
        log.i('Session gültig bis: ${currentUser.userMetadata}');

        // Passwort über Supabase aktualisieren
        final response = await supabase.auth.updateUser(
          UserAttributes(password: newPasswordController.text),
        );

        log.i('Passwort erfolgreich aktualisiert');
        log.i('Response User: ${response.user?.email}');

        if (context.mounted) {
          context.showSuccessSnackBar(
            'Passwort erfolgreich geändert!',
          );
          
          // Navigation basierend auf der Quelle
          if (source == 'auth_event' || source == 'deep_link' || source == 'initial_session') {
            // Bei Passwort-Reset zur Login-Seite navigieren
            log.i('Navigiere zur Login-Seite nach Passwort-Reset');
            context.go('/login?message=password_changed');
          } else {
            // Bei normalem Passwort-Ändern zurück zum vorherigen Screen
            log.i('Navigiere zurück zum vorherigen Screen');
            Navigator.of(context).pop();
          }
        }
      } on AuthException catch (e) {
        log.e('Supabase AuthException: ${e.message}');
        log.e('Error Code: ${e.statusCode}');
        
        // Spezifische Fehlerbehandlung
        String errorMessage;
        switch (e.message.toLowerCase()) {
          case String msg when msg.contains('weak'):
            errorMessage = 'Das Passwort ist zu schwach. Bitte wählen Sie ein stärkeres Passwort.';
            break;
          case String msg when msg.contains('same'):
            errorMessage = 'Das neue Passwort muss sich vom aktuellen Passwort unterscheiden.';
            break;
          case String msg when msg.contains('session'):
            errorMessage = 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.';
            break;
          default:
            errorMessage = 'Fehler beim Ändern des Passworts: ${e.message}';
        }
        
        errorState.value = errorMessage;
      } catch (e) {
        log.e('Unerwarteter Fehler: $e');
        errorState.value = 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';
      } finally {
        if (context.mounted) {
          isSubmitting.value = false;
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(sourceInfo),
        backgroundColor: source != null ? Theme.of(context).colorScheme.primaryContainer : null,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Informationsanzeige über die Quelle
              if (source != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: AppTheme.spacingSmall),
                          Expanded(
                            child: Text(
                              'Passwort-Reset aktiv',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (email != null) ...[
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          'E-Mail: $email',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Bitte geben Sie Ihr neues Passwort ein. Nach der Änderung werden Sie zur Anmeldung weitergeleitet.',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Fehleranzeige
              if (errorState.value != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusMedium,
                    ),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          errorState.value!,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Neues Passwort
              TextFormField(
                controller: newPasswordController,
                obscureText: !isNewPasswordVisible.value,
                decoration: InputDecoration(
                  labelText: AppLocalizationsWrapper.of(context).newPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isNewPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        () =>
                            isNewPasswordVisible.value =
                                !isNewPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizationsWrapper.of(context).enterNewPassword;
                  }
                  if (value.length < 8) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordMinLength;
                  }
                  if (!RegExp(r'[A-Z]').hasMatch(value)) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordRequireUppercase;
                  }
                  if (!RegExp(r'[0-9]').hasMatch(value)) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordRequireNumber;
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingMedium),

              // Neues Passwort bestätigen
              TextFormField(
                controller: confirmPasswordController,
                obscureText: !isConfirmPasswordVisible.value,
                decoration: InputDecoration(
                  labelText:
                      AppLocalizationsWrapper.of(context).confirmPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isConfirmPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        () =>
                            isConfirmPasswordVisible.value =
                                !isConfirmPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).confirmNewPassword;
                  }
                  if (value != newPasswordController.text) {
                    return AppLocalizationsWrapper.of(context).passwordMismatch;
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingXLarge),

              // Speicherbutton
              ElevatedButton(
                onPressed: isSubmitting.value ? null : changePassword,
                style: ElevatedButton.styleFrom(
                  backgroundColor: source != null 
                    ? Theme.of(context).colorScheme.primary
                    : null,
                  foregroundColor: source != null 
                    ? Theme.of(context).colorScheme.onPrimary
                    : null,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: isSubmitting.value
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(color: Colors.white),
                      )
                    : Text(
                        source != null 
                          ? 'Passwort zurücksetzen'
                          : 'Passwort speichern',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
              
              // Zusätzliche Hinweise für Passwort-Reset
              if (source != null) ...[
                const SizedBox(height: AppTheme.spacingMedium),
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingSmall),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          'Nach dem Zurücksetzen werden Sie automatisch abgemeldet und zur Anmeldung weitergeleitet.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

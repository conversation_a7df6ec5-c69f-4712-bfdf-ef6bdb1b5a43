import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io'; // Für File
import 'package:path/path.dart' as p; // Für basename
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

// import 'package:ki_test/src/presentation/profile/widgets/editable_work_experience_list.dart'; // Keep for internal structure
import 'package:ki_test/src/presentation/profile/widgets/dropdown_profile_section.dart';
import 'package:ki_test/src/presentation/widgets/custom_switch_list_tile.dart';
import 'package:ki_test/src/presentation/profile/widgets/additional_documents_section.dart';

/// Modell für persönliche Daten des Benutzers
class PersonalData {
  final String? name;
  final String? email;
  final String? phone;
  final String? address;

  PersonalData({this.name, this.email, this.phone, this.address});

  /// Erstellt eine Kopie mit aktualisierten Werten
  PersonalData copyWith({
    String? name,
    String? email,
    String? phone,
    String? address,
  }) {
    return PersonalData(
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
    );
  }
}

// Einfache Datenklasse zum Halten der Controller und Werte
class _EditProfileData {
  final TextEditingController nameController;
  final TextEditingController phoneController;
  final TextEditingController addressController;
  final ValueNotifier<File?>
  selectedCvFile; // Verwendet ValueNotifier für die ausgewählte Datei
  final ValueNotifier<String?>
  currentCvFileName; // Für den angezeigten Dateinamen

  // Skills
  final List<TextEditingController> _skillsControllers;
  int _lastAddedSkillIndex = -1;

  // Work Experience
  final List<dynamic>
  _workExperienceItems; // Placeholder for work experience items
  int _lastAddedWorkExperienceIndex = -1;

  // Education
  final List<TextEditingController> _educationControllers;
  int _lastAddedEducationIndex = -1;

  // KI Personalisierung
  final ValueNotifier<bool> includeWorkExperience;
  final ValueNotifier<String> preferredWritingStyle;
  final ValueNotifier<String> applicationLength;

  _EditProfileData({
    required this.nameController,
    required this.phoneController,
    required this.addressController,
    required this.selectedCvFile,
    required this.currentCvFileName,
    required List<TextEditingController> skillsControllers,
    required List<dynamic> workExperienceItems,
    required List<TextEditingController> educationControllers,
    required this.includeWorkExperience,
    required this.preferredWritingStyle,
    required this.applicationLength,
  }) : _skillsControllers = skillsControllers,
       _workExperienceItems = workExperienceItems,
       _educationControllers = educationControllers;

  // Getters
  List<TextEditingController> get skillsControllers => _skillsControllers;
  List<dynamic> get workExperienceItems => _workExperienceItems;
  List<TextEditingController> get educationControllers => _educationControllers;
  int get lastAddedSkillIndex => _lastAddedSkillIndex;
  int get lastAddedWorkExperienceIndex => _lastAddedWorkExperienceIndex;
  int get lastAddedEducationIndex => _lastAddedEducationIndex;

  // Methods for skills
  void addSkill() {
    _skillsControllers.add(TextEditingController());
    _lastAddedSkillIndex = _skillsControllers.length - 1;
  }

  void removeSkill(int index) {
    if (index >= 0 && index < _skillsControllers.length) {
      _skillsControllers[index].dispose();
      _skillsControllers.removeAt(index);
      _lastAddedSkillIndex = -1;
    }
  }

  // Methods for work experience
  void addWorkExperience() {
    // Placeholder implementation
    _lastAddedWorkExperienceIndex = _workExperienceItems.length;
  }

  void removeWorkExperience(int index) {
    if (index >= 0 && index < _workExperienceItems.length) {
      _workExperienceItems.removeAt(index);
      _lastAddedWorkExperienceIndex = -1;
    }
  }

  // Methods for education
  void addEducation() {
    _educationControllers.add(TextEditingController());
    _lastAddedEducationIndex = _educationControllers.length - 1;
  }

  void removeEducation(int index) {
    if (index >= 0 && index < _educationControllers.length) {
      _educationControllers[index].dispose();
      _educationControllers.removeAt(index);
      _lastAddedEducationIndex = -1;
    }
  }

  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    addressController.dispose();
    selectedCvFile.dispose();
    currentCvFileName.dispose();

    // Dispose skills controllers
    for (var controller in _skillsControllers) {
      controller.dispose();
    }

    // Dispose education controllers
    for (var controller in _educationControllers) {
      controller.dispose();
    }

    // Dispose KI Personalisierung ValueNotifier
    includeWorkExperience.dispose();
    preferredWritingStyle.dispose();
    applicationLength.dispose();
  }
}

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  EditProfileScreenState createState() => EditProfileScreenState();
}

class EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  @override
  Widget build(BuildContext context) {
    debugPrint('EditProfileScreen build!');
    final userProfileState = ref.watch(userProfileProvider);
    final userProfileNotifier = ref.read(userProfileProvider.notifier);
    final initialProfile = userProfileState.valueOrNull ?? UserProfile.empty();

    // Verwende useMemoized, um sicherzustellen, dass die Controller nur einmal erstellt werden
    final data = useMemoized(() {
      final selectedCvFileNotifier = useState<File?>(null);
      final currentCvFileNameNotifier = useState<String?>(
        initialProfile.cvFileName,
      ); // Initialer Dateiname

      // Initialisiere den ValueNotifier für die Datei mit dem initialen Zustand
      final valueNotifierFile = ValueNotifier<File?>(
        selectedCvFileNotifier.value,
      );
      // Verknüpfe den useState mit dem ValueNotifier
      useEffect(() {
        void listener() {
          valueNotifierFile.value = selectedCvFileNotifier.value;
        }

        selectedCvFileNotifier.addListener(listener);
        return () => selectedCvFileNotifier.removeListener(listener);
      }, [selectedCvFileNotifier]);

      // Initialisiere den ValueNotifier für den Dateinamen
      final valueNotifierFileName = ValueNotifier<String?>(
        currentCvFileNameNotifier.value,
      );
      useEffect(() {
        void listener() {
          valueNotifierFileName.value = currentCvFileNameNotifier.value;
        }

        currentCvFileNameNotifier.addListener(listener);
        return () => currentCvFileNameNotifier.removeListener(listener);
      }, [currentCvFileNameNotifier]);

      return _EditProfileData(
        nameController: TextEditingController(text: initialProfile.name ?? ''),
        phoneController: TextEditingController(
          text: initialProfile.phone ?? '',
        ),
        addressController: TextEditingController(
          text: initialProfile.address ?? '',
        ),
        selectedCvFile: valueNotifierFile, // Verwende den ValueNotifier
        currentCvFileName: valueNotifierFileName,
        skillsControllers:
            (initialProfile.skills ?? [])
                .map((skill) => TextEditingController(text: skill))
                .toList(),
        workExperienceItems: initialProfile.workExperience ?? [],
        educationControllers:
            (initialProfile.education ?? [])
                .map(
                  (education) =>
                      TextEditingController(text: education.institution ?? ''),
                )
                .toList(),
        includeWorkExperience: ValueNotifier<bool>(
          initialProfile.includeExperienceInApplication ?? true,
        ),
        preferredWritingStyle: ValueNotifier<String>(
          initialProfile.preferredWritingStyle ?? 'Professionell',
        ),
        applicationLength: ValueNotifier<String>(
          initialProfile.applicationLength ?? 'Standard',
        ),
      );
    }, [initialProfile]); // Abhängig vom initialen Profil

    // Dispose-Effekt für die Controller
    useEffect(() {
      return data.dispose;
    }, [data]);

    // ----- CV Upload Funktion mit Dialog -----
    Future<void> pickAndUploadCV() async {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['pdf'],
        );

        if (result != null && result.files.single.path != null) {
          final pickedFile = File(result.files.single.path!);
          final fileName = p.basename(pickedFile.path);

          // Frage den Benutzer, ob die vorhandene Datei ersetzt und analysiert werden soll
          final bool confirm =
              await showDialog<bool>(
                context: context,
                builder: (BuildContext dialogContext) {
                  return AlertDialog(
                    title: const Text('Lebenslauf ersetzen?'),
                    content: Text(
                      'Möchten Sie die Datei "${data.currentCvFileName.value ?? 'vorhandene'}" durch "$fileName" ersetzen und neu analysieren lassen?',
                    ),
                    actions: <Widget>[
                      TextButton(
                        child: const Text('Abbrechen'),
                        onPressed: () {
                          Navigator.of(
                            dialogContext,
                          ).pop(false); // Nicht bestätigen
                        },
                      ),
                      TextButton(
                        child: const Text('Ersetzen & Analysieren'),
                        onPressed: () {
                          Navigator.of(dialogContext).pop(true); // Bestätigen
                        },
                      ),
                    ],
                  );
                },
              ) ??
              false; // Standard ist false, wenn Dialog geschlossen wird

          if (confirm) {
            // Hier die Logik zum Upload und zur Analyse im Notifier aufrufen
            try {
              // Ladeanzeige starten (optional, aber empfohlen)
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Lebenslauf wird hochgeladen und analysiert...',
                  ),
                ),
              );

              if (mounted) {
                await userProfileNotifier.uploadAndAnalyzeCv(
                  pickedFile.path,
                  fileName,
                );
              }

              // Update UI
              data.selectedCvFile.value = pickedFile; // Update ValueNotifier
              data.currentCvFileName.value = fileName; // Update Dateiname

              if (mounted) {
                ScaffoldMessenger.of(context).removeCurrentSnackBar();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Lebenslauf erfolgreich verarbeitet.'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } catch (e) {
              ScaffoldMessenger.of(context).removeCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Fehler bei der Verarbeitung: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else {
          // User canceled the picker
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Keine Datei ausgewählt.')),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Fehler beim Auswählen der Datei: $e')),
        );
      }
    }
    // ----- ENDE CV Upload Funktion -----

    // ----- Speicherfunktion -----
    Future<void> saveProfile() async {
      // Aktualisiere das Profil direkt
      try {
        // Aktualisiere das Profil mit den neuen Werten
        final updatedProfile = initialProfile.copyWith(
          name: data.nameController.text,
          phone: data.phoneController.text,
          address: data.addressController.text,
          skills:
              data.skillsControllers
                  .map((controller) => controller.text.trim())
                  .where((skill) => skill.isNotEmpty)
                  .toList(),
          includeExperienceInApplication: data.includeWorkExperience.value,
          preferredWritingStyle: data.preferredWritingStyle.value,
          applicationLength: data.applicationLength.value,
        );

        // Speichere das aktualisierte Profil
        await userProfileNotifier.updateProfile(updatedProfile);

        // Zeige Erfolgsmeldung
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profil gespeichert.'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(); // Zurück zum Profil
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fehler beim Speichern: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
    // ----- ENDE Speicherfunktion -----

    return Scaffold(
      backgroundColor:
          Theme.of(
            context,
          ).scaffoldBackgroundColor, // Sicherstellen, dass der Hintergrund dunkel ist
      appBar: AppBar(
        backgroundColor:
            Theme.of(
              context,
            ).scaffoldBackgroundColor, // AppBar an Hintergrund anpassen
        elevation: 0, // Keine Schattierung
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('Profil bearbeiten'),
        actions: [
          TextButton(
            onPressed: saveProfile,
            child: Text(
              'SPEICHERN',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              userProfileState.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(child: Text('Fehler: $error')),
                data: (profile) {
                  // UI Aufbau
                  return ListView(
                    shrinkWrap:
                        true, // Important for ListView inside SingleChildScrollView
                    physics:
                        const NeverScrollableScrollPhysics(), // Disable ListView's own scrolling
                    children: [
                      // --- Persönliche Daten ---
                      DropdownProfileSection(
                        title: 'Persönliche Daten',
                        icon: Icons.person_outline,
                        isExpanded:
                            true, // Persönliche Daten standardmäßig geöffnet
                        child: Column(
                          children: [
                            _buildModernTextField(
                              context,
                              controller: data.nameController,
                              label: 'Name',
                              icon: Icons.person_outline,
                            ),
                            _buildModernTextField(
                              context,
                              controller: TextEditingController(
                                text: profile.email ?? '',
                              ),
                              label: 'E-Mail',
                              icon: Icons.email_outlined,
                              readOnly: true,
                            ), // E-Mail ist nicht bearbeitbar
                            _buildModernTextField(
                              context,
                              controller: data.phoneController,
                              label: 'Telefon (Optional)',
                              icon: Icons.phone_outlined,
                              keyboardType: TextInputType.phone,
                            ),
                            _buildModernTextField(
                              context,
                              controller: data.addressController,
                              label: 'Adresse (Optional)',
                              icon: Icons.home_outlined,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- Lebenslauf (PDF) ---
                      DropdownProfileSection(
                        title: 'Lebenslauf (PDF)',
                        icon: Icons.description_outlined,
                        isExpanded: false,
                        child: _EditableCvSection(
                          currentFileName: data.currentCvFileName,
                          onUpload: pickAndUploadCV,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- Zusätzliche Dokumente ---
                      DropdownProfileSection(
                        title: 'Zusätzliche Dokumente',
                        icon: Icons.folder_outlined,
                        isExpanded: false,
                        child: const AdditionalDocumentsSection(),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- Fähigkeiten ---
                      DropdownProfileSection(
                        title: AppLocalizationsWrapper.of(context).skills,
                        icon: Icons.lightbulb_outline,
                        isExpanded: false,
                        child: _buildEditableChipList(
                          label: AppLocalizationsWrapper.of(context).skills,
                          controllers: data.skillsControllers,
                          onAdd: data.addSkill,
                          onRemove: data.removeSkill,
                          lastAddedIndex: data.lastAddedSkillIndex,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- Berufserfahrung ---
                      DropdownProfileSection(
                        title:
                            AppLocalizationsWrapper.of(context).workExperience,
                        icon: Icons.work_outline,
                        isExpanded: false,
                        child: Text(
                          'Berufserfahrung-Bearbeitung hier implementieren',
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- Ausbildung ---
                      DropdownProfileSection(
                        title: AppLocalizationsWrapper.of(context).education,
                        icon: Icons.school_outlined,
                        isExpanded: false,
                        child: _buildEditableChipList(
                          label: AppLocalizationsWrapper.of(context).education,
                          controllers: data.educationControllers,
                          onAdd: data.addEducation,
                          onRemove: data.removeEducation,
                          lastAddedIndex: data.lastAddedEducationIndex,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // --- KI Personalisierung ---
                      DropdownProfileSection(
                        title: 'KI Personalisierung',
                        icon: Icons.psychology_outlined,
                        isExpanded: false,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomSwitchListTile(
                              title:
                                  'Berufserfahrung in Bewerbungen einbeziehen',
                              value: data.includeWorkExperience.value,
                              onChanged: (value) {
                                data.includeWorkExperience.value = value;
                              },
                            ),
                            const SizedBox(height: AppTheme.spacingMedium),
                            Text(
                              'Bevorzugter Schreibstil',
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            const SizedBox(height: AppTheme.spacingSmall),
                            DropdownButtonFormField<String>(
                              value:
                                  data.preferredWritingStyle.value.isEmpty
                                      ? 'Professionell'
                                      : data.preferredWritingStyle.value,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: 'Professionell',
                                  child: Text('Professionell'),
                                ),
                                DropdownMenuItem(
                                  value: 'Kreativ',
                                  child: Text('Kreativ'),
                                ),
                                DropdownMenuItem(
                                  value: 'Formell',
                                  child: Text('Formell'),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  data.preferredWritingStyle.value = value;
                                }
                              },
                            ),
                            const SizedBox(height: AppTheme.spacingMedium),
                            Text(
                              'Bewerbungslänge',
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                            const SizedBox(height: AppTheme.spacingSmall),
                            DropdownButtonFormField<String>(
                              value:
                                  data.applicationLength.value.isEmpty
                                      ? 'Standard'
                                      : data.applicationLength.value,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: 'Kurz',
                                  child: Text('Kurz'),
                                ),
                                DropdownMenuItem(
                                  value: 'Standard',
                                  child: Text('Standard'),
                                ),
                                DropdownMenuItem(
                                  value: 'Lang',
                                  child: Text('Lang'),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  data.applicationLength.value = value;
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper Widget für Sektionsüberschriften
  // Widget _buildSectionHeader(
  //   BuildContext context,
  //   String title,
  //   IconData icon,
  // ) {
  //   return Row(
  //     children: [
  //       Icon(icon, color: Theme.of(context).colorScheme.primary),
  //       const SizedBox(width: AppTheme.spacingSmall),
  //       Text(title, style: Theme.of(context).textTheme.titleMedium),
  //     ],
  //   );
  // }

  // Helper Widget für Textfelder
  Widget _buildModernTextField(
    BuildContext context, {
    required TextEditingController controller,
    required String label,
    IconData? icon,
    int? maxLines = 1,
    TextInputType? keyboardType,
    bool readOnly = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: TextField(
        controller: controller,
        readOnly: readOnly,
        maxLines: maxLines,
        keyboardType: keyboardType,
        style: TextStyle(
          color:
              readOnly
                  ? colorScheme.onSurface.withAlpha(153)
                  : colorScheme.onSurface,
        ),
        decoration: InputDecoration(
          prefixIcon:
              icon != null
                  ? Icon(icon, size: 20, color: colorScheme.primary)
                  : null,
          labelText: label,
          labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest.withAlpha(76),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: AppTheme.spacingMedium,
          ),
          isDense: true,
        ),
      ),
    );
  }

  // Einfache Ersatzimplementierung für _EditableChipList
  Widget _buildEditableChipList({
    required String label,
    required List<TextEditingController> controllers,
    required VoidCallback onAdd,
    required Function(int) onRemove,
    required int lastAddedIndex,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...controllers.asMap().entries.map((entry) {
          final index = entry.key;
          final controller = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      hintText: '$label ${index + 1}',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.remove_circle_outline),
                  onPressed: () => onRemove(index),
                ),
              ],
            ),
          );
        }),
        const SizedBox(height: 8),
        Center(
          child: OutlinedButton.icon(
            onPressed: onAdd,
            icon: const Icon(Icons.add),
            label: Text('$label hinzufügen'),
          ),
        ),
      ],
    );
  }
}

/// Widget für die bearbeitbare CV-Sektion
class _EditableCvSection extends StatelessWidget {
  final ValueNotifier<String?> currentFileName;
  final VoidCallback onUpload;

  const _EditableCvSection({
    required this.currentFileName,
    required this.onUpload,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(
        context,
      ).colorScheme.surfaceContainerHighest.withAlpha(76),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.file_present_outlined, size: 18),
                const SizedBox(width: AppTheme.spacingSmall),
                const Text('Aktuell: '),
                ValueListenableBuilder<String?>(
                  valueListenable: currentFileName,
                  builder: (context, fileName, child) {
                    return Expanded(
                      child: Text(
                        fileName ?? 'Keine Datei hochgeladen',
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Center(
              child: ElevatedButton.icon(
                icon: const Icon(Icons.upload_file),
                label: const Text('PDF auswählen'),
                onPressed: onUpload,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            const Center(
              child: Text(
                'Unterstützte Formate: PDF',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

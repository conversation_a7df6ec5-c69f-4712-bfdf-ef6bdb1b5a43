import 'dart:io';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/application/services/profile_image_service.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Widget für Profilbild-Upload und -Anzeige
class ProfileImageWidget extends HookConsumerWidget {
  final bool isEditing;
  final UserProfile profile;

  const ProfileImageWidget({
    super.key,
    required this.isEditing,
    required this.profile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isUploading = useState<bool>(false);
    final localImagePath = useState<String?>(null);
    final profileImageService = ProfileImageService();

    // Zeige lokales Bild oder Profilbild-URL
    String? displayImageUrl = localImagePath.value ?? profile.profileImageUrl;

    return Center(
      child: Stack(
        children: [
          // Profilbild-Container
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppTheme.primaryColor, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipOval(
              child:
                  displayImageUrl != null
                      ? _buildProfileImage(displayImageUrl)
                      : _buildPlaceholderImage(),
            ),
          ),

          // Upload-Indikator
          if (isUploading.value)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.5),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                ),
              ),
            ),

          // Edit-Button (nur im Bearbeitungsmodus)
          if (isEditing && !isUploading.value)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 20,
                  ),
                  onPressed:
                      () => _showImagePickerDialog(
                        context,
                        profileImageService,
                        ref,
                        isUploading,
                        localImagePath,
                      ),
                ),
              ),
            ),

          // Löschen-Button (wenn Bild vorhanden)
          if (isEditing && displayImageUrl != null && !isUploading.value)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 16),
                  onPressed:
                      () => _deleteProfileImage(
                        context,
                        profileImageService,
                        ref,
                        isUploading,
                        localImagePath,
                      ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Erstellt das Profilbild-Widget
  Widget _buildProfileImage(String imageUrl) {
    if (imageUrl.startsWith('/') || imageUrl.startsWith('file://')) {
      // Lokales Bild
      return Image.file(
        File(imageUrl.replaceFirst('file://', '')),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
      );
    } else {
      // Netzwerk-Bild
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
      );
    }
  }

  /// Erstellt das Platzhalter-Bild
  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[300],
      child: Icon(Icons.person, size: 60, color: Colors.grey[600]),
    );
  }

  /// Zeigt den Bildauswahl-Dialog
  void _showImagePickerDialog(
    BuildContext context,
    ProfileImageService service,
    WidgetRef ref,
    ValueNotifier<bool> isUploading,
    ValueNotifier<String?> localImagePath,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Aus Galerie wählen'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(
                    ImageSource.gallery,
                    service,
                    ref,
                    isUploading,
                    localImagePath,
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Foto aufnehmen'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(
                    ImageSource.camera,
                    service,
                    ref,
                    isUploading,
                    localImagePath,
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Wählt ein Bild aus und lädt es hoch
  Future<void> _pickAndUploadImage(
    ImageSource source,
    ProfileImageService service,
    WidgetRef ref,
    ValueNotifier<bool> isUploading,
    ValueNotifier<String?> localImagePath,
  ) async {
    try {
      isUploading.value = true;

      // Hole User-ID direkt aus Supabase Auth
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('Nicht angemeldet. Bitte melden Sie sich erneut an.');
      }

      final userId = currentUser.id;
      debugPrint('📸 Profilbild-Upload für User-ID: $userId');

      // Bild auswählen
      final XFile? imageFile = await service.pickImage(source: source);
      if (imageFile == null) {
        isUploading.value = false;
        return;
      }

      // Lokale Vorschau setzen
      localImagePath.value = imageFile.path;

      // Bild hochladen
      final imageUrl = await service.uploadProfileImage(imageFile, userId);

      // Profil aktualisieren
      await service.updateProfileWithImage(userId, imageUrl);

      // Warte kurz und lade dann das Profil neu aus der Datenbank
      await Future.delayed(const Duration(milliseconds: 500));

      // Provider invalidieren um Neuladung zu erzwingen (mit Fehlerbehandlung)
      try {
        ref.invalidate(userProfileProvider);
      } catch (e) {
        debugPrint('⚠️ Provider bereits disposed: $e');
        return;
      }

      debugPrint(
        '🔄 UserProfileProvider invalidiert - UI sollte sich aktualisieren',
      );

      // Zusätzlich: Lokales Bild für sofortige Anzeige setzen
      localImagePath.value = imageUrl;

      debugPrint('✅ Profilbild erfolgreich hochgeladen und gespeichert');
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Upload: $e');

      // Lokale Vorschau zurücksetzen bei Fehler
      localImagePath.value = null;

      // Fehler-Snackbar anzeigen
      if (ref.context.mounted) {
        ScaffoldMessenger.of(ref.context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Hochladen: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isUploading.value = false;
    }
  }

  /// Löscht das Profilbild
  Future<void> _deleteProfileImage(
    BuildContext context,
    ProfileImageService service,
    WidgetRef ref,
    ValueNotifier<bool> isUploading,
    ValueNotifier<String?> localImagePath,
  ) async {
    // Bestätigung anzeigen
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Profilbild löschen'),
          content: const Text('Möchten Sie Ihr Profilbild wirklich löschen?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Abbrechen'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Löschen'),
            ),
          ],
        );
      },
    );

    if (confirmed != true) return;

    try {
      isUploading.value = true;

      // Hole User-ID direkt aus Supabase Auth
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('Nicht angemeldet. Bitte melden Sie sich erneut an.');
      }

      final userId = currentUser.id;
      debugPrint('📸 Profilbild-Löschung für User-ID: $userId');

      // Profilbild löschen
      await service.deleteProfileImage(userId);

      // Lokale Vorschau zurücksetzen
      localImagePath.value = null;

      // Provider aktualisieren
      ref.invalidate(userProfileProvider);

      debugPrint('✅ Profilbild erfolgreich gelöscht');
    } catch (e) {
      debugPrint('🚨 Fehler beim Profilbild-Löschen: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Löschen: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      isUploading.value = false;
    }
  }
}

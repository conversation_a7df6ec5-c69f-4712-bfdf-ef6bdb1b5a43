import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../utils/cv_storage_helper.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:open_file/open_file.dart';

/// Screen für die Vorschau von CV-Dokumenten
class CvPreviewScreen extends ConsumerStatefulWidget {
  final String fileName;
  final String downloadUrl;

  const CvPreviewScreen({
    super.key,
    required this.fileName,
    required this.downloadUrl,
  });

  @override
  ConsumerState<CvPreviewScreen> createState() => _CvPreviewScreenState();
}

class _CvPreviewScreenState extends ConsumerState<CvPreviewScreen> {
  bool _isLoading = true;
  String? _localFilePath;
  String? _errorMessage;
  int _totalPages = 0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Verwende CvStorageHelper für authentifizierten Download
      final localPath = await CvStorageHelper.downloadCvFromUrl(
        widget.downloadUrl,
        Supabase.instance.client.auth.currentUser?.id,
      );
      
      if (localPath == null) {
        setState(() {
          _errorMessage = 'CV konnte nicht geladen werden (Status: 400)';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _localFilePath = localPath;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Fehler beim Laden: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.fileName,
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _shareDocument,
            icon: const Icon(Icons.share),
            tooltip: 'Teilen',
          ),
          IconButton(
            onPressed: _downloadDocument,
            icon: const Icon(Icons.download),
            tooltip: 'Herunterladen',
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppTheme.spacingMedium),
            Text('Lebenslauf wird geladen...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.red[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ElevatedButton(
              onPressed: _loadDocument,
              child: const Text('Erneut versuchen'),
            ),
          ],
        ),
      );
    }

    if (_localFilePath == null) {
      return const Center(
        child: Text('Kein Lebenslauf verfügbar'),
      );
    }

    // PDF-Vorschau
    return _buildPdfViewer();
  }

  Widget _buildPdfViewer() {
    return PDFView(
      filePath: _localFilePath!,
      enableSwipe: true,
      swipeHorizontal: false,
      autoSpacing: false,
      pageFling: true,
      pageSnap: true,
      defaultPage: _currentPage,
      fitPolicy: FitPolicy.BOTH,
      preventLinkNavigation: false,
      onRender: (pages) {
        setState(() {
          _totalPages = pages ?? 0;
        });
      },
      onError: (error) {
        setState(() {
          _errorMessage = 'PDF-Fehler: $error';
        });
      },
      onPageError: (page, error) {
        setState(() {
          _errorMessage = 'Seite $page Fehler: $error';
        });
      },
      onViewCreated: (PDFViewController controller) {
        // PDF-Controller für weitere Funktionen verfügbar
      },
      onLinkHandler: (String? uri) {
        // Link-Handling falls nötig
      },
      onPageChanged: (int? page, int? total) {
        setState(() {
          _currentPage = page ?? 0;
          _totalPages = total ?? 0;
        });
      },
    );
  }

  Widget? _buildBottomBar() {
    if (_totalPages <= 1) {
      return null;
    }

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Seite ${_currentPage + 1} von $_totalPages',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _shareDocument() async {
    if (_localFilePath == null) return;

    try {
      // Hier könnte share_plus Package verwendet werden
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Teilen-Funktionalität wird implementiert'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fehler beim Teilen: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _downloadDocument() async {
    if (_localFilePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Keine Datei zum Herunterladen verfügbar'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Zeige Ladeindikator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Datei wird heruntergeladen...'),
            ],
          ),
        ),
      );

      // Hole das Downloads-Verzeichnis
      Directory? downloadsDirectory;
      if (Platform.isAndroid) {
        downloadsDirectory = Directory('/storage/emulated/0/Download');
        if (!downloadsDirectory.existsSync()) {
          downloadsDirectory = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        downloadsDirectory = await getApplicationDocumentsDirectory();
      } else {
        downloadsDirectory = await getDownloadsDirectory();
      }

      if (downloadsDirectory == null) {
        throw Exception('Downloads-Verzeichnis nicht verfügbar');
      }

      // Erstelle einen eindeutigen Dateinamen
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = widget.fileName.split('.').last;
      final baseFileName = widget.fileName.replaceAll('.$fileExtension', '');
      final downloadFileName = '${baseFileName}_$timestamp.$fileExtension';
      final downloadPath = '${downloadsDirectory.path}/$downloadFileName';

      // Kopiere die Datei ins Downloads-Verzeichnis
      final sourceFile = File(_localFilePath!);
      final downloadFile = File(downloadPath);
      await sourceFile.copy(downloadPath);

      // Schließe den Ladeindikator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Zeige Erfolgsmeldung
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Platform.isIOS 
                ? 'Datei in Dokumente gespeichert: $downloadFileName'
                : 'Datei heruntergeladen: $downloadFileName',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
            action: Platform.isAndroid ? SnackBarAction(
              label: 'Öffnen',
              onPressed: () async {
                try {
                  final result = await OpenFile.open(downloadPath);
                  if (result.type != ResultType.done) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Fehler beim Öffnen: ${result.message}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Fehler beim Öffnen: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
            ) : null,
          ),
        );
      }
    } catch (e) {
      // Schließe den Ladeindikator bei Fehler
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Download: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // Temporäre Datei löschen
    if (_localFilePath != null) {
      final file = File(_localFilePath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
    super.dispose();
  }
}
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import 'package:ki_test/src/presentation/profile/models/profile_models.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';

/// KI-Personalisierung Widgets für das Profile-Management
/// Diese Datei enthält alle KI-Personalisierung bezogenen Widgets

// --- Editierbare KI Personalisierung ---

/// Editierbare KI Personalisierung Widget
class EditableKiPersonalizationNew extends ConsumerStatefulWidget {
  final ProfileControllers controllers;
  final bool initialIncludeExperience;
  final ValueChanged<bool> onIncludeExperienceChanged;
  final TextEditingController preferredWritingStyleController;
  final Function(String?) onStyleChanged;
  final TextEditingController applicationLengthController;
  final Function(String?) onApplicationLengthChanged;

  const EditableKiPersonalizationNew({
    super.key,
    required this.controllers,
    required this.initialIncludeExperience,
    required this.onIncludeExperienceChanged,
    required this.preferredWritingStyleController,
    required this.onStyleChanged,
    required this.applicationLengthController,
    required this.onApplicationLengthChanged,
  });

  @override
  ConsumerState<EditableKiPersonalizationNew> createState() =>
      _EditableKiPersonalizationNewState();
}

class _EditableKiPersonalizationNewState
    extends ConsumerState<EditableKiPersonalizationNew> {
  String? _generatedPrompt;
  bool _isGeneratingPrompt = false;

  @override
  void initState() {
    super.initState();
    // Überprüfe beim Laden der Seite, ob der Benutzer noch ein aktives Unlimited-Abonnement hat
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkSubscriptionForPersonalizedStyle();
    });
  }

  // Methode zum Überprüfen des Abonnements für den personalisierten Schreibstil
  Future<void> _checkSubscriptionForPersonalizedStyle() async {
    // Vereinfacht - nur prüfen, wenn "Passend zu meinem Stil" ausgewählt ist
    if (widget.preferredWritingStyleController.text !=
        'Passend zu meinem Stil') {
      return;
    }

    // Vereinfachte Implementierung ohne Provider-Abhängigkeiten
    // In der echten App würde hier die Subscription-Prüfung stattfinden
    debugPrint('Checking subscription for personalized style...');
  }

  // Methode zum Generieren des personalisierten Schreibstil-Prompts
  Future<void> _generatePersonalizedPrompt() async {
    if (widget.preferredWritingStyleController.text !=
        'Passend zu meinem Stil') {
      _generatedPrompt = null;
      return;
    }

    _isGeneratingPrompt = true;

    try {
      // Vereinfachte Implementierung ohne Provider-Abhängigkeiten
      // In der echten App würde hier die KI-Analyse stattfinden
      await Future.delayed(const Duration(seconds: 2)); // Simuliere API-Call

      if (mounted) {
        _isGeneratingPrompt = false;
        _generatedPrompt = "Personalisierter Stil basierend auf Ihrem Profil";

        // Zeige eine Erfolgsmeldung an ohne setState
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Personalisierter Schreibstil wurde erfolgreich generiert.',
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint(
        'Fehler bei der Generierung des personalisierten Schreibstils: $e',
      );
      if (mounted) {
        _isGeneratingPrompt = false;

        // Zeige eine Fehlermeldung an ohne setState
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Generieren des Schreibstils: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // Methode zum Anzeigen des benutzerdefinierten Dropdown-Menüs
  void _showCustomDropdown(BuildContext context) {
    // Optionen mit Beschreibungen und Farben
    final Map<String, Map<String, dynamic>> optionsWithDescriptions = {
      'Professionell': {
        'description':
            'Sachlicher, formeller Stil mit klarer Sprache, angemessener Distanz und präzisen Formulierungen. Ideal für konservative Branchen und formelle Bewerbungen.',
        'icon': Icons.business,
        'color': const Color(0xFF4A90E2), // Blau für Professionalität
      },
      'Kreativ': {
        'description':
            'Origineller, dynamischer Stil mit bildlicher Sprache und persönlicher Note. Gut geeignet für kreative Berufe, Marketing und Medien.',
        'icon': Icons.brush,
        'color': const Color(0xFFE94B3C), // Rot für Kreativität
      },
      'Technisch': {
        'description':
            'Präziser, faktenbasierter Stil mit Fachbegriffen und logischem Aufbau. Optimal für IT, Ingenieurwesen und wissenschaftliche Positionen.',
        'icon': Icons.code,
        'color': const Color(0xFF50C878), // Grün für Technik
      },
      'Relevant': {
        'description':
            'Intelligente Auswahl der passendsten Qualifikationen aus deinem Lebenslauf basierend auf der Stellenbeschreibung. Fokussiert nur auf relevante Erfahrungen für eine zielgerichtete Bewerbung.',
        'icon': Icons.center_focus_strong,
        'color': const Color(0xFFFF9500), // Orange für Relevanz
      },
      'Passend zu meinem Stil': {
        'description':
            'Analysiert dein Profil und erstellt einen personalisierten Schreibstil, der zu deinem Bildungsniveau, deiner Berufserfahrung und deinem Hintergrund passt. Nur für Unlimited-Abonnenten verfügbar.',
        'icon': Icons.person,
        'color': const Color(0xFF9B59B6), // Lila für Personalisierung
      },
    };

    // Dialog anzeigen mit verbesserter Darstellung und Scrolling
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 40,
          ),
          child: Container(
            width: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Schreibstil wählen',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ...optionsWithDescriptions.entries.map((entry) {
                          final option = entry.key;
                          final icon = entry.value['icon'] as IconData;
                          final description =
                              entry.value['description'] as String;
                          final color = entry.value['color'] as Color;

                          return InkWell(
                            onTap: () => Navigator.of(context).pop(option),
                            borderRadius: BorderRadius.circular(12),
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              padding: const EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: color.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: color.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Icon(
                                          icon,
                                          color: color,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Expanded(
                                        child: Text(
                                          option,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 15,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    description,
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                      fontSize: 12,
                                      height: 1.2,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                        const SizedBox(height: 8),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((selectedValue) async {
      if (selectedValue != null) {
        widget.preferredWritingStyleController.text = selectedValue;
        widget.onStyleChanged(selectedValue);
        if (selectedValue == 'Passend zu meinem Stil') {
          _generatePersonalizedPrompt();
        }
      }
    });
  }

  // Methode zum Anzeigen des Bewerbungslänge-Dropdown-Menüs
  void _showApplicationLengthDropdown(BuildContext context) {
    // Optionen mit Beschreibungen
    final Map<String, Map<String, dynamic>> lengthOptionsWithDescriptions = {
      'Kurz': {
        'description':
            'Kompakte Bewerbung (maximal 2/3 einer A4-Seite). Konzentriert sich auf die wichtigsten Punkte.',
        'icon': Icons.short_text,
      },
      'Standard': {
        'description':
            'Ausgewogene Bewerbung (etwa eine A4-Seite). Gute Balance zwischen Detail und Prägnanz.',
        'icon': Icons.text_fields,
      },
      'Lang': {
        'description':
            'Ausführliche Bewerbung (bis zu 1,5 A4-Seiten). Detaillierte Darstellung von Erfahrungen und Qualifikationen.',
        'icon': Icons.subject,
      },
    };

    // Dialog anzeigen
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 300,
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Bewerbungslänge wählen',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                ...lengthOptionsWithDescriptions.entries.map((entry) {
                  final option = entry.key;
                  final icon = entry.value['icon'] as IconData;
                  final description = entry.value['description'] as String;
                  return InkWell(
                    onTap: () => Navigator.of(context).pop(option),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(icon, color: AppTheme.primaryLightColor),
                              const SizedBox(width: 12),
                              Text(
                                option,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Padding(
                            padding: const EdgeInsets.only(left: 36),
                            child: Text(
                              description,
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    ).then((selectedValue) {
      if (selectedValue != null) {
        // Aktualisiere den Controller sofort
        widget.applicationLengthController.text = selectedValue;
        // Rufe auch den Callback auf
        widget.onApplicationLengthChanged(selectedValue);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Echte Premium-Prüfung über den Provider
    final isPremium = ref.watch(isPremiumProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Globale KI-Hinweise - immer vollständig sichtbar
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 0.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.psychology_outlined,
                    color: AppTheme.primaryLightColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      "Globale KI-Hinweise für alle Bewerbungen",
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextField(
                controller: widget.controllers.globalAiHintsController,
                maxLines: null,
                style: const TextStyle(color: Colors.white, fontSize: 14),
                decoration: InputDecoration(
                  hintText:
                      "Diese Hinweise werden bei jeder Bewerbung berücksichtigt",
                  hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        // Schreibstil-Auswahl
        GestureDetector(
          onTap: () => _showCustomDropdown(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.edit_outlined,
                  color: AppTheme.primaryLightColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bevorzugter Schreibstil',
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.preferredWritingStyleController.text.isEmpty
                            ? 'Schreibstil auswählen'
                            : widget.preferredWritingStyleController.text,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_drop_down, color: Colors.grey[400]),
              ],
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingSmall),

        // Bewerbungslänge-Auswahl
        ValueListenableBuilder<TextEditingValue>(
          valueListenable: widget.applicationLengthController,
          builder: (context, value, child) {
            return GestureDetector(
              onTap: () => _showApplicationLengthDropdown(context),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.1),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.text_fields_outlined,
                      color: AppTheme.primaryLightColor,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bewerbungslänge',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            value.text.isEmpty ? 'Standard' : value.text,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_drop_down, color: Colors.grey[400]),
                  ],
                ),
              ),
            );
          },
        ),

        const SizedBox(height: AppTheme.spacingSmall),

        // Berufserfahrung einbeziehen
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 0.5,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.work_outline,
                color: AppTheme.primaryLightColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Berufserfahrung in Bewerbungen einbeziehen',
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
              Switch(
                value: widget.initialIncludeExperience,
                onChanged: widget.onIncludeExperienceChanged,
                activeColor: AppTheme.primaryLightColor,
                inactiveThumbColor: Colors.grey,
                inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppTheme.spacingSmall),
        Text(
          AppLocalizationsWrapper.of(context).aiPersonalizationHint,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            fontStyle: FontStyle.normal,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

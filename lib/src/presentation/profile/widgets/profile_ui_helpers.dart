import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// UI Helper-Funktionen für das Profile-Management
/// Diese Datei enthält alle wiederverwendbaren UI-Komponenten und Helper-Funktionen

// --- U<PERSON> Helper Widgets ---

/// Widget für moderne Textfelder mit verbessertem Design
Widget buildModernTextField(
  BuildContext context, {
  required TextEditingController controller,
  required String label,
  IconData? icon,
  int? maxLines = 1,
  TextInputType? keyboardType,
  bool readOnly = false,
  TextStyle? style,
  String? hintText,
}) {
  return Container(
    margin: const EdgeInsets.only(bottom: 16.0),
    decoration: BoxDecoration(
      color: Colors.black.withValues(alpha: 0.2),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: Colors.white.withValues(alpha: 0.1),
        width: 0.5,
      ),
    ),
    child: Text<PERSON><PERSON>(
      controller: controller,
      readOnly: readOnly,
      keyboardType: keyboardType ?? TextInputType.text,
      maxLines: maxLines ?? 1,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          color: Colors.grey[400],
          fontSize: 14,
        ),
        prefixIcon: icon != null
            ? Icon(
                icon,
                color: AppTheme.primaryLightColor,
                size: 20,
              )
            : null,
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 12.0,
        ),
      ),
    ),
  );
}

/// Widget für moderne Datumsfelder
Widget buildModernDateField({
  required BuildContext context,
  required String label,
  required DateTime? selectedDate,
  bool isOptional = false,
  required VoidCallback onTap,
  VoidCallback? onClear,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        color: AppTheme.primaryLightColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today_outlined,
              color: AppTheme.primaryLightColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                selectedDate != null 
                  ? DateFormat('dd.MM.yyyy').format(selectedDate)
                  : label,
                style: TextStyle(
                  color: selectedDate != null ? Colors.white : Colors.grey[400],
                  fontSize: 14,
                ),
              ),
            ),
            if (isOptional && selectedDate != null && onClear != null)
              GestureDetector(
                onTap: onClear,
                child: const Icon(
                  Icons.clear,
                  color: Colors.grey,
                  size: 18,
                ),
              ),
          ],
        ),
      ),
    ),
  );
}

/// Widget für moderne Info-Zeilen im Display-Modus
Widget buildModernInfoRow(String label, String value, {IconData? icon}) {
  return Container(
    margin: const EdgeInsets.only(bottom: 12.0),
    padding: const EdgeInsets.all(16.0),
    decoration: BoxDecoration(
      color: Colors.black.withValues(alpha: 0.2),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: Colors.white.withValues(alpha: 0.1),
        width: 0.5,
      ),
    ),
    child: Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            color: AppTheme.primaryLightColor,
            size: 20,
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

/// Widget zum Umrahmen von Eingabefeldern (angepasst für dunkles Theme mit Glow)
Widget buildModernFramedWidget(BuildContext context, {required Widget child}) {
  return Container(
    decoration: BoxDecoration(
      color: AppTheme.primaryLightColor.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: Colors.white.withValues(alpha: 0.1),
        width: 0.5,
      ),
    ),
    child: child,
  );
}

// --- UI Helper Functions ---

/// Moderne Funktion für Date Picker
Future<void> selectModernDate(
  BuildContext context, {
  required DateTime initialDate,
  required DateTime firstDate,
  required DateTime lastDate,
  required Function(DateTime) onDateSelected,
}) async {
  final DateTime? picked = await showDatePicker(
    context: context,
    initialDate: initialDate,
    firstDate: firstDate,
    lastDate: lastDate,
    locale: const Locale('de', 'DE'),
    builder: (context, child) {
      return Theme(
        data: Theme.of(context).copyWith(
          colorScheme: const ColorScheme.dark(
            primary: AppTheme.primaryLightColor,
            onPrimary: Colors.white,
            onSurface: Colors.white,
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.primaryLightColor,
            ),
          ),
        ),
        child: child!,
      );
    },
  );
  if (picked != null && picked != initialDate) {
    onDateSelected(picked);
  }
}

/// Moderne Funktion für Bestätigungsdialog
Future<void> confirmModernRemoval(
  BuildContext context,
  String title,
  VoidCallback onConfirm,
) async {
  final bool? confirmed = await showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: AppTheme.surfaceDarkColor.withValues(
          alpha: 0.9,
        ), // Darker background
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        title: Text(title, style: const TextStyle(color: Colors.white)),
        content: const Text(
          'Diese Aktion kann nicht rückgängig gemacht werden.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: <Widget>[
          TextButton(
            style: Theme.of(context).textButtonTheme.style?.copyWith(
              foregroundColor: WidgetStateProperty.all(Colors.grey),
            ),
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Abbrechen'),
          ),
          TextButton(
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('Löschen'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      );
    },
  );

  if (confirmed == true) {
    onConfirm();
  }
}

/// Widget für moderne Section Cards mit verbessertem Design
class ModernSectionCardWidget extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget child;
  final VoidCallback? onAdd;
  final bool isEditing;

  const ModernSectionCardWidget({
    super.key,
    required this.title,
    required this.icon,
    required this.child,
    this.onAdd,
    this.isEditing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header mit Gradient
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.1),
                  AppTheme.primaryLightColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppTheme.primaryLightColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                    ),
                  ),
                ),
                if (onAdd != null && isEditing)
                  Container(
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.add_rounded),
                      tooltip: 'Hinzufügen',
                      color: AppTheme.primaryLightColor,
                      iconSize: 24,
                      onPressed: onAdd,
                    ),
                  ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: child,
          ),
        ],
      ),
    );
  }
}

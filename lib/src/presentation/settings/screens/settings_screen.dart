import 'package:flutter/material.dart';
// import 'package:firebase_auth/firebase_auth.dart'; // Entfernt
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart'; // NEU: Supabase importiert
import 'package:ki_test/src/core/theme/app_theme.dart'; // Korrigiert (war vorher falsch relativ)
import 'package:url_launcher/url_launcher.dart'; // Für Links
// Korrigiert
// Korrigiert
import 'package:ki_test/src/presentation/profile/screens/change_password_screen.dart'; // Korrigiert
import 'package:ki_test/src/application/providers/auth_provider.dart';
// Korrigiert
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/core/utils/logging.dart'; // Korrigiert
// Für Premium-Status
import 'package:shared_preferences/shared_preferences.dart'; // Für SharedPreferences

import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart'; // Für Lokalisierungs-Wrapper
// Für Onboarding-Screen
import 'package:ki_test/src/application/providers/onboarding_provider.dart'; // Für OnboardingProvider
import 'package:ki_test/src/presentation/auth/screens/login_screen.dart'; // Für isSubmittingGoogleProvider

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  // Helper zum Starten von URLs
  Future<void> _launchUrl(BuildContext context, String urlString) async {
    // context hinzugefügt
    final Uri uri = Uri.parse(urlString);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      print('Could not launch $urlString');
      // Optional: Fehlermeldung anzeigen
      if (context.mounted) {
        // context check hinzugefügt
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Konnte URL nicht öffnen: $urlString')),
        );
      }
    }
  }

  // NEU: Bestätigungsdialog für Konto löschen
  Future<void> _confirmAccountDeletion(
    BuildContext context,
    WidgetRef ref,
  ) async {
    // Rückgabetyp zu void geändert, ref hinzugefügt
    final log = getLogger('SettingsScreen'); // Logger hier holen
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Benutzer muss eine Aktion wählen
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Konto löschen?'),
          content: const Text(
            'Bist du sicher, dass du dein Konto dauerhaft löschen möchtest? '
            'Diese Aktion kann nicht rückgängig gemacht werden.',
            style: TextStyle(fontSize: 15),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Abbrechen'),
              onPressed: () {
                Navigator.of(context).pop(false); // Bestätigung abbrechen
              },
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Konto löschen'),
              onPressed: () {
                Navigator.of(context).pop(true); // Bestätigung erteilen
              },
            ),
          ],
        );
      },
    );

    if (confirmed != true) return; // Abbrechen, wenn nicht bestätigt

    // Stelle sicher, dass alle Ladezustände zurückgesetzt werden
    ref.read(isSubmittingGoogleProvider.notifier).state = false;

    // Stelle sicher, dass der has_existing_account-Flag in den SharedPreferences zurückgesetzt wird
    try {
      final prefs = await SharedPreferences.getInstance();
      // await prefs.setBool('has_existing_account', false); // Entfernt
      log.i("SharedPreferences zurückgesetzt");

      // Lösche auch alle anderen relevanten Daten aus den SharedPreferences
      await prefs.remove('onboarding_complete');
      await prefs.remove('device_id');
      log.i("Weitere relevante Daten aus SharedPreferences gelöscht");
    } catch (prefsError) {
      log.w("Fehler beim Zurücksetzen von SharedPreferences: $prefsError");
      // Wir fahren trotzdem fort
    }

    // Verwende direkt den Supabase-Client anstelle des authProvider
    log.i(
      "Bestätigung für Kontolöschung erhalten. Versuche, Konto zu löschen...",
    );

    // Zeige Ladeindikator
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Konto wird gelöscht...'),
                ],
              ),
            ),
      );
    }

    try {
      // Rufe die Löschfunktion vom authProvider auf
      final authNotifier = ref.read(authProvider.notifier);
      log.i("Rufe deleteAccount() auf...");

      // Führe die Kontolöschung durch
      final success = await authNotifier.deleteAccount();

      // Schließe den Ladedialog sofort nach der Kontolöschung
      if (context.mounted) {
        try {
          Navigator.of(context).pop();
        } catch (navigatorError) {
          log.w(
            "Navigator bereits disposed beim Schließen des Dialogs: $navigatorError",
          );
        }
      }

      if (success) {
        log.i(
          "Kontolöschung erfolgreich - AuthStateListener wird die Navigation übernehmen",
        );

        // Sofort zurückkehren - keine weitere Navigation oder UI-Updates
        // Der AuthStateListener wird automatisch zur Login-Seite navigieren
        return;
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Kontolöschung fehlgeschlagen.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      log.e(
        "Fehler beim Löschen des Kontos: $e",
        error: e,
        stackTrace: stackTrace,
      );

      // Schließe den Ladedialog bei Fehler
      if (context.mounted) {
        try {
          Navigator.of(context).pop();
        } catch (navigatorError) {
          log.w(
            "Navigator bereits disposed beim Fehler-Handling: $navigatorError",
          );
        }

        // Zeige Fehlermeldung nur bei Fehlern
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ein Fehler ist aufgetreten: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('SettingsScreen'); // Logger Instanz korrekt geholt
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizationsWrapper.of(context).settingsTitle),
        elevation: 0,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: ListView(
        // Verringerter Abstand zur Navigation
        padding: const EdgeInsets.only(
          top: AppTheme.spacingMedium,
          bottom: kBottomNavigationBarHeight - 32,
        ),
        children: [
          // --- Konto ---
          _buildSectionTitle(
            context,
            AppLocalizationsWrapper.of(context).accountSettings,
          ),
          ListTile(
            leading: const Icon(Icons.person_outline),
            title: Text(AppLocalizationsWrapper.of(context).editProfile),
            subtitle: Text('Persönliche Daten ändern'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go('/profile'),
          ),
          ListTile(
            leading: const Icon(Icons.lock_outline),
            title: Text(AppLocalizationsWrapper.of(context).changePassword),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go('/change-password'),
          ),
          ListTile(
            leading: Icon(
              Icons.workspace_premium,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text(
              'Premium verwalten',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
            subtitle: Text(
              'Abonnement verwalten, Preise anzeigen und Pakete wechseln',
            ),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go('/premium-management'),
          ),
          // --- NEU: Sicherheit & Passwort ---
          _buildSectionTitle(
            context,
            AppLocalizationsWrapper.of(context).securitySettings,
          ),
          ListTile(
            leading: const Icon(Icons.lock_outline), // Passwort-Icon
            title: Text(AppLocalizationsWrapper.of(context).changePassword),
            onTap: () {
              // Navigiere zum ChangePasswordScreen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangePasswordScreen(),
                ),
              );
              // TODO: Implementiere Navigation zum Passwort-Ändern-Screen
              /* ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Passwort ändern noch nicht implementiert.')),
              ); */
            },
          ),
          // --- App-Einstellungen ---
          _buildSectionTitle(
            context,
            AppLocalizationsWrapper.of(context).appSettings,
          ),

          // Onboarding-Screen Button
          ListTile(
            leading: const Icon(Icons.slideshow_outlined),
            title: const Text('Onboarding-Screen anzeigen'),
            subtitle: const Text(
              'Einführung und Funktionen der App erneut ansehen',
            ),
            onTap: () async {
              // Onboarding-Status zurücksetzen
              await ref.read(onboardingProvider.notifier).resetOnboarding();
              // Zur Onboarding-Route navigieren
              if (context.mounted) {
                context.go('/onboarding');
              }
            },
          ),

          // --- KI-Tools ---
          _buildSectionTitle(context, 'KI-Tools'),
          ListTile(
            leading: const Icon(Icons.edit_document, color: Colors.blue),
            title: const Text('Bewerbung erstellen'),
            subtitle: const Text(
              'Jobbeschreibung eingeben und KI-Bewerbung generieren',
            ),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go('/manual-job-input'),
          ),
          ListTile(
            leading: const Icon(Icons.picture_as_pdf, color: Colors.green),
            title: const Text('Lebenslauf-Generator'),
            subtitle: const Text(
              'Professionelle PDF-Lebensläufe mit verschiedenen Vorlagen erstellen',
            ),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => context.go('/cv-generator'),
          ),

          // --- Info & Hilfe ---
          _buildSectionTitle(
            context,
            AppLocalizationsWrapper.of(context).infoHelp,
          ),
          ListTile(
            leading: const Icon(Icons.info_outline),
            title: Text(AppLocalizationsWrapper.of(context).aboutApp),
            onTap: () {
              showAboutDialog(
                context: context,
                applicationName: 'Bewerbung KI',
                applicationVersion: '1.0.0', // TODO: Version dynamisch machen
                applicationLegalese: '© 2025 Bewerbung KI',
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 15),
                    child: Text(
                      'Diese App hilft dir bei der Jobsuche mit KI-Unterstützung.',
                    ),
                  ),
                ],
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.help_outline),
            title: Text(AppLocalizationsWrapper.of(context).helpFeedback),
            onTap:
                () => _launchUrl(
                  context,
                  'mailto:<EMAIL>?subject=Feedback%20Bewerbung%20KI',
                ),
          ),
          ListTile(
            leading: const Icon(Icons.policy_outlined),
            title: Text(AppLocalizationsWrapper.of(context).privacyPolicy),
            onTap:
                () => _launchUrl(context, 'https://bewerbungki.de/datenschutz'),
          ),

          const Divider(height: 40, indent: 16, endIndent: 16),

          // --- Abmelde-Button (angepasst für Supabase) ---
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
              vertical: AppTheme.spacingMedium,
            ),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.logout),
              label: Text(AppLocalizationsWrapper.of(context).logout),
              onPressed: () async {
                log.i("--> Abmelden Button gedrückt (Supabase)");
                try {
                  // Zuerst die SharedPreferences löschen
                  final prefs = await SharedPreferences.getInstance();
                  // await prefs.setBool('has_existing_account', false); // Entfernt
                  log.i("SharedPreferences zurückgesetzt");

                  // Dann bei Supabase abmelden
                  await Supabase.instance.client.auth.signOut();
                  log.i("<-- Supabase.signOut() erfolgreich aufgerufen.");

                  // Explizit zur Login-Seite navigieren
                  if (context.mounted) {
                    // Zeige die Erfolgsmeldung
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text("Erfolgreich abgemeldet"),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 2),
                      ),
                    );

                    // Warte kurz und navigiere dann zur Login-Seite
                    // Verwende WidgetsBinding.instance.addPostFrameCallback, um Navigationsprobleme zu vermeiden
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (context.mounted) {
                        // Navigiere zur Login-Seite mit GoRouter
                        context.go('/login');
                      }
                    });
                  }
                } catch (e) {
                  log.e("XXX Fehler beim Abmelden (Supabase): $e");
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Fehler beim Abmelden: $e')),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    AppTheme
                        .warningColor, // Geändert zu Warnfarbe für Konsistenz
                foregroundColor: Colors.black87, // Besserer Kontrast auf Orange
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusLarge,
                  ),
                ),
              ),
            ),
          ),

          // --- Konto löschen Button (Funktion _deleteAccount wurde angepasst) ---
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
              vertical: AppTheme.spacingSmall,
            ),
            child: TextButton.icon(
              icon: const Icon(
                Icons.delete_forever_outlined,
                color: AppTheme.errorColor,
              ),
              label: Text(
                'Konto löschen',
                style: TextStyle(color: AppTheme.errorColor),
              ),
              onPressed:
                  () => _confirmAccountDeletion(
                    context,
                    ref,
                  ), // Ruft die angepasste Funktion auf
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 10),
                // Optional: Füge eine leichte Umrandung hinzu
                // side: BorderSide(color: AppTheme.errorColor.withValues(alpha: 0.5)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusLarge,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: AppTheme.spacingMedium), // Abstand unten
        ],
      ),
    );
  }

  // Hilfsfunktion für Sektionstitel
  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppTheme.spacingMedium,
        right: AppTheme.spacingMedium,
        top: AppTheme.spacingLarge,
        bottom: AppTheme.spacingSmall,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          color: Theme.of(context).colorScheme.primary, // Primärfarbe
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

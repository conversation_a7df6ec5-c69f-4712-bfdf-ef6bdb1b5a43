import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../../core/config/api_keys.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/theme/app_theme.dart';
import '../../core/l10n/app_localizations.dart';
import '../../core/l10n/app_localizations_wrapper.dart';
import '../../core/security/secure_data_manager.dart';
import '../../core/security/input_sanitizer.dart';

class NewKiSettingsScreen extends StatefulWidget {
  const NewKiSettingsScreen({super.key});

  @override
  _NewKiSettingsScreenState createState() => _NewKiSettingsScreenState();
}

class _NewKiSettingsScreenState extends State<NewKiSettingsScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController experienceController = TextEditingController();
  final TextEditingController skillsController = TextEditingController();
  final TextEditingController interestsController = TextEditingController();
  String selectedStyle = 'Professionell';
  bool includeExperience = true;
  bool isLoading = false;
  String? uploadedCVFileName;
  bool cvAnalysisComplete = false;
  bool isAnalyzingCV = false;
  Map<String, dynamic>? cvAnalysisResult;
  bool autoFillFieldsFromCV = true;

  @override
  void initState() {
    super.initState();
    _loadSavedData();
  }

  _loadSavedData() async {
    setState(() => isLoading = true);
    try {
      // SECURITY FIX: Migriere alte Daten und lade dann sicher
      await SecureDataManager.migrateFromSharedPreferences();

      // Lade sensitive Daten aus sicherer Speicherung
      final secureUserName = await SecureDataManager.getUserName();
      final secureUserEmail = await SecureDataManager.getUserEmail();
      final secureUserExperience = await SecureDataManager.getUserExperience();
      final secureUserSkills = await SecureDataManager.getUserSkills();
      final secureUserInterests = await SecureDataManager.getUserInterests();
      final secureCvFileName = await SecureDataManager.getCvFileName();

      // Lade nicht-sensitive Daten aus SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();

      setState(() {
        nameController.text = secureUserName ?? '';
        emailController.text = secureUserEmail ?? '';
        experienceController.text = secureUserExperience ?? '';
        skillsController.text = secureUserSkills ?? '';
        interestsController.text = secureUserInterests ?? '';
        uploadedCVFileName = secureCvFileName;

        selectedStyle = prefs.getString('style') ?? 'Professionell';
        includeExperience = prefs.getBool('includeExperience') ?? true;
        cvAnalysisComplete = prefs.getBool('cvAnalysisComplete') ?? false;
        autoFillFieldsFromCV = prefs.getBool('autoFillFieldsFromCV') ?? true;

        // Lade auch das CV-Analyseergebnis, falls vorhanden
        final cvAnalysisResultJson = prefs.getString('cvAnalysisResult');
        if (cvAnalysisResultJson != null) {
          cvAnalysisResult = jsonDecode(cvAnalysisResultJson);
        }
      });
      print('--- Gespeicherte Einstellungen ---');
      print('Name: ${nameController.text}');
      print('E-Mail: ${emailController.text}');
      print('Erfahrung: ${experienceController.text}');
      print('Fähigkeiten: ${skillsController.text}');
      print('Interessen: ${interestsController.text}');
      print('Stil: $selectedStyle');
      print('Erfahrung einbeziehen: $includeExperience');
      print('Lebenslauf: $uploadedCVFileName');
      print('CV-Analyse abgeschlossen: $cvAnalysisComplete');
      print('Automatisches Ausfüllen: $autoFillFieldsFromCV');
      print('------------------------------');
    } catch (e) {
      print('Fehler beim Laden der Einstellungen: $e');
    } finally {
      setState(() => isLoading = false);
    }
  }

  _saveData() async {
    setState(() => isLoading = true);
    try {
      // SECURITY FIX: Verwende sichere Datenspeicherung statt unverschlüsselter SharedPreferences

      // Sanitisiere alle Inputs vor der Speicherung
      final sanitizedName = InputSanitizer.sanitizeUserInput(
        nameController.text,
      );
      final sanitizedEmail = InputSanitizer.sanitizeEmail(emailController.text);
      final sanitizedExperience = InputSanitizer.sanitizeUserInput(
        experienceController.text,
      );
      final sanitizedSkills = InputSanitizer.sanitizeUserInput(
        skillsController.text,
      );
      final sanitizedInterests = InputSanitizer.sanitizeUserInput(
        interestsController.text,
      );

      // Speichere sensitive Daten verschlüsselt
      await SecureDataManager.saveUserName(sanitizedName);
      await SecureDataManager.saveUserEmail(sanitizedEmail);
      await SecureDataManager.saveUserExperience(sanitizedExperience);
      await SecureDataManager.saveUserSkills(sanitizedSkills);
      await SecureDataManager.saveUserInterests(sanitizedInterests);

      // Nicht-sensitive Daten können weiterhin in SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('style', selectedStyle);
      await prefs.setBool('includeExperience', includeExperience);
      await prefs.setBool('autoFillFieldsFromCV', autoFillFieldsFromCV);

      // Speichere auch den Lebenslauf-Dateinamen sicher
      if (uploadedCVFileName != null) {
        final sanitizedFileName = InputSanitizer.sanitizeFileName(
          uploadedCVFileName!,
        );
        await SecureDataManager.saveCvFileName(sanitizedFileName);
      }

      if (cvAnalysisComplete) {
        await prefs.setBool('cvAnalysisComplete', true);
      }

      if (cvAnalysisResult != null) {
        await prefs.setString('cvAnalysisResult', jsonEncode(cvAnalysisResult));
      }

      print('Daten erfolgreich gespeichert');
      _showSaveConfirmation();
    } catch (e) {
      print('Fehler beim Speichern der Daten: $e');
    } finally {
      setState(() => isLoading = false);
    }
  }

  _showSaveConfirmation() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Einstellungen gespeichert!')));
  }

  Future<void> _pickAndUploadCV() async {
    debugPrint('### _pickAndUploadCV aufgerufen ###');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('CV-Upload gestartet'),
        backgroundColor: Colors.indigo,
      ),
    );
    setState(() {
      isAnalyzingCV = true;
    });
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );
      debugPrint(
        'FilePickerResult: result=[32m$result[0m, path=[36m${result?.files.single.path}[0m',
      );
      if (result == null || result.files.single.path == null) {
        setState(() {
          isAnalyzingCV = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Keine Datei ausgewählt.'),
            backgroundColor: Colors.orange,
          ),
        );
        debugPrint('FilePicker: Keine Datei ausgewählt!');
        return;
      }
      final pickedFile = File(result.files.single.path!);
      final fileName = pickedFile.path.split('/').last;

      setState(() {
        uploadedCVFileName = fileName;
      });

      debugPrint('[CV-Upload] Datei gewählt: $fileName');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Datei gewählt: $fileName'),
          backgroundColor: Colors.blue,
        ),
      );

      // Feedback: Analyse läuft
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Analyse läuft...'),
          backgroundColor: Colors.green,
        ),
      );

      debugPrint('[CV-Upload] Versuche JWT-Token zu holen...');
      // Supabase Auth-Token holen
      final jwtToken =
          Supabase.instance.client.auth.currentSession?.accessToken;
      if (jwtToken == null) {
        debugPrint(
          '[CV-Upload] Kein JWT-Token gefunden! User nicht eingeloggt?',
        );
        setState(() {
          isAnalyzingCV = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Nicht eingeloggt – bitte zuerst anmelden!'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      debugPrint(
        '[CV-Upload] JWT-Token vorhanden, sende Anfrage an Supabase Edge Function...',
      );
      // Anfrage an Supabase Edge Function
      final response = await http.post(
        Uri.parse(supabaseOcrFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $jwtToken',
        },
        body: jsonEncode({'storagePath': fileName}),
      );
      debugPrint(
        '[CV-Upload] Antwort vom Backend: Status ${response.statusCode}',
      );

      if (response.statusCode == 0) {
        setState(() {
          isAnalyzingCV = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Keine Verbindung zum Backend!'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        if (data['success'] == true && data['extractedText'] != null) {
          // Extrahierten Text verarbeiten und in Felder übernehmen
          final extractedText = data['extractedText'] as String;

          // CV-Analyse-Ergebnis speichern
          final Map<String, dynamic> analysisResult = {
            'rawText': extractedText,
          };

          // Versuche strukturierte Informationen zu extrahieren
          final Map<String, String> extractedInfo = _extractInfoFromCV(
            extractedText,
          );

          // Analyseergebnis mit strukturierten Daten erweitern
          analysisResult.addAll(extractedInfo);

          // State aktualisieren
          setState(() {
            cvAnalysisResult = analysisResult;
            cvAnalysisComplete = true;
            isAnalyzingCV = false;
          });

          // Ergebnis in SharedPreferences speichern
          SharedPreferences prefs = await SharedPreferences.getInstance();
          await prefs.setString('cvAnalysisResult', jsonEncode(analysisResult));
          await prefs.setBool('cvAnalysisComplete', true);
          await prefs.setString('uploadedCVFileName', fileName);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lebenslauf erfolgreich analysiert!'),
              backgroundColor: Colors.green,
            ),
          );

          // Nach erfolgreicher Analyse fragen, ob die Felder aktualisiert werden sollen
          if (extractedInfo.isNotEmpty) {
            final bool? shouldUpdateFields = await showDialog<bool>(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: Text('Felder aktualisieren?'),
                    content: Text(
                      'Möchtest du die aktuellen Eingabefelder mit den Informationen aus dem Lebenslauf aktualisieren?',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: Text('Nein'),
                      ),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: Text('Ja, aktualisieren'),
                      ),
                    ],
                  ),
            );

            if (shouldUpdateFields == true) {
              _updateFieldsFromCVAnalysis(extractedInfo);
            }
          }
        } else {
          setState(() {
            cvAnalysisComplete = false;
            isAnalyzingCV = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler bei der Analyse: ${data['error'] ?? 'Unbekannter Fehler'}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        setState(() {
          cvAnalysisComplete = false;
          isAnalyzingCV = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Serverfehler: ${response.statusCode}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        cvAnalysisComplete = false;
        isAnalyzingCV = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fehler beim Hochladen/Analysieren: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Extrahiert strukturierte Informationen aus dem CV-Text
  Map<String, String> _extractInfoFromCV(String cvText) {
    final Map<String, String> result = {};

    // Einfache Extraktion von Informationen mit Regex
    // Name
    final nameRegex = RegExp(
      r'(?:Name|Vorname und Nachname)[:\s]*([\w\s]+)',
      caseSensitive: false,
    );
    final nameMatch = nameRegex.firstMatch(cvText);
    if (nameMatch != null && nameMatch.group(1) != null) {
      result['name'] = nameMatch.group(1)!.trim();
    }

    // E-Mail
    final emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
    );
    final emailMatch = emailRegex.firstMatch(cvText);
    if (emailMatch != null) {
      result['email'] = emailMatch.group(0)!.trim();
    }

    // Fähigkeiten/Skills
    final skillsRegex = RegExp(
      r'(?:Fähigkeiten|Skills|Kenntnisse|Kompetenzen)[:\s]*([\w\s,\.+-]+)',
      caseSensitive: false,
    );
    final skillsMatch = skillsRegex.firstMatch(cvText);
    if (skillsMatch != null && skillsMatch.group(1) != null) {
      result['skills'] = skillsMatch.group(1)!.trim();
    }

    // Berufserfahrung
    final expRegex = RegExp(
      r'(?:Berufserfahrung|Erfahrung|Experience)[:\s]*([\w\s,\.+-]+)',
      caseSensitive: false,
    );
    final expMatch = expRegex.firstMatch(cvText);
    if (expMatch != null && expMatch.group(1) != null) {
      result['experience'] = expMatch.group(1)!.trim();
    }

    // Interessen
    final interestsRegex = RegExp(
      r'(?:Interessen|Hobbys)[:\s]*([\w\s,\.+-]+)',
      caseSensitive: false,
    );
    final interestsMatch = interestsRegex.firstMatch(cvText);
    if (interestsMatch != null && interestsMatch.group(1) != null) {
      result['interests'] = interestsMatch.group(1)!.trim();
    }

    return result;
  }

  // Überschreibt die Eingabefelder mit den extrahierten Informationen
  void _updateFieldsFromCVAnalysis(Map<String, String> extractedInfo) {
    if (extractedInfo.containsKey('name')) {
      nameController.text = extractedInfo['name']!;
    }

    if (extractedInfo.containsKey('email')) {
      emailController.text = extractedInfo['email']!;
    }

    if (extractedInfo.containsKey('experience')) {
      experienceController.text = extractedInfo['experience']!;
    }

    if (extractedInfo.containsKey('skills')) {
      skillsController.text = extractedInfo['skills']!;
    }

    if (extractedInfo.containsKey('interests')) {
      interestsController.text = extractedInfo['interests']!;
    }
  }

  Future<void> _analyzeCV(File cvFile) async {
    // Diese Methode wird nicht mehr benötigt, da alles in _pickAndUploadCV läuft
  }

  Future<File> _saveBytesToFile(List<int> bytes, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final path = directory.path;
    final file = File('$path/$fileName');
    await file.writeAsBytes(bytes);
    return file;
  }

  Future<Map<String, String>> _callDeepSeekAPIForCVAnalysis(
    String cvContent,
  ) async {
    // HINWEIS: Direkte API-Aufrufe wurden aus Sicherheitsgründen entfernt
    // CV-Analyse wird jetzt über Firebase Cloud Functions durchgeführt
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Die CV-Analyse via DeepSeek wurde auf Firebase Cloud Functions umgestellt und ist vorübergehend nicht verfügbar.',
        ),
        duration: Duration(seconds: 5),
      ),
    );

    // Für Testzwecke geben wir ein Beispielergebnis zurück
    return {
      "name": "Beispiel Name",
      "email": "<EMAIL>",
      "berufserfahrung":
          "Diese Funktion wurde aus Sicherheitsgründen auf Firebase Cloud Functions umgestellt.",
      "fähigkeiten": "Funktion vorübergehend nicht verfügbar.",
      "bildung": "",
      "stärken": "",
      "sprachen": "",
      "zertifizierungen": "",
      "interessen": "",
    };
  }

  _sendToDeepSeek(String prompt) async {
    print('--- Die direkte Nutzung von DeepSeek API ist deaktiviert ---');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizationsWrapper.of(context).apiCallsDisabled),
        duration: const Duration(seconds: 5),
      ),
    );

    // Daten wurden bereits durch _saveData() gespeichert
    print('Daten wurden lokal gespeichert: $prompt');
  }

  // Testfunktion zur Überprüfung von AI-Änderungen
  void testAIChange() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(AppLocalizationsWrapper.of(context).aiTest),
            content: Text(AppLocalizationsWrapper.of(context).aiChangeActive),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(AppLocalizationsWrapper.of(context).ok),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizationsWrapper.of(context).settingsTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // AI-Test Button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ElevatedButton(
                  onPressed: testAIChange,
                  child: Text(AppLocalizations.of(context).aiTest),
                ),
              ),
              _buildSectionTitle('Lebenslauf'),
              const SizedBox(height: 8),
              _buildCVUploadSection(),

              const SizedBox(height: 24),
              _buildSectionTitle('Persönliche Informationen'),
              const SizedBox(height: 8),
              _buildTextField(nameController, 'Name'),
              _buildTextField(emailController, 'E-Mail'),

              const SizedBox(height: 24),
              _buildSectionTitle('Beruflicher Hintergrund'),
              const SizedBox(height: 8),
              _buildTextField(
                experienceController,
                'Erfahrung',
                maxLines: 3,
                hint: 'Beschreibe kurz deinen beruflichen Werdegang',
              ),
              SwitchListTile(
                title: const Text('Erfahrung einbeziehen'),
                value: includeExperience,
                onChanged: (bool value) {
                  setState(() {
                    includeExperience = value;
                  });
                },
              ),
              const SizedBox(height: 8),
              _buildTextField(
                skillsController,
                'Fähigkeiten',
                maxLines: 3,
                hint: 'Deine wichtigsten Fähigkeiten und Kompetenzen',
              ),
              const SizedBox(height: 8),
              _buildTextField(
                interestsController,
                'Interessen',
                maxLines: 2,
                hint: 'Deine beruflichen Interessen und Schwerpunkte',
              ),

              const SizedBox(height: 24),
              _buildSectionTitle('KI-Anpassungen'),
              const SizedBox(height: 8),
              _buildDropdown(),

              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    _saveData();
                    String prompt =
                        'Name: ${nameController.text}, E-Mail: ${emailController.text}, Erfahrung: ${experienceController.text}, Fähigkeiten: ${skillsController.text}, Interessen: ${interestsController.text}, Stil: $selectedStyle, Erfahrung einbeziehen: $includeExperience';
                    _sendToDeepSeek(prompt);
                  },
                  child: const Text('Speichern'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCVUploadSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? AppTheme.surfaceDarkColor : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow:
            isDarkMode
                ? []
                : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
        border: Border.all(
          color:
              isDarkMode
                  ? AppTheme.borderDarkColor
                  : cvAnalysisComplete
                  ? Colors.green.shade300
                  : Colors.blue.shade200,
          width: isDarkMode ? 0.5 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color:
                      isDarkMode
                          ? AppTheme.primaryDarkColor.withValues(alpha: 0.3)
                          : cvAnalysisComplete
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.blue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  cvAnalysisComplete
                      ? Icons.check_circle
                      : Icons.upload_file_rounded,
                  color:
                      isDarkMode
                          ? AppTheme.primaryLightColor
                          : cvAnalysisComplete
                          ? Colors.green.shade600
                          : Colors.blue.shade600,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      cvAnalysisComplete
                          ? 'Lebenslauf analysiert'
                          : 'Lebenslauf hochladen',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color:
                            isDarkMode
                                ? AppTheme.textPrimaryDarkColor
                                : AppTheme.textPrimaryLightColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      uploadedCVFileName ?? 'Keine Datei ausgewählt',
                      style: TextStyle(
                        color:
                            isDarkMode
                                ? AppTheme.textSecondaryDarkColor
                                : Colors.grey.shade700,
                        fontSize: 14,
                        fontStyle:
                            uploadedCVFileName != null
                                ? FontStyle.normal
                                : FontStyle.italic,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color:
                  isDarkMode
                      ? AppTheme.backgroundDarkColor
                      : cvAnalysisComplete
                      ? Colors.green.shade50
                      : Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    isDarkMode
                        ? AppTheme.borderDarkColor
                        : cvAnalysisComplete
                        ? Colors.green.shade200
                        : Colors.blue.shade100,
                width: isDarkMode ? 0.5 : 1,
              ),
            ),
            child: Text(
              cvAnalysisComplete
                  ? 'Dein Lebenslauf wurde analysiert und kann für Bewerbungen verwendet werden.'
                  : 'Lade deinen Lebenslauf hoch, um personalisierte Bewerbungen zu erstellen und deine beruflichen Fähigkeiten automatisch zu extrahieren.',
              style: TextStyle(
                color:
                    isDarkMode
                        ? AppTheme.textPrimaryDarkColor
                        : cvAnalysisComplete
                        ? Colors.green.shade800
                        : Colors.blue.shade800,
                fontSize: 13,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: Icon(
                    cvAnalysisComplete ? Icons.refresh : Icons.upload_file,
                    size: 18,
                    color: Colors.white,
                  ),
                  label: Text(
                    uploadedCVFileName == null
                        ? 'Lebenslauf hochladen'
                        : cvAnalysisComplete
                        ? 'Neuen Lebenslauf hochladen'
                        : 'Datei auswählen',
                    style: const TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isDarkMode
                            ? AppTheme.primaryColor
                            : cvAnalysisComplete
                            ? Colors.green.shade600
                            : Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    elevation: isDarkMode ? 0 : 1,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: isAnalyzingCV ? null : _pickAndUploadCV,
                ),
              ),
              if (uploadedCVFileName != null)
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: ElevatedButton.icon(
                    icon: const Icon(
                      Icons.delete_outline,
                      size: 18,
                      color: Colors.white,
                    ),
                    label: const Text(
                      'Löschen',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isDarkMode
                              ? Colors.red.shade900
                              : Colors.red.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: isDarkMode ? 0 : 1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: _deleteCVData,
                  ),
                ),
            ],
          ),
          if (isAnalyzingCV)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color:
                    isDarkMode
                        ? AppTheme.backgroundDarkColor
                        : Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      isDarkMode
                          ? AppTheme.borderDarkColor
                          : Colors.amber.shade200,
                  width: isDarkMode ? 0.5 : 1,
                ),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isDarkMode
                            ? AppTheme.primaryLightColor
                            : Colors.amber.shade700,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Analyse läuft...',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color:
                                isDarkMode
                                    ? AppTheme.textPrimaryDarkColor
                                    : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Dein Lebenslauf wird automatisch analysiert und relevante Informationen werden extrahiert.',
                          style: TextStyle(
                            color:
                                isDarkMode
                                    ? AppTheme.textSecondaryDarkColor
                                    : Colors.grey.shade800,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          if (cvAnalysisComplete && cvAnalysisResult != null)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: SwitchListTile(
                title: Text(
                  'Felder automatisch ausfüllen',
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        isDarkMode
                            ? AppTheme.textPrimaryDarkColor
                            : Colors.black87,
                  ),
                ),
                subtitle: Text(
                  'Extrahierte Informationen aus dem Lebenslauf in die Eingabefelder übernehmen',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        isDarkMode
                            ? AppTheme.textSecondaryDarkColor
                            : Colors.grey.shade700,
                  ),
                ),
                value: autoFillFieldsFromCV,
                activeColor:
                    isDarkMode
                        ? AppTheme.primaryLightColor
                        : Colors.green.shade600,
                contentPadding: EdgeInsets.zero,
                onChanged: (value) {
                  setState(() {
                    autoFillFieldsFromCV = value;
                    if (value) {
                      _updateFieldsFromCVAnalysis({});
                    }
                  });
                },
                dense: true,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label, {
    int maxLines = 1,
    String? hint,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
        maxLines: maxLines,
        keyboardType:
            maxLines > 1 ? TextInputType.multiline : TextInputType.text,
      ),
    );
  }

  Widget _buildDropdown() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: selectedStyle,
          hint: const Text('Bewerbungsstil'),
          onChanged: (String? newValue) {
            setState(() {
              selectedStyle = newValue!;
            });
          },
          items: const [
            DropdownMenuItem(
              value: 'Professionell',
              child: Text('Professionell'),
            ),
            DropdownMenuItem(value: 'Kreativ', child: Text('Kreativ')),
            DropdownMenuItem(value: 'Technisch', child: Text('Technisch')),
            DropdownMenuItem(
              value: 'Passend zu meinem Stil',
              child: Text('Passend zu meinem Stil'),
            ),
            DropdownMenuItem(value: 'Akademisch', child: Text('Akademisch')),
            DropdownMenuItem(value: 'Förmlich', child: Text('Förmlich')),
            DropdownMenuItem(value: 'Modern', child: Text('Modern')),
          ],
        ),
      ),
    );
  }

  void _updateFieldsFromAnalysis() {
    if (cvAnalysisResult != null && autoFillFieldsFromCV) {
      // Name aus dem Lebenslauf extrahieren, falls vorhanden und das Feld leer ist
      if (nameController.text.isEmpty &&
          cvAnalysisResult!.containsKey('name') &&
          cvAnalysisResult!['name'] != null &&
          cvAnalysisResult!['name'].toString().trim().isNotEmpty) {
        nameController.text = cvAnalysisResult!['name'];
      }

      // E-Mail aus dem Lebenslauf extrahieren, falls vorhanden und das Feld leer ist
      if (emailController.text.isEmpty &&
          cvAnalysisResult!.containsKey('email') &&
          cvAnalysisResult!['email'] != null &&
          cvAnalysisResult!['email'].toString().trim().isNotEmpty) {
        emailController.text = cvAnalysisResult!['email'];
      }

      // Fähigkeiten aus dem Lebenslauf extrahieren
      if (cvAnalysisResult!.containsKey('skills') &&
          cvAnalysisResult!['skills'] != null &&
          cvAnalysisResult!['skills'].toString().trim().isNotEmpty) {
        skillsController.text = cvAnalysisResult!['skills'];
      }

      // Berufserfahrung aus dem Lebenslauf extrahieren
      if (cvAnalysisResult!.containsKey('experience') &&
          cvAnalysisResult!['experience'] != null &&
          cvAnalysisResult!['experience'].toString().trim().isNotEmpty) {
        experienceController.text = cvAnalysisResult!['experience'];
      }

      // Interessen aus dem Lebenslauf extrahieren, falls vorhanden und das Feld leer ist
      if (interestsController.text.isEmpty &&
          cvAnalysisResult!.containsKey('interests') &&
          cvAnalysisResult!['interests'] != null &&
          cvAnalysisResult!['interests'].toString().trim().isNotEmpty) {
        interestsController.text = cvAnalysisResult!['interests'];
      }

      // Bildungshintergrund zu Erfahrungen hinzufügen, wenn vorhanden
      if (cvAnalysisResult!.containsKey('education') &&
          cvAnalysisResult!['education'] != null &&
          cvAnalysisResult!['education'].toString().trim().isNotEmpty) {
        final education = cvAnalysisResult!['education'];
        if (experienceController.text.isNotEmpty) {
          // Nur hinzufügen, wenn nicht bereits enthalten
          if (!experienceController.text.contains(education)) {
            experienceController.text =
                '${experienceController.text}\n\nBildung: $education';
          }
        } else {
          experienceController.text = 'Bildung: $education';
        }
      }

      // Stärken zu Skills hinzufügen, wenn vorhanden
      if (cvAnalysisResult!.containsKey('strengths') &&
          cvAnalysisResult!['strengths'] != null &&
          cvAnalysisResult!['strengths'].toString().trim().isNotEmpty) {
        final strengths = cvAnalysisResult!['strengths'];
        if (skillsController.text.isNotEmpty) {
          // Nur hinzufügen, wenn nicht bereits enthalten
          if (!skillsController.text.contains(strengths)) {
            skillsController.text =
                '${skillsController.text}\n\nStärken: $strengths';
          }
        } else {
          skillsController.text = 'Stärken: $strengths';
        }
      }

      // Sprachen zu Skills hinzufügen, wenn vorhanden
      if (cvAnalysisResult!.containsKey('languages') &&
          cvAnalysisResult!['languages'] != null &&
          cvAnalysisResult!['languages'].toString().trim().isNotEmpty) {
        final languages = cvAnalysisResult!['languages'];
        if (skillsController.text.isNotEmpty) {
          // Nur hinzufügen, wenn nicht bereits enthalten
          if (!skillsController.text.contains(languages)) {
            skillsController.text =
                '${skillsController.text}\n\nSprachen: $languages';
          }
        } else {
          skillsController.text = 'Sprachen: $languages';
        }
      }

      // Zertifizierungen zu Skills hinzufügen, wenn vorhanden
      if (cvAnalysisResult!.containsKey('certifications') &&
          cvAnalysisResult!['certifications'] != null &&
          cvAnalysisResult!['certifications'].toString().trim().isNotEmpty) {
        final certifications = cvAnalysisResult!['certifications'];
        if (skillsController.text.isNotEmpty) {
          // Nur hinzufügen, wenn nicht bereits enthalten
          if (!skillsController.text.contains(certifications)) {
            skillsController.text =
                '${skillsController.text}\n\nZertifizierungen: $certifications';
          }
        } else {
          skillsController.text = 'Zertifizierungen: $certifications';
        }
      }

      // Speichere die Änderungen
      _saveData();
    }
  }

  Future<void> _deleteCVData() async {
    final shouldDelete =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Lebenslauf löschen'),
                content: const Text(
                  'Möchtest du deinen hochgeladenen Lebenslauf wirklich löschen? '
                  'Die daraus extrahierten Informationen bleiben in deinen Einstellungen erhalten.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Abbrechen'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: const Text('Löschen'),
                  ),
                ],
              ),
        ) ??
        false;

    if (!shouldDelete) return;

    setState(() => isLoading = true);

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final String? cvFilePath = prefs.getString('uploadedCVFilePath');
      if (cvFilePath != null) {
        try {
          final cvFile = File(cvFilePath);
          if (await cvFile.exists()) {
            await cvFile.delete();
            print('Lebenslauf-Datei gelöscht: $cvFilePath');
          }
        } catch (e) {
          print('Fehler beim Löschen der Lebenslauf-Datei: $e');
        }
      }

      await prefs.remove('uploadedCVFileName');
      await prefs.remove('uploadedCVFilePath');
      await prefs.remove('cvAnalysisComplete');
      await prefs.remove('cvAnalysisResult');

      setState(() {
        uploadedCVFileName = null;
        cvAnalysisComplete = false;
        cvAnalysisResult = null;
        isAnalyzingCV = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Lebenslauf erfolgreich gelöscht'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('Fehler beim Löschen des Lebenslaufs: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fehler beim Löschen: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => isLoading = false);
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    experienceController.dispose();
    skillsController.dispose();
    interestsController.dispose();
    super.dispose();
  }
}

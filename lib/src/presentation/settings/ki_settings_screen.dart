import 'package:flutter/material.dart';

class KiSettingsScreen extends StatefulWidget {
  const KiSettingsScreen({super.key});

  @override
  _KiSettingsScreenState createState() => _KiSettingsScreenState();
}

class _KiSettingsScreenState extends State<KiSettingsScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController experienceController = TextEditingController();

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    experienceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('KI Einstellungen')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text<PERSON>ield(
              controller: nameController,
              decoration: InputDecoration(labelText: 'Name'),
            ),
            TextField(
              controller: emailController,
              decoration: InputDecoration(labelText: 'E-Mail'),
            ),
            TextField(
              controller: experienceController,
              decoration: InputDecoration(labelText: 'Erfahrung'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Speichern der Eingaben und Senden an DeepSeek
                String prompt =
                    'Name: ${nameController.text}, E-Mail: ${emailController.text}, Erfahrung: ${experienceController.text}';
                print('Prompt an DeepSeek: $prompt');
                // Hier die Logik zum Senden an DeepSeek einfügen
              },
              child: Text('Speichern und Senden'),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/locale_provider.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

/// Dialog zur Auswahl der Sprache
class LanguageSelectionDialog extends ConsumerWidget {
  const LanguageSelectionDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(localeProvider);
    final l10n = AppLocalizationsWrapper.of(context);

    // Gruppiere Sprachen nach Kontinenten/Regionen für bessere Organisation
    final Map<String, List<Locale>> groupedLocales = {
      'Europa': [
        const Locale('de'), // Deutsch
        const Locale('en'), // Englisch
        const Locale('fr'), // Französisch
        const Locale('es'), // Spanisch
        const Locale('it'), // Italienisch
        const Locale('pt'), // Portugiesisch
        const Locale('ru'), // Russisch
        const Locale('nl'), // Niederländisch
        const Locale('pl'), // Polnisch
        const Locale('sv'), // Schwedisch
      ],
      'Asien': [
        const Locale('zh'), // Chinesisch
        const Locale('ja'), // Japanisch
        const Locale('ko'), // Koreanisch
        const Locale('hi'), // Hindi
      ],
      'Naher Osten': [
        const Locale('ar'), // Arabisch
        const Locale('tr'), // Türkisch
      ],
    };

    return AlertDialog(
      title: Text(l10n.selectLanguage),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView(
          shrinkWrap: true,
          children: [
            // Aktuell ausgewählte Sprache hervorheben
            Container(
              padding: const EdgeInsets.all(8.0),
              margin: const EdgeInsets.only(bottom: 16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                children: [
                  const Icon(Icons.language),
                  const SizedBox(width: 8.0),
                  Text(
                    '${l10n.detectedLanguage}: ${languageNames[currentLocale.languageCode] ?? currentLocale.languageCode}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),

            // Sprachen nach Regionen gruppiert anzeigen
            for (final entry in groupedLocales.entries)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Regionstitel
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      entry.key,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),

                  // Sprachen in dieser Region
                  for (final locale in entry.value)
                    Builder(
                      builder: (context) {
                        final isSelected =
                            locale.languageCode == currentLocale.languageCode;
                        final languageName =
                            languageNames[locale.languageCode] ??
                            locale.languageCode;

                        return ListTile(
                          title: Text(languageName),
                          subtitle: Text(
                            _getLocalizedLanguageName(
                              locale.languageCode,
                              l10n,
                            ),
                          ),
                          trailing:
                              isSelected
                                  ? Icon(
                                    Icons.check_circle,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  )
                                  : null,
                          selected: isSelected,
                          selectedTileColor: Theme.of(
                            context,
                          ).colorScheme.primaryContainer.withAlpha(80),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          onTap: () async {
                            if (!isSelected) {
                              // Sprache ändern
                              await ref
                                  .read(localeProvider.notifier)
                                  .setLocale(locale);

                              if (context.mounted) {
                                // Dialog schließen
                                Navigator.of(context).pop();

                                // Zeige eine Snackbar an, dass die Sprache geändert wurde
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      AppLocalizationsWrapper.of(
                                        context,
                                      ).languageChanged,
                                    ),
                                    duration: const Duration(seconds: 3),
                                  ),
                                );
                              }
                            }
                          },
                        );
                      },
                    ),

                  const Divider(),
                ],
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
      ],
    );
  }

  // Hilfsmethode, um den lokalisierten Sprachnamen zu erhalten
  String _getLocalizedLanguageName(
    String languageCode,
    AppLocalizationsWrapper l10n,
  ) {
    switch (languageCode) {
      case 'de':
        return l10n.german;
      case 'en':
        return l10n.english;
      case 'fr':
        return l10n.french;
      case 'es':
        return l10n.spanish;
      case 'it':
        return l10n.italian;
      case 'pt':
        return 'Português';
      case 'ru':
        return 'Русский';
      case 'zh':
        return '中文';
      case 'ja':
        return '日本語';
      case 'ar':
        return 'العربية';
      case 'hi':
        return 'हिन्दी';
      case 'tr':
        return 'Türkçe';
      case 'ko':
        return '한국어';
      case 'nl':
        return 'Nederlands';
      case 'pl':
        return 'Polski';
      case 'sv':
        return 'Svenska';
      default:
        return languageCode.toUpperCase();
    }
  }
}

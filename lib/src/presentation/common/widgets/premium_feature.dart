import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_screen.dart';

/// Ein Widget, das eine Premium-Funktion überwacht und
/// Nicht-Premium-Nutzern eine verschwommene Vorschau mit Upgrade-Aufforderung zeigt.
///
/// Verwende dieses Widget, um Premium-Funktionen in der App zu kennzeichnen und 
/// Nicht-Premium-Nutzer zur Conversion zu bewegen.
class PremiumFeature extends ConsumerWidget {
  /// Erstellt ein Widget für eine Premium-Funktion.
  ///
  /// Der [child] ist die Premium-Funktion selbst.
  /// [premiumText] ist der Text, der Nicht-Premium-Nutzern angezeigt wird.
  /// [blurStrength] kontrolliert die Stärke des Verschwommenheitseffekts (0-20).
  const PremiumFeature({
    super.key,
    required this.child,
    this.premiumText = 'Diese Funktion ist nur für Premium-Nutzer verfügbar',
    this.blurStrength = 5.0,
    this.showUpgradeButton = true,
    this.backgroundOpacity = 0.7,
  }) : assert(blurStrength >= 0 && blurStrength <= 20);

  /// Der Widget-Baum, der die Premium-Funktion enthält.
  final Widget child;
  
  /// Text, der angezeigt wird, wenn der Nutzer kein Premium hat.
  final String premiumText;
  
  /// Stärke des Verschwommenheitseffekts.
  final double blurStrength;
  
  /// Ob der Upgrade-Button angezeigt werden soll.
  final bool showUpgradeButton;
  
  /// Deckkraft des Hintergrunds.
  final double backgroundOpacity;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // OVERRIDE: Der premiumOverrideProvider gibt immer false zurück,
    // um alle Premium-Funktionen als gesperrt anzuzeigen
    final isPremium = ref.watch(premiumOverrideProvider);
    
    // Wenn Premium aktiv ist, zeige die Funktion ohne Einschränkung
    if (isPremium) {
      return child;
    }
    
    // Nicht-Premium-Nutzer sehen eine verschwommene Vorschau mit Upgrade-Aufforderung
    return Stack(
      children: [
        // Verschwommene Version der Premium-Funktion
        Opacity(
          opacity: 0.7,
          child: child,
        ),
        
        // Overlay mit Upgrade-Aufforderung
        Positioned.fill(
          child: ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: blurStrength, 
                sigmaY: blurStrength,
              ),
              child: Container(
                color: Colors.black.withValues(alpha: backgroundOpacity),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Premium-Icon
                        Icon(
                          Icons.workspace_premium,
                          size: 50,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(height: 16),
                        
                        // Premium-Infotext
                        Text(
                          premiumText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        // Upgrade-Button
                        if (showUpgradeButton) ...[
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: () {
                              // Navigation zum Premium-Screen
                              context.go('/premium');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32, 
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Premium freischalten',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Ein Widget, das eine Conditional Wrap für Premium-Funktionen bereitstellt.
/// 
/// Verwendet `PremiumFeature` nur für Nicht-Premium-Nutzer, sodass
/// Premium-Nutzer die Funktion direkt nutzen können.
class MaybePremiumFeature extends ConsumerWidget {
  /// Erstellt ein Widget, das basierend auf dem Premium-Status entscheidet,
  /// ob eine Funktion umhüllt werden soll.
  const MaybePremiumFeature({
    super.key,
    required this.child,
    this.premiumText = 'Diese Funktion ist nur für Premium-Nutzer verfügbar',
    this.blurStrength = 5.0,
    this.showUpgradeButton = true,
    this.isAlwaysPremium = true,
  });

  /// Der Widget-Baum, der die möglicherweise Premium-Funktion enthält.
  final Widget child;
  
  /// Text, der angezeigt wird, wenn der Nutzer kein Premium hat.
  final String premiumText;
  
  /// Stärke des Verschwommenheitseffekts.
  final double blurStrength;
  
  /// Ob der Upgrade-Button angezeigt werden soll.
  final bool showUpgradeButton;
  
  /// Ob die Funktion immer Premium erfordert.
  /// Bei `false` können auch äußere Bedingungen festlegen,
  /// ob die Funktion als Premium behandelt wird.
  final bool isAlwaysPremium;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // OVERRIDE: Der premiumOverrideProvider gibt immer false zurück,
    // um alle Premium-Funktionen als gesperrt anzuzeigen
    final isPremium = ref.watch(premiumOverrideProvider);
    
    // Wenn der Nutzer Premium hat oder die Funktion nicht Premium erfordert,
    // zeige die Funktion direkt
    if (isPremium || !isAlwaysPremium) {
      return child;
    }
    
    // Ansonsten zeige die verschwommene Version mit Upgrade-Aufforderung
    return PremiumFeature(
      premiumText: premiumText,
      blurStrength: blurStrength,
      showUpgradeButton: showUpgradeButton,
      child: child,
    );
  }
} 
 
 
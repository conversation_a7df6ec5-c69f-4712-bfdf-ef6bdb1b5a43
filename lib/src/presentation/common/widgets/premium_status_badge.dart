import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_benefits_screen.dart';
import 'package:intl/intl.dart';

/// Widget zur Anzeige des Premium-Status als Badge
class PremiumStatusBadge extends ConsumerWidget {
  final bool showIcon;
  final bool showLabel;
  final bool isClickable;
  final double iconSize;
  final double fontSize;

  const PremiumStatusBadge({
    super.key,
    this.showIcon = true,
    this.showLabel = true,
    this.isClickable = true,
    this.iconSize = 16,
    this.fontSize = 12,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('PremiumStatusBadge');

    // Plan & Remaining ausschließlich aus Server-Providern beziehen
    final remainingApplicationsAsync = ref.watch(remainingApplicationsProvider);
    final currentSubscriptionAsync = ref.watch(currentSubscriptionProvider);

    return remainingApplicationsAsync.when(
      data: (remainingData) {
        final planType =
            (remainingData['plan_type'] as String?)?.toLowerCase() ?? 'free';
        final isUnlimited = remainingData['unlimited'] == true;
        final isPremium =
            planType == 'premium' || planType == 'unlimited' || isUnlimited;

        final expiryDate = currentSubscriptionAsync.maybeWhen(
          data: (sub) => sub?.endDate,
          orElse: () => null,
        );

        // Zeige Badge an
        final color = _getPlanColor(planType);
        final planName = _getPlanName(planType);

        Widget badge = Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withAlpha(40),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withAlpha(100), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showIcon) ...[
                Icon(
                  isPremium
                      ? Icons.workspace_premium
                      : Icons.workspace_premium_outlined,
                  color: color,
                  size: iconSize,
                ),
                const SizedBox(width: 4),
              ],
              if (showLabel)
                Text(
                  planName,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: fontSize,
                  ),
                ),

              // Zeige Ablaufdatum an, wenn vorhanden und Premium
              if (expiryDate != null && isPremium) ...[
                const SizedBox(width: 4),
                Text(
                  '(${DateFormat('dd.MM.yy').format(expiryDate)})',
                  style: TextStyle(color: color, fontSize: fontSize - 2),
                ),
              ],

              // Zeige verbleibende Bewerbungen an
              if (remainingApplicationsAsync.hasValue) ...[
                const SizedBox(width: 4),
                Text(
                  remainingApplicationsAsync.value!['unlimited'] == true
                      ? '(∞)'
                      : '(${remainingApplicationsAsync.value!['remaining']})',
                  style: TextStyle(color: color, fontSize: fontSize - 2),
                ),
              ],
            ],
          ),
        );

        if (isClickable) {
          return InkWell(
            onTap: () => _navigateToPremiumScreen(context),
            borderRadius: BorderRadius.circular(12),
            child: badge,
          );
        }

        return badge;
      },
      loading:
          () => const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  void _navigateToPremiumScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PremiumBenefitsScreen()),
    );
  }

  String _getPlanName(String? planType) {
    switch (planType) {
      case 'basic':
        return 'Basic';
      case 'pro':
        return 'Pro';
      case 'unlimited':
        return 'Unlimited';
      default:
        return 'Premium';
    }
  }

  Color _getPlanColor(String? planType) {
    switch (planType) {
      case 'basic':
        return Colors.blue;
      case 'pro':
        return Colors.purple;
      case 'unlimited':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }
}

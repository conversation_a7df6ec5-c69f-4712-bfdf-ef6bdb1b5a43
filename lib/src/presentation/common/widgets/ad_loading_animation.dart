import 'package:flutter/material.dart';

/// Eine Widget-Klasse, die eine einfache Ladeanimation für Werbeanzeigen darstellt.
///
/// Diese Animation wird angezeigt, während eine Werbeanzeige geladen wird.
/// Verwendet nur Flutter-eigene Widgets ohne externe Assets.
class AdLoadingAnimation extends StatefulWidget {
  /// Erstellt eine neue Instanz der [AdLoadingAnimation].
  const AdLoadingAnimation({
    super.key,
    this.message = 'Werbung wird geladen...',
    this.backgroundColor,
    this.loaderColor,
    this.textColor,
    this.onClose, // NEU: Callback zum Schließen der Werbung
  });

  /// Callback, der aufgerufen wird, wenn der Schließen-Button gedrückt wird.
  final VoidCallback? onClose;

  /// Die Nachricht, die unter der Animation angezeigt wird.
  final String message;

  /// Die Hintergrundfarbe der Animation.
  final Color? backgroundColor;

  /// Die Farbe des Loaders.
  final Color? loaderColor;

  /// Die Textfarbe.
  final Color? textColor;

  @override
  State<AdLoadingAnimation> createState() => _AdLoadingAnimationState();
}

class _AdLoadingAnimationState extends State<AdLoadingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = widget.backgroundColor ?? theme.colorScheme.surface.withValues(alpha: 0.85);
    final effectiveLoaderColor = widget.loaderColor ?? theme.colorScheme.primary;
    final effectiveTextColor = widget.textColor ?? theme.colorScheme.onSurface;

    return Container(
      color: effectiveBackgroundColor,
      child: Center(
        child: Container(
          width: 200,
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Einfache animierte Punkte
              _buildSimpleAnimation(effectiveLoaderColor),
              const SizedBox(height: 16),
              // Nachricht
              Text(
                widget.message,
                style: TextStyle(
                  color: effectiveTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              // NEU: Schließen-Button für Testzwecke
              if (widget.onClose != null)
                Padding(
                  padding: const EdgeInsets.only(top: 24.0),
                  child: ElevatedButton(
                    onPressed: widget.onClose,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.error, // Roter Button
                      foregroundColor: theme.colorScheme.onError,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Werbung schließen (Test)'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Baut eine einfache animierte Ladeanimation mit drei Punkten.
  Widget _buildSimpleAnimation(Color loaderColor) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildAnimatedDot(loaderColor, 0),
              const SizedBox(width: 8),
              _buildAnimatedDot(loaderColor, 0.2),
              const SizedBox(width: 8),
              _buildAnimatedDot(loaderColor, 0.4),
            ],
          ),
        );
      },
    );
  }

  /// Baut einen einzelnen animierten Punkt.
  Widget _buildAnimatedDot(Color color, double delay) {
    final animationValue = (_animation.value + delay) % 1.0;
    final scale = 0.5 + (0.5 * (1 - (animationValue - 0.5).abs() * 2).clamp(0.0, 1.0));
    final opacity = 0.3 + (0.7 * (1 - (animationValue - 0.5).abs() * 2).clamp(0.0, 1.0));

    return Transform.scale(
      scale: scale,
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: color.withValues(alpha: opacity),
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

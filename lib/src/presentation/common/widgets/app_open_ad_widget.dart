import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/ad_service.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';
import 'package:ki_test/src/infrastructure/providers/providers.dart';
import 'package:ki_test/src/infrastructure/utils/logger.dart';

/// Widget für App-Open-Ads beim App-Start
class AppOpenAdWidget extends ConsumerStatefulWidget {
  final Widget child;
  final VoidCallback? onAdDismissed;

  const AppOpenAdWidget({
    super.key,
    required this.child,
    this.onAdDismissed,
  });

  @override
  ConsumerState<AppOpenAdWidget> createState() => _AppOpenAdWidgetState();
}

class _AppOpenAdWidgetState extends ConsumerState<AppOpenAdWidget> {
  bool _adShown = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showAppOpenAdIfNeeded();
    });
  }

  Future<void> _showAppOpenAdIfNeeded() async {
    if (_adShown) return;

    try {
      final isPremium = ref.read(isPremiumProvider);
      final adService = ref.read(adServiceProvider);

      // Nur für kostenlose User
      if (!isPremium) {
        log.i('Zeige App-Open-Ad für kostenlosen User');
        
        await adService.showAppOpenAd(
          onAdDismissed: () {
            log.i('App-Open-Ad geschlossen');
            widget.onAdDismissed?.call();
          },
          onAdFailedToShow: () {
            log.w('App-Open-Ad konnte nicht angezeigt werden');
            widget.onAdDismissed?.call();
          },
        );
        
        _adShown = true;
      } else {
        log.i('Premium-User - keine App-Open-Ad');
      }
    } catch (e) {
      log.e('Fehler beim Anzeigen der App-Open-Ad: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

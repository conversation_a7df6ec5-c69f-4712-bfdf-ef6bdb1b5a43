import 'dart:math';
import 'package:flutter/material.dart';

/// Animation Variante 6: Moderne Partikel-Animation mit Farbwechsel
class ModernParticleAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const ModernParticleAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<ModernParticleAnimation> createState() =>
      _ModernParticleAnimationState();
}

class _ModernParticleAnimationState extends State<ModernParticleAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final Random _random = Random(42); // Fester Seed für konsistente Partikel

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 4000),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return RepaintBoundary(
            child: CustomPaint(
              painter: _ModernParticlePainter(
                animation: _controller.value,
                color: widget.color,
                random: _random,
              ),
            ),
          );
        },
      ),
    );
  }
}

class _ModernParticlePainter extends CustomPainter {
  final double animation;
  final Color color;
  final Random random;

  _ModernParticlePainter({
    required this.animation,
    required this.color,
    required this.random,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Farbpalette erstellen
    final baseColor = color;
    final accentColor1 = Color.lerp(baseColor, Colors.blue, 0.3)!;
    final accentColor2 = Color.lerp(baseColor, Colors.purple, 0.3)!;

    // Hintergrund-Kreis zeichnen
    final bgPaint =
        Paint()
          ..color = baseColor.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.9, bgPaint);

    // Partikel zeichnen
    final particleCount = 12;
    for (int i = 0; i < particleCount; i++) {
      // Partikelposition berechnen
      final angle = (i / particleCount) * 2 * pi;
      final oscillation = sin(animation * 2 * pi + (i * 0.5));
      final distance = radius * (0.4 + 0.4 * oscillation.abs());

      final x = center.dx + distance * cos(angle + animation * 2 * pi);
      final y = center.dy + distance * sin(angle + animation * 2 * pi);

      // Partikelgröße und Farbe
      final particleSize =
          2.0 + 3.0 * random.nextDouble() * (1.0 + oscillation * 0.5);
      final particleColor =
          i % 3 == 0 ? baseColor : (i % 3 == 1 ? accentColor1 : accentColor2);

      final particlePaint =
          Paint()
            ..color = particleColor.withValues(
              alpha: 0.7 + 0.3 * oscillation.abs(),
            )
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

      canvas.drawCircle(Offset(x, y), particleSize, particlePaint);

      // Verbindungslinien zwischen benachbarten Partikeln zeichnen
      if (i > 0) {
        final prevAngle = ((i - 1) / particleCount) * 2 * pi;
        final prevOscillation = sin(animation * 2 * pi + ((i - 1) * 0.5));
        final prevDistance = radius * (0.4 + 0.4 * prevOscillation.abs());

        final prevX =
            center.dx + prevDistance * cos(prevAngle + animation * 2 * pi);
        final prevY =
            center.dy + prevDistance * sin(prevAngle + animation * 2 * pi);

        final linePaint =
            Paint()
              ..color = baseColor.withValues(alpha: 0.2 * oscillation.abs())
              ..style = PaintingStyle.stroke
              ..strokeWidth = 0.5;

        canvas.drawLine(Offset(x, y), Offset(prevX, prevY), linePaint);
      }
    }

    // Zentraler pulsierender Punkt
    final corePaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              baseColor,
              accentColor1.withValues(alpha: 0.8),
              accentColor2.withValues(alpha: 0.5),
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius * 0.3))
          ..style = PaintingStyle.fill;

    canvas.drawCircle(
      center,
      radius * 0.2 * (0.8 + 0.2 * sin(animation * 3 * pi)),
      corePaint,
    );
  }

  @override
  bool shouldRepaint(_ModernParticlePainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 7: Minimalistischer Ladekreis mit Akzenten
class MinimalistLoadingAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const MinimalistLoadingAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<MinimalistLoadingAnimation> createState() =>
      _MinimalistLoadingAnimationState();
}

class _MinimalistLoadingAnimationState extends State<MinimalistLoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _MinimalistLoadingPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _MinimalistLoadingPainter extends CustomPainter {
  final double animation;
  final Color color;

  _MinimalistLoadingPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Hauptkreis zeichnen
    final circlePaint =
        Paint()
          ..color = color.withValues(alpha: 0.2)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    canvas.drawCircle(center, radius, circlePaint);

    // Rotierender Bogen
    final arcPaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0
          ..strokeCap = StrokeCap.round;

    // Zwei Bögen zeichnen, die sich gegenläufig bewegen
    final startAngle1 = animation * 2 * pi;
    final sweepAngle1 = pi * 0.5 + pi * 0.3 * sin(animation * 2 * pi);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle1,
      sweepAngle1,
      false,
      arcPaint,
    );

    final startAngle2 = startAngle1 + pi;
    final sweepAngle2 = pi * 0.3 + pi * 0.2 * sin(animation * 2 * pi + pi);

    final arcPaint2 =
        Paint()
          ..color = color.withValues(alpha: 0.7)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0
          ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      startAngle2,
      sweepAngle2,
      false,
      arcPaint2,
    );

    // Kleine Akzentpunkte
    final dotPaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    // Punkt am Ende des ersten Bogens
    final endAngle1 = startAngle1 + sweepAngle1;
    final dotX1 = center.dx + radius * cos(endAngle1);
    final dotY1 = center.dy + radius * sin(endAngle1);

    canvas.drawCircle(Offset(dotX1, dotY1), 3.0, dotPaint);

    // Punkt am Ende des zweiten Bogens
    final endAngle2 = startAngle2 + sweepAngle2;
    final dotX2 = center.dx + radius * 0.8 * cos(endAngle2);
    final dotY2 = center.dy + radius * 0.8 * sin(endAngle2);

    canvas.drawCircle(Offset(dotX2, dotY2), 2.0, dotPaint);
  }

  @override
  bool shouldRepaint(_MinimalistLoadingPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 8: Futuristische Neon-Animation
class NeonAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const NeonAnimation({super.key, this.color = Colors.white, this.size = 24.0});

  @override
  State<NeonAnimation> createState() => _NeonAnimationState();
}

class _NeonAnimationState extends State<NeonAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return RepaintBoundary(
            child: CustomPaint(
              painter: _NeonAnimationPainter(
                animation: _controller.value,
                color: widget.color,
              ),
            ),
          );
        },
      ),
    );
  }
}

class _NeonAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;

  _NeonAnimationPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Neon-Farben erstellen
    final neonColor = Color.lerp(color, Colors.cyan, 0.3)!;
    final neonAccent = Color.lerp(color, Colors.pink, 0.3)!;

    // Äußerer Glow-Effekt
    final outerGlowPaint =
        Paint()
          ..color = neonColor.withValues(alpha: 0.15)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15.0);

    canvas.drawCircle(center, radius * 1.2, outerGlowPaint);

    // Innerer Glow-Effekt
    final innerGlowPaint =
        Paint()
          ..color = neonAccent.withValues(alpha: 0.2)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10.0);

    canvas.drawCircle(center, radius * 0.8, innerGlowPaint);

    // Neon-Ringe zeichnen
    for (int i = 0; i < 3; i++) {
      final ringRadius = radius * (0.6 + i * 0.2);
      final ringOpacity = 0.7 - (i * 0.2);
      final ringWidth = 2.0 - (i * 0.5);

      final ringPaint =
          Paint()
            ..color =
                i.isEven
                    ? neonColor.withValues(alpha: ringOpacity)
                    : neonAccent.withValues(alpha: ringOpacity)
            ..style = PaintingStyle.stroke
            ..strokeWidth = ringWidth
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);

      // Animierte Rotation
      final startAngle = animation * 2 * pi * (i.isEven ? 1 : -1);
      final sweepAngle = pi * 1.8;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: ringRadius),
        startAngle,
        sweepAngle,
        false,
        ringPaint,
      );
    }

    // Pulsierende Punkte entlang der Ringe
    for (int i = 0; i < 5; i++) {
      final dotAngle = (i / 5) * 2 * pi + (animation * 2 * pi);
      final dotRadius = radius * (0.7 + 0.3 * sin(animation * 3 * pi + i));

      final dotX = center.dx + dotRadius * cos(dotAngle);
      final dotY = center.dy + dotRadius * sin(dotAngle);

      final dotPaint =
          Paint()
            ..color = (i % 2 == 0 ? neonColor : neonAccent).withValues(
              alpha: 0.8,
            )
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

      canvas.drawCircle(Offset(dotX, dotY), 3.0, dotPaint);
    }

    // Zentraler pulsierender Punkt
    final centerDotPaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              neonColor,
              neonAccent.withValues(alpha: 0.7),
              neonColor.withValues(alpha: 0.3),
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius * 0.3))
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

    canvas.drawCircle(
      center,
      radius * 0.2 * (0.8 + 0.2 * sin(animation * 4 * pi)),
      centerDotPaint,
    );
  }

  @override
  bool shouldRepaint(_NeonAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 9: Elegante Wellen mit Farbverlauf
class GradientWaveAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const GradientWaveAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<GradientWaveAnimation> createState() => _GradientWaveAnimationState();
}

class _GradientWaveAnimationState extends State<GradientWaveAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _GradientWavePainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _GradientWavePainter extends CustomPainter {
  final double animation;
  final Color color;

  _GradientWavePainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Farbverläufe erstellen
    final baseColor = color;
    final gradientColor1 = Color.lerp(baseColor, Colors.teal, 0.3)!;
    final gradientColor2 = Color.lerp(baseColor, Colors.amber, 0.3)!;

    // Hintergrund-Kreis mit Farbverlauf
    final bgPaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              baseColor.withValues(alpha: 0.1),
              gradientColor1.withValues(alpha: 0.05),
            ],
            stops: const [0.0, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius))
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, bgPaint);

    // Mehrere Wellenschichten zeichnen
    for (int i = 0; i < 3; i++) {
      final waveRadius = radius * (0.6 + i * 0.15);
      final wavePath = Path();

      // Wellenparameter
      final amplitude = 5.0 - (i * 1.0);
      final frequency = 8.0 + (i * 2.0);
      final phase = animation * 2 * pi * (i.isEven ? 1 : -1);

      // Wellenpfad erstellen
      for (double angle = 0; angle < 2 * pi; angle += 0.05) {
        final waveOffset = amplitude * sin(frequency * angle + phase);
        final x = center.dx + (waveRadius + waveOffset) * cos(angle);
        final y = center.dy + (waveRadius + waveOffset) * sin(angle);

        if (angle == 0) {
          wavePath.moveTo(x, y);
        } else {
          wavePath.lineTo(x, y);
        }
      }
      wavePath.close();

      // Wellenfarbe mit Farbverlauf
      final wavePaint =
          Paint()
            ..shader = SweepGradient(
              colors: [
                baseColor.withValues(alpha: 0.8 - (i * 0.2)),
                gradientColor1.withValues(alpha: 0.8 - (i * 0.2)),
                gradientColor2.withValues(alpha: 0.8 - (i * 0.2)),
                baseColor.withValues(alpha: 0.8 - (i * 0.2)),
              ],
              stops: const [0.0, 0.3, 0.7, 1.0],
              transform: GradientRotation(animation * 2 * pi),
            ).createShader(Rect.fromCircle(center: center, radius: waveRadius))
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0 - (i * 0.5);

      canvas.drawPath(wavePath, wavePaint);
    }

    // Zentraler pulsierender Punkt mit Farbverlauf
    final centerDotPaint =
        Paint()
          ..shader = RadialGradient(
            colors: [
              baseColor,
              gradientColor1.withValues(alpha: 0.8),
              gradientColor2.withValues(alpha: 0.5),
            ],
            stops: const [0.0, 0.5, 1.0],
          ).createShader(Rect.fromCircle(center: center, radius: radius * 0.3))
          ..style = PaintingStyle.fill;

    canvas.drawCircle(
      center,
      radius * 0.15 * (0.8 + 0.2 * sin(animation * 3 * pi)),
      centerDotPaint,
    );
  }

  @override
  bool shouldRepaint(_GradientWavePainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 10: Dynamische Punkte-Animation
class DynamicDotsAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const DynamicDotsAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<DynamicDotsAnimation> createState() => _DynamicDotsAnimationState();
}

class _DynamicDotsAnimationState extends State<DynamicDotsAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _DynamicDotsPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _DynamicDotsPainter extends CustomPainter {
  final double animation;
  final Color color;

  _DynamicDotsPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Farbpalette
    final baseColor = color;
    final accentColor = Color.lerp(baseColor, Colors.deepOrange, 0.3)!;

    // Hintergrund
    final bgPaint =
        Paint()
          ..color = baseColor.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, bgPaint);

    // Punkte in kreisförmiger Anordnung
    final dotCount = 8;
    for (int i = 0; i < dotCount; i++) {
      final angle = (i / dotCount) * 2 * pi;

      // Pulsierender Radius
      final pulsePhase = animation * 2 * pi + (i / dotCount) * 2 * pi;
      final pulseRadius = radius * (0.6 + 0.2 * sin(pulsePhase));

      final x = center.dx + pulseRadius * cos(angle);
      final y = center.dy + pulseRadius * sin(angle);

      // Punktgröße variiert mit der Animation
      final dotSize = 3.0 + 2.0 * sin(animation * 2 * pi + (i / dotCount) * pi);

      // Punktfarbe wechselt zwischen Basis- und Akzentfarbe
      final dotColor = i % 2 == 0 ? baseColor : accentColor;
      final dotOpacity =
          0.5 + 0.5 * sin(animation * 2 * pi + (i / dotCount) * pi);

      final dotPaint =
          Paint()
            ..color = dotColor.withValues(alpha: dotOpacity)
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);

      canvas.drawCircle(Offset(x, y), dotSize, dotPaint);

      // Verbindungslinien zwischen benachbarten Punkten
      if (i > 0) {
        final prevAngle = ((i - 1) / dotCount) * 2 * pi;
        final prevPulsePhase =
            animation * 2 * pi + ((i - 1) / dotCount) * 2 * pi;
        final prevPulseRadius = radius * (0.6 + 0.2 * sin(prevPulsePhase));

        final prevX = center.dx + prevPulseRadius * cos(prevAngle);
        final prevY = center.dy + prevPulseRadius * sin(prevAngle);

        final lineOpacity = 0.2 + 0.2 * sin(animation * 2 * pi);
        final linePaint =
            Paint()
              ..color = baseColor.withValues(alpha: lineOpacity)
              ..style = PaintingStyle.stroke
              ..strokeWidth = 1.0;

        canvas.drawLine(Offset(x, y), Offset(prevX, prevY), linePaint);
      }

      // Verbinde den letzten Punkt mit dem ersten
      if (i == dotCount - 1) {
        final firstAngle = 0.0;
        final firstPulsePhase = animation * 2 * pi;
        final firstPulseRadius = radius * (0.6 + 0.2 * sin(firstPulsePhase));

        final firstX = center.dx + firstPulseRadius * cos(firstAngle);
        final firstY = center.dy + firstPulseRadius * sin(firstAngle);

        final lineOpacity = 0.2 + 0.2 * sin(animation * 2 * pi);
        final linePaint =
            Paint()
              ..color = baseColor.withValues(alpha: lineOpacity)
              ..style = PaintingStyle.stroke
              ..strokeWidth = 1.0;

        canvas.drawLine(Offset(x, y), Offset(firstX, firstY), linePaint);
      }
    }

    // Zentraler Punkt
    final centerDotPaint =
        Paint()
          ..color = baseColor
          ..style = PaintingStyle.fill;

    canvas.drawCircle(
      center,
      radius * 0.15 * (0.8 + 0.2 * sin(animation * 3 * pi)),
      centerDotPaint,
    );
  }

  @override
  bool shouldRepaint(_DynamicDotsPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

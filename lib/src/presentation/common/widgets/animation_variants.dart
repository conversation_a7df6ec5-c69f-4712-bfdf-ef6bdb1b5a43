import 'dart:math';
import 'package:flutter/material.dart';

/// Animation Variante 1: Langsame, flüssige Animation mit Glow-Effekt
class AnimationVariant1 extends StatefulWidget {
  final Color color;
  final double size;
  
  const AnimationVariant1({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimationVariant1> createState() => _AnimationVariant1State();
}

class _AnimationVariant1State extends State<AnimationVariant1> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3500), // Langsame Animation für besseren Effekt
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AnimationVariant1Painter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _AnimationVariant1Painter extends CustomPainter {
  final double animation;
  final Color color;

  _AnimationVariant1Painter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Definiere mehrere Farben für eine hochwertigere Animation
    final colors = [
      color.withValues(alpha: 0.9),
      color.withValues(alpha: 0.8),
      color.withValues(alpha: 0.7),
      color.withValues(alpha: 0.6),
      color.withValues(alpha: 0.5),
      color.withValues(alpha: 0.4),
    ];

    // Zeichne mehrere Kreise mit unterschiedlichen Radien und Farben
    for (int i = 0; i < colors.length; i++) {
      final paint = Paint()
        ..color = colors[i]
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0
        ..strokeCap = StrokeCap.round;

      // Berechne den Startwinkel für jede Schicht mit unterschiedlichen Geschwindigkeiten
      final startAngle = (animation * 2 * pi * (1.0 + i * 0.1)) + (i * pi / 6);

      // Zeichne einen Kreisbogen mit unterschiedlichen Längen
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - (i * 1.5)),
        startAngle,
        pi * (1.0 + i * 0.2), // Unterschiedliche Bogenlängen
        false,
        paint,
      );
    }

    // Zeichne einen pulsierenden Punkt in der Mitte mit schönerem Effekt
    final opacity = 0.6 + ((sin(animation * 3 * pi) + 1) / 2 * 0.2);
    final dotPaint = Paint()
      ..color = color.withValues(alpha: opacity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0); // Weicher Rand

    // Zeichne zwei Kreise für einen Glow-Effekt
    canvas.drawCircle(
      center,
      radius * 0.35 * (0.8 + 0.2 * sin(animation * 2 * pi)),
      Paint()
        ..color = color.withValues(alpha: 0.3) // Glow-Effekt
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0),
    );

    canvas.drawCircle(
      center,
      radius * 0.25 * (0.8 + 0.2 * sin(animation * 2 * pi)),
      dotPaint,
    );
  }

  @override
  bool shouldRepaint(_AnimationVariant1Painter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 2: Elegante Wellen-Animation
class AnimationVariant2 extends StatefulWidget {
  final Color color;
  final double size;
  
  const AnimationVariant2({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimationVariant2> createState() => _AnimationVariant2State();
}

class _AnimationVariant2State extends State<AnimationVariant2> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 4000), // Noch langsamere Animation
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AnimationVariant2Painter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _AnimationVariant2Painter extends CustomPainter {
  final double animation;
  final Color color;

  _AnimationVariant2Painter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Welleneffekt mit Farbverlauf
    final gradient = SweepGradient(
      colors: [
        color.withValues(alpha: 0.2),
        color.withValues(alpha: 0.8),
        color.withValues(alpha: 0.2),
      ],
      stops: const [0.0, 0.5, 1.0],
      transform: GradientRotation(animation * 2 * pi),
    );

    // Zeichne drei Wellen mit unterschiedlichen Radien
    for (int i = 0; i < 3; i++) {
      final wavePaint = Paint()
        ..shader = gradient.createShader(
          Rect.fromCircle(center: center, radius: radius),
        )
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.5
        ..strokeCap = StrokeCap.round;

      final waveRadius = radius - (i * 4);
      final wavePhase = animation * 2 * pi + (i * pi / 3);

      final path = Path();
      for (double angle = 0; angle < 2 * pi; angle += 0.1) {
        final waveAmplitude = 3.0 * sin(5 * angle + wavePhase);
        final x = center.dx + (waveRadius + waveAmplitude) * cos(angle);
        final y = center.dy + (waveRadius + waveAmplitude) * sin(angle);

        if (angle == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      path.close();
      canvas.drawPath(path, wavePaint);
    }

    // Zeichne einen pulsierenden Punkt in der Mitte
    final centerDotRadius = radius * 0.3 * (0.8 + 0.2 * sin(animation * 3 * pi));
    final centerDotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    canvas.drawCircle(center, centerDotRadius, centerDotPaint);
  }

  @override
  bool shouldRepaint(_AnimationVariant2Painter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 3: Fließende Partikel-Animation
class AnimationVariant3 extends StatefulWidget {
  final Color color;
  final double size;
  
  const AnimationVariant3({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimationVariant3> createState() => _AnimationVariant3State();
}

class _AnimationVariant3State extends State<AnimationVariant3> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AnimationVariant3Painter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _AnimationVariant3Painter extends CustomPainter {
  final double animation;
  final Color color;
  final Random _random = Random(42); // Fester Seed für konsistente Partikel

  _AnimationVariant3Painter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    
    // Zeichne einen rotierenden Ring
    final ringPaint = Paint()
      ..color = color.withValues(alpha: 0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      animation * 2 * pi,
      pi * 1.5,
      false,
      ringPaint,
    );
    
    // Zeichne einen zweiten Ring in entgegengesetzter Richtung
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      -animation * 2 * pi,
      pi * 1.2,
      false,
      ringPaint,
    );
    
    // Zeichne Partikel, die um das Zentrum kreisen
    final particleCount = 8;
    for (int i = 0; i < particleCount; i++) {
      final particleAngle = (i / particleCount) * 2 * pi + (animation * 2 * pi);
      final distance = radius * (0.5 + 0.3 * _random.nextDouble());
      final particleX = center.dx + distance * cos(particleAngle);
      final particleY = center.dy + distance * sin(particleAngle);
      
      final particleSize = 2.0 + 2.0 * _random.nextDouble();
      final particleOpacity = 0.4 + 0.4 * sin(animation * 2 * pi + i);
      
      final particlePaint = Paint()
        ..color = color.withValues(alpha: particleOpacity)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);
      
      canvas.drawCircle(
        Offset(particleX, particleY),
        particleSize,
        particlePaint,
      );
    }
    
    // Zeichne einen pulsierenden Kern
    final corePaint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);
    
    canvas.drawCircle(
      center,
      radius * 0.25 * (0.8 + 0.2 * sin(animation * 3 * pi)),
      corePaint,
    );
  }

  @override
  bool shouldRepaint(_AnimationVariant3Painter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 4: Pulsierende Ringe mit Farbverlauf
class AnimationVariant4 extends StatefulWidget {
  final Color color;
  final double size;
  
  const AnimationVariant4({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimationVariant4> createState() => _AnimationVariant4State();
}

class _AnimationVariant4State extends State<AnimationVariant4> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3200),
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AnimationVariant4Painter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _AnimationVariant4Painter extends CustomPainter {
  final double animation;
  final Color color;

  _AnimationVariant4Painter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width * 0.4;
    
    // Erzeuge einen Farbverlauf mit Gold- und Rosa-Tönen
    final Color goldColor = Color.lerp(color, Colors.amber, 0.3)!;
    final Color pinkColor = Color.lerp(color, Colors.pink, 0.3)!;
    
    // Zeichne pulsierende Ringe
    for (int i = 0; i < 4; i++) {
      final ringPhase = animation + (i * 0.25);
      final normalizedPhase = ringPhase - ringPhase.floor();
      
      // Radius wächst und verschwindet dann
      final ringRadius = maxRadius * normalizedPhase;
      final ringOpacity = 0.7 * (1.0 - normalizedPhase);
      
      if (ringOpacity > 0.05) {
        final ringPaint = Paint()
          ..color = i.isEven 
              ? goldColor.withValues(alpha: ringOpacity)
              : pinkColor.withValues(alpha: ringOpacity)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
        
        canvas.drawCircle(center, ringRadius, ringPaint);
      }
    }
    
    // Zeichne rotierende Bögen
    for (int i = 0; i < 2; i++) {
      final arcPaint = Paint()
        ..color = color.withValues(alpha: 0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.5
        ..strokeCap = StrokeCap.round;
      
      final startAngle = animation * 2 * pi + (i * pi);
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: maxRadius * 0.7),
        startAngle,
        pi * 0.8,
        false,
        arcPaint,
      );
    }
    
    // Zeichne einen schimmernden Kern
    final coreRadius = maxRadius * 0.3;
    final shimmerPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          color,
          color.withValues(alpha: 0.7),
          color.withValues(alpha: 0.3),
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: coreRadius))
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, coreRadius * (0.8 + 0.2 * sin(animation * 3 * pi)), shimmerPaint);
  }

  @override
  bool shouldRepaint(_AnimationVariant4Painter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Animation Variante 5: Minimalistisch mit eleganten Linien
class AnimationVariant5 extends StatefulWidget {
  final Color color;
  final double size;
  
  const AnimationVariant5({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimationVariant5> createState() => _AnimationVariant5State();
}

class _AnimationVariant5State extends State<AnimationVariant5> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2800),
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AnimationVariant5Painter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class _AnimationVariant5Painter extends CustomPainter {
  final double animation;
  final Color color;

  _AnimationVariant5Painter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    
    // Zeichne drei rotierende Linien mit unterschiedlichen Längen und Geschwindigkeiten
    for (int i = 0; i < 3; i++) {
      final linePaint = Paint()
        ..color = color.withValues(alpha: 0.7 - (i * 0.15))
        ..strokeWidth = 2.0
        ..strokeCap = StrokeCap.round;
      
      final rotationSpeed = 1.0 + (i * 0.5);
      final angle = animation * 2 * pi * rotationSpeed;
      final lineLength = radius * (0.7 - (i * 0.1));
      
      final startPoint = Offset(
        center.dx + lineLength * cos(angle),
        center.dy + lineLength * sin(angle),
      );
      
      final endPoint = Offset(
        center.dx + lineLength * cos(angle + pi),
        center.dy + lineLength * sin(angle + pi),
      );
      
      canvas.drawLine(startPoint, endPoint, linePaint);
    }
    
    // Zeichne einen pulsierenden Punkt in der Mitte
    final dotPaint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      center,
      radius * 0.15 * (0.8 + 0.2 * sin(animation * 3 * pi)),
      dotPaint,
    );
    
    // Zeichne einen subtilen äußeren Ring
    final ringPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawCircle(center, radius, ringPaint);
  }

  @override
  bool shouldRepaint(_AnimationVariant5Painter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Widget für Native Ads mit besserer UI-Integration
class NativeAdWidget extends ConsumerStatefulWidget {
  final bool showOnlyForFreeUsers;
  final double height;
  final EdgeInsets margin;

  const NativeAdWidget({
    super.key,
    this.showOnlyForFreeUsers = true,
    this.height = 300,
    this.margin = const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
  });

  @override
  ConsumerState<NativeAdWidget> createState() => _NativeAdWidgetState();
}

class _NativeAdWidgetState extends ConsumerState<NativeAdWidget> {
  final _log = getLogger('NativeAdWidget');
  NativeAd? _nativeAd;
  bool _isAdLoaded = false;
  bool _shouldShowAd = false;

  @override
  void initState() {
    super.initState();
    _checkAndLoadAd();
  }

  @override
  void dispose() {
    _nativeAd?.dispose();
    super.dispose();
  }

  Future<void> _checkAndLoadAd() async {
    try {
      if (!mounted) return;
      // Prüfe, ob Werbung angezeigt werden soll
      if (widget.showOnlyForFreeUsers) {
        // Direkter Check über SubscriptionManagementService
        final subscriptionService = ref.read(
          subscriptionManagementServiceProvider,
        );
        final remainingApps =
            await subscriptionService.getRemainingApplications();
        if (!mounted) return;
        final planType = remainingApps['plan_type'] as String?;

        // Nur für kostenlose User (free plan)
        if (planType != 'free') {
          _log.i('Premium-User (Plan: $planType) - keine Native Ad anzeigen');
          return;
        }
        _log.i('Kostenloser User (Plan: $planType) - Native Ad wird geladen');
      }

      // Lade Native Ad direkt (jedes Widget hat seine eigene Ad)
      final adService = ref.read(adServiceProvider);
      if (!mounted) return;
      if (!adService.areAdsEnabled) {
        _log.i("Werbung ist deaktiviert - keine Native Ad geladen");
        return;
      }

      try {
        _nativeAd = NativeAd(
          adUnitId:
              Platform.isAndroid
                  ? 'ca-app-pub-5967659832492607/**********' // Echte Native-Ad ID (gleiche wie Banner-Ad Block)
                  : 'ca-app-pub-3940256099942544/**********', // Test ID für iOS
          request: const AdRequest(),
          listener: NativeAdListener(
            onAdLoaded: (ad) {
              _log.i('Native Ad erfolgreich geladen');
              if (mounted) {
                setState(() {
                  _isAdLoaded = true;
                  _shouldShowAd = true;
                });
              }
            },
            onAdFailedToLoad: (ad, error) {
              _log.e('Native Ad konnte nicht geladen werden: $error');
              ad.dispose();
              if (mounted) {
                setState(() {
                  _nativeAd = null;
                  _isAdLoaded = false;
                  _shouldShowAd = false;
                });
              }
            },
            onAdOpened: (ad) {
              _log.i('Native Ad geöffnet');
            },
            onAdClosed: (ad) {
              _log.i('Native Ad geschlossen');
            },
          ),
          nativeTemplateStyle: NativeTemplateStyle(
            templateType: TemplateType.medium,
            mainBackgroundColor: Colors.white,
            cornerRadius: 10.0,
            callToActionTextStyle: NativeTemplateTextStyle(
              textColor: Colors.white,
              backgroundColor: Colors.blue,
              style: NativeTemplateFontStyle.monospace,
              size: 16.0,
            ),
            primaryTextStyle: NativeTemplateTextStyle(
              textColor: Colors.black,
              backgroundColor: Colors.white,
              style: NativeTemplateFontStyle.bold,
              size: 16.0,
            ),
            secondaryTextStyle: NativeTemplateTextStyle(
              textColor: Colors.grey,
              backgroundColor: Colors.white,
              style: NativeTemplateFontStyle.normal,
              size: 14.0,
            ),
            tertiaryTextStyle: NativeTemplateTextStyle(
              textColor: Colors.grey,
              backgroundColor: Colors.white,
              style: NativeTemplateFontStyle.normal,
              size: 12.0,
            ),
          ),
        );

        await _nativeAd!.load();
        _log.i('Native Ad Ladevorgang gestartet');
      } catch (e) {
        _log.e('Fehler beim Laden der Native Ad: $e');
        _nativeAd = null;
        if (mounted) {
          setState(() {
            _isAdLoaded = false;
            _shouldShowAd = false;
          });
        }
      }
    } catch (e) {
      _log.e('Fehler beim Laden der Native Ad: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    _log.i(
      '🎯 Native Ad Widget Build: shouldShow=$_shouldShowAd, isLoaded=$_isAdLoaded, ad=${_nativeAd != null}',
    );

    // Zeige einen Platzhalter während des Ladens
    if (!_shouldShowAd) {
      _log.i('❌ Native Ad wird nicht angezeigt: shouldShow=$_shouldShowAd');
      return const SizedBox.shrink();
    }

    if (!_isAdLoaded || _nativeAd == null) {
      _log.i(
        '⏳ Native Ad lädt noch: isLoaded=$_isAdLoaded, ad=${_nativeAd != null}',
      );
      return Container(
        margin: widget.margin,
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          color: Colors.grey.shade100,
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    _log.i('✅ Native Ad wird angezeigt!');

    return Container(
      margin: widget.margin,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AdWidget(ad: _nativeAd!),
      ),
    );
  }
}

/// Native Ad Widget für die Startseite
class HomeNativeAdWidget extends ConsumerWidget {
  const HomeNativeAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    return const NativeAdWidget(
      height: 250,
      margin: EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
    );
  }
}

/// Native Ad Widget für Job-Listen
class JobListNativeAdWidget extends ConsumerWidget {
  const JobListNativeAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    return const NativeAdWidget(
      height: 200,
      margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
    );
  }
}

/// Native Ad Widget für das Profil
class ProfileNativeAdWidget extends ConsumerWidget {
  const ProfileNativeAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    return const NativeAdWidget(
      height: 280,
      margin: EdgeInsets.symmetric(vertical: 16.0, horizontal: 0.0),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/infrastructure/services/network_status_service.dart';

/// Widget zur Anzeige des Netzwerk-Status
/// Zeigt eine Benachrichtigung an, wenn das Netzwerk nicht verfügbar ist
class NetworkStatusWidget extends ConsumerWidget {
  final Widget child;
  final bool showOnlineStatus;
  final Duration hideDelay;

  const NetworkStatusWidget({
    super.key,
    required this.child,
    this.showOnlineStatus = false,
    this.hideDelay = const Duration(seconds: 3),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkStatusProvider);

    return Stack(
      children: [
        child,
        networkStatus.when(
          data: (status) => _buildStatusBanner(context, status),
          loading: () => const SizedBox.shrink(),
          error: (error, stack) => _buildErrorBanner(context),
        ),
      ],
    );
  }

  Widget _buildStatusBanner(BuildContext context, NetworkStatus status) {
    switch (status) {
      case NetworkStatus.disconnected:
        return _buildOfflineBanner(context);
      case NetworkStatus.connected:
        return showOnlineStatus ? _buildOnlineBanner(context) : const SizedBox.shrink();
      case NetworkStatus.unknown:
        return _buildUnknownBanner(context);
    }
  }

  Widget _buildOfflineBanner(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.red.shade600,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            bottom: false,
            child: Row(
              children: [
                const Icon(
                  Icons.wifi_off,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Keine Internetverbindung',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    return TextButton(
                      onPressed: () async {
                        final service = ref.read(networkStatusServiceProvider);
                        await service.checkConnectivity();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      ),
                      child: const Text(
                        'Erneut versuchen',
                        style: TextStyle(fontSize: 12),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOnlineBanner(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Material(
        elevation: 4,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.green.shade600,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            bottom: false,
            child: Row(
              children: [
                const Icon(
                  Icons.wifi,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Internetverbindung wiederhergestellt',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUnknownBanner(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade600,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SafeArea(
            bottom: false,
            child: Row(
              children: [
                const Icon(
                  Icons.signal_wifi_statusbar_null,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Netzwerkstatus unbekannt',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    return TextButton(
                      onPressed: () async {
                        final service = ref.read(networkStatusServiceProvider);
                        await service.checkConnectivity();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      ),
                      child: const Text(
                        'Prüfen',
                        style: TextStyle(fontSize: 12),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorBanner(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade600,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const SafeArea(
            bottom: false,
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Fehler beim Überwachen der Netzwerkverbindung',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Kompaktes Netzwerk-Status-Icon für die App-Bar
class NetworkStatusIcon extends ConsumerWidget {
  final double size;
  final Color? color;

  const NetworkStatusIcon({
    super.key,
    this.size = 24,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkStatusProvider);

    return networkStatus.when(
      data: (status) => _buildStatusIcon(context, status),
      loading: () => Icon(
        Icons.signal_wifi_statusbar_null,
        size: size,
        color: color ?? Colors.grey,
      ),
      error: (error, stack) => Icon(
        Icons.error_outline,
        size: size,
        color: color ?? Colors.red,
      ),
    );
  }

  Widget _buildStatusIcon(BuildContext context, NetworkStatus status) {
    switch (status) {
      case NetworkStatus.connected:
        return Icon(
          Icons.wifi,
          size: size,
          color: color ?? Colors.green,
        );
      case NetworkStatus.disconnected:
        return Icon(
          Icons.wifi_off,
          size: size,
          color: color ?? Colors.red,
        );
      case NetworkStatus.unknown:
        return Icon(
          Icons.signal_wifi_statusbar_null,
          size: size,
          color: color ?? Colors.orange,
        );
    }
  }
}

/// Netzwerk-Status-Indikator für Debug-Zwecke
class NetworkStatusDebugWidget extends ConsumerWidget {
  const NetworkStatusDebugWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkStatusProvider);

    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: networkStatus.when(
        data: (status) => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Netzwerk: ${status.displayName}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            Consumer(
              builder: (context, ref, child) {
                final service = ref.read(networkStatusServiceProvider);
                final stats = service.getNetworkStats();
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Letzte Prüfung: ${stats['last_successful_check'] ?? 'Nie'}',
                      style: const TextStyle(color: Colors.white70, fontSize: 10),
                    ),
                    Text(
                      'Fehler: ${stats['consecutive_failures']}',
                      style: const TextStyle(color: Colors.white70, fontSize: 10),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        loading: () => const Text(
          'Netzwerk: Lädt...',
          style: TextStyle(color: Colors.white, fontSize: 12),
        ),
        error: (error, stack) => Text(
          'Netzwerk: Fehler',
          style: TextStyle(color: Colors.red.shade300, fontSize: 12),
        ),
      ),
    );
  }
}

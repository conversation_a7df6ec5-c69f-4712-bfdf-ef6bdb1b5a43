import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Widget für die Anzeige von Banner-Ads im neuen Werbungssystem
class BannerAdWidget extends ConsumerStatefulWidget {
  final double? height;
  final EdgeInsets? margin;
  final bool showOnlyForFreeUsers;

  const BannerAdWidget({
    super.key,
    this.height,
    this.margin,
    this.showOnlyForFreeUsers = true,
  });

  @override
  ConsumerState<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends ConsumerState<BannerAdWidget> {
  final _log = getLogger('BannerAdWidget');
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _shouldShowAd = false;

  @override
  void initState() {
    super.initState();
    _checkAndLoadAd();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  Future<void> _checkAndLoadAd() async {
    try {
      if (!mounted) return;
      // Prüfe, ob Werbung angezeigt werden soll
      if (widget.showOnlyForFreeUsers) {
        // Direkter Check über SubscriptionManagementService
        final subscriptionService = ref.read(
          subscriptionManagementServiceProvider,
        );
        final remainingApps =
            await subscriptionService.getRemainingApplications();
        if (!mounted) return;
        final planType = remainingApps['plan_type'] as String?;

        // Nur für kostenlose User (free plan)
        if (planType != 'free') {
          _log.i('Premium-User (Plan: $planType) - keine Banner-Ad anzeigen');
          return;
        }
        _log.i('Kostenloser User (Plan: $planType) - Banner-Ad wird geladen');
      }

      // Lade Banner-Ad über AdService
      final adService = ref.read(adServiceProvider);
      if (!mounted) return;
      _bannerAd = await adService.loadBannerAd();
      if (!mounted) return;

      if (_bannerAd != null) {
        if (!mounted) return;
        setState(() {
          _isAdLoaded = true;
          _shouldShowAd = true;
        });
        _log.i('Banner-Ad erfolgreich geladen und bereit zur Anzeige');
      } else {
        _log.w('Banner-Ad konnte nicht geladen werden');
      }
    } catch (e) {
      _log.e('Fehler beim Laden der Banner-Ad: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Zeige nichts an, wenn keine Ad geladen oder nicht angezeigt werden soll
    if (!_shouldShowAd || !_isAdLoaded || _bannerAd == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8.0),
      height: widget.height ?? 60.0,
      child: AdWidget(ad: _bannerAd!),
    );
  }
}

/// Spezialisiertes Banner-Ad-Widget für die Startseite
class HomeBannerAdWidget extends ConsumerWidget {
  const HomeBannerAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    return const BannerAdWidget(
      height: 60.0,
      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      showOnlyForFreeUsers: true,
    );
  }
}

/// Spezialisiertes Banner-Ad-Widget für Job-Listen
class JobListBannerAdWidget extends ConsumerWidget {
  const JobListBannerAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    // Für Free-User: Ad mit Padding (da es in AnimatedJobList ohne Padding verwendet wird)
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 8.0),
      child: BannerAdWidget(
        height: 50.0,
        margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        showOnlyForFreeUsers: true,
      ),
    );
  }
}

/// Spezialisiertes Banner-Ad-Widget für das Profil
class ProfileBannerAdWidget extends ConsumerWidget {
  const ProfileBannerAdWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe Premium-Status direkt hier
    final isPremium = ref.watch(isPremiumProvider);

    // Für Premium-User: Komplett unsichtbar ohne jegliches Spacing
    if (isPremium) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: BannerAdWidget(height: 60.0, showOnlyForFreeUsers: true),
      ),
    );
  }
}

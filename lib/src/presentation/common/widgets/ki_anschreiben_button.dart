import 'package:flutter/material.dart';

class KIAnschreibenButton extends StatelessWidget {
  final bool isGenerating;
  final String text;
  final String generatingText;
  final VoidCallback? onPressed;

  const KIAnschreibenButton({
    super.key,
    required this.isGenerating,
    required this.text,
    required this.generatingText,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;
    
    return ElevatedButton.icon(
      icon: isGenerating
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(onPrimaryColor),
              ),
            )
          : Icon(Icons.auto_awesome, size: 20, color: onPrimaryColor),
      label: Text(
        isGenerating ? generatingText : text,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: onPrimaryColor,
        ),
      ),
      onPressed: isGenerating ? null : onPressed, // Explizit null setzen während Animation
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: onPrimaryColor,
        disabledBackgroundColor: primaryColor, // WICHTIG: Behält Farbe während Animation
        disabledForegroundColor: onPrimaryColor, // WICHTIG: Behält Textfarbe während Animation
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: isGenerating ? 0 : 2, // Reduzierte Elevation während Animation
      ),
    );
  }
}

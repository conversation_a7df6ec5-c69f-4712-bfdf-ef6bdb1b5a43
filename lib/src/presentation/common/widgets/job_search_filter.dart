import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/entities/job_entity.dart';

/// Eine erweiterte Suchfilter-Komponente für JobEntity-Listen
/// Ermöglicht die Filterung nach Jobtitel, Firmenname und Standort
class JobSearchFilter extends HookWidget {
  final List<JobEntity> originalJobs;
  final Function(List<JobEntity>) onFilteredJobsChanged;
  final String? hintText;
  final bool showFilterChips;

  const JobSearchFilter({
    super.key,
    required this.originalJobs,
    required this.onFilteredJobsChanged,
    this.hintText,
    this.showFilterChips = true,
  });

  @override
  Widget build(BuildContext context) {
    final searchController = useTextEditingController();
    final selectedFilters = useState<Set<FilterType>>({});
    final isSearchActive = useState(false);
    final filteredJobs = useState<List<JobEntity>>(originalJobs);

    // Helper-Methode für die Suche in allen Feldern
    bool matchesAnyField(JobEntity job, String query) {
      return job.title.toLowerCase().contains(query) ||
             job.companyName.toLowerCase().contains(query) ||
             job.location.toLowerCase().contains(query);
    }

    // Filterlogik
    void applyFilters() {
      final query = searchController.text.toLowerCase().trim();
      
      if (query.isEmpty) {
        filteredJobs.value = originalJobs;
        onFilteredJobsChanged(originalJobs);
        isSearchActive.value = false;
        return;
      }

      isSearchActive.value = true;
      final filtered = originalJobs.where((job) {
        // Wenn keine spezifischen Filter ausgewählt sind, suche in allen Feldern
        if (selectedFilters.value.isEmpty) {
          return matchesAnyField(job, query);
        }

        // Suche nur in den ausgewählten Feldern
        return selectedFilters.value.any((filter) {
          switch (filter) {
            case FilterType.title:
              return job.title.toLowerCase().contains(query);
            case FilterType.company:
              return job.companyName.toLowerCase().contains(query);
            case FilterType.location:
              return job.location.toLowerCase().contains(query);
          }
        });
      }).toList();

      filteredJobs.value = filtered;
      onFilteredJobsChanged(filtered);
    }

    // Listener für Textänderungen
    useEffect(() {
      void listener() => applyFilters();
      searchController.addListener(listener);
      return () => searchController.removeListener(listener);
    }, [searchController]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Suchfeld
        Container(
          margin: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingMedium,
            vertical: AppTheme.spacingSmall,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(128),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            border: isSearchActive.value
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary.withAlpha(128),
                    width: 1.5,
                  )
                : null,
          ),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: hintText ?? 'Suche nach Job, Firma oder Ort...',
              hintStyle: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(180),
                fontSize: 14,
              ),
              prefixIcon: Icon(
                Icons.search,
                size: 20,
                color: isSearchActive.value
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              suffixIcon: searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(
                        Icons.clear,
                        size: 20,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      onPressed: () {
                        searchController.clear();
                        selectedFilters.value = {};
                        applyFilters();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.transparent,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: AppTheme.spacingMedium,
              ),
              isDense: true,
            ),
            textInputAction: TextInputAction.search,
            style: const TextStyle(fontSize: 14),
          ),
        ),

        // Filter-Chips (optional)
        if (showFilterChips && searchController.text.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
            ),
            child: Wrap(
              spacing: 8,
              children: FilterType.values.map((filter) {
                final isSelected = selectedFilters.value.contains(filter);
                return FilterChip(
                  label: Text(
                    filter.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    final newFilters = Set<FilterType>.from(selectedFilters.value);
                    if (selected) {
                      newFilters.add(filter);
                    } else {
                      newFilters.remove(filter);
                    }
                    selectedFilters.value = newFilters;
                    applyFilters();
                  },
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  selectedColor: Theme.of(context).colorScheme.primary,
                  checkmarkColor: Theme.of(context).colorScheme.onPrimary,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withAlpha(100),
                    width: 1,
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                );
              }).toList(),
            ),
          ),

        // Suchergebnisse-Anzeige
        if (isSearchActive.value)
          Container(
            margin: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
              vertical: AppTheme.spacingSmall,
            ),
            child: Text(
              '${filteredJobs.value.length} von ${originalJobs.length} Jobs gefunden',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}

/// Enum für die verschiedenen Filtertypen
enum FilterType {
  title,
  company,
  location;

  String get displayName {
    switch (this) {
      case FilterType.title:
        return 'Jobtitel';
      case FilterType.company:
        return 'Firma';
      case FilterType.location:
        return 'Ort';
    }
  }
}

/// Erweiterte Version mit Callback für gefilterte Anzahl
class JobSearchFilterWithCount extends JobSearchFilter {
  final Function(List<JobEntity>, int)? onFilteredJobsChangedWithCount;

  const JobSearchFilterWithCount({
    super.key,
    required super.originalJobs,
    required super.onFilteredJobsChanged,
    this.onFilteredJobsChangedWithCount,
    super.hintText,
    super.showFilterChips,
  });

  void _notifyWithCount(List<JobEntity> filteredJobs) {
    onFilteredJobsChanged(filteredJobs);
    onFilteredJobsChangedWithCount?.call(filteredJobs, filteredJobs.length);
  }
}
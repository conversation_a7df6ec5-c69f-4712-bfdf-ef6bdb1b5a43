import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../application/providers/services_providers.dart';
import '../../../application/providers/anti_manipulation_provider.dart';

/// Widget für Anti-Manipulation-Warnung bei Zero-Credit-Accounts
class AntiManipulationWarningWidget extends ConsumerWidget {
  const AntiManipulationWarningWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final remainingApplications = ref.watch(remainingApplicationsProvider);
    final antiState = ref.watch(antiManipulationProvider);

    // SOFORTIGE WARNUNG, falls Zero‑Credit aktiv ist, unabhängig vom Zähler‑Ladestatus
    if (antiState.zeroCredit) {
      final originalEmail = antiState.originalEmail;
      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Anti-Manipulation erkannt: Guthaben wurde auf 0 gesetzt für diesen Account.',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange.shade800,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (originalEmail != null && originalEmail.isNotEmpty)
              Text(
                'Original-Account: $originalEmail',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            const SizedBox(height: 12),
            _buildSolutionOption(
              icon: Icons.person_outline,
              title: 'Original-Account verwenden',
              description: 'Melden Sie sich mit Ihrem ersten Account an',
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            _buildSolutionOption(
              icon: Icons.star,
              title: 'Premium upgraden',
              description: 'Unbegrenzte Bewerbungen ohne Einschränkungen',
              color: Colors.purple,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Navigation zur Premium-Seite (existierende GoRouter-Route: '/premium')
                      context.push('/premium');
                    },
                    icon: const Icon(Icons.star),
                    label: const Text('Premium upgraden'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.purple,
                      side: BorderSide(color: Colors.purple.shade300),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextButton.icon(
                    onPressed: () {
                      // Zu Einstellungen wechseln (existierende verschachtelte Route: '/settings' unter '/')
                      context.push('/settings');
                    },
                    icon: const Icon(Icons.settings),
                    label: const Text('Account wechseln'),
                    style: TextButton.styleFrom(foregroundColor: Colors.blue),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return remainingApplications.when(
      data: (data) {
        // Prüfe, ob User Zero-Credit hat (Anti-Manipulation erkannt)
        final remaining = data['remaining'] as int? ?? 0;
        final planType = data['plan_type'] as String? ?? 'free';

        // Zeige Warnung wenn Server Zero‑Credit bestätigt hat (Backup-Pfad)
        if (planType == 'free' && remaining == 0 && antiState.zeroCredit) {
          final originalEmail = antiState.originalEmail;
          return Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Mehrere Accounts erkannt',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (originalEmail != null && originalEmail.isNotEmpty)
                  Text(
                    'Original-Account: $originalEmail',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                const SizedBox(height: 12),
                Text(
                  'Wir haben erkannt, dass Sie mehrere Accounts auf diesem Gerät verwenden. '
                  'Um Missbrauch zu verhindern, wurde Ihr kostenloses Bewerbungsguthaben auf 0 gesetzt.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Lösungsoptionen:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                _buildSolutionOption(
                  icon: Icons.person_outline,
                  title: 'Original-Account verwenden',
                  description: 'Melden Sie sich mit Ihrem ersten Account an',
                  color: Colors.blue,
                ),
                const SizedBox(height: 8),
                _buildSolutionOption(
                  icon: Icons.star_outline,
                  title: 'Premium upgraden',
                  description: 'Unbegrenzte Bewerbungen mit Premium',
                  color: Colors.purple,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // Navigation zur Premium-Seite (existierende GoRouter-Route: '/premium')
                          context.push('/premium');
                        },
                        icon: const Icon(Icons.star),
                        label: const Text('Premium upgraden'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.purple,
                          side: BorderSide(color: Colors.purple.shade300),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextButton.icon(
                        onPressed: () {
                          // Zu Einstellungen wechseln (existierende verschachtelte Route: '/settings' unter '/')
                          context.push('/settings');
                        },
                        icon: const Icon(Icons.settings),
                        label: const Text('Account wechseln'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        // Keine Warnung nötig
        return const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildSolutionOption({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

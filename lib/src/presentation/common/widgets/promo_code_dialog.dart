import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/infrastructure/services/subscription_service.dart';

/// Dialog für die Eingabe von Promo Codes
class PromoCodeDialog extends ConsumerStatefulWidget {
  const PromoCodeDialog({super.key});

  @override
  ConsumerState<PromoCodeDialog> createState() => _PromoCodeDialogState();
}

class _PromoCodeDialogState extends ConsumerState<PromoCodeDialog> {
  final _promoCodeController = TextEditingController();
  final _log = getLogger('PromoCodeDialog');
  bool _isLoading = false;

  @override
  void dispose() {
    _promoCodeController.dispose();
    super.dispose();
  }

  Future<void> _redeemPromoCode() async {
    final promoCode = _promoCodeController.text.trim();
    
    if (promoCode.isEmpty) {
      _showErrorSnackBar('Bitte geben Sie einen Promo Code ein');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final subscriptionService = ref.read(subscriptionServiceProvider);
      final success = await subscriptionService.redeemPromoCode(promoCode);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(true);
          _showSuccessSnackBar('Promo Code erfolgreich eingelöst! 🎉');
        }
      } else {
        _showErrorSnackBar('Ungültiger oder bereits verwendeter Promo Code');
      }
    } catch (e) {
      _log.e('Fehler beim Einlösen des Promo Codes: $e');
      _showErrorSnackBar('Fehler beim Einlösen des Promo Codes');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFF1A1A1A),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: const Row(
        children: [
          Icon(
            Icons.card_giftcard,
            color: Colors.orange,
            size: 24,
          ),
          SizedBox(width: 8),
          Text(
            'Promo Code einlösen',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Geben Sie Ihren Promo Code ein, um kostenlosen Premium-Zugang zu erhalten:',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _promoCodeController,
            enabled: !_isLoading,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'z.B. WELCOME2025',
              hintStyle: const TextStyle(color: Colors.white38),
              filled: true,
              fillColor: const Color(0xFF2A2A2A),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.orange, width: 2),
              ),
              prefixIcon: const Icon(
                Icons.confirmation_number,
                color: Colors.orange,
              ),
            ),
            textCapitalization: TextCapitalization.characters,
            onSubmitted: _isLoading ? null : (_) => _redeemPromoCode(),
          ),
          const SizedBox(height: 12),
          const Text(
            '• Nur für neue Abonnenten\n• Gewährt 7 Tage kostenlosen Zugang\n• Kann nur einmal verwendet werden',
            style: TextStyle(
              color: Colors.white54,
              fontSize: 12,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text(
            'Abbrechen',
            style: TextStyle(color: Colors.white54),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _redeemPromoCode,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Einlösen'),
        ),
      ],
    );
  }
}

/// Zeigt den Promo Code Dialog an
Future<bool?> showPromoCodeDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const PromoCodeDialog(),
  );
}

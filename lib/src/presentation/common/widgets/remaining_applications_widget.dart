import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/application/providers/anti_manipulation_provider.dart';

/// Widget zur Anzeige der verbleibenden Bewerbungen
class RemainingApplicationsWidget extends ConsumerWidget {
  final bool showLabel;
  final bool showIcon;
  final bool compact;
  final bool showDetailed;
  // Erzwingt im "keine Bewerbungen"-Fall einen Upgrade-CTA statt Countdown
  final bool forceUpgradeCTA;
  final VoidCallback? onUpgradePressed;

  static final _log = Logger();

  const RemainingApplicationsWidget({
    super.key,
    this.showLabel = true,
    this.showIcon = true,
    this.compact = false,
    this.showDetailed = false,
    this.forceUpgradeCTA = false,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _log.i('🎨 RemainingApplicationsWidget build() aufgerufen');

    final isPremium = ref.watch(isPremiumProvider);
    final userProfileState = ref.watch(userProfileProvider);

    return userProfileState.when(
      data: (userProfile) {
        final planType = userProfile.premiumPlanType?.toLowerCase() ?? 'basic';
        _log.i('👤 User-Plan: $planType, isPremium: $isPremium');

        // Wenn der Benutzer Premium oder Unlimited hat, zeige KEINE Bewerbungszähler an
        // Stattdessen zeige das Unlimited-Widget oder nichts
        if (planType == 'premium' || planType == 'unlimited') {
          // Bei Premium oder Unlimited zeigen wir keinen Bewerbungszähler an
          // Nur in bestimmten Kontexten (z.B. auf der Premium-Verwaltungsseite)
          // zeigen wir das Unlimited-Widget an
          if (ModalRoute.of(context)?.settings.name == '/premium-management') {
            return _buildUnlimitedWidget(context);
          } else {
            // In allen anderen Kontexten (z.B. in der AppBar) zeigen wir nichts an
            return const SizedBox.shrink();
          }
        }

        // KRITISCH: Verwende den StateNotifier für automatische Updates
        // Dieser Provider wird bei Plan-Änderungen automatisch invalidiert
        _log.i('📡 Watching remainingApplicationsAutoRefreshProvider...');
        final remainingApplicationsState = ref.watch(
          remainingApplicationsAutoRefreshProvider,
        );

        // WICHTIG: Bei Plan-Änderungen manuell refreshen
        ref.listen(userProfileProvider, (previous, next) {
          _log.i('👂 UserProfile-Listener ausgelöst');
          next.whenData((profile) {
            // Wenn sich der Plan-Typ geändert hat, refresh die Bewerbungsdaten
            final previousPlan =
                previous?.value?.premiumPlanType?.toLowerCase();
            final currentPlan = profile.premiumPlanType?.toLowerCase();

            _log.i('🔄 Plan-Änderung: $previousPlan → $currentPlan');

            if (previousPlan != currentPlan) {
              _log.i('🚀 Manueller Refresh des StateNotifiers ausgelöst');
              // Manueller Refresh des StateNotifiers
              ref
                  .read(remainingApplicationsNotifierProvider.notifier)
                  .refresh();
            }
          });
        });

        // Für Basic und Pro Pläne zeige den Zähler an
        return remainingApplicationsState.when(
          data: (data) {
            _log.i('📊 Provider-Daten erhalten: $data');
            final remaining = data['remaining'] as int?;
            final total = data['total'] as int?;
            final unlimited = data['unlimited'] as bool? ?? false;
            _log.i(
              '🔢 Parsed: remaining=$remaining, total=$total, unlimited=$unlimited',
            );

            if (unlimited) {
              // Auch hier: Bei unlimited zeigen wir keinen Bewerbungszähler an
              if (ModalRoute.of(context)?.settings.name ==
                  '/premium-management') {
                return _buildUnlimitedWidget(context);
              } else {
                return const SizedBox.shrink();
              }
            }

            if (remaining == null || total == null) {
              // Defensiver Fallback: zeige 0/0 statt generischem Fehler
              return _buildCounterWidget(context, 0, 0);
            }

            // Wenn der Benutzer keine Bewerbungen mehr hat und nicht Premium ist,
            // zeige einen speziellen Widget an
            if (remaining <= 0 && !isPremium) {
              // Sonderfall: Anti‑Manipulation Zero‑Credit → kein Countdown/Reset‑Datum anzeigen
              final anti = ref.watch(antiManipulationProvider);
              if (anti.zeroCredit) {
                return _buildZeroCreditWidget(context, anti.originalEmail);
              }
              // Hinweis: Standard-Warnung separat über AntiManipulationWarningWidget
              return _buildNoApplicationsLeftWidget(context, ref);
            }

            // Keine Client-Seitige Korrektur: zeige exakt die Server-Werte
            return _buildCounterWidget(context, remaining, total);
          },
          loading: () => _buildLoadingWidget(context),
          error: (error, stackTrace) => _buildErrorWidget(context),
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (error, stackTrace) => _buildErrorWidget(context),
    );
  }

  Widget _buildCounterWidget(BuildContext context, int remaining, int total) {
    // Keine Standardisierung mehr: UI zeigt exakt die Server-Werte
    // Wenn total/remaining 0 sind, wird 0/0 angezeigt
    // Wenn unlimited, wird an anderer Stelle abgefangen

    final percentage = total > 0 ? (remaining / total) : 0.0;
    final color = _getColorForPercentage(context, percentage);

    return compact
        ? _buildCompactCounter(context, remaining, total, color)
        : _buildFullCounter(context, remaining, total, color);
  }

  Widget _buildCompactCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    // Für bessere Lesbarkeit in der AppBar immer weiße Farbe verwenden
    final bool isInAppBar =
        ModalRoute.of(context)?.settings.name != '/premium-management';
    final textColor = isInAppBar ? Colors.white : color;
    final iconColor = isInAppBar ? Colors.white : color;

    // Prüfe ob wir in der JobDetailScreen sind
    final bool isJobDetailScreen =
        ModalRoute.of(context)?.settings.name?.contains('/job-detail') == true;

    // Kurzer Text mit Icon statt "Bewerbungen"
    String displayText;
    if (isJobDetailScreen && remaining == 0) {
      displayText = '0';
    } else if (isInAppBar) {
      displayText = '$remaining'; // Nur Zahl, Icon zeigt was es ist
    } else {
      displayText = '$remaining/$total';
    }

    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 70,
        ), // Angepasste Breite für Icon + Zahl
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ), // Größeres Padding
        decoration:
            isInAppBar
                ? BoxDecoration(
                  color: Colors.white.withAlpha(51),
                  borderRadius: BorderRadius.circular(8), // Größerer Radius
                )
                : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon)
              Icon(
                Icons.work_outline,
                size: 16,
                color: iconColor,
              ), // Größeres Icon
            if (showIcon) const SizedBox(width: 4), // Größerer Abstand
            Flexible(
              child: Text(
                displayText,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14, // Größere, besser lesbare Schrift
                ),
                overflow: TextOverflow.ellipsis, // Overflow-Schutz
                maxLines: 1,
                softWrap: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    return showDetailed
        ? _buildDetailedCounter(context, remaining, total, color)
        : _buildSimpleCounter(context, remaining, total, color);
  }

  Widget _buildSimpleCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showLabel)
            Text(
              'Verbleibende Bewerbungen',
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          if (showLabel) const SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showIcon) Icon(Icons.work_outline, size: 20, color: color),
              if (showIcon) const SizedBox(width: 8),
              Text(
                '$remaining/$total',
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                softWrap: false,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          SizedBox(
            width: 100,
            height: 4,
            child: LinearProgressIndicator(
              value: total > 0 ? (remaining / total) : 0.0,
              backgroundColor: Colors.grey.withAlpha(77),
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    final percentage = total > 0 ? (remaining / total) * 100 : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.work_outline, size: 24, color: color),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$remaining von $total Bewerbungen verfügbar',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${percentage.toStringAsFixed(0)}% verbleibend',
                  style: TextStyle(fontSize: 14, color: color.withAlpha(204)),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: total > 0 ? (remaining / total) : 0.0,
            backgroundColor: Colors.grey.withAlpha(77),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ),
        if (remaining < 5) ...[
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed:
                onUpgradePressed ??
                () {
                  // Navigiere zur Premium-Seite
                  Navigator.of(context).pushNamed('/premium-management');
                },
            icon: const Icon(Icons.upgrade),
            label: const Text('Jetzt upgraden'),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUnlimitedWidget(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon)
            Icon(
              Icons.all_inclusive,
              size: compact ? 16 : 20,
              color: Theme.of(context).colorScheme.primary,
            ),
          if (showIcon) SizedBox(width: compact ? 4 : 8),
          Text(
            'Unbegrenzt',
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            softWrap: false,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
              fontSize: compact ? 14 : 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      width: compact ? 16 : 20,
      height: compact ? 16 : 20,
      child: CircularProgressIndicator(
        strokeWidth: compact ? 2 : 3,
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: compact ? 16 : 20,
            color: Theme.of(context).colorScheme.error,
          ),
          SizedBox(width: compact ? 4 : 8),
          Text(
            'Fehler',
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            softWrap: false,
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
              fontSize: compact ? 14 : 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoApplicationsLeftWidget(BuildContext context, WidgetRef ref) {
    return showDetailed
        ? _buildDetailedNoApplicationsWidget(context, ref)
        : _buildSimpleNoApplicationsWidget(context, ref);
  }

  Widget _buildSimpleNoApplicationsWidget(BuildContext context, WidgetRef ref) {
    // Hole das nächste Reset-Datum für kostenlose Bewerbungen
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        // Wenn kein Datum verfügbar ist oder es ein Premium-Benutzer ist, zeige den Standard-Widget an
        if (nextResetDate == null) {
          return _buildStandardNoApplicationsWidget(context);
        }

        // Berechne die verbleibende Zeit bis zum nächsten Reset
        final now = DateTime.now();
        final difference = nextResetDate.difference(now);

        // Wenn das Datum in der Vergangenheit liegt, zeige den Standard-Widget an
        if (difference.isNegative) {
          return _buildStandardNoApplicationsWidget(context);
        }

        // Formatiere die verbleibende Zeit
        final days = difference.inDays;
        final hours = difference.inHours % 24;

        String timeText;
        if (days > 0) {
          timeText = '$days Tag${days > 1 ? 'e' : ''} $hours Std.';
        } else {
          timeText = '$hours Std. ${difference.inMinutes % 60} Min.';
        }

        // Im JobDetail-Screen oder wenn explizit erzwungen: statt Countdown Upgrade-CTA
        final bool isJobDetail =
            ModalRoute.of(context)?.settings.name?.contains('/job-detail') ==
            true;
        if (forceUpgradeCTA || isJobDetail) {
          return InkWell(
            onTap: onUpgradePressed ?? () => context.go('/premium-management'),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.upgrade,
                  size: compact ? 16 : 20,
                  color: Colors.blue,
                ),
                SizedBox(width: compact ? 4 : 8),
                Text(
                  'Upgraden',
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                    fontSize: compact ? 14 : 16,
                  ),
                ),
              ],
            ),
          );
        }

        return InkWell(
          onTap:
              onUpgradePressed ??
              () {
                // Navigiere zur Premium-Seite
                context.go('/premium-management');
              },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.timer, size: compact ? 16 : 20, color: Colors.blue),
              SizedBox(width: compact ? 4 : 8),
              Text(
                'Neue Bewerbungen in $timeText',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                  fontSize: compact ? 14 : 16,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (_, __) => _buildStandardNoApplicationsWidget(context),
    );
  }

  Widget _buildStandardNoApplicationsWidget(BuildContext context) {
    return InkWell(
      onTap:
          onUpgradePressed ??
          () {
            // Navigiere zur Premium-Seite
            Navigator.of(context).pushNamed('/premium-management');
          },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: compact ? 16 : 20,
            color: Colors.orange,
          ),
          SizedBox(width: compact ? 4 : 8),
          Text(
            'Keine Bewerbungen mehr',
            style: TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
              fontSize: compact ? 14 : 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedNoApplicationsWidget(
    BuildContext context,
    WidgetRef ref,
  ) {
    // Hole das nächste Reset-Datum für kostenlose Bewerbungen
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        // Wenn kein Datum verfügbar ist oder es ein Premium-Benutzer ist, zeige den Standard-Widget an
        if (nextResetDate == null) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        // Berechne die verbleibende Zeit bis zum nächsten Reset
        final now = DateTime.now();
        final difference = nextResetDate.difference(now);

        // Wenn das Datum in der Vergangenheit liegt, zeige den Standard-Widget an
        if (difference.isNegative) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        // Formatiere die verbleibende Zeit und das Datum
        final days = difference.inDays;
        final hours = difference.inHours % 24;
        final dateFormat = DateFormat('dd.MM.yyyy');

        String timeText;
        if (days > 0) {
          timeText = '$days Tag${days > 1 ? 'e' : ''} und $hours Stunden';
        } else {
          timeText = '$hours Stunden und ${difference.inMinutes % 60} Minuten';
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.timer, size: 24, color: Colors.blue),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Keine Bewerbungen mehr verfügbar',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Neue kostenlose Bewerbungen in $timeText',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Nächste Freischaltung: ${dateFormat.format(nextResetDate)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        onUpgradePressed ??
                        () {
                          // Navigiere zur Premium-Seite
                          Navigator.of(
                            context,
                          ).pushNamed('/premium-management');
                        },
                    icon: const Icon(Icons.upgrade),
                    label: const Text('Jetzt upgraden'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (_, __) => _buildStandardDetailedNoApplicationsWidget(context),
    );
  }

  Widget _buildStandardDetailedNoApplicationsWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              size: 24,
              color: Colors.orange,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Keine Bewerbungen mehr verfügbar',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.orange,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Upgrade auf ein höheres Paket, um mehr Bewerbungen zu erhalten.',
                    style: TextStyle(fontSize: 14, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: onUpgradePressed ?? () => context.push('/premium'),
          icon: const Icon(Icons.upgrade),
          label: const Text('Jetzt upgraden'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildZeroCreditWidget(BuildContext context, String? originalEmail) {
    return InkWell(
      onTap: onUpgradePressed ?? () => context.push('/premium'),
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Anti-Manipulation erkannt: Guthaben wurde auf 0 gesetzt.',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange.shade800,
                    ),
                  ),
                ),
              ],
            ),
            if (originalEmail != null && originalEmail.isNotEmpty) ...[
              const SizedBox(height: 6),
              Text(
                'Original-Account: $originalEmail',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => context.push('/premium'),
                    icon: const Icon(Icons.star),
                    label: const Text('Premium upgraden'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextButton.icon(
                    onPressed: () => context.push('/settings'),
                    icon: const Icon(Icons.settings),
                    label: const Text('Account wechseln'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForPercentage(BuildContext context, double percentage) {
    if (percentage > 0.5) {
      return Theme.of(context).colorScheme.primary;
    } else if (percentage > 0.2) {
      return Colors.orange;
    } else {
      return Theme.of(context).colorScheme.error;
    }
  }
}

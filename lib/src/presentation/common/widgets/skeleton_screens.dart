import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Einheitliches Skeleton Screen Framework für bessere Loading States
class SkeletonScreens {
  
  /// Job List Skeleton
  static Widget jobListSkeleton({int itemCount = 5}) {
    return ListView.separated(
      itemCount: itemCount,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) => const JobCardSkeleton(),
      padding: const EdgeInsets.all(16),
    );
  }

  /// Profile Screen Skeleton
  static Widget profileSkeleton() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProfileHeaderSkeleton(),
          <PERSON><PERSON><PERSON><PERSON>(height: 24),
          ProfileSectionSkeleton(title: 'Persönliche Informationen'),
          Si<PERSON><PERSON><PERSON>(height: 20),
          ProfileSectionSkeleton(title: 'Berufserfahrung'),
          <PERSON><PERSON><PERSON>ox(height: 20),
          ProfileSectionSkeleton(title: 'Fähigkeiten'),
        ],
      ),
    );
  }

  /// Job Detail Skeleton
  static Widget jobDetailSkeleton() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          JobDetailHeaderSkeleton(),
          SizedBox(height: 24),
          JobDetailContentSkeleton(),
        ],
      ),
    );
  }

  /// Generic List Skeleton
  static Widget genericListSkeleton({
    int itemCount = 5,
    double itemHeight = 80,
    EdgeInsets? padding,
  }) {
    return ListView.separated(
      itemCount: itemCount,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) => SkeletonBox(
        height: itemHeight,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: padding ?? const EdgeInsets.all(16),
    );
  }
}

/// Job Card Skeleton
class JobCardSkeleton extends StatelessWidget {
  const JobCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const SkeletonBox(width: 50, height: 50, isCircular: true),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonBox(
                        width: double.infinity,
                        height: 16,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      const SizedBox(height: 8),
                      SkeletonBox(
                        width: MediaQuery.of(context).size.width * 0.6,
                        height: 14,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SkeletonBox(
              width: double.infinity,
              height: 14,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),
            SkeletonBox(
              width: MediaQuery.of(context).size.width * 0.8,
              height: 14,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                SkeletonBox(
                  width: 80,
                  height: 32,
                  borderRadius: BorderRadius.circular(16),
                ),
                const SizedBox(width: 12),
                SkeletonBox(
                  width: 100,
                  height: 32,
                  borderRadius: BorderRadius.circular(16),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Profile Header Skeleton
class ProfileHeaderSkeleton extends StatelessWidget {
  const ProfileHeaderSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SkeletonBox(width: 100, height: 100, isCircular: true),
        const SizedBox(height: 16),
        SkeletonBox(
          width: MediaQuery.of(context).size.width * 0.6,
          height: 24,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: 8),
        SkeletonBox(
          width: MediaQuery.of(context).size.width * 0.4,
          height: 16,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }
}

/// Profile Section Skeleton
class ProfileSectionSkeleton extends StatelessWidget {
  final String title;
  
  const ProfileSectionSkeleton({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SkeletonBox(
          width: title.length * 8.0,
          height: 18,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: 12),
        ...List.generate(3, (index) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: SkeletonBox(
            width: double.infinity,
            height: 14,
            borderRadius: BorderRadius.circular(4),
          ),
        )),
      ],
    );
  }
}

/// Job Detail Header Skeleton
class JobDetailHeaderSkeleton extends StatelessWidget {
  const JobDetailHeaderSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SkeletonBox(
          width: double.infinity,
          height: 28,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: 12),
        SkeletonBox(
          width: MediaQuery.of(context).size.width * 0.7,
          height: 20,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: 8),
        SkeletonBox(
          width: MediaQuery.of(context).size.width * 0.5,
          height: 16,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }
}

/// Job Detail Content Skeleton
class JobDetailContentSkeleton extends StatelessWidget {
  const JobDetailContentSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...List.generate(8, (index) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: SkeletonBox(
            width: index % 3 == 0 
                ? double.infinity 
                : MediaQuery.of(context).size.width * (0.6 + (index % 3) * 0.15),
            height: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        )),
        const SizedBox(height: 24),
        SkeletonBox(
          width: double.infinity,
          height: 48,
          borderRadius: BorderRadius.circular(8),
        ),
      ],
    );
  }
}

/// Basis Skeleton Box Widget
class SkeletonBox extends StatelessWidget {
  final double? width;
  final double height;
  final BorderRadius? borderRadius;
  final bool isCircular;
  final Color? baseColor;
  final Color? highlightColor;

  const SkeletonBox({
    super.key,
    this.width,
    required this.height,
    this.borderRadius,
    this.isCircular = false,
    this.baseColor,
    this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Shimmer.fromColors(
      baseColor: baseColor ?? (isDark ? Colors.grey[800]! : Colors.grey[300]!),
      highlightColor: highlightColor ?? (isDark ? Colors.grey[700]! : Colors.grey[100]!),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[800] : Colors.grey[300],
          borderRadius: isCircular 
              ? BorderRadius.circular(height / 2)
              : borderRadius ?? BorderRadius.circular(4),
        ),
      ),
    );
  }
}

/// Skeleton Text Widget
class SkeletonText extends StatelessWidget {
  final int lines;
  final double? lineHeight;
  final double? spacing;
  final List<double>? lineWidths;

  const SkeletonText({
    super.key,
    this.lines = 3,
    this.lineHeight = 16,
    this.spacing = 8,
    this.lineWidths,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(lines, (index) {
        final isLastLine = index == lines - 1;
        final width = lineWidths != null && lineWidths!.length > index
            ? lineWidths![index]
            : isLastLine 
                ? MediaQuery.of(context).size.width * 0.6
                : double.infinity;
        
        return Padding(
          padding: EdgeInsets.only(bottom: isLastLine ? 0 : spacing ?? 8),
          child: SkeletonBox(
            width: width,
            height: lineHeight ?? 16,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }
}

/// Adaptive Skeleton Widget
class AdaptiveSkeleton extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Widget? skeleton;
  final Duration animationDuration;

  const AdaptiveSkeleton({
    super.key,
    required this.child,
    required this.isLoading,
    this.skeleton,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: animationDuration,
      child: isLoading 
          ? (skeleton ?? _buildDefaultSkeleton())
          : child,
    );
  }

  Widget _buildDefaultSkeleton() {
    return const SkeletonBox(
      width: double.infinity,
      height: 200,
      borderRadius: BorderRadius.all(Radius.circular(8)),
    );
  }
}

/// Skeleton Screen Builder
class SkeletonBuilder {
  static Widget build({
    required BuildContext context,
    required SkeletonType type,
    Map<String, dynamic>? config,
  }) {
    switch (type) {
      case SkeletonType.jobList:
        return SkeletonScreens.jobListSkeleton(
          itemCount: config?['itemCount'] ?? 5,
        );
      case SkeletonType.profile:
        return SkeletonScreens.profileSkeleton();
      case SkeletonType.jobDetail:
        return SkeletonScreens.jobDetailSkeleton();
      case SkeletonType.genericList:
        return SkeletonScreens.genericListSkeleton(
          itemCount: config?['itemCount'] ?? 5,
          itemHeight: config?['itemHeight'] ?? 80,
        );
    }
  }
}

/// Skeleton Types Enum
enum SkeletonType {
  jobList,
  profile,
  jobDetail,
  genericList,
}

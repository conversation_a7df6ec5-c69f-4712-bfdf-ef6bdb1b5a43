import 'dart:math';
import 'package:flutter/material.dart';

/// Eine einfache, weiße Kreis-Animation für den Button
class SimpleCircleAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;

  SimpleCircleAnimationPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    
    // Hauptkreis zeichnen
    final circlePaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawCircle(center, radius, circlePaint);
    
    // Rotierender Bogen
    final arcPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..strokeCap = StrokeCap.round;
    
    // Rotierender Bogen
    final startAngle = animation * 2 * pi;
    final sweepAngle = pi * 1.5;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      arcPaint,
    );
    
    // Zentraler Punkt
    final centerDotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      center,
      radius * 0.15,
      centerDotPaint,
    );
  }

  @override
  bool shouldRepaint(SimpleCircleAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Widget zum einfachen Einbinden der Animation
class SimpleCircleAnimation extends StatefulWidget {
  final Color color;
  final double size;
  
  const SimpleCircleAnimation({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<SimpleCircleAnimation> createState() => _SimpleCircleAnimationState();
}

class _SimpleCircleAnimationState extends State<SimpleCircleAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000), // Langsame Animation für besseren Effekt
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: SimpleCircleAnimationPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/premium_required_error.dart';
// Import für adServiceProvider

/// Dialog, der angezeigt wird, wenn ein Benutzer versucht, ein Premium-Feature zu nutzen,
/// aber keinen Premium-Zugriff hat.
class PremiumRequiredDialog extends ConsumerWidget {
  /// <PERSON>, der den Dialog ausgelöst hat
  final PremiumRequiredError error;

  /// Callback, der aufgerufen wird, wenn der Benutzer eine Werbung angesehen hat
  final VoidCallback? onAdWatched;

  /// Erstellt einen neuen PremiumRequiredDialog
  const PremiumRequiredDialog({
    super.key,
    required this.error,
    this.onAdWatched,
  });

  /// Zeigt den Dialog an
  static Future<String?> show(
    BuildContext context, {
    required PremiumRequiredError error,
    VoidCallback? onAdWatched,
  }) {
    return showDialog<String>(
      context: context,
      builder:
          (context) =>
              PremiumRequiredDialog(error: error, onAdWatched: onAdWatched),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Bestimme den Feature-Text basierend auf dem Feature
    String featureText = 'Premium-Feature';
    String benefitText =
        'Genieße alle Premium-Funktionen mit einem Abonnement.';

    switch (error.feature) {
      case 'aiCoverLetter':
        featureText = 'KI-Anschreiben-Generator';
        benefitText =
            'Lass dir automatisch personalisierte Anschreiben erstellen, die perfekt auf den Job und dein Profil abgestimmt sind.';
        break;
      case 'aiJobSearch':
        featureText = 'KI-optimierte Jobsuche';
        benefitText =
            'Finde Jobs, die perfekt zu deinen Fähigkeiten und Erfahrungen passen, mit unserer KI-gestützten Suche.';
        break;
      case 'unlimitedFavorites':
        featureText = 'Unbegrenzte Favoriten';
        benefitText =
            'Speichere unbegrenzt viele Jobs als Favoriten und behalte alle interessanten Stellen im Blick.';
        break;
      case 'adFree':
        featureText = 'Werbefreie Nutzung';
        benefitText =
            'Genieße eine störungsfreie Erfahrung ohne Werbeeinblendungen.';
        break;
      case 'insights':
        featureText = 'Premium-Insights';
        benefitText =
            'Erhalte detaillierte Statistiken und Einblicke zu deiner Jobsuche und deinen Bewerbungen.';
        break;
    }

    return AlertDialog(
      title: Text('Zugriff auf $featureText'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Für $featureText benötigst du Premium-Zugriff.'),
          const SizedBox(height: 8),
          Text(benefitText),
          const SizedBox(height: 16),
          const Text('Du hast folgende Möglichkeiten:'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop('cancel'),
          child: const Text('Abbrechen'),
        ),
        if (onAdWatched != null)
          TextButton(
            onPressed: () async {
              await _showAd(context, ref);
              if (context.mounted) {
                Navigator.of(context).pop('ad');
                onAdWatched?.call();
              }
            },
            child: const Text('Werbung ansehen für 24h Zugriff'),
          ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop('premium');
            _navigateToPremiumScreen(context);
          },
          child: const Text('Premium abonnieren'),
        ),
      ],
    );
  }

  /// Gewährt direkten Zugriff auf das Feature (neues Werbungssystem)
  Future<void> _showAd(BuildContext context, WidgetRef ref) async {
    // Neues Werbungssystem: Alle User haben direkten Zugriff auf AI-Features
    // mit normalem Guthaben-Abzug. Werbung wird an anderen UI-Stellen angezeigt.
    debugPrint('Direkter Zugriff gewährt (neues Werbungssystem)');

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Zugriff gewährt! AI-Features verwenden normalen Guthaben-Abzug.'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// Navigiert zur Premium-Verwaltung
  void _navigateToPremiumScreen(BuildContext context) {
    Navigator.of(context).pushNamed('/premium-management');
  }
}

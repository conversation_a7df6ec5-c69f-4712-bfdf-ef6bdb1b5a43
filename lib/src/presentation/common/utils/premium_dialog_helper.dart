import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/premium_required_error.dart';
import 'package:ki_test/src/infrastructure/services/supabase_models.dart';
import 'package:ki_test/src/presentation/common/dialogs/premium_required_dialog.dart';

/// Hilfsfunktion zum Anzeigen des Premium-Dialogs
Future<bool> showPremiumOrAdDialog({
  required BuildContext context,
  required WidgetRef ref,
  VoidCallback? onAdWatched,
  String feature = 'aiCoverLetter',
}) async {
  try {
    // Erstelle einen PremiumRequiredError
    final error = PremiumRequiredError(
      message: 'Premium-Zugriff erforderlich',
      feature: feature,
    );
    
    // Zeige den Dialog an
    final result = await PremiumRequiredDialog.show(
      context,
      error: error,
      onAdWatched: onAdWatched,
    );
    
    // Verarbeite das Ergebnis
    if (result == 'premium') {
      // <PERSON><PERSON>er hat auf "Premium abonnieren" geklickt
      return false;
    } else if (result == 'ad') {
      // Benutzer hat auf "Werbung ansehen" geklickt
      return true;
    } else {
      // Benutzer hat auf "Abbrechen" geklickt oder den Dialog geschlossen
      return false;
    }
  } catch (e) {
    // Fehlerbehandlung
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Fehler: ${e.toString()}'),
        backgroundColor: Colors.red,
      ),
    );
    return false;
  }
}

/// Hilfsfunktion zum Ausführen einer Funktion mit Premium-Fehlerbehandlung
Future<T?> executeWithPremiumCheck<T>({
  required BuildContext context,
  required WidgetRef ref,
  required Future<T> Function() function,
  VoidCallback? onAdWatched,
  String feature = 'aiCoverLetter',
}) async {
  try {
    // Versuche, die Funktion auszuführen
    return await function();
  } on PremiumRequiredError catch (e) {
    // Zeige den Premium-Dialog an
    final result = await PremiumRequiredDialog.show(
      context,
      error: e,
      onAdWatched: onAdWatched,
    );
    
    // Verarbeite das Ergebnis
    if (result == 'ad' && onAdWatched != null) {
      // Benutzer hat auf "Werbung ansehen" geklickt
      return null;
    } else {
      // Benutzer hat auf "Premium abonnieren" oder "Abbrechen" geklickt
      return null;
    }
  } on FunctionError catch (e) {
    // Zeige eine Fehlermeldung an
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Fehler: ${e.message}'),
        backgroundColor: Colors.red,
      ),
    );
    return null;
  } catch (e) {
    // Zeige eine Fehlermeldung an
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Unerwarteter Fehler: ${e.toString()}'),
        backgroundColor: Colors.red,
      ),
    );
    return null;
  }
}

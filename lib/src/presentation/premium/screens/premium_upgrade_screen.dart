import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/models/subscription_plan.dart';
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/presentation/common/widgets/subscription_status_widget.dart';

/// Screen zum Upgrade auf Premium
class PremiumUpgradeScreen extends ConsumerStatefulWidget {
  static const routeName = '/premium-upgrade';

  const PremiumUpgradeScreen({super.key});

  @override
  ConsumerState<PremiumUpgradeScreen> createState() =>
      _PremiumUpgradeScreenState();
}

class _PremiumUpgradeScreenState extends ConsumerState<PremiumUpgradeScreen> {
  final _log = getLogger('PremiumUpgradeScreen');
  SubscriptionPlanType _selectedPlan = SubscriptionPlanType.basic;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Premium-Upgrade')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Aktueller Abonnement-Status
                      const SubscriptionStatusWidget(showNavigateButton: false),
                      const SizedBox(height: AppTheme.spacingLarge),

                      // Überschrift
                      Text(
                        'Wähle deinen Plan',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // Plan-Auswahl
                      _buildPlanSelection(),
                      const SizedBox(height: AppTheme.spacingLarge),

                      // Upgrade-Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _handleUpgrade,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor:
                                Theme.of(context).colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(
                              vertical: AppTheme.spacingMedium,
                            ),
                          ),
                          child: const Text('Upgrade durchführen'),
                        ),
                      ),

                      // Hinweis
                      const SizedBox(height: AppTheme.spacingMedium),
                      Text(
                        kDebugMode
                            ? 'Hinweis: In der Testversion wird kein Geld abgebucht. Du kannst die Premium-Funktionen kostenlos testen.'
                            : 'Hinweis: Für den Kauf wird ein gültiges Zahlungsverfahren über den Google Play Store benötigt.',
                        style: TextStyle(
                          color: kDebugMode ? Colors.grey : Colors.orange[800],
                          fontSize: 12,
                          fontWeight:
                              kDebugMode ? FontWeight.normal : FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildPlanSelection() {
    return Column(
      children: [
        // Basic-Plan
        _buildPlanCard(
          SubscriptionPlans.basic,
          isSelected: _selectedPlan == SubscriptionPlanType.basic,
          onTap:
              () => setState(() => _selectedPlan = SubscriptionPlanType.basic),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        // Pro-Plan
        _buildPlanCard(
          SubscriptionPlans.pro,
          isSelected: _selectedPlan == SubscriptionPlanType.pro,
          onTap: () => setState(() => _selectedPlan = SubscriptionPlanType.pro),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        // Unlimited-Plan
        _buildPlanCard(
          SubscriptionPlans.unlimited,
          isSelected: _selectedPlan == SubscriptionPlanType.unlimited,
          onTap:
              () => setState(
                () => _selectedPlan = SubscriptionPlanType.unlimited,
              ),
        ),
      ],
    );
  }

  Widget _buildPlanCard(
    SubscriptionPlan plan, {
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Card(
        elevation: isSelected ? 4 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          side:
              isSelected
                  ? BorderSide(color: plan.getColor(context), width: 2)
                  : BorderSide.none,
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Plan-Name und Preis
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    plan.name,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: plan.getColor(context),
                    ),
                  ),
                  Text(
                    plan.priceText,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingSmall),

              // Plan-Details
              Row(
                children: [
                  Icon(
                    Icons.description_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Bewerbungen: ${plan.applicationsText}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              Row(
                children: [
                  Icon(
                    plan.showsAds ? Icons.ads_click : Icons.block,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(plan.adsText, style: TextStyle(color: Colors.grey[600])),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleUpgrade() async {
    setState(() => _isLoading = true);

    try {
      // Verwende den echten PaymentService, um das Upgrade durchzuführen
      final paymentService = ref.read(paymentServiceProvider);
      final success = await paymentService.buyPremiumSubscription(
        planType: _selectedPlan.name,
      );

      if (success) {
        if (mounted) {
          AppDialogs.showSuccessDialog(
            context,
            'Upgrade erfolgreich',
            'Dein Abonnement wurde erfolgreich auf ${_selectedPlan.toString().split('.').last} aktualisiert.',
          );
        }
      } else {
        if (mounted) {
          AppDialogs.showErrorDialog(
            context,
            'Dein Abonnement konnte nicht aktualisiert werden. Bitte versuche es später erneut.',
            title: 'Fehler beim Upgrade',
          );
        }
      }
    } catch (e) {
      _log.e('Fehler beim Upgrade', error: e);
      if (mounted) {
        AppDialogs.showErrorDialog(
          context,
          'Ein unerwarteter Fehler ist aufgetreten: $e',
          title: 'Fehler beim Upgrade',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/presentation/premium/screens/subscription_plans_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_upgrade_screen.dart';
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/presentation/common/widgets/remaining_applications_widget.dart';

/// Screen zur Anzeige der Premium-Vorteile
class PremiumBenefitsScreen extends ConsumerWidget {
  static const routeName = '/premium-benefits';

  const PremiumBenefitsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileProvider);
    final isPremium = userProfileState.asData?.value.isPremium ?? false;

    void navigateToPlansScreen() {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const SubscriptionPlansScreen(),
        ),
      );
    }

    void navigateToUpgradeScreen() {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const PremiumUpgradeScreen()),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Premium Vorteile'),
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 16),

              // Header
              const Text(
                'Wechseln Sie zu Premium und nutzen Sie das volle Potenzial:',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 32),

              // Unbegrenzte KI-Anschreiben
              _buildFeatureTile(
                context,
                icon: Icons.auto_awesome,
                iconColor: Colors.purple,
                title: 'Unbegrenzte KI-Anschreiben',
                description:
                    'Generieren Sie so viele Bewerbungen, wie Sie benötigen.',
              ),

              const SizedBox(height: 24),

              // Werbefreie Erfahrung
              _buildFeatureTile(
                context,
                icon: Icons.block,
                iconColor: Colors.red,
                title: 'Werbefreie Erfahrung',
                description: 'Nutzen Sie die App ohne Unterbrechungen.',
              ),

              const SizedBox(height: 24),

              // Erweiterte KI-Analysen
              _buildFeatureTile(
                context,
                icon: Icons.insights,
                iconColor: Colors.blue,
                title: 'Erweiterte KI-Analysen',
                description:
                    'Erhalten Sie tiefere Einblicke und Optimierungsvorschläge (bald verfügbar).',
              ),

              const SizedBox(height: 40),

              // Premium-Status und verbleibende Bewerbungen
              if (isPremium)
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 24,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.purple.withAlpha(50),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.purple,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Sie sind bereits Premium-Nutzer!',
                            style: TextStyle(
                              color: Colors.purple,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Verbleibende Bewerbungen
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(20),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          const Text(
                            'Deine verbleibenden Bewerbungen',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          const RemainingApplicationsWidget(),
                        ],
                      ),
                    ),
                  ],
                )
              else
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: navigateToPlansScreen,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        'Premium-Pläne anzeigen',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: navigateToUpgradeScreen,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        'Premium-Upgrade',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

              // Test Premium Button (nur für Entwicklung)
              const SizedBox(height: 24),
              if (!isPremium)
                OutlinedButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder:
                          (context) => _buildTestPremiumDialog(context, ref),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text('Premium testen'),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureTile(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: iconColor, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTestPremiumDialog(BuildContext context, WidgetRef ref) {
    return AlertDialog(
      title: const Text('Premium-Plan testen'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Wähle einen Premium-Plan zum Testen:'),
          const SizedBox(height: 16),

          // Basic Plan
          _buildPlanOption(
            context,
            title: 'Basic',
            description: '30 Bewerbungen/Monat',
            color: Colors.blue,
            onTap: () {
              Navigator.of(context).pop();
              _activateTestPremium(context, ref, 'basic');
            },
          ),

          const SizedBox(height: 8),

          // Pro Plan
          _buildPlanOption(
            context,
            title: 'Pro',
            description: '150 Bewerbungen/Monat',
            color: Colors.purple,
            onTap: () {
              Navigator.of(context).pop();
              _activateTestPremium(context, ref, 'pro');
            },
          ),

          const SizedBox(height: 8),

          // Unlimited Plan
          _buildPlanOption(
            context,
            title: 'Unlimited',
            description: 'Unbegrenzte Bewerbungen',
            color: Colors.orange,
            onTap: () {
              Navigator.of(context).pop();
              _activateTestPremium(context, ref, 'unlimited');
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Abbrechen'),
        ),
      ],
    );
  }

  Future<void> _activateTestPremium(
    BuildContext context,
    WidgetRef ref,
    String planType,
  ) async {
    // Zeige Ladeindikator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Setze das Ablaufdatum auf 30 Tage in der Zukunft
      final expiryDate = DateTime.now().add(const Duration(days: 30));

      // Aktualisiere den Premium-Status über den UserProfileNotifier
      await ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: true,
            premiumExpiryDate: expiryDate,
            planType: planType,
          );

      // Schließe den Ladeindikator
      if (context.mounted) {
        Navigator.of(context).pop();

        // Zeige Erfolgsmeldung
        AppDialogs.showSuccessDialog(
          context,
          'Premium aktiviert',
          'Du hast jetzt Zugriff auf alle Funktionen des $planType-Plans für 30 Tage.',
        );
      }
    } catch (e) {
      // Schließe den Ladeindikator
      if (context.mounted) {
        Navigator.of(context).pop();

        // Zeige Fehlermeldung
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler bei der Premium-Aktivierung: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildPlanOption(
    BuildContext context, {
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: color),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle_outline, color: color),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold, color: color),
                  ),
                  Text(description, style: const TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

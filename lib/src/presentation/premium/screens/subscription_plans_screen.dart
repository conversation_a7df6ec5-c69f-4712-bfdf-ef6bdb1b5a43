import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/models/subscription_plan.dart';
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/presentation/common/widgets/remaining_applications_widget.dart';

final _selectedPlanProvider = StateProvider<SubscriptionPlanType>(
  (ref) => SubscriptionPlanType.basic,
);
final _isLoadingProvider = StateProvider<bool>((_) => false);

/// Screen zur Anzeige und Auswahl der verschiedenen Abonnementpläne
class SubscriptionPlansScreen extends ConsumerWidget {
  static const routeName = '/subscription-plans';

  const SubscriptionPlansScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('SubscriptionPlansScreen');
    final userProfileState = ref.watch(userProfileProvider);
    final paymentService = ref.read(paymentServiceProvider);
    final isLoading = ref.watch(_isLoadingProvider);
    final selectedPlan = ref.watch(_selectedPlanProvider);

    final isPremium = userProfileState.asData?.value.isPremium ?? false;

    void handlePurchase() async {
      final planType =
          selectedPlan.toString().split('.').last; // Konvertiere Enum zu String
      log.i("Premium-Kauf initiiert für Plan: $planType");
      ref.read(_isLoadingProvider.notifier).state = true;
      AppDialogs.showLoadingDialog(context, "Verbinde mit Store...");

      try {
        final success = await paymentService.buyPremiumSubscription(
          planType: planType,
        );
        if (context.mounted) {
          Navigator.of(context).pop(); // Schließe den Lade-Dialog
        }

        if (success) {
          ref.refresh(userProfileProvider);
          final updatedUserProfile =
              ref.read(userProfileProvider).asData?.value;
          final purchaseSuccess = updatedUserProfile?.isPremium ?? false;

          if (context.mounted) {
            if (purchaseSuccess) {
              AppDialogs.showSuccessDialog(
                context,
                "Abonnement aktiviert",
                "Vielen Dank für deinen Kauf! Du hast jetzt Zugriff auf alle Funktionen deines ${planType.toUpperCase()}-Plans.",
              );
            } else {
              AppDialogs.showErrorDialog(
                context,
                "Kauf erfolgreich, aber Abonnement-Status konnte nicht aktiviert werden. Bitte kontaktiere den Support.",
              );
            }
          }
        } else {
          if (context.mounted) {
            AppDialogs.showErrorDialog(
              context,
              "Der Kauf konnte nicht abgeschlossen werden. Bitte versuche es später erneut.",
            );
          }
        }
      } catch (e, s) {
        log.e("Fehler während des Abonnement-Kaufs", error: e, stackTrace: s);
        if (context.mounted) {
          Navigator.of(context).pop(); // Schließe den Lade-Dialog
        }
        if (context.mounted) {
          AppDialogs.showErrorDialog(
            context,
            "Ein unerwarteter Fehler ist aufgetreten: ${e.toString()}",
          );
        }
      } finally {
        ref.read(_isLoadingProvider.notifier).state = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Premium-Pläne'),
        actions: [
          if (isPremium)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Chip(
                backgroundColor: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.2),
                label: Text(
                  'Aktiv',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                avatar: Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingLarge),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primary.withBlue(150),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusLarge,
                  ),
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.workspace_premium,
                      size: 64,
                      color: Colors.white,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    Text(
                      'Wähle deinen Plan',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    Text(
                      'Finde den perfekten Plan für deine Jobsuche',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Preistabelle
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusMedium,
                  ),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.5),
                  ),
                ),
                child: Column(
                  children: [
                    // Tabellenkopf
                    Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppTheme.spacingMedium,
                        horizontal: AppTheme.spacingSmall,
                      ),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(AppTheme.borderRadiusMedium),
                          topRight: Radius.circular(
                            AppTheme.borderRadiusMedium,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              'Plan',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Text(
                              'Bewerbungen/Monat',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              'Preis (€)',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(flex: 1, child: Container()),
                        ],
                      ),
                    ),

                    // Pläne
                    ...SubscriptionPlans.allPlans.map(
                      (plan) => _buildPlanRow(
                        context,
                        ref,
                        plan,
                        isSelected:
                            selectedPlan ==
                            (plan.type ?? SubscriptionPlanType.basic),
                        onSelect:
                            () =>
                                ref.read(_selectedPlanProvider.notifier).state =
                                    plan.type ?? SubscriptionPlanType.basic,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Ausgewählter Plan Details
              _buildSelectedPlanDetails(context, ref, selectedPlan),

              const SizedBox(height: AppTheme.spacingMedium),

              // Verbleibende Bewerbungen
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusMedium,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  child: Column(
                    children: [
                      Text(
                        'Deine verbleibenden Bewerbungen',
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),
                      const Center(child: RemainingApplicationsWidget()),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Kaufen-Button
              if (!isPremium)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon:
                        isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Icon(Icons.star),
                    label: Text(isLoading ? 'Verarbeite...' : 'Abonnieren'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppTheme.spacingMedium,
                      ),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      textStyle: Theme.of(
                        context,
                      ).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    onPressed: isLoading ? null : handlePurchase,
                  ),
                ),

              const SizedBox(height: AppTheme.spacingMedium),

              // Hinweis
              Text(
                'Alle Preise inkl. MwSt. Du kannst dein Abonnement jederzeit in den Einstellungen kündigen.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanRow(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlan plan, {
    required bool isSelected,
    required VoidCallback onSelect,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: AppTheme.spacingMedium,
        horizontal: AppTheme.spacingSmall,
      ),
      decoration: BoxDecoration(
        color:
            isSelected
                ? plan.getColor(context).withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              plan.name,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: isSelected ? plan.getColor(context) : null,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              plan.applicationsText,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              plan.priceText,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Radio<SubscriptionPlanType>(
              value: plan.type ?? SubscriptionPlanType.basic,
              groupValue: ref.watch(_selectedPlanProvider),
              onChanged: (_) => onSelect(),
              activeColor: plan.getColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedPlanDetails(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlanType selectedPlanType,
  ) {
    final plan = SubscriptionPlans.getPlan(selectedPlanType);

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: plan.getColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: plan.getColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dein ausgewählter Plan: ${plan.name}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: plan.getColor(context),
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(plan.benefits, style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildFeatureItem(
            context,
            'Bewerbungen pro Monat',
            plan.applicationsText,
            isIncluded: true,
          ),
          _buildFeatureItem(
            context,
            'Werbung',
            plan.showsAds ? 'Mit Werbung' : 'Ohne Werbung',
            isIncluded: !plan.showsAds,
          ),
          _buildFeatureItem(
            context,
            'KI-Anschreiben',
            plan.type == SubscriptionPlanType.unlimited
                ? 'Unbegrenzt'
                : 'Begrenzt',
            isIncluded: true,
          ),
          _buildFeatureItem(
            context,
            '1-Klick Bewerbung',
            plan.type == SubscriptionPlanType.unlimited
                ? 'Inklusive'
                : 'Nicht verfügbar',
            isIncluded: plan.type == SubscriptionPlanType.unlimited,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    String title,
    String value, {
    required bool isIncluded,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        children: [
          Icon(
            isIncluded ? Icons.check_circle : Icons.cancel,
            color:
                isIncluded
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

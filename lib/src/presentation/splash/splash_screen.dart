import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../core/theme/app_theme.dart';
// Importiere AppShell aus main.dart
// NEU: Importiere Onboarding Provider und Screen
// Import entfernt - wird bereits über andere Datei importiert


class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {


    // Animation Controller für die Animationen
    final tickerProvider = useSingleTickerProvider();
    final animationController = useMemoized(() {
      return AnimationController(
        vsync: tickerProvider,
        duration: const Duration(milliseconds: 400), // Minimale Animation
      );
    }, [tickerProvider]);

    // App-Open-Ad wird jetzt in PrelaunchApp gehandhabt
    // Keine redundante Logik hier mehr nötig

    // Starte die Animation einmal und kümmere dich um die Entsorgung
    useEffect(() {
      // Starte die Animation nur, wenn sie noch nicht läuft
      if (!animationController.isAnimating &&
          animationController.status != AnimationStatus.completed) {
        animationController.forward();
      }

      // Cleanup-Funktion, die aufgerufen wird, wenn das Widget entfernt wird
      return () {
        // Stoppe die Animation, bevor sie entsorgt wird
        if (animationController.isAnimating) {
          animationController.stop();
        }
        // Entsorge den Controller
        animationController.dispose();
      };
    }, []);

    // Animation für das Logo (schnellere Animation)
    final logoScaleAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
        ),
      ),
    );

    // Animation für den Title (Fade) (schnellere Animation)
    final titleOpacityAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0.2, 0.5, curve: Curves.easeIn),
        ),
      ),
    );

    // Erstelle die Slide-Animation direkt (schnellere Animation)
    final Animation<Offset> titleSlideAnimationObject = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: animationController,
        curve: const Interval(0.2, 0.5, curve: Curves.easeOut),
      ),
    );

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        // Verwende einen Stack für stabile Positionierung
        child: Stack(
          alignment: Alignment.center, // Zentriert Kinder im Stack
          children: [
            // Logo Animation (positioniert im Zentrum)
            Transform.scale(
              scale: logoScaleAnimation,
              child: Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: AppTheme.mediumShadow,
                ),
                padding: const EdgeInsets.all(20),
                child: const Icon(
                  Icons.work_outline,
                  color: AppTheme.primaryColor,
                  size: 80,
                ),
              ),
            ),

            // App-Name mit Fade-In und Slide-Up
            Align(
              alignment: const Alignment(0.0, 0.4),
              child: SlideTransition(
                position: titleSlideAnimationObject,
                child: Opacity(
                  opacity: titleOpacityAnimation,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Bewerbung KI',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(
                                76,
                              ), // 0.3 * 255 = ~76
                              offset: const Offset(0, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  
}

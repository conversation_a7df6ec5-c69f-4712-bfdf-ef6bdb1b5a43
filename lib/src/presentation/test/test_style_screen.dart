import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/utils/logging.dart';

class TestStyleScreen extends HookConsumerWidget {
  const TestStyleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('TestStyleScreen');
    final resultState = useState<String?>(null);
    final isLoading = useState(false);

    Future<void> testAnalyzeProfileStyle() async {
      isLoading.value = true;
      resultState.value = "Teste Funktion 'analyze-profile-style'...";

      try {
        final supabaseService = ref.read(supabaseServiceProvider);

        // Rufe die Funktion auf
        final result = await supabaseService.analyzeProfileStyle();

        log.i("<PERSON><PERSON><PERSON><PERSON> von analyze-profile-style: $result");
        resultState.value = "Ergebnis:\n\n$result";
      } catch (e) {
        log.e("Fehler beim Testen von analyze-profile-style", error: e);
        resultState.value = "Fehler: ${e.toString()}";
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(title: const Text('Test: Personalisierter Schreibstil')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Diese Seite testet die Funktion "analyze-profile-style", die einen personalisierten Schreibstil-Prompt basierend auf Ihrem Profil generiert.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: isLoading.value ? null : testAnalyzeProfileStyle,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child:
                  isLoading.value
                      ? const CircularProgressIndicator()
                      : const Text(
                        'Test starten',
                        style: TextStyle(fontSize: 18),
                      ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    resultState.value ?? 'Noch kein Ergebnis',
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/application/providers/favorites_provider.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_screen.dart';

import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/services.dart';

// Verwende den minimalistischen Ladekreis für die Bewerbungsgenerierung

// Benutzerdefinierter Painter für den animierten Hintergrund
class AnimatedBackgroundPainter extends CustomPainter {
  final double animation;
  final List<Color> colors;

  AnimatedBackgroundPainter({required this.animation, required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Erstelle einen Farbverlauf für den Hintergrund
    final gradient = LinearGradient(
      colors: colors,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );

    final rect = Rect.fromLTWH(0, 0, width, height);
    final paint =
        Paint()
          ..shader = gradient.createShader(rect)
          ..style = PaintingStyle.fill;

    // Zeichne den Hintergrund
    canvas.drawRRect(RRect.fromRectAndRadius(rect, Radius.circular(8)), paint);

    // Zeichne animierte Wellen
    final wavePaint =
        Paint()
          ..color = Colors.white.withAlpha(50)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    for (int i = 0; i < 3; i++) {
      final path = Path();
      final amplitude = 5.0 + (i * 2.0);
      final frequency = 0.05 + (i * 0.02);
      final phase = animation * 2 * pi + (i * 1.0);

      path.moveTo(0, height / 2);

      for (double x = 0; x < width; x += 5) {
        final y = height / 2 + amplitude * sin(frequency * x + phase);
        path.lineTo(x, y);
      }

      canvas.drawPath(path, wavePaint);
    }

    // Zeichne glänzende Punkte
    final pointPaint =
        Paint()
          ..color = Colors.white.withAlpha(150)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < 10; i++) {
      final x =
          (width * 0.2) +
          (width * 0.6 * ((i / 10) + (animation * 0.2))) % width;
      final y = height * (0.3 + 0.4 * sin(animation * pi + i));
      final radius = 1.5 + (1.0 * sin(animation * pi * 2 + i));

      canvas.drawCircle(Offset(x, y), radius, pointPaint);
    }
  }

  @override
  bool shouldRepaint(AnimatedBackgroundPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

// Enum für die Modellauswahl entfernt
// enum AiModel { deepseek, gemini }

// Kein Provider mehr für Details nötig
// final jobDetailProvider = StateProvider<AsyncValue<JobEntity?>>((ref) => const AsyncValue.loading());

// Wird zu einem ConsumerStatefulWidget (StatefulWidget wegen AnimationController)
class JobDetailScreen extends ConsumerStatefulWidget {
  // StatefulWidget für WebView Controller
  final String jobRefnr;
  final String jobTitle;
  final JobEntity? jobEntity; // NEU: Optionale JobEntity für Metadaten

  const JobDetailScreen({
    super.key,
    required this.jobRefnr,
    required this.jobTitle,
    this.jobEntity, // NEU
  });

  @override
  ConsumerState<JobDetailScreen> createState() => _JobDetailScreenState();
}

// Nutze ConsumerState
class _JobDetailScreenState extends ConsumerState<JobDetailScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // Widget für den Premium-Button mit Animation
  Widget _buildPremiumButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _kiAnschreibenAnimationController,
      builder: (context, child) {
        // Berechne einen pulsierenden Wert für die Animation
        final pulseValue =
            _isGenerating
                ? 0.5 + 0.5 * sin(_kiAnschreibenAnimation.value * pi * 2 + 0.5)
                : 0.0;

        return Container(
          width: double.infinity,
          height: 48, // Etwas kleiner als der Hauptbutton
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient:
                _isGenerating
                    ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.purple.shade300,
                        Colors.blue.shade300,
                        Colors.purple.shade500,
                        Colors.blue.shade500,
                      ],
                      stops: [
                        0.0,
                        0.3 + (0.2 * pulseValue),
                        0.6 + (0.1 * pulseValue),
                        1.0,
                      ],
                    )
                    : null,
            boxShadow:
                _isGenerating
                    ? [
                      BoxShadow(
                        color: Colors.purple.shade300.withAlpha(100),
                        blurRadius: 8 + (4 * pulseValue),
                        spreadRadius: 1 + (1 * pulseValue),
                      ),
                      BoxShadow(
                        color: Colors.blue.shade300.withAlpha(100),
                        blurRadius: 8 + (4 * pulseValue),
                        spreadRadius: 1 + (1 * pulseValue),
                      ),
                    ]
                    : null,
          ),
          child: Stack(
            children: [
              // Animierter Hintergrund während der Generierung
              if (_isGenerating)
                Positioned.fill(
                  child: CustomPaint(
                    painter: AnimatedBackgroundPainter(
                      animation:
                          _kiAnschreibenAnimation.value +
                          0.3, // Versatz für andere Effekte
                      colors: [
                        Colors.purple.shade300,
                        Colors.blue.shade300,
                        Colors.purple.shade500,
                        Colors.blue.shade500,
                      ],
                    ),
                  ),
                ),

              // Button-Inhalt
              Material(
                color: _isGenerating ? Colors.transparent : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    _log.i("Premium-Button geklickt, navigiere zu /premium");
                    GoRouter.of(context).push(PremiumScreen.routeName);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.lock_outline,
                          size: 18,
                          color:
                              _isGenerating
                                  ? Colors.white
                                  : Theme.of(context).disabledColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Zusätzliche Hinweise (Premium)',
                          style: TextStyle(
                            color:
                                _isGenerating
                                    ? Colors.white
                                    : Theme.of(context).disabledColor,
                            fontWeight:
                                _isGenerating
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                            fontSize: 14,
                            shadows:
                                _isGenerating
                                    ? [
                                      Shadow(
                                        color: Colors.black.withAlpha(100),
                                        blurRadius: 3,
                                        offset: Offset(0, 1),
                                      ),
                                    ]
                                    : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Widget für den KI-Anschreiben-Button mit Animation
  Widget _buildAnimatedButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _kiAnschreibenAnimationController,
      builder: (context, child) {
        // Berechne einen pulsierenden Wert für die Animation
        final pulseValue =
            _isGenerating
                ? 0.5 + 0.5 * sin(_kiAnschreibenAnimation.value * pi * 2)
                : 0.0;

        return Container(
          width: double.infinity,
          height: 56, // Feste Höhe für bessere Sichtbarkeit
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient:
                _isGenerating
                    ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.pink.shade400,
                        Colors.amber.shade400,
                        Colors.pink.shade600,
                        Colors.amber.shade600,
                      ],
                      stops: [
                        0.0,
                        0.3 + (0.2 * pulseValue),
                        0.6 + (0.1 * pulseValue),
                        1.0,
                      ],
                    )
                    : null,
            boxShadow:
                _isGenerating
                    ? [
                      BoxShadow(
                        color: Colors.pink.shade400.withAlpha(128),
                        blurRadius: 10 + (5 * pulseValue),
                        spreadRadius: 1 + (2 * pulseValue),
                      ),
                      BoxShadow(
                        color: Colors.amber.shade400.withAlpha(128),
                        blurRadius: 10 + (5 * pulseValue),
                        spreadRadius: 1 + (2 * pulseValue),
                      ),
                    ]
                    : null,
          ),
          child: Stack(
            children: [
              // Animierter Hintergrund während der Generierung
              if (_isGenerating)
                Positioned.fill(
                  child: CustomPaint(
                    painter: AnimatedBackgroundPainter(
                      animation: _kiAnschreibenAnimation.value,
                      colors: [
                        Colors.pink.shade300,
                        Colors.amber.shade300,
                        Colors.pink.shade700,
                        Colors.amber.shade700,
                      ],
                    ),
                  ),
                ),

              // Button-Inhalt
              Material(
                color:
                    _isGenerating
                        ? Colors.transparent
                        : Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap:
                      _isGenerating
                          ? null
                          : () => _onGenerateButtonPressed(context),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isGenerating) ...[
                          // Animierter Kreis
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: Stack(
                              children: [
                                // Hintergrund-Kreis
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withAlpha(51),
                                  ),
                                ),
                                // Animierter Fortschrittskreis
                                CircularProgressIndicator(
                                  strokeWidth: 3,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),
                        ],
                        Text(
                          _isGenerating
                              ? 'Generiere Anschreiben...'
                              : 'KI-Anschreiben generieren',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            shadows:
                                _isGenerating
                                    ? [
                                      Shadow(
                                        color: Colors.black.withAlpha(100),
                                        blurRadius: 3,
                                        offset: Offset(0, 1),
                                      ),
                                    ]
                                    : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Methode zum Starten der Anschreiben-Generierung
  Future<void> _onGenerateButtonPressed(BuildContext context) async {
    if (_isGenerating) return; // Verhindere mehrfache Aufrufe

    // Starte die Animation
    setState(() {
      _isGenerating = true;
    });

    // Starte die Animation
    _kiAnschreibenAnimationController.reset();
    _kiAnschreibenAnimationController.repeat();

    // Prüfe, ob ein CAPTCHA erkannt wurde und noch nicht gelöst ist
    if (_captchaDetected && !_captchaSolved) {
      _log.i("CAPTCHA erkannt, aber noch nicht gelöst. Scrolle zum CAPTCHA...");
      await _scrollToCaptchaAndHighlight();

      // Zeige eine Meldung an
      if (mounted) {
        // Verwende WidgetsBinding.instance.addPostFrameCallback, um BuildContext-Probleme zu vermeiden
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Bitte lösen Sie zuerst das CAPTCHA, um fortzufahren.',
                ),
                duration: Duration(seconds: 5),
              ),
            );
          }
        });
      }

      // Stoppe die Animation
      setState(() {
        _isGenerating = false;
      });
      _kiAnschreibenAnimationController.stop();
      return;
    }

    // Hier würde die eigentliche Anschreiben-Generierung stattfinden
    // Simuliere eine Verzögerung für die Demo
    await Future.delayed(const Duration(seconds: 3));

    // Stoppe die Animation
    if (mounted) {
      setState(() {
        _isGenerating = false;
      });
      _kiAnschreibenAnimationController.stop();

      // Zeige eine Erfolgsmeldung an
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Anschreiben erfolgreich generiert!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  late final WebViewController _controller;
  bool _isLoadingPage = true; // Ladezustand für die Seite
  bool _captchaDetected = false; // Flag für erkanntes Captcha
  bool _captchaSolved = false; // Flag für gelöstes Captcha

  bool _externalLinkCheckRunning = false; // Flag für externe Link-Prüfung

  // NEU: Zustandsvariablen für WebView-Navigation und Hinweis
  bool _canGoBack = false;
  bool _showExternalLinkInfo = false;

  // NEU: Variable, die anzeigt, ob die Tastatur sichtbar ist
  bool _isKeyboardVisible = false;
  final FocusNode _hintsFocusNode = FocusNode();

  // Animation Controller für Button-Effekt
  late AnimationController _buttonAnimationController;
  // Controller für Hintergrund-Animation
  late AnimationController _gradientSweepController;
  // Controller für KI-Anschreiben-Button-Animation
  late AnimationController _kiAnschreibenAnimationController;
  late Animation<double> _kiAnschreibenAnimation;

  final TextEditingController _hintsController =
      TextEditingController(); // Hinzugefügt
  final _log = getLogger('JobDetailScreen'); // Logger Instanz

  // *** NEU: Zustand für Generierungsprozess ***
  bool _isGenerating = false;

  // *** NEU: GlobalKey und State für Footer-Höhe ***
  final GlobalKey _footerKey = GlobalKey();
  double _footerHeight = 150.0; // Initialer Schätzwert, wird überschrieben

  @override
  void initState() {
    super.initState();
    // Registriere diesen State als WidgetsBindingObserver
    WidgetsBinding.instance.addObserver(this);

    // SystemUI-Farben werden in didChangeDependencies gesetzt, nicht hier

    final String url =
        'https://www.arbeitsagentur.de/jobsuche/jobdetail/${widget.jobRefnr}';
    _log.i("Initialisiere JobDetailScreen mit URL: $url");

    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0x00000000))
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                _log.d("WebView loading progress: $progress%");
              },
              onPageStarted: (String url) {
                _log.i("WebView started loading: $url");
                setState(() {
                  _isLoadingPage = true;
                  _log.d(
                    'BANNER_DEBUG: onPageStarted - Setze _showExternalLinkInfo auf false.',
                  );
                  _showExternalLinkInfo = false; // Reset bei neuer Seite
                  _externalLinkCheckRunning =
                      false; // *** Flag zurücksetzen ***
                });
              },
              onPageFinished: (url) async {
                if (!mounted) return;
                _log.d('BANNER_DEBUG: onPageFinished - Seite geladen: $url');
                setState(() {
                  _isLoadingPage = false;
                });

                // Prüfe, ob wir auf einer externen Seite sind (nicht arbeitsagentur.de)
                bool isExternalUrl = !url.contains('arbeitsagentur.de');

                if (isExternalUrl) {
                  _log.i(
                    "Externe Seite erkannt, CAPTCHA-Prüfung wird vollständig übersprungen",
                  );
                  // Setze den CAPTCHA-Status explizit
                  if (mounted) {
                    setState(() {
                      _captchaDetected = false;
                      _captchaSolved = true;
                      _showExternalLinkInfo =
                          true; // Setze das Flag für externe Seiten
                    });
                  }
                } else {
                  // Optional: Prüfe nach Captcha, wenn Seite fertig geladen ist und wir auf arbeitsagentur.de sind
                  _checkForCaptcha();
                }

                await Future.delayed(
                  const Duration(milliseconds: 1200),
                ); // 1200ms warten
                if (!mounted) return; // Erneut prüfen, da Delay

                if (_externalLinkCheckRunning) {
                  _log.d(
                    'BANNER_DEBUG: onPageFinished - _checkForExternalLink läuft bereits, überspringe erneuten Aufruf.',
                  );
                  return;
                }
                _externalLinkCheckRunning = true; // *** Flag setzen ***
                _log.d(
                  'BANNER_DEBUG: onPageFinished - Rufe _checkForExternalLink nach Delay auf...',
                );

                await _checkForExternalLink(); // Führe die Prüfung durch
                _log.d(
                  'BANNER_DEBUG: onPageFinished - _checkForExternalLink abgeschlossen.',
                );
                // _externalLinkCheckRunning wird in _checkForExternalLink zurückgesetzt

                // NEU: WebView kann zurückgehen?
                _controller.canGoBack().then((canGoBack) {
                  if (mounted && _canGoBack != canGoBack) {
                    setState(() {
                      _canGoBack = canGoBack;
                    });
                  }
                });
              },
              onWebResourceError: (WebResourceError error) {
                _log.e("WebView error: ${error.description}", error: error);
                setState(() {
                  _isLoadingPage = false; // Ladeindikator stoppen bei Fehler
                });
                // Zeige eine Fehlermeldung an
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Fehler beim Laden der Seite: ${error.description}',
                    ),
                  ),
                );
              },
              onNavigationRequest: (NavigationRequest request) {
                if (request.url.startsWith('https://www.youtube.com/')) {
                  _log.w("Navigation zu YouTube blockiert: ${request.url}");
                  return NavigationDecision.prevent;
                }
                _log.d("WebView navigation request: ${request.url}");
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse(url));

    // Animation Controller initialisieren
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );

    // Zweiten Controller für Hintergrund-Animation initialisieren
    _gradientSweepController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 1500,
      ), // Etwas langsamer für den Sweep
    );

    // KI-Anschreiben-Button-Animation initialisieren
    _kiAnschreibenAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _kiAnschreibenAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _kiAnschreibenAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // *** NEU: Höhe des Footers nach dem ersten Frame messen ***
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_footerKey.currentContext != null) {
        final RenderBox renderBox =
            _footerKey.currentContext!.findRenderObject() as RenderBox;
        if (mounted && _footerHeight != renderBox.size.height) {
          setState(() {
            _footerHeight = renderBox.size.height;
            _log.d("FOOTER_DEBUG: Gemessene Footer-Höhe: $_footerHeight");
          });
        }
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // SystemUI-Farben hier setzen, wo Theme.of(context) sicher verfügbar ist
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    // Entferne Observer
    WidgetsBinding.instance.removeObserver(this);

    // Setze SystemUI-Farben zurück
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.light,
    ); // Zurück zum Standard

    // Stoppe den CAPTCHA-Überprüfungstimer
    _stopCaptchaCheckTimer();

    _buttonAnimationController.dispose(); // Controller entsorgen
    _gradientSweepController.dispose(); // Sweep-Controller entsorgen
    _kiAnschreibenAnimationController
        .dispose(); // KI-Anschreiben-Button-Controller entsorgen
    _hintsController.dispose(); // Hinzugefügt
    _hintsFocusNode.dispose(); // FocusNode entsorgen
    super.dispose();
  }

  // NEU: Implementiere didChangeMetrics aus WidgetsBindingObserver, um die Tastatur zu überwachen
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // Berechne, ob die Tastatur sichtbar ist
    // Verwende die neue API statt der veralteten window-Eigenschaft
    final mediaQueryData = MediaQueryData.fromView(View.of(context));
    final bottomInset = mediaQueryData.viewInsets.bottom;
    final newKeyboardVisible = bottomInset > 0.0;

    // Aktualisiere den Zustand nur, wenn sich etwas geändert hat
    if (_isKeyboardVisible != newKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = newKeyboardVisible;
      });
    }
  }

  // Methode zum Stoppen des CAPTCHA-Überprüfungstimers
  void _stopCaptchaCheckTimer() {
    // Keine Timer-Implementierung nötig, da wir Future.delayed verwenden
    _log.d("CAPTCHA-Überprüfungstimer gestoppt");
  }

  // Methode zum Prüfen auf CAPTCHA
  Future<bool> _checkForCaptcha() async {
    if (!mounted || _showExternalLinkInfo) return false;

    _log.i("Prüfe auf CAPTCHA...");
    bool captchaDetected = false;

    try {
      // Extrahiere Text aus der WebView
      final String? extractedText = await _extractTextFromWebView();
      if (extractedText == null || extractedText.isEmpty) {
        _log.w("Konnte keinen Text aus der WebView extrahieren");
        return false;
      }

      // Prüfe auf CAPTCHA-Hinweise im Text
      if (extractedText.toLowerCase().contains("captcha") ||
          extractedText.toLowerCase().contains("sicherheitsabfrage") ||
          extractedText.toLowerCase().contains(
            "bestätigen sie, dass sie kein roboter sind",
          )) {
        _log.i("CAPTCHA im Text erkannt");
        captchaDetected = true;
      }

      // Erweiterte Prüfung via JavaScript mit direkter Erkennung und Lokalisierung
      final result = await _controller.runJavaScriptReturningResult('''
        (function() {
          // Erweiterte Liste von CAPTCHA-Selektoren
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]',
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]',
            // Zusätzliche Selektoren für Arbeitsagentur-spezifische CAPTCHAs
            '.captcha-container',
            '.captcha-image',
            '.captcha-input'
          ];

          // Suche nach CAPTCHA-Elementen
          for (const selector of captchaSelectors) {
            try {
              const elements = document.querySelectorAll(selector);
              if (elements.length > 0) {
                return true; // CAPTCHA gefunden
              }
            } catch (e) {
              console.error('Fehler bei Selector: ' + selector, e);
            }
          }

          // Prüfe auf Text, der auf CAPTCHA hinweist
          const bodyText = document.body.innerText || document.body.textContent || '';
          const captchaTerms = ['captcha', 'sicherheitsabfrage', 'security check', 'bot check'];
          for (const term of captchaTerms) {
            if (bodyText.toLowerCase().includes(term)) {
              return true; // CAPTCHA-Text gefunden
            }
          }

          return false; // Kein CAPTCHA gefunden
        })();
      ''');

      // Prüfe das JavaScript-Ergebnis
      if (result.toString().toLowerCase() == 'true') {
        _log.i("CAPTCHA via JavaScript erkannt");
        captchaDetected = true;
      }

      // Wenn ein CAPTCHA erkannt wurde, aktualisiere den State und scrolle sofort dorthin
      if (captchaDetected && mounted) {
        setState(() {
          _captchaDetected = true;
          _captchaSolved = false; // Setze explizit auf nicht gelöst
        });

        // Direkt zum CAPTCHA scrollen und animieren
        if (!_showExternalLinkInfo) {
          await Future.delayed(const Duration(milliseconds: 100));
          await _scrollToCaptchaAndHighlight();

          // Starte die Überprüfung, ob das CAPTCHA gelöst wurde
          _checkIfCaptchaSolved();
        }
      } else if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved =
              true; // Wenn kein CAPTCHA erkannt wurde, gilt es als gelöst
        });
      }

      return captchaDetected;
    } catch (e, stackTrace) {
      _log.e("Fehler bei CAPTCHA-Prüfung", error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // Überprüft, ob das CAPTCHA gelöst wurde
  Future<void> _checkIfCaptchaSolved() async {
    if (!mounted || _showExternalLinkInfo) return;

    _log.i("Überprüfe, ob CAPTCHA gelöst wurde...");

    try {
      // Extrahiere Text aus der WebView
      final String? extractedText = await _extractTextFromWebView();
      if (extractedText == null || extractedText.isEmpty) return;

      // Prüfe, ob CAPTCHA-Hinweise noch vorhanden sind
      bool captchaStillPresent =
          extractedText.toLowerCase().contains(
            "sicherheitsgründen keine kontaktdaten",
          ) ||
          extractedText.toLowerCase().contains(
            "lösen sie bitte die sicherheitsfrage",
          ) ||
          extractedText.toLowerCase().contains(
            "lösen sie die sicherheitsfrage",
          ) ||
          extractedText.toLowerCase().contains(
            "geben sie die dargestellten zeichen",
          ) ||
          extractedText.toLowerCase().contains(
            "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
          );

      // Prüfe, ob eine E-Mail-Adresse im Text gefunden werden kann
      final String? extractedEmail = _findEmailInText(extractedText);

      // Wenn eine E-Mail gefunden wurde oder keine CAPTCHA-Hinweise mehr vorhanden sind,
      // gilt das CAPTCHA als gelöst
      if ((extractedEmail != null && extractedEmail.isNotEmpty) ||
          !captchaStillPresent) {
        _log.i(
          "CAPTCHA wurde gelöst! E-Mail gefunden: ${extractedEmail ?? 'Keine'}",
        );
        if (mounted) {
          setState(() {
            _captchaSolved = true;
          });
        }
        return;
      }

      // Zusätzliche JavaScript-Prüfung, ob CAPTCHA-Elemente noch sichtbar sind
      final bool captchaElementsStillVisible =
          await _controller.runJavaScriptReturningResult('''
        (function() {
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]',
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]'
          ];

          // Prüfe, ob eines der CAPTCHA-Elemente noch sichtbar ist
          for (const selector of captchaSelectors) {
            try {
              const elements = document.querySelectorAll(selector);
              for (const element of elements) {
                // Prüfe, ob das Element sichtbar ist
                const style = window.getComputedStyle(element);
                if (style.display !== 'none' && style.visibility !== 'hidden' && element.offsetParent !== null) {
                  return true; // CAPTCHA-Element ist noch sichtbar
                }
              }
            } catch (e) {
              console.error('Fehler bei Selector: ' + selector, e);
            }
          }

          return false; // Keine sichtbaren CAPTCHA-Elemente gefunden
        })();
      ''')
              as bool? ??
          false;

      // Wenn keine CAPTCHA-Elemente mehr sichtbar sind, gilt das CAPTCHA als gelöst
      if (!captchaElementsStillVisible) {
        _log.i(
          "CAPTCHA wurde gelöst! Keine sichtbaren CAPTCHA-Elemente mehr gefunden.",
        );
        if (mounted) {
          setState(() {
            _captchaSolved = true;
          });
        }
        return;
      }

      // CAPTCHA ist noch nicht gelöst, plane eine erneute Überprüfung
      if (mounted && !_captchaSolved) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted && !_captchaSolved) {
            _checkIfCaptchaSolved();
          }
        });
      }
    } catch (e, stackTrace) {
      _log.e(
        "Fehler bei CAPTCHA-Überprüfung",
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // Methode zum Scrollen zum CAPTCHA und Hervorheben
  Future<void> _scrollToCaptchaAndHighlight() async {
    if (!mounted || _showExternalLinkInfo) return;

    _log.i("Scrolle zum CAPTCHA und hebe es hervor...");

    try {
      // Führe das JavaScript aus, um das Captcha zu finden und hervorzuheben
      await _controller.runJavaScript('''
        (function() {
          // Entferne zuerst alle vorherigen Hervorhebungen
          const oldStyles = document.querySelectorAll('style[data-captcha-style]');
          oldStyles.forEach(style => style.remove());

          const oldOverlays = document.querySelectorAll('.captcha-overlay, .captcha-spotlight');
          oldOverlays.forEach(overlay => overlay.remove());

          // Suche nach Captcha-Elementen mit erweiterten Selektoren
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]',
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]',
            // Spezifische Selektoren für Bundesagentur für Arbeit
            'h2:contains("Sicherheitsabfrage")',
            'div:contains("Sicherheitsabfrage")',
            'input[type="text"]',
            'img[alt*="Sicherheitsabfrage"]',
            'img[alt*="Captcha"]',
            'button:contains("Absenden")'
          ];

          // Füge CSS für die Hervorhebung hinzu
          const style = document.createElement('style');
          style.setAttribute('data-captcha-style', 'true');
          style.textContent = `
            .captcha-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-color: rgba(0, 0, 0, 0.7);
              z-index: 9998;
              animation: fadeIn 0.5s ease-in-out;
            }

            .captcha-spotlight {
              position: absolute;
              box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
              z-index: 9999;
              border-radius: 8px;
              animation: pulse 2s infinite;
            }

            @keyframes fadeIn {
              from { opacity: 0; }
              to { opacity: 1; }
            }

            @keyframes pulse {
              0% { box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7), 0 0 10px 3px rgba(255, 105, 180, 0.7); }
              50% { box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7), 0 0 20px 6px rgba(255, 215, 0, 0.7); }
              100% { box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7), 0 0 10px 3px rgba(255, 105, 180, 0.7); }
            }
          `;
          document.head.appendChild(style);

          // Suche nach Captcha-Elementen
          let captchaElement = null;

          for (const selector of captchaSelectors) {
            try {
              const elements = document.querySelectorAll(selector);
              if (elements.length > 0) {
                // Verwende das erste gefundene Element
                captchaElement = elements[0];
                break;
              }
            } catch (e) {
              console.error('Fehler bei Selector: ' + selector, e);
            }
          }

          if (captchaElement) {
            // Scrolle zum Captcha-Element
            captchaElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Erstelle Spotlight-Effekt
            const rect = captchaElement.getBoundingClientRect();
            const spotlight = document.createElement('div');
            spotlight.className = 'captcha-spotlight';
            spotlight.style.top = rect.top + 'px';
            spotlight.style.left = rect.left + 'px';
            spotlight.style.width = rect.width + 'px';
            spotlight.style.height = rect.height + 'px';
            document.body.appendChild(spotlight);

            // Entferne die Hervorhebung nach 5 Sekunden
            setTimeout(() => {
              const overlays = document.querySelectorAll('.captcha-overlay, .captcha-spotlight');
              overlays.forEach(overlay => overlay.remove());
            }, 5000);

            return true;
          }

          return false;
        })();
      ''');

      _log.i("CAPTCHA-Hervorhebung und Scroll ausgeführt");
    } catch (e, stackTrace) {
      _log.e(
        "Fehler beim Scrollen zum CAPTCHA",
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // Hilfsmethode zum Extrahieren von Text aus der WebView
  Future<String?> _extractTextFromWebView() async {
    try {
      final String result =
          await _controller.runJavaScriptReturningResult('''
        (function() {
          return document.body.innerText || document.body.textContent || '';
        })();
      ''')
              as String? ??
          '';

      return result;
    } catch (e, stackTrace) {
      _log.e(
        "Fehler beim Extrahieren von Text aus der WebView",
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // Hilfsmethode zum Finden einer E-Mail-Adresse im Text
  String? _findEmailInText(String text) {
    // Wenn das CAPTCHA nicht gelöst ist, sollte der Text keine E-Mail-Adresse enthalten
    // Wir prüfen, ob bestimmte Hinweise auf ein ungelöstes CAPTCHA vorhanden sind
    if (text.toLowerCase().contains("sicherheitsgründen keine kontaktdaten") ||
        text.toLowerCase().contains("lösen sie bitte die sicherheitsfrage") ||
        text.toLowerCase().contains("lösen sie die sicherheitsfrage") ||
        text.toLowerCase().contains("geben sie die dargestellten zeichen") ||
        text.toLowerCase().contains(
          "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
        )) {
      _log.i(
        "CAPTCHA-Hinweis im Text gefunden, E-Mail-Suche wird übersprungen",
      );
      return null; // CAPTCHA ist definitiv nicht gelöst
    }

    // Regulärer Ausdruck für E-Mail-Adressen
    final RegExp emailRegex = RegExp(
      r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
    );

    // Suche nach E-Mail-Adressen im Text
    final Iterable<RegExpMatch> matches = emailRegex.allMatches(text);

    if (matches.isNotEmpty) {
      // Erste gefundene E-Mail-Adresse zurückgeben
      return matches.first.group(0);
    }

    return null;
  }

  // Methode zum Teilen der aktuellen URL
  void _shareUrl(BuildContext context) {
    // Zeige sofort eine Meldung an, ohne auf die URL zu warten
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Teilen-Funktion noch nicht implementiert')),
    );

    // Optional: Hole die URL im Hintergrund für Logging
    _controller
        .currentUrl()
        .then((url) {
          _log.d("URL zum Teilen: $url");
        })
        .catchError((e) {
          _log.e("Fehler beim Abrufen der URL", error: e);
        });
  }

  // Methode zum Prüfen auf externe Links
  Future<void> _checkForExternalLink() async {
    if (!mounted) return;

    try {
      // Hole die aktuelle URL
      final String currentUrl = await _controller.currentUrl() ?? '';
      _log.d('BANNER_DEBUG: _checkForExternalLink - Aktuelle URL: $currentUrl');

      // Prüfe, ob wir auf einer externen Seite sind (nicht arbeitsagentur.de)
      bool isExternalUrl = !currentUrl.contains('arbeitsagentur.de');

      if (isExternalUrl) {
        _log.i('BANNER_DEBUG: Externe URL erkannt: $currentUrl');
        if (mounted) {
          setState(() {
            _showExternalLinkInfo = true;
            _captchaDetected = false;
            _captchaSolved = true; // Auf externen Seiten gibt es kein CAPTCHA
          });
        }
      } else {
        _log.d('BANNER_DEBUG: Interne URL erkannt: $currentUrl');
        if (mounted) {
          setState(() {
            _showExternalLinkInfo = false;
          });
        }
      }
    } catch (e, stackTrace) {
      _log.e(
        'BANNER_DEBUG: Fehler bei _checkForExternalLink',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      // Flag zurücksetzen
      _externalLinkCheckRunning = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.jobTitle,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          // Favoriten-Button, wenn JobEntity vorhanden ist
          if (widget.jobEntity != null)
            Consumer(
              builder: (context, ref, child) {
                final favoritesAsyncValue = ref.watch(favoritesProvider);
                final favorites = favoritesAsyncValue.asData?.value ?? [];
                final isFavorite = favorites.any(
                  (job) => job.id == widget.jobEntity!.id,
                );

                return IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : null,
                  ),
                  onPressed: () {
                    // Verwende die korrekten Methoden für den FavoritesNotifier
                    if (isFavorite) {
                      ref
                          .read(favoritesProvider.notifier)
                          .removeFavorite(widget.jobEntity!.id);
                    } else {
                      ref
                          .read(favoritesProvider.notifier)
                          .addFavorite(widget.jobEntity!);
                    }
                  },
                );
              },
            ),
        ],
      ),
      body: Column(
        children: [
          // WebView nimmt den größten Teil des Bildschirms ein
          Expanded(
            child: Stack(
              children: [
                WebViewWidget(controller: _controller),

                // Ladeindikator
                if (_isLoadingPage) Center(child: CircularProgressIndicator()),

                // Hinweis für externe Links
                if (_showExternalLinkInfo)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      color: Colors.black.withAlpha(204), // 0.8 Opazität
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.white),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Sie befinden sich auf einer externen Seite.',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Footer mit Buttons
          Container(
            key: _footerKey,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 Opazität
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // KI-Anschreiben-Button mit Animation
                _buildAnimatedButton(context),

                SizedBox(height: 12),

                // Premium-Button mit Animation
                _buildPremiumButton(context),

                SizedBox(height: 12),

                // Weitere Buttons
                Row(
                  children: [
                    // Zurück-Button
                    if (_canGoBack)
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: Icon(Icons.arrow_back),
                          label: Text('Zurück'),
                          onPressed: () {
                            _controller.goBack();
                          },
                        ),
                      ),

                    if (_canGoBack) SizedBox(width: 8),

                    // Teilen-Button
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.share),
                        label: Text('Teilen'),
                        onPressed: () {
                          // Verwende eine lokale Funktion, um den BuildContext-Fehler zu vermeiden
                          _shareUrl(context);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

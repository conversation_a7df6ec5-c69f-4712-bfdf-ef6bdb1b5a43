import 'package:flutter/material.dart';
import 'package:ki_test/src/presentation/common/widgets/animation_variants.dart';
import 'package:ki_test/src/presentation/common/widgets/advanced_animations.dart';
import 'package:ki_test/src/presentation/common/widgets/application_generation_animation.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Demo-Screen zum Testen und Auswählen verschiedener Animationsstile
class AnimationSelectionDemoScreen extends StatefulWidget {
  const AnimationSelectionDemoScreen({super.key});

  @override
  State<AnimationSelectionDemoScreen> createState() => _AnimationSelectionDemoScreenState();
}

class _AnimationSelectionDemoScreenState extends State<AnimationSelectionDemoScreen> {
  bool _isGenerating = false;
  int _selectedAnimationIndex = 0;
  
  // Liste der verfügbaren Animationen
  final List<Map<String, dynamic>> _animations = [
    {
      'name': 'Bewerbungsgenerierung',
      'widget': (Color color) => ApplicationGenerationAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Moderne Partikel',
      'widget': (Color color) => ModernParticleAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Minimalistischer Ladekreis',
      'widget': (Color color) => MinimalistLoadingAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Neon-Animation',
      'widget': (Color color) => NeonAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Gradient Wellen',
      'widget': (Color color) => GradientWaveAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Dynamische Punkte',
      'widget': (Color color) => DynamicDotsAnimation(color: color, size: 24.0),
    },
    {
      'name': 'Animation 1',
      'widget': (Color color) => AnimationVariant1(color: color, size: 24.0),
    },
    {
      'name': 'Animation 2',
      'widget': (Color color) => AnimationVariant2(color: color, size: 24.0),
    },
    {
      'name': 'Animation 3',
      'widget': (Color color) => AnimationVariant3(color: color, size: 24.0),
    },
    {
      'name': 'Animation 4',
      'widget': (Color color) => AnimationVariant4(color: color, size: 24.0),
    },
    {
      'name': 'Animation 5',
      'widget': (Color color) => AnimationVariant5(color: color, size: 24.0),
    },
  ];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Animation Auswahl'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Wählen Sie einen Animationsstil für die Bewerbungsgenerierung:',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              
              // Animationsauswahl
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _animations.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 12.0),
                      child: _buildAnimationOption(index),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Generierungsbutton
              SizedBox(
                width: double.infinity,
                child: _isGenerating
                    ? _buildProcessingButton()
                    : _buildNormalButton(),
              ),
              
              const SizedBox(height: 32),
              
              // Erklärung
              const Text(
                'Funktionsweise:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem('Wählen Sie einen Animationsstil aus den Optionen oben'),
              _buildFeatureItem('Klicken Sie auf "Bewerbung generieren", um die Animation zu sehen'),
              _buildFeatureItem('Die Animation wird für 10 Sekunden angezeigt'),
              _buildFeatureItem('Testen Sie verschiedene Stile, um den besten zu finden'),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildAnimationOption(int index) {
    final isSelected = index == _selectedAnimationIndex;
    final animation = _animations[index];
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedAnimationIndex = index;
        });
      },
      child: Container(
        width: 100,
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 40,
              width: 40,
              child: animation['widget'](
                isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              animation['name'],
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 20),
          const SizedBox(width: 8),
          Expanded(child: Text(text, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }
  
  // Button für den Verarbeitungszustand
  Widget _buildProcessingButton() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Dynamisch ausgewählte Animation
          _animations[_selectedAnimationIndex]['widget'](
            Theme.of(context).colorScheme.onPrimary,
          ),
          const SizedBox(width: 12),
          // Text
          Text(
            'Generiere Bewerbung...',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Normaler Button
  Widget _buildNormalButton() {
    return ElevatedButton.icon(
      icon: const Icon(Icons.auto_awesome, size: 20),
      label: const Text('Bewerbung generieren'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 14),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      onPressed: () {
        setState(() {
          _isGenerating = true;
        });
        
        // Simuliere den Generierungsprozess
        Future.delayed(const Duration(seconds: 10), () {
          if (mounted) {
            setState(() {
              _isGenerating = false;
            });
            
            // Zeige eine Erfolgsmeldung an
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Bewerbung mit ${_animations[_selectedAnimationIndex]['name']} erfolgreich generiert!'),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      },
    );
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ki_test/src/application/providers/favorites_provider.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/applied_jobs_provider.dart';
import 'package:ki_test/src/application/providers/additional_documents_provider.dart';
import 'package:ki_test/src/application/services/email_intent_tracker.dart';
import 'package:path_provider/path_provider.dart';

import 'package:ki_test/src/core/config/deepseek_prompts.dart';
import 'package:ki_test/src/core/config/mistral_prompts.dart'; // Import für Mistral-Prompts
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/utils/cv_storage_helper.dart'; // Import für CV Storage Helper
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/services.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/gestures.dart'; // Für gestureRecognizers
import 'package:ki_test/src/presentation/premium/screens/premium_screen.dart';
import 'package:ki_test/src/presentation/common/widgets/simple_circle_animation.dart';
import 'package:ki_test/src/presentation/common/widgets/remaining_applications_widget.dart';
import 'package:ki_test/src/core/optimization/webview_performance_optimizer.dart';

// Enum für die Modellauswahl entfernt
// enum AiModel { deepseek, gemini }

// Kein Provider mehr für Details nötig
// final jobDetailProvider = StateProvider<AsyncValue<JobEntity?>>((ref) => const AsyncValue.loading());

// Wird zu einem ConsumerStatefulWidget (StatefulWidget wegen AnimationController)
class JobDetailScreen extends ConsumerStatefulWidget {
  // StatefulWidget für WebView Controller
  final String jobRefnr;
  final String jobTitle;
  final String? sourceUrl;
  final JobEntity? jobEntity; // NEU: Optionale JobEntity für Metadaten

  const JobDetailScreen({
    super.key,
    required this.jobRefnr,
    required this.jobTitle,
    this.sourceUrl,
    this.jobEntity, // NEU
  });

  @override
  ConsumerState<JobDetailScreen> createState() => _JobDetailScreenState();
}

// Nutze ConsumerState
class _JobDetailScreenState extends ConsumerState<JobDetailScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late final WebViewController _controller;
  bool _isLoadingPage = true; // Ladezustand für die Seite
  bool _isExtracting = false; // Ladezustand für Text-Extraktion/KI
  bool _isGenerating = false; // Ladezustand für KI-Anschreiben-Generierung
  bool _hasPageError = false; // Fehlerzustand für Seite
  bool _captchaDetected = false; // Flag für erkanntes Captcha
  bool _isDownloadingCv = false; // Neuer Zustand für CV-Download
  bool _captchaSolved = false; // Flag für gelöstes Captcha
  String _extractedJobText =
      ''; // Variable zum Speichern des extrahierten Texts
  final String _jobUrl = ''; // URL zum Job

  // Übersetzungsfunktion wurde entfernt

  // NEU: Zustandsvariablen für WebView-Navigation und Hinweis
  bool _canGoBack = false;
  bool _showExternalLinkInfo = false;

  // Variable für Fokus-Tracking des Hinweise-Textfelds
  bool _isHintsTextFieldFocused = false;
  // NEU: Variable, die anzeigt, ob die Tastatur sichtbar ist
  bool _isKeyboardVisible = false;
  final FocusNode _hintsFocusNode = FocusNode();

  // Wieder hinzufügen: Animation Controller für Button-Effekt
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonOpacityAnimation;
  // *** NEU: Zweiter Controller für Hintergrund-Animation ***
  late AnimationController _gradientSweepController;

  // Übersetzungsvariable entfernt

  final TextEditingController _hintsController =
      TextEditingController(); // Hinzugefügt
  final _log = getLogger('JobDetailScreen'); // Logger Instanz

  // NEU: Zustand für Werbeanzeige-Laden
  bool _isLoadingAd = false;
  // *** NEU: Zustand für erfolgreiche Generierung ***
  bool _generationCompletedSuccessfully = false;

  // Zeitpunkt, an dem die erste Werbung gestartet wurde
  DateTime? _firstAdStartTime;

  // Zeitpunkt, an dem die erste Werbung beendet wurde
  DateTime? _firstAdEndTime;

  // Ausgewähltes KI-Modell für die Bewerbungsgenerierung
  String? _selectedModelType;

  // *** NEU: GlobalKey und State für Footer-Höhe ***
  final GlobalKey _footerKey = GlobalKey();
  double _footerHeight = 150.0; // Initialer Schätzwert, wird überschrieben

  // *** NEU: Flag, um mehrfache Prüfung zu verhindern ***
  bool _externalLinkCheckRunning = false;

  // Performance-Optimierung: Debouncing für CAPTCHA-Checks
  Timer? _captchaCheckTimer;
  final bool _captchaCheckRunning = false;

  @override
  void initState() {
    super.initState();
    // Registriere diesen State als WidgetsBindingObserver
    WidgetsBinding.instance.addObserver(this);

    // SystemUI-Farben werden in didChangeDependencies gesetzt, nicht hier

    // Prüfen, ob das JobEntity eine sourceUrl hat und diese verwenden
    final String url =
        widget.jobEntity?.sourceUrl ??
        'https://www.arbeitsagentur.de/jobsuche/jobdetail/${widget.jobRefnr}';
    print("Initialisiere JobDetailScreen mit URL: $url");

    // PERFORMANCE FIX: Verwende optimierten WebViewController
    _controller = WebViewPerformanceOptimizer.createOptimizedController(
      controllerId: 'job_detail_${widget.jobRefnr}',
      // Hintergrund auf Weiß setzen, um GPU-Overdraw zu reduzieren
      backgroundColor: Colors.white,
      onProgress: (int progress) {
        _log.d("WebView loading progress: $progress%");
      },
      onPageStarted: (String url) {
        _log.i("WebView started loading: $url");
        setState(() {
          _isLoadingPage = true;
          _hasPageError = false;
          _log.d(
            'BANNER_DEBUG: onPageStarted - Setze _showExternalLinkInfo auf false.',
          );
          _showExternalLinkInfo = false; // Reset bei neuer Seite
          _externalLinkCheckRunning = false; // *** Flag zurücksetzen ***
        });
      },
      onPageFinished: (url) async {
        if (!mounted) return;
        _log.d('BANNER_DEBUG: onPageFinished - Seite geladen: $url');
        setState(() {
          _isLoadingPage = false;
        });

        // Prüfe, ob wir auf einer externen Seite sind (nicht arbeitsagentur.de)
        bool isExternalUrl = !url.contains('arbeitsagentur.de');

        if (isExternalUrl) {
          _log.i(
            "Externe Seite erkannt, CAPTCHA-Prüfung wird vollständig übersprungen",
          );
          // Setze den CAPTCHA-Status explizit
          if (mounted) {
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
              // _showExternalLinkInfo = true; // ENTFERNT: MaterialBanner nicht mehr benötigt
            });
          }
        } else {
          // Optional: Prüfe nach Captcha, wenn Seite fertig geladen ist und wir auf arbeitsagentur.de sind
          _checkForCaptcha();
        }

        await Future.delayed(
          const Duration(milliseconds: 1200),
        ); // 1200ms warten
        if (!mounted) return; // Erneut prüfen, da Delay

        // DEAKTIVIERT: Funktion zur Erkennung unvollständiger Jobbeschreibungen
        // Diese Funktion verursacht RenderFlex Overflow-Probleme während des Ladens
        /*
                if (_externalLinkCheckRunning) {
                  _log.d(
                    'BANNER_DEBUG: onPageFinished - _checkForExternalLink läuft bereits, überspringe erneuten Aufruf.',
                  );
                  return;
                }
                _externalLinkCheckRunning = true; // *** Flag setzen ***
                _log.d(
                  'BANNER_DEBUG: onPageFinished - Rufe _checkForExternalLink nach Delay auf...',
                );

                await _checkForExternalLink(); // Führe die Prüfung durch
                _log.d(
                  'BANNER_DEBUG: onPageFinished - _checkForExternalLink abgeschlossen.',
                );
                // _externalLinkCheckRunning wird in _checkForExternalLink zurückgesetzt
                */

        _log.d(
          'BANNER_DEBUG: _checkForExternalLink ist deaktiviert - RenderFlex Overflow Fix',
        );

        // NEU: WebView kann zurückgehen?
        _controller.canGoBack().then((canGoBack) {
          if (mounted && _canGoBack != canGoBack) {
            setState(() {
              _canGoBack = canGoBack;
            });
          }
        });
      },
      onWebResourceError: (WebResourceError error) {
        _log.e("WebView error: ${error.description}", error: error);
        setState(() {
          _isLoadingPage = false; // Ladeindikator stoppen bei Fehler
        });
        // Snackbar entfernt - störend bei externen Seiten mit net::ERR_FAILED
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text(
        //       'Fehler beim Laden der Seite: ${error.description}',
        //     ),
        //   ),
        // );
      },
    );

    // Lade die URL nach der Konfiguration
    _controller.loadRequest(Uri.parse(url));

    // Wieder hinzufügen: Animation Controller initialisieren
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 1200,
      ), // Langsamer für flüssigere Animation
    );
    _buttonOpacityAnimation = Tween<double>(begin: 1.0, end: 0.4).animate(
      CurvedAnimation(
        parent: _buttonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // *** NEU: Zweiten Controller initialisieren ***
    _gradientSweepController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 2500,
      ), // Deutlich langsamer für flüssigere Animation
    );

    // FocusNode Listener hinzufügen
    _hintsFocusNode.addListener(() {
      setState(() {
        _isHintsTextFieldFocused = _hintsFocusNode.hasFocus;
      });
    });

    // *** NEU: Höhe des Footers nach dem ersten Frame messen ***
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_footerKey.currentContext != null) {
        final RenderBox renderBox =
            _footerKey.currentContext!.findRenderObject() as RenderBox;
        if (mounted && _footerHeight != renderBox.size.height) {
          setState(() {
            _footerHeight = renderBox.size.height;
            _log.d("FOOTER_DEBUG: Gemessene Footer-Höhe: $_footerHeight");
          });
        }
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // SystemUI-Farben hier setzen, wo Theme.of(context) sicher verfügbar ist
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
        // Zusätzliche Eigenschaften für bessere Sichtbarkeit
        systemNavigationBarContrastEnforced: false,
      ),
    );

    // Aktualisiere die Footer-Höhe, wenn sich die Abhängigkeiten ändern
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_footerKey.currentContext != null) {
        final RenderBox renderBox =
            _footerKey.currentContext!.findRenderObject() as RenderBox;
        if (mounted && _footerHeight != renderBox.size.height) {
          setState(() {
            _footerHeight = renderBox.size.height;
            _log.d("FOOTER_DEBUG: Aktualisierte Footer-Höhe: $_footerHeight");
          });
        }
      }
    });
  }

  @override
  void dispose() {
    // WICHTIG: WebView-Storage NICHT leeren, um Session/Cookies (CAPTCHA gelöst) zu behalten
    // Zuvor wurden hier clearCache()/clearLocalStorage() aufgerufen, was bei jedem
    // Verlassen der Seite die Arbeitsagentur-Session zurückgesetzt hat und erneut CAPTCHA auslöste.

    // Entferne Observer
    WidgetsBinding.instance.removeObserver(this);

    // Setze SystemUI-Farben zurück
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarContrastEnforced: false,
      ),
    ); // Zurück zum Standard

    _buttonAnimationController.dispose(); // Controller entsorgen
    // *** NEU: Zweiten Controller entsorgen ***
    _gradientSweepController.dispose();
    _hintsController.dispose(); // Hinzugefügt
    _hintsFocusNode.dispose(); // FocusNode entsorgen
    // Performance-Optimierung: Timer bereinigen
    _captchaCheckTimer?.cancel();
    // _selectedAiModel.dispose(); // Entfernt
    super.dispose();
  }

  // NEU: Implementiere didChangeMetrics aus WidgetsBindingObserver, um die Tastatur zu überwachen
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // Berechne, ob die Tastatur sichtbar ist
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final newKeyboardVisible = bottomInset > 0.0;

    // Aktualisiere den Zustand nur, wenn sich etwas geändert hat
    if (_isKeyboardVisible != newKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = newKeyboardVisible;
      });
    }
  }

  // --- Funktion zum Extrahieren von Text ---
  Future<String?> _extractTextFromWebView({bool isExternalPage = false}) async {
    // Wenn wir auf einer externen Seite sind, setzen wir den CAPTCHA-Status explizit
    if (isExternalPage || _showExternalLinkInfo) {
      if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }
      _log.i(
        "Externe Seite erkannt in _extractTextFromWebView, CAPTCHA-Status gesetzt",
      );
    }

    try {
      // Führe JavaScript aus, um den innerText des Body zu bekommen
      // Wichtig: `evaluateJavascript` wurde durch `runJavaScriptReturningResult` ersetzt
      final result = await _controller.runJavaScriptReturningResult(
        isExternalPage || _showExternalLinkInfo
            ? '''
            (function() {
              // Für externe Seiten: Versuche, den relevanten Inhalt zu extrahieren
              // Suche nach typischen Job-Beschreibungs-Containern
              const jobContainers = document.querySelectorAll('.job-description, .job-details, .job-content, article, main, [role="main"], .content, #content, .description');

              if (jobContainers.length > 0) {
                // Verwende den ersten gefundenen Container
                return jobContainers[0].innerText || jobContainers[0].textContent || document.body.innerText;
              }

              // Fallback: Gesamten Body-Text verwenden
              return document.body.innerText;
            })()
          '''
            : "document.body.innerText",
      );

      // Das Ergebnis ist oft ein String mit Anführungszeichen, diese entfernen
      if (result is String) {
        // Einfache Bereinigung (kann verbessert werden)
        String text = result;
        if (text.startsWith('"') && text.endsWith('"')) {
          text = text.substring(1, text.length - 1);
        }
        // Newlines und Tabs etc. bereinigen
        text = text.replaceAll('\\n', '\n').replaceAll('\\t', '\t').trim();

        // Wenn wir auf einer externen Seite sind, überspringen wir die CAPTCHA-Erkennung
        if (!isExternalPage && !_showExternalLinkInfo) {
          // Prüfen, ob möglicherweise ein Captcha vorhanden ist
          if (text.toLowerCase().contains("captcha") ||
              text.toLowerCase().contains("sicherheitsabfrage") ||
              text.toLowerCase().contains(
                "bestätigen sie, dass sie kein roboter sind",
              )) {
            _log.i(
              "Captcha erkannt: Möglicherweise werden nicht alle Informationen extrahiert.",
            );
            // Speichern wir für spätere Nutzung
            if (mounted) {
              setState(() {
                _captchaDetected = true;
              });
            }
          } else {
            if (mounted) {
              setState(() {
                _captchaDetected = false;
              });
            }
          }
        } else {
          // Auf externen Seiten setzen wir _captchaDetected immer auf false und _captchaSolved auf true
          if (mounted) {
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
          }
          _log.i(
            "Externe Seite: CAPTCHA-Erkennung übersprungen und Status gesetzt",
          );
        }

        return text;
      }
      return null; // Falls Ergebnis kein String
    } catch (e) {
      _log.e("Fehler beim Extrahieren von Text via JS: $e");
      return null;
    }
  }

  // VERBESSERT: Funktion zum Prüfen auf externe Links und fehlende Jobbeschreibungen
  Future<void> _checkForExternalLink() async {
    if (!mounted) return;
    _log.d(
      "BANNER_DEBUG: _checkForExternalLink - Starte Prüfung auf externe Links und fehlende Jobbeschreibungen...",
    );

    try {
      // JavaScript, das nur nach dem exakten Text "externe seite öffnen" sucht
      final result = await _controller.runJavaScriptReturningResult('''
        (function() {
          // Nur ein Keyword: "externe seite öffnen"
          const keyword = 'externe seite öffnen';

          // Suche in mehr Element-Typen (Buttons, Links, und Elemente mit Klick-Handlern oder Link-Rollen)
          const potentialLinks = Array.from(document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]'));

          let externalLinkFound = false;
          let externalLinkElement = null;
          let externalLinkKeyword = '';

          // Prüfe auf externe Links mit exakter Übereinstimmung
          for (let i = 0; i < potentialLinks.length; i++) {
            // Versuche, den sichtbaren Text zu bekommen (innerText ist oft besser als textContent)
            const text = (potentialLinks[i].innerText || potentialLinks[i].textContent || '').toLowerCase().trim();
            if (!text) continue; // Überspringe Elemente ohne Text

            // Nur exakte Übereinstimmung mit "externe seite öffnen"
            if (text === keyword) {
              // Gib das gefundene Keyword und den Element-Typ zurück für Debugging
              console.log('BANNER_DEBUG: Externer Link Hinweis gefunden: "' + keyword + '" in Element: ' + potentialLinks[i].tagName);
              externalLinkFound = true;
              externalLinkElement = potentialLinks[i];
              externalLinkKeyword = keyword;
              break;
            }
          }

          // Prüfe auf fehlende Jobbeschreibung
          let hasMinimalContent = false;

          // 1. Prüfe Textlänge im Hauptinhalt
          const mainContent = document.querySelector('.ba-jobd-content') || document.body;
          const contentText = mainContent.innerText || '';

          // 2. Suche nach typischen Abschnitten einer Jobbeschreibung - KORRIGIERT: Verwende einfachere Selektoren
          const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6, .section-heading, .heading');
          let hasJobDescriptionSection = false;
          let hasRequirementsSection = false;

          // Durchsuche alle Überschriften nach relevanten Begriffen
          headings.forEach(heading => {
            const headingText = heading.innerText.toLowerCase();
            if (headingText.includes('stellenbeschreibung') ||
                headingText.includes('tätigkeitsbeschreibung') ||
                headingText.includes('aufgaben') ||
                headingText.includes('job description')) {
              hasJobDescriptionSection = true;
            }
            if (headingText.includes('anforderungen') ||
                headingText.includes('qualifikation') ||
                headingText.includes('profil') ||
                headingText.includes('requirements')) {
              hasRequirementsSection = true;
            }
          });

          // 3. Prüfe, ob die Seite hauptsächlich aus einem Link zu einer externen Seite besteht
          // Wenn der Inhalt sehr kurz ist oder keine typischen Abschnitte gefunden wurden
          hasMinimalContent = contentText.length < 500 || (!hasJobDescriptionSection && !hasRequirementsSection);

          // Hervorhebe den externen Link mit auffälliger Animation, falls gefunden
          if (externalLinkElement) {
            try {
              // Füge CSS-Animation für den Button hinzu
              const animationStyle = document.createElement('style');
              animationStyle.textContent = `
                @keyframes pulseButton {
                  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                  50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
                }

                @keyframes glowBorder {
                  0% { border-color: #FFC107; }
                  50% { border-color: #FF5722; }
                  100% { border-color: #FFC107; }
                }

                .external-link-highlight {
                  background-color: #FFEB3B !important;
                  border: 3px solid #FFC107 !important;
                  padding: 8px 16px !important;
                  border-radius: 4px !important;
                  box-shadow: 0 0 15px rgba(255, 193, 7, 0.8) !important;
                  position: relative !important;
                  animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                  transition: all 0.3s ease !important;
                  z-index: 9999 !important;
                  font-weight: bold !important;
                }

                .external-link-highlight:before {
                  content: "👉 " !important;
                }

                .external-link-highlight:after {
                  content: " 👈" !important;
                }

                .external-link-highlight:hover {
                  transform: scale(1.1) !important;
                  background-color: #FFC107 !important;
                }
              `;
              document.head.appendChild(animationStyle);

              // Wende die Klasse auf das Element an
              externalLinkElement.classList.add('external-link-highlight');

              // Scrolle zum Element
              externalLinkElement.scrollIntoView({behavior: 'smooth', block: 'center'});

              // Füge einen Tooltip hinzu, falls noch nicht vorhanden
              if (!externalLinkElement.title) {
                externalLinkElement.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
              }

              // Füge einen Event-Listener hinzu, um die Animation zu verstärken, wenn der Mauszeiger darüber schwebt
              externalLinkElement.addEventListener('mouseover', function() {
                this.style.transform = 'scale(1.1)';
                this.style.backgroundColor = '#FFC107';
              });

              externalLinkElement.addEventListener('mouseout', function() {
                this.style.transform = 'scale(1)';
                this.style.backgroundColor = '#FFEB3B';
              });

            } catch (e) {
              console.log('Fehler beim Hervorheben des externen Links:', e);
            }
          }

          // Stringifiziere das Ergebnis, um Probleme mit der Rückgabe zu vermeiden
          return JSON.stringify({
            externalLinkFound: externalLinkFound,
            hasMinimalContent: hasMinimalContent,
            contentLength: contentText.length,
            externalLinkKeyword: externalLinkKeyword
          });
        })();
      ''');

      _log.d(
        'BANNER_DEBUG: _checkForExternalLink - JS-Ergebnis erhalten: $result',
      );

      try {
        // Ergebnis auswerten - jetzt immer als String zurückgegeben
        Map<String, dynamic> resultMap;

        if (result is String) {
          // Versuche, das Ergebnis als JSON zu parsen
          try {
            // Entferne Anführungszeichen am Anfang und Ende, falls vorhanden
            String cleanResult = result;

            // Wenn das Ergebnis in doppelten Anführungszeichen eingeschlossen ist
            // (was bei einem JSON-String in einem String der Fall sein kann)
            if (cleanResult.startsWith('"') && cleanResult.endsWith('"')) {
              // Entferne die äußeren Anführungszeichen
              cleanResult = cleanResult.substring(1, cleanResult.length - 1);
              // Escape-Sequenzen für innere Anführungszeichen korrigieren
              cleanResult = cleanResult.replaceAll('\\"', '"');
            }

            // Versuche das JSON zu parsen
            resultMap = Map<String, dynamic>.from(jsonDecode(cleanResult));
            _log.d(
              'BANNER_DEBUG: JavaScript-Ergebnis erfolgreich als JSON geparst.',
            );
          } catch (jsonError) {
            _log.e('Fehler beim Parsen des JSON-Ergebnisses: $jsonError');

            // Zweiter Versuch mit direktem Parsen ohne Vorverarbeitung
            try {
              resultMap = Map<String, dynamic>.from(jsonDecode(result));
              _log.d(
                'BANNER_DEBUG: JSON im zweiten Versuch erfolgreich geparst.',
              );
            } catch (secondError) {
              _log.e('Auch zweiter Parse-Versuch fehlgeschlagen: $secondError');

              // Fallback-Werte verwenden
              resultMap = {
                'externalLinkFound': false,
                'hasMinimalContent': true, // Vorsichtshalber auf true setzen
                'contentLength': 0,
                'externalLinkKeyword': '',
              };
            }
          }
        } else if (result is Map) {
          // Direkte Map-Konvertierung
          resultMap = Map<String, dynamic>.from(result);
          _log.d('BANNER_DEBUG: JavaScript-Ergebnis ist bereits eine Map.');
        } else if (result is bool) {
          // Fallback für den Fall, dass das Ergebnis ein einfacher Boolean ist (alte Implementierung)
          resultMap = {
            'externalLinkFound': result,
            'hasMinimalContent': false,
            'contentLength': 0,
            'externalLinkKeyword': '',
          };
          _log.d('BANNER_DEBUG: JavaScript-Ergebnis ist ein Boolean: $result');
        } else {
          // Unbekannter Typ - Fallback
          resultMap = {
            'externalLinkFound': false,
            'hasMinimalContent': true, // Vorsichtshalber auf true setzen
            'contentLength': 0,
            'externalLinkKeyword': '',
          };
          _log.d(
            'BANNER_DEBUG: JavaScript-Ergebnis hat unbekannten Typ: ${result.runtimeType}',
          );
        }

        // Extrahiere die Werte mit Null-Checks
        final bool externalLinkFound =
            resultMap['externalLinkFound'] as bool? ?? false;
        final bool hasMinimalContent =
            resultMap['hasMinimalContent'] as bool? ?? false;
        final int contentLength = resultMap['contentLength'] as int? ?? 0;

        _log.d(
          'BANNER_DEBUG: Externer Link gefunden: $externalLinkFound, Minimaler Inhalt: $hasMinimalContent, Inhaltslänge: $contentLength',
        );

        // Zeige Warnung, wenn externer Link gefunden ODER minimaler Inhalt erkannt wurde
        if (externalLinkFound || hasMinimalContent) {
          _log.i('Externer Link oder minimaler Inhalt erkannt. Zeige Hinweis.');
          if (mounted) {
            _log.d(
              'BANNER_DEBUG: _checkForExternalLink - Rufe setState auf, um _showExternalLinkInfo auf true zu setzen.',
            );
            setState(() {
              _showExternalLinkInfo = true;
              _log.d(
                'BANNER_DEBUG: _checkForExternalLink - setState abgeschlossen. _showExternalLinkInfo ist jetzt: $_showExternalLinkInfo',
              );
            });
          }
        } else {
          _log.d('Kein externer Link oder minimaler Inhalt gefunden.');
        }
      } catch (e, stackTrace) {
        _log.e(
          'Fehler beim Verarbeiten des JavaScript-Ergebnisses: $e',
          error: e,
          stackTrace: stackTrace,
        );

        // Fallback: Bei jedem Fehler zeigen wir die Warnung an, um sicherzugehen
        _log.i('Fehler bei der Erkennung. Zeige Hinweis zur Sicherheit.');
        if (mounted) {
          setState(() {
            _showExternalLinkInfo = true;
          });
        }
      }
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Prüfen auf externen Link via JS:',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      // Flag zurücksetzen, egal ob Erfolg oder Fehler
      if (mounted) {
        _externalLinkCheckRunning = false;
        _log.d(
          'BANNER_DEBUG: _checkForExternalLink - Flag _externalLinkCheckRunning zurückgesetzt.',
        );
      }
    }
  }

  // Verbessert: Prüft auf Captcha und scrollt direkt dorthin mit Animation
  Future<bool> _checkForCaptcha() async {
    // Wenn wir auf einer externen Seite sind, überspringen wir die CAPTCHA-Prüfung vollständig
    if (_showExternalLinkInfo) {
      _log.i(
        "Externe Seite erkannt, CAPTCHA-Prüfung wird vollständig übersprungen",
      );
      if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }
      return false; // Kein CAPTCHA auf externen Seiten
    }

    bool captchaDetected = false;

    // Erweiterte Prüfung via JavaScript mit direkter Erkennung und Lokalisierung
    try {
      final result = await _controller.runJavaScriptReturningResult('''
        (function() {
          // Erweiterte Liste von CAPTCHA-Selektoren
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]',
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]',
            // Zusätzliche Selektoren für Arbeitsagentur-spezifische CAPTCHAs
            '.captcha-container',
            '.captcha-image',
            '.captcha-input',
            '.captcha-refresh',
            'input[name*="captcha" i]',
            'img[alt*="captcha" i]',
            'div[aria-label*="captcha" i]'
          ];

          // Textbasierte Erkennung
          const captchaTextTerms = [
            'captcha',
            'sicherheitsabfrage',
            'bestätigen sie, dass sie kein roboter sind',
            'ich bin kein roboter',
            'security check',
            'verification',
            'verifizierung',
            'zeichen eingeben',
            'enter the characters',
            'sind sie ein mensch',
            'are you human',
            'bot check',
            'robot check'
          ];

          // Suche nach CAPTCHA-Elementen
          let captchaElements = [];

          // 1. Suche nach Elementen mit CAPTCHA-Selektoren
          for (const selector of captchaSelectors) {
            try {
              const elements = document.querySelectorAll(selector);
              if (elements.length > 0) {
                captchaElements = [...captchaElements, ...Array.from(elements)];
              }
            } catch (e) {
              console.error('Fehler bei Selector: ' + selector, e);
            }
          }

          // 2. Suche nach Text-basierten CAPTCHA-Hinweisen
          if (captchaElements.length === 0) {
            const allElements = document.querySelectorAll('body *');
            for (const element of allElements) {
              try {
                const text = element.innerText || element.textContent || '';
                const lowerText = text.toLowerCase();

                if (captchaTextTerms.some(term => lowerText.includes(term))) {
                  const rect = element.getBoundingClientRect();
                  if (rect.width > 0 && rect.height > 0) {
                    captchaElements.push(element);
                  }
                }
              } catch (e) {
                console.error('Fehler bei Textsuche', e);
              }
            }
          }

          // 3. Suche nach Bildern, die CAPTCHA sein könnten
          if (captchaElements.length === 0) {
            const images = document.querySelectorAll('img');
            for (const img of images) {
              try {
                const src = img.src || '';
                const alt = img.alt || '';
                if (src.toLowerCase().includes('captcha') || alt.toLowerCase().includes('captcha')) {
                  captchaElements.push(img);
                }
              } catch (e) {
                console.error('Fehler bei Bildsuche', e);
              }
            }
          }

          // 4. Suche nach Formularen, die CAPTCHA enthalten könnten
          if (captchaElements.length === 0) {
            const forms = document.querySelectorAll('form');
            for (const form of forms) {
              try {
                const formHTML = form.innerHTML.toLowerCase();
                if (captchaTextTerms.some(term => formHTML.includes(term))) {
                  captchaElements.push(form);
                }
              } catch (e) {
                console.error('Fehler bei Formularsuche', e);
              }
            }
          }

          return {
            captchaDetected: captchaElements.length > 0,
            captchaCount: captchaElements.length
          };
        })()
      ''');

      // Ergebnis auswerten
      final Map<String, dynamic> captchaResult = jsonDecode(result.toString());

      captchaDetected = captchaResult['captchaDetected'] as bool;
      final int captchaCount = captchaResult['captchaCount'] as int;

      if (captchaDetected) {
        _log.w('CAPTCHA auf der Seite erkannt. Anzahl: $captchaCount');
      } else {
        _log.i('Kein CAPTCHA auf der Seite gefunden.');
      }
    } catch (e, stackTrace) {
      _log.e(
        'Fehler bei der erweiterten CAPTCHA-Erkennung:',
        error: e,
        stackTrace: stackTrace,
      );

      // Fallback: Prüfe den extrahierten Text
      if (_extractedJobText.isNotEmpty) {
        if (_extractedJobText.toLowerCase().contains("captcha") ||
            _extractedJobText.toLowerCase().contains("sicherheitsabfrage") ||
            _extractedJobText.toLowerCase().contains(
              "bestätigen sie, dass sie kein roboter sind",
            )) {
          _log.w('CAPTCHA im extrahierten Text erkannt (Fallback).');
          captchaDetected = true;
        }
      }
    }

    // Wenn ein CAPTCHA erkannt wurde, aktualisiere den State und scrolle sofort dorthin
    if (captchaDetected && mounted) {
      setState(() {
        _captchaDetected = true;
        _captchaSolved = false; // Setze explizit auf nicht gelöst
      });

      // Direkt zum CAPTCHA scrollen und animieren - mit Verzögerung für bessere Zuverlässigkeit
      // Nur wenn wir nicht auf einer externen Seite sind
      if (!_showExternalLinkInfo) {
        await Future.delayed(const Duration(milliseconds: 100));
        await _scrollToCaptchaAndHighlight();

        // Nochmal scrollen nach kurzer Verzögerung, um sicherzustellen, dass es funktioniert
        await Future.delayed(const Duration(milliseconds: 500));
        await _scrollToCaptchaAndHighlight();

        // Starte die Überprüfung, ob das CAPTCHA gelöst wurde
        _checkIfCaptchaSolved();
      } else {
        _log.i(
          "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _checkForCaptcha",
        );
      }
    } else if (mounted) {
      // Wenn kein CAPTCHA erkannt wurde, setze den Status auf gelöst
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
      });
    }

    return captchaDetected;
  }

  // Neue Methode: Überprüft regelmäßig, ob das CAPTCHA gelöst wurde
  Future<void> _checkIfCaptchaSolved() async {
    if (!mounted ||
        _showExternalLinkInfo ||
        !_captchaDetected ||
        _captchaSolved) {
      return;
    }

    _log.i("Überprüfe, ob CAPTCHA gelöst wurde...");

    try {
      // 1. Prüfe, ob CAPTCHA-Elemente noch sichtbar sind
      final captchaElementsResult = await _controller
          .runJavaScriptReturningResult('''
        (function() {
          // Suche nach typischen CAPTCHA-Elementen
          const captchaElements = document.querySelectorAll('input[type="text"], .captcha, #captcha, [id*="captcha"], [class*="captcha"], [id*="Captcha"], [class*="Captcha"], .sicherheitsfrage, #sicherheitsfrage');

          // Suche nach Überschriften oder Texten, die auf ein CAPTCHA hinweisen
          const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, label, div');
          let captchaHeadingFound = false;

          for (let i = 0; i < headings.length; i++) {
            const text = headings[i].innerText || '';
            if (text.toLowerCase().includes('captcha') ||
                text.toLowerCase().includes('sicherheitsfrage') ||
                text.toLowerCase().includes('sicherheitsabfrage') ||
                text.toLowerCase().includes('zeichen eingeben') ||
                text.toLowerCase().includes('dargestellten zeichen')) {
              captchaHeadingFound = true;
              break;
            }
          }

          // Prüfe, ob der Absenden-Button geklickt wurde (könnte auf ein gelöstes CAPTCHA hindeuten)
          const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"], button');
          let submitButtonClicked = false;

          for (let i = 0; i < submitButtons.length; i++) {
            const button = submitButtons[i];
            const buttonText = (button.textContent || '').toLowerCase();

            // Prüfe auf relevante Button-Texte
            const isRelevantButton = buttonText.includes('absenden') ||
                                   buttonText.includes('weiter') ||
                                   buttonText.includes('ok') ||
                                   buttonText.includes('submit');

            if (isRelevantButton) {
              // Prüfe, ob der Button deaktiviert ist oder eine "loading"-Klasse hat (Hinweis auf Klick)
              if (button.disabled || button.classList.contains('loading') || button.classList.contains('clicked')) {
                submitButtonClicked = true;
                break;
              }
            }
          }

          return {
            captchaElementsFound: captchaElements.length > 0,
            captchaHeadingFound: captchaHeadingFound,
            submitButtonClicked: submitButtonClicked
          };
        })()
      ''');

      // 2. Extrahiere Text aus der WebView
      final String? extractedText = await _extractTextFromWebView();

      // 3. Prüfe, ob eine E-Mail-Adresse im Text gefunden werden kann
      final String? extractedEmail =
          extractedText != null ? _findEmailInText(extractedText) : null;

      // Parse das Ergebnis der CAPTCHA-Elemente
      bool captchaElementsStillVisible = true;
      bool submitButtonClicked = false;

      // Null-Safety: Prüfe ob captchaElementsResult nicht null ist
      try {
        final Map<String, dynamic> parsedResult = jsonDecode(
          captchaElementsResult.toString(),
        );
        final bool captchaElementsFound =
            parsedResult['captchaElementsFound'] as bool? ?? false;
        final bool captchaHeadingFound =
            parsedResult['captchaHeadingFound'] as bool? ?? false;
        submitButtonClicked =
            parsedResult['submitButtonClicked'] as bool? ?? false;

        captchaElementsStillVisible =
            captchaElementsFound || captchaHeadingFound;
      } catch (e) {
        _log.w('Fehler beim Parsen der CAPTCHA-Elemente: $e');
        // Fallback: Annahme dass CAPTCHA-Elemente noch sichtbar sind
        captchaElementsStillVisible = true;
        submitButtonClicked = false;
      }

      // 4. Prüfe, ob CAPTCHA-Hinweise noch im Text vorhanden sind
      bool captchaHintsStillPresent = false;

      if (extractedText != null) {
        captchaHintsStillPresent =
            extractedText.toLowerCase().contains(
              "sicherheitsgründen keine kontaktdaten",
            ) ||
            extractedText.toLowerCase().contains(
              "lösen sie bitte die sicherheitsfrage",
            ) ||
            extractedText.toLowerCase().contains(
              "lösen sie die sicherheitsfrage",
            ) ||
            extractedText.toLowerCase().contains(
              "geben sie die dargestellten zeichen",
            ) ||
            extractedText.toLowerCase().contains(
              "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
            );
      }

      // 5. Entscheide, ob das CAPTCHA als gelöst gilt
      bool captchaSolved = false;

      // a) E-Mail gefunden ist ein starker Indikator für ein gelöstes CAPTCHA
      if (extractedEmail != null && extractedEmail.isNotEmpty) {
        _log.i("CAPTCHA wurde gelöst! E-Mail gefunden: $extractedEmail");
        captchaSolved = true;
      }
      // b) Keine CAPTCHA-Elemente mehr sichtbar und keine CAPTCHA-Hinweise im Text
      else if (!captchaElementsStillVisible && !captchaHintsStillPresent) {
        _log.i(
          "CAPTCHA wurde gelöst! Keine CAPTCHA-Elemente oder Hinweise mehr gefunden.",
        );
        captchaSolved = true;
      }
      // c) Submit-Button wurde geklickt (könnte auf ein gelöstes CAPTCHA hindeuten)
      else if (submitButtonClicked) {
        _log.i(
          "CAPTCHA wurde möglicherweise gelöst! Submit-Button wurde geklickt.",
        );
        captchaSolved = true;
      }

      // 6. Aktualisiere den State, wenn das CAPTCHA als gelöst gilt
      if (captchaSolved && mounted) {
        setState(() {
          _captchaSolved = true;
        });
        _log.i("CAPTCHA-Status auf 'gelöst' gesetzt.");
        return; // Beende die Methode, da das CAPTCHA gelöst ist
      }

      // 7. CAPTCHA ist noch nicht gelöst, plane eine erneute Überprüfung mit Debouncing
      if (mounted && !_captchaSolved && !_captchaCheckRunning) {
        _log.i("CAPTCHA ist noch nicht gelöst, prüfe erneut in 2 Sekunden...");
        _captchaCheckTimer?.cancel();
        _captchaCheckTimer = Timer(const Duration(seconds: 2), () {
          if (mounted && _captchaDetected && !_captchaSolved) {
            _checkIfCaptchaSolved();
          }
        });
      }
    } catch (e, stackTrace) {
      _log.e(
        "Fehler bei CAPTCHA-Überprüfung",
        error: e,
        stackTrace: stackTrace,
      );

      // Bei einem Fehler planen wir trotzdem eine erneute Überprüfung
      if (mounted && _captchaDetected && !_captchaSolved) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _captchaDetected && !_captchaSolved) {
            _checkIfCaptchaSolved();
          }
        });
      }
    }
  }

  // Verbesserte Methode: Scrollt zum Captcha und hebt es hervor
  Future<void> _scrollToCaptchaAndHighlight() async {
    // Wenn wir auf einer externen Seite sind oder das CAPTCHA bereits gelöst ist, überspringen wir die CAPTCHA-Hervorhebung vollständig
    if (_showExternalLinkInfo || _captchaSolved) {
      _log.i(
        "Externe Seite erkannt oder CAPTCHA bereits gelöst, CAPTCHA-Hervorhebung wird vollständig übersprungen",
      );
      // Setze den CAPTCHA-Status explizit, um sicherzustellen, dass keine weiteren CAPTCHA-Prüfungen stattfinden
      if (mounted) {
        setState(() {
          if (_showExternalLinkInfo) {
            _captchaDetected = false;
            _captchaSolved = true;
          }
        });
      }
      return;
    }

    try {
      _log.i('Starte Captcha-Hervorhebung und Scroll...');

      // Führe das JavaScript aus, um das Captcha zu finden und hervorzuheben
      await _controller.runJavaScript('''
        (function() {
          // Entferne zuerst alle vorherigen Hervorhebungen
          const oldStyles = document.querySelectorAll('style[data-captcha-style]');
          oldStyles.forEach(style => style.remove());

          const oldOverlays = document.querySelectorAll('.captcha-overlay, .captcha-spotlight');
          oldOverlays.forEach(overlay => overlay.remove());

          const oldContainers = document.querySelectorAll('.captcha-container');
          oldContainers.forEach(container => {
            // Bewege alle Kinder zurück zum ursprünglichen Elternelement
            const parent = container.parentNode;
            while (container.firstChild) {
              parent.insertBefore(container.firstChild, container);
            }
            container.remove();
          });

          // Entferne alle Highlight-Klassen
          document.querySelectorAll('.captcha-highlight').forEach(el => {
            el.classList.remove('captcha-highlight');
          });

          // Suche nach Captcha-Elementen mit erweiterten Selektoren
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]', // reCAPTCHA v2/v3
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]',
            // Spezifische Selektoren für Bundesagentur für Arbeit
            'input[type="text"]',
            'img[alt*="Sicherheitsabfrage"]',
            'img[alt*="Captcha"]',
            '.captcha-container',
            '.captcha-image',
            '.captcha-input'
          ];

          // Alle Captcha-Elemente finden
          let captchaElements = [];
          captchaSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              if (elements.length > 0) {
                captchaElements = [...captchaElements, ...Array.from(elements)];
              }
            } catch (e) {
              console.error('Fehler bei Selector: ' + selector, e);
            }
          });

          // Spezifische Suche für Bundesagentur für Arbeit Sicherheitsabfrage
          try {
            // Suche nach Überschriften mit Sicherheitsabfrage-Text
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            headings.forEach(heading => {
              const text = (heading.textContent || '').toLowerCase();
              if (text.includes('sicherheitsabfrage') || text.includes('captcha')) {
                captchaElements.push(heading);
                if (heading.parentElement) {
                  captchaElements.push(heading.parentElement);
                }
              }
            });

            // Suche nach Divs mit Sicherheitsabfrage-Text
            const divs = document.querySelectorAll('div');
            divs.forEach(div => {
              const text = (div.textContent || '').toLowerCase();
              if (text.includes('sicherheitsabfrage') && text.length < 200) { // Nur kurze Texte
                captchaElements.push(div);
              }
            });

            // Suche nach dem CAPTCHA-Button, der im Screenshot zu sehen ist
            const captchaButtons = document.querySelectorAll('button, a, div');
            captchaButtons.forEach(button => {
              try {
                const text = (button.textContent || '').toLowerCase();
                if (text.includes('captcha') || text.includes('lösen') || text.includes('absenden')) {
                  console.log('CAPTCHA-Button gefunden:', button.textContent);
                  captchaElements.push(button);

                  // Auch das Elternelement hinzufügen
                  if (button.parentElement) {
                    captchaElements.push(button.parentElement);
                  }

                  // Auch das Großelternelement hinzufügen
                  if (button.parentElement && button.parentElement.parentElement) {
                    captchaElements.push(button.parentElement.parentElement);
                  }
                }
              } catch (buttonError) {
                console.error('Fehler bei der Button-Suche', buttonError);
              }
            });

            // Suche nach allen Elementen, die "Sicherheitsabfrage" enthalten
            document.querySelectorAll('*').forEach(el => {
              try {
                if (el.textContent && el.textContent.includes('Sicherheitsabfrage')) {
                  console.log('Element mit Sicherheitsabfrage gefunden:', el.tagName);
                  captchaElements.push(el);

                  // Suche nach dem Formular, das dieses Element enthält
                  const form = el.closest('form');
                  if (form) {
                    console.log('Formular für Sicherheitsabfrage gefunden');
                    captchaElements.push(form);

                    // Suche nach Eingabefeldern im Formular
                    const inputFields = form.querySelectorAll('input[type="text"]');
                    inputFields.forEach(input => {
                      console.log('Eingabefeld für Sicherheitsabfrage gefunden');
                      captchaElements.push(input);
                    });

                    // Suche nach Bildern im Formular
                    const images = form.querySelectorAll('img');
                    images.forEach(img => {
                      console.log('Bild für Sicherheitsabfrage gefunden');
                      captchaElements.push(img);
                    });
                  }
                }
              } catch (innerError) {
                console.error('Fehler bei der Verarbeitung eines Elements', innerError);
              }
            });

            // Zusätzlich: Suche nach allen Bildern mit "captcha" im src oder alt
            document.querySelectorAll('img').forEach(img => {
              try {
                const src = img.src || '';
                const alt = img.alt || '';
                if (src.toLowerCase().includes('captcha') ||
                    alt.toLowerCase().includes('captcha') ||
                    alt.toLowerCase().includes('sicherheitsabfrage')) {
                  console.log('CAPTCHA-Bild gefunden:', img.src);
                  captchaElements.push(img);

                  // Auch das Elternelement hinzufügen
                  if (img.parentElement) {
                    captchaElements.push(img.parentElement);
                  }
                }
              } catch (imgError) {
                console.error('Fehler bei der Bildsuche', imgError);
              }
            });

            // Spezifische Suche für den Button im Screenshot
            const pinkButtons = document.querySelectorAll('.MuiButtonBase-root, button');
            pinkButtons.forEach(button => {
              try {
                const computedStyle = window.getComputedStyle(button);
                const backgroundColor = computedStyle.backgroundColor;
                // Prüfe auf pinke/rote Farben
                if (backgroundColor.includes('rgb(') &&
                    (backgroundColor.includes('255') || backgroundColor.includes('233') || backgroundColor.includes('219'))) {
                  console.log('Farbiger Button gefunden:', button.textContent);
                  captchaElements.push(button);
                }
              } catch (styleError) {
                console.error('Fehler bei der Style-Analyse', styleError);
              }
            });
          } catch (e) {
            console.error('Fehler bei Bundesagentur-spezifischer Suche', e);
          }

          // Wenn kein Captcha gefunden wurde, suche nach Text
          if (captchaElements.length === 0) {
            const textSearchTerms = [
              'captcha',
              'sicherheitsabfrage',
              'bestätigen sie, dass sie kein roboter sind',
              'ich bin kein roboter',
              'security check',
              'verification',
              'verifizierung',
              'zeichen eingeben',
              'enter the characters'
            ];

            const allElements = document.querySelectorAll('body *');
            for (const element of allElements) {
              try {
                const text = element.innerText || element.textContent || '';
                const lowerText = text.toLowerCase();

                if (textSearchTerms.some(term => lowerText.includes(term))) {
                  // Prüfe, ob das Element sichtbar ist
                  const rect = element.getBoundingClientRect();
                  if (rect.width > 0 && rect.height > 0) {
                    captchaElements.push(element);
                  }
                }
              } catch (e) {
                console.error('Fehler bei Textsuche', e);
              }
            }
          }

          // Suche nach Bildern, die Captchas sein könnten
          const images = document.querySelectorAll('img');
          for (const img of images) {
            try {
              const src = img.src || '';
              if (src.toLowerCase().includes('captcha')) {
                captchaElements.push(img);
              }
            } catch (e) {
              console.error('Fehler bei Bildsuche', e);
            }
          }

          // Wenn Captcha-Elemente gefunden wurden
          if (captchaElements.length > 0) {
            console.log('Captcha-Elemente gefunden: ' + captchaElements.length);

            // Füge CSS-Animation für das Captcha hinzu
            const animationStyle = document.createElement('style');
            animationStyle.setAttribute('data-captcha-style', 'true');
            animationStyle.textContent = `
              @keyframes colorBorderFlow {
                0% { border-color: rgba(219, 39, 119, 0.5); }  /* Pink */
                20% { border-color: rgba(234, 179, 8, 0.5); }  /* Gold */
                40% { border-color: rgba(124, 58, 237, 0.5); }  /* Purple */
                60% { border-color: rgba(234, 88, 12, 0.5); }  /* Orange */
                80% { border-color: rgba(59, 130, 246, 0.5); }  /* Blue */
                100% { border-color: rgba(219, 39, 119, 0.5); } /* Pink again */
              }

              @keyframes captchaBorderGlow {
                0% { box-shadow: 0 0 3px rgba(219, 39, 119, 0.3); }
                25% { box-shadow: 0 0 5px rgba(234, 179, 8, 0.3); }
                50% { box-shadow: 0 0 7px rgba(124, 58, 237, 0.3); }
                75% { box-shadow: 0 0 5px rgba(234, 88, 12, 0.3); }
                100% { box-shadow: 0 0 3px rgba(219, 39, 119, 0.3); }
              }

              .captcha-highlight {
                border: 2px solid rgba(219, 39, 119, 0.5) !important;
                border-radius: 4px !important;
                position: relative !important;
                animation: colorBorderFlow 12s infinite ease-in-out, captchaBorderGlow 10s infinite ease-in-out !important;
                transition: all 0.3s ease !important;
                z-index: 9999 !important;
                color: #000000 !important;
              }

              .captcha-container {
                position: relative !important;
                padding: 15px !important;
                margin: 20px 0 !important;
                background-color: rgba(255, 255, 255, 0.95) !important;
                border-radius: 8px !important;
                border: 2px solid rgba(219, 39, 119, 0.7) !important;
                z-index: 9998 !important;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1) !important;
              }

              .captcha-label {
                position: absolute !important;
                top: -15px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                background-color: #db2777 !important;
                color: white !important;
                padding: 6px 16px !important;
                border-radius: 4px !important;
                font-weight: bold !important;
                font-size: 14px !important;
                z-index: 10000 !important;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
                text-align: center !important;
              }

              /* Overlay-Klassen entfernt */
            `;
            document.head.appendChild(animationStyle);

            // Finde das Haupt-Captcha-Element (das größte oder das erste)
            let mainCaptchaElement = captchaElements[0];
            let maxArea = 0;

            for (const element of captchaElements) {
              try {
                const rect = element.getBoundingClientRect();
                const area = rect.width * rect.height;
                if (area > maxArea && area > 100) { // Mindestgröße, um kleine Elemente zu ignorieren
                  maxArea = area;
                  mainCaptchaElement = element;
                }
              } catch (e) {
                console.error('Fehler bei Größenberechnung', e);
              }
            }

            // Suche nach dem Elternelement, das ein Formular sein könnte
            let captchaParent = mainCaptchaElement;
            let formFound = false;

            // Versuche, ein umgebendes Formular zu finden
            for (let i = 0; i < 5; i++) { // Maximal 5 Ebenen nach oben
              if (!captchaParent.parentNode) break;

              captchaParent = captchaParent.parentNode;
              if (captchaParent.tagName && captchaParent.tagName.toLowerCase() === 'form') {
                formFound = true;
                break;
              }
            }

            // Wenn kein Formular gefunden wurde, gehe zurück zum ursprünglichen Element
            if (!formFound) {
              captchaParent = mainCaptchaElement;
            }

            // Erstelle einen Container um das Captcha
            const captchaContainer = document.createElement('div');
            captchaContainer.className = 'captcha-container';

            // Erstelle ein Label für das Captcha
            const captchaLabel = document.createElement('div');
            captchaLabel.className = 'captcha-label';
            captchaLabel.textContent = 'CAPTCHA HIER LÖSEN';
            captchaLabel.style.backgroundColor = '#db2777'; // Pink
            captchaLabel.style.color = 'white';
            captchaLabel.style.fontWeight = 'bold';
            captchaLabel.style.padding = '6px 12px';
            captchaLabel.style.borderRadius = '4px';
            captchaLabel.style.position = 'absolute';
            captchaLabel.style.top = '-15px';
            captchaLabel.style.left = '50%';
            captchaLabel.style.transform = 'translateX(-50%)';
            captchaLabel.style.zIndex = '10000';

            // Füge das Label zum Container hinzu
            captchaContainer.appendChild(captchaLabel);

            try {
              // Wende die Highlight-Klasse auf das Captcha-Element an
              mainCaptchaElement.classList.add('captcha-highlight');

              // Wenn das Element ein iframe ist oder ein Formular gefunden wurde, umhülle es mit dem Container
              if (mainCaptchaElement.tagName.toLowerCase() === 'iframe' || formFound) {
                const parent = captchaParent.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, captchaParent);
                  captchaContainer.appendChild(captchaParent);
                }
              } else {
                // Sonst füge den Container direkt ein
                const parent = mainCaptchaElement.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, mainCaptchaElement);
                  captchaContainer.appendChild(mainCaptchaElement);
                }
              }
            } catch (e) {
              console.error('Fehler beim Einfügen des Containers', e);
              // Fallback: Füge den Container direkt neben dem Element ein
              try {
                const parent = mainCaptchaElement.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, mainCaptchaElement.nextSibling);
                }
              } catch (e2) {
                console.error('Auch Fallback fehlgeschlagen', e2);
              }
            }

            // Direkt das CAPTCHA-Feld markieren ohne Overlay oder Spotlight
            // Finde das Eingabefeld für das CAPTCHA
            const captchaInputField = document.querySelector('input[type="text"]');
            if (captchaInputField) {
              console.log('CAPTCHA-Eingabefeld gefunden');

              // Füge einen dezenten Rahmen hinzu
              captchaInputField.style.border = '2px solid #db2777';
              captchaInputField.style.borderRadius = '4px';
              captchaInputField.style.boxShadow = '0 0 10px rgba(219, 39, 119, 0.5)';
              captchaInputField.style.padding = '8px';
              captchaInputField.style.fontSize = '16px';
              captchaInputField.style.fontWeight = 'bold';
              captchaInputField.style.backgroundColor = 'rgba(255, 240, 245, 0.7)';

              // Füge Animation hinzu
              captchaInputField.style.animation = 'colorBorderFlow 5s infinite ease-in-out';

              // Füge einen Fokus hinzu
              setTimeout(() => {
                captchaInputField.focus();
              }, 800);

              // Hinweistext entfernt

              // Verbessere das Styling des Elternelements
              const inputParent = captchaInputField.parentElement;
              if (inputParent) {
                inputParent.style.position = 'relative';
              }
            } else {
              console.log('Kein CAPTCHA-Eingabefeld gefunden');
            }

            // Verbesserte Scroll-Funktion zum CAPTCHA mit mehreren Fallbacks
            try {
              // Versuche zuerst, direkt zum Eingabefeld zu scrollen, falls vorhanden
              const captchaInputField = document.querySelector('input[type="text"]');
              if (captchaInputField) {
                console.log('Scrolle zum CAPTCHA-Eingabefeld');
                // Hebe das Element sehr stark hervor, bevor wir scrollen
                captchaInputField.style.border = '4px solid #db2777';
                captchaInputField.style.borderRadius = '4px';
                captchaInputField.style.boxShadow = '0 0 25px rgba(219, 39, 119, 0.9)';
                captchaInputField.style.padding = '8px';
                captchaInputField.style.fontSize = '16px';
                captchaInputField.style.fontWeight = 'bold';
                captchaInputField.style.backgroundColor = 'rgba(255, 240, 245, 0.9)';
                captchaInputField.style.animation = 'colorBorderFlow 5s infinite ease-in-out';

                // Berechne die Position des Elements
                const rect = captchaInputField.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                window.scrollTo(0, targetY);

                // Fokussiere das Eingabefeld nach dem Scrollen
                setTimeout(() => {
                  captchaInputField.focus();
                }, 100);

                return true;
              }

              // Fallback 1: Zum Hauptelement scrollen mit sanfter Animation
              console.log('Scrolle zum Haupt-CAPTCHA-Element');

              // Berechne die Position des Elements
              const rect = mainCaptchaElement.getBoundingClientRect();
              const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

              // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
              window.scrollTo(0, targetY);
            } catch (e) {
              console.error('Fehler beim Scrollen zum Captcha', e);

              // Fallback 2: Versuche es mit dem Container
              try {
                console.log('Fallback: Scrolle zum CAPTCHA-Container');

                // Berechne die Position des Elements
                const rect = captchaContainer.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                window.scrollTo(0, targetY);
              } catch (e2) {
                console.error('Auch Fallback-Scrollen fehlgeschlagen', e2);

                // Fallback 3: Versuche, zu einem beliebigen CAPTCHA-Element zu scrollen
                try {
                  const anyElement = document.querySelector('*[id*="captcha" i], *[class*="captcha" i], input[type="text"], img[src*="captcha" i]');
                  if (anyElement) {
                    console.log('Letzter Fallback: Scrolle zu einem beliebigen CAPTCHA-Element');

                    // Berechne die Position des Elements
                    const rect = anyElement.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                    // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                    window.scrollTo(0, targetY);
                  }
                } catch (e3) {
                  console.error('Alle Scroll-Versuche fehlgeschlagen', e3);
                }
              }
            }

            // Keine Overlay-Entfernung mehr nötig, da wir keinen Overlay haben

            return true;
          } else {
            console.log('Keine Captcha-Elemente gefunden');
            return false;
          }
        })();
      ''');

      _log.i('Captcha-Hervorhebung und Scroll ausgeführt.');
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Scrollen zum Captcha:',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // --- Generierungslogik wurde hierhin verschoben ---
  Future<Map<String, dynamic>> _generateApplicationWithAI(
    String jobText,
  ) async {
    _log.i('🔥 DEBUG: _generateApplicationWithAI() gestartet');
    _log.i('Starte KI-Anschreiben-Generierung...');

    // GUTHABEN-ABZUG ENTFERNT: Wird jetzt nur noch in _startGenerationProcess() gemacht
    _log.i(
      '✅ AI-Generierung startet - Guthaben-Abzug erfolgt in _startGenerationProcess()',
    );
    // User Profil holen
    final userProfileState = ref.read(userProfileProvider);
    final UserProfile? userProfile = userProfileState.asData?.value;
    final JobEntity? jobEntityNullable = widget.jobEntity;

    if (userProfile == null) {
      _log.e('User-Profil nicht verfügbar für KI-Generierung.');
      return {'error': 'Benutzerprofil konnte nicht geladen werden.'};
    }

    if (jobEntityNullable == null) {
      _log.e('JobEntity ist null, kann kein Anschreiben generieren.');
      return {'error': 'Job-Details konnten nicht geladen werden.'};
    }

    // Verwende das vom Benutzer ausgewählte Modell
    _log.i(
      'Verwende ausgewähltes Modell für die Bewerbungsgenerierung: $_selectedModelType',
    );

    final JobEntity jobEntity = jobEntityNullable;

    // --- Daten für die Supabase Function vorbereiten ---
    // WICHTIG: Die Schlüssel müssen exakt mit denen übereinstimmen,
    // die in der Supabase Function (`index.ts` -> `RequestPayload`) erwartet werden.
    final Map<String, dynamic> userProfileDataForFunction = {
      // Passe dies an die tatsächliche Struktur in deiner Edge Function an!
      'name': userProfile.name,
      'skills': userProfile.skills ?? [],
      'experience':
          (userProfile.workExperience
                  ?.map((e) => '${e.position} bei ${e.company}')
                  .join('\n') ??
              ''),
      // Füge die zusätzlichen Hinweise aus dem TextField hinzu, wenn vorhanden
      if (_hintsController.text.isNotEmpty)
        'additionalHints': _hintsController.text.trim(),
      // Füge die globalen KI-Hinweise hinzu, wenn vorhanden
      if (userProfile.globalAiHints != null &&
          userProfile.globalAiHints!.isNotEmpty)
        'globalAiHints': userProfile.globalAiHints,
      // Füge die Bewerbungslänge hinzu, wenn vorhanden
      if (userProfile.applicationLength != null &&
          userProfile.applicationLength!.isNotEmpty)
        'applicationLength': userProfile.applicationLength,
      // Füge hier ggf. weitere benötigte Felder hinzu
    };

    // 🔥 DEBUG: Zeige die gesendeten Daten
    debugPrint('🔥 FRONTEND - SENDING USER PROFILE DATA TO SUPABASE:');
    debugPrint('=' * 80);
    debugPrint('APPLICATION LENGTH: ${userProfile.applicationLength}');
    debugPrint('GLOBAL AI HINTS: ${userProfile.globalAiHints}');
    debugPrint('ADDITIONAL HINTS: ${_hintsController.text}');
    debugPrint('FULL USER PROFILE DATA:');
    debugPrint(userProfileDataForFunction.toString());
    debugPrint('=' * 80);

    final Map<String, dynamic> jobPostingDataForFunction = {
      // Passe dies an die tatsächliche Struktur in deiner Edge Function an!
      'title': jobEntity.title,
      'company': jobEntity.companyName ?? '',
      'description': jobText, // Der extrahierte Text
    };

    _log.d("--- Daten für Supabase Function ---");
    _log.d("UserProfile Data: ${jsonEncode(userProfileDataForFunction)}");
    _log.d("JobPosting Data: ${jsonEncode(jobPostingDataForFunction)}");
    _log.i("--- Rufe Supabase Function 'generate-cover-letter' auf ---");

    try {
      // *** NEU: Supabase Service verwenden mit Timeout ***
      final supabaseService = ref.read(supabaseServiceProvider);

      // Timeout für die Anfrage setzen (2 Minuten)
      // Durchsetzen: Free darf die hohe Qualitätsstufe nicht nutzen
      final planType = userProfile.premiumPlanType?.toLowerCase() ?? 'basic';
      final canUseHighQuality = planType == 'pro' || planType == 'unlimited';
      if (_selectedModelType == 'deepseek' && !canUseHighQuality) {
        _log.w('Qualitätsstufe gesperrt für Free. Vorgang abgebrochen.');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Diese Qualitätsstufe ist nur für Pro/Unlimited verfügbar.',
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
        return {'error': 'Nicht erlaubt'};
      }

      final result = await Future.any([
        // Hauptanfrage
        supabaseService.generateCoverLetter(
          userProfile: userProfileDataForFunction,
          jobPosting: jobPostingDataForFunction,
          modelType: _selectedModelType, // erlaubte Auswahl
        ),
        // Timeout-Future
        Future.delayed(const Duration(minutes: 2), () {
          throw TimeoutException(
            'Die Anschreiben-Generierung hat zu lange gedauert. Bitte versuchen Sie es später erneut.',
          );
        }),
      ]);

      _log.i("--- Antwort von Supabase Function erhalten ---");

      // 🔥 DEBUG: Zeige die empfangene Antwort
      debugPrint('🔥 FRONTEND - RECEIVED RESPONSE FROM SUPABASE:');
      debugPrint('=' * 80);
      debugPrint(
        'COVER LETTER WORD COUNT: ${result.coverLetter.split(' ').length}',
      );
      debugPrint('EXTRACTED EMAIL: ${result.extractedEmail}');
      debugPrint('MODEL TYPE: ${result.modelType}');
      debugPrint('COVER LETTER:');
      debugPrint(result.coverLetter);
      debugPrint('=' * 80);

      // Erfolg: Extrahiere Daten aus dem Ergebnisobjekt
      return {
        'generatedText': result.coverLetter,
        'extractedEmail':
            result.extractedEmail, // *** Verwende die extrahierte E-Mail ***
        'modelType':
            result.modelType ??
            _selectedModelType, // Verwende das Modell aus der Antwort oder das ausgewählte Modell
        'error': null,
      };
    } on TimeoutException catch (e) {
      _log.e("Timeout bei der Anschreiben-Generierung", error: e);

      // Fallback zu Firebase Cloud Functions entfernt
      _log.i("Kein Fallback mehr verfügbar, da Firebase entfernt wurde");
      return {
        'error':
            'Die Anschreiben-Generierung hat zu lange gedauert. Bitte versuchen Sie es später erneut.',
      };
    } catch (e, stackTrace) {
      _log.e(
        "Allgemeiner Fehler beim Aufruf der Supabase Function:",
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'error':
            "Die Anschreiben-Generierung ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.",
      };
    }
  }

  // Variable zum Speichern des CAPTCHA-Status wurde bereits oben definiert

  // --- Dialog zur Auswahl des KI-Modells ---
  Future<String?> _showModelSelectionDialog(BuildContext context) async {
    // Lade die gespeicherte Standardauswahl
    final prefs = await SharedPreferences.getInstance();
    final String? defaultModel = prefs.getString('default_model_type');
    final bool hasDefaultModel = defaultModel != null;
    final bool shouldShowDialog =
        prefs.getBool('show_model_selection_dialog') ?? true;

    // Wenn eine Standardauswahl existiert und der Dialog nicht angezeigt werden soll, verwende diese
    if (hasDefaultModel && !shouldShowDialog) {
      _log.i("Verwende gespeicherte Standardauswahl: $defaultModel");
      return defaultModel;
    }

    // Checkbox-Status für "Als Standard speichern"
    bool saveAsDefault = false;

    return showDialog<String>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Anschreiben generieren'),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Wähle die Generierungsmethode:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 20),
                        // Option 1: Schnell (Mistral)
                        InkWell(
                          onTap: () {
                            final modelType = 'mistral';
                            if (saveAsDefault) {
                              _saveModelPreference(modelType, saveAsDefault);
                            }
                            Navigator.of(context).pop(modelType);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.speed, color: Colors.blue.shade700),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Schnell',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Schnellere Generierung mit guter Qualität',
                                        style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Option 2: Qualität (DeepSeek)
                        InkWell(
                          onTap: () {
                            final modelType = 'deepseek';
                            if (saveAsDefault) {
                              _saveModelPreference(modelType, saveAsDefault);
                            }
                            Navigator.of(context).pop(modelType);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.star, color: Colors.amber.shade700),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Qualität',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Höhere Qualität, kann etwas länger dauern',
                                        style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Checkbox für "Als Standard speichern"
                        Row(
                          children: [
                            Checkbox(
                              value: saveAsDefault,
                              onChanged: (value) {
                                setState(() {
                                  saveAsDefault = value ?? false;
                                });
                              },
                            ),
                            const Text('Als Standard speichern'),
                          ],
                        ),
                        if (hasDefaultModel) ...[
                          const SizedBox(height: 8),
                          TextButton.icon(
                            onPressed: () {
                              // Zurücksetzen der Standardauswahl
                              _resetModelPreference();
                              Navigator.of(context).pop(null);
                            },
                            icon: const Icon(Icons.refresh, size: 16),
                            label: const Text('Standardauswahl zurücksetzen'),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                              ),
                              minimumSize: const Size(0, 36),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(null),
                      child: const Text('Abbrechen'),
                    ),
                  ],
                ),
          ),
    );
  }

  // Hilfsmethode zum Speichern der Modellpräferenz
  void _saveModelPreference(String modelType, bool saveAsDefault) {
    if (saveAsDefault) {
      // Speichere die Präferenz asynchron in einem separaten Isolate
      Future.microtask(() async {
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('default_model_type', modelType);
          await prefs.setBool('show_model_selection_dialog', false);
          _log.i("Modellpräferenz gespeichert: $modelType");

          // Zeige eine Bestätigungsmeldung an, wenn der Widget noch mounted ist
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Standardauswahl gespeichert'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          _log.e('Fehler beim Speichern der Modellpräferenz: $e');
        }
      });
    }
  }

  // Hilfsmethode zum Zurücksetzen der Modellpräferenz
  void _resetModelPreference() {
    // Setze die Präferenz asynchron in einem separaten Isolate zurück
    Future.microtask(() async {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('default_model_type');
        await prefs.setBool('show_model_selection_dialog', true);
        _log.i("Modellpräferenz zurückgesetzt");

        // Zeige eine Bestätigungsmeldung an, wenn der Widget noch mounted ist
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Standardauswahl zurückgesetzt'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        _log.e('Fehler beim Zurücksetzen der Modellpräferenz: $e');
      }
    });
  }

  // Hilfsmethode zum Anzeigen des Modellauswahl-Dropdowns
  void _showModelSelectionDropdown(BuildContext context) {
    // Statt showMenu verwenden wir ein benutzerdefiniertes Dialog-Widget
    // Das löst Probleme mit Overflow und Positionierung
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          // Kein Inset-Padding, um mehr Kontrolle über das Layout zu haben
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 24,
          ),
          // Abgerundete Ecken und Schatten
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          // Elevation für Schatten
          elevation: 8,
          // Hintergrundfarbe aus dem Theme
          backgroundColor: Theme.of(context).cardColor,
          // Inhalt des Dialogs
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              // Minimale Größe für den Dialog
              mainAxisSize: MainAxisSize.min,
              children: [
                // Titel des Dialogs
                Padding(
                  padding: const EdgeInsets.only(
                    left: 16.0,
                    right: 16.0,
                    top: 8.0,
                    bottom: 16.0,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.model_training,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'KI-Modell auswählen',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Theme.of(context).textTheme.titleLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                // Option 1: Schnell (Mistral)
                _buildModelOption(
                  context: context,
                  icon: Icons.speed,
                  iconColor: Colors.blue.shade700,
                  title: 'Schnell',
                  description: 'Schnellere Generierung mit guter Qualität',
                  onTap: () {
                    _saveModelPreference('mistral', true);
                    setState(() {
                      _selectedModelType = 'mistral';
                    });
                    Navigator.of(context).pop();
                  },
                ),
                // Option 2: Qualität (DeepSeek)
                Builder(
                  builder: (context) {
                    final userProfileState = ref.watch(userProfileProvider);
                    final planType =
                        userProfileState.asData?.value.premiumPlanType
                            ?.toLowerCase() ??
                        'basic';
                    final canUseDeepseek =
                        planType == 'pro' || planType == 'unlimited';
                    return _buildModelOption(
                      context: context,
                      icon: Icons.star,
                      iconColor: Colors.amber.shade700,
                      title: 'Qualität',
                      description:
                          canUseDeepseek
                              ? 'Höhere Qualität, kann etwas länger dauern'
                              : 'Nur für Pro/Unlimited verfügbar',
                      onTap: () {
                        if (!canUseDeepseek) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'DeepSeek ist nur für Pro/Unlimited verfügbar.',
                              ),
                              backgroundColor: Colors.orange,
                              duration: Duration(seconds: 2),
                            ),
                          );
                          return;
                        }
                        _saveModelPreference('deepseek', true);
                        setState(() {
                          _selectedModelType = 'deepseek';
                        });
                        Navigator.of(context).pop();
                      },
                    );
                  },
                ),
                // Trennlinie
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 16,
                    endIndent: 16,
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                // Option zum Zurücksetzen
                InkWell(
                  onTap: () {
                    _resetModelPreference();
                    Navigator.of(context).pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 12.0,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.refresh,
                          color: Colors.grey.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Standardauswahl zurücksetzen',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Abbrechen-Button
                Padding(
                  padding: const EdgeInsets.only(
                    left: 16.0,
                    right: 16.0,
                    top: 16.0,
                    bottom: 8.0,
                  ),
                  child: SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Abbrechen'),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Hilfsmethode zum Erstellen einer Modelloption
  Widget _buildModelOption({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 26),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: iconColor, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // --- Anschreiben-Generierung ---
  Future<void> _triggerApplicationGeneration() async {
    _log.i('🔥 DEBUG: _triggerApplicationGeneration() gestartet');

    // Zeige den Dialog zur Auswahl des KI-Modells
    final String? selectedModel = await _showModelSelectionDialog(context);

    // Wenn kein Modell ausgewählt wurde (Abbrechen), beende die Methode
    if (selectedModel == null) {
      _log.i("Benutzer hat die Modellauswahl abgebrochen");
      return;
    }

    // Setze das ausgewählte Modell
    _selectedModelType = selectedModel;
    _log.i("Benutzer hat Modell ausgewählt: $_selectedModelType");

    // Prüfe vor Start, ob die gewählte Qualitätsstufe erlaubt ist
    final userProfileState = ref.read(userProfileProvider);
    final planType =
        userProfileState.asData?.value.premiumPlanType?.toLowerCase() ??
        'basic';
    final canUseHighQuality = planType == 'pro' || planType == 'unlimited';
    if (_selectedModelType == 'deepseek' && !canUseHighQuality) {
      if (mounted) {
        await showDialog(
          context: context,
          builder:
              (ctx) => AlertDialog(
                title: const Text('Nicht verfügbar'),
                content: const Text(
                  'Diese Qualitätsstufe ist nur für Pro oder Unlimited Mitglieder verfügbar.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(ctx).pop(),
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      }
      return;
    }

    // Wenn wir auf einer externen Seite sind, überspringen wir alle CAPTCHA-Prüfungen
    // aber führen trotzdem den Premium-Check durch
    if (_showExternalLinkInfo) {
      _log.i("Externe Seite erkannt, überspringe CAPTCHA-Prüfung");
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
        _isExtracting =
            false; // Noch nicht auf true setzen, erst nach Premium-Check
        _generationCompletedSuccessfully = false;
      });

      // Text extrahieren für externe Seiten
      try {
        // Extrahiere den Text mit einem speziellen Flag für externe Seiten
        final String? extractedJobText = await _extractTextFromWebView(
          isExternalPage: true,
        );
        if (extractedJobText == null || extractedJobText.isEmpty) {
          throw Exception('Konnte keinen Text von der Webseite extrahieren.');
        }
        _extractedJobText = extractedJobText;
        _log.i(
          "Text von externer Seite extrahiert, Länge: ${extractedJobText.length}",
        );

        // Premium-Check für externe Seiten
        final isPremium = ref.read(isPremiumProvider);
        _log.i("Premium/Ad Check auf externer Seite - isPremium: $isPremium");

        if (!isPremium) {
          // NICHT Premium: Direkt mit Generierung fortfahren (keine Werbung mehr erforderlich)
          if (!mounted) return; // Sicherheitscheck

          _log.i("Free-User: Starte Bewerbungsgenerierung direkt ohne Werbung");

          setState(() {
            _isGenerating = true; // Animation aktivieren
          });

          // Animationen starten
          _buttonAnimationController.repeat(reverse: true);
          _gradientSweepController.repeat(); // Sweep starten

          // Starte die Generierung direkt
          _startGenerationProcess();
          return;
        } else {
          // Premium-Nutzer: Direkt generieren
          setState(() {
            _isExtracting = true;
          });
          _buttonAnimationController.repeat(reverse: true);
          _gradientSweepController.repeat();

          // Direkt zum Generierungsprozess springen
          await _startGenerationProcess(isExternalPage: true);

          // Aufräumen
          if (mounted) {
            _buttonAnimationController.stop();
            _buttonAnimationController.reset();
            _gradientSweepController.stop();
            _gradientSweepController.reset();
            setState(() => _isExtracting = false);
          }
        }
      } catch (e) {
        _log.e("Fehler bei der Textextraktion auf externer Seite: $e");
        if (mounted) {
          _showGeneratedApplicationDialog(context, {'error': e.toString()});

          // Aufräumen
          _buttonAnimationController.stop();
          _buttonAnimationController.reset();
          _gradientSweepController.stop();
          _gradientSweepController.reset();
          setState(() => _isExtracting = false);
        }
      }

      return; // Beende die Methode hier für externe Seiten
    }

    // Ab hier nur für normale Seiten (nicht extern)

    // Prüfe zuerst, ob ein CAPTCHA erkannt wurde und noch nicht gelöst ist
    if (_captchaDetected && !_captchaSolved) {
      // Wenn ein ungelöstes CAPTCHA vorhanden ist, scrolle direkt dorthin und breche ab
      // Aber nur, wenn wir nicht auf einer externen Seite sind
      if (!_showExternalLinkInfo) {
        await _scrollToCaptchaAndHighlight();
      } else {
        _log.i(
          "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration",
        );
        // Setze den CAPTCHA-Status explizit
        if (mounted) {
          setState(() {
            _captchaDetected = false;
            _captchaSolved = true;
          });
        }
      }
      return; // Beende die Methode hier
    }

    // Nur für normale Seiten: Zusätzliche Sicherheitsprüfung für CAPTCHA
    final String? securityCheckText = await _extractTextFromWebView();
    if (securityCheckText != null && securityCheckText.isNotEmpty) {
      // Prüfe auf CAPTCHA-Hinweise im Text
      if (securityCheckText.toLowerCase().contains(
            "sicherheitsgründen keine kontaktdaten",
          ) ||
          securityCheckText.toLowerCase().contains(
            "lösen sie bitte die sicherheitsfrage",
          ) ||
          securityCheckText.toLowerCase().contains(
            "lösen sie die sicherheitsfrage",
          ) ||
          securityCheckText.toLowerCase().contains(
            "geben sie die dargestellten zeichen",
          ) ||
          securityCheckText.toLowerCase().contains(
            "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
          )) {
        // CAPTCHA ist möglicherweise nicht gelöst
        if (mounted) {
          // Wenn der Benutzer explizit auf den "CAPTCHA gelöst"-Button geklickt hat oder
          // wenn wir auf einer externen Seite sind, betrachten wir das CAPTCHA als gelöst
          if (_captchaSolved || _showExternalLinkInfo) {
            _log.i(
              "CAPTCHA wurde als gelöst markiert oder externe Seite erkannt, fahre mit Generierung fort",
            );

            // Setze den CAPTCHA-Status explizit
            setState(() {
              _captchaDetected =
                  _showExternalLinkInfo ? false : _captchaDetected;
              _captchaSolved = true;
            });

            // Fahre mit der Generierung fort, anstatt die Methode zu beenden
            // Wir setzen _extractedJobText, damit es für die Generierung verfügbar ist
            _extractedJobText = securityCheckText;
          }
          // Nur wenn das CAPTCHA nicht als gelöst markiert wurde und wir nicht auf einer externen Seite sind,
          // zeigen wir das CAPTCHA an und brechen ab
          else if (!_captchaSolved && !_showExternalLinkInfo) {
            _log.i("CAPTCHA nicht gelöst, zeige CAPTCHA an");

            // Setze den CAPTCHA-Status
            setState(() {
              _captchaDetected = true;
            });

            await _scrollToCaptchaAndHighlight();
            return; // Beende die Methode nur in diesem Fall
          }
        }
      }

      // Prüfe, ob eine E-Mail-Adresse im Text gefunden werden kann
      final String? extractedEmail = _findEmailInText(securityCheckText);
      if (extractedEmail == null || extractedEmail.isEmpty) {
        // Keine E-Mail gefunden, aber wir fahren trotzdem fort
        _log.i("Keine E-Mail im Text gefunden, aber wir fahren trotzdem fort");

        if (mounted) {
          // Wenn der Benutzer explizit auf den "CAPTCHA gelöst"-Button geklickt hat oder
          // wenn wir auf einer externen Seite sind, betrachten wir das CAPTCHA als gelöst
          if (_captchaSolved || _showExternalLinkInfo) {
            _log.i(
              "CAPTCHA wurde als gelöst markiert oder externe Seite erkannt, fahre mit Generierung fort",
            );

            // Setze den CAPTCHA-Status explizit
            setState(() {
              _captchaDetected =
                  _showExternalLinkInfo ? false : _captchaDetected;
              _captchaSolved = true;
            });

            // Fahre mit der Generierung fort, anstatt die Methode zu beenden
            // Wir setzen _extractedJobText, damit es für die Generierung verfügbar ist
            _extractedJobText = securityCheckText;
          }
          // Nur wenn das CAPTCHA nicht als gelöst markiert wurde und wir nicht auf einer externen Seite sind,
          // zeigen wir das CAPTCHA an und brechen ab
          else if (!_captchaSolved && !_showExternalLinkInfo) {
            _log.i("CAPTCHA nicht gelöst, zeige CAPTCHA an");

            // Setze den CAPTCHA-Status
            setState(() {
              _captchaDetected = true;
            });

            await _scrollToCaptchaAndHighlight();
            return; // Beende die Methode nur in diesem Fall
          }
        }
      } else {
        // E-Mail gefunden, CAPTCHA ist gelöst
        if (mounted) {
          setState(() {
            _captchaSolved = true;
          });
        }
      }
    }

    if (_isExtracting) return;

    setState(() {
      _isExtracting = true; // Startet den *Gesamtprozess*
      _isGenerating = true; // Für die KIAnschreibenButton-Animation
      _generationCompletedSuccessfully =
          false; // Bei jedem neuen Start zurücksetzen
      _captchaSolved =
          true; // WICHTIG: Setze CAPTCHA immer als gelöst, wenn der Benutzer auf "Neues Anschreiben generieren" klickt
    });
    // Animationen starten
    _buttonAnimationController.repeat(reverse: true);
    _gradientSweepController.repeat(); // Sweep starten (nicht reversierend)

    String? jobText = '';
    bool captchaDetected = false;
    bool proceedWithGeneration = true; // Flag, ob weitergemacht werden soll

    // 1. Text extrahieren und auf Captcha prüfen
    try {
      final String? extractedJobText = await _extractTextFromWebView();
      // KORREKTUR: Null-Check hinzufügen
      if (extractedJobText == null || extractedJobText.isEmpty) {
        throw Exception('Konnte keinen Text von der Webseite extrahieren.');
      }
      _extractedJobText = extractedJobText; // Jetzt sicher, da nicht null/leer

      // CAPTCHA-Prüfung nur durchführen, wenn wir nicht auf einer externen Seite sind
      if (!_showExternalLinkInfo) {
        captchaDetected = await _checkForCaptcha(); // Ruft interne Logik auf
        _captchaDetected = captchaDetected; // State aktualisieren
        _log.i(
          "Extraktion & Captcha-Check - Text (Länge: ${extractedJobText.length}), Captcha: $captchaDetected",
        );
      } else {
        _log.i("Externe Seite erkannt, überspringe CAPTCHA-Prüfung");
        captchaDetected = false;
        _captchaDetected = false;
        _captchaSolved = true;
      }

      // Wenn ein CAPTCHA erkannt wurde und wir nicht auf einer externen Seite sind, müssen wir prüfen, ob es gelöst werden muss
      if (captchaDetected && !_showExternalLinkInfo) {
        // Prüfe, ob die E-Mail-Adresse des Arbeitgebers im Text gefunden werden kann
        final String? extractedEmail = _findEmailInText(extractedJobText);

        if (extractedEmail == null || extractedEmail.isEmpty) {
          _log.i(
            "Keine E-Mail im Text gefunden, aber wir fahren trotzdem fort",
          );

          // Wenn das CAPTCHA bereits als gelöst markiert wurde, behalten wir diesen Status bei
          if (!_captchaSolved) {
            setState(() {
              _captchaDetected = true;
              // Wir setzen _captchaSolved nicht auf false, wenn es bereits true ist
            });
          }

          // Wenn der Benutzer explizit auf den "CAPTCHA gelöst"-Button geklickt hat,
          // betrachten wir das CAPTCHA als gelöst, auch wenn keine E-Mail gefunden wurde
          if (_captchaSolved) {
            _log.i(
              "CAPTCHA wurde vom Benutzer als gelöst markiert, fahre mit Generierung fort",
            );
            // Wir setzen proceedWithGeneration nicht auf false, damit die Generierung fortgesetzt wird
          }
          // Nur wenn das CAPTCHA nicht als gelöst markiert wurde, zeigen wir es an und brechen ab
          else if (!_captchaSolved && !_showExternalLinkInfo) {
            _log.i("CAPTCHA nicht gelöst, zeige CAPTCHA an");
            await _scrollToCaptchaAndHighlight();
            proceedWithGeneration = false;
          } else if (_showExternalLinkInfo) {
            _log.i(
              "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration (extractedJobText)",
            );
            // Setze den CAPTCHA-Status explizit
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
            // Wir setzen proceedWithGeneration nicht auf false, damit die Generierung fortgesetzt wird
          }
        } else {
          // E-Mail gefunden, CAPTCHA ist gelöst
          _log.i(
            "E-Mail im Text gefunden: $extractedEmail, CAPTCHA wird als gelöst markiert",
          );
          setState(() {
            _captchaSolved = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        _showGeneratedApplicationDialog(context, {'error': e.toString()});
      }
      proceedWithGeneration = false;
    }

    // 2. Bei Captcha: Setze den Status und beende den Prozess (nur wenn nicht auf einer externen Seite)
    if (proceedWithGeneration &&
        captchaDetected &&
        !_captchaSolved &&
        !_showExternalLinkInfo) {
      // Setze den CAPTCHA-Status, damit der Button entsprechend angezeigt wird
      if (mounted) {
        setState(() {
          _captchaDetected = true;
        });

        // Beende den aktuellen Prozess, damit der Benutzer den CAPTCHA-Button sieht
        proceedWithGeneration = false;
      } else {
        proceedWithGeneration = false;
      }
    }

    // Wenn das CAPTCHA bereits gelöst wurde oder kein CAPTCHA vorhanden ist, fahre mit dem normalen Ablauf fort
    if (proceedWithGeneration) {
      // 3. Auf existierendes Anschreiben prüfen
      Map<String, dynamic>? existingApplicationData =
          await _checkExistingApplication();
      if (existingApplicationData != null) {
        _log.i("Prüfung existierendes Anschreiben: Vorhanden");
        final String existingText =
            existingApplicationData['generatedText'] as String;
        final String? modelType =
            existingApplicationData['modelType'] as String?;
        final choice = await _showUseExistingApplicationDialog(
          context,
          existingText,
          modelType: modelType,
        );
        if (choice == 'cancel') {
          _log.i("Wahl: Abbruch");
          proceedWithGeneration = false;
        } else if (choice == 'use_existing') {
          _log.i("Wahl: Bestehendes verwenden");
          if (mounted) {
            // Zeige das gespeicherte Anschreiben mit der gespeicherten E-Mail-Adresse an
            _showGeneratedApplicationDialog(context, existingApplicationData);
          }
          proceedWithGeneration = false; // Nicht neu generieren
        } else {
          _log.i("Wahl: Neu generieren");
          // proceedWithGeneration bleibt true
        }
      }

      // 4. Premium/Ad-Check (NUR wenn neu generiert werden soll)
      if (proceedWithGeneration) {
        // Premium-Check
        final bool isPremium = ref.read(isPremiumProvider);
        _log.i("Premium/Ad Check - isPremium: $isPremium");

        if (!isPremium) {
          // NICHT Premium: Direkt mit Generierung fortfahren (keine Werbung mehr erforderlich)
          if (!mounted) return; // Sicherheitscheck

          _log.i("Free-User: Starte Bewerbungsgenerierung direkt ohne Werbung");

          // State für Animation setzen und Animation starten
          setState(() {
            _isExtracting =
                true; // Zeigt "Generiere..." an und blockiert Button
            _isGenerating = true; // Für die KIAnschreibenButton-Animation
            _generationCompletedSuccessfully =
                false; // Sicherstellen, dass zurückgesetzt
          });
          _buttonAnimationController.repeat(reverse: true);
          _gradientSweepController.repeat(); // Sweep starten

          // Direkt mit der Generierung fortfahren
          _startGenerationProcess();
          return;
        }
        // Wenn isPremium true ist, wird proceedWithGeneration nicht verändert und es geht weiter
      }

      // 5. Generierung starten (wenn proceedWithGeneration immer noch true ist)
      if (proceedWithGeneration) {
        // Dieser Block wird nur noch erreicht, wenn isPremium true war ODER
        // wenn die Ad-Logik fehlerhaft ist und proceed nicht auf false gesetzt wurde.
        _log.i(
          "Starte _startGenerationProcess (isPremium war true oder Fehler in Ad-Logik)",
        );
        await _startGenerationProcess();
      }
    }

    // Aufräumen (wird erreicht nach Abbruch oder nach _startGenerationProcess)
    if (mounted && !_isLoadingAd) {
      // Nur aufräumen, wenn nicht gerade Ad lädt
      _buttonAnimationController.stop();
      _buttonAnimationController.reset(); // Animation zurücksetzen
      _gradientSweepController.stop(); // Sweep stoppen
      _gradientSweepController.reset(); // Sweep zurücksetzen
      setState(() {
        _isExtracting = false; // Gesamtprozess beenden
        _isGenerating = false; // KIAnschreibenButton-Animation beenden
      });
      _log.i(
        "_triggerApplicationGeneration abgeschlossen oder abgebrochen, UI aufgeräumt",
      );
    }
  }

  // Hilfsmethode, die den eigentlichen Generierungsprozess startet
  Future<void> _startGenerationProcess({bool isExternalPage = false}) async {
    _log.i(
      '🔥 DEBUG: _startGenerationProcess() gestartet, isExternalPage: $isExternalPage',
    );
    if (_extractedJobText.isNotEmpty) {
      // Mounted-Check vor ref-Zugriff
      if (!mounted) {
        _log.w('Widget wurde disposed, breche _startGenerationProcess ab');
        return;
      }

      // Prüfe, ob der Benutzer noch Bewerbungen übrig hat (defensiv)
      final remainingApplications = await ref.read(
        remainingApplicationsProvider.future,
      );
      final int remaining = (remainingApplications['remaining'] as int?) ?? 0;
      final bool unlimited = remainingApplications['unlimited'] == true;
      final hasRemainingApplications = unlimited || remaining > 0;

      if (!hasRemainingApplications) {
        // Zeige Dialog, dass keine Bewerbungen mehr übrig sind
        if (mounted) {
          // Zeige einen Dialog an
          showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: const Text('Keine Bewerbungen mehr übrig'),
                  content: const Text(
                    'Du hast keine Bewerbungen mehr übrig. Upgrade auf ein höheres Abonnement, um mehr Bewerbungen zu erhalten.',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Abbrechen'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        context.go('/premium-management');
                      },
                      child: const Text('Upgrade'),
                    ),
                  ],
                ),
          );

          // Aufräumen und Prozess abbrechen
          if (mounted) {
            setState(() {
              _isExtracting = false;
              _isGenerating = false;
              _isLoadingAd = false;
            });
            _buttonAnimationController.stop();
            _buttonAnimationController.reset();
            _gradientSweepController.stop();
            _gradientSweepController.reset();
          }
          return;
        }
      }

      // HINWEIS: Guthaben-Abzug wird in _generateApplicationWithAI() durchgeführt
      // Entfernt doppelten Abzug für interne Seiten

      // Rufe direkt die KI-Generierung auf
      _log.i(
        "Aufruf _generateApplicationWithAI mit jobText (Länge: ${_extractedJobText.length}), isExternalPage: $isExternalPage",
      );

      // Wenn wir auf einer externen Seite sind, setzen wir den CAPTCHA-Status explizit
      if (isExternalPage) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }

      // Starte die zweite Werbung während der Generierung
      _showSecondAdDuringGeneration();

      _log.i('🔥 DEBUG: Rufe _generateApplicationWithAI() auf...');
      final result = await _generateApplicationWithAI(_extractedJobText);
      _log.i(
        '🔥 DEBUG: _generateApplicationWithAI() abgeschlossen, result: ${result.keys}',
      );

      // Speichere das NEU generierte Anschreiben mit E-Mail-Adresse und Modell
      if (result['generatedText'] != null && result['error'] == null) {
        final String generatedText = result['generatedText'] as String;
        final String? extractedEmail = result['extractedEmail'] as String?;
        final String? modelType = result['modelType'] as String?;

        await _saveGeneratedApplication(
          generatedText,
          extractedEmail: extractedEmail,
          modelType: modelType,
        );

        _log.i(
          "Neu generiertes Anschreiben gespeichert ${extractedEmail != null ? 'mit E-Mail-Adresse' : 'ohne E-Mail-Adresse'}.",
        );

        // Zähle die Bewerbung erst nach erfolgreicher Generierung
        // WICHTIG: Verwende den GLEICHEN Service wie die UI (SubscriptionManagementService)
        final subscriptionService = ref.read(
          subscriptionManagementServiceProvider,
        );
        final success = await subscriptionService.incrementApplicationCounter();

        if (success) {
          _log.i(
            "🔥 GUTHABEN-ABZUG: Bewerbung erfolgreich gezählt mit SubscriptionManagementService",
          );

          // KRITISCH: Provider sofort invalidieren für UI-Update
          _log.i(
            '🔄 GUTHABEN-ABZUG: Invalidiere Provider für sofortiges UI-Update',
          );
          ref.invalidate(remainingApplicationsProvider);
          ref.invalidate(remainingApplicationsNotifierProvider);
          _log.i(
            '✅ GUTHABEN-ABZUG: Provider invalidiert - UI sollte sich sofort aktualisieren',
          );
        } else {
          _log.e("❌ GUTHABEN-ABZUG: Fehler beim Zählen der Bewerbung");
        }

        // Setze den CAPTCHA-Status zurück, aber nur wenn wir nicht auf einer externen Seite sind
        if (!isExternalPage) {
          _captchaSolved = false;
        }
      }

      if (mounted) {
        // Wenn wir auf einer externen Seite sind oder keine E-Mail gefunden wurde, fügen wir eine Dummy-E-Mail hinzu
        if ((isExternalPage || _captchaSolved) &&
            result['extractedEmail'] == null &&
            result['error'] == null) {
          // Kopiere das Ergebnis und füge eine Dummy-E-Mail hinzu
          final Map<String, dynamic> modifiedResult = Map.from(result);

          // Für externe Seiten KEINE Dummy-E-Mail mehr verwenden
          if (isExternalPage) {
            // E-Mail-Feld bleibt leer (null)
            _log.i("Externe Seite: E-Mail-Feld bleibt leer");
          }
          // Für gelöste CAPTCHAs ohne E-Mail verwenden wir eine andere Dummy-E-Mail
          else if (_captchaSolved) {
            modifiedResult['extractedEmail'] = '<EMAIL>';
            _log.i(
              "CAPTCHA gelöst, aber keine E-Mail gefunden: Dummy-E-Mail '<EMAIL>' hinzugefügt",
            );
          }

          _showGeneratedApplicationDialog(context, modifiedResult);
        } else {
          _showGeneratedApplicationDialog(context, result);
        }
      }
    } else {
      if (mounted) {
        _showGeneratedApplicationDialog(context, {
          'error':
              'Konnte keinen Text von der Webseite extrahieren (in _startGenerationProcess).',
        });
      }
    }
    // Hier wird _isExtracting nicht mehr auf false gesetzt, das passiert in _triggerApplicationGeneration
  }

  // ... _buildPrompt, _handleApplication, _applyForJob, build method ...

  // ... Rest der Methoden ...

  String _cleanupHints(String text) {
    String cleanedText = text; // Start with original text

    // ... (Rest der cleanupHints-Methode bleibt gleich) ...

    return cleanedText.trim();
  }

  Future<void> _launchUrl(String urlString) async {
    final uri = Uri.parse(urlString);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      _log.e('Could not launch $urlString');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Konnte URL nicht öffnen: $urlString')),
        );
      }
    }
  }

  // Dialog zur Auswahl des KI-Modells entfernt

  // Angepasster Dialog zum Anzeigen des Ergebnisses (bleibt bestehen)
  void _showGeneratedApplicationDialog(
    BuildContext context,
    Map<String, dynamic> result,
  ) {
    final String? error = result['error'] as String?;
    final String generatedText = result['generatedText'] as String? ?? '';
    final String? extractedEmail = result['extractedEmail'] as String?;
    final String? modelType = result['modelType'] as String?; // Neu: Modelltyp
    bool isError = error != null;
    bool showCaptchaWarning =
        _captchaDetected &&
        (extractedEmail == null || extractedEmail.isEmpty) &&
        !isError &&
        !_showExternalLinkInfo; // Keine CAPTCHA-Warnung auf externen Seiten

    // *** NEU: Erfolgsstatus setzen, wenn kein Fehler ***
    if (!isError && mounted) {
      setState(() {
        _generationCompletedSuccessfully = true;
      });
    }

    // Log des verwendeten Modells
    if (modelType != null) {
      _log.i("Anschreiben wurde mit $modelType generiert");
    }

    String contentToShow = isError ? error : generatedText;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: Theme.of(context).colorScheme.surface,
            title: Text(isError ? 'Fehler' : 'KI-Bewerbungsentwurf'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Getrennte Captcha-Warnung
                  if (showCaptchaWarning)
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      margin: const EdgeInsets.only(bottom: 12.0),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.orange.shade900.withValues(alpha: 77)
                                : Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? Colors.orange.shade700
                                  : Colors.orange.shade300,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.orange.shade300
                                    : Colors.orange.shade800,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'CAPTCHA erkannt! E-Mail-Adresse konnte nicht extrahiert werden. Bitte manuell prüfen.',
                              style: TextStyle(
                                color:
                                    Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.orange.shade300
                                        : Colors.orange.shade800,
                                fontSize: 13,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  // Modelltyp-Anzeige entfernt
                  // Eigentlicher Text
                  SelectableText(
                    contentToShow,
                  ), // SelectableText für einfaches Kopieren
                ],
              ),
            ),
            actions: [
              // Schließen-Button
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Schließen'),
              ),
              // Kopieren-Button (nur wenn kein Fehler)
              if (!isError && generatedText.isNotEmpty)
                TextButton.icon(
                  icon: const Icon(Icons.copy, size: 16),
                  label: const Text('Kopieren'),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: generatedText));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Bewerbungstext kopiert!')),
                    );

                    // Job als beworben markieren, wenn JobEntity vorhanden ist
                    if (widget.jobEntity != null) {
                      ref
                          .read(appliedJobsProvider.notifier)
                          .markJobAsApplied(
                            widget.jobEntity!.id,
                            jobEntity: widget.jobEntity,
                          );
                      _log.i(
                        "Job ${widget.jobEntity!.id} als beworben markiert nach Kopieren des Anschreibens",
                      );
                    }
                  },
                ),
              // E-Mail-Buttons (nur wenn kein Fehler)
              if (!isError && generatedText.isNotEmpty)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Direkt bewerben Button (wichtiger, daher zuerst und größer)
                    ElevatedButton.icon(
                      icon: const Icon(Icons.send),
                      label: const Text('Direkt bewerben'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onPressed: () async {
                        _log.i("'Direkt bewerben' geklickt");
                        _log.i(
                          "Wert von extractedEmail im Dialog: $extractedEmail",
                        );

                        // Prüfe, ob der Benutzer ein kostenloses Abonnement hat
                        final subscriptionService = ref.read(
                          subscriptionManagementServiceProvider,
                        );
                        final subscription =
                            await subscriptionService.getCurrentSubscription();

                        if (subscription?.planType?.toLowerCase() == 'free') {
                          // Zeige einen Dialog an, dass diese Funktion nur für Pro- und Unlimited-Abonnenten verfügbar ist
                          if (mounted) {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  title: const Text('Premium-Funktion'),
                                  content: const Text(
                                    'Die "Direkt bewerben"-Funktion ist nur für Pro- und Unlimited-Abonnenten verfügbar. Upgrade jetzt, um diese Funktion zu nutzen!',
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                        // Navigiere zur Premium-Verwaltungsseite
                                        Navigator.of(
                                          context,
                                        ).pushNamed('/premium-management');
                                      },
                                      child: const Text('Upgraden'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('Abbrechen'),
                                    ),
                                  ],
                                );
                              },
                            );
                            return;
                          }
                        }

                        // Bestätigungsdialog anzeigen
                        // Hole die E-Mail-Adresse des Benutzers
                        final userProfileState = ref.read(userProfileProvider);
                        String userEmail = "Deine E-Mail-Adresse";

                        if (userProfileState is AsyncData<UserProfile?> &&
                            userProfileState.value != null) {
                          userEmail =
                              userProfileState.value!.email ??
                              "Keine E-Mail verfügbar";
                        }

                        // Erstelle einen TextEditingController für die E-Mail-Adresse
                        final emailController = TextEditingController(
                          text: extractedEmail ?? '',
                        );

                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              backgroundColor:
                                  Theme.of(context).colorScheme.surface,
                              title: Row(
                                children: [
                                  Icon(
                                    Icons.email,
                                    color: AppTheme.primaryColor,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Bewerbung senden'),
                                ],
                              ),
                              content: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxHeight:
                                      MediaQuery.of(context).size.height * 0.5,
                                  maxWidth:
                                      MediaQuery.of(context).size.width * 0.8,
                                ),
                                child: SingleChildScrollView(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Warnhinweis
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color:
                                              Theme.of(context).brightness ==
                                                      Brightness.dark
                                                  ? Colors.amber.shade900
                                                      .withValues(alpha: 77)
                                                  : Colors.amber.shade100,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Colors.amber.shade700
                                                    : Colors.amber.shade300,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.warning_amber_rounded,
                                                  color: Colors.amber.shade800,
                                                  size: 20,
                                                ),
                                                const SizedBox(width: 8),
                                                const Text(
                                                  'Wichtiger Hinweis:',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            const Text(
                                              'Die Bewerbung wird direkt gesendet und kann nicht rückgängig gemacht werden.',
                                              style: TextStyle(fontSize: 13),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 12),
                                      const Text(
                                        'Die E-Mail wird automatisch mit deinem Lebenslauf an den Arbeitgeber gesendet.',
                                        style: TextStyle(fontSize: 14),
                                      ),
                                      const SizedBox(height: 12),
                                      const Text(
                                        'Absender:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(userEmail),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'Empfänger:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      // Textfeld für die E-Mail-Adresse
                                      TextField(
                                        controller: emailController,
                                        decoration: InputDecoration(
                                          hintText: 'E-Mail-Adresse eingeben',
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 8,
                                              ),
                                          isDense: true,
                                          border: OutlineInputBorder(),
                                          helperText:
                                              'Andere E-Mail-Adresse eingeben',
                                          prefixIcon: Icon(Icons.edit),
                                        ),
                                        keyboardType:
                                            TextInputType.emailAddress,
                                        autocorrect: false,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Abbrechen'),
                                ),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.send),
                                  label: const Text('Jetzt senden'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () {
                                    // Verwende die bearbeitete E-Mail-Adresse
                                    final editedEmail =
                                        emailController.text.trim();

                                    // Schließe zuerst den "Bewerbung senden" Dialog
                                    Navigator.of(context).pop();

                                    // Schließe dann auch den "KI-Bewerbungsentwurf" Dialog
                                    Navigator.of(context).pop();

                                    // Sende die Bewerbung
                                    _sendDirectApplication(
                                      generatedText,
                                      extractedEmail:
                                          editedEmail.isNotEmpty
                                              ? editedEmail
                                              : extractedEmail,
                                      modelType: modelType,
                                    );

                                    // Keine Erfolgsmeldung hier, da sie in _sendDirectApplication angezeigt wird
                                  },
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    // E-Mail-App öffnen Button (als Alternative)
                    OutlinedButton.icon(
                      icon: const Icon(Icons.email_outlined),
                      label: const Text('E-Mail-App öffnen'),
                      onPressed: () {
                        _log.i("'E-Mail-App öffnen' geklickt");
                        _log.i(
                          "Wert von extractedEmail im Dialog: $extractedEmail",
                        );

                        // Erstelle einen TextEditingController für die E-Mail-Adresse
                        final emailController = TextEditingController(
                          text: extractedEmail ?? '',
                        );

                        // Zeige Dialog zur Bearbeitung der E-Mail-Adresse
                        showDialog(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                backgroundColor:
                                    Theme.of(context).colorScheme.surface,
                                title: const Text('E-Mail-Adresse bearbeiten'),
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Bitte gib die E-Mail-Adresse ein, an die die Bewerbung gesendet werden soll:',
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      controller: emailController,
                                      decoration: const InputDecoration(
                                        hintText: 'E-Mail-Adresse eingeben',
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 8,
                                        ),
                                        isDense: true,
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType: TextInputType.emailAddress,
                                      autocorrect: false,
                                    ),
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(),
                                    child: const Text('Abbrechen'),
                                  ),
                                  ElevatedButton(
                                    onPressed: () {
                                      // Verwende die bearbeitete E-Mail-Adresse
                                      final editedEmail =
                                          emailController.text.trim();

                                      // Schließe zuerst den "E-Mail-Adresse bearbeiten" Dialog
                                      Navigator.of(context).pop();

                                      // Schließe dann auch den "KI-Bewerbungsentwurf" Dialog
                                      Navigator.of(context).pop();

                                      // 🔥 KRITISCHER DEBUG: Prüfe generatedText vor _shareViaEmail
                                      _log.i(
                                        '🔥 KRITISCHER DEBUG: Vor _shareViaEmail - generatedText: "${generatedText.length} Zeichen"',
                                      );
                                      if (generatedText.isNotEmpty) {
                                        _log.i(
                                          '🔥 KRITISCHER DEBUG: generatedText Vorschau: "${generatedText.substring(0, generatedText.length > 100 ? 100 : generatedText.length)}..."',
                                        );
                                      } else {
                                        _log.e(
                                          '🔥 KRITISCHER FEHLER: generatedText ist LEER vor _shareViaEmail!',
                                        );
                                      }

                                      _shareViaEmail(
                                        generatedText,
                                        extractedEmail:
                                            editedEmail.isNotEmpty
                                                ? editedEmail
                                                : extractedEmail,
                                        modelType: modelType,
                                      );

                                      // Job als beworben markiert
                                      _markJobAsApplied();

                                      // Keine Erfolgsmeldung hier, da sie in _shareViaEmail angezeigt wird
                                    },
                                    child: const Text('Öffnen'),
                                  ),
                                ],
                              ),
                        );
                      },
                    ),
                  ],
                ),
            ],
          ),
    );
  }

  // E-Mail Body korrigiert und Anhang hinzugefügt
  Future<void> _shareViaEmail(
    String text, {
    String? extractedEmail,
    String? modelType,
  }) async {
    try {
      final userProfileState = ref.read(userProfileProvider);
      UserProfile? userProfile;

      if (userProfileState is AsyncData<UserProfile?>) {
        userProfile = userProfileState.value;
      }

      if (userProfile == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Benutzerprofil nicht geladen. Versuche es später erneut.',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Verwende den Text ohne Signatur
      String emailBody = text;

      // 🔥 KRITISCHER DEBUG: Prüfe Anschreiben-Inhalt
      _log.i('🔥 DEBUG: _shareViaEmail aufgerufen');
      _log.i(
        '🔥 DEBUG: text Parameter (Anschreiben): "${text.length} Zeichen"',
      );
      _log.i('🔥 DEBUG: emailBody gesetzt: "${emailBody.length} Zeichen"');
      if (text.isNotEmpty) {
        _log.i(
          '🔥 DEBUG: Anschreiben-Vorschau: "${text.substring(0, text.length > 100 ? 100 : text.length)}..."',
        );
      } else {
        _log.e('🔥 KRITISCHER FEHLER: text Parameter ist LEER!');
      }

      // Wenn keine E-Mail-Adresse extrahiert wurde, versuche sie aus dem Jobtext zu extrahieren
      if (extractedEmail == null || extractedEmail.isEmpty) {
        _log.i(
          "Keine E-Mail-Adresse übergeben, versuche sie aus dem Jobtext zu extrahieren",
        );

        // Versuche, die E-Mail-Adresse aus dem Jobtext zu extrahieren
        final String? fallbackEmail = _extractEmailFromJobText();

        if (fallbackEmail != null && fallbackEmail.isNotEmpty) {
          extractedEmail = fallbackEmail;
          _log.i("E-Mail-Adresse aus Jobtext extrahiert: $extractedEmail");

          // Speichere die extrahierte E-Mail-Adresse für zukünftige Verwendung
          if (_extractedJobText.isNotEmpty) {
            await _saveGeneratedApplication(
              text,
              extractedEmail: fallbackEmail,
            );
            _log.i(
              "Anschreiben mit nachträglich extrahierter E-Mail-Adresse gespeichert",
            );
          }
        } else {
          _log.w(
            "Keine E-Mail-Adresse gefunden, weder übergeben noch im Jobtext",
          );
        }
      } else {
        _log.i("Verwende übergebene E-Mail-Adresse: $extractedEmail");
      }

      // NEU: Anzeigen, dass wir die Datei herunterladen
      setState(() => _isDownloadingCv = true);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Lebenslauf wird für E-Mail-Anhang vorbereitet..."),
        ),
      );

      // Lokalen Pfad für den CV holen, falls vorhanden
      String? localCvPath;

      try {
        _log.i("Versuche Lebenslauf für E-Mail-Anhang zu finden...");

        // Verwende den CvStorageHelper, um den Lebenslauf zu finden oder herunterzuladen
        localCvPath = await CvStorageHelper.ensureLocalCvAvailable(
          userProfile.id,
          userProfile.cvFilePath,
          userProfile.cvDownloadUrl,
        );

        if (localCvPath != null) {
          _log.i("Lebenslauf für E-Mail-Anhang gefunden: $localCvPath");

          // Aktualisiere das Profil mit dem lokalen Pfad, falls er sich geändert hat
          if (userProfile.cvFilePath != localCvPath) {
            _log.i("Aktualisiere lokalen Pfad im Profil: $localCvPath");
            final userProfileNotifier = ref.read(userProfileProvider.notifier);
            await userProfileNotifier.updateCvInfo(
              userProfile.cvDownloadUrl ?? '',
              localCvPath,
            );
          }
        } else {
          _log.w("Kein Lebenslauf gefunden (weder lokal noch über URL)");
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Kein Lebenslauf gefunden. Bitte laden Sie einen Lebenslauf in Ihrem Profil hoch.',
                ),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: 'Zum Profil',
                  onPressed: () {
                    // Navigiere zum Profil-Tab
                    context.go('/profile');
                  },
                ),
              ),
            );
          }
        }
      } catch (e) {
        _log.e("Fehler beim Suchen/Herunterladen des Lebenslaufs: $e");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Laden des Lebenslaufs: ${e.toString()}',
              ),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Erneut versuchen',
                onPressed: () {
                  // Setze den Ladezustand zurück und versuche es erneut
                  setState(() => _isDownloadingCv = true);
                  // Rufe die aktuelle Methode erneut auf
                  _shareViaEmail(text, extractedEmail: extractedEmail);
                },
              ),
            ),
          );
        }
      } finally {
        setState(() => _isDownloadingCv = false);
      }

      // Bereitstellen der E-Mail-Parameter
      // Stelle sicher, dass extractedEmail nicht null oder leer ist
      if (extractedEmail == null || extractedEmail.isEmpty) {
        _log.w("Keine E-Mail-Adresse gefunden, versuche erneut zu extrahieren");
        extractedEmail = _extractEmailFromJobText();
        _log.i("Erneut extrahierte E-Mail-Adresse: $extractedEmail");

        // Wenn immer noch keine E-Mail-Adresse gefunden wurde, verwende eine Fallback-E-Mail-Adresse
        // basierend auf dem Firmennamen, falls vorhanden
        if ((extractedEmail == null || extractedEmail.isEmpty) &&
            widget.jobEntity?.companyName != null) {
          final String companyName = widget.jobEntity!.companyName;
          // Erstelle eine Fallback-E-Mail-Adresse basierend auf dem Firmennamen
          final String cleanCompanyName =
              companyName
                  .toLowerCase()
                  .replaceAll(
                    RegExp(r'[^a-z0-9]'),
                    '',
                  ) // Entferne alle Nicht-Alphanumerischen Zeichen
                  .trim();

          if (cleanCompanyName.isNotEmpty) {
            extractedEmail = 'bewerbung@$cleanCompanyName.de';
            _log.i(
              "Fallback-E-Mail-Adresse basierend auf Firmennamen erstellt: $extractedEmail",
            );
          } else {
            // Wenn der Firmenname keine gültigen Zeichen enthält, verwende eine generische E-Mail-Adresse
            extractedEmail = '<EMAIL>';
            _log.i(
              "Generische Fallback-E-Mail-Adresse verwendet: $extractedEmail",
            );
          }
        } else if (extractedEmail == null || extractedEmail.isEmpty) {
          // Wenn keine Firma bekannt ist, verwende eine generische E-Mail-Adresse
          extractedEmail = '<EMAIL>';
          _log.i(
            "Generische Fallback-E-Mail-Adresse verwendet: $extractedEmail",
          );
        }
      }

      // Erstelle temporäre Kopie mit gewünschtem Dateinamen
      String? tempCvPath;
      if (localCvPath != null) {
        tempCvPath = await CvStorageHelper.createTemporaryCvCopy(
          localCvPath,
          'Lebenslauf.pdf',
        );
      }

      // Lade zusätzliche Dokumente und erstelle temporäre Dateien
      List<String> allAttachmentPaths = [];

      // Prüfe ob CV für Bewerbungen aktiviert ist
      final isCvActiveForApplications = ref.read(cvApplicationStatusProvider);
      if (tempCvPath != null && isCvActiveForApplications) {
        allAttachmentPaths.add(tempCvPath);
      }

      try {
        final additionalDocsService = ref.read(
          additionalDocumentsServiceProvider,
        );
        final additionalDocs =
            await additionalDocsService.getActiveDocumentsForApplications();

        for (final doc in additionalDocs) {
          try {
            final docBytes = await additionalDocsService.downloadDocument(
              doc.id,
            );
            if (docBytes != null) {
              // Erstelle temporäre Datei
              final tempDir = await getTemporaryDirectory();
              final tempFile = File('${tempDir.path}/${doc.fileName}');
              await tempFile.writeAsBytes(docBytes);
              allAttachmentPaths.add(tempFile.path);
              _log.i(
                'Zusätzliches Dokument als temporäre Datei erstellt: ${tempFile.path}',
              );
            }
          } catch (e) {
            _log.w('Fehler beim Laden des Dokuments ${doc.fileName}: $e');
          }
        }
      } catch (e) {
        _log.w('Fehler beim Laden zusätzlicher Dokumente: $e');
      }

      final Email email = Email(
        body: emailBody,
        subject: 'Bewerbung für: ${widget.jobTitle}',
        recipients: [
          extractedEmail, // Jetzt ist extractedEmail garantiert nicht null
        ],
        // Verwende alle Anhänge (CV + zusätzliche Dokumente)
        attachmentPaths: allAttachmentPaths,
        isHTML: false,
      );

      _log.i("--- Preparing email with flutter_email_sender ---");
      _log.i("Recipient: ${email.recipients}");
      _log.i("Subject: ${email.subject}");
      _log.i(
        "🔥 KRITISCHER DEBUG: Email.body: '${email.body}' (${email.body.length ?? 0} Zeichen)",
      );
      _log.i(
        "Attachment Paths (${allAttachmentPaths.length}): ${email.attachmentPaths}",
      );

      // Debug: Überprüfe alle Anhänge
      for (int i = 0; i < allAttachmentPaths.length; i++) {
        final file = File(allAttachmentPaths[i]);
        final exists = await file.exists();
        final size = exists ? await file.length() : 0;
        _log.i(
          "Anhang $i: ${allAttachmentPaths[i]} (existiert: $exists, Größe: $size Bytes)",
        );
      }

      // VERBESSERTE FEHLERBEHANDLUNG
      try {
        // 🔥 KRITISCHER FIX: Starte EmailIntentTracker BEVOR E-Mail gesendet wird
        if (tempCvPath != null) {
          _log.i('🔥 EmailIntentTracker: Starte Tracking für: $tempCvPath');
          await EmailIntentTracker.startEmailIntent(
            tempFilePath: tempCvPath,
            timeoutMinutes: 10, // 10 Minuten Fallback-Timer
          );
        }

        // Verwende native Android-Implementierung für E-Mail-only Intent
        await _sendEmailOnlyApps(email);

        // 🔥 KRITISCHER FIX: KEINE sofortige Löschung mehr!
        // Die temporären Dateien werden jetzt vom EmailIntentTracker verwaltet
        // und erst gelöscht, wenn die E-Mail-App fertig ist
        _log.i(
          '🔥 E-Mail-Intent gestartet - temporäre Dateien werden vom EmailIntentTracker verwaltet',
        );

        // --- ERFOLGSMELDUNG ENTFERNT ---
        // if (mounted) {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(
        //       content: const Text('E-Mail-App wurde geöffnet. Überprüfen Sie Ihre E-Mail-App, um die Nachricht zu senden.'),
        //       backgroundColor: Colors.green,
        //       duration: const Duration(seconds: 3),
        //     ),
        //   );
        // }
        // --- ENDE ERFOLGSMELDUNG ENTFERNT ---
      } on PlatformException catch (pe) {
        _log.e(
          'Platform Exception beim E-Mail-Versand: ${pe.message}',
          error: pe,
        );

        // 🔥 KRITISCHER FIX: Bei Fehlern EmailIntentTracker-Cleanup erzwingen
        _log.i(
          '🔥 E-Mail-Intent fehlgeschlagen - erzwinge EmailIntentTracker-Cleanup',
        );
        await EmailIntentTracker.forceCleanup();

        // Zusätzlich: Fallback-Cleanup für andere temporäre Dateien
        await _cleanupTemporaryFiles(
          allAttachmentPaths,
          null,
        ); // tempCvPath wird vom Tracker verwaltet

        if (mounted) {
          // Benutzerfreundliche Fehlermeldung anzeigen
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'E-Mail konnte nicht gesendet werden: ${_getReadableEmailError(pe.message)}',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Alternative',
                onPressed: () {
                  // Alternative Methode anbieten - z.B. Text in Zwischenablage kopieren
                  Clipboard.setData(ClipboardData(text: emailBody));
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Text wurde in die Zwischenablage kopiert',
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          );
        }
      } catch (e) {
        _log.e('Allgemeiner Fehler beim E-Mail-Versand: $e');

        // 🔥 KRITISCHER FIX: Bei Fehlern EmailIntentTracker-Cleanup erzwingen
        _log.i(
          '🔥 Allgemeiner E-Mail-Fehler - erzwinge EmailIntentTracker-Cleanup',
        );
        await EmailIntentTracker.forceCleanup();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'E-Mail konnte nicht geöffnet werden: ${e.toString()}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stack) {
      _log.e('Fehler beim Vorbereiten der E-Mail: $e\\n$stack');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Teilen per E-Mail: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Hilfsmethode zum Markieren eines Jobs als beworben
  void _markJobAsApplied() {
    // Job als beworben markieren, wenn JobEntity vorhanden ist
    if (widget.jobEntity != null) {
      ref
          .read(appliedJobsProvider.notifier)
          .markJobAsApplied(widget.jobEntity!.id, jobEntity: widget.jobEntity);
      _log.i("Job ${widget.jobEntity!.id} als beworben markiert");
    }
  }

  // Hilfsmethode zum direkten Senden einer Bewerbung
  Future<void> _sendDirectApplication(
    String text, {
    String? extractedEmail,
    String? modelType,
  }) async {
    if (mounted) {
      // Zeige Ladeindikator mit verbesserter Animation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutCubic,
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      "Bewerbung wird gesendet...",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(
            seconds: 60,
          ), // Lange Dauer, wird manuell geschlossen
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          margin: EdgeInsets.only(
            bottom:
                MediaQuery.of(context).viewPadding.bottom > 0
                    ? MediaQuery.of(context).viewPadding.bottom
                    : 16,
            left: 8,
            right: 8,
          ),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }

    try {
      final userProfileState = ref.read(userProfileProvider);
      UserProfile? userProfile;

      if (userProfileState is AsyncData<UserProfile?>) {
        userProfile = userProfileState.value;
      }

      if (userProfile == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Benutzerprofil nicht geladen. Versuche es später erneut.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Prüfe, ob der Benutzer eine E-Mail-Adresse hat
      if (userProfile.email == null || userProfile.email!.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Du hast keine E-Mail-Adresse in deinem Profil. Bitte füge eine hinzu, um Bewerbungen zu senden.',
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      // Wenn keine E-Mail-Adresse extrahiert wurde, versuche sie aus dem Jobtext zu extrahieren
      if (extractedEmail == null || extractedEmail.isEmpty) {
        _log.i(
          "Keine E-Mail-Adresse übergeben, versuche sie aus dem Jobtext zu extrahieren",
        );

        // Versuche, die E-Mail-Adresse aus dem Jobtext zu extrahieren
        final String? fallbackEmail = _extractEmailFromJobText();

        if (fallbackEmail != null && fallbackEmail.isNotEmpty) {
          extractedEmail = fallbackEmail;
          _log.i("E-Mail-Adresse aus Jobtext extrahiert: $extractedEmail");

          // Speichere die extrahierte E-Mail-Adresse für zukünftige Verwendung
          if (_extractedJobText.isNotEmpty) {
            await _saveGeneratedApplication(
              text,
              extractedEmail: fallbackEmail,
            );
            _log.i(
              "Anschreiben mit nachträglich extrahierter E-Mail-Adresse gespeichert",
            );
          }
        } else {
          _log.w(
            "Keine E-Mail-Adresse gefunden, weder übergeben noch im Jobtext",
          );
        }
      }

      // Stelle sicher, dass extractedEmail nicht null oder leer ist
      if (extractedEmail == null || extractedEmail.isEmpty) {
        _log.w("Keine E-Mail-Adresse gefunden, versuche erneut zu extrahieren");
        extractedEmail = _extractEmailFromJobText();
        _log.i("Erneut extrahierte E-Mail-Adresse: $extractedEmail");

        // Wenn immer noch keine E-Mail-Adresse gefunden wurde, verwende eine Fallback-E-Mail-Adresse
        // basierend auf dem Firmennamen, falls vorhanden
        if ((extractedEmail == null || extractedEmail.isEmpty) &&
            widget.jobEntity?.companyName != null) {
          final String companyName = widget.jobEntity!.companyName;
          // Erstelle eine Fallback-E-Mail-Adresse basierend auf dem Firmennamen
          final String cleanCompanyName =
              companyName
                  .toLowerCase()
                  .replaceAll(
                    RegExp(r'[^a-z0-9]'),
                    '',
                  ) // Entferne alle Nicht-Alphanumerischen Zeichen
                  .trim();

          if (cleanCompanyName.isNotEmpty) {
            extractedEmail = 'bewerbung@$cleanCompanyName.de';
            _log.i(
              "Fallback-E-Mail-Adresse basierend auf Firmennamen erstellt: $extractedEmail",
            );
          } else {
            // Wenn der Firmenname keine gültigen Zeichen enthält, verwende eine generische E-Mail-Adresse
            extractedEmail = '<EMAIL>';
            _log.i(
              "Generische Fallback-E-Mail-Adresse verwendet: $extractedEmail",
            );
          }
        } else if (extractedEmail == null || extractedEmail.isEmpty) {
          // Wenn keine Firma bekannt ist, verwende eine generische E-Mail-Adresse
          extractedEmail = '<EMAIL>';
          _log.i(
            "Generische Fallback-E-Mail-Adresse verwendet: $extractedEmail",
          );
        }
      }

      // Verwende den EmailService, um die E-Mail direkt zu senden
      final emailService = ref.read(emailServiceProvider);

      // Standardmäßig Gmail verwenden ohne Dialog anzuzeigen
      bool useGmail = true;

      // Keine Signatur mehr hinzufügen
      String coverLetterText = text;

      // Stelle sicher, dass der Lebenslauf lokal verfügbar ist
      String? localCvPath;
      try {
        _log.i(
          "Stelle sicher, dass Lebenslauf für E-Mail-Anhang verfügbar ist...",
        );
        localCvPath = await CvStorageHelper.ensureLocalCvAvailable(
          userProfile.id,
          userProfile.cvFilePath,
          userProfile.cvDownloadUrl,
        );

        if (localCvPath != null) {
          _log.i("Lebenslauf für E-Mail-Anhang gefunden: $localCvPath");
        } else {
          _log.w("Kein Lebenslauf gefunden (weder lokal noch über URL)");
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: const [
                    Icon(Icons.warning_amber_rounded, color: Colors.white),
                    SizedBox(width: 12),
                    Flexible(
                      child: Text(
                        'Kein Lebenslauf gefunden. Die E-Mail wird ohne Anhang gesendet.',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange,
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.only(
                  bottom:
                      MediaQuery.of(context).viewPadding.bottom > 0
                          ? MediaQuery.of(context).viewPadding.bottom
                          : 16,
                  left: 8,
                  right: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }
        }
      } catch (e) {
        _log.e("Fehler beim Suchen/Herunterladen des Lebenslaufs: $e");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.warning_amber_rounded, color: Colors.white),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      'Fehler beim Laden des Lebenslaufs: ${e.toString()}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.only(
                bottom:
                    MediaQuery.of(context).viewPadding.bottom > 0
                        ? MediaQuery.of(context).viewPadding.bottom
                        : 16,
                left: 8,
                right: 8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }

      // Sende die E-Mail mit der gewählten Methode
      final result = await emailService.sendApplicationEmail(
        recipientEmail: extractedEmail,
        jobTitle: widget.jobTitle,
        coverLetter: coverLetterText,
        cvFileId:
            localCvPath ??
            userProfile.cvFilePath, // Verwende den lokalen Pfad, wenn verfügbar
        companyName: widget.jobEntity?.companyName,
        jobId: widget.jobEntity?.id,
        useGmail: useGmail,
      );

      // Verstecke den Ladeindikator
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      // Zeige Erfolgs- oder Fehlermeldung
      if (result.success) {
        if (mounted) {
          // Markiere den Job als beworben
          _markJobAsApplied();

          // Nicht mehr nötig, das Bewerbungsfenster zu schließen, da die Dialoge bereits geschlossen wurden

          // Zeige eine animierte Erfolgsmeldung als SnackBar am unteren Bildschirmrand an
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 800),
                curve: Curves.easeOutCubic,
                builder: (context, value, child) {
                  return Opacity(
                    opacity: value,
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.white,
                            size: 24 * value,
                          ),
                          SizedBox(width: 12 * value),
                          Text(
                            'Bewerbung erfolgreich gesendet!',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
              // Verwende floating statt fixed für bessere Positionierung über der Navigationsleiste
              behavior: SnackBarBehavior.floating,
              // Füge Margin hinzu, damit die Nachricht über der Navigationsleiste angezeigt wird
              margin: EdgeInsets.only(
                bottom:
                    MediaQuery.of(context).viewPadding.bottom > 0
                        ? MediaQuery.of(context).viewPadding.bottom
                        : 16,
                left: 8,
                right: 8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      'Fehler beim Senden der Bewerbung: ${result.errorMessage}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              // Verwende floating statt fixed für bessere Positionierung über der Navigationsleiste
              behavior: SnackBarBehavior.floating,
              // Füge Margin hinzu, damit die Nachricht über der Navigationsleiste angezeigt wird
              margin: EdgeInsets.only(
                bottom:
                    MediaQuery.of(context).viewPadding.bottom > 0
                        ? MediaQuery.of(context).viewPadding.bottom
                        : 16,
                left: 8,
                right: 8,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              action: SnackBarAction(
                label: 'E-Mail-App öffnen',
                textColor: Colors.white,
                onPressed: () {
                  // Fallback: Öffne die E-Mail-App
                  _shareViaEmail(
                    text,
                    extractedEmail: extractedEmail,
                    modelType: modelType,
                  );
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      _log.e('Fehler beim direkten Senden der Bewerbung: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 12),
                Flexible(
                  child: Text(
                    'Fehler beim Senden der Bewerbung: ${e.toString()}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            // Verwende floating statt fixed für bessere Positionierung über der Navigationsleiste
            behavior: SnackBarBehavior.floating,
            // Füge Margin hinzu, damit die Nachricht über der Navigationsleiste angezeigt wird
            margin: EdgeInsets.only(
              bottom:
                  MediaQuery.of(context).viewPadding.bottom > 0
                      ? MediaQuery.of(context).viewPadding.bottom
                      : 16,
              left: 8,
              right: 8,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            action: SnackBarAction(
              label: 'E-Mail-App öffnen',
              textColor: Colors.white,
              onPressed: () {
                // Fallback: Öffne die E-Mail-App
                _shareViaEmail(
                  text,
                  extractedEmail: extractedEmail,
                  modelType: modelType,
                );
              },
            ),
          ),
        );
      }
    }
  }

  // Hilfsmethode zum Extrahieren der E-Mail-Adresse aus dem Jobtext
  String? _extractEmailFromJobText() {
    if (_extractedJobText.isEmpty) {
      return null;
    }

    try {
      // Einfacher regulärer Ausdruck zum Finden von E-Mail-Adressen
      final RegExp emailRegex = RegExp(
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
        caseSensitive: false,
      );

      final Iterable<RegExpMatch> matches = emailRegex.allMatches(
        _extractedJobText,
      );

      if (matches.isNotEmpty) {
        // Nehme die erste gefundene E-Mail-Adresse
        final String email = matches.first.group(0)!;
        _log.i("Fallback: Erste E-Mail gefunden: $email");
        return email;
      }
    } catch (e) {
      _log.e("Fehler beim Extrahieren der E-Mail-Adresse aus dem Jobtext: $e");
    }

    return null;
  }

  // Neue Hilfsmethode für benutzerfreundlichere Fehlermeldungen
  String _getReadableEmailError(String? errorMessage) {
    if (errorMessage == null) return "Unbekannter Fehler";

    // Bekannte Fehlermeldungen übersetzen
    if (errorMessage.contains("No email clients found") ||
        errorMessage.contains("No Activity found to handle Intent")) {
      return "Keine E-Mail-App auf diesem Gerät gefunden";
    }

    if (errorMessage.contains("User canceled")) {
      return "Vorgang vom Benutzer abgebrochen";
    }

    if (errorMessage.contains("NullPointerException")) {
      return "Problem beim Öffnen der E-Mail-App";
    }

    // Fallback auf die originale Fehlermeldung
    return errorMessage;
  }

  // Diese Methode wurde durch CvStorageHelper ersetzt

  // Methode zum Anzeigen der zweiten Werbung während der Generierung
  Future<void> _showSecondAdDuringGeneration() async {
    // Nur fortfahren, wenn wir nicht Premium sind
    final isPremium = ref.watch(isPremiumProvider);
    if (isPremium) {
      _log.i("Benutzer ist Premium, zeige keine zweite Werbung an");
      return;
    }

    // Sofort die zweite Werbung starten, ohne jegliche Verzögerung
    if (!mounted) return;

    final adService = ref.read(adServiceProvider);

    try {
      // Für Basic-Nutzer: Zeige automatisch die zweite Werbung ohne Dialog
      _log.i("Basic-Nutzer: Zeige automatisch zweite Werbung ohne Dialog");

      try {
        // Verwende die verbesserte Methode, um die zweite Werbung anzuzeigen
        adService.loadRewardedAd(
          onAdLoadedCallback: () {
            _log.i('Zweite Rewarded Ad geladen und wird angezeigt.');
            if (mounted) {
              // Setze SystemUI-Farben für die Navigationsleiste
              SystemChrome.setSystemUIOverlayStyle(
                SystemUiOverlayStyle(
                  systemNavigationBarColor: Colors.black,
                  systemNavigationBarDividerColor: Colors.transparent,
                  systemNavigationBarIconBrightness: Brightness.light,
                  systemNavigationBarContrastEnforced: false,
                ),
              );
            }
          },
          onAdFailedToLoadCallback: () {
            _log.e('Zweite Rewarded Ad konnte nicht geladen werden.');
          },
        );
      } catch (error) {
        _log.e('Fehler beim Anzeigen der zweiten Rewarded Ad: $error');
      }
    } catch (error) {
      _log.e("Fehler beim Anzeigen der zweiten Werbung: $error");
    }
  }

  // Methoden für gespeicherte Bewerbungen (optional, aber zur Vollständigkeit)
  Future<Map<String, dynamic>?> _checkExistingApplication() async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    final String? applicationJson = prefs.getString(jobKey);

    if (applicationJson == null) {
      return null;
    }

    try {
      final Map<String, dynamic> applicationData =
          jsonDecode(applicationJson) as Map<String, dynamic>;

      // Log des verwendeten Modells, falls vorhanden
      if (applicationData.containsKey('modelType')) {
        _log.i(
          "Vorhandenes Anschreiben wurde mit ${applicationData['modelType']} generiert",
        );
      }

      return applicationData;
    } catch (e) {
      // Für Abwärtskompatibilität: Wenn der gespeicherte Wert kein JSON ist,
      // sondern nur der Text, dann geben wir ein Map mit nur dem Text zurück
      _log.w("Gespeichertes Anschreiben ist kein JSON, verwende Legacy-Format");
      return {'generatedText': applicationJson};
    }
  }

  Future<void> _saveGeneratedApplication(
    String text, {
    String? extractedEmail,
    String? modelType,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';

    // Speichere Anschreiben und E-Mail-Adresse als JSON
    final Map<String, dynamic> applicationData = {
      'generatedText': text,
      if (extractedEmail != null) 'extractedEmail': extractedEmail,
      if (modelType != null)
        'modelType': modelType, // Neu: Speichere das verwendete Modell
    };

    await prefs.setString(jobKey, jsonEncode(applicationData));
    _log.i(
      "Anschreiben und E-Mail-Adresse gespeichert: ${extractedEmail != null ? 'mit E-Mail' : 'ohne E-Mail'}, Modell: ${modelType ?? 'nicht angegeben'}",
    );
  }

  // --- NEUE Hilfsfunktion zum Formatieren der Schlüssel für den Prompt ---
  String _formatPromptKey(String key) {
    switch (key) {
      case 'name':
        return 'Name des Bewerbers';
      case 'email':
        return 'E-Mail des Bewerbers';
      case 'phoneNumber':
        return 'Telefonnummer';
      case 'skills':
        return 'Fähigkeiten';
      case 'workExperience':
        return 'Berufserfahrung';
      case 'education':
        return 'Ausbildung';
      case 'jobPreferences':
        return 'Jobpräferenzen';
      case 'experienceSummary':
        return 'Zusammenfassung der Erfahrung';
      case 'interests':
        return 'Interessen';
      case 'preferredWritingStyle':
        return 'Bevorzugter Schreibstil';
      case 'includeExperienceInApplication':
        return 'Berufserfahrung im Anschreiben erwähnen';
      default:
        return key; // Fallback
    }
  }
  // --- Ende Hilfsfunktion ---

  // Diese Funktion wurde entfernt, da wir jetzt direkt eine Snackbar anzeigen
  // und den CAPTCHA-Status automatisch setzen

  // Methode _handleApplication verbessert mit klareren Kommentaren
  void _handleApplication(BuildContext context, WidgetRef ref) {
    _log.i(
      "_handleApplication aufgerufen - löst _triggerApplicationGeneration aus",
    );

    // _triggerApplicationGeneration beinhaltet jetzt den Premium Check
    _triggerApplicationGeneration();
  }

  // Methode _applyForJob wiederhergestellt
  void _applyForJob(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Bewerbungsprozess gestartet (Platzhalter)'),
      ),
    );
  }

  // Verbesserte Methode zum Finden einer E-Mail-Adresse im Text
  String? _findEmailInText(String text) {
    // Wenn wir auf einer externen Seite sind, ignorieren wir die CAPTCHA-Prüfung
    if (_showExternalLinkInfo) {
      _log.i("Externe Seite erkannt, CAPTCHA-Prüfung wird übersprungen");
      if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }
      return null; // Keine Dummy-E-Mail mehr für externe Seiten
    }

    // Wenn das CAPTCHA nicht gelöst ist, sollte der Text keine E-Mail-Adresse enthalten
    // Wir prüfen, ob bestimmte Hinweise auf ein ungelöstes CAPTCHA vorhanden sind
    if (text.toLowerCase().contains("sicherheitsgründen keine kontaktdaten") ||
        text.toLowerCase().contains("lösen sie bitte die sicherheitsfrage") ||
        text.toLowerCase().contains("lösen sie die sicherheitsfrage") ||
        text.toLowerCase().contains("geben sie die dargestellten zeichen") ||
        text.toLowerCase().contains(
          "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
        )) {
      _log.i(
        "CAPTCHA-Hinweis im Text gefunden, E-Mail-Suche wird übersprungen",
      );
      return null; // CAPTCHA ist definitiv nicht gelöst
    }

    // Wenn der Text Hinweise auf ein gelöstes CAPTCHA enthält
    if (text.toLowerCase().contains("e-mail:") ||
        text.toLowerCase().contains("e-mail-adresse:") ||
        text.toLowerCase().contains("email:") ||
        text.toLowerCase().contains("kontakt:") ||
        text.toLowerCase().contains("bewerbung an:") ||
        text.toLowerCase().contains("bewerbungen an:")) {
      // Verbesserte E-Mail-Regex mit mehr Kontext
      final emailWithLabelRegex = RegExp(
        r'(?:e-?mail|kontakt|bewerbung(?:en)?)(?:\s*:|bei|an|unter)?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        caseSensitive: false,
      );
      final labelMatch = emailWithLabelRegex.firstMatch(text);
      if (labelMatch != null && labelMatch.groupCount >= 1) {
        _log.i("E-Mail mit Label gefunden: ${labelMatch.group(1)}");
        return labelMatch.group(1);
      }
    }

    // Standard-E-Mail-Suche als Fallback
    final emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
    );
    final matches = emailRegex.allMatches(text).toList();

    // Wenn mehrere E-Mails gefunden wurden, bevorzuge die mit "bewerbung" oder "karriere"
    if (matches.length > 1) {
      for (final match in matches) {
        final email = match.group(0)?.toLowerCase() ?? "";
        if (email.contains("bewerbung") ||
            email.contains("karriere") ||
            email.contains("job") ||
            email.contains("personal") ||
            email.contains("hr")) {
          _log.i("Bevorzugte E-Mail gefunden: $email");
          return match.group(0);
        }
      }
    }

    // Fallback: Erste gefundene E-Mail zurückgeben
    if (matches.isNotEmpty) {
      _log.i("Fallback: Erste E-Mail gefunden: ${matches.first.group(0)}");
      return matches.first.group(0);
    }

    _log.i("Keine E-Mail im Text gefunden");
    return null;
  }

  // Methode _buildPrompt wiederhergestellt
  String _buildPrompt({
    required String jobText,
    required UserProfile userProfile,
    required bool includeExperience,
    required String stylePreference,
  }) {
    final Map<String, dynamic> profileDataForPrompt = {
      'name': userProfile.name,
      'email': userProfile.email,
      'phoneNumber': userProfile.phoneNumber,
      'skills':
          (userProfile.skills?.isNotEmpty ?? false)
              ? userProfile.skills?.join(', ')
              : null,
      'workExperience':
          (userProfile.workExperience?.isNotEmpty ?? false)
              ? userProfile.workExperience
                  ?.map(
                    (exp) =>
                        '${exp.position} bei ${exp.company} (${DateFormat('MM/yyyy', 'de_DE').format(exp.startDate)} - ${exp.endDate != null ? DateFormat('MM/yyyy', 'de_DE').format(exp.endDate!) : 'heute'}): ${exp.description}',
                  )
                  .join('\n\n')
              : null,
      'education':
          (userProfile.education?.isNotEmpty ?? false)
              ? userProfile.education
                  ?.map(
                    (edu) =>
                        '${edu.degree}${edu.fieldOfStudy.isNotEmpty ? ' im Bereich ${edu.fieldOfStudy}' : ''} an der ${edu.institution} (${DateFormat('MM/yyyy', 'de_DE').format(edu.startDate)} - ${edu.endDate != null ? DateFormat('MM/yyyy', 'de_DE').format(edu.endDate!) : 'heute'})',
                  )
                  .join('\n')
              : null,
      'jobPreferences': {
        'targetPosition': userProfile.jobPreferencesObj?.targetPosition,
        'industry': userProfile.jobPreferencesObj?.industry,
        'locationPreference': userProfile.jobPreferencesObj?.locationPreference,
        'desiredSalary': userProfile.jobPreferencesObj?.desiredSalary,
        'employmentType': userProfile.jobPreferencesObj?.employmentType,
      },
      'experienceSummary': userProfile.experienceSummary,
      'interests': userProfile.interests,
      'preferredWritingStyle': stylePreference,
      'includeExperienceInApplication': includeExperience,
    };

    profileDataForPrompt.removeWhere(
      (key, value) =>
          value == null ||
          (value is String && value.isEmpty) ||
          (value is List && value.isEmpty),
    );
    if (profileDataForPrompt['jobPreferences'] is Map) {
      (profileDataForPrompt['jobPreferences'] as Map).removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      if ((profileDataForPrompt['jobPreferences'] as Map).isEmpty) {
        profileDataForPrompt.remove('jobPreferences');
      }
    }

    String formattedProfileData =
        "Hier sind die Informationen über den Bewerber:\n";
    profileDataForPrompt.forEach((key, value) {
      if (value != null) {
        String formattedValue;
        if (value is Map) {
          formattedValue = value.entries
              .map((e) => "- ${e.key}: ${e.value}")
              .join('\n  ');
          formattedProfileData +=
              "\n${_formatPromptKey(key)}:\n  $formattedValue";
        } else if (value is List) {
          formattedValue = value.join(', ');
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else if (value is bool) {
          formattedValue = value ? 'Ja' : 'Nein';
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else {
          formattedValue = value.toString();
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        }
      }
    });

    if (!includeExperience) {
      formattedProfileData +=
          "\n\nWichtiger Hinweis: Der Bewerber möchte NICHT, dass seine detaillierte Berufserfahrung im Anschreiben explizit aufgelistet wird. Konzentriere dich stattdessen auf die Fähigkeiten und die Eignung für die Stelle.";
    }

    final String style = stylePreference;
    final String detailedStyleInstructions =
        DeepSeekPrompts.styleInstructions[style] ?? '';
    final String emailInstructions = '''
Wichtig: Erstelle eine E-Mail-Bewerbung, KEINEN Brief. Beginne direkt mit der Anrede (z.B. "Sehr geehrte/r ..."). Füge KEINE Betreffzeile oder Absender-/Empfängeradressen in den generierten Text ein.''';

    // Verwende immer Mistral-Prompts
    final String prompt = MistralPrompts.generateCoverLetterPrompt(
      jobTitle: widget.jobTitle,
      jobDescription: jobText,
      personalizationText: formattedProfileData,
      styleInstructions: detailedStyleInstructions + emailInstructions,
    );

    return prompt;
  }

  // Methode _launchMailto wiederhergestellt
  Future<void> _launchMailto({
    required String to,
    String? subject,
    String? body,
  }) async {
    String? encodedSubject =
        subject != null ? Uri.encodeComponent(subject) : null;
    String? encodedBody = body
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    if (encodedSubject != null) queryParts.add('subject=$encodedSubject');
    if (encodedBody != null) queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        _showMailtoError();
      }
    } catch (e) {
      _log.e('Error launching mailto: $e');
      _showMailtoError();
    }
  }

  // Methode _showMailtoError wiederhergestellt
  void _showMailtoError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('E-Mail-App konnte nicht geöffnet werden.'),
        ),
      );
    }
  }

  // --- NEU: Dialog, um zu fragen, ob bestehendes Anschreiben genutzt werden soll ---
  Future<String?> _showUseExistingApplicationDialog(
    BuildContext context,
    String existingText, {
    String? modelType,
  }) async {
    return showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Bestehendes Anschreiben gefunden'),
            content: SingleChildScrollView(
              // Scrollbar hinzugefügt
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Für diesen Job existiert bereits folgender Entwurf:',
                  ),
                  // Modelltyp-Anzeige entfernt
                  const SizedBox(height: 15),
                  // Container entfernt, Text direkt angezeigt
                  SelectableText(
                    existingText, // Gesamten Text anzeigen
                    style: const TextStyle(fontSize: 14), // Kleinere Schrift
                  ),
                  const SizedBox(height: 15),
                  const Text(
                    'Möchtest du diesen Entwurf verwenden oder einen neuen generieren?',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('cancel'), // Option 'Abbrechen'
                child: const Text('Abbrechen'),
              ),
              TextButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('use_existing'), // Option 'Bestehendes nutzen'
                child: const Text('Bestehenden nutzen'),
              ),
              ElevatedButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('generate_new'), // Option 'Neu generieren'
                child: const Text('Neu generieren'),
              ),
            ],
          ),
    );
  }
  // --- Ende neuer Dialog ---

  // Hinzugefügt: build Methode
  @override
  Widget build(BuildContext context) {
    final bool showWebView = !kIsWeb;
    // Schmale Geräte: AppBar-Actions reduzieren, um Overflow zu vermeiden
    final bool isNarrow = MediaQuery.of(context).size.width < 380;

    // Favoritenstatus abrufen
    final favoritesState = ref.watch(favoritesProvider);
    final isFavorite = favoritesState.maybeWhen(
      data:
          (jobs) => jobs.any(
            (job) => job.id == widget.jobEntity?.id,
          ), // Vergleiche mit optionaler JobEntity ID
      orElse:
          () => false, // Standardmäßig nicht Favorit, wenn Daten nicht geladen
    );

    // HINWEIS: Footer-Widget extrahieren NICHT MEHR NÖTIG

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.jobTitle,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 16), // Anpassung für lange Titel
        ),
        actions: [
          // Bewerbungszähler nur anzeigen, wenn der Benutzer kein Premium hat
          Consumer(
            builder: (context, ref, child) {
              final userProfileState = ref.watch(userProfileProvider);
              return userProfileState.when(
                data: (userProfile) {
                  final planType = userProfile.premiumPlanType ?? 'basic';
                  // Nur für Basic und Pro Pläne den Bewerbungszähler anzeigen
                  if (!isNarrow &&
                      planType != 'premium' &&
                      planType != 'unlimited') {
                    return Padding(
                      padding: const EdgeInsets.only(right: 4.0),
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 80),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: RemainingApplicationsWidget(
                            compact: true,
                            showLabel: false,
                            forceUpgradeCTA: true,
                          ),
                        ),
                      ),
                    );
                  } else {
                    // Bei Premium oder Unlimited keinen Bewerbungszähler anzeigen
                    return const SizedBox.shrink();
                  }
                },
                loading: () => const SizedBox.shrink(),
                error: (_, __) => const SizedBox.shrink(),
              );
            },
          ),
          // Favoriten-Toggle Button
          if (widget.jobEntity !=
              null) // Nur anzeigen, wenn JobEntity vorhanden ist
            IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : null,
              ),
              tooltip:
                  isFavorite
                      ? 'Von Favoriten entfernen'
                      : 'Zu Favoriten hinzufügen',
              onPressed: () {
                if (isFavorite) {
                  ref
                      .read(favoritesProvider.notifier)
                      .removeFavorite(widget.jobEntity!.id);
                } else {
                  // Aktuelle URL aus dem WebView holen und ein aktualisiertes JobEntity erstellen
                  _controller.currentUrl().then((currentUrl) {
                    if (currentUrl != null) {
                      _log.i("Aktueller URL beim Favorisieren: $currentUrl");
                      // Erstelle eine Kopie des JobEntity mit der aktuellen URL
                      final updatedJob = widget.jobEntity!.copyWith(
                        sourceUrl: currentUrl,
                      );
                      ref
                          .read(favoritesProvider.notifier)
                          .addFavorite(updatedJob);
                    } else {
                      // Fallback: Originales JobEntity verwenden, wenn URL nicht ermittelt werden kann
                      ref
                          .read(favoritesProvider.notifier)
                          .addFavorite(widget.jobEntity!);
                    }
                  });
                }
              },
            ),
          // Zurück-Button für WebView
          if (_canGoBack)
            IconButton(
              icon: const Icon(Icons.arrow_back),
              tooltip: 'Zurück',
              onPressed: () {
                _controller.goBack();
              },
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Neu laden',
            onPressed: () {
              _controller.reload();
            },
          ),
          if (!isNarrow)
            IconButton(
              icon: const Icon(Icons.open_in_browser),
              tooltip: 'Im Browser öffnen',
              onPressed:
                  () => _launchUrl(
                    'https://www.arbeitsagentur.de/jobsuche/jobdetail/${widget.jobRefnr}',
                  ),
            ),
        ],
      ),
      // Wichtige Eigenschaften für die richtige Anzeige der System-Navigation
      extendBody:
          true, // Wichtig: Erlaubt dem Body, sich hinter die Bottom-Navigation zu erstrecken
      extendBodyBehindAppBar: false, // Nicht nötig für die AppBar
      backgroundColor:
          Theme.of(
            context,
          ).scaffoldBackgroundColor, // Sicherstellen, dass der Hintergrund undurchsichtig ist
      // ANPASSUNG: resizeToAvoidBottomInset WIEDER auf false, da wir es manuell handhaben
      resizeToAvoidBottomInset: false,
      // ANPASSUNG: Body ist wieder ein Stack
      body: Stack(
        children: [
          // Verbesserte Warnung für fehlende Jobbeschreibung oder externe Links
          if (_showExternalLinkInfo)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Builder(
                builder: (context) {
                  _log.d(
                    'BANNER_DEBUG: Build - Baue MaterialBanner, da _showExternalLinkInfo true ist.',
                  );
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8.0),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.red.shade50, Colors.orange.shade50],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withAlpha(50),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.red.shade300,
                          width: 1.5,
                        ),
                      ),
                      child: MaterialBanner(
                        padding: const EdgeInsets.all(AppTheme.spacingMedium),
                        content: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.warning_amber_rounded,
                                  color: Colors.red,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                const Expanded(
                                  child: Text(
                                    'Unvollständige Jobbeschreibung erkannt!',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Diese Seite enthält möglicherweise nicht die vollständige Jobbeschreibung. Bitte klicken Sie auf den Button "Auf Seite suchen" unten, um den Button "Externe Seite öffnen" zu finden und zu markieren.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blueGrey.shade800,
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Hinweis: Es wird nur nach dem exakten Button "Externe Seite öffnen" gesucht.',
                              style: TextStyle(
                                fontSize: 13,
                                fontStyle: FontStyle.italic,
                                color: Colors.blueGrey.shade700,
                              ),
                            ),
                          ],
                        ),
                        leading: Stack(
                          alignment: Alignment.center,
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.red.shade100,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const Icon(
                              Icons.warning_amber_rounded,
                              color: Colors.red,
                              size: 32,
                            ),
                          ],
                        ),
                        backgroundColor: Colors.transparent,
                        forceActionsBelow: true,
                        actions: [
                          TextButton.icon(
                            icon: const Icon(Icons.visibility_off),
                            label: const Text('Ausblenden'),
                            onPressed: () {
                              if (mounted) {
                                setState(() {
                                  _showExternalLinkInfo = false;
                                });
                              }
                            },
                          ),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.search),
                            label: const Text('Auf Seite suchen'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () {
                              // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                              _controller.runJavaScript('''
                                (function() {
                                  // Strikt nur "externe seite öffnen"
                                  const keywords = [
                                    'externe seite öffnen'
                                  ];

                                  // Füge CSS-Animation für den Button hinzu
                                  const animationStyle = document.createElement('style');
                                  animationStyle.textContent = `
                                    @keyframes pulseButton {
                                      0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                                      50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                                      100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
                                    }

                                    @keyframes glowBorder {
                                      0% { border-color: #FFC107; }
                                      50% { border-color: #FF5722; }
                                      100% { border-color: #FFC107; }
                                    }

                                    .external-link-highlight {
                                      background-color: #FFEB3B !important;
                                      border: 3px solid #FFC107 !important;
                                      padding: 8px 16px !important;
                                      border-radius: 4px !important;
                                      box-shadow: 0 0 15px rgba(255, 193, 7, 0.8) !important;
                                      position: relative !important;
                                      animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                                      transition: all 0.3s ease !important;
                                      z-index: 9999 !important;
                                      font-weight: bold !important;
                                    }

                                    .external-link-highlight:before {
                                      content: "👉 " !important;
                                    }

                                    .external-link-highlight:after {
                                      content: " 👈" !important;
                                    }

                                    .external-link-highlight:hover {
                                      transform: scale(1.1) !important;
                                      background-color: #FFC107 !important;
                                    }
                                  `;
                                  document.head.appendChild(animationStyle);

                                  // Suche nach Elementen mit diesen Keywords
                                  const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                                  let foundElements = [];

                                  // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                                  for (let i = 0; i < elements.length; i++) {
                                    const text = (elements[i].innerText || elements[i].textContent || '').toLowerCase().trim();

                                    // Exakte Übereinstimmung mit "externe seite öffnen"
                                    if (text === 'externe seite öffnen') {
                                      foundElements.push({
                                        element: elements[i],
                                        keyword: 'externe seite öffnen'
                                      });
                                    }
                                  }

                                  if (foundElements.length > 0) {
                                    // Sortiere nach Sichtbarkeit und Position im Dokument
                                    foundElements.sort((a, b) => {
                                      const aRect = a.element.getBoundingClientRect();
                                      const bRect = b.element.getBoundingClientRect();

                                      // Prüfe, ob das Element sichtbar ist
                                      const aVisible = aRect.width > 0 && aRect.height > 0;
                                      const bVisible = bRect.width > 0 && bRect.height > 0;

                                      if (aVisible && !bVisible) return -1;
                                      if (!aVisible && bVisible) return 1;

                                      // Wenn beide sichtbar oder beide nicht sichtbar, sortiere nach Position
                                      return aRect.top - bRect.top;
                                    });

                                    // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                                    foundElements.forEach((item, index) => {
                                      // Wende die Klasse auf das Element an
                                      item.element.classList.add('external-link-highlight');

                                      // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                                      if (!item.element.title) {
                                        item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                                      }

                                      // Scrolle nur zum ersten Element
                                      if (index === 0) {
                                        item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                                      }
                                    });

                                    // Zeige eine Meldung mit der Anzahl der gefundenen Links
                                    if (foundElements.length > 1) {
                                      alert("Es wurden " + foundElements.length + " Buttons mit dem exakten Text 'Externe Seite öffnen' gefunden und hervorgehoben. Bitte klicken Sie auf einen der hervorgehobenen Buttons.");
                                    } else {
                                      alert("Ein Button mit dem exakten Text 'Externe Seite öffnen' wurde gefunden und hervorgehoben.");
                                    }
                                  } else {
                                    // Wenn nichts gefunden wurde, zeige eine Meldung
                                    alert("Kein Button mit dem exakten Text 'Externe Seite öffnen' gefunden. Bitte suchen Sie manuell nach einem Link zur externen Seite.");
                                  }
                                })();
                              ''');
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

          // WebView oder Platzhalter - füllt den Stack
          // ANPASSUNG: Padding unten hinzufügen, basierend auf Footer-Höhe und Navigationsleiste
          Padding(
            padding: EdgeInsets.only(
              bottom: _footerHeight + MediaQuery.of(context).padding.bottom,
            ),
            child: Stack(
              children: [
                if (showWebView)
                  // RepaintBoundary reduziert Repaint-Propagation (Overdraw)
                  RepaintBoundary(
                    child: WebViewWidget(
                      controller: _controller,
                      gestureRecognizers:
                          <Factory<OneSequenceGestureRecognizer>>{
                            Factory<VerticalDragGestureRecognizer>(
                              () => VerticalDragGestureRecognizer(),
                            ),
                            Factory<TapGestureRecognizer>(
                              () => TapGestureRecognizer(),
                            ),
                          },
                    ),
                  )
                else
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        "WebView ist im Web-Modus nicht verfügbar.",
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  ),
                // Lade- und Fehleranzeigen (innerhalb des gepaddeten Bereichs)
                if (_isLoadingPage)
                  const Center(child: CircularProgressIndicator()),
                if (_hasPageError)
                  const Center(
                    child: Text(
                      "Fehler beim Laden der Jobseite.",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),

          // Footer mit Hinweisfeld und Buttons - wieder im AnimatedPositioned
          AnimatedPositioned(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeOutQuad,
            left: 0,
            right: 0,
            // Verbesserte Positionierung mit Berücksichtigung der Navigationsleiste
            bottom:
                (_isHintsTextFieldFocused && _isKeyboardVisible)
                    ? MediaQuery.of(context).viewInsets.bottom
                    : MediaQuery.of(
                      context,
                    ).padding.bottom, // Berücksichtigt die Navigationsleiste
            // ANPASSUNG: GlobalKey hinzufügen
            child: Container(
              key: _footerKey,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 25),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 0.5,
                  ),
                ),
              ),
              // Padding wie vorher, inkl. Berücksichtigung der Navigationsleiste
              padding: EdgeInsets.only(
                left: AppTheme.spacingMedium,
                right: AppTheme.spacingMedium,
                top: AppTheme.spacingSmall,
                bottom:
                    (_isHintsTextFieldFocused && _isKeyboardVisible)
                        ? AppTheme.spacingSmall
                        : MediaQuery.of(context).viewPadding.bottom > 0
                        ? 0 // Kein zusätzliches Padding, wenn Navigationsleiste vorhanden ist
                        : AppTheme
                            .spacingSmall, // Minimales Padding, wenn keine Navigationsleiste vorhanden ist
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Job-Metadaten
                    _buildJobMetadataSection(context),
                    const SizedBox(height: AppTheme.spacingSmall),

                    // --- HINWEISE FELD (Logik geändert) ---
                    Consumer(
                      builder: (context, ref, child) {
                        final userProfileState = ref.watch(userProfileProvider);
                        final isPremium =
                            userProfileState.asData?.value.isPremium ?? false;

                        if (isPremium) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Modellauswahl-Anzeige über dem Hinweisfeld
                              FutureBuilder<SharedPreferences>(
                                future: SharedPreferences.getInstance(),
                                builder: (context, snapshot) {
                                  if (!snapshot.hasData) {
                                    return const SizedBox.shrink();
                                  }

                                  final prefs = snapshot.data!;
                                  final defaultModel = prefs.getString(
                                    'default_model_type',
                                  );
                                  final showDialog =
                                      prefs.getBool(
                                        'show_model_selection_dialog',
                                      ) ??
                                      true;

                                  // Wenn keine Standardauswahl existiert oder der Dialog angezeigt werden soll,
                                  // zeige keinen Modellauswahl-Chip an
                                  if (defaultModel == null || showDialog) {
                                    return const SizedBox.shrink();
                                  }

                                  // Zeige den Modellauswahl-Chip an
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: () {
                                          // Zeige Dropdown-Menü an
                                          _showModelSelectionDropdown(context);
                                        },
                                        borderRadius: BorderRadius.circular(16),
                                        child: Ink(
                                          decoration: BoxDecoration(
                                            color:
                                                defaultModel == 'deepseek'
                                                    ? Colors.amber.shade700
                                                        .withValues(alpha: 26)
                                                    : Colors.blue.shade700
                                                        .withValues(alpha: 26),
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            border: Border.all(
                                              color:
                                                  defaultModel == 'deepseek'
                                                      ? Colors.amber.shade700
                                                      : Colors.blue.shade700,
                                              width: 1,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 8,
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  width: 24,
                                                  height: 24,
                                                  decoration: BoxDecoration(
                                                    color:
                                                        defaultModel ==
                                                                'deepseek'
                                                            ? Colors
                                                                .amber
                                                                .shade700
                                                                .withValues(
                                                                  alpha: 51,
                                                                )
                                                            : Colors
                                                                .blue
                                                                .shade700
                                                                .withValues(
                                                                  alpha: 51,
                                                                ),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    defaultModel == 'deepseek'
                                                        ? Icons.star
                                                        : Icons.speed,
                                                    size: 14,
                                                    color:
                                                        defaultModel ==
                                                                'deepseek'
                                                            ? Colors
                                                                .amber
                                                                .shade700
                                                            : Colors
                                                                .blue
                                                                .shade700,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  defaultModel == 'deepseek'
                                                      ? 'Qualität'
                                                      : 'Schnell',
                                                  style: TextStyle(
                                                    color:
                                                        defaultModel ==
                                                                'deepseek'
                                                            ? Colors
                                                                .amber
                                                                .shade900
                                                            : Colors
                                                                .blue
                                                                .shade900,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                                const SizedBox(width: 4),
                                                Icon(
                                                  Icons.arrow_drop_down,
                                                  size: 18,
                                                  color:
                                                      defaultModel == 'deepseek'
                                                          ? Colors
                                                              .amber
                                                              .shade700
                                                          : Colors
                                                              .blue
                                                              .shade700,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              // Hinweisfeld nach der Modellauswahl
                              TextField(
                                controller: _hintsController,
                                focusNode: _hintsFocusNode,
                                maxLines: 3,
                                minLines: 1,
                                decoration: InputDecoration(
                                  labelText:
                                      'Zusätzliche Hinweise für die KI (optional)',
                                  hintText:
                                      'z.B. Gehaltsvorstellung erwähnen, bestimmten Skill hervorheben...',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(
                                      AppTheme.borderRadiusMedium,
                                    ),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: AppTheme.spacingMedium,
                                    vertical: AppTheme.spacingSmall,
                                  ),
                                ),
                              ),
                            ],
                          );
                        } else {
                          return OutlinedButton.icon(
                            icon: const Icon(Icons.lock_outline, size: 18),
                            label: const Text('Zusätzliche Hinweise (Premium)'),
                            onPressed: () {
                              _log.i(
                                "Premium-Hinweis-Button geklickt, navigiere zu /premium",
                              );
                              GoRouter.of(
                                context,
                              ).push(PremiumScreen.routeName);
                            },
                            style: OutlinedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 40),
                              foregroundColor: Theme.of(context).disabledColor,
                              side: BorderSide(
                                color: Theme.of(
                                  context,
                                ).disabledColor.withValues(alpha: 128),
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMedium,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    ),

                    // --- ENDE HINWEISE FELD ---
                    const SizedBox(height: AppTheme.spacingMedium),

                    // Generierungsbutton direkt hier platzieren
                    SizedBox(
                      width: double.infinity,
                      child: AnimatedBuilder(
                        animation: _gradientSweepController,
                        builder: (context, child) {
                          // Definiere den Button-Stil direkt im ElevatedButton.icon

                          // Komplett neuer Ansatz ohne ShaderMask
                          return ElevatedButton.icon(
                            icon: AnimatedBuilder(
                              animation: _buttonAnimationController,
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _buttonOpacityAnimation.value,
                                  child: child,
                                );
                              },
                              child:
                                  _generationCompletedSuccessfully
                                      ? const Icon(Icons.refresh, size: 20)
                                      : (_isExtracting
                                          ? const SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: SimpleCircleAnimation(
                                              color: Colors.white,
                                              size: 24.0,
                                            ),
                                          )
                                          : (_showExternalLinkInfo
                                              ? const Icon(
                                                Icons.auto_awesome,
                                                size: 20,
                                              )
                                              : (_captchaDetected &&
                                                      !_captchaSolved
                                                  ? const Icon(
                                                    Icons.security,
                                                    size: 20,
                                                  )
                                                  : const Icon(
                                                    Icons.auto_awesome,
                                                    size: 20,
                                                  )))),
                            ),
                            label: Text(
                              _generationCompletedSuccessfully
                                  ? 'Neues Anschreiben generieren'
                                  : (_isExtracting
                                      ? (_isLoadingAd
                                          ? 'Lade Werbung...'
                                          : 'Generiere KI-Anschreiben...')
                                      : (_showExternalLinkInfo
                                          ? 'KI-Anschreiben generieren'
                                          : (_captchaDetected && !_captchaSolved
                                              ? 'CAPTCHA gelöst? Hier klicken'
                                              : 'KI-Anschreiben generieren'))),
                            ),
                            onPressed:
                                (_isExtracting)
                                    ? null
                                    : () async {
                                      // Wenn bereits ein Anschreiben generiert wurde, setzen wir _generationCompletedSuccessfully zurück,
                                      // damit der neue Generierungsprozess starten kann
                                      if (_generationCompletedSuccessfully) {
                                        setState(() {
                                          _generationCompletedSuccessfully =
                                              false;
                                          _captchaSolved =
                                              true; // CAPTCHA als gelöst markieren, damit es nicht erneut geprüft wird
                                        });
                                      }
                                      // Prüfe, ob wir auf einer externen Seite sind
                                      if (_showExternalLinkInfo) {
                                        // Prüfe, ob wir auf einer Seite mit "Externe Seite öffnen"-Button sind
                                        final result = await _controller
                                            .runJavaScriptReturningResult('''
                                          (function() {
                                            // Nur ein Keyword: "externe seite öffnen"
                                            const keyword = 'externe seite öffnen';

                                            // Suche in mehr Element-Typen (Buttons, Links, und Elemente mit Klick-Handlern oder Link-Rollen)
                                            const potentialLinks = Array.from(document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]'));

                                            // Prüfe auf externe Links mit exakter Übereinstimmung
                                            for (let i = 0; i < potentialLinks.length; i++) {
                                              // Versuche, den sichtbaren Text zu bekommen (innerText ist oft besser als textContent)
                                              const text = (potentialLinks[i].innerText || potentialLinks[i].textContent || '').toLowerCase().trim();
                                              if (!text) continue; // Überspringe Elemente ohne Text

                                              // Nur exakte Übereinstimmung mit "externe seite öffnen"
                                              if (text === keyword) {
                                                return true; // Button gefunden
                                              }
                                            }
                                            return false; // Kein Button gefunden
                                          })();
                                        ''');

                                        // Konvertiere das Ergebnis in einen booleschen Wert
                                        final bool externalButtonFound =
                                            result.toString() == 'true';

                                        if (externalButtonFound && mounted) {
                                          // Zeige eine spezifische Warnung für "Externe Seite öffnen"-Button
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: const Text(
                                                'Bitte klicken Sie zuerst auf den "Externe Seite öffnen"-Button, um die vollständige Jobbeschreibung zu sehen.',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              duration: const Duration(
                                                seconds: 5,
                                              ),
                                              backgroundColor:
                                                  Colors.orange.shade700,
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              margin: const EdgeInsets.only(
                                                bottom: 80,
                                                left: 20,
                                                right: 20,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              action: SnackBarAction(
                                                label: 'Suchen',
                                                textColor: Colors.white,
                                                onPressed: () {
                                                  // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                                                  _controller.runJavaScript('''
                                                    (function() {
                                                      // Strikt nur "externe seite öffnen"
                                                      const keywords = ['externe seite öffnen'];

                                                      // Füge Animationsstil hinzu
                                                      const animationStyle = document.createElement('style');
                                                      animationStyle.textContent = `
                                                        @keyframes pulseButton {
                                                          0% { transform: scale(1); }
                                                          50% { transform: scale(1.05); }
                                                          100% { transform: scale(1); }
                                                        }

                                                        @keyframes glowBorder {
                                                          0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                                          50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
                                                          100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                                        }

                                                        .external-link-highlight {
                                                          background-color: #FFEB3B !important;
                                                          border: 3px solid #FFC107 !important;
                                                          padding: 8px 16px !important;
                                                          border-radius: 4px !important;
                                                          box-shadow: 0 0 15px rgba(255, 193, 7, 0.8) !important;
                                                          position: relative !important;
                                                          animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                                                          transition: all 0.3s ease !important;
                                                          z-index: 9999 !important;
                                                          font-weight: bold !important;
                                                        }

                                                        .external-link-highlight:before {
                                                          content: "👉 " !important;
                                                        }

                                                        .external-link-highlight:after {
                                                          content: " 👈" !important;
                                                        }

                                                        .external-link-highlight:hover {
                                                          transform: scale(1.1) !important;
                                                          background-color: #FFC107 !important;
                                                        }
                                                      `;
                                                      document.head.appendChild(animationStyle);

                                                      // Suche nach Elementen mit diesen Keywords
                                                      const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                                                      let foundElements = [];

                                                      // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                                                      for (let i = 0; i < elements.length; i++) {
                                                        const text = (elements[i].innerText || elements[i].textContent || '').toLowerCase().trim();

                                                        // Exakte Übereinstimmung mit "externe seite öffnen"
                                                        if (text === 'externe seite öffnen') {
                                                          foundElements.push({
                                                            element: elements[i],
                                                            keyword: 'externe seite öffnen'
                                                          });
                                                        }
                                                      }

                                                      if (foundElements.length > 0) {
                                                        // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                                                        foundElements.forEach((item, index) => {
                                                          // Wende die Klasse auf das Element an
                                                          item.element.classList.add('external-link-highlight');

                                                          // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                                                          if (!item.element.title) {
                                                            item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                                                          }

                                                          // Scrolle nur zum ersten Element
                                                          if (index === 0) {
                                                            item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                                                          }
                                                        });
                                                      }
                                                    })();
                                                  ''');
                                                },
                                              ),
                                            ),
                                          );

                                          // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                                          _controller.runJavaScript('''
                                            (function() {
                                              // Strikt nur "externe seite öffnen"
                                              const keywords = ['externe seite öffnen'];

                                              // Füge Animationsstil hinzu
                                              const animationStyle = document.createElement('style');
                                              animationStyle.textContent = `
                                                @keyframes pulseButton {
                                                  0% { transform: scale(1); }
                                                  50% { transform: scale(1.05); }
                                                  100% { transform: scale(1); }
                                                }

                                                @keyframes glowBorder {
                                                  0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                                  50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
                                                  100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                                }

                                                .external-link-highlight {
                                                  background-color: #FFEB3B !important;
                                                  border: 3px solid #FFC107 !important;
                                                  padding: 8px 16px !important;
                                                  border-radius: 4px !important;
                                                  box-shadow: 0 0 15px rgba(255, 193, 7, 0.8) !important;
                                                  position: relative !important;
                                                  animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                                                  transition: all 0.3s ease !important;
                                                  z-index: 9999 !important;
                                                  font-weight: bold !important;
                                                }

                                                .external-link-highlight:before {
                                                  content: "👉 " !important;
                                                }

                                                .external-link-highlight:after {
                                                  content: " 👈" !important;
                                                }

                                                .external-link-highlight:hover {
                                                  transform: scale(1.1) !important;
                                                  background-color: #FFC107 !important;
                                                }
                                              `;
                                              document.head.appendChild(animationStyle);

                                              // Suche nach Elementen mit diesen Keywords
                                              const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                                              let foundElements = [];

                                              // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                                              for (let i = 0; i < elements.length; i++) {
                                                const text = (elements[i].innerText || elements[i].textContent || '').toLowerCase().trim();

                                                // Exakte Übereinstimmung mit "externe seite öffnen"
                                                if (text === 'externe seite öffnen') {
                                                  foundElements.push({
                                                    element: elements[i],
                                                    keyword: 'externe seite öffnen'
                                                  });
                                                }
                                              }

                                              if (foundElements.length > 0) {
                                                // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                                                foundElements.forEach((item, index) => {
                                                  // Wende die Klasse auf das Element an
                                                  item.element.classList.add('external-link-highlight');

                                                  // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                                                  if (!item.element.title) {
                                                    item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                                                  }

                                                  // Scrolle nur zum ersten Element
                                                  if (index === 0) {
                                                    item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                                                  }
                                                });
                                              }
                                            })();
                                          ''');
                                          return; // Beende die Methode hier
                                        } else if (mounted) {
                                          // Setze CAPTCHA-Status für externe Seiten
                                          setState(() {
                                            _captchaDetected = false;
                                            _captchaSolved = true;
                                          });
                                          _triggerApplicationGeneration();
                                          return; // Beende die Methode hier
                                        }
                                      } else if (_captchaDetected &&
                                          !_captchaSolved) {
                                        // Wenn der Benutzer auf den Button "CAPTCHA gelöst? Hier klicken" klickt,
                                        // markieren wir das CAPTCHA als gelöst und fahren mit der Generierung fort
                                        _log.i(
                                          "Benutzer hat auf 'CAPTCHA gelöst? Hier klicken' geklickt",
                                        );

                                        // Setze den CAPTCHA-Status explizit
                                        setState(() {
                                          _captchaSolved = true;
                                        });

                                        // Zeige eine Bestätigungsmeldung an
                                        if (mounted) {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: const Text(
                                                'CAPTCHA als gelöst markiert. Generiere Anschreiben...',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              duration: const Duration(
                                                seconds: 3,
                                              ),
                                              backgroundColor:
                                                  Colors.green.shade600,
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              margin: const EdgeInsets.only(
                                                bottom: 80,
                                                left: 20,
                                                right: 20,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                          );
                                        }

                                        // Starte die Generierung
                                        _triggerApplicationGeneration();
                                        return; // Beende die Methode hier
                                      } else {
                                        // Doppelte Prüfung, um sicherzustellen, dass das CAPTCHA gelöst ist
                                        // Diese Prüfung sollte eigentlich nicht mehr erreicht werden, da wir oben bereits
                                        // das CAPTCHA als gelöst markieren, wenn der Benutzer auf den Button klickt
                                        if (_captchaDetected &&
                                            !_captchaSolved &&
                                            !_showExternalLinkInfo) {
                                          _log.i(
                                            "Doppelte CAPTCHA-Prüfung: Markiere CAPTCHA als gelöst",
                                          );

                                          // Setze den CAPTCHA-Status explizit
                                          setState(() {
                                            _captchaSolved = true;
                                          });

                                          // Zeige eine Bestätigungsmeldung an
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: const Text(
                                                'CAPTCHA als gelöst markiert. Generiere Anschreiben...',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              duration: const Duration(
                                                seconds: 3,
                                              ),
                                              backgroundColor:
                                                  Colors.green.shade600,
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              margin: const EdgeInsets.only(
                                                bottom: 80,
                                                left: 20,
                                                right: 20,
                                              ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                          );
                                        }
                                        _triggerApplicationGeneration();
                                      }
                                    },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              textStyle: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              disabledBackgroundColor:
                                  Colors
                                      .blue, // WICHTIG: Behält Farbe während Animation
                              disabledForegroundColor:
                                  Colors
                                      .white, // WICHTIG: Behält Textfarbe während Animation
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      // ANPASSUNG: persistentFooterButtons entfernt
    );
  }

  // Hilfsmethode _buildJobMetadataSection
  Widget _buildJobMetadataSection(BuildContext context) {
    if (widget.jobEntity == null) return const SizedBox.shrink();
    final arbeitszeit = widget.jobEntity!.metadata['arbeitszeit'];
    final befristung = widget.jobEntity!.metadata['befristung'];

    if ((arbeitszeit == null || arbeitszeit.isEmpty) &&
        (befristung == null || befristung.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (arbeitszeit != null && arbeitszeit.isNotEmpty)
            _buildInfoRow(
              context,
              Icons.access_time,
              'Arbeitszeit',
              arbeitszeit,
            ),
          if (befristung != null && befristung.isNotEmpty)
            _buildInfoRow(
              context,
              Icons.calendar_today,
              'Befristung',
              befristung,
            ),
        ],
      ),
    );
  }

  // Hilfsmethode _buildInfoRow
  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingXSmall),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Theme.of(context).colorScheme.outline),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodySmall),
          ),
        ],
      ),
    );
  }

  // Übersetzungsfunktion wurde entfernt

  // Übersetzungsfunktion wurde entfernt

  // Übersetzungsfunktion wurde entfernt

  // Übersetzungsfunktion wurde entfernt

  // Übersetzungsfunktionen wurden entfernt

  // Übersetzungsfunktionen wurden entfernt

  // Hilfsmethode zum Anzeigen eines CAPTCHA-Dialogs
  Future<void> showCaptchaDialog() async {
    if (mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              title: Text('CAPTCHA erforderlich'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Diese Seite erfordert die Lösung eines CAPTCHAs, um fortzufahren. '
                    'Sie werden zur externen Seite weitergeleitet, um das CAPTCHA zu lösen.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.security,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Verstanden'),
                ),
              ],
            ),
      );
    }
  }

  // Hilfsmethode zum Extrahieren einer E-Mail-Adresse aus einem Text
  String? extractEmailFromText(String text) {
    // Regulärer Ausdruck für E-Mail-Adressen
    final RegExp emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );

    // Suche nach allen E-Mail-Adressen im Text
    final Iterable<RegExpMatch> matches = emailRegex.allMatches(text);

    // Wenn keine E-Mail gefunden wurde, gib null zurück
    if (matches.isEmpty) return null;

    // Gib die erste gefundene E-Mail-Adresse zurück
    return matches.first.group(0);
  }

  // Native E-Mail-Versand mit erzwungenen E-Mail-Apps
  Future<void> _sendEmailOnlyApps(Email email) async {
    try {
      if (Platform.isAndroid) {
        // 🔥 KRITISCHER DEBUG: Versuche nativen Android Intent
        _log.i('🔥 NATIVE INTENT: Versuche nativen Android E-Mail-Intent...');
        await _sendAndroidEmailIntent(email);
        _log.i('🔥 NATIVE INTENT: Nativer Android Intent erfolgreich!');
      } else {
        // iOS: Verwende normalen flutter_email_sender
        _log.i('🔥 iOS: Verwende flutter_email_sender für iOS');
        await FlutterEmailSender.send(email);
      }
    } catch (e) {
      _log.e('🔥 KRITISCHER FEHLER: Native E-Mail-Intent fehlgeschlagen: $e');
      _log.e('🔥 FALLBACK: Verwende flutter_email_sender als Fallback');
      // Fallback: Normaler flutter_email_sender
      await FlutterEmailSender.send(email);
    }
  }

  // Native Android E-Mail-Intent mit erzwungenen E-Mail-Apps
  Future<void> _sendAndroidEmailIntent(Email email) async {
    const platform = MethodChannel('ki_test/email');

    try {
      // 🔥 KRITISCHER DEBUG: Prüfe Flutter-zu-Android-Übertragung
      _log.i('🔥 ANDROID DEBUG: Sende E-Mail-Parameter an Android:');
      _log.i('🔥 ANDROID DEBUG: recipients: ${email.recipients}');
      _log.i('🔥 ANDROID DEBUG: subject: ${email.subject ?? ''}');
      _log.i(
        '🔥 ANDROID DEBUG: body: "${email.body ?? ''}" (${email.body.length ?? 0} Zeichen)',
      );
      _log.i(
        '🔥 ANDROID DEBUG: attachmentPaths: ${email.attachmentPaths ?? []}',
      );

      await platform.invokeMethod('sendEmail', {
        'recipients': email.recipients,
        'subject': email.subject ?? '',
        'body': email.body ?? '',
        'attachmentPaths': email.attachmentPaths ?? [],
      });
    } catch (e) {
      _log.e('Native E-Mail-Intent fehlgeschlagen: $e');
      rethrow;
    }
  }

  // Hilfsmethode zum Bereinigen temporärer Dateien
  Future<void> _cleanupTemporaryFiles(
    List<String> allAttachmentPaths,
    String? tempCvPath,
  ) async {
    try {
      for (final path in allAttachmentPaths) {
        final file = File(path);
        if (await file.exists()) {
          if (path == tempCvPath) {
            // Verwende CvStorageHelper für CV-Dateien
            await CvStorageHelper.deleteTemporaryCvCopy(path);
          } else {
            // Lösche andere temporäre Dateien direkt
            await file.delete();
          }
          _log.i('Temporäre Datei gelöscht: $path');
        }
      }
    } catch (e) {
      _log.w('Fehler beim Löschen temporärer Dateien: $e');
    }
  }
} // <--- Schließende Klammer für die Klasse _JobDetailScreenState

import 'package:flutter/material.dart';
import 'package:ki_test/src/presentation/common/widgets/app_loading_animation.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// <PERSON>, der während der Verarbeitung eine schöne Animation anzeigt
class ProcessingButton extends StatelessWidget {
  final bool isLoading;
  final String loadingText;
  final String normalText;
  final VoidCallback? onPressed;

  const ProcessingButton({
    super.key,
    required this.isLoading,
    this.loadingText = 'Generiere...',
    required this.normalText,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildProcessingButton(context);
    } else {
      return _buildNormalButton(context);
    }
  }

  // Button für den Verarbeitungszustand
  Widget _buildProcessingButton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Angepasste App-Ladeanimation
          AppLoadingAnimation(
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24.0,
          ),
          const SizedBox(width: 12),
          // Text
          Text(
            loadingText,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Normaler Button
  Widget _buildNormalButton(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 14),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      child: Text(normalText),
    );
  }
}

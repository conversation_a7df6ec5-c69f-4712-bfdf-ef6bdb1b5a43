import 'package:flutter/material.dart';
import 'package:ki_test/src/presentation/common/widgets/ki_anschreiben_button.dart';
import 'package:ki_test/src/core/l10n/app_localizations.dart'; // Für Lokalisierung

class JobDetailKIButton extends StatelessWidget {
  final bool isGenerating;
  final bool isLoadingAd;
  final bool generationCompletedSuccessfully;
  final bool captchaDetected;
  final bool captchaSolved;
  final VoidCallback? onPressed;

  const JobDetailKIButton({
    super.key,
    required this.isGenerating,
    required this.isLoadingAd,
    required this.generationCompletedSuccessfully,
    required this.captchaDetected,
    required this.captchaSolved,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: KIAnschreibenButton(
        isGenerating: isGenerating,
        text:
            generationCompletedSuccessfully
                ? AppLocalizations.of(context).coverLetterGenerated
                : (captchaDetected && !captchaSolved
                    ? AppLocalizations.of(context).solveCaptcha
                    : AppLocalizations.of(context).generateAICoverLetter),
        generatingText:
            isLoadingAd
                ? AppLocalizations.of(context).loadingAd
                : AppLocalizations.of(context).generatingCoverLetter,
        onPressed:
            (isGenerating || generationCompletedSuccessfully)
                ? null
                : onPressed,
      ),
    );
  }
}

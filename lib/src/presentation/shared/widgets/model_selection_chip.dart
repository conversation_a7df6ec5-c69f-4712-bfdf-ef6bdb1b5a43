import 'package:flutter/material.dart';

class ModelSelectionChip extends StatelessWidget {
  final String selectedModel;
  final Function(String) onModelChanged;
  final bool canUseDeepseek;

  const ModelSelectionChip({
    super.key,
    required this.selectedModel,
    required this.onModelChanged,
    this.canUseDeepseek = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showModelSelectionDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color:
              selectedModel == 'deepseek'
                  ? const Color(0xFF2A1F0F) // Qualitäts-Hintergrund
                  : const Color(0xFF1A2332), // Schnell-Hintergrund
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: (selectedModel == 'deepseek'
                    ? const Color(0xFFFFB74D) // Orange
                    : const Color(0xFF64B5F6)) // Blau
                .withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: (selectedModel == 'deepseek'
                      ? const Color(0xFFFFB74D)
                      : const Color(0xFF64B5F6))
                  .withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    selectedModel == 'deepseek'
                        ? const Color(0xFFFFB74D)
                        : const Color(0xFF64B5F6),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (selectedModel == 'deepseek'
                            ? const Color(0xFFFFB74D)
                            : const Color(0xFF64B5F6))
                        .withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                selectedModel == 'deepseek'
                    ? Icons.star_rounded
                    : Icons.flash_on_rounded,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              selectedModel == 'deepseek' ? 'Qualität' : 'Schnell',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              size: 20,
              color:
                  selectedModel == 'deepseek'
                      ? const Color(0xFFFFB74D) // Orange für Qualität
                      : const Color(0xFF64B5F6), // Blau für Schnell
            ),
          ],
        ),
      ),
    );
  }

  void _showModelSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2A2A2A),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'KI-Modell wählen',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Option 1: Schnell (Mistral)
              _buildModelOption(
                context: context,
                icon: Icons.flash_on_rounded,
                iconColor: const Color(0xFF64B5F6),
                backgroundColor: const Color(0xFF1A2332),
                title: 'Schnell',
                description: 'Schnellere Generierung mit guter Qualität',
                isSelected: selectedModel == 'mistral',
                onTap: () {
                  onModelChanged('mistral');
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(height: 12),
              // Option 2: Qualität (DeepSeek)
              _buildModelOption(
                context: context,
                icon: Icons.star_rounded,
                iconColor: const Color(0xFFFFB74D),
                backgroundColor: const Color(0xFF2A1F0F),
                title: 'Qualität',
                description: 'Höhere Qualität, kann etwas länger dauern',
                isSelected: selectedModel == 'deepseek',
                isEnabled: canUseDeepseek,
                onTap: () {
                  onModelChanged('deepseek');
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModelOption({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required Color backgroundColor,
    required String title,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
    bool isEnabled = true,
  }) {
    return GestureDetector(
      onTap: () async {
        if (!isEnabled) {
          await showDialog(
            context: context,
            builder:
                (ctx) => AlertDialog(
                  backgroundColor: const Color(0xFF2A2A2A),
                  title: const Text(
                    'Nicht verfügbar',
                    style: TextStyle(color: Colors.white),
                  ),
                  content: const Text(
                    'Diese Qualitätsstufe ist nur für Pro oder Unlimited Mitglieder verfügbar.',
                    style: TextStyle(color: Colors.white70),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      child: const Text('OK'),
                    ),
                  ],
                ),
          );
          return;
        }
        onTap();
      },
      child: Opacity(
        opacity: isEnabled ? 1.0 : 0.5,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  isSelected
                      ? iconColor.withValues(alpha: 0.5)
                      : iconColor.withValues(alpha: 0.2),
              width: isSelected ? 2 : 1,
            ),
            boxShadow:
                isSelected
                    ? [
                      BoxShadow(
                        color: iconColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : null,
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: iconColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: iconColor.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(icon, size: 24, color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(color: Colors.grey[400], fontSize: 12),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: iconColor, size: 24),
            ],
          ),
        ),
      ),
    );
  }
}

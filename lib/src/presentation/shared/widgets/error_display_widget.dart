import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// Ein einheitliches Widget für die Anzeige von Fehlern in der gesamten App
/// Basiert auf dem Design der Favoriten-Seite
class ErrorDisplayWidget extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  final String? customTitle;
  final String? customMessage;
  final IconData? customIcon;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.customTitle,
    this.customMessage,
    this.customIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              customIcon ?? Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              customTitle ?? _getErrorTitle(),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              customMessage ?? error,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppTheme.spacingLarge),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Erneut versuchen'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getErrorTitle() {
    final errorString = error.toLowerCase();

    if (errorString.contains('verbindungsfehler') ||
        errorString.contains('netzwerkfehler') ||
        errorString.contains('socket') ||
        errorString.contains('host lookup') ||
        errorString.contains('connection')) {
      return 'Keine Internetverbindung';
    } else if (errorString.contains('timeout')) {
      return 'Zeitüberschreitung';
    } else if (errorString.contains('500') || errorString.contains('503')) {
      return 'Server derzeit nicht verfügbar';
    }

    return 'Fehler aufgetreten';
  }
}

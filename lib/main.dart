import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/auth/screens/login_screen.dart'
    show LoginScreen, isSubmittingGoogleProvider;
import 'package:ki_test/src/presentation/auth/screens/signup_screen.dart';
import 'package:ki_test/src/presentation/auth/screens/forgot_password_screen.dart';
import 'package:ki_test/src/presentation/auth/screens/password_reset_callback_screen.dart';
import 'package:ki_test/src/presentation/auth/screens/reset_password_screen.dart';
import 'package:app_links/app_links.dart';

import 'package:ki_test/src/presentation/job_search/screens/job_search_screen.dart';
import 'package:ki_test/src/presentation/profile/screens/profile_screen.dart';
import 'package:ki_test/src/presentation/favorites/screens/favorites_screen.dart';
import 'package:ki_test/src/presentation/applied_jobs/screens/applied_jobs_screen.dart';
import 'package:ki_test/src/presentation/settings/screens/settings_screen.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import 'package:flutter/services.dart';
import 'package:ki_test/src/application/providers/onboarding_provider.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:ki_test/src/presentation/splash/splash_screen.dart';
import 'package:ki_test/src/presentation/onboarding/screens/simple_onboarding_screen.dart';
import 'package:ki_test/src/presentation/onboarding/screens/cv_upload_screen.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as sp_flutter;

import 'src/core/utils/logging.dart';
import 'src/core/config/app_config.dart';
import 'src/core/utils/env_config.dart';
// Für deutsche Datumsformatierung

import 'package:ki_test/src/presentation/profile/screens/change_password_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_comparison_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_benefits_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/subscription_plans_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_upgrade_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_management_screen.dart';
import 'package:ki_test/src/presentation/cv_generator/screens/cv_generator_main_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/core/l10n/app_localizations.dart'; // Für Lokalisierung
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart'; // Für Lokalisierungs-Wrapper
import 'package:ki_test/src/application/providers/locale_provider.dart'; // Für Locale-Provider
import 'package:ki_test/src/presentation/test/test_style_screen.dart'; // Für Test-Seite
import 'package:ki_test/src/presentation/manual_job_input/screens/manual_job_input_screen.dart';
import 'package:ki_test/src/infrastructure/services/share_intent_service.dart';
import 'package:ki_test/src/application/services/email_intent_tracker.dart';

import 'package:ki_test/src/core/widgets/safe_area_wrapper.dart'; // Für Safe Area Wrapper
import 'package:ki_test/src/core/utils/app_responsive.dart'; // Für App Responsive
import 'package:flutter_phoenix/flutter_phoenix.dart'; // Für App-Neustart
import 'package:ki_test/src/core/optimization/system_error_handler.dart';

// Für Bewerbungszähler
import 'package:ki_test/src/application/providers/services_providers.dart'; // Für SubscriptionManagementService
import 'package:ki_test/src/infrastructure/services/ad_service.dart';
import 'package:ki_test/src/presentation/auth/screens/login_screen.dart'; // Für isSubmittingGoogleProvider

// KRITISCHER FIX: Memory-Leak-Detection
import 'package:ki_test/src/core/optimization/memory_leak_detector.dart';
import 'package:ki_test/src/core/optimization/memory_leak_fixer.dart';

import 'package:ki_test/src/core/utils/device_manager.dart';
import 'package:ki_test/src/application/providers/anti_manipulation_provider.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Supabase Auth State Stream Provider
final supabaseAuthStateProvider = StreamProvider<sp_flutter.AuthState>((ref) {
  return sp_flutter.Supabase.instance.client.auth.onAuthStateChange;
});

// SharedPreferences Provider für die direkte Verwendung
final sharedPrefsProvider = FutureProvider<SharedPreferences>((ref) async {
  return await SharedPreferences.getInstance();
});

// NEU: ChangeNotifier für GoRouter Refresh
class GoRouterNotifier extends ChangeNotifier {
  final Ref _ref;
  final _log = getLogger('GoRouterNotifier');
  late final ProviderSubscription<sp_flutter.AuthState> _authStateSubscription;

  GoRouterNotifier(this._ref) {
    _authStateSubscription = _ref.listen(
      supabaseAuthStateProvider.select((value) => value.value!),
      (previous, next) {
        // Prüfe auf Abmeldung und setze UI-Provider zurück
        if (previous?.session != null && next.session == null) {
          _log.i('Benutzer abgemeldet - setze UI-Provider zurück');
          // Setze isSubmittingGoogleProvider zurück
          _ref.read(isSubmittingGoogleProvider.notifier).state = false;
        }
        notifyListeners();
      },
    );

    // NEU: Höre auf den Onboarding-Status
    _ref.listen(onboardingProvider, (previous, next) {
      _log.i('Onboarding-Status geändert: $previous -> $next');
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _authStateSubscription.close();
    super.dispose();
  }
}

// NEU: Provider für den GoRouterNotifier
final goRouterNotifierProvider = Provider<GoRouterNotifier>((ref) {
  return GoRouterNotifier(ref);
});

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Logging initialisieren (früh, um auch Initialisierungsfehler zu loggen)
  initializeLogging();
  final log = getLogger('main');

  log.i('App wird gestartet...');

  // Firebase-Initialisierung entfernt

  try {
    // Optimiere System UI Einstellungen
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // Performance-Optimierung für Bildschirmaktualisierung
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // Initialisiere EnvConfig
    log.i('Initialisiere EnvConfig...');
    try {
      // Verwende einen hartcodierten Anon-Key für die Entwicklung
      // In einer Produktionsumgebung sollte dieser Schlüssel sicher gespeichert werden
      const supabaseAnonKey =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZwdHRkeGlidmpyZmp6YnRrdHFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxODYzNzQsImV4cCI6MjA2MDc2MjM3NH0.WRijbwvWdLPVo0fhk_G9ppQR0nznCTZf7BvFDF55psg';

      log.i('Supabase initialisieren...');
      log.i('Supabase Anon Key: ${supabaseAnonKey.substring(0, 10)}...');

      // Die Session wird standardmäßig in SharedPreferences gespeichert
      // und bleibt zwischen App-Neustarts erhalten
      await sp_flutter.Supabase.initialize(
        url: EnvConfig.supabaseUrl,
        anonKey: supabaseAnonKey,
        authOptions: const sp_flutter.FlutterAuthClientOptions(
          detectSessionInUri:
              false, // Deaktiviere automatische URL-Session-Erkennung für eigene Deep Link-Behandlung
        ),
      );

      log.i('Supabase erfolgreich initialisiert.');

      // Initialisiere EnvConfig nach Supabase
      await EnvConfig.init();
    } catch (e) {
      log.e('Fehler bei der Initialisierung von Supabase: $e');
      // Zeige einen Dialog an, der den Benutzer über das Problem informiert
      runApp(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text(
                    'Fehler bei der Initialisierung',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      'Die App konnte nicht initialisiert werden. Bitte stelle sicher, dass die .env-Datei korrekt konfiguriert ist und der Supabase Anon Key vorhanden ist.\n\nFehler: $e',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton(
                    onPressed: () {
                      // App neu starten
                      Phoenix.rebirth(navigatorKey.currentContext!);
                    },
                    child: const Text('App neu starten'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      return; // Beende die Initialisierung
    }

    // --- System Error Handler initialisieren ---
    SystemErrorHandler.initialize(); // Behandelt Android System-Fehler

    // --- Schwere Dienste lazy nach dem ersten Frame initialisieren ---
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // EmailIntentTracker asynchron initialisieren
      Future(() async {
        log.i('Initialisiere EmailIntentTracker (lazy)...');
        try {
          await EmailIntentTracker.initialize();
          log.i('EmailIntentTracker erfolgreich initialisiert.');
        } catch (e) {
          log.w('Fehler bei EmailIntentTracker-Initialisierung: $e');
        }
      });

      // AdService asynchron initialisieren
      Future(() async {
        try {
          await AdService.initialize();
          log.i('AdService erfolgreich initialisiert.');
        } catch (e) {
          log.w('Fehler bei AdService-Initialisierung: $e');
        }
      });
    });

    // final prefs = await SharedPreferences.getInstance(); // Entfernt, da nicht mehr direkt hier benötigt
    // final bool showOnboarding = prefs.getBool('showOnboarding') ?? true; // Entfernt
    // log.d('Onboarding-Status: $showOnboarding'); // Entfernt

    // --- AppConfig --- // Wird jetzt in PrelaunchApp geladen

    // Den Rest der main-Funktion in einer runScoped App ausführen
    // um sicherzustellen, dass Provider verfügbar sind, wenn GoRouter erstellt wird
    // Verwende Phoenix für App-Neustart
    runApp(
      Phoenix(child: ProviderScope(child: PrelaunchApp())),
    ); // Lädt MyApp indirekt
    log.i('App erfolgreich gestartet.');
  } catch (e, stackTrace) {
    log.e(
      'Fehler während der Initialisierung:',
      error: e,
      stackTrace: stackTrace,
    );
    runApp(ErrorApp(error: e));
  }
}

// NEU: Widget, das ProviderScope sicherstellt, bevor MyApp/GoRouter initialisiert wird
class PrelaunchApp extends ConsumerStatefulWidget {
  const PrelaunchApp({super.key});

  @override
  ConsumerState<PrelaunchApp> createState() => _PrelaunchAppState();
}

class _PrelaunchAppState extends ConsumerState<PrelaunchApp> {
  bool _hasInitialized = false;
  final ShareIntentService shareIntentService = ShareIntentService();
  late AppLinks _appLinks;

  // Variable um zu verfolgen, ob ein Auth Event bereits verarbeitet wurde
  bool _authEventHandled = false;

  @override
  void initState() {
    super.initState();
    final log = getLogger('PrelaunchApp');
    log.i(
      '🚀 PrelaunchApp initState: Initialisierung mit Premium-Synchronisation',
    );

    // Lifecycle beobachten, um Anti‑Manipulation früh zu setzen
    WidgetsBinding.instance.addObserver(_LifecycleObserver(ref));

    // Initialisiere Share Intent Service
    _initializeShareIntentService();

    // Initialisiere Deep Link Handler für Passwort-Reset
    _initializeDeepLinkHandler();

    // Initialisiere Auth State Change Listener für Passwort-Recovery
    _initializeAuthStateListener();
  }

  // Synchronisiere Premium-Status beim App-Start (asynchron, non-blocking)
  void _syncPremiumStatusOnStartupAsync() {
    if (_hasInitialized) return;
    _hasInitialized = true;

    final log = getLogger('PrelaunchApp');

    // Verwende Microtask um Main Thread nicht zu blockieren
    scheduleMicrotask(() async {
      // Führe Premium-Sync in separatem Microtask aus um Main Thread nicht zu blockieren
      Future.microtask(() async {
        try {
          // Prüfe, ob der Benutzer bereits angemeldet ist
          final session =
              sp_flutter.Supabase.instance.client.auth.currentSession;
          if (session != null) {
            log.i(
              'Benutzer ist bereits angemeldet, synchronisiere Premium-Status...',
            );
            final subscriptionService = ref.read(
              subscriptionManagementServiceProvider,
            );
            final result = await subscriptionService.syncSubscription();
            if (result) {
              log.i('Premium-Status erfolgreich synchronisiert');
            } else {
              log.w('Fehler beim Synchronisieren des Premium-Status');
            }

            // Prüfe den aktuellen Plan-Typ über getRemainingApplications (Server-Status)
            final adService = ref.read(adServiceProvider);
            final remainingApps =
                await subscriptionService.getRemainingApplications();
            final planType = remainingApps['plan_type'] as String?;

            log.i('Server-Plan-Typ: $planType');

            if (planType == 'free' || planType == 'basic') {
              adService.enableAds();
              log.i(
                'AdService explizit für kostenlosen User aktiviert (Plan: $planType)',
              );

              // Jank-Fix: App-Open-Ad beim Start deaktivieren (kann später manuell getriggert werden)
              // Falls gewünscht: adService.setAppOpenOnStartup(true);
              log.i('App-Open-Ad beim Start ist deaktiviert (Jank-Reduktion)');
            } else {
              adService.disableAds();
              log.i(
                'AdService für Premium-User deaktiviert - keine App-Open-Ad (Plan: $planType)',
              );
            }
          } else {
            log.i(
              'Benutzer ist nicht angemeldet, überspringe Premium-Synchronisation',
            );
          }
        } catch (e, stackTrace) {
          log.e(
            'Fehler beim Synchronisieren des Premium-Status:',
            error: e,
            stackTrace: stackTrace,
          );
        }
      });
    }); // Schließe scheduleMicrotask
  }

  void _initializeShareIntentService() {
    final log = getLogger('ShareIntentInit');
    log.i('Initialisiere ShareIntentService...');

    try {
      // Initialisiere den ShareIntentService
      shareIntentService.initialize();

      // Registriere Callback für geteilte URLs
      shareIntentService.setOnUrlSharedCallback((url, scrapingResult) {
        log.i('URL geteilt empfangen: $url');

        // Navigiere zur Manual Job Input Seite mit den Daten
        final context = navigatorKey.currentContext;
        if (context != null) {
          ShareIntentService.navigateToManualJobInputWithData(
            context: context,
            url: url,
            scrapingResult: scrapingResult,
          );
        } else {
          log.w('Kein gültiger Context für Navigation verfügbar');
        }
      });

      log.i('ShareIntentService erfolgreich initialisiert');
    } catch (e) {
      log.e('Fehler beim Initialisieren des ShareIntentService: $e');
    }
  }

  // Initialize Deep Link Handler für Passwort-Reset
  void _initializeDeepLinkHandler() async {
    final log = getLogger('DeepLinkHandler');
    _appLinks = AppLinks();

    try {
      // Check for initial link when app starts
      final initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        _handleAuthCallbackLink(initialLink, log);
      }

      // Listen for incoming deep links while app is running
      _appLinks.uriLinkStream.listen(
        (uri) {
          _handleAuthCallbackLink(uri, log);
        },
        onError: (err) {
          log.e('Fehler beim Deep Link Stream: $err');
        },
      );
    } catch (e) {
      log.e('Fehler beim Initialisieren des Deep Link Handlers: $e');
    }
  }

  // Handle auth callback deep links
  void _handleAuthCallbackLink(Uri uri, Logger log) async {
    try {
      final String host = uri.host;
      log.i('🔗 DEEP-LINK EMPFANGEN!');
      log.i('📱 URI: $uri');
      log.i('🏠 Host: $host');
      log.i('📍 Path: ${uri.path}');
      log.i('❓ Query: ${uri.query}');
      log.i('🔗 Fragment: ${uri.fragment}');
      log.i('🔍 Starte Verarbeitung...');

      if (host == 'confirm-signup' ||
          host == 'password-reset' ||
          host == 'reset-password' ||
          host == 'password-reset-callback') {
        log.i('✅ Host ist $host - verarbeite Auth-Callback');
        final queryParams = uri.queryParameters;
        final fragmentParams = Uri.splitQueryString(uri.fragment);

        // Debug-Logging für URL-Parsing
        log.i('URL Query Parameters: $queryParams');
        log.i('URL Fragment Parameters: $fragmentParams');
        log.i('Vollständige URI: $uri');

        final String? token =
            queryParams['token'] ??
            fragmentParams['token'] ??
            queryParams['code'] ??
            fragmentParams['code'];

        // Token wird nicht mehr gespeichert, da ChangePasswordScreen es nicht benötigt
        final String? error = queryParams['error'] ?? fragmentParams['error'];
        String? email = queryParams['email'] ?? fragmentParams['email'];
        final String? type = queryParams['type'] ?? fragmentParams['type'];

        // Wenn keine E-Mail im Deep Link vorhanden ist, versuche sie aus SharedPreferences zu laden
        if (email == null || email.isEmpty) {
          try {
            final prefs = await SharedPreferences.getInstance();
            email = prefs.getString('password_reset_email');
            log.i('📧 E-Mail aus SharedPreferences geladen: $email');
          } catch (e) {
            log.e('❌ Fehler beim Laden der E-Mail aus SharedPreferences: $e');
          }
        }

        // Debug-Logging für Parameter
        log.i(
          'Deep Link Parameter - Token: ${token != null ? 'vorhanden' : 'null'}, Email: ${email ?? 'null'}, Error: ${error ?? 'null'}, Type: $type',
        );

        // Prüfe, ob es sich um einen Passwort-Reset-Link handelt
        if (type == 'recovery' ||
            uri.path.contains('password-reset') ||
            host == 'password-reset' ||
            host == 'password-reset-callback') {
          log.i('🔑 PASSWORT-RESET-LINK ERKANNT!');
          log.i('🔗 Link-Details:');
          log.i('  - Type: $type');
          log.i('  - Path: ${uri.path}');
          log.i('  - Token vorhanden: ${token != null}');
          log.i('  - Email: $email');
          log.i('  - Error: $error');

          // Prüfe auf Fehler im Passwort-Reset-Link
          if (error != null) {
            log.w('❌ PASSWORT-RESET-LINK FEHLER: $error');

            WidgetsBinding.instance.addPostFrameCallback((_) {
              final context = navigatorKey.currentContext;
              if (context != null) {
                String message;
                Color backgroundColor;

                // Spezifische Fehlermeldungen basierend auf dem Fehlertyp
                if (error == 'access_denied') {
                  final errorCode =
                      queryParams['error_code'] ?? fragmentParams['error_code'];
                  final errorDescription =
                      queryParams['error_description'] ??
                      fragmentParams['error_description'];

                  if (errorCode == 'otp_expired') {
                    message =
                        'Der Passwort-Reset-Link ist abgelaufen. Bitte fordern Sie einen neuen Link an.';
                  } else {
                    message =
                        errorDescription != null
                            ? 'Passwort-Reset-Fehler: ${Uri.decodeComponent(errorDescription)}'
                            : 'Der Passwort-Reset-Link ist ungültig oder abgelaufen.';
                  }
                  backgroundColor = Colors.red;
                } else {
                  message = 'Fehler beim Passwort-Reset: $error';
                  backgroundColor = Colors.red;
                }

                // Zeige Fehlermeldung und navigiere zur Login-Seite
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    duration: const Duration(seconds: 6),
                    backgroundColor: backgroundColor,
                    action: SnackBarAction(
                      label: 'Neuen Link anfordern',
                      textColor: Colors.white,
                      onPressed: () {
                        context.go('/forgot-password');
                      },
                    ),
                  ),
                );

                // Sichere Navigation zur Login-Seite mit Fehlerparameter
                try {
                  context.go('/login?password_reset_error=true');
                } catch (e) {
                  log.e('❌ Navigationsfehler: $e');
                  // Fallback: Verwende pushReplacement
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              } else {
                log.e('❌ Navigator context ist null!');
              }
            });
            return;
          }

          // Kein Fehler - prüfe ob Token vorhanden ist
          if (token == null) {
            log.w('❌ PASSWORT-RESET-LINK ohne Token!');

            WidgetsBinding.instance.addPostFrameCallback((_) {
              final context = navigatorKey.currentContext;
              if (context != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                      'Der Passwort-Reset-Link ist unvollständig. Bitte fordern Sie einen neuen Link an.',
                    ),
                    duration: const Duration(seconds: 6),
                    backgroundColor: Colors.red,
                    action: SnackBarAction(
                      label: 'Neuen Link anfordern',
                      textColor: Colors.white,
                      onPressed: () {
                        context.go('/forgot-password');
                      },
                    ),
                  ),
                );
                try {
                  context.go('/login?password_reset_error=true');
                } catch (e) {
                  log.e('❌ Navigationsfehler: $e');
                  // Fallback: Verwende pushReplacement
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              }
            });
            return;
          }

          log.i('📱 Gültiger Passwort-Reset-Token gefunden im Deep Link');

          // Setze Auth Event als nicht behandelt zurück
          _authEventHandled = false;

          // Prüfe, ob bereits eine Session vorhanden ist
          final currentSession =
              sp_flutter.Supabase.instance.client.auth.currentSession;
          if (currentSession != null) {
            _authEventHandled = true;
            log.i('🔐 Session bereits vorhanden, navigiere direkt');

            WidgetsBinding.instance.addPostFrameCallback((_) {
              final context = navigatorKey.currentContext;
              if (context != null) {
                final currentLocation =
                    GoRouter.of(
                      context,
                    ).routeInformationProvider.value.uri.path;
                if (currentLocation != '/change-password') {
                  log.i('✅ Navigation zu /change-password (Session vorhanden)');
                  context.go(
                    '/change-password?source=initial_session&email=${Uri.encodeComponent(email ?? '')}',
                  );
                }
              }
            });
          } else {
            // NEUE KORREKTE IMPLEMENTIERUNG: Nicht manuell verifizieren!
            // Supabase loggt den User automatisch ein und feuert PASSWORD_RECOVERY Event
            log.i(
              '🔐 Passwort-Reset-Link erkannt - warte auf automatisches PASSWORD_RECOVERY Event...',
            );
            log.i(
              '🔍 Debug - Token vorhanden: ${token != null}, Email: $email',
            );

            // Speichere die E-Mail für die Passwort-Reset-Seite
            if (email != null && email.isNotEmpty) {
              final prefs = await SharedPreferences.getInstance();
              await prefs.setString('password_reset_email', email);
              log.i('📧 E-Mail für Passwort-Reset gespeichert: $email');
            }

            // Setze Flag, dass wir auf PASSWORD_RECOVERY Event warten
            _authEventHandled = false;

            // Warte auf das automatische PASSWORD_RECOVERY Event
            // Supabase wird automatisch den User einloggen und das Event feuern
            Timer(const Duration(milliseconds: 1000), () {
              if (!_authEventHandled) {
                log.w(
                  '⚠️ Kein PASSWORD_RECOVERY Event empfangen nach 1 Sekunde',
                );
                log.i('🔄 Versuche direkte Navigation als Fallback...');

                WidgetsBinding.instance.addPostFrameCallback((_) {
                  final context = navigatorKey.currentContext;
                  if (context != null) {
                    final currentLocation =
                        GoRouter.of(
                          context,
                        ).routeInformationProvider.value.uri.path;
                    if (currentLocation != '/reset-password') {
                      log.i('✅ Fallback-Navigation zu /reset-password');
                      context.go(
                        '/reset-password?source=deep_link_fallback&email=${Uri.encodeComponent(email ?? '')}',
                      );
                    }
                  }
                });
              }
            });
          }

          // Markiere als behandelt nach Verzögerung (für Auth Event Koordination)
          Future.delayed(const Duration(milliseconds: 1000), () {
            _authEventHandled = true;
          });

          return;
        }

        log.i(
          'ℹ️ Kein Passwort-Reset-Link - verarbeite als E-Mail-Bestätigung',
        );

        // Überprüfe E-Mail-Bestätigungsstatus über Edge Function
        String confirmationStatus = 'unknown';
        String? userEmail;

        // Nur Edge Function aufrufen, wenn Token oder E-Mail vorhanden sind
        if (token != null || email != null) {
          log.i(
            'Rufe Edge Function auf mit Token: ${token != null}, Email: ${email != null}',
          );
          try {
            final supabase = sp_flutter.Supabase.instance.client;
            final response = await supabase.functions.invoke(
              'check-email-confirmation',
              body: {
                if (token != null) 'token': token,
                if (email != null) 'email': email,
              },
            );

            if (response.status == 200 && response.data != null) {
              confirmationStatus = response.data['status'] ?? 'unknown';
              userEmail = response.data['email'];
              log.i(
                'E-Mail-Bestätigungsstatus: $confirmationStatus für E-Mail: $userEmail',
              );
            } else {
              log.w(
                'Fehler beim Abrufen des Bestätigungsstatus: ${response.status}',
              );
              confirmationStatus = 'error';
            }
          } on sp_flutter.AuthException catch (authError) {
            log.e(
              'AuthException beim Überprüfen des E-Mail-Bestätigungsstatus: ${authError.message}',
            );
            // Spezifische Behandlung für abgelaufene oder ungültige Token
            if (authError.message.toLowerCase().contains('expired') ||
                authError.message.toLowerCase().contains('invalid') ||
                authError.message.toLowerCase().contains('abgelaufen')) {
              confirmationStatus = 'expired_token';
            } else {
              confirmationStatus = 'auth_error';
            }
          } catch (e) {
            log.e(
              'Allgemeiner Fehler beim Überprüfen des E-Mail-Bestätigungsstatus: $e',
            );
            // Fallback zur ursprünglichen Logik
            if (error != null) {
              confirmationStatus = 'error';
            } else if (token != null) {
              confirmationStatus = 'confirmed';
            } else {
              confirmationStatus = 'unknown';
            }
          }
        } else {
          // Keine Parameter vorhanden - verwende Fallback-Logik
          log.w(
            'Kein Token oder E-Mail-Parameter gefunden, verwende Fallback-Logik',
          );
          if (error != null) {
            // Spezifische Behandlung für access_denied (bereits bestätigte E-Mail)
            if (error == 'access_denied') {
              confirmationStatus = 'already_confirmed';
            } else {
              confirmationStatus = 'error';
            }
          } else {
            confirmationStatus = 'unknown';
          }
        }

        WidgetsBinding.instance.addPostFrameCallback((_) {
          final context = navigatorKey.currentContext;
          if (context == null) {
            log.e('Navigator context ist null, Navigation nicht möglich.');
            return;
          }

          // Prüfe, ob der User bereits angemeldet ist
          final currentUser =
              sp_flutter.Supabase.instance.client.auth.currentUser;
          final isUserLoggedIn = currentUser != null;

          log.i(
            'User bereits angemeldet: $isUserLoggedIn, User: ${currentUser?.email}',
          );

          // Spezielle Behandlung für bereits angemeldete Benutzer
          if (isUserLoggedIn) {
            // Wenn User bereits angemeldet ist, zeige In-App-Nachricht ohne Navigation zum Login
            if (confirmationStatus == 'already_confirmed' ||
                confirmationStatus == 'invalid_token' ||
                confirmationStatus == 'expired_token' ||
                confirmationStatus == 'error' ||
                confirmationStatus == 'auth_error') {
              log.i(
                'User ist bereits angemeldet, zeige In-App-Nachricht für Status: $confirmationStatus',
              );

              // Zeige eine In-App-Nachricht ohne Navigation zum Login
              String message;
              Color backgroundColor;
              switch (confirmationStatus) {
                case 'already_confirmed':
                  message = 'Diese E-Mail-Adresse wurde bereits bestätigt.';
                  backgroundColor = Colors.green;
                  break;
                case 'invalid_token':
                case 'expired_token':
                  message =
                      'Der E-Mail-Bestätigungslink ist ungültig oder abgelaufen.';
                  backgroundColor = Colors.orange;
                  break;
                case 'error':
                case 'auth_error':
                  message =
                      error != null
                          ? 'Fehler bei der E-Mail-Bestätigung: $error'
                          : 'Fehler bei der E-Mail-Bestätigung. Der Link ist möglicherweise ungültig oder abgelaufen.';
                  backgroundColor = Colors.red;
                  break;
                default:
                  message =
                      'E-Mail-Bestätigung konnte nicht verarbeitet werden.';
                  backgroundColor = Colors.orange;
                  break;
              }

              // Zeige SnackBar mit der Nachricht
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  duration: const Duration(seconds: 4),
                  backgroundColor: backgroundColor,
                ),
              );

              // Bleibe in der aktuellen App-Ansicht
              return;
            }
            // Wenn angemeldeter User eine neue erfolgreiche Bestätigung hat (sollte selten vorkommen)
            else if (confirmationStatus == 'confirmed') {
              log.i('Angemeldeter User hat neue E-Mail-Bestätigung erhalten');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('E-Mail-Adresse wurde erfolgreich bestätigt!'),
                  duration: Duration(seconds: 4),
                  backgroundColor: Colors.green,
                ),
              );
              return;
            }
          }

          // Normale Navigation für nicht angemeldete User oder erfolgreiche Bestätigungen
          switch (confirmationStatus) {
            case 'confirmed':
              // Neue erfolgreiche E-Mail-Bestätigung - navigiere direkt zur Login-Seite
              // Die Login-Seite zeigt die Erfolgsmeldung basierend auf den Query-Parametern an
              log.i(
                'Neue E-Mail-Bestätigung erfolgreich, navigiere zur Login-Seite',
              );

              context.go(
                '/login?confirmed=true${userEmail != null ? '&email=${Uri.encodeComponent(userEmail)}' : ''}',
              );
              break;
            case 'already_confirmed':
              context.go(
                '/login?already_confirmed=true${userEmail != null ? '&email=${Uri.encodeComponent(userEmail)}' : ''}',
              );
              break;
            case 'not_confirmed':
              context.go(
                '/login?not_confirmed=true${userEmail != null ? '&email=${Uri.encodeComponent(userEmail)}' : ''}',
              );
              break;
            case 'invalid_token':
              context.go('/login?invalid_token=true');
              break;
            case 'expired_token':
              context.go(
                '/login?expired_token=true&message=${Uri.encodeComponent('Der E-Mail-Bestätigungslink ist abgelaufen. Bitte fordern Sie einen neuen Link an.')}',
              );
              break;
            case 'auth_error':
              context.go(
                '/login?auth_error=true&message=${Uri.encodeComponent('Fehler bei der E-Mail-Bestätigung. Bitte versuchen Sie es erneut oder kontaktieren Sie den Support.')}',
              );
              break;
            case 'error':
              context.go(
                '/login?error=${Uri.encodeComponent(error ?? 'Unbekannter Fehler bei der E-Mail-Bestätigung')}',
              );
              break;
            default:
              context.go(
                '/login?message=${Uri.encodeComponent('E-Mail-Bestätigung konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.')}',
              );
              break;
          }
        });
      } else {
        log.w('⚠️ Unbekannter Host: $host');
        log.w('📍 Vollständige URI: $uri');
        log.w('❓ Dieser Deep-Link wird nicht verarbeitet');
      }
      // Passwort-Reset wird jetzt über Auth State Change Event behandelt
    } catch (e, stackTrace) {
      log.e(
        'Kritischer Fehler beim Verarbeiten des Deep Links: $e',
        stackTrace: stackTrace,
      );

      // Sichere Navigation zur Login-Seite mit Fehlermeldung
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final context = navigatorKey.currentContext;
        if (context != null) {
          context.go(
            '/login?critical_error=true&message=${Uri.encodeComponent('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.')}',
          );
        }
      });
    }
  }

  // Initialize Auth State Change Listener für Passwort-Recovery Events
  void _initializeAuthStateListener() {
    final log = getLogger('AuthStateListener');

    try {
      sp_flutter.Supabase.instance.client.auth.onAuthStateChange.listen(
        (data) {
          final event = data.event;
          final session = data.session;

          log.i('🔐 Auth State Change Event: $event');
          log.i('📱 Session vorhanden: ${session != null}');
          if (session?.user != null) {
            log.i('👤 User: ${session!.user.email}');
          }

          // Behandle passwordRecovery Event (Priorität vor Deep Links)
          if (event == sp_flutter.AuthChangeEvent.passwordRecovery) {
            log.i('🔑 PASSWORD RECOVERY EVENT ERKANNT!');
            log.i('Session vorhanden: ${session != null}');

            // Markiere Auth Event als behandelt
            _authEventHandled = true;

            if (session != null) {
              log.i('User: ${session.user.email}');

              // Navigiere zur Change Password Screen mit Auth Event Quelle
              WidgetsBinding.instance.addPostFrameCallback((_) {
                final context = navigatorKey.currentContext;
                if (context != null) {
                  log.i(
                    '✅ Navigation zu /reset-password via Auth State Change',
                  );
                  context.go(
                    '/reset-password?source=auth_event&email=${Uri.encodeComponent(session.user.email ?? '')}',
                  );
                } else {
                  log.e('❌ Navigator context ist null bei Auth State Change!');
                }
              });
            } else {
              log.w('❌ Password Recovery Event ohne gültige Session empfangen');
              // Fallback: Zeige Fehlermeldung
              WidgetsBinding.instance.addPostFrameCallback((_) {
                final context = navigatorKey.currentContext;
                if (context != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text(
                        'Passwort-Reset-Session ungültig. Bitte fordern Sie einen neuen Link an.',
                      ),
                      backgroundColor: Colors.red,
                      action: SnackBarAction(
                        label: 'Neuen Link anfordern',
                        textColor: Colors.white,
                        onPressed: () => context.go('/forgot-password'),
                      ),
                    ),
                  );
                  context.go('/login?password_reset_error=true');
                }
              });
            }
          }
          // Behandle signedIn Event - könnte bei Passwort-Reset auftreten
          else if (event == sp_flutter.AuthChangeEvent.signedIn) {
            log.i('🔐 SIGNED_IN EVENT ERKANNT!');
            log.i('Session vorhanden: ${session != null}');
            if (session?.user != null) {
              log.i('User: ${session!.user.email}');
            }

            // Prüfe, ob es sich um einen Passwort-Reset handelt
            // (basierend auf gespeicherter E-Mail aus Deep Link)
            SharedPreferences.getInstance().then((prefs) {
              final resetEmail = prefs.getString('password_reset_email');

              if (resetEmail != null && session?.user.email == resetEmail) {
                log.i('🔑 PASSWORT-RESET SIGNED_IN ERKANNT für: $resetEmail');

                // Markiere Auth Event als behandelt
                _authEventHandled = true;

                // Lösche die gespeicherte E-Mail
                prefs.remove('password_reset_email');

                // Navigiere zur Change Password Screen
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  final context = navigatorKey.currentContext;
                  if (context != null) {
                    log.i(
                      '✅ Navigation zu /reset-password via SIGNED_IN (Passwort-Reset)',
                    );
                    context.go(
                      '/reset-password?source=signed_in_reset&email=${Uri.encodeComponent(resetEmail)}',
                    );
                  } else {
                    log.e('❌ Navigator context ist null bei SIGNED_IN!');
                  }
                });
              } else {
                log.i('ℹ️ Normaler SIGNED_IN Event (kein Passwort-Reset)');
                // Normale Anmeldung - keine spezielle Behandlung nötig
              }
            });
          }
          // Behandle signedOut Event
          else if (event == sp_flutter.AuthChangeEvent.signedOut) {
            log.i('👋 User abgemeldet, prüfe Navigation');

            // Verwende eine längere Verzögerung, um sicherzustellen, dass andere Navigator-Operationen abgeschlossen sind
            Future.delayed(const Duration(milliseconds: 100), () {
              try {
                final context = navigatorKey.currentContext;
                if (context != null && context.mounted) {
                  final currentLocation =
                      GoRouter.of(
                        context,
                      ).routeInformationProvider.value.uri.path;
                  log.i('Current location: $currentLocation');

                  // Nur navigieren, wenn wir nicht bereits auf der Login-Seite sind
                  if (currentLocation != '/login' &&
                      currentLocation != '/register') {
                    log.i('Navigiere zur Login-Seite nach Abmeldung');
                    context.go('/login');
                  } else {
                    log.i(
                      'Bereits auf Auth-Seite ($currentLocation), keine Navigation nötig',
                    );
                  }
                } else {
                  log.w(
                    'Navigator context ist null oder nicht mounted, Navigation übersprungen.',
                  );
                }
              } catch (e) {
                log.w('Fehler bei der Navigation nach signOut: $e');
                // Fallback: Versuche es nochmal nach einer weiteren Verzögerung
                Future.delayed(const Duration(milliseconds: 200), () {
                  try {
                    final context = navigatorKey.currentContext;
                    if (context != null && context.mounted) {
                      context.go('/login');
                    }
                  } catch (fallbackError) {
                    log.e('Fallback-Navigation fehlgeschlagen: $fallbackError');
                  }
                });
              }
            });
          }
          // Behandle initialSession Event
          else if (event == sp_flutter.AuthChangeEvent.initialSession) {
            log.i('🚀 INITIAL SESSION EVENT ERKANNT');
            final currentPath =
                navigatorKey.currentContext != null
                    ? GoRouter.of(
                      navigatorKey.currentContext!,
                    ).routeInformationProvider.value.uri.path
                    : 'unknown';
            log.i('📍 Aktueller Pfad: $currentPath');

            // Prüfe, ob es sich um eine Passwort-Recovery-Session handelt
            final session = data.session;
            if (session != null) {
              final user = session.user;
              log.i(
                'Initial Session User: ${user.email}, Email Confirmed: ${user.emailConfirmedAt}',
              );

              // Wenn wir uns auf dem password-reset-callback Screen befinden
              WidgetsBinding.instance.addPostFrameCallback((_) {
                final context = navigatorKey.currentContext;
                if (context != null) {
                  final currentLocation =
                      GoRouter.of(
                        context,
                      ).routeInformationProvider.value.uri.path;
                  log.i('Current location: $currentLocation');

                  if (currentLocation == '/password-reset-callback') {
                    log.i(
                      'Auf password-reset-callback Screen erkannt, navigiere zur ChangePasswordScreen',
                    );
                    context.go(
                      '/change-password?source=initial_session&email=${Uri.encodeComponent(user.email ?? '')}',
                    );
                  }
                }
              });
            }
          }
          // Behandle andere relevante Events
          else if (event == sp_flutter.AuthChangeEvent.signedIn) {
            log.i('✅ User erfolgreich angemeldet: ${session?.user.email}');
          } else if (event == sp_flutter.AuthChangeEvent.tokenRefreshed) {
            log.d('🔄 Token aktualisiert für User: ${session?.user.email}');
          }
        },
        onError: (error) {
          log.e('❌ Fehler im Auth State Change Listener: $error');
        },
      );

      log.i('✅ Auth State Change Listener erfolgreich initialisiert');
    } catch (e) {
      log.e('❌ Fehler beim Initialisieren des Auth State Change Listeners: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialisiere hier ggf. Dinge, die ref benötigen, *bevor* GoRouter erstellt wird
    // z.B. den GoRouterNotifier lauschen lassen
    ref.watch(goRouterNotifierProvider);

    // Synchronisiere Premium-Status beim App-Start für bereits angemeldete Benutzer
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Führe Premium-Sync asynchron aus ohne Main Thread zu blockieren
      _syncPremiumStatusOnStartupAsync();
    });

    // Lade AppConfig hier, da wir jetzt im ProviderScope sind
    final appConfig = AppConfig.fromEnvironment();
    // Der Onboarding-Status wird jetzt direkt im GoRouter Redirect gelesen,
    // daher keine Übergabe an MyApp mehr nötig.

    return MyApp(appConfig: appConfig); // showOnboarding entfernt
  }
}

// MyApp ist jetzt ein ConsumerWidget, um auf den Router zuzugreifen
class MyApp extends ConsumerWidget {
  final AppConfig appConfig;
  // final bool showOnboarding; // Entfernt

  const MyApp({
    super.key,
    required this.appConfig /*, required this.showOnboarding*/,
  }); // showOnboarding entfernt

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // GoRouter hier erstellen, da wir jetzt `ref` haben
    final GoRouter router = GoRouter(
      navigatorKey: navigatorKey, // Optional, aber nützlich
      initialLocation: '/splash', // Starte immer beim Splash Screen
      routes: <RouteBase>[
        GoRoute(
          path: '/',
          builder: (BuildContext context, GoRouterState state) {
            return const AppShell();
          },
          routes: <RouteBase>[
            GoRoute(
              path: 'job-detail/:refnr',
              builder: (BuildContext context, GoRouterState state) {
                final refnr = state.pathParameters['refnr']!;
                final jobEntity = state.extra as JobEntity?;
                return JobDetailScreen(
                  jobRefnr: refnr,
                  jobTitle: jobEntity?.title ?? 'Jobdetails',
                  jobEntity: jobEntity,
                );
              },
            ),
            GoRoute(
              path: 'profile',
              builder: (BuildContext context, GoRouterState state) {
                return const ProfileScreen();
              },
            ),
            GoRoute(
              path: 'settings',
              builder: (BuildContext context, GoRouterState state) {
                return const SettingsScreen();
              },
            ),

            GoRoute(
              path: 'premium',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumComparisonScreen();
              },
            ),
            GoRoute(
              path: 'premium-benefits',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumBenefitsScreen();
              },
            ),
            GoRoute(
              path: 'subscription-plans',
              builder: (BuildContext context, GoRouterState state) {
                return const SubscriptionPlansScreen();
              },
            ),

            GoRoute(
              path: 'premium-upgrade',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumUpgradeScreen();
              },
            ),

            GoRoute(
              path: 'premium-management',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumManagementScreen();
              },
            ),
            GoRoute(
              path: 'test-style',
              builder: (BuildContext context, GoRouterState state) {
                return const TestStyleScreen();
              },
            ),
            GoRoute(
              path: 'manual-job-input',
              builder: (BuildContext context, GoRouterState state) {
                final extraData = state.extra as Map<String, dynamic>?;
                return ManualJobInputScreen(extraData: extraData);
              },
            ),
            GoRoute(
              path: 'cv-generator',
              builder: (BuildContext context, GoRouterState state) {
                return const CvGeneratorMainScreen();
              },
            ),
          ],
        ),
        GoRoute(
          path: '/login',
          builder: (BuildContext context, GoRouterState state) {
            return const LoginScreen();
          },
        ),
        GoRoute(
          path: '/register',
          builder: (BuildContext context, GoRouterState state) {
            return const SignupScreen();
          },
        ),
        GoRoute(
          path: '/forgot-password',
          builder: (BuildContext context, GoRouterState state) {
            return const ForgotPasswordScreen();
          },
        ),
        GoRoute(
          path: '/password-reset-callback',
          builder: (BuildContext context, GoRouterState state) {
            return const PasswordResetCallbackScreen();
          },
        ),
        GoRoute(
          path: '/change-password',
          builder: (BuildContext context, GoRouterState state) {
            return const ChangePasswordScreen();
          },
        ),
        GoRoute(
          path: '/reset-password',
          builder: (BuildContext context, GoRouterState state) {
            return const ResetPasswordScreen();
          },
        ),

        GoRoute(
          path: '/onboarding',
          builder: (BuildContext context, GoRouterState state) {
            // Verwende den neuen Onboarding-Screen
            return const SimpleOnboardingScreen();
          },
          routes: <RouteBase>[
            GoRoute(
              path: 'cv-upload',
              builder: (BuildContext context, GoRouterState state) {
                return const CvUploadScreen();
              },
            ),
          ],
        ),
        // Spezielle Route für den Onboarding-Screen, die nicht von der Umleitung betroffen ist
        GoRoute(
          path: '/view-onboarding',
          builder: (BuildContext context, GoRouterState state) {
            // Verwende den neuen Onboarding-Screen im Ansichtsmodus
            return const SimpleOnboardingScreen();
          },
        ),
        GoRoute(
          path: '/splash',
          builder: (BuildContext context, GoRouterState state) {
            return const SplashScreen();
          },
        ),
      ],
      redirect: (BuildContext context, GoRouterState state) async {
        final log = getLogger('GoRouterRedirect');

        final session = sp_flutter.Supabase.instance.client.auth.currentSession;
        final bool isLoggedIn = session != null;
        final onboardingCompleted = ref.read(onboardingProvider);

        final bool isSplash = state.matchedLocation == '/splash';
        final bool isOnboardingRoute =
            state.matchedLocation == '/onboarding' ||
            state.matchedLocation == '/view-onboarding';
        final bool isAuthRoute =
            state.matchedLocation == '/login' ||
            state.matchedLocation == '/register' ||
            state.matchedLocation == '/forgot-password' ||
            state.matchedLocation == '/password-reset-callback' ||
            state.matchedLocation ==
                '/confirm-signup' || // New auth callback route
            state.matchedLocation ==
                '/change-password'; // Include change-password as auth route

        log.d(
          'Redirect check: location=${state.matchedLocation}, isLoggedIn=$isLoggedIn, onboardingCompleted=$onboardingCompleted, isAuthRoute=$isAuthRoute',
        );

        // 1. If on any auth-related route, always allow it to proceed.
        // This is crucial for password reset and signup confirmation flows.
        if (isAuthRoute) {
          log.d(
            'Redirect: On Auth Route (${state.matchedLocation}), allowing navigation.',
          );
          return null;
        }

        // 2. If on splash screen, handle initial redirects based on login/onboarding status.
        if (isSplash) {
          log.d('Redirect: On Splash Screen.');
          if (isLoggedIn && onboardingCompleted) {
            log.d(
              'Redirect: On Splash, logged in and onboarding completed -> /',
            );
            return '/';
          }
          if (isLoggedIn && !onboardingCompleted) {
            log.d(
              'Redirect: On Splash, logged in and onboarding NOT completed -> /onboarding',
            );
            return '/onboarding';
          }
          log.d('Redirect: On Splash, not logged in -> /login');
          return '/login';
        }

        // 3. If not logged in, redirect to login (unless already on an auth route, which is handled above).
        if (!isLoggedIn) {
          log.d('Redirect: Not logged in, redirecting to /login.');
          return '/login';
        }

        // 4. If logged in but onboarding not completed, redirect to onboarding.
        if (!onboardingCompleted) {
          log.d(
            'Redirect: Logged in, but onboarding not completed, redirecting to /onboarding.',
          );
          return '/onboarding';
        }

        // 5. Otherwise, no redirect needed (stay on current page or go to requested page).
        log.d('Redirect: No special redirect needed. Staying on current page.');
        return null;
      },
      errorBuilder:
          (context, state) => Scaffold(
            body: Center(child: Text('Seite nicht gefunden: ${state.error}')),
          ),
      // NEU: refreshListenable verwendet den Notifier
      refreshListenable: ref.watch(goRouterNotifierProvider),
    );

    // Aktuelle Locale aus dem Provider lesen
    final currentLocale = ref.watch(localeProvider);

    // Nutze MaterialApp.router
    return MaterialApp.router(
      routerConfig: router,
      title: 'Bewerbung KI',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: ThemeMode.dark,
      localizationsDelegates: [
        AppLocalizations.delegate, // Für unsere eigenen Übersetzungen
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: supportedLocales, // Aus dem locale_provider
      locale: currentLocale, // Dynamisch aus dem Provider
      builder: (context, child) {
        // Verwende MediaQuery, um die Textgröße zu fixieren und sicherzustellen,
        // dass die App auf allen Geräten konsistent aussieht
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(1.0),
            // Stelle sicher, dass die App auf allen Geräten richtig angezeigt wird
            devicePixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          child: child!,
        );
      },
    );
  }
}

class AppShell extends ConsumerStatefulWidget {
  const AppShell({super.key});

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  int _selectedIndex = 0;
  DateTime? _lastBackPressed;

  // Liste der Widgets passend zu den Navigationsitems
  static const List<Widget> _widgetOptions = <Widget>[
    JobSearchScreen(),
    FavoritesScreen(), // Favoriten jetzt an Index 1
    AppliedJobsScreen(), // Beworbene Jobs jetzt an Index 2
    ProfileScreen(), // Profil jetzt an Index 3
    SettingsScreen(), // Einstellungen jetzt an Index 4
  ];

  void _onItemTapped(int index) {
    // Für alle Tabs (Jobs, Favoriten, Profil, Einstellungen): Navigation direkt erlauben
    setState(() {
      _selectedIndex = index;
    });
  }

  // Methode für Doppel-Zurück-Bestätigung
  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const maxDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > maxDuration) {
      _lastBackPressed = now;

      // Zeige SnackBar mit Hinweis
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Zum Verlassen der App erneut drücken'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );

      return false; // Verhindere das Verlassen der App
    }

    return true; // Erlaube das Verlassen der App
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Verhindere automatisches Verlassen
      onPopInvoked: (didPop) async {
        if (!didPop) {
          final shouldPop = await _onWillPop();
          if (shouldPop && context.mounted) {
            // Verwende SystemNavigator.pop() um die App zu verlassen
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        // extendBody: false, damit der Body nicht hinter die BottomNavigationBar erweitert wird
        extendBody: false,
        // Verwende SafeAreaWrapper für den Body
        body: SafeAreaWrapper(
          // bottom: true, damit der untere Rand berücksichtigt wird
          bottom: true,
          child: Center(
            child: IndexedStack(
              index: _selectedIndex,
              children: _widgetOptions,
            ),
          ),
        ),
        // Verwende BottomNavigationBar mit angepassten Eigenschaften für verschiedene Gerätetypen
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            // Füge einen leichten Schatten hinzu, um die Navigationsleiste hervorzuheben
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(
                  26,
                ), // Entspricht etwa 0.1 Opazität
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: Container(
                  height: 36.0, // Größere Höhe für mehr Abstand
                  padding: const EdgeInsets.only(
                    bottom: 6.0,
                  ), // Mehr Abstand zum Text
                  margin: const EdgeInsets.only(top: 8.0), // Abstand nach oben
                  alignment: Alignment.center,
                  child: const Icon(Icons.search),
                ),
                label: AppLocalizationsWrapper.of(context).jobSearchTitle,
              ),
              BottomNavigationBarItem(
                icon: Container(
                  height: 36.0,
                  padding: const EdgeInsets.only(bottom: 6.0),
                  margin: const EdgeInsets.only(top: 8.0),
                  alignment: Alignment.center,
                  child: const Icon(Icons.favorite_border),
                ),
                label: AppLocalizationsWrapper.of(context).favoritesTitle,
              ),
              BottomNavigationBarItem(
                icon: Container(
                  height: 36.0,
                  padding: const EdgeInsets.only(bottom: 6.0),
                  margin: const EdgeInsets.only(top: 8.0),
                  alignment: Alignment.center,
                  child: const Icon(Icons.work_outline),
                ),
                label: 'Beworbene Jobs',
              ),
              BottomNavigationBarItem(
                icon: Container(
                  height: 36.0,
                  padding: const EdgeInsets.only(bottom: 6.0),
                  margin: const EdgeInsets.only(top: 8.0),
                  alignment: Alignment.center,
                  child: const Icon(Icons.person_outline),
                ),
                label: AppLocalizationsWrapper.of(context).profileTitle,
              ),
              BottomNavigationBarItem(
                icon: Container(
                  height: 36.0,
                  padding: const EdgeInsets.only(bottom: 6.0),
                  margin: const EdgeInsets.only(top: 8.0),
                  alignment: Alignment.center,
                  child: const Icon(Icons.settings_outlined),
                ),
                label: AppLocalizationsWrapper.of(context).settingsTitle,
              ),
            ],
            currentIndex: _selectedIndex,
            selectedItemColor: Colors.amber[700], // Etwas kräftigere Farbe
            unselectedItemColor:
                Colors.grey[500], // Helleres Grau für besseren Kontrast
            onTap: _onItemTapped,
            type: BottomNavigationBarType.fixed,
            elevation:
                0.0, // Kein eigener Schatten, da wir einen Container mit Schatten verwenden
            backgroundColor:
                Theme.of(context).bottomNavigationBarTheme.backgroundColor,
            // Verbesserte Textgrößen für bessere Lesbarkeit
            selectedLabelStyle: TextStyle(
              fontSize: AppResponsive.value(
                context: context,
                mobile: 11.0,
                tablet: 13.0,
                desktop: 15.0,
              ),
              fontWeight:
                  FontWeight.w600, // Deutlich stärker für bessere Sichtbarkeit
              height: 1.0,
              letterSpacing: 0.3, // Mehr Abstand zwischen Buchstaben
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: AppResponsive.value(
                context: context,
                mobile:
                    10.0, // Etwas kleiner für besseren Kontrast zum ausgewählten Element
                tablet: 12.0,
                desktop: 14.0,
              ),
              fontWeight: FontWeight.w400,
              height: 1.0,
              letterSpacing: 0.2,
            ),
            iconSize: AppResponsive.value(
              context: context,
              mobile: 26.0, // Größere Icons für bessere Sichtbarkeit
              tablet: 30.0,
              desktop: 34.0,
            ),
          ),
        ),
      ),
    );
  }
}

// --- Platzhalter für fehlende Screens ---

// --- Ende: Code für den AI Search Provider ---

// Eine einfache App zur Fehleranzeige
class ErrorApp extends StatelessWidget {
  final Object error;
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(child: Text('Fehler bei der App-Initialisierung: $error')),
      ),
    );
  }
}

/// Beobachtet App-Lifecycle und setzt Anti‑Manipulations‑Status beim Resume
class _LifecycleObserver with WidgetsBindingObserver {
  final WidgetRef ref;
  _LifecycleObserver(this.ref);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      final log = getLogger('LifecycleObserver');
      try {
        final supa = sp_flutter.Supabase.instance.client;
        final user = supa.auth.currentUser;
        if (user == null) return;

        // 1) Device erneut registrieren/validieren → Provider setzen
        final deviceManager = DeviceManager();
        final result = await deviceManager.registerDeviceForUser(user.id);
        ref.read(antiManipulationProvider.notifier).setFromServerResult(result);

        // 2) Bewerbungszähler sofort neu laden
        await ref
            .read(remainingApplicationsNotifierProvider.notifier)
            .refresh();
        log.i(
          '✅ Lifecycle Resume: Anti‑Manipulation & Bewerbungen aktualisiert',
        );
      } catch (e, st) {
        log.e('❌ Lifecycle Resume Fehler: $e', stackTrace: st);
      }
    }
  }
}

/// Initialisiert Memory-Leak-Detection für die App
void _initializeMemoryLeakDetection() {
  // Starte Memory-Leak-Detection nur im Debug-Modus
  if (kDebugMode) {
    MemoryLeakDetector.startMonitoring();
    MemoryLeakFixer.startAutoFix();

    // Log Memory-Status alle 5 Minuten
    Timer.periodic(const Duration(minutes: 5), (timer) {
      final report = MemoryLeakDetector.generateDetailedReport();
      print('📊 MEMORY LEAK REPORT:\n$report');
    });
  }
}

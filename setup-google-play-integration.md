# Google Play Billing Integration Setup

## 1. Supabase Environment Variables konfigurieren

Du musst die folgenden Environment Variables in Supabase setzen:

### Über Supabase Dashboard:
1. <PERSON><PERSON><PERSON> zu deinem Supabase Projekt Dashboard
2. <PERSON>vi<PERSON><PERSON> zu Settings > Edge Functions
3. Füge folgende Environment Variables hinzu:

```bash
# Google Service Account JSON (kompletter Inhalt der google-service-account.json)
GOOGLE_SERVICE_ACCOUNT={"type":"service_account","project_id":"ai-job-assistent",...}

# Sandbox Mode (für Testing)
IS_SANDBOX_MODE=false

# Apple Shared Secret (falls iOS unterstützt wird)
APPLE_SHARED_SECRET=your_apple_shared_secret_here
```

### Über Supabase CLI:
```bash
# Setze Google Service Account
supabase secrets set GOOGLE_SERVICE_ACCOUNT='{"type":"service_account","project_id":"ai-job-assistent","private_key_id":"12602a8f302e341f26f92b5fec61b58f02e6f028","private_key":"-----B<PERSON>IN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCR95cq8aBIbCrW\nRBrscSdxHHdxDtAX121yoELu0ziM2VG32OkeARWi1hGB33oYCM31J+Wm2QE5A44p\nczKX0FRsjP5LbOcmbEGk6IOgq3HDKFbSmY1D9nrX2CFoqK1+QWkf1PbYy0wja7ZH\np1HRPM7Bf89Sjv52EOfVvJmwmNgAYmIDPu0BnyZs95JFv15dX1YrxodRA9ep0sXm\nc6ODU91BCH8fcIZV4voYDYr97oHdQPwsh6GH5fjuKdYZE5gjSFQ5LN0OVO/kVsxP\nNOVvgQ2zr16gU7+kDHWQFptPbM49sYq14Z3aYSCit19V1xrs/2zE7fDr26lVeie4\nHyX4wsfhAgMBAAECggEAFKPQ8VaXVoZNDF02/WYGsLzYutjgXe6tYD7/F+wwG+KC\n5p86X27B03O3qAikqA9eqCLLG2ul+5zgFkZeyg7AUVUihCHTEaDPK3VhX2++t1zV\n4hOqYQjSWAEMwFLRrX+sUL75ZLBihMOvU7R6Ij8Z8UyUUVU88A716Ugw50LFkFAi\nUgvys5+Dq6QAAuOUxf/wiH4sWsd+HHBQrFPDDrUrYSIEUpRn+KyMk7JC92AGEeBC\nB4n7LBzsQHA1q3m9AbiryijEf7U2iREk2ogvSMx0cmnCUWhe7ntUr0TBEMyp9Gjd\neNYR16li8vE/DmhYAyFk9VntovjHlLvEInL1pm3nzQKBgQDMGsw+Hq6hEuRYvEEj\niDLUTvzS1b55dWN+neTWRSSAU7rS71Ph8pgJ8Ba6zkbnmSpFlXdc0OKiJj0ojUQB\n0pipDEiL436xMwIyDxIGtACgfdDaSE5VDNJIMbGT2rKFIs0sAz54ta7m3klGkoxv\nKqDTTw4vzXBq6poGTkwvq3SWxwKBgQC3FJzfkssxCU8Y+GsYVz1mJImzys6qszii\nH75qm+j46pzjZMW/IGYA6urjenaVbzO/58FvVmf3aOjj66+HaaPxCx5r80qJ/Y5z\nh4SVnBlvJddQdavY2IZ4/TnJc0x0quDILY2bRl1YVsgLIC7ldF+JR361WnbGmBZ/\n/r9RbQDkFwKBgQC4nXimq7wLJ/pxpP5KdHR9g2C+F+YtGJhBO/J0OaBVjubKyv2V\nv4f0XT1CBTkvXWRWAmcwBiUow+vH1VdxSpsZNWodVk4PW/MdcnI8q+5xPLx/5K0t\n0uYVtRVJhqS5NgxdRiXoq3Wjj+a5te7e0rEb0UMky+9mVgOxtnrFF5HimwKBgQCw\nrX66L0JRXydq9Ij9Y2tIQCh3Tldk2jHkGvg6j50jYd3nf99pk418gzgMMKlxinAJ\nWVEN7ffFBgQBt/ebmTHxswY3rQCY7A1lpjkn3A2beowwBG7DhCk5Zz42D0OQLztf\nUr6eQyhf0NeaDQPUwRpVM56uys9m1WyZNrFQlf3PHwKBgHoXH2y4sdGa30AbswCO\n2K9bYOmaD565nbd6z/gRuYnjEyO0VU0EYzuHVvv0FlCcUum93HacOrX0p/HOLPnF\njcw08zC78mz8/6AkhA/9q+DK2540TtmYr3pifMj5AHpKS/Rye5Uy3bfCnueaGo/p\nCldLBLGnpNQ5tHY8O+f4yFUS\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"107521792510622514168","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/supabase-play-verification%40ai-job-assistent.iam.gserviceaccount.com","universe_domain":"googleapis.com"}'

# Sandbox Mode deaktivieren für Produktion
supabase secrets set IS_SANDBOX_MODE=false
```

## 2. Google Play Console Konfiguration

### Real-time Developer Notifications:
1. Gehe zu Google Play Console
2. Navigiere zu Monetization > Monetization setup
3. Setze die Real-time developer notifications URL:
   ```
   https://vpttdxibvjrfjzbtktqg.supabase.co/functions/v1/google-play-webhook-no-jwt
   ```

### Produkt-IDs konfigurieren:
Stelle sicher, dass folgende Subscription-IDs in Google Play Console erstellt sind:
- `pro_subscription` (Monatlich, €19,99 - 150 Bewerbungen/Monat, ohne Werbung)
- `unlimited_subscription` (Monatlich, €39,99 - unbegrenzte Bewerbungen, ohne Werbung + personalisierter Stil)

**Hinweis**: Free-Plan ist kostenlos und benötigt keine Google Play Subscription-ID.

## 3. Service Account Berechtigungen

Stelle sicher, dass der Service Account folgende Berechtigungen hat:
- Google Play Developer API access
- View financial data
- Manage orders and subscriptions

## 4. Edge Functions deployen

```bash
# Deploy verify-purchase function
supabase functions deploy verify-purchase

# Deploy google-play-webhook function
supabase functions deploy google-play-webhook
```

## 5. Testing

### Test Purchase Verification:
```bash
curl -X POST 'https://vpttdxibvjrfjzbtktqg.supabase.co/functions/v1/verify-purchase' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "platform": "android",
    "productId": "basic_subscription",
    "purchaseToken": "test_purchase_token",
    "packageName": "com.einsteinai.app",
    "userId": "test_user_id"
  }'
```

### Test Webhook:
```bash
curl -X POST 'https://vpttdxibvjrfjzbtktqg.supabase.co/functions/v1/google-play-webhook' \
  -H 'Content-Type: application/json' \
  -d '{
    "message": {
      "data": "************************************************************************************************************************************************************************"
    }
  }'
```

## 6. App Package Name

Stelle sicher, dass der Package Name in der App korrekt ist:
- Android: `com.einsteinai.app`
- Dieser muss mit Google Play Console übereinstimmen

## 7. Produktionsreife Checkliste

- [ ] Service Account JSON in Supabase Environment Variables gesetzt
- [ ] Google Play Console Real-time Notifications URL konfiguriert
- [ ] Subscription-Produkte in Google Play Console erstellt
- [ ] Service Account Berechtigungen korrekt gesetzt
- [ ] Edge Functions deployed
- [ ] Package Name in App und Google Play Console identisch
- [ ] IS_SANDBOX_MODE=false für Produktion
- [ ] Purchase Verification getestet
- [ ] Webhook Notifications getestet

## Troubleshooting

### Häufige Probleme:
1. **JWT Signature Fehler**: Prüfe Private Key Format in Service Account JSON
2. **API Access Denied**: Prüfe Service Account Berechtigungen in Google Cloud Console
3. **Package Name Mismatch**: Stelle sicher, dass Package Names überall identisch sind
4. **Webhook nicht erreichbar**: Prüfe Supabase Edge Function URL und CORS-Einstellungen

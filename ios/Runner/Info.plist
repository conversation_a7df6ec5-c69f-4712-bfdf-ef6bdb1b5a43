<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Ki Test</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>ki_test</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>

	<!-- Standortberechtigungen -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Dein Standort wird benötigt, um passende Jobs in deiner Nähe zu finden.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Dein Standort wird benötigt, um passende Jobs in deiner Nähe zu finden, auch wenn die App im Hintergrund ist (optional).</string>
	<key>NSLocationUsageDescription</key>
	<string>Dein Standort wird benötigt, um passende Jobs in deiner Nähe zu finden.</string>

	<!-- Optional: Für ältere iOS-Versionen -->
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Dein Standort wird benötigt, um passende Jobs in deiner Nähe zu finden, auch wenn die App im Hintergrund ist (optional).</string>

	<!-- URL-Schemas für OAuth -->
	<key>CFBundleURLTypes</key>
	<array>
		<!-- Supabase OAuth Callback -->
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.einsteinai.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.einsteinai.app</string>
			</array>
		</dict>
		<!-- Google Sign-In -->
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google-sign-in</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.341017562213-rq7t5vqfe0r57jq27nrpa09c3fth6etu</string>
			</array>
		</dict>
	</array>
</dict>
</plist>

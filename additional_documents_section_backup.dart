import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:ki_test/src/application/providers/additional_documents_provider.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/domain/models/additional_document.dart';
import 'package:ki_test/src/presentation/profile/widgets/document_preview_screen.dart';

/// Widget für die Verwaltung zusätzlicher Dokumente im Profil-Bearbeitungsmodus
class AdditionalDocumentsSection extends ConsumerWidget {
  const AdditionalDocumentsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final documentsAsync = ref.watch(additionalDocumentsProvider);
    final uploadProgress = ref.watch(documentUploadProgressProvider);
    final userProfileAsync = ref.watch(userProfileProvider);
    final availableSlots = 5 - (documentsAsync.valueOrNull?.length ?? 0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Info-Text
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.blue[300]),
                  const SizedBox(width: 8),
                  Text(
                    'Zusätzliche Bewerbungsunterlagen',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue[300],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '• Dein Lebenslauf wird automatisch angezeigt (falls hochgeladen)\n'
                '• Lade bis zu 5 zusätzliche Dokumente hoch (Zeugnisse, Zertifikate, etc.)\n'
                '• Hake Dokumente an, die automatisch bei jeder Bewerbung mitgesendet werden sollen',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[300],
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        // Upload-Progress
        if (uploadProgress != null) _UploadProgressWidget(progress: uploadProgress),

        // Dokumente-Liste oder Empty State
        documentsAsync.when(
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(AppTheme.spacingMedium),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stack) => _ErrorWidget(
            message: 'Fehler beim Laden der Dokumente: $error',
            onRetry: () => ref.refresh(additionalDocumentsProvider),
          ),
          data: (documents) {
            return userProfileAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Fehler: $error'),
              data: (userProfile) {
                final hasCv = userProfile.cvDownloadUrl != null && userProfile.cvDownloadUrl!.isNotEmpty;
                final hasDocuments = documents.isNotEmpty;

                // Debug-Ausgabe
                print('DEBUG AdditionalDocuments: hasCv=$hasCv');
                print('DEBUG AdditionalDocuments: cvDownloadUrl=${userProfile.cvDownloadUrl}');
                print('DEBUG AdditionalDocuments: cvFileName=${userProfile.cvFileName}');
                print('DEBUG AdditionalDocuments: cvFilePath=${userProfile.cvFilePath}');
                print('DEBUG AdditionalDocuments: hasDocuments=$hasDocuments, documentsCount=${documents.length}');

                if (!hasCv && !hasDocuments) {
                  return _EmptyStateWidget(
                    onUpload: availableSlots > 0
                        ? () => _pickAndUploadDocument(context, ref)
                        : null,
                  );
                }

                return Column(
                  children: [
                    _DocumentsList(
                      documents: documents,
                      cvFileName: hasCv ? userProfile.cvFileName : null,
                      cvDownloadUrl: hasCv ? userProfile.cvDownloadUrl : null,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    if (availableSlots > 0)
                      _UploadButton(
                        onPressed: () => _pickAndUploadDocument(context, ref),
                        isUploading: uploadProgress != null,
                      ),
                  ],
                );
              },
            );
          },
        ),

        // Verfügbare Slots Info
        if (availableSlots <= 2 && (documentsAsync.valueOrNull?.isNotEmpty ?? false))
          Padding(
            padding: const EdgeInsets.only(top: AppTheme.spacingSmall),
            child: Text(
              availableSlots > 0
                  ? 'Noch $availableSlots von 5 Dokumenten verfügbar'
                  : 'Maximale Anzahl von 5 Dokumenten erreicht',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: availableSlots > 0 ? Colors.orange[700] : Colors.red[700],
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
      ],
    );
  }

  Future<void> _pickAndUploadDocument(BuildContext context, WidgetRef ref) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: SupportedFileType.allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          final uploadResult = await ref
              .read(additionalDocumentsProvider.notifier)
              .uploadDocument(
                filePath: filePath,
                fileName: file.name,
              );

          if (!uploadResult.success && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(uploadResult.errorMessage ?? 'Upload fehlgeschlagen'),
                backgroundColor: Colors.red,
              ),
            );
          } else if (uploadResult.success && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Dokument erfolgreich hochgeladen'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Datei-Upload: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Upload-Button Widget
class _UploadButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isUploading;

  const _UploadButton({
    required this.onPressed,
    required this.isUploading,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: isUploading ? null : onPressed,
      icon: isUploading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            )
          : const Icon(Icons.upload_file, size: 18),
      label: Text(isUploading ? 'Lädt...' : 'Weiteres Dokument hochladen'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        minimumSize: const Size(double.infinity, 48),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}

/// Upload-Progress Widget
class _UploadProgressWidget extends StatelessWidget {
  final DocumentUploadProgress progress;

  const _UploadProgressWidget({required this.progress});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                progress.status == DocumentUploadStatus.failed
                    ? Icons.error
                    : Icons.upload_file,
                size: 20,
                color: progress.status == DocumentUploadStatus.failed
                    ? Colors.red
                    : Colors.blue,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  progress.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          LinearProgressIndicator(
            value: progress.progress,
            backgroundColor: Colors.grey.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              progress.status == DocumentUploadStatus.failed
                  ? Colors.red
                  : Colors.blue,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            _getStatusText(progress.status),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: progress.status == DocumentUploadStatus.failed
                      ? Colors.red
                      : Colors.blue[200],
                ),
          ),
          if (progress.errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                progress.errorMessage!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                    ),
              ),
            ),
        ],
      ),
    );
  }

  String _getStatusText(DocumentUploadStatus status) {
    switch (status) {
      case DocumentUploadStatus.preparing:
        return 'Vorbereitung...';
      case DocumentUploadStatus.uploading:
        return 'Upload läuft...';
      case DocumentUploadStatus.processing:
        return 'Verarbeitung...';
      case DocumentUploadStatus.completed:
        return 'Abgeschlossen';
      case DocumentUploadStatus.failed:
        return 'Fehlgeschlagen';
    }
  }
}

/// Dokumente-Liste Widget
class _DocumentsList extends StatelessWidget {
  final List<AdditionalDocument> documents;
  final String? cvFileName;
  final String? cvDownloadUrl;

  const _DocumentsList({
    required this.documents,
    this.cvFileName,
    this.cvDownloadUrl,
  });

  @override
  Widget build(BuildContext context) {
    final List<Widget> items = [];

    // CV als erstes Element hinzufügen (falls vorhanden)
    if (cvDownloadUrl != null && cvDownloadUrl!.isNotEmpty) {
      final displayName = cvFileName?.isNotEmpty == true ? cvFileName! : 'Lebenslauf.pdf';
      items.add(_CvDocumentItem(
        fileName: displayName,
        downloadUrl: cvDownloadUrl!,
      ));
    }

    // Zusätzliche Dokumente hinzufügen
    items.addAll(documents.map((doc) => _MinimalDocumentItem(document: doc)));

    return Column(children: items);
  }
}

/// Einzelnes Dokument Widget
class _DocumentItem extends ConsumerWidget {
  final AdditionalDocument document;

  const _DocumentItem({required this.document});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getFileIcon(document.fileType),
                size: 20,
                color: AppTheme.primaryLightColor,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  document.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                onPressed: () => _openPreview(context),
                icon: const Icon(Icons.visibility_outlined),
                iconSize: 20,
                color: AppTheme.primaryLightColor,
                tooltip: 'Vorschau',
              ),
              IconButton(
                onPressed: () => _showDeleteDialog(context, ref),
                icon: const Icon(Icons.delete_outline),
                iconSize: 20,
                color: Colors.red[400],
                tooltip: 'Dokument löschen',
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            children: [
              Text(
                _formatFileSize(document.fileSize),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[400],
                    ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Text(
                _formatUploadDate(document.uploadDate),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[400],
                    ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            children: [
              Checkbox(
                value: document.isActiveForApplications,
                onChanged: (value) => _toggleApplicationStatus(ref, value ?? false),
                activeColor: AppTheme.primaryColor,
                checkColor: Colors.black,
                side: BorderSide(color: Colors.grey[400]!),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Automatisch bei Bewerbungen anhängen',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      'Wird bei jeder E-Mail-Bewerbung mitgesendet',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileType) {
    if (fileType.contains('pdf')) {
      return Icons.picture_as_pdf_outlined;
    } else if (fileType.contains('word') || fileType.contains('document')) {
      return Icons.description_outlined;
    }
    return Icons.insert_drive_file_outlined;
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatUploadDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} Tag${difference.inDays == 1 ? '' : 'e'} alt';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} Stunde${difference.inHours == 1 ? '' : 'n'} alt';
    } else {
      return 'Gerade hochgeladen';
    }
  }

  void _toggleApplicationStatus(WidgetRef ref, bool isActive) {
    ref.read(additionalDocumentsProvider.notifier).updateDocumentApplicationStatus(
      documentId: document.id,
      isActive: isActive,
    );
  }

  void _openPreview(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentPreviewScreen(document: document),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E1E1E),
        title: const Text('Dokument löschen'),
        content: Text('Möchten Sie "${document.fileName}" wirklich löschen?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Abbrechen'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(additionalDocumentsProvider.notifier).deleteDocument(document.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red[400]),
            child: const Text('Löschen'),
          ),
        ],
      ),
    );
  }
}

/// Empty State Widget
class _EmptyStateWidget extends StatelessWidget {
  final VoidCallback? onUpload;

  const _EmptyStateWidget({this.onUpload});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        children: [
          Icon(
            Icons.folder_open_outlined,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Keine Dokumente hochgeladen',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[400],
                ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            'Lade Zeugnisse, Zertifikate oder Referenzen hoch,\ndie automatisch bei Bewerbungen angehängt werden',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
          if (onUpload != null) ...[
            const SizedBox(height: AppTheme.spacingLarge),
            ElevatedButton.icon(
              onPressed: onUpload,
              icon: const Icon(Icons.add_circle_outline, size: 20),
              label: const Text('Dokument hochladen'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 52),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Error Widget
class _ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const _ErrorWidget({
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red[300],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('Erneut versuchen'),
          ),
        ],
      ),
    );
  }
}

/// CV-Dokument Widget (minimale Darstellung)
class _CvDocumentItem extends ConsumerWidget {
  final String fileName;
  final String downloadUrl;

  const _CvDocumentItem({
    required this.fileName,
    required this.downloadUrl,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Prüfe ob CV für Bewerbungen aktiviert ist (aus SharedPreferences)
    final isCvActiveForApplications = ref.watch(cvApplicationStatusProvider);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description,
            size: 18,
            color: Colors.blue[300],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              fileName.isNotEmpty ? fileName : 'Lebenslauf.pdf',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.blue[300],
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'CV',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.blue[400],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Checkbox(
            value: isCvActiveForApplications,
            onChanged: (value) {
              ref.read(cvApplicationStatusProvider.notifier).state = value ?? false;
            },
            activeColor: Colors.blue,
            checkColor: Colors.white,
            side: BorderSide(color: Colors.grey[400]!),
          ),
        ],
      ),
    );
  }
}

/// Minimales zusätzliches Dokument Widget
class _MinimalDocumentItem extends ConsumerWidget {
  final AdditionalDocument document;

  const _MinimalDocumentItem({required this.document});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(document.fileType),
            size: 18,
            color: AppTheme.primaryLightColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              document.fileName,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _openPreview(context),
            icon: const Icon(Icons.visibility_outlined),
            iconSize: 16,
            color: AppTheme.primaryLightColor,
            tooltip: 'Vorschau',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          IconButton(
            onPressed: () => _showDeleteDialog(context, ref),
            icon: const Icon(Icons.delete_outline),
            iconSize: 16,
            color: Colors.red[400],
            tooltip: 'Löschen',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          Checkbox(
            value: document.isActiveForApplications,
            onChanged: (value) => _toggleApplicationStatus(ref, value ?? false),
            activeColor: AppTheme.primaryColor,
            checkColor: Colors.white,
            side: BorderSide(color: Colors.grey[400]!),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _toggleApplicationStatus(WidgetRef ref, bool isActive) {
    ref.read(additionalDocumentsProvider.notifier).updateDocumentApplicationStatus(
      documentId: document.id,
      isActive: isActive,
    );
  }

  void _openPreview(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentPreviewScreen(document: document),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E1E1E),
        title: const Text('Dokument löschen'),
        content: Text('Möchten Sie "${document.fileName}" wirklich löschen?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Abbrechen'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(additionalDocumentsProvider.notifier).deleteDocument(document.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red[400]),
            child: const Text('Löschen'),
          ),
        ],
      ),
    );
  }
}

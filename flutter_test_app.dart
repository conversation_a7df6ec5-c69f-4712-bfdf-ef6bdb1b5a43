// 🧪 Flutter Test App für Kaufverifizierung
// Diese <PERSON> zeigt, wie die verify-purchase Edge Function in einer Flutter App integriert wird

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PurchaseTestApp extends StatefulWidget {
  const PurchaseTestApp({super.key});

  @override
  _PurchaseTestAppState createState() => _PurchaseTestAppState();
}

class _PurchaseTestAppState extends State<PurchaseTestApp> {
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  final SupabaseClient supabase = Supabase.instance.client;

  bool _isAvailable = false;
  List<ProductDetails> _products = [];
  final List<PurchaseDetails> _purchases = [];
  String _testResult = '';
  bool _isLoading = false;

  // Produkt-IDs (müssen in Google Play Console und App Store Connect konfiguriert sein)
  // Nur monatliche Abonnements verfügbar - Pro/Unlimited System (Free ist kostenlos)
  static const String proSubscriptionId = 'pro_subscription';
  static const String unlimitedSubscriptionId = 'unlimited_subscription';
  static const Set<String> _productIds = {proSubscriptionId, unlimitedSubscriptionId};

  @override
  void initState() {
    super.initState();
    _initializeInAppPurchase();
  }

  Future<void> _initializeInAppPurchase() async {
    final bool isAvailable = await _inAppPurchase.isAvailable();
    setState(() {
      _isAvailable = isAvailable;
    });

    if (isAvailable) {
      await _loadProducts();
      await _loadPurchases();
    }
  }

  Future<void> _loadProducts() async {
    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails(_productIds);
    setState(() {
      _products = response.productDetails;
    });
  }

  Future<void> _loadPurchases() async {
    await _inAppPurchase.restorePurchases();
  }

  // Hauptfunktion: Kauf verifizieren mit Supabase Edge Function
  Future<void> _verifyPurchase(PurchaseDetails purchase) async {
    setState(() {
      _isLoading = true;
      _testResult = 'Verifiziere Kauf...';
    });

    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Benutzer nicht angemeldet');
      }

      // Platform-spezifische Daten vorbereiten
      Map<String, dynamic> requestData = {
        'userId': user.id,
        'productId': purchase.productID,
      };

      // iOS vs Android unterscheiden
      if (purchase.verificationData.source == 'app_store') {
        requestData.addAll({
          'platform': 'ios',
          'receiptData': purchase.verificationData.serverVerificationData,
        });
      } else if (purchase.verificationData.source == 'google_play') {
        requestData.addAll({
          'platform': 'android',
          'receiptData': purchase.verificationData.serverVerificationData,
          'purchaseToken': purchase.purchaseID,
          'packageName': 'com.example.app', // Deine App Package Name
        });
      }

      // Edge Function aufrufen
      final response = await supabase.functions.invoke(
        'verify-purchase',
        body: requestData,
      );

      if (response.status == 200) {
        final data = response.data;
        if (data['isValid'] == true) {
          setState(() {
            _testResult =
                '✅ Kauf erfolgreich verifiziert!\n'
                'Subscription ID: ${data['subscriptionId']}\n'
                'Ablaufdatum: ${data['expireDate']}';
          });

          // Kauf als abgeschlossen markieren
          await _inAppPurchase.completePurchase(purchase);

          // Premium-Features freischalten
          await _unlockPremiumFeatures();
        } else {
          throw Exception('Kaufverifizierung fehlgeschlagen');
        }
      } else {
        throw Exception('Server-Fehler: ${response.status}');
      }
    } catch (e) {
      setState(() {
        _testResult = '❌ Fehler bei Kaufverifizierung: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _unlockPremiumFeatures() async {
    // Hier würdest du die Premium-Features in deiner App freischalten
    print('🎉 Premium-Features freigeschaltet!');
  }

  // Test-Kauf durchführen
  Future<void> _buyProduct(ProductDetails product) async {
    final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);

    if (product.id == proSubscriptionId || product.id == unlimitedSubscriptionId) {
      await _inAppPurchase.buyAutoRenewingSubscription(purchaseParam: purchaseParam);
    }
  }

  // Manuelle Test-Verifizierung (für Entwicklung)
  Future<void> _testManualVerification() async {
    setState(() {
      _isLoading = true;
      _testResult = 'Führe manuellen Test durch...';
    });

    try {
      final response = await http.post(
        Uri.parse(
          'https://vpttdxibvjrfjzbtktqg.supabase.co/functions/v1/verify-purchase',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Authorization':
              'Bearer ${supabase.auth.currentSession?.accessToken ?? "anon-key"}',
        },
        body: jsonEncode({
          'userId': 'test-user-flutter-123',
          'platform': 'android',
          'receiptData': 'test-receipt-data-flutter',
          'purchaseToken': 'test-purchase-token-flutter',
          'packageName': 'com.example.testapp',
          'productId': 'monthly_premium',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _testResult =
              '✅ Manueller Test erfolgreich!\n'
              'Response: ${jsonEncode(data)}';
        });
      } else {
        setState(() {
          _testResult =
              '❌ Manueller Test fehlgeschlagen\n'
              'Status: ${response.statusCode}\n'
              'Body: ${response.body}';
        });
      }
    } catch (e) {
      setState(() {
        _testResult = '❌ Fehler beim manuellen Test: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🧪 Kaufverifizierung Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status-Anzeige
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📊 System Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'In-App Purchase verfügbar: ${_isAvailable ? "✅" : "❌"}',
                    ),
                    Text('Verfügbare Produkte: ${_products.length}'),
                    Text('Aktive Käufe: ${_purchases.length}'),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Verfügbare Produkte
            Text(
              '🛒 Verfügbare Produkte',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 2,
              child: ListView.builder(
                itemCount: _products.length,
                itemBuilder: (context, index) {
                  final product = _products[index];
                  return Card(
                    child: ListTile(
                      title: Text(product.title),
                      subtitle: Text(
                        '${product.price} - ${product.description}',
                      ),
                      trailing: ElevatedButton(
                        onPressed:
                            _isLoading ? null : () => _buyProduct(product),
                        child: Text('Kaufen'),
                      ),
                    ),
                  );
                },
              ),
            ),

            SizedBox(height: 16),

            // Test-Buttons
            Text(
              '🧪 Test-Funktionen',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testManualVerification,
                    child: Text('Manueller Test'),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _loadProducts,
                    child: Text('Produkte laden'),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Test-Ergebnisse
            Text(
              '📋 Test-Ergebnisse',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResult.isEmpty
                        ? 'Keine Tests durchgeführt'
                        : _testResult,
                    style: TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),

            if (_isLoading)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Purchase-Stream cleanup würde hier stattfinden
    super.dispose();
  }
}

// Hauptfunktion für die Test-App
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Supabase initialisieren
  await Supabase.initialize(
    url: 'https://vpttdxibvjrfjzbtktqg.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZwdHRkeGlidmpyZmp6YnRrdHFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxODYzNzQsImV4cCI6MjA2MDc2MjM3NH0.WRijbwvWdLPVo0fhk_G9ppQR0nznCTZf7BvFDF55psg',
  );

  runApp(
    MaterialApp(
      title: 'Kaufverifizierung Test',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: PurchaseTestApp(),
    ),
  );
}

/*
📋 INTEGRATION CHECKLIST:

1. Dependencies in pubspec.yaml hinzufügen:
   dependencies:
     flutter:
       sdk: flutter
     in_app_purchase: ^3.1.11
     supabase_flutter: ^2.0.0
     http: ^1.1.0

2. Android Konfiguration (android/app/build.gradle):
   dependencies {
       implementation 'com.android.billingclient:billing:6.0.1'
   }

3. iOS Konfiguration (ios/Runner/Info.plist):
   <key>SKAdNetworkItems</key>
   <array>
       <dict>
           <key>SKAdNetworkIdentifier</key>
           <string>cstr6suwn9.skadnetwork</string>
       </dict>
   </array>

4. Google Play Console:
   - App hochladen und Produkte konfigurieren
   - Test-Konten einrichten
   - Lizenz-Tester hinzufügen

5. App Store Connect:
   - App einreichen und Produkte konfigurieren
   - Sandbox-Tester einrichten

6. Supabase Edge Function:
   - verify-purchase Function deployed ✅
   - Google Service Account konfiguriert ✅
   - Umgebungsvariablen gesetzt ✅

🎯 NÄCHSTE SCHRITTE:
1. Flutter App mit echten Produkt-IDs konfigurieren
2. Test-Käufe in Google Play Console durchführen
3. End-to-End Tests mit echten Zahlungen
4. Monitoring und Logging einrichten
5. Produktions-Deployment vorbereiten
*/

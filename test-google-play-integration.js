#!/usr/bin/env node

/**
 * Test-Script für Google Play Billing Integration
 * 
 * Dieses Script testet:
 * 1. Purchase Verification Edge Function
 * 2. Google Play Webhook Edge Function
 * 3. Service Account Authentifizierung
 */

const SUPABASE_URL = 'https://vpttdxibvjrfjzbtktqg.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WRijbwvWdLPVo0fhk_G9ppQR0nznCTZf7BvFDF55psg';

/**
 * Test Purchase Verification
 */
async function testPurchaseVerification() {
  console.log('\n🧪 Testing Purchase Verification...');
  
  const testData = {
    platform: 'android',
    productId: 'pro_subscription',
    purchaseToken: 'test_purchase_token_' + Date.now(),
    packageName: 'com.einsteinai.app',
    userId: 'test_user_' + Date.now(),
    receiptData: 'test_receipt_data_' + Date.now()
  };

  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/verify-purchase`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Purchase Verification Test erfolgreich');
      console.log('📄 Response:', JSON.stringify(result, null, 2));
    } else {
      console.log('❌ Purchase Verification Test fehlgeschlagen');
      console.log('📄 Error:', JSON.stringify(result, null, 2));
    }
  } catch (error) {
    console.log('❌ Purchase Verification Test Fehler:', error.message);
  }
}

/**
 * Test Google Play Webhook
 */
async function testGooglePlayWebhook() {
  console.log('\n🧪 Testing Google Play Webhook...');
  
  // Test Notification (Base64-kodiert)
  const testNotification = {
    version: "1.0",
    packageName: "com.einsteinai.app",
    eventTimeMillis: Date.now().toString(),
    testNotification: {
      version: "1.0"
    }
  };

  // Base64-kodiere die Test-Notification
  const encodedNotification = btoa(JSON.stringify(testNotification));
  
  const webhookData = {
    message: {
      data: encodedNotification
    }
  };

  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/google-play-webhook-no-jwt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Google Play Webhook Test erfolgreich');
      console.log('📄 Response:', JSON.stringify(result, null, 2));
    } else {
      console.log('❌ Google Play Webhook Test fehlgeschlagen');
      console.log('📄 Error:', JSON.stringify(result, null, 2));
    }
  } catch (error) {
    console.log('❌ Google Play Webhook Test Fehler:', error.message);
  }
}

/**
 * Test Subscription Notification
 */
async function testSubscriptionNotification() {
  console.log('\n🧪 Testing Subscription Notification...');
  
  // Subscription Notification (Base64-kodiert)
  const subscriptionNotification = {
    version: "1.0",
    packageName: "com.einsteinai.app",
    eventTimeMillis: Date.now().toString(),
    subscriptionNotification: {
      version: "1.0",
      notificationType: 4, // SUBSCRIPTION_PURCHASED
      purchaseToken: "test_purchase_token_" + Date.now(),
      subscriptionId: "pro_subscription"
    }
  };

  // Base64-kodiere die Subscription-Notification
  const encodedNotification = btoa(JSON.stringify(subscriptionNotification));
  
  const webhookData = {
    message: {
      data: encodedNotification
    }
  };

  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/google-play-webhook-no-jwt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Subscription Notification Test erfolgreich');
      console.log('📄 Response:', JSON.stringify(result, null, 2));
    } else {
      console.log('❌ Subscription Notification Test fehlgeschlagen');
      console.log('📄 Error:', JSON.stringify(result, null, 2));
    }
  } catch (error) {
    console.log('❌ Subscription Notification Test Fehler:', error.message);
  }
}

/**
 * Test Edge Function Erreichbarkeit
 */
async function testEdgeFunctionAvailability() {
  console.log('\n🧪 Testing Edge Function Availability...');
  
  const functions = [
    'verify-purchase',
    'google-play-webhook-no-jwt'
  ];

  for (const functionName of functions) {
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, {
        method: 'OPTIONS'
      });
      
      if (response.ok || response.status === 405) {
        console.log(`✅ ${functionName} ist erreichbar`);
      } else {
        console.log(`❌ ${functionName} nicht erreichbar (Status: ${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${functionName} Fehler:`, error.message);
    }
  }
}

/**
 * Hauptfunktion
 */
async function main() {
  console.log('🚀 Google Play Billing Integration Tests');
  console.log('==========================================');
  
  await testEdgeFunctionAvailability();
  await testPurchaseVerification();
  await testGooglePlayWebhook();
  await testSubscriptionNotification();
  
  console.log('\n✨ Tests abgeschlossen!');
  console.log('\n📋 Nächste Schritte:');
  console.log('1. Setze Environment Variables in Supabase');
  console.log('2. Konfiguriere Google Play Console Real-time Notifications');
  console.log('3. Erstelle Subscription-Produkte in Google Play Console');
  console.log('4. Teste mit echten Purchase Tokens');
}

// Führe Tests aus
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testPurchaseVerification,
  testGooglePlayWebhook,
  testSubscriptionNotification,
  testEdgeFunctionAvailability
};

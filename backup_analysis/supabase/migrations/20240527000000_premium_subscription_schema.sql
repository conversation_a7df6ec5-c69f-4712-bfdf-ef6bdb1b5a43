-- Premium-Subscription-Schema Migration
-- Erstellt die notwendigen Tabellen für das Premium-Abonnement-System

-- Erweiterung der users Tabelle mit Premium-Feldern (falls die Tabelle bereits existiert)
ALTER TABLE IF EXISTS public.users
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS premium_expiry_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS purchase_token TEXT;

-- <PERSON><PERSON><PERSON> die subscriptions Tabelle für Abonnements
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  subscription_type TEXT NOT NULL, -- 'monthly', 'yearly'
  status TEXT NOT NULL, -- 'active', 'cancelled', 'expired'
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON> die purchases Tabelle für Kaufhistorie
CREATE TABLE IF NOT EXISTS public.purchases (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  subscription_id UUID REFERENCES public.subscriptions,
  amount DECIMAL,
  currency TEXT,
  payment_method TEXT,
  receipt_data TEXT, -- Rohdaten des Kaufbelegs
  purchase_date TIMESTAMPTZ,
  platform TEXT, -- 'ios', 'android'
  transaction_id TEXT,
  verification_status TEXT, -- 'verified', 'pending', 'failed'
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Erstelle die ad_access Tabelle für werbebasierte Premium-Zugänge
CREATE TABLE IF NOT EXISTS public.ad_access (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  granted_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  ad_id TEXT, -- Identifikator für die gesehene Werbung
  feature TEXT, -- Welches Premium-Feature wurde freigeschaltet
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Funktionen und Trigger für Abonnement-Verwaltung

-- Funktion zur Aktualisierung des Premium-Status im Nutzerprofil
CREATE OR REPLACE FUNCTION public.update_user_premium_status()
RETURNS TRIGGER AS $$
DECLARE
  active_subscription RECORD;
BEGIN
  -- Prüfe, ob es ein aktives Abonnement für den Nutzer gibt
  SELECT INTO active_subscription *
  FROM public.subscriptions
  WHERE user_id = NEW.user_id
    AND status = 'active'
    AND end_date > NOW()
  ORDER BY end_date DESC
  LIMIT 1;
  
  -- Aktualisiere den Premium-Status im Nutzerprofil
  IF active_subscription.id IS NOT NULL THEN
    UPDATE public.users
    SET is_premium = TRUE,
        premium_expiry_date = active_subscription.end_date
    WHERE id = NEW.user_id;
  ELSE
    UPDATE public.users
    SET is_premium = FALSE,
        premium_expiry_date = NULL
    WHERE id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger für Abonnement-Änderungen
CREATE TRIGGER on_subscription_change
AFTER INSERT OR UPDATE ON public.subscriptions
FOR EACH ROW
EXECUTE FUNCTION public.update_user_premium_status();

-- RLS (Row Level Security) für die Tabellen

-- Aktiviere RLS
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ad_access ENABLE ROW LEVEL SECURITY;

-- RLS-Richtlinien für subscriptions
CREATE POLICY "Benutzer können nur ihre eigenen Abonnements sehen"
  ON public.subscriptions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Benutzer können keine Abonnements direkt ändern"
  ON public.subscriptions FOR UPDATE
  USING (false);

-- RLS-Richtlinien für purchases
CREATE POLICY "Benutzer können nur ihre eigenen Käufe sehen"
  ON public.purchases FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Benutzer können keine Käufe direkt ändern"
  ON public.purchases FOR UPDATE
  USING (false);

-- RLS-Richtlinien für ad_access
CREATE POLICY "Benutzer können nur ihre eigenen werbebasierten Zugänge sehen"
  ON public.ad_access FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Benutzer können keine werbebasierten Zugänge direkt ändern"
  ON public.ad_access FOR UPDATE
  USING (false);

-- Index für schnellere Abfragen
CREATE INDEX IF NOT EXISTS subscription_user_id_idx ON public.subscriptions (user_id);
CREATE INDEX IF NOT EXISTS purchase_user_id_idx ON public.purchases (user_id);
CREATE INDEX IF NOT EXISTS ad_access_user_id_idx ON public.ad_access (user_id);
CREATE INDEX IF NOT EXISTS subscription_status_idx ON public.subscriptions (status); 
 
 
-- Erst<PERSON>t die Tabelle für Benutzer-Abonnements
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'expired')),
  product_id TEXT NOT NULL,
  platform TEXT NOT NULL,
  expires_at TIMESTAMPTZ,
  cancelled_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Füge expires_at Spalte hinzu falls sie nicht existiert
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'subscriptions' AND column_name = 'expires_at') THEN
        ALTER TABLE subscriptions ADD COLUMN expires_at TIMESTAMPTZ;
    END IF;
END $$;

-- Erstellt einen Index für schnelle Abfragen nach Benutzer-ID
CREATE INDEX IF NOT EXISTS subscriptions_user_id_idx ON subscriptions(user_id);

-- Erstellt einen Index für schnelle Abfragen nach Status und Ablaufdatum (nur wenn expires_at existiert)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'subscriptions' AND column_name = 'expires_at') THEN
        CREATE INDEX IF NOT EXISTS subscriptions_status_expires_idx ON subscriptions(status, expires_at);
    END IF;
END $$;

-- Sicherheitsrichtlinie: Abonnements können nur vom Benutzer selbst oder vom Service eingesehen werden
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'subscriptions' AND policyname = 'Users can view their own subscriptions') THEN
        CREATE POLICY "Users can view their own subscriptions"
        ON subscriptions FOR SELECT
        USING (auth.uid() = user_id);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'subscriptions' AND policyname = 'Service can manage all subscriptions') THEN
        CREATE POLICY "Service can manage all subscriptions"
        ON subscriptions
        USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Funktion zum Prüfen, ob ein Benutzer ein aktives Abonnement hat
CREATE OR REPLACE FUNCTION has_active_subscription(user_uuid UUID)
RETURNS BOOLEAN 
LANGUAGE SQL 
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM subscriptions
    WHERE user_id = user_uuid
    AND status = 'active'
    AND expires_at > now()
  );
$$;

-- Funktion zum Aktualisieren abgelaufener Abonnements
CREATE OR REPLACE FUNCTION update_expired_subscriptions()
RETURNS TRIGGER 
LANGUAGE PLPGSQL 
SECURITY DEFINER
AS $$
BEGIN
  UPDATE subscriptions
  SET status = 'expired', updated_at = now()
  WHERE status = 'active' AND expires_at < now();
  
  RETURN NULL;
END;
$$;

-- Erstellt einen Trigger, der stündlich abgelaufene Abonnements aktualisiert
CREATE OR REPLACE FUNCTION hourly_subscription_check()
RETURNS VOID
LANGUAGE PLPGSQL
SECURITY DEFINER
AS $$
BEGIN
  PERFORM update_expired_subscriptions();
END;
$$;

-- Funktion, die von der Edge-Funktion aufgerufen werden kann, um den Premium-Status eines Benutzers zu aktualisieren
CREATE OR REPLACE FUNCTION update_user_premium_status(p_user_id UUID, p_is_premium BOOLEAN, p_expiry_date TIMESTAMPTZ DEFAULT NULL)
RETURNS BOOLEAN
LANGUAGE PLPGSQL
SECURITY DEFINER
AS $$
DECLARE
  v_success BOOLEAN;
BEGIN
  -- Prüfen, ob das Profil des Benutzers existiert
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id) THEN
    -- Profil erstellen, falls es nicht existiert
    INSERT INTO profiles (id, is_premium, premium_expiry_date)
    VALUES (p_user_id, p_is_premium, p_expiry_date);
    v_success := TRUE;
  ELSE
    -- Profil aktualisieren
    UPDATE profiles
    SET is_premium = p_is_premium,
        premium_expiry_date = p_expiry_date,
        updated_at = now()
    WHERE id = p_user_id;
    v_success := FOUND;
  END IF;
  
  RETURN v_success;
END;
$$; 
 
 
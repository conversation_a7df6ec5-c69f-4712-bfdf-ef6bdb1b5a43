-- Aktualisiere die Funktion create_profile_for_new_user, um den Onboarding-Status auf false zu setzen
CREATE OR REPLACE FUNCTION public.create_profile_for_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, onboarding_complete)
    VALUES (NEW.id, false);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON>, dass der Trigger existiert
DROP TRIGGER IF EXISTS create_profile_after_auth_user_created ON auth.users;
CREATE TRIGGER create_profile_after_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.create_profile_for_new_user();

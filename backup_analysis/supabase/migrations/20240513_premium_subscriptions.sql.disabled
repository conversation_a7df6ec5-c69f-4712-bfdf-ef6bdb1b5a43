-- <PERSON><PERSON><PERSON> die Tabelle für Abonnements
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Abonnement-Details
  subscription_type VARCHAR(50) NOT NULL CHECK (subscription_type IN ('monthly', 'yearly', 'lifetime')),
  
  -- Status des Abonnements (aktiv, gekündigt, abgelaufen)
  status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'cancelled', 'expired')) DEFAULT 'active',
  
  -- Zahlungs-Informationen
  payment_provider VARCHAR(50) NOT NULL, -- z.B. 'apple', 'google', 'stripe', 'test'
  provider_transaction_id VARCHAR(255), -- Externe Transaktions-ID vom Anbieter
  receipt_data TEXT, -- Empfangsdaten für Validierung
  
  -- Zeitliche Daten
  start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  end_date TIMESTAMP WITH TIME ZONE,
  auto_renew BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Rollen-basierte Zugriffsrechte (RLS)
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Standardrichtlinie: Benutzer können nur ihre eigenen Abonnements sehen
CREATE POLICY "Users can view their own subscriptions"
  ON subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Nur Service-Rolle kann Abonnements erstellen oder aktualisieren
CREATE POLICY "Service role can manage all subscriptions"
  ON subscriptions
  USING (auth.jwt() ->> 'role' = 'service_role');

-- Trigger zum automatischen Aktualisieren des updated_at-Feldes
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = now();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscriptions_updated_at
BEFORE UPDATE ON subscriptions
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- Index für schnelle Abfragen
CREATE INDEX subscriptions_user_id_idx ON subscriptions(user_id);
CREATE INDEX subscriptions_status_idx ON subscriptions(status);

-- Standardprozeduren
-- Verwenden Sie diese Funktionen, um ein neues Abonnement hinzuzufügen oder zu aktualisieren

-- Funktion zum Hinzufügen eines neuen Abonnements
CREATE OR REPLACE FUNCTION add_subscription(
  p_user_id UUID,
  p_subscription_type VARCHAR(50),
  p_payment_provider VARCHAR(50),
  p_provider_transaction_id VARCHAR(255),
  p_receipt_data TEXT,
  p_duration_days INTEGER DEFAULT NULL,
  p_auto_renew BOOLEAN DEFAULT FALSE
)
RETURNS UUID AS $$
DECLARE
  v_end_date TIMESTAMP WITH TIME ZONE;
  v_subscription_id UUID;
BEGIN
  -- Endedatum berechnen, wenn eine Dauer angegeben ist
  IF p_duration_days IS NOT NULL THEN
    v_end_date := now() + (p_duration_days || ' days')::INTERVAL;
  ELSE
    v_end_date := NULL;
  END IF;
  
  -- Altes aktives Abonnement beenden, falls vorhanden
  UPDATE subscriptions
  SET status = 'cancelled', end_date = now(), auto_renew = FALSE
  WHERE user_id = p_user_id AND status = 'active';
  
  -- Neues Abonnement erstellen
  INSERT INTO subscriptions (
    user_id,
    subscription_type,
    payment_provider,
    provider_transaction_id,
    receipt_data,
    end_date,
    auto_renew
  ) VALUES (
    p_user_id,
    p_subscription_type,
    p_payment_provider,
    p_provider_transaction_id,
    p_receipt_data,
    v_end_date,
    p_auto_renew
  ) RETURNING id INTO v_subscription_id;
  
  RETURN v_subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funktion zum Verlängern eines Abonnements
CREATE OR REPLACE FUNCTION extend_subscription(
  p_subscription_id UUID,
  p_days INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
  v_new_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Aktuelles Enddatum abrufen und verlängern
  SELECT 
    CASE 
      WHEN end_date IS NULL THEN now() + (p_days || ' days')::INTERVAL
      ELSE end_date + (p_days || ' days')::INTERVAL
    END INTO v_new_end_date
  FROM subscriptions
  WHERE id = p_subscription_id;
  
  -- Abonnement aktualisieren
  UPDATE subscriptions
  SET end_date = v_new_end_date, status = 'active'
  WHERE id = p_subscription_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funktion zum Überprüfen des Abonnementstatus eines Benutzers
CREATE OR REPLACE FUNCTION is_premium_user(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_is_premium BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM subscriptions
    WHERE user_id = p_user_id
      AND status = 'active'
      AND (end_date IS NULL OR end_date > now())
  ) INTO v_is_premium;
  
  RETURN v_is_premium;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 
 
 
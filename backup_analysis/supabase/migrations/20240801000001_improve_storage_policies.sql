-- Migration zur Verbesserung der Storage-Bucket-Richtlinien
-- Diese Migration erstellt und verbessert die Richtlinien für die Storage-Buckets

-- Hinweis: Diese <PERSON>QL-Datei enthält Anweisungen, die manuell in der Supabase-Konsole ausgeführt werden müssen,
-- da Storage-Bucket-Richtlinien nicht direkt über SQL-Migrationen verwaltet werden können.

/*
ANLEITUNG ZUR MANUELLEN AUSFÜHRUNG:

1. Gehe zur Supabase-Konsole: https://app.supabase.com/project/vpttdxibvjrfjzbtktqg
2. Navigiere zu "Storage" -> "Policies"
3. Wähle den Bucket "cv-backups" aus (oder erstelle ihn, falls er nicht existiert)
4. Lösche alle bestehenden Richtlinien für diesen Bucket
5. Erstelle neue Richtlinien mit den unten angegebenen Definitionen
*/

-- Bucket: cv-backups
-- Dieser Bucket speichert die Lebensläufe der Benutzer

-- Richtlinie 1: Benutzer können nur ihre eigenen Dateien sehen
/*
Name: "Users can only view their own files"
Allowed operation: SELECT
Policy definition: 
  bucket_id = 'cv-backups' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 2: Benutzer können nur in ihren eigenen Ordner hochladen
/*
Name: "Users can only upload to their own folder"
Allowed operation: INSERT
Policy definition:
  bucket_id = 'cv-backups' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 3: Benutzer können nur ihre eigenen Dateien aktualisieren
/*
Name: "Users can only update their own files"
Allowed operation: UPDATE
Policy definition:
  bucket_id = 'cv-backups' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 4: Benutzer können nur ihre eigenen Dateien löschen
/*
Name: "Users can only delete their own files"
Allowed operation: DELETE
Policy definition:
  bucket_id = 'cv-backups' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 5: Service-Rolle hat vollen Zugriff
/*
Name: "Service role has full access"
Allowed operation: ALL
Policy definition:
  bucket_id = 'cv-backups' AND auth.role() = 'service_role'
*/

-- Bucket: cv-uploads
-- Dieser Bucket wird für temporäre Uploads zur Analyse verwendet

-- Richtlinie 1: Benutzer können nur ihre eigenen Dateien sehen
/*
Name: "Users can only view their own files"
Allowed operation: SELECT
Policy definition: 
  bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 2: Benutzer können nur in ihren eigenen Ordner hochladen
/*
Name: "Users can only upload to their own folder"
Allowed operation: INSERT
Policy definition:
  bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 3: Benutzer können nur ihre eigenen Dateien aktualisieren
/*
Name: "Users can only update their own files"
Allowed operation: UPDATE
Policy definition:
  bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 4: Benutzer können nur ihre eigenen Dateien löschen
/*
Name: "Users can only delete their own files"
Allowed operation: DELETE
Policy definition:
  bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]
*/

-- Richtlinie 5: Service-Rolle hat vollen Zugriff
/*
Name: "Service role has full access"
Allowed operation: ALL
Policy definition:
  bucket_id = 'cv-uploads' AND auth.role() = 'service_role'
*/

-- Richtlinie 6: Edge Functions können auf alle Dateien zugreifen
/*
Name: "Edge functions can access all files"
Allowed operation: SELECT
Policy definition:
  bucket_id = 'cv-uploads' AND auth.jwt()->>'role' = 'anon'
*/

-- WICHTIGE HINWEISE:
-- 1. Stelle sicher, dass die Ordnerstruktur in den Buckets dem Muster "{user_id}/..." folgt
-- 2. Für den cv-uploads Bucket sollte die Aufbewahrungsdauer begrenzt werden (z.B. 24 Stunden)
-- 3. Erwäge die Implementierung von Datei-Größenbeschränkungen und Typ-Validierung
-- 4. Überlege, ob der cv-backups Bucket wirklich öffentlich sein muss oder ob er privat sein kann

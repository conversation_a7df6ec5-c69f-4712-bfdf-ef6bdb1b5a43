-- Migration zur Verbesserung der Row Level Security (RLS) Richtlinien
-- Diese Migration standardisiert und verbessert die RLS-Richtlinien für alle Tabellen

-- Hilfsfunktion zum Überprüfen, ob eine Tabelle existiert
CREATE OR REPLACE FUNCTION table_exists(table_name text)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = table_name
  );
END;
$$ LANGUAGE plpgsql;

-- Hilfsfunktion zum Überprüfen, ob eine Policy existiert
CREATE OR REPLACE FUNCTION policy_exists(policy_name text, table_name text)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM pg_policies 
    WHERE policyname = policy_name
    AND tablename = table_name
  );
END;
$$ LANGUAGE plpgsql;

-- 1. Benutzerprofile und persönliche Daten

-- users/profiles Tabelle
DO $$
BEGIN
  IF table_exists('users') THEN
    -- Aktiviere RLS
    ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Benutzer können nur ihr eigenes Profil lesen" ON public.users;
    DROP POLICY IF EXISTS "Benutzer können nur ihr eigenes Profil ändern" ON public.users;
    DROP POLICY IF EXISTS "Benutzer können ihr eigenes Profil löschen" ON public.users;
    DROP POLICY IF EXISTS "Service kann alle Profile verwalten" ON public.users;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Benutzer können nur ihr eigenes Profil lesen"
      ON public.users FOR SELECT
      USING (auth.uid() = id);
    
    CREATE POLICY "Benutzer können nur ihr eigenes Profil ändern"
      ON public.users FOR UPDATE
      USING (auth.uid() = id);
    
    CREATE POLICY "Benutzer können ihr eigenes Profil löschen"
      ON public.users FOR DELETE
      USING (auth.uid() = id);
    
    CREATE POLICY "Service kann alle Profile verwalten"
      ON public.users
      USING (auth.role() = 'service_role');
  END IF;
  
  -- Prüfe auch auf profiles Tabelle (alternative Benennung)
  IF table_exists('profiles') THEN
    -- Aktiviere RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can read their own profile" ON public.profiles;
    DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
    DROP POLICY IF EXISTS "Users can delete their own profile" ON public.profiles;
    DROP POLICY IF EXISTS "Service can manage all profiles" ON public.profiles;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can read their own profile"
      ON public.profiles FOR SELECT
      USING (auth.uid() = id);
    
    CREATE POLICY "Users can update their own profile"
      ON public.profiles FOR UPDATE
      USING (auth.uid() = id);
    
    CREATE POLICY "Users can delete their own profile"
      ON public.profiles FOR DELETE
      USING (auth.uid() = id);
    
    CREATE POLICY "Service can manage all profiles"
      ON public.profiles
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- 2. Abonnements und Zahlungen

-- subscriptions Tabelle
DO $$
BEGIN
  IF table_exists('subscriptions') THEN
    -- Aktiviere RLS
    ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;
    DROP POLICY IF EXISTS "Benutzer können nur ihre eigenen Abonnements sehen" ON public.subscriptions;
    DROP POLICY IF EXISTS "Benutzer können keine Abonnements direkt ändern" ON public.subscriptions;
    DROP POLICY IF EXISTS "Service role can manage all subscriptions" ON public.subscriptions;
    DROP POLICY IF EXISTS "Service can manage all subscriptions" ON public.subscriptions;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can view their own subscriptions"
      ON public.subscriptions FOR SELECT
      USING (auth.uid() = user_id);
    
    -- Benutzer können keine Abonnements direkt erstellen/ändern/löschen (nur über Service-Funktionen)
    CREATE POLICY "Users cannot modify subscriptions directly"
      ON public.subscriptions FOR INSERT
      WITH CHECK (false);
    
    CREATE POLICY "Users cannot update subscriptions directly"
      ON public.subscriptions FOR UPDATE
      USING (false);
    
    CREATE POLICY "Users cannot delete subscriptions"
      ON public.subscriptions FOR DELETE
      USING (false);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all subscriptions"
      ON public.subscriptions
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- purchases Tabelle
DO $$
BEGIN
  IF table_exists('purchases') THEN
    -- Aktiviere RLS
    ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can view their own purchases" ON public.purchases;
    DROP POLICY IF EXISTS "Benutzer können nur ihre eigenen Käufe sehen" ON public.purchases;
    DROP POLICY IF EXISTS "Benutzer können keine Käufe direkt ändern" ON public.purchases;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can view their own purchases"
      ON public.purchases FOR SELECT
      USING (auth.uid() = user_id);
    
    -- Benutzer können keine Käufe direkt erstellen/ändern/löschen (nur über Service-Funktionen)
    CREATE POLICY "Users cannot modify purchases directly"
      ON public.purchases FOR INSERT
      WITH CHECK (false);
    
    CREATE POLICY "Users cannot update purchases"
      ON public.purchases FOR UPDATE
      USING (false);
    
    CREATE POLICY "Users cannot delete purchases"
      ON public.purchases FOR DELETE
      USING (false);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all purchases"
      ON public.purchases
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- ad_access Tabelle
DO $$
BEGIN
  IF table_exists('ad_access') THEN
    -- Aktiviere RLS
    ALTER TABLE public.ad_access ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Benutzer können nur ihre eigenen werbebasierten Zugänge sehen" ON public.ad_access;
    DROP POLICY IF EXISTS "Benutzer können keine werbebasierten Zugänge direkt ändern" ON public.ad_access;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can view their own ad access"
      ON public.ad_access FOR SELECT
      USING (auth.uid() = user_id);
    
    -- Benutzer können keine Ad-Access-Einträge direkt erstellen/ändern/löschen (nur über Service-Funktionen)
    CREATE POLICY "Users cannot modify ad access directly"
      ON public.ad_access FOR INSERT
      WITH CHECK (false);
    
    CREATE POLICY "Users cannot update ad access"
      ON public.ad_access FOR UPDATE
      USING (false);
    
    CREATE POLICY "Users cannot delete ad access"
      ON public.ad_access FOR DELETE
      USING (false);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all ad access"
      ON public.ad_access
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- application_counters Tabelle
DO $$
BEGIN
  IF table_exists('application_counters') THEN
    -- Aktiviere RLS
    ALTER TABLE public.application_counters ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can view their own application counters" ON public.application_counters;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can view their own application counters"
      ON public.application_counters FOR SELECT
      USING (auth.uid() = user_id);
    
    -- Benutzer können keine Zähler direkt erstellen/ändern/löschen (nur über Service-Funktionen)
    CREATE POLICY "Users cannot modify application counters directly"
      ON public.application_counters FOR INSERT
      WITH CHECK (false);
    
    CREATE POLICY "Users cannot update application counters"
      ON public.application_counters FOR UPDATE
      USING (false);
    
    CREATE POLICY "Users cannot delete application counters"
      ON public.application_counters FOR DELETE
      USING (false);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all application counters"
      ON public.application_counters
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- 3. Anwendungsdaten

-- applied_jobs Tabelle
DO $$
BEGIN
  IF table_exists('applied_jobs') THEN
    -- Aktiviere RLS
    ALTER TABLE public.applied_jobs ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can view their own applied jobs" ON public.applied_jobs;
    DROP POLICY IF EXISTS "Users can insert their own applied jobs" ON public.applied_jobs;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can view their own applied jobs"
      ON public.applied_jobs FOR SELECT
      USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can insert their own applied jobs"
      ON public.applied_jobs FOR INSERT
      WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "Users can update their own applied jobs"
      ON public.applied_jobs FOR UPDATE
      USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can delete their own applied jobs"
      ON public.applied_jobs FOR DELETE
      USING (auth.uid() = user_id);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all applied jobs"
      ON public.applied_jobs
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- user_favorites Tabelle
DO $$
BEGIN
  IF table_exists('user_favorites') THEN
    -- Aktiviere RLS
    ALTER TABLE public.user_favorites ENABLE ROW LEVEL SECURITY;
    
    -- Lösche bestehende Richtlinien, falls vorhanden
    DROP POLICY IF EXISTS "Users can read their own favorites" ON public.user_favorites;
    DROP POLICY IF EXISTS "Users can insert their own favorites" ON public.user_favorites;
    DROP POLICY IF EXISTS "Users can update their own favorites" ON public.user_favorites;
    DROP POLICY IF EXISTS "Users can delete their own favorites" ON public.user_favorites;
    
    -- Erstelle neue standardisierte Richtlinien
    CREATE POLICY "Users can read their own favorites"
      ON public.user_favorites FOR SELECT
      USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can insert their own favorites"
      ON public.user_favorites FOR INSERT
      WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "Users can update their own favorites"
      ON public.user_favorites FOR UPDATE
      USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can delete their own favorites"
      ON public.user_favorites FOR DELETE
      USING (auth.uid() = user_id);
    
    -- Service-Rolle hat vollen Zugriff
    CREATE POLICY "Service can manage all favorites"
      ON public.user_favorites
      USING (auth.role() = 'service_role');
  END IF;
END $$;

-- Entferne die Hilfsfunktionen
DROP FUNCTION IF EXISTS table_exists(text);
DROP FUNCTION IF EXISTS policy_exists(text, text);

-- Funktion zum Erstellen des Subscriptions-Schemas
CREATE OR REPLACE FUNCTION create_subscriptions_schema()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER -- Mit Berechtigungen des Erstellers ausführen
SET search_path = public
AS $$
DECLARE
  result jsonb;
  schema_exists boolean;
  table_exists boolean;
BEGIN
  -- <PERSON><PERSON><PERSON><PERSON>, ob die Tabelle bereits existiert
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'subscriptions'
  ) INTO table_exists;

  -- Wenn <PERSON>belle nicht existiert, Schema erstellen
  IF NOT table_exists THEN
    -- Subscriptions Tabelle erstellen
    CREATE TABLE IF NOT EXISTS public.subscriptions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'expired', 'paused', 'pending')),
      subscription_id TEXT NOT NULL, -- ID von Google/Apple
      product_id TEXT NOT NULL,      -- Produkt-ID
      platform TEXT NOT NULL CHECK (platform IN ('android', 'ios', 'web')),
      purchased_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      expires_at TIMESTAMPTZ,        -- Ablaufdatum des Abonnements
      renewed_at TIMESTAMPTZ,        -- Wann das Abonnement verlängert wurde
      canceled_at TIMESTAMPTZ,       -- Wann das Abonnement gekündigt wurde
      receipt_data TEXT,             -- Quittungsdaten für Überprüfung
      metadata JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      
      UNIQUE(user_id, subscription_id)
    );
    
    -- Indizes für bessere Abfrageleistung
    CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
    CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);
    
    -- UPDATE-Trigger für updated_at 
    CREATE OR REPLACE FUNCTION update_subscriptions_updated_at()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    CREATE TRIGGER set_subscriptions_updated_at
    BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_subscriptions_updated_at();
    
    -- Berechtigungsregeln (RLS)
    ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
    
    -- Benutzer können nur ihre eigenen Abonnements sehen
    CREATE POLICY "Users can view their own subscriptions"
      ON public.subscriptions
      FOR SELECT
      USING (auth.uid() = user_id);
    
    -- Benutzer können keine Abonnements erstellen (nur Backend)
    CREATE POLICY "Users cannot create subscriptions directly"
      ON public.subscriptions
      FOR INSERT
      WITH CHECK (false);
    
    -- Benutzer können ihre Abonnements nicht aktualisieren (nur Backend)
    CREATE POLICY "Users cannot update subscriptions"
      ON public.subscriptions
      FOR UPDATE
      USING (false);
    
    -- Benutzer können ihre Abonnements nicht löschen
    CREATE POLICY "Users cannot delete subscriptions"
      ON public.subscriptions
      FOR DELETE
      USING (false);
    
    -- Service-Rolle kann alle Operationen durchführen
    CREATE POLICY "Service role can do all operations"
      ON public.subscriptions
      FOR ALL
      USING (auth.role() = 'service_role')
      WITH CHECK (auth.role() = 'service_role');
    
    result := jsonb_build_object(
      'success', true,
      'message', 'Subscriptions-Tabelle wurde erstellt',
      'table_created', true
    );
  ELSE
    result := jsonb_build_object(
      'success', true,
      'message', 'Subscriptions-Tabelle existiert bereits',
      'table_created', false
    );
  END IF;
  
  RETURN result;
END;
$$; 
 
 
-- Migration zur Aktualisierung der Abonnementpläne und Hinzufügen von Bewerbungszählern

-- <PERSON>üge Spalte für den Abonnementplan hinzu
ALTER TABLE IF EXISTS public.subscriptions
ADD COLUMN IF NOT EXISTS plan_type TEXT CHECK (plan_type IN ('basic', 'pro', 'unlimited')) DEFAULT 'basic';

-- Füge Spalte für die Anzahl der verbleibenden Bewerbungen hinzu
ALTER TABLE IF EXISTS public.subscriptions
ADD COLUMN IF NOT EXISTS remaining_applications INTEGER;

-- Füge Spalte für die Anzahl der insgesamt verfügbaren Bewerbungen hinzu
ALTER TABLE IF EXISTS public.subscriptions
ADD COLUMN IF NOT EXISTS total_applications INTEGER;

-- Füge Spalte für das Datum der letzten Aktualisierung der Bewerbungszähler hinzu
ALTER TABLE IF EXISTS public.subscriptions
ADD COLUMN IF NOT EXISTS applications_reset_date TIMESTAMPTZ;

-- <PERSON><PERSON><PERSON> eine Tabelle für die Bewerbungszähler
CREATE TABLE IF NOT EXISTS public.application_counters (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  subscription_id UUID REFERENCES public.subscriptions,
  month_year TEXT NOT NULL, -- Format: 'YYYY-MM'
  used_applications INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, month_year)
);

-- Erstelle eine Funktion zum Aktualisieren der verbleibenden Bewerbungen
CREATE OR REPLACE FUNCTION public.update_remaining_applications()
RETURNS TRIGGER AS $$
DECLARE
  v_subscription RECORD;
  v_total_applications INTEGER;
BEGIN
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = NEW.user_id
    AND status = 'active'
    AND (end_date IS NULL OR end_date > NOW())
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein aktives Abonnement gefunden wurde, beende die Funktion
  IF v_subscription.id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Bestimme die Gesamtzahl der Bewerbungen basierend auf dem Plan
  CASE v_subscription.plan_type
    WHEN 'basic' THEN v_total_applications := 30;
    WHEN 'pro' THEN v_total_applications := 150;
    WHEN 'unlimited' THEN v_total_applications := NULL; -- Unbegrenzt
    ELSE v_total_applications := 30; -- Standard: Basic
  END CASE;
  
  -- Aktualisiere die verbleibenden Bewerbungen
  UPDATE public.subscriptions
  SET remaining_applications = CASE 
                                WHEN v_total_applications IS NULL THEN NULL -- Unbegrenzt
                                ELSE v_total_applications - NEW.used_applications
                              END,
      total_applications = v_total_applications,
      updated_at = NOW()
  WHERE id = v_subscription.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Erstelle einen Trigger für die Aktualisierung der verbleibenden Bewerbungen
CREATE TRIGGER on_application_counter_change
AFTER INSERT OR UPDATE ON public.application_counters
FOR EACH ROW
EXECUTE FUNCTION public.update_remaining_applications();

-- Erstelle eine Funktion zum Zurücksetzen der Bewerbungszähler am Monatsende
CREATE OR REPLACE FUNCTION public.reset_application_counters()
RETURNS void AS $$
DECLARE
  v_subscription RECORD;
  v_new_month_year TEXT;
BEGIN
  -- Aktuelles Monat/Jahr im Format 'YYYY-MM'
  v_new_month_year := to_char(NOW(), 'YYYY-MM');
  
  -- Durchlaufe alle aktiven Abonnements
  FOR v_subscription IN 
    SELECT * 
    FROM public.subscriptions 
    WHERE status = 'active' 
      AND (end_date IS NULL OR end_date > NOW())
      AND (applications_reset_date IS NULL OR applications_reset_date < date_trunc('month', NOW()))
  LOOP
    -- Bestimme die Gesamtzahl der Bewerbungen basierend auf dem Plan
    CASE v_subscription.plan_type
      WHEN 'basic' THEN 
        UPDATE public.subscriptions
        SET remaining_applications = 30,
            total_applications = 30,
            applications_reset_date = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription.id;
      WHEN 'pro' THEN 
        UPDATE public.subscriptions
        SET remaining_applications = 150,
            total_applications = 150,
            applications_reset_date = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription.id;
      WHEN 'unlimited' THEN 
        UPDATE public.subscriptions
        SET remaining_applications = NULL,
            total_applications = NULL,
            applications_reset_date = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription.id;
      ELSE 
        UPDATE public.subscriptions
        SET remaining_applications = 30,
            total_applications = 30,
            applications_reset_date = NOW(),
            updated_at = NOW()
        WHERE id = v_subscription.id;
    END CASE;
    
    -- Erstelle einen neuen Eintrag in der application_counters Tabelle für den neuen Monat
    INSERT INTO public.application_counters (
      user_id,
      subscription_id,
      month_year,
      used_applications
    ) VALUES (
      v_subscription.user_id,
      v_subscription.id,
      v_new_month_year,
      0
    ) ON CONFLICT (user_id, month_year) DO NOTHING;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Erstelle eine Funktion zum Inkrementieren des Bewerbungszählers
CREATE OR REPLACE FUNCTION public.increment_application_counter(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_subscription RECORD;
  v_counter RECORD;
  v_month_year TEXT;
  v_has_access BOOLEAN;
BEGIN
  -- Aktuelles Monat/Jahr im Format 'YYYY-MM'
  v_month_year := to_char(NOW(), 'YYYY-MM');
  
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
    AND (end_date IS NULL OR end_date > NOW())
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein aktives Abonnement gefunden wurde, hat der Benutzer keinen Zugriff
  IF v_subscription.id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Prüfe, ob der Benutzer noch Bewerbungen übrig hat
  IF v_subscription.plan_type = 'unlimited' THEN
    v_has_access := TRUE; -- Unbegrenzter Plan hat immer Zugriff
  ELSIF v_subscription.remaining_applications IS NULL OR v_subscription.remaining_applications <= 0 THEN
    v_has_access := FALSE; -- Keine Bewerbungen mehr übrig
  ELSE
    v_has_access := TRUE; -- Noch Bewerbungen übrig
  END IF;
  
  -- Wenn der Benutzer Zugriff hat, inkrementiere den Zähler
  IF v_has_access THEN
    -- Hole den aktuellen Zähler oder erstelle einen neuen
    SELECT * INTO v_counter
    FROM public.application_counters
    WHERE user_id = p_user_id
      AND month_year = v_month_year;
    
    IF v_counter.id IS NULL THEN
      -- Erstelle einen neuen Zähler
      INSERT INTO public.application_counters (
        user_id,
        subscription_id,
        month_year,
        used_applications
      ) VALUES (
        p_user_id,
        v_subscription.id,
        v_month_year,
        1
      );
    ELSE
      -- Inkrementiere den bestehenden Zähler
      UPDATE public.application_counters
      SET used_applications = used_applications + 1,
          updated_at = NOW()
      WHERE id = v_counter.id;
    END IF;
  END IF;
  
  RETURN v_has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Erstelle eine Funktion zum Abrufen der verbleibenden Bewerbungen
CREATE OR REPLACE FUNCTION public.get_remaining_applications(p_user_id UUID)
RETURNS TABLE (
  remaining INTEGER,
  total INTEGER,
  unlimited BOOLEAN
) AS $$
DECLARE
  v_subscription RECORD;
BEGIN
  -- Hole das aktive Abonnement des Benutzers
  SELECT * INTO v_subscription
  FROM public.subscriptions
  WHERE user_id = p_user_id
    AND status = 'active'
    AND (end_date IS NULL OR end_date > NOW())
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Wenn kein aktives Abonnement gefunden wurde, gib 0 zurück
  IF v_subscription.id IS NULL THEN
    RETURN QUERY SELECT 0::INTEGER, 0::INTEGER, FALSE::BOOLEAN;
    RETURN;
  END IF;
  
  -- Wenn der Plan unbegrenzt ist, gib NULL zurück
  IF v_subscription.plan_type = 'unlimited' THEN
    RETURN QUERY SELECT NULL::INTEGER, NULL::INTEGER, TRUE::BOOLEAN;
  ELSE
    RETURN QUERY SELECT 
      COALESCE(v_subscription.remaining_applications, 0)::INTEGER,
      COALESCE(v_subscription.total_applications, 0)::INTEGER,
      FALSE::BOOLEAN;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

// supabase/functions/translate-text/index.ts

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts' // CORS Helper

console.log('Function translate-text starting...');

// Funktion zur Interaktion mit der Übersetzungs-API (DeepL)
async function translateText(text: string, targetLang: string, sourceLang?: string): Promise<object | { error: string }> {
  console.log('translateText aufgerufen.');
  const deeplApiKey = Deno.env.get('DEEPL_API_KEY');
  
  if (!deeplApiKey) {
    console.error('DEEPL_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: DeepL API Key fehlt.' };
  }

  try {
    // Sende Anfrage an DeepL API
    console.log(`Sende Übersetzungsanfrage an DeepL API für Text (${text.length} Zeichen) von ${sourceLang || 'auto'} nach ${targetLang}...`);
    
    const formData = new FormData();
    formData.append('text', text);
    formData.append('target_lang', targetLang.toUpperCase());
    
    if (sourceLang) {
      formData.append('source_lang', sourceLang.toUpperCase());
    }
    
    const response = await fetch('https://api-free.deepl.com/v2/translate', {
      method: 'POST',
      headers: {
        'Authorization': `DeepL-Auth-Key ${deeplApiKey}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`DeepL API Fehler (${response.status}): ${errorText}`);
      return { 
        error: `Fehler bei der Übersetzung (${response.status}): ${errorText}` 
      };
    }

    const data = await response.json();
    console.log('DeepL API Antwort erhalten:', JSON.stringify(data).substring(0, 200) + '...');
    
    // Extrahiere den übersetzten Text aus der Antwort
    if (data.translations && data.translations.length > 0) {
      return {
        translatedText: data.translations[0].text,
        detectedSourceLanguage: data.translations[0].detected_source_language || sourceLang,
      };
    } else {
      return { error: 'Keine Übersetzung in der API-Antwort gefunden.' };
    }
  } catch (error) {
    console.error('Fehler bei der Anfrage an die DeepL API:', error);
    return { error: `Fehler bei der Kommunikation mit der Übersetzungs-API: ${error.message}` };
  }
}

serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Nur POST-Anfragen erlauben
    if (req.method !== 'POST') {
      console.log(`[Serve] Methode ${req.method} nicht erlaubt.`);
      return new Response(
        JSON.stringify({ error: 'Nur POST-Anfragen sind erlaubt.' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Request-Body parsen
    const requestData = await req.json();
    console.log('[Serve] Request-Daten empfangen:', JSON.stringify(requestData).substring(0, 200) + '...');

    // Validiere die Eingabedaten
    if (!requestData.text) {
      console.log('[Serve] Fehlende Eingabedaten: text');
      return new Response(
        JSON.stringify({ error: 'Der zu übersetzende Text muss angegeben werden.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Standardwerte für Ziel- und Quellsprache
    const targetLang = requestData.targetLang || 'de';
    const sourceLang = requestData.sourceLang || undefined; // Optional

    // Text übersetzen
    const result = await translateText(requestData.text, targetLang, sourceLang);

    // Prüfe auf Fehler
    if ('error' in result) {
      console.error('[Serve] Fehler bei der Übersetzung:', result.error);
      return new Response(
        JSON.stringify({ error: result.error }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Erfolgreiche Antwort
    console.log('[Serve] Text erfolgreich übersetzt.');
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('[Serve] Unerwarteter Fehler:', error);
    return new Response(
      JSON.stringify({ error: `Unerwarteter Fehler: ${error.message}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

/*
// Example Usage from Flutter (using Supabase client):
try {
  final response = await supabase.functions.invoke(
    'translate-text',
    body: {
      'text': 'Hello world',
      'targetLang': 'de',
      'sourceLang': 'en' // Optional
    },
  );

  if (response.error != null) {
    print('Supabase function error: ${response.error!.message}');
    // Handle error
  } else {
    print('Supabase function success: ${response.data}');
    // Process the translated text (response.data['translatedText'])
    final translatedText = response.data['translatedText'] as String;
    // Update UI...
  }
} catch (e) {
  print('Error invoking Supabase function: $e');
  // Handle error
}
*/

# Premium-Aktivierungs-Funktion

Diese Supabase Edge Function ermöglicht die manuelle Aktivierung von Premium-Accounts für bestimmte Benutzer.

## Deployment

Um die Funktion zu deployen, führe folgende Befehle aus:

```bash
# Navigiere zum Supabase-Funktionsverzeichnis
cd supabase/functions

# Deploye die Funktion
supabase functions deploy activate-premium --project-ref vpttdxibvjrfjzbtktqg
```

## Umgebungsvariablen

Setze die folgenden Umgebungsvariablen für die Funktion:

```bash
# Setze einen geheimen Admin-Schlüssel für die Aktivierung
supabase secrets set ADMIN_ACTIVATION_KEY=dein_geheimer_schlüssel --project-ref vpttdxibvjrfjzbtktqg
```

## Verwendung

Du kannst die Funktion mit folgendem curl-Befehl aufrufen:

```bash
curl -X POST 'https://vpttdxibvjrfjzbtktqg.functions.supabase.co/activate-premium' \
-H "Content-Type: application/json" \
-d '{
  "user_id": "BENUTZER_ID",
  "duration_months": 12,
  "admin_key": "dein_geheimer_schlüssel"
}'
```

Oder mit JavaScript:

```javascript
const activatePremium = async (userId, durationMonths = 12) => {
  const response = await fetch(
    'https://vpttdxibvjrfjzbtktqg.functions.supabase.co/activate-premium',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        duration_months: durationMonths,
        admin_key: 'dein_geheimer_schlüssel'
      }),
    }
  );
  
  return await response.json();
};
```

## Sicherheitshinweise

- Halte den `ADMIN_ACTIVATION_KEY` geheim und teile ihn nur mit autorisierten Personen
- Überlege, ob du zusätzliche Authentifizierungsmechanismen implementieren möchtest
- Protokolliere alle Premium-Aktivierungen für Audit-Zwecke

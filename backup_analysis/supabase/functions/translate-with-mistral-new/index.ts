// supabase/functions/translate-with-llama/index.ts
// Übersetzungsfunktion mit Mistral 7B über Groq API

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts' // CORS Helper

console.log('Function translate-with-llama starting...');

// Hilfsfunktion zur Spracherkennung (einfache Implementierung)
function detectLanguage(text: string): string {
  // Einfache Spracherkennung basierend auf häufigen Wörtern und Zeichen
  // Dies ist eine vereinfachte Version und nicht so genau wie spezialisierte Dienste

  // Normalisiere den Text für die Analyse
  const normalizedText = text.toLowerCase().substring(0, 1000); // Begrenzen auf 1000 Zeichen

  // Häufige Wörter in verschiedenen Sprachen
  const languagePatterns: Record<string, RegExp[]> = {
    'de': [/\b(der|die|das|und|ist|in|zu|den|für|auf|mit|dem|sich|des|ein|eine|einen|einer|eines|nicht|von|werden|hat|sind|dass|du|sie|ich|er|es|ein|als|auch|aber|nach|bei|um|wenn|nur|oder|aus|wie|noch|vor|zum|schon|so|zur|kann|doch|muss|wird|mehr|durch|man|unter|über|immer|wieder|sehr|am|bis|gegen|ohne|mich|dir|uns|euch|mir|ihm|ihr|ihnen|seine|meine|deine|unsere|eure|ihre)\b/g],
    'en': [/\b(the|and|to|of|a|in|is|that|for|it|as|with|was|on|are|by|this|be|or|have|from|an|they|which|you|one|were|all|we|their|has|would|will|there|if|can|no|when|who|what|more|about|out|up|them|than|some|could|into|only|other|time|new|just|any|each|should|these|two|may|such|then|do|first|over|way|also|like|our|how|well|where|very|through|before|between|after|since|those|under|same|both|while|against|during|without|within|among|upon)\b/g],
    'fr': [/\b(le|la|les|un|une|des|et|est|en|à|de|pour|dans|que|qui|ce|il|elle|ils|elles|nous|vous|je|tu|on|son|sa|ses|mon|ma|mes|ton|ta|tes|notre|nos|votre|vos|leur|leurs|du|au|aux|avec|par|sur|sous|entre|vers|chez|sans|ni|ou|où|quand|comment|pourquoi|car|mais|donc|or|si|comme|plus|moins|très|trop|peu|beaucoup|tout|toute|tous|toutes|autre|autres|même|aussi|ainsi|alors|après|avant|pendant|depuis|jusque|jusqu'à|déjà|encore|toujours|jamais|souvent|parfois|rien|personne|quelque|quelques|plusieurs|chaque|certain|certains|certaines|tel|tels|telle|telles|cet|cette|ces)\b/g],
    'es': [/\b(el|la|los|las|un|una|unos|unas|y|es|en|a|de|para|por|con|que|se|lo|le|su|sus|mi|mis|tu|tus|su|sus|nuestro|nuestros|nuestra|nuestras|vuestro|vuestros|vuestra|vuestras|este|esta|estos|estas|ese|esa|esos|esas|aquel|aquella|aquellos|aquellas|del|al|no|si|sí|más|menos|mucho|muchos|mucha|muchas|poco|pocos|poca|pocas|alguno|algunos|alguna|algunas|ninguno|ninguna|otro|otros|otra|otras|mismo|mismos|misma|mismas|tan|tanto|tanta|tantos|tantas|como|cuando|donde|quien|quienes|cuyo|cuya|cuyos|cuyas|cual|cuales|qué|quién|quiénes|cuál|cuáles|cómo|dónde|cuándo|cuánto|cuánta|cuántos|cuántas|porque|aunque|pero|sino|ya|bien|mal|así|también|siempre|nunca|jamás|ahora|después|antes|durante|luego|desde|hasta|entre|sobre|bajo|contra|según|sin|tras|mediante|excepto|salvo|menos|además|incluso|también|tampoco|sino|aunque|pero|mas|empero|sino|ya|aún|todavía|mientras|luego|después|antes|apenas|pronto|tarde|temprano|siempre|nunca|jamás|acaso|quizá|quizás|tal vez|a lo mejor|probablemente|posiblemente|seguramente|ciertamente|verdaderamente|efectivamente|realmente|así|asimismo|también|igualmente|del mismo modo|de igual manera|de igual modo|de igual forma|de la misma forma|de la misma manera|así como|tanto como|tan como|igual que|lo mismo que|tal como|tal cual)\b/g],
    'it': [/\b(il|lo|la|i|gli|le|un|uno|una|dei|degli|delle|e|è|sono|in|a|di|per|con|su|che|chi|cui|si|mi|ti|ci|vi|li|lo|la|le|gli|ne|questo|questa|questi|queste|quello|quella|quelli|quelle|mio|mia|miei|mie|tuo|tua|tuoi|tue|suo|sua|suoi|sue|nostro|nostra|nostri|nostre|vostro|vostra|vostri|vostre|loro|non|sì|più|meno|molto|molti|molta|molte|poco|pochi|poca|poche|alcuno|alcuni|alcuna|alcune|nessuno|nessuna|altro|altri|altra|altre|stesso|stessi|stessa|stesse|tutto|tutti|tutta|tutte|tanto|tanti|tanta|tante|troppo|troppi|troppa|troppe|quando|dove|come|perché|poiché|giacché|sebbene|benché|affinché|cosicché|allorché|qualora|purché|finché|siccome|mentre|dopo|prima|durante|fino|da|verso|contro|fra|tra|sotto|sopra|davanti|dietro|dentro|fuori|vicino|lontano|oltre|senza|con|contro|mediante|tramite|secondo|oltre|eccetto|tranne|salvo|invece|malgrado|nonostante|pure|anche|ancora|sempre|mai|già|ormai|appena|subito|presto|tardi|spesso|raramente|talvolta|forse|magari|probabilmente|certamente|sicuramente|davvero|veramente|proprio|certo|giusto|esatto|così|quindi|dunque|perciò|pertanto|infatti|cioè|ossia|ovvero|tuttavia|però|ma|eppure|anzi|piuttosto|altrimenti|invece|comunque|nondimeno|pur|sebbene|malgrado|benché|quantunque|finché|affinché|perché|poiché|siccome|giacché|dato che|visto che|considerato che|dal momento che|per il fatto che|in quanto|in modo che|in maniera che|a condizione che|a patto che|purché|qualora|nel caso in cui|nell'eventualità che|nell'ipotesi che|posto che|ammesso che|concesso che|sia che|vuoi che|che|quale|cui|chi|dove|quando|come|perché|quanto|quanti|quante)\b/g],
    'pt': [/\b(o|a|os|as|um|uma|uns|umas|e|é|são|em|de|para|por|com|que|se|me|te|nos|vos|lhe|lhes|o|a|os|as|meu|minha|meus|minhas|teu|tua|teus|tuas|seu|sua|seus|suas|nosso|nossa|nossos|nossas|vosso|vossa|vossos|vossas|este|esta|estes|estas|esse|essa|esses|essas|aquele|aquela|aqueles|aquelas|isto|isso|aquilo|do|da|dos|das|no|na|nos|nas|ao|à|aos|às|pelo|pela|pelos|pelas|não|sim|mais|menos|muito|muitos|muita|muitas|pouco|poucos|pouca|poucas|algum|alguns|alguma|algumas|nenhum|nenhuma|outro|outros|outra|outras|mesmo|mesmos|mesma|mesmas|tão|tanto|tanta|tantos|tantas|quanto|quantos|quantas|como|quando|onde|quem|cujo|cuja|cujos|cujas|qual|quais|que|quê|quem|como|onde|quando|quanto|quanta|quantos|quantas|porque|embora|mas|porém|todavia|contudo|entretanto|senão|já|bem|mal|assim|também|sempre|nunca|jamais|agora|depois|antes|durante|logo|desde|até|entre|sobre|sob|contra|conforme|sem|após|mediante|exceto|salvo|menos|ademais|inclusive|também|tampouco|senão|ainda|enquanto|logo|depois|antes|apenas|cedo|tarde|sempre|nunca|jamais|acaso|talvez|quiçá|porventura|possivelmente|provavelmente|certamente|realmente|verdadeiramente|efetivamente|assim|igualmente|do mesmo modo|da mesma maneira|da mesma forma|assim como|tanto quanto|tão como|igual a|o mesmo que|tal como|tal qual)\b/g],
    'nl': [/\b(de|het|een|van|in|is|en|op|dat|die|te|zijn|voor|niet|aan|met|als|er|door|ook|naar|of|uit|bij|nog|om|over|tot|dan|al|maar|toch|nu|dus|zo|wel|geen|zich|reeds|wat|alle|onder|ja|mij|daar|wie|waar|waarom|wanneer|hoe|hier|werd|zonder|kan|hun|mijn|werd|hen|deze|dit|die|dat|onze|uw|ons|jullie|zij|wij|jij|hij|zij|het|haar|hem|u|jou|jouw|mijn|zijn|haar|ons|hun|diens|wiens|wier)\b/g],
    'ru': [/[а-яА-Я]/g], // Kyrillische Zeichen für Russisch
    'zh': [/[\u4e00-\u9fa5]/g], // Chinesische Schriftzeichen
    'ja': [/[\u3040-\u309F\u30A0-\u30FF]/g], // Japanische Hiragana und Katakana
    'ko': [/[\uAC00-\uD7AF]/g], // Koreanische Hangul
    'ar': [/[\u0600-\u06FF]/g], // Arabische Schriftzeichen
  };

  // Zähle die Übereinstimmungen für jede Sprache
  const scores: Record<string, number> = {};

  for (const [lang, patterns] of Object.entries(languagePatterns)) {
    let score = 0;
    for (const pattern of patterns) {
      const matches = normalizedText.match(pattern);
      if (matches) {
        score += matches.length;
      }
    }
    scores[lang] = score;
  }

  // Finde die Sprache mit der höchsten Punktzahl
  let detectedLang = 'en'; // Standardsprache
  let maxScore = 0;

  for (const [lang, score] of Object.entries(scores)) {
    if (score > maxScore) {
      maxScore = score;
      detectedLang = lang;
    }
  }

  console.log(`Spracherkennung: ${detectedLang} (Score: ${maxScore})`);
  return detectedLang;
}

// Hilfsfunktion zum Abrufen des Sprachnamens
function getLanguageName(langCode: string): string {
  const languageNames: Record<string, string> = {
    'auto': 'automatisch erkannte Sprache',
    'de': 'Deutsch',
    'en': 'Englisch',
    'fr': 'Französisch',
    'es': 'Spanisch',
    'it': 'Italienisch',
    'pt': 'Portugiesisch',
    'nl': 'Niederländisch',
    'ru': 'Russisch',
    'zh': 'Chinesisch',
    'ja': 'Japanisch',
    'ko': 'Koreanisch',
    'ar': 'Arabisch',
  };

  return languageNames[langCode] || langCode;
}

// Funktion zur Interaktion mit der Groq API für Übersetzungen mit Mistral 7B
async function translateWithLlama(
  text: string,
  targetLang: string,
  sourceLang?: string,
  useStream: boolean = false
): Promise<object | { error: string } | ReadableStream<Uint8Array>> {
  console.log('translateWithLlama aufgerufen. Stream-Modus:', useStream);
  const groqApiKey = Deno.env.get('GROQ_API_KEY');

  if (!groqApiKey) {
    console.error('GROQ_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: Groq API Key fehlt.' };
  }

  try {
    // Erstelle einen Prompt für die Übersetzung
    // Erstelle einen optimierten System-Prompt für Mistral 7B
    const systemPrompt = `Du bist ein professioneller Übersetzer mit höchster Präzision. Übersetze Texte exakt, ohne Informationen zu verlieren oder hinzuzufügen.

KRITISCHE ANWEISUNGEN:
- Übersetze JEDEN Teil des Textes vollständig und korrekt
- Behalte die Struktur und Formatierung des Originaltextes bei
- Wiederhole NIEMALS denselben Text oder Phrasen mehrfach
- Füge KEINE eigenen Erklärungen oder Kommentare hinzu
- Wenn du HTML oder Formatierungen siehst, behalte sie bei
- Übersetze jeden Abschnitt nur EINMAL
- Setze immer Leerzeichen zwischen Wörtern und Sätzen
- Füge keine Wiederholungen ein
- Prüfe deine Übersetzung auf Duplikate, bevor du sie zurückgibst
- Bei arabischen Texten verwende modernes Standardarabisch (MSA)
- Stelle sicher, dass jedes Wort nur einmal erscheint`;

    // Bestimme die Quell- und Zielsprache für den Prompt
    const sourceLanguageName = getLanguageName(sourceLang || 'auto');
    const targetLanguageName = getLanguageName(targetLang);

    // Analysiere den Text, um Wiederholungen zu erkennen
    const paragraphs = text.split('\n');
    const uniqueParagraphs = [];
    const seenParagraphs = new Set();

    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim();
      if (trimmedParagraph && !seenParagraphs.has(trimmedParagraph)) {
        seenParagraphs.add(trimmedParagraph);
        uniqueParagraphs.push(paragraph);
      }
    }

    // Verwende nur eindeutige Absätze für die Übersetzung
    const cleanedText = uniqueParagraphs.join('\n');

    // Vereinfachte sprachspezifische Anweisungen für Mistral 7B
    let languageSpecificInstructions = '';

    // Füge sprachspezifische Anweisungen hinzu
    if (targetLang.toLowerCase() === 'ar') {
      // Arabisch-spezifische Anweisungen
      languageSpecificInstructions = `
ANWEISUNGEN FÜR ARABISCH:
- Verwende modernes Standardarabisch (MSA)
- Vermeide Wiederholungen von Wörtern
- Achte auf korrekte Richtung (RTL)`;
    } else if (targetLang.toLowerCase() === 'zh') {
      // Chinesisch-spezifische Anweisungen
      languageSpecificInstructions = `
ANWEISUNGEN FÜR CHINESISCH:
- Verwende vereinfachtes Chinesisch
- Behalte Fachbegriffe bei`;
    }

    let userPrompt = '';
    if (sourceLang && sourceLang !== 'auto') {
      userPrompt = `Übersetze den folgenden Text von ${sourceLanguageName} nach ${targetLanguageName}.

ANWEISUNGEN:
1. Übersetze den Text vollständig und korrekt.
2. Behalte die Struktur des Originaltextes bei.
3. Wiederhole keine Texte oder Phrasen mehrfach.
4. Gib nur den übersetzten Text zurück.
5. Setze Leerzeichen zwischen Wörtern.
6. Prüfe auf Duplikate.
${languageSpecificInstructions}

Text zum Übersetzen:
${cleanedText}`;
    } else {
      userPrompt = `Übersetze den folgenden Text nach ${targetLanguageName}. Erkenne automatisch die Quellsprache.

ANWEISUNGEN:
1. Übersetze den Text vollständig und korrekt.
2. Behalte die Struktur des Originaltextes bei.
3. Wiederhole keine Texte oder Phrasen mehrfach.
4. Gib nur den übersetzten Text zurück.
5. Setze Leerzeichen zwischen Wörtern.
6. Prüfe auf Duplikate.
${languageSpecificInstructions}

Text zum Übersetzen:
${cleanedText}`;
    }

    console.log(`Sende Übersetzungsanfrage an Groq API für Text (${text.length} Zeichen) nach ${targetLang}...`);

    // Erstelle den Request-Body für die Groq API
    // Berechne eine angemessene Anzahl von Tokens, aber beschränke auf den gültigen Bereich
    const estimatedTokens = Math.min(8000, Math.max(1000, Math.min(text.length * 2, 8000)));

    const payload = {
      model: 'mistral-7b-instruct', // Mistral 7B Instruct Modell
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.1, // Niedrige Temperatur für konsistente Übersetzungen
      max_tokens: estimatedTokens, // Genug Tokens für die Übersetzung
      stream: useStream // Stream-Modus basierend auf Parameter
    };

    // Sende die Anfrage an die Groq API
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${groqApiKey}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Groq API Fehler (${response.status}): ${errorText}`);
      return {
        error: `Fehler bei der Übersetzung (${response.status}): ${errorText}`
      };
    }

    // Wenn Stream-Modus aktiviert ist, gib den Stream direkt zurück
    if (useStream) {
      console.log('Stream-Modus: Gebe Groq API Stream zurück');

      // Transformiere den Stream, um die Daten zu verarbeiten
      const transformStream = new TransformStream({
        start(controller) {
          // Sende eine initiale Nachricht mit Metadaten
          const initialMessage = {
            type: 'meta',
            detectedSourceLanguage: sourceLang || detectLanguage(text),
            targetLanguage: targetLang,
            timestamp: new Date().toISOString(),
            status: 'started'
          };
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(initialMessage)}\n\n`));
        },
        async transform(chunk, controller) {
          // Verarbeite die Chunk-Daten vom Groq-Stream
          const text = new TextDecoder().decode(chunk);

          // Verarbeite jede Zeile im Chunk
          const lines = text.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
              try {
                const data = JSON.parse(line.substring(6));
                if (data.choices && data.choices.length > 0) {
                  const content = data.choices[0].delta?.content || '';
                  if (content) {
                    // Verarbeite den Inhalt, um Wiederholungen zu entfernen
                    // Wir können hier keine vollständige Nachbearbeitung durchführen,
                    // da wir nur Teilstücke des Textes haben

                    // Sende den Inhalt als SSE-Event
                    const message = {
                      type: 'content',
                      content: content
                    };
                    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(message)}\n\n`));
                  }
                }
              } catch (e) {
                console.error('Fehler beim Parsen der Stream-Daten:', e);
              }
            } else if (line === 'data: [DONE]') {
              // Sende eine abschließende Nachricht
              const finalMessage = {
                type: 'meta',
                status: 'completed',
                timestamp: new Date().toISOString()
              };
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(finalMessage)}\n\n`));
            }
          }
        }
      });

      // Verbinde den Response-Stream mit unserem TransformStream
      const responseStream = response.body;
      if (responseStream) {
        responseStream.pipeTo(transformStream.writable);
        return transformStream.readable;
      } else {
        return { error: 'Stream konnte nicht erstellt werden.' };
      }
    }

    // Für nicht-Stream-Anfragen, verarbeite die Antwort als JSON
    const data = await response.json();
    console.log('Groq API Antwort erhalten:', JSON.stringify(data).substring(0, 200) + '...');

    // Extrahiere den übersetzten Text aus der Antwort
    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
      let translatedText = data.choices[0].message.content.trim();

      // Nachbearbeitung des übersetzten Textes
      translatedText = postProcessTranslation(translatedText);

      // Versuche, die Quellsprache zu erkennen (falls nicht angegeben)
      let detectedSourceLanguage = sourceLang || 'auto';
      if (detectedSourceLanguage === 'auto') {
        detectedSourceLanguage = detectLanguage(text);
      }

      return {
        translatedText: translatedText,
        detectedSourceLanguage: detectedSourceLanguage,
        // Zusätzliche Metadaten
        model: data.model,
        timestamp: new Date().toISOString()
      };
    }

    // Verbesserte Hilfsfunktion zur Nachbearbeitung der Übersetzung
    function postProcessTranslation(text: string): string {
      if (!text) return '';

      // Entferne Übersetzungshinweise, falls vorhanden
      text = text.replace(/^(Übersetzung:|Translation:|Translated text:|Here's the translation:|Hier ist die Übersetzung:)/i, '').trim();

      // Entferne Quellsprachenhinweise
      text = text.replace(/^(Source language detected as [a-z]+:)/i, '').trim();

      // Teile den Text in Absätze auf
      const paragraphs = text.split('\n');
      const uniqueParagraphs: string[] = [];
      const seenParagraphs = new Set<string>();

      // Entferne Duplikate und normalisiere Absätze
      for (const paragraph of paragraphs) {
        // Normalisiere den Absatz (entferne mehrfache Leerzeichen, etc.)
        const normalizedParagraph = paragraph.trim().replace(/\s+/g, ' ');

        if (normalizedParagraph && !seenParagraphs.has(normalizedParagraph)) {
          seenParagraphs.add(normalizedParagraph);

          // Prüfe auf Teilstrings und Ähnlichkeiten
          let isSubstring = false;

          for (const existingParagraph of uniqueParagraphs) {
            const normalizedExisting = existingParagraph.trim().replace(/\s+/g, ' ');

            // Prüfe, ob dieser Absatz ein Teilstring eines bereits vorhandenen ist
            if (normalizedExisting.includes(normalizedParagraph)) {
              isSubstring = true;
              break;
            }

            // Prüfe, ob ein bereits vorhandener Absatz ein Teilstring dieses Absatzes ist
            if (normalizedParagraph.includes(normalizedExisting)) {
              // Entferne den kürzeren Absatz und füge den längeren hinzu
              const index = uniqueParagraphs.indexOf(existingParagraph);
              if (index !== -1) {
                uniqueParagraphs.splice(index, 1);
              }
            }
          }

          if (!isSubstring) {
            uniqueParagraphs.push(paragraph);
          }
        }
      }

      // Entferne zusammengesetzte Texte ohne Leerzeichen
      let cleanedText = uniqueParagraphs.join('\n');

      // Suche nach wiederholten Wortgruppen ohne Leerzeichen dazwischen
      // Unterstützt auch arabische und andere Unicode-Zeichen
      const repeatedPhraseRegex = /([\p{L}\p{M}]{3,}\s+[\p{L}\p{M}]{3,})(\1)+/gu;
      cleanedText = cleanedText.replace(repeatedPhraseRegex, '$1');

      // Suche nach wiederholten Wörtern ohne Leerzeichen dazwischen
      const repeatedWordRegex = /([\p{L}\p{M}]{3,})(\1)+/gu;
      cleanedText = cleanedText.replace(repeatedWordRegex, '$1');

      // Entferne zusammengesetzte Wörter ohne Leerzeichen (für arabische Texte)
      // z.B. "تفاصيلتفاصيل" -> "تفاصيل"
      const arabicRepeatedWordRegex = /([\u0600-\u06FF]{3,})(\1)+/g;
      cleanedText = cleanedText.replace(arabicRepeatedWordRegex, '$1');

      // Füge Leerzeichen zwischen Wörtern ein, die fälschlicherweise zusammengefügt wurden
      // Erkenne Wortgrenzen durch Groß-/Kleinschreibung
      cleanedText = cleanedText.replace(/([a-z])([A-Z])/g, '$1 $2');

      return cleanedText;
    }
    } else {
      return { error: 'Keine Übersetzung in der API-Antwort gefunden.' };
    }
  } catch (error) {
    console.error('Fehler bei der Übersetzung:', error);
    return { error: `Übersetzungsfehler: ${error.message || 'Unbekannter Fehler'}` };
  }
}

// Hauptfunktion zum Verarbeiten der HTTP-Anfragen
serve(async (req) => {
  // CORS-Vorflug-Anfragen verarbeiten
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Nur POST-Anfragen akzeptieren
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Nur POST-Anfragen werden unterstützt.' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Anfragedaten parsen
    let requestData;
    try {
      requestData = await req.json();
    } catch (e) {
      console.error('[Serve] Fehler beim Parsen der Anfragedaten:', e);
      return new Response(
        JSON.stringify({ error: 'Ungültiges JSON-Format in der Anfrage.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Prüfe, ob der zu übersetzende Text vorhanden ist
    if (!requestData.text || typeof requestData.text !== 'string') {
      console.error('[Serve] Fehlender oder ungültiger Text in der Anfrage.');
      return new Response(
        JSON.stringify({ error: 'Der zu übersetzende Text fehlt oder ist ungültig.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Standardwerte für Ziel- und Quellsprache
    const targetLang = requestData.targetLang || 'de';
    const sourceLang = requestData.sourceLang || undefined; // Optional
    const useStream = requestData.stream === true; // Stream-Modus aktivieren, wenn explizit angefordert

    // Text übersetzen
    const result = await translateWithLlama(requestData.text, targetLang, sourceLang, useStream);

    // Wenn es ein Stream ist, gib ihn direkt zurück
    if (result instanceof ReadableStream) {
      console.log('[Serve] Stream-Antwort wird zurückgegeben.');
      return new Response(result, {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      });
    }

    // Prüfe auf Fehler
    if ('error' in result) {
      console.error('[Serve] Fehler bei der Übersetzung:', result.error);
      return new Response(
        JSON.stringify({ error: result.error }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Erfolgreiche Antwort (nicht-Stream)
    console.log('[Serve] Text erfolgreich übersetzt.');
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('[Serve] Unerwarteter Fehler:', error);
    return new Response(
      JSON.stringify({ error: `Unerwarteter Fehler: ${error.message}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

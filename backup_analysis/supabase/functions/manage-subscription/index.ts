// Supabase Edge Function zum Verwalten von Abonnements
// Diese Funktion ermöglicht das Erstellen, Verlängern und Überprüfen von Premium-Abonnements

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface Subscription {
  id: string;
  user_id: string;
  status: 'active' | 'cancelled' | 'expired';
  product_id: string;
  platform: string;
  expires_at: string;
  cancelled_at: string | null;
  created_at: string;
  updated_at: string;
}

interface VerifyRequest {
  action: 'verify_purchase';
  receipt: string;
  platform: string;
  product_id: string;
  user_id: string;
}

interface UpdateRequest {
  action: 'update_subscription';
  subscription_id: string;
  status: string;
  user_id: string;
}

interface CheckRequest {
  action: 'check_subscription';
  user_id: string;
}

type SubscriptionRequest = VerifyRequest | UpdateRequest | CheckRequest;

serve(async (req) => {
  // CORS-Vorflug-Anfrage behandeln
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Supabase-Client erstellen
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Anfragedaten extrahieren
    const requestData: SubscriptionRequest = await req.json();
    const { action } = requestData;

    // Je nach Aktion unterschiedliche Funktionen aufrufen
    switch (action) {
      case 'verify_purchase':
        return await handleVerifyPurchase(requestData, supabaseClient);
      case 'update_subscription':
        return await handleUpdateSubscription(requestData, supabaseClient);
      case 'check_subscription':
        return await handleCheckSubscription(requestData, supabaseClient);
      default:
        return new Response(
          JSON.stringify({ success: false, message: 'Ungültige Aktion' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
        );
    }
  } catch (error) {
    console.error('Fehler bei der Verarbeitung:', error);
    return new Response(
      JSON.stringify({ success: false, message: 'Serverfehler', error: String(error) }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});

/**
 * Verifiziert einen App-Store oder Play Store Kauf und erstellt ein neues Abonnement
 */
async function handleVerifyPurchase(
  data: VerifyRequest,
  supabaseClient: ReturnType<typeof createClient>
) {
  const { receipt, platform, product_id, user_id } = data;

  // Hier würde im Produktiveinsatz eine echte Validierung erfolgen:
  // - Für iOS: App Store Server API aufrufen
  // - Für Android: Google Play Developer API aufrufen
  
  // Für diese Demo/Entwicklung: Angenommen die Validierung ist erfolgreich
  const validationSuccessful = true;
  const mockExpiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 Tage in der Zukunft

  if (validationSuccessful) {
    try {
      // Prüfen, ob bereits ein aktives Abonnement existiert
      const { data: existingSubscriptions, error: fetchError } = await supabaseClient
        .from('subscriptions')
        .select('*')
        .eq('user_id', user_id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1);

      let subscription: Subscription | null = null;

      const now = new Date().toISOString();
      const expiresAt = mockExpiresAt.toISOString();

      if (fetchError) {
        console.error('Fehler beim Abrufen existierender Abonnements:', fetchError);
      }

      // Wenn ein aktives Abonnement existiert, aktualisieren wir dieses
      if (existingSubscriptions && existingSubscriptions.length > 0) {
        subscription = existingSubscriptions[0];
        
        const { data: updatedSub, error: updateError } = await supabaseClient
          .from('subscriptions')
          .update({
            expires_at: expiresAt,
            updated_at: now,
          })
          .eq('id', subscription.id)
          .select()
          .single();

        if (updateError) {
          console.error('Fehler beim Aktualisieren des Abonnements:', updateError);
          throw updateError;
        }
        
        subscription = updatedSub;
        console.log('Bestehendes Abonnement aktualisiert:', subscription.id);
      } else {
        // Neues Abonnement erstellen
        const { data: newSub, error: insertError } = await supabaseClient
          .from('subscriptions')
          .insert({
            user_id,
            status: 'active',
            product_id,
            platform,
            expires_at: expiresAt,
            created_at: now,
            updated_at: now,
          })
          .select()
          .single();

        if (insertError) {
          console.error('Fehler beim Erstellen des Abonnements:', insertError);
          throw insertError;
        }
        
        subscription = newSub;
        console.log('Neues Abonnement erstellt:', subscription.id);
      }

      return new Response(
        JSON.stringify({
          verified: true,
          message: 'Kauf erfolgreich verifiziert',
          expires_at: expiresAt,
          subscription,
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } catch (error) {
      console.error('Fehler bei der Datenbankoperation:', error);
      return new Response(
        JSON.stringify({
          verified: false,
          message: 'Fehler bei der Datenbankoperation',
          error: String(error),
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      );
    }
  } else {
    return new Response(
      JSON.stringify({
        verified: false,
        message: 'Kaufvalidierung fehlgeschlagen',
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

/**
 * Aktualisiert den Status eines bestehenden Abonnements
 */
async function handleUpdateSubscription(
  data: UpdateRequest,
  supabaseClient: ReturnType<typeof createClient>
) {
  const { subscription_id, status, user_id } = data;

  try {
    // Prüfen, ob das Abonnement dem Benutzer gehört
    const { data: existingSub, error: fetchError } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('id', subscription_id)
      .eq('user_id', user_id)
      .single();

    if (fetchError) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Abonnement nicht gefunden oder nicht berechtigt',
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
      );
    }

    const updateData: Partial<Subscription> = {
      status: status as 'active' | 'cancelled' | 'expired',
      updated_at: new Date().toISOString(),
    };

    // Falls das Abonnement gekündigt wird, setzen wir das Kündigungsdatum
    if (status === 'cancelled') {
      updateData.cancelled_at = new Date().toISOString();
    }

    // Aktualisiere das Abonnement
    const { data: updatedSub, error: updateError } = await supabaseClient
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscription_id)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Abonnementstatus erfolgreich aktualisiert',
        subscription: updatedSub,
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Fehler bei der Aktualisierung des Abonnements:', error);
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Fehler bei der Aktualisierung des Abonnements',
        error: String(error),
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
}

/**
 * Prüft, ob der Benutzer ein aktives Abonnement hat
 */
async function handleCheckSubscription(
  data: CheckRequest,
  supabaseClient: ReturnType<typeof createClient>
) {
  const { user_id } = data;

  try {
    // Aktives Abonnement suchen
    const { data: subscriptions, error } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('user_id', user_id)
      .eq('status', 'active')
      .gt('expires_at', new Date().toISOString()) // Nur Abonnements, die noch nicht abgelaufen sind
      .order('expires_at', { ascending: false }) // Das mit dem spätesten Ablaufdatum zuerst
      .limit(1);

    if (error) {
      throw error;
    }

    const hasActiveSubscription = subscriptions && subscriptions.length > 0;
    
    // Prüfen und ggf. abgelaufene Abonnements aktualisieren
    await updateExpiredSubscriptions(user_id, supabaseClient);

    return new Response(
      JSON.stringify({
        has_subscription: hasActiveSubscription,
        subscription: hasActiveSubscription ? subscriptions[0] : null,
        message: hasActiveSubscription 
          ? 'Aktives Abonnement gefunden' 
          : 'Kein aktives Abonnement gefunden',
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Fehler bei der Abonnementprüfung:', error);
    return new Response(
      JSON.stringify({
        has_subscription: false,
        message: 'Fehler bei der Abonnementprüfung',
        error: String(error),
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
}

/**
 * Hilfsfunktion zum Aktualisieren abgelaufener Abonnements
 */
async function updateExpiredSubscriptions(
  user_id: string,
  supabaseClient: ReturnType<typeof createClient>
) {
  try {
    const now = new Date().toISOString();
    
    // Finde alle aktiven Abonnements, die abgelaufen sind
    const { data: expiredSubs, error: fetchError } = await supabaseClient
      .from('subscriptions')
      .select('id')
      .eq('user_id', user_id)
      .eq('status', 'active')
      .lt('expires_at', now); // Abonnements, deren Ablaufdatum in der Vergangenheit liegt
    
    if (fetchError) {
      console.error('Fehler beim Suchen abgelaufener Abonnements:', fetchError);
      return;
    }
    
    if (!expiredSubs || expiredSubs.length === 0) {
      // Keine abgelaufenen Abonnements gefunden
      return;
    }
    
    // Abgelaufene Abonnements aktualisieren
    const expiredIds = expiredSubs.map(sub => sub.id);
    const { error: updateError } = await supabaseClient
      .from('subscriptions')
      .update({ 
        status: 'expired',
        updated_at: now
      })
      .in('id', expiredIds);
    
    if (updateError) {
      console.error('Fehler beim Aktualisieren abgelaufener Abonnements:', updateError);
    } else {
      console.log(`${expiredIds.length} abgelaufene Abonnements auf 'expired' gesetzt`);
    }
  } catch (error) {
    console.error('Fehler beim Verarbeiten abgelaufener Abonnements:', error);
  }
} 
 
 
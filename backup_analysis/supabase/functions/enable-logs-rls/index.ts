// Supabase Edge Function zum Aktivieren von RLS für die public.logs-Tabelle
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// CORS-Header für Cross-Origin-Anfragen
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // CORS-Präflug-Anfragen behandeln
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Supabase-Client mit Service-Rolle erstellen
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // SQL-Befehle für die RLS-Aktivierung
    const sql = `
    -- Aktiviere RLS für die logs-Tabelle
    ALTER TABLE IF EXISTS public.logs ENABLE ROW LEVEL SECURITY;

    -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu lesen
    CREATE POLICY IF NOT EXISTS "Service role can read logs"
      ON public.logs
      FOR SELECT
      USING (auth.jwt() ->> 'role' = 'service_role');

    -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu erstellen
    CREATE POLICY IF NOT EXISTS "Service role can insert logs"
      ON public.logs
      FOR INSERT
      WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

    -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu aktualisieren
    CREATE POLICY IF NOT EXISTS "Service role can update logs"
      ON public.logs
      FOR UPDATE
      USING (auth.jwt() ->> 'role' = 'service_role');

    -- Erstelle eine Richtlinie, die nur der Service-Rolle erlaubt, Logs zu löschen
    CREATE POLICY IF NOT EXISTS "Service role can delete logs"
      ON public.logs
      FOR DELETE
      USING (auth.jwt() ->> 'role' = 'service_role');

    -- Kommentar zur Erklärung
    COMMENT ON TABLE public.logs IS 'Tabelle für Systemlogs mit aktivierter Row Level Security';
    `;

    // SQL-Befehle ausführen
    const { error } = await supabaseClient.rpc("exec_sql", { sql });

    if (error) {
      // Wenn exec_sql nicht existiert, versuche es mit direkten Abfragen
      console.error("Fehler bei exec_sql:", error);
      
      // Aktiviere RLS
      await supabaseClient.rpc("execute_sql", { 
        command: "ALTER TABLE IF EXISTS public.logs ENABLE ROW LEVEL SECURITY;" 
      });
      
      // Erstelle Richtlinien
      await supabaseClient.rpc("execute_sql", { 
        command: `CREATE POLICY IF NOT EXISTS "Service role can read logs" ON public.logs FOR SELECT USING (auth.jwt() ->> 'role' = 'service_role');` 
      });
      
      await supabaseClient.rpc("execute_sql", { 
        command: `CREATE POLICY IF NOT EXISTS "Service role can insert logs" ON public.logs FOR INSERT WITH CHECK (auth.jwt() ->> 'role' = 'service_role');` 
      });
      
      await supabaseClient.rpc("execute_sql", { 
        command: `CREATE POLICY IF NOT EXISTS "Service role can update logs" ON public.logs FOR UPDATE USING (auth.jwt() ->> 'role' = 'service_role');` 
      });
      
      await supabaseClient.rpc("execute_sql", { 
        command: `CREATE POLICY IF NOT EXISTS "Service role can delete logs" ON public.logs FOR DELETE USING (auth.jwt() ->> 'role' = 'service_role');` 
      });
      
      await supabaseClient.rpc("execute_sql", { 
        command: `COMMENT ON TABLE public.logs IS 'Tabelle für Systemlogs mit aktivierter Row Level Security';` 
      });
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "RLS für public.logs erfolgreich aktiviert",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Fehler:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});

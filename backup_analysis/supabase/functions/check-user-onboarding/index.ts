import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body to get user credentials or token
    let authToken = null
    let userEmail = null

    // Try to get auth token from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      authToken = authHeader.substring(7)
    }

    // Try to get credentials from request body
    if (req.method === 'POST') {
      try {
        const body = await req.json()
        userEmail = body.email
        authToken = body.token || authToken
      } catch (e) {
        // Body parsing failed, continue with header token
      }
    }

    console.log('Server-side authentication check started')
    console.log('Auth token present:', !!authToken)
    console.log('User email provided:', !!userEmail)

    // Create Supabase client for server-side operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', // Use service role for server-side
    )

    let user = null

    // Server-side authentication check
    if (authToken) {
      // Verify the provided token
      const { data: tokenUser, error: tokenError } = await supabaseClient.auth.getUser(authToken)
      if (!tokenError && tokenUser.user) {
        user = tokenUser.user
        console.log('User authenticated via token:', user.id)
      }
    }

    if (!user && userEmail) {
      // Try to find user by email in auth.users (server-side lookup)
      const { data: users, error: usersError } = await supabaseClient.auth.admin.listUsers()
      if (!usersError && users.users) {
        user = users.users.find(u => u.email === userEmail)
        if (user) {
          console.log('User found by email lookup:', user.id)
        }
      }
    }

    if (!user) {
      console.log('No user found - server-side authentication failed')
      return new Response(
        JSON.stringify({
          error: 'Server-side authentication failed - User not found',
          shouldShowOnboarding: false,
          isAuthenticated: false
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    console.log(`Prüfe Onboarding-Status für User: ${user.id}`)
    console.log(`User Auth created_at: ${user.created_at}`)

    // Check if user profile exists and onboarding is complete
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('onboarding_complete, created_at')
      .eq('id', user.id)
      .maybeSingle()

    if (profileError) {
      console.error('Fehler beim Laden des Profils:', profileError)
      return new Response(
        JSON.stringify({ 
          error: 'Fehler beim Laden des Profils',
          shouldShowOnboarding: false 
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    let shouldShowOnboarding = false
    let isNewUser = false
    let message = ''

    // Intelligente User-Erkennung basierend auf Auth-Account und Profil
    const userAuthCreatedAt = new Date(user.created_at)
    const now = new Date()
    const timeDifferenceMinutes = (now.getTime() - userAuthCreatedAt.getTime()) / (1000 * 60)

    console.log(`Auth Account erstellt vor ${timeDifferenceMinutes.toFixed(2)} Minuten`)

    if (!profile) {
      // Kein Profil vorhanden - prüfe wann Auth-Account erstellt wurde
      if (timeDifferenceMinutes <= 5) {
        // Auth-Account ist sehr neu (≤5 Minuten) -> definitiv neuer User
        shouldShowOnboarding = true
        isNewUser = true
        message = `Neuer User - Auth-Account vor ${timeDifferenceMinutes.toFixed(1)} Min erstellt`

        console.log(`User ${user.id}: Definitiv neuer User (Auth-Account sehr neu)`)
      } else {
        // Auth-Account ist älter aber kein Profil -> wahrscheinlich bestehender User ohne Profil
        shouldShowOnboarding = false
        isNewUser = false
        message = `Bestehender User ohne Profil - Auth-Account vor ${timeDifferenceMinutes.toFixed(1)} Min erstellt`

        console.log(`User ${user.id}: Wahrscheinlich bestehender User ohne Profil`)
      }

      // Erstelle Profil mit entsprechendem onboarding_complete Status
      const { error: insertError } = await supabaseClient
        .from('profiles')
        .insert({
          id: user.id,
          data: {
            email: user.email || '',
            firstName: '',
            lastName: '',
            profileCompleteness: 0,
          },
          onboarding_complete: !shouldShowOnboarding, // Wenn kein Onboarding nötig, dann als abgeschlossen markieren
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

      if (insertError) {
        console.error('Fehler beim Erstellen des Profils:', insertError)
      } else {
        console.log(`Profil erstellt für User ${user.id} mit onboarding_complete=${!shouldShowOnboarding}`)
      }
    } else {
      // Profil existiert -> prüfe Onboarding-Status und Profil-Alter
      const onboardingComplete = profile.onboarding_complete ?? false
      const profileCreatedAt = new Date(profile.created_at)
      const profileAgeMinutes = (now.getTime() - profileCreatedAt.getTime()) / (1000 * 60)

      console.log(`Profil erstellt vor ${profileAgeMinutes.toFixed(2)} Minuten`)

      if (onboardingComplete) {
        shouldShowOnboarding = false
        isNewUser = false
        message = 'Onboarding bereits abgeschlossen'
      } else {
        // Onboarding nicht abgeschlossen - prüfe ob User wirklich neu ist
        if (profileAgeMinutes <= 10) {
          // Profil ist sehr neu -> wahrscheinlich neuer User
          shouldShowOnboarding = true
          isNewUser = true
          message = `Neuer User - Profil vor ${profileAgeMinutes.toFixed(1)} Min erstellt`
        } else {
          // Profil ist älter aber Onboarding nicht abgeschlossen -> bestehender User
          shouldShowOnboarding = false
          isNewUser = false
          message = `Bestehender User - Onboarding übersprungen (Profil ${profileAgeMinutes.toFixed(1)} Min alt)`

          // Markiere Onboarding als abgeschlossen für bestehende User
          await supabaseClient
            .from('profiles')
            .update({
              onboarding_complete: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id)

          console.log(`Onboarding automatisch als abgeschlossen markiert für bestehenden User ${user.id}`)
        }
      }

      console.log(`User ${user.id}: onboarding_complete = ${onboardingComplete}, shouldShow = ${shouldShowOnboarding}`)
    }

    const result = {
      shouldShowOnboarding,
      isNewUser,
      message,
      userId: user.id,
      userEmail: user.email,
      userAuthCreatedAt: user.created_at,
      timeDifferenceMinutes: timeDifferenceMinutes.toFixed(2),
      isAuthenticated: true,
      serverSideCheck: true,
      timestamp: new Date().toISOString(),
    }

    console.log(`Ergebnis für User ${user.id}:`, result)

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Unerwarteter Fehler:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Unerwarteter Fehler',
        shouldShowOnboarding: false 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

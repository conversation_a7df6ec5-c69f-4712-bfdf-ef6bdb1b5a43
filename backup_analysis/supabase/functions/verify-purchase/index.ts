import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

/**
 * Typ für Kaufverifizierungsanfrage
 */
interface VerifyPurchaseRequest {
  userId: string;
  platform: "ios" | "android";
  receiptData: string;
  transactionId?: string;
  purchaseToken?: string; // Für Android
  packageName?: string; // Für Android
  productId: string;
}

/**
 * Typ für Kaufverifizierungsantwort
 */
interface VerifyPurchaseResponse {
  isValid: boolean;
  subscriptionId?: string;
  expireDate?: string;
  errorCode?: string;
  errorMessage?: string;
}

/**
 * Env-Variablen
 */
const APPLE_SHARED_SECRET = Deno.env.get("APPLE_SHARED_SECRET") || "";
const IS_SANDBOX_MODE = Deno.env.get("IS_SANDBOX_MODE") === "true";
const GOOGLE_SERVICE_ACCOUNT = Deno.env.get("GOOGLE_SERVICE_ACCOUNT") || "{}";

// Apple App Store Verifikations-URLs
const APPLE_PRODUCTION_URL = "https://buy.itunes.apple.com/verifyReceipt";
const APPLE_SANDBOX_URL = "https://sandbox.itunes.apple.com/verifyReceipt";

// Supabase-Client erzeugen
const supabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
);

/**
 * Überprüft einen iOS-Kauf über die Apple Verification API
 */
async function verifyIosPurchase(
  receiptData: string,
  transactionId?: string
): Promise<VerifyPurchaseResponse> {
  try {
    console.log("Überprüfe iOS-Kauf");
    
    // Zuerst die Produktion-URL versuchen
    let verificationUrl = APPLE_PRODUCTION_URL;
    if (IS_SANDBOX_MODE) {
      verificationUrl = APPLE_SANDBOX_URL;
    }
    
    // Anfrage an Apple senden
    const response = await fetch(verificationUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "receipt-data": receiptData,
        "password": APPLE_SHARED_SECRET,
        "exclude-old-transactions": true
      }),
    });
    
    if (!response.ok) {
      console.error(`Apple API Error: ${response.status}`);
      return {
        isValid: false,
        errorCode: "api_error",
        errorMessage: `Apple API Status: ${response.status}`,
      };
    }
    
    const data = await response.json();
    
    // Status 21007 bedeutet, dass es eine Sandbox-Quittung ist, die an die Produktion-URL gesendet wurde
    if (data.status === 21007 && verificationUrl !== APPLE_SANDBOX_URL) {
      console.log("Quittung ist für Sandbox, versuche Sandbox-URL");
      return verifyIosPurchase(receiptData, transactionId);
    }
    
    // Verifizierungsergebnis prüfen
    if (data.status !== 0) {
      return {
        isValid: false,
        errorCode: `apple_status_${data.status}`,
        errorMessage: `Apple Verification Failed: Status ${data.status}`,
      };
    }
    
    // Abonnement-Details extrahieren
    if (data.receipt && data.latest_receipt_info) {
      // Verwende das neueste Abonnement
      const latestTransaction = data.latest_receipt_info.sort((a: any, b: any) => 
        parseInt(b.purchase_date_ms) - parseInt(a.purchase_date_ms)
      )[0];
      
      // Prüfe, ob das Abonnement noch aktiv ist
      const expireDate = new Date(parseInt(latestTransaction.expires_date_ms));
      const isActive = expireDate > new Date();
      
      if (!isActive) {
        return {
          isValid: false,
          expireDate: expireDate.toISOString(),
          errorCode: "subscription_expired",
          errorMessage: "Das Abonnement ist abgelaufen",
        };
      }
      
      return {
        isValid: true,
        subscriptionId: latestTransaction.original_transaction_id,
        expireDate: expireDate.toISOString(),
      };
    }
    
    return {
      isValid: false,
      errorCode: "invalid_receipt_data",
      errorMessage: "Keine gültigen Abonnementdaten in der Quittung",
    };
  } catch (error) {
    console.error("Fehler bei der iOS-Kaufüberprüfung:", error);
    return {
      isValid: false,
      errorCode: "verification_error",
      errorMessage: `Verification Error: ${error.message}`,
    };
  }
}

/**
 * Überprüft einen Android-Kauf über die Google Play Developer API
 */
async function verifyAndroidPurchase(
  purchaseToken: string,
  packageName: string,
  productId: string
): Promise<VerifyPurchaseResponse> {
  try {
    console.log("Überprüfe Android-Kauf");
    
    // Im Testmodus simulieren wir eine erfolgreiche Überprüfung
    if (IS_SANDBOX_MODE) {
      console.log("Android-Kaufüberprüfung im Testmodus, simuliere Erfolg");
      return {
        isValid: true,
        subscriptionId: `sandbox_${productId}_${Date.now()}`,
        expireDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      };
    }
    
    // Grundsätzlich würde hier ein komplexerer Flow für die Authentifizierung bei Google
    // und die Überprüfung des Kaufs über die Google Play Developer API folgen.
    // Dies erfordert die Implementierung von OAuth mit einem Service Account.
    
    // Da dies den Rahmen dieser Funktion sprengen würde, ist hier ein einfaches Muster,
    // wie der tatsächliche Aufruf aussehen könnte:
    
    // 1. Authentifizierungstoken von Google erhalten
    // 2. API-Aufruf zur Purchase-Verifikation
    // const response = await fetch(
    //   `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${packageName}/purchases/subscriptions/${productId}/tokens/${purchaseToken}`,
    //   {
    //     headers: {
    //       Authorization: `Bearer ${accessToken}`,
    //     },
    //   }
    // );
    
    // For now, we'll implement a dummy success
    return {
      isValid: true,
      subscriptionId: `temp_${productId}_${Date.now()}`,
      expireDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    };
  } catch (error) {
    console.error("Fehler bei der Android-Kaufüberprüfung:", error);
    return {
      isValid: false,
      errorCode: "verification_error",
      errorMessage: `Verification Error: ${error.message}`,
    };
  }
}

/**
 * Speichert das Abonnement in der Datenbank
 */
async function storeSubscription(
  userId: string,
  platform: string,
  productId: string,
  receiptData: string,
  transactionId: string | undefined,
  verificationResult: VerifyPurchaseResponse
): Promise<string | null> {
  try {
    if (!verificationResult.isValid || !verificationResult.expireDate) {
      console.error("Ungültiges Verifizierungsergebnis");
      return null;
    }
    
    // Aktuelles Datum für Start- und Erstellungszeitstempel
    const now = new Date().toISOString();
    
    // Abonnementtyp basierend auf Produkt-ID bestimmen
    const subscriptionType = productId.includes("month") ? "monthly" : "yearly";
    
    // Neues Abonnement in der Datenbank erstellen
    const { data: subscription, error: subscriptionError } = await supabaseClient
      .from("subscriptions")
      .insert({
        user_id: userId,
        subscription_type: subscriptionType,
        status: "active",
        start_date: now,
        end_date: verificationResult.expireDate,
        auto_renew: true,
      })
      .select()
      .single();
    
    if (subscriptionError) {
      console.error("Fehler beim Erstellen des Abonnements:", subscriptionError);
      return null;
    }
    
    // Kauf in der Datenbank speichern
    const { error: purchaseError } = await supabaseClient
      .from("purchases")
      .insert({
        user_id: userId,
        subscription_id: subscription.id,
        platform: platform,
        transaction_id: transactionId || verificationResult.subscriptionId,
        receipt_data: receiptData,
        purchase_date: now,
        verification_status: "verified",
      });
    
    if (purchaseError) {
      console.error("Fehler beim Speichern des Kaufs:", purchaseError);
      return null;
    }
    
    return subscription.id;
  } catch (error) {
    console.error("Fehler beim Speichern des Abonnements:", error);
    return null;
  }
}

serve(async (req: Request) => {
  // CORS-Header für Browser-Anfragen
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  // CORS-Präflight-Anfragen beantworten
  if (req.method === "OPTIONS") {
    return new Response(null, { headers, status: 204 });
  }
  
  // Nur POST-Anfragen akzeptieren
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { headers, status: 405 }
    );
  }
  
  try {
    // Anfragedaten verarbeiten
    const requestData: VerifyPurchaseRequest = await req.json();
    
    // Pflichtfelder prüfen
    if (!requestData.userId || !requestData.platform || !requestData.productId) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { headers, status: 400 }
      );
    }
    
    if (!requestData.receiptData) {
      return new Response(
        JSON.stringify({ error: "Receipt data is required" }),
        { headers, status: 400 }
      );
    }
    
    let verificationResult: VerifyPurchaseResponse;
    
    // Je nach Plattform verifizieren
    if (requestData.platform === "ios") {
      verificationResult = await verifyIosPurchase(
        requestData.receiptData,
        requestData.transactionId
      );
    } else if (requestData.platform === "android") {
      if (!requestData.purchaseToken || !requestData.packageName) {
        return new Response(
          JSON.stringify({ error: "Purchase token and package name are required for Android" }),
          { headers, status: 400 }
        );
      }
      
      verificationResult = await verifyAndroidPurchase(
        requestData.purchaseToken,
        requestData.packageName,
        requestData.productId
      );
    } else {
      return new Response(
        JSON.stringify({ error: "Invalid platform" }),
        { headers, status: 400 }
      );
    }
    
    // Wenn der Kauf gültig ist, speichern wir das Abonnement in der Datenbank
    let subscriptionId: string | null = null;
    if (verificationResult.isValid) {
      subscriptionId = await storeSubscription(
        requestData.userId,
        requestData.platform,
        requestData.productId,
        requestData.receiptData,
        requestData.transactionId || requestData.purchaseToken,
        verificationResult
      );
    }
    
    // Antwortobjekt erstellen
    const responseData = {
      ...verificationResult,
      subscriptionId: subscriptionId || verificationResult.subscriptionId,
    };
    
    return new Response(
      JSON.stringify(responseData),
      { headers, status: 200 }
    );
  } catch (error) {
    console.error("Fehler bei der Kaufverifikation:", error);
    
    return new Response(
      JSON.stringify({
        isValid: false,
        errorCode: "server_error",
        errorMessage: `Server Error: ${error.message}`,
      }),
 
 
 
// Supabase Edge Function für die Überprüfung und Speicherung von Abonnements
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Typen für die Anfrage und Antwort
interface VerifySubscriptionRequest {
  userId: string;
  platform: 'ios' | 'android' | 'web';
  subscriptionType: 'monthly' | 'yearly' | 'lifetime';
  transactionId?: string;
  receiptData?: string;
  purchaseDate: string;
}

interface VerifySubscriptionResponse {
  success: boolean;
  message: string;
  subscriptionId?: string;
  expiryDate?: string;
  error?: any;
}

// Verifizierungsfunktion für iOS
async function verifyIosPurchase(
  receipt: string,
  sandbox: boolean = false
): Promise<{ verified: boolean; expirationDate?: string; error?: any }> {
  try {
    // URL für die Verifizierung (Produktion oder Sandbox)
    const verifyUrl = sandbox
      ? "https://sandbox.itunes.apple.com/verifyReceipt"
      : "https://buy.itunes.apple.com/verifyReceipt";

    // Apple-Verifizierungsanfrage
    const response = await fetch(verifyUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "receipt-data": receipt,
        password: Deno.env.get("APPLE_SHARED_SECRET"),
      }),
    });

    if (!response.ok) {
      throw new Error(`Apple API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Statuscode 0 bedeutet, dass die Verifizierung erfolgreich war
    if (data.status === 0) {
      // Extrahieren der Ablaufdaten aus den neuesten In-App-Käufen
      let expirationDate: string | undefined;
      
      // Prüfen auf Abonnementdaten
      if (data.latest_receipt_info && data.latest_receipt_info.length > 0) {
        // Sortieren nach Ablaufdatum (absteigend)
        const sortedReceipts = [...data.latest_receipt_info].sort(
          (a, b) => parseInt(b.expires_date_ms) - parseInt(a.expires_date_ms)
        );
        
        // Neustes Ablaufdatum extrahieren
        if (sortedReceipts[0].expires_date_ms) {
          expirationDate = new Date(
            parseInt(sortedReceipts[0].expires_date_ms)
          ).toISOString();
        }
      }

      return {
        verified: true,
        expirationDate,
      };
    } else if (data.status === 21007 && !sandbox) {
      // Status 21007 bedeutet, dass der Receipt aus der Sandbox-Umgebung stammt
      console.log("Receipt is from sandbox, retrying with sandbox URL");
      return verifyIosPurchase(receipt, true);
    } else {
      console.error("Apple verification error:", data);
      return {
        verified: false,
        error: `Apple verification failed with status: ${data.status}`,
      };
    }
  } catch (error) {
    console.error("Error verifying iOS purchase:", error);
    return {
      verified: false,
      error: `Verification error: ${error.message}`,
    };
  }
}

// Verifizierungsfunktion für Android
async function verifyAndroidPurchase(
  purchaseToken: string,
  packageName: string = "de.krysuk.jobagent",
  productId: string = "premium_monthly_subscription"
): Promise<{ verified: boolean; expirationDate?: string; error?: any }> {
  try {
    // Verwende eine Umgebungsvariable, um den Test-Modus zu aktivieren
    const testMode = Deno.env.get("ANDROID_TEST_MODE") === "true";
    
    if (testMode) {
      console.log("Running in test mode, simulating successful verification");
      // Im Testmodus eine erfolgreiche Verifizierung simulieren
      
      // Ein Ablaufdatum in einem Monat festlegen
      const expirationDate = new Date();
      expirationDate.setMonth(expirationDate.getMonth() + 1);
      
      return {
        verified: true,
        expirationDate: expirationDate.toISOString(),
      };
    }
    
    // TODO: Implementiere die echte Verifizierung mit der Google Play Developer API
    // Diese Implementierung würde die Google Play Developer API verwenden,
    // um das Abonnement zu verifizieren
    
    // Beispielimplementierung:
    /*
    const auth = new GoogleAuth({
      credentials: JSON.parse(Deno.env.get("GOOGLE_SERVICE_ACCOUNT_KEY")),
      scopes: ['https://www.googleapis.com/auth/androidpublisher'],
    });
    
    const client = await auth.getClient();
    const url = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${packageName}/purchases/subscriptions/${productId}/tokens/${purchaseToken}`;
    
    const response = await client.request({ url });
    
    if (response.data && response.data.expiryTimeMillis) {
      const expirationDate = new Date(parseInt(response.data.expiryTimeMillis)).toISOString();
      return {
        verified: response.data.paymentState === 1,
        expirationDate
      };
    }
    */
    
    // Vorübergehend: Eine erfolgreiche Verifizierung mit einem Ablaufdatum in einem Monat simulieren
    const expirationDate = new Date();
    expirationDate.setMonth(expirationDate.getMonth() + 1);
    
    return {
      verified: true,
      expirationDate: expirationDate.toISOString(),
    };
  } catch (error) {
    console.error("Error verifying Android purchase:", error);
    return {
      verified: false,
      error: `Verification error: ${error.message}`,
    };
  }
}

// Funktion zum Speichern des Abonnements in der Datenbank
async function storeSubscription(
  supabase: any,
  userId: string,
  subscriptionType: 'monthly' | 'yearly' | 'lifetime',
  platform: 'ios' | 'android' | 'web',
  transactionId: string | null,
  receiptData: string | null,
  purchaseDate: string,
  verified: boolean,
  expirationDate: string | null
): Promise<{ success: boolean; subscriptionId?: string; error?: any }> {
  try {
    // Transaktion starten
    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .insert({
        user_id: userId,
        subscription_type: subscriptionType,
        status: verified ? "active" : "pending",
        start_date: purchaseDate,
        end_date: expirationDate,
        auto_renew: true,
      })
      .select("id")
      .single();

    if (subscriptionError) {
      throw subscriptionError;
    }

    // Kaufeintrag speichern
    const { error: purchaseError } = await supabase
      .from("purchases")
      .insert({
        user_id: userId,
        subscription_id: subscription.id,
        platform: platform,
        transaction_id: transactionId,
        receipt_data: receiptData,
        purchase_date: purchaseDate,
        verification_status: verified ? "verified" : "pending",
        verification_date: new Date().toISOString(),
      });

    if (purchaseError) {
      throw purchaseError;
    }

    return {
      success: true,
      subscriptionId: subscription.id,
    };
  } catch (error) {
    console.error("Error storing subscription:", error);
    return {
      success: false,
      error: `Database error: ${error.message}`,
    };
  }
}

serve(async (req) => {
  // CORS-Header für alle Anfragen
  const headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  };

  // OPTIONS-Anfragen für CORS beantworten
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers,
      status: 204,
    });
  }

  // Nur POST-Anfragen akzeptieren
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      headers: { ...headers, "Content-Type": "application/json" },
      status: 405,
    });
  }

  try {
    // Anfragedaten parsen
    const requestData: VerifySubscriptionRequest = await req.json();
    
    // Validierung
    if (!requestData.userId) {
      throw new Error("userId is required");
    }
    
    if (!requestData.platform) {
      throw new Error("platform is required");
    }
    
    if (!requestData.subscriptionType) {
      throw new Error("subscriptionType is required");
    }
    
    // Platform-spezifische Validierung
    if (requestData.platform === "ios" && !requestData.receiptData) {
      throw new Error("receiptData is required for iOS");
    }
    
    if (requestData.platform === "android" && !requestData.transactionId) {
      throw new Error("transactionId is required for Android");
    }

    // Supabase-Client erstellen
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Verifizierungsanfrage basierend auf der Plattform
    let verificationResult = {
      verified: false,
      expirationDate: null,
      error: null,
    };

    if (requestData.platform === "ios") {
      const result = await verifyIosPurchase(requestData.receiptData!);
      verificationResult = {
        verified: result.verified,
        expirationDate: result.expirationDate || null,
        error: result.error || null,
      };
    } else if (requestData.platform === "android") {
      const result = await verifyAndroidPurchase(requestData.transactionId!);
      verificationResult = {
        verified: result.verified,
        expirationDate: result.expirationDate || null,
        error: result.error || null,
      };
    } else if (requestData.platform === "web") {
      // Web-Plattform - hier könnte eine andere Verifizierung implementiert werden
      // Für Testzwecke setzen wir verified = true
      const expirationDate = new Date();
      expirationDate.setMonth(expirationDate.getMonth() + 1);
      
      verificationResult = {
        verified: true,
        expirationDate: expirationDate.toISOString(),
        error: null,
      };
    }

    // Bei erfolgreicher Verifizierung das Abonnement speichern
    let storeResult = { success: false, subscriptionId: null, error: null };
    
    if (verificationResult.verified) {
      storeResult = await storeSubscription(
        supabaseClient,
        requestData.userId,
        requestData.subscriptionType,
        requestData.platform,
        requestData.transactionId || null,
        requestData.receiptData || null,
        requestData.purchaseDate,
        verificationResult.verified,
        verificationResult.expirationDate
      );
    }

    // Antwort zusammenstellen
    const response: VerifySubscriptionResponse = {
      success: verificationResult.verified && storeResult.success,
      message: verificationResult.verified 
        ? "Subscription verified and stored successfully"
        : "Subscription verification failed",
      subscriptionId: storeResult.subscriptionId || undefined,
      expiryDate: verificationResult.expirationDate || undefined,
    };

    if (verificationResult.error || storeResult.error) {
      response.error = verificationResult.error || storeResult.error;
    }

    return new Response(JSON.stringify(response), {
      headers: { ...headers, "Content-Type": "application/json" },
      status: response.success ? 200 : 400,
    });
  } catch (error) {
    console.error("Error processing request:", error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: "Error processing subscription verification",
        error: error.message,
      }),
      {
        headers: { ...headers, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
}); 
 
 
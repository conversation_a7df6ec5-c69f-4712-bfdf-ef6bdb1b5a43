import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

console.log('Subscription Schema Setup Function geladen')

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // CORS-Präflug-Anfragen verarbeiten
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Prüfe Authorization Header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Nicht autorisiert' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Supabase-Client initialisieren
    const supabaseClient = createClient(
      // Supabase API URL - aus Umgebungsvariablen bereitstellen
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - aus Umgebungsvariablen bereitstellen
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Benutzerdefinierte Supabase-Client-Options - Auth-Header vom Request
      { global: { headers: { Authorization: authHeader } } }
    )

    // Benutzer authentifizieren
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      throw new Error('Nicht autorisierter Benutzer')
    }

    // SQL zur Erstellung des Schemas ausführen
    const { data, error } = await supabaseClient.rpc('create_subscriptions_schema')

    if (error) {
      throw new Error(`Fehler beim Ausführen des SQL-Befehls: ${error.message}`)
    }

    // Erfolgreiche Antwort
    return new Response(
      JSON.stringify({ success: true, message: 'Subscription-Schema erfolgreich erstellt', data }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    // Fehlerbehandlung
    console.error('Fehler:', error.message)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 
 
 
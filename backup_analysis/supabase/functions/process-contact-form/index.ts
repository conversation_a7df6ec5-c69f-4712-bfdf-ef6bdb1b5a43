// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'
import { corsHeaders } from '../_shared/cors.ts'
import { SMTPClient } from "https://deno.land/x/denomailer@0.12.0/mod.ts";

console.log("Hello from process-contact-form function!")

// SMTP-Konfiguration für E-Mail-Versand
const smtpConfig = {
  hostname: Deno.env.get("SMTP_HOST") || "", // SMTP-Host (z.B. smtp.ionos.de)
  port: parseInt(Deno.env.get("SMTP_PORT") || "587"), // SMTP-Port (meist 587 für TLS)
  username: Deno.env.get("SMTP_USERNAME") || "", // SMTP-Benutzername (meist E-Mail-Adresse)
  password: Deno.env.get("SMTP_PASSWORD") || "", // SMTP-Passwort
}

// Empfänger-E-Mail (wohin die Kontaktanfragen gesendet werden)
const RECIPIENT_EMAIL = Deno.env.get("RECIPIENT_EMAIL") || "<EMAIL>"

// Supabase-Client initialisieren
const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_ANON_KEY') ?? '',
  { global: { headers: { Authorization: Deno.env.get('SUPABASE_ANON_KEY') ?? '' } } }
)

Deno.serve(async (req) => {
  // CORS-Präflug-Anfragen behandeln
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Nur POST-Anfragen zulassen
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Diese Funktion unterstützt nur POST-Anfragen' 
      }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Anfragedaten verarbeiten
    const requestData = await req.json()
    const { name, email, subject, message } = requestData

    // Validierung der Eingaben
    if (!name || !email || !message) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Bitte fülle alle erforderlichen Felder aus' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // E-Mail-Format überprüfen
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Bitte gib eine gültige E-Mail-Adresse ein' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Nachricht in der Datenbank speichern
    const { data: savedMessage, error: dbError } = await supabaseClient
      .from('contact_messages')
      .insert([
        { 
          name, 
          email, 
          subject: subject || '(Kein Betreff)', 
          message 
        }
      ])
      .select()

    if (dbError) {
      console.error('Fehler beim Speichern der Nachricht:', dbError)
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Fehler beim Speichern deiner Nachricht' 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // SMTP-Konfiguration ist vorhanden, E-Mail senden
    if (smtpConfig.hostname && smtpConfig.username && smtpConfig.password) {
      try {
        const client = new SMTPClient(smtpConfig)

        // HTML-Body für die E-Mail
        const htmlBody = `
          <html>
            <head>
              <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #4a148c; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .message { background: #f9f9f9; padding: 15px; border-left: 4px solid #4a148c; margin: 20px 0; }
                .footer { font-size: 12px; color: #777; margin-top: 30px; }
              </style>
            </head>
            <body>
              <div class="container">
                <h1>Neue Kontaktanfrage von der Bewerbung KI Website</h1>
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>E-Mail:</strong> ${email}</p>
                <p><strong>Betreff:</strong> ${subject || '(Kein Betreff)'}</p>
                <div class="message">
                  <p><strong>Nachricht:</strong></p>
                  <p>${message.replace(/\n/g, '<br>')}</p>
                </div>
                <div class="footer">
                  <p>Diese Nachricht wurde über das Kontaktformular auf bewerbungki.de gesendet.</p>
                </div>
              </div>
            </body>
          </html>
        `

        // Text-Body als Fallback für E-Mail-Clients, die kein HTML unterstützen
        const textBody = `
Neue Kontaktanfrage von der Bewerbung KI Website

Name: ${name}
E-Mail: ${email}
Betreff: ${subject || '(Kein Betreff)'}

Nachricht:
${message}

Diese Nachricht wurde über das Kontaktformular auf bewerbungki.de gesendet.
        `

        await client.send({
          from: smtpConfig.username,
          to: RECIPIENT_EMAIL,
          subject: `Neue Kontaktanfrage: ${subject || 'Kontaktformular Anfrage'}`,
          html: htmlBody,
          text: textBody,
          replyTo: email,
        })

        await client.close()

        // Bestätigungs-E-Mail an den Absender
        const confirmationClient = new SMTPClient(smtpConfig)

        const confirmationHtmlBody = `
          <html>
            <head>
              <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #4a148c; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .message { background: #f9f9f9; padding: 15px; border-left: 4px solid #4a148c; margin: 20px 0; }
                .footer { font-size: 12px; color: #777; margin-top: 30px; }
              </style>
            </head>
            <body>
              <div class="container">
                <h1>Vielen Dank für deine Nachricht!</h1>
                <p>Hallo ${name},</p>
                <p>wir haben deine Kontaktanfrage erhalten und werden uns in Kürze bei dir melden.</p>
                <div class="message">
                  <p><strong>Deine Nachricht:</strong></p>
                  <p>${message.replace(/\n/g, '<br>')}</p>
                </div>
                <p>Unser Team wird deine Anfrage schnellstmöglich bearbeiten. In der Regel antworten wir innerhalb von 24 Stunden.</p>
                <p>Mit freundlichen Grüßen,<br>Das Bewerbung KI Team</p>
                <div class="footer">
                  <p>Dies ist eine automatische Bestätigung. Bitte antworte nicht auf diese E-Mail.</p>
                </div>
              </div>
            </body>
          </html>
        `

        const confirmationTextBody = `
Vielen Dank für deine Nachricht!

Hallo ${name},

wir haben deine Kontaktanfrage erhalten und werden uns in Kürze bei dir melden.

Deine Nachricht:
${message}

Unser Team wird deine Anfrage schnellstmöglich bearbeiten. In der Regel antworten wir innerhalb von 24 Stunden.

Mit freundlichen Grüßen,
Das Bewerbung KI Team

Dies ist eine automatische Bestätigung. Bitte antworte nicht auf diese E-Mail.
        `

        await confirmationClient.send({
          from: smtpConfig.username,
          to: email,
          subject: `Deine Anfrage bei Bewerbung KI wurde empfangen`,
          html: confirmationHtmlBody,
          text: confirmationTextBody,
        })

        await confirmationClient.close()

        // Status in der Datenbank aktualisieren
        await supabaseClient
          .from('contact_messages')
          .update({ status: 'sent', processed_at: new Date().toISOString() })
          .eq('id', savedMessage[0].id)

      } catch (emailError) {
        console.error('Fehler beim Senden der E-Mail:', emailError)
        
        // Status in der Datenbank aktualisieren
        await supabaseClient
          .from('contact_messages')
          .update({ 
            status: 'error', 
            error_message: `E-Mail-Fehler: ${emailError.message}`,
            processed_at: new Date().toISOString()
          })
          .eq('id', savedMessage[0].id)
        
        // E-Mail-Fehler sind nicht kritisch - wir haben die Nachricht in der Datenbank
        // Also geben wir trotzdem Erfolg zurück, mit einem Hinweis
        return new Response(JSON.stringify({ 
          success: true, 
          message: 'Deine Nachricht wurde empfangen, aber es gab ein Problem beim Versenden der E-Mail-Benachrichtigung. Wir werden uns trotzdem bei dir melden.' 
        }), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }
    } else {
      console.log('SMTP nicht konfiguriert - keine E-Mail gesendet')
      
      // Status in der Datenbank aktualisieren
      await supabaseClient
        .from('contact_messages')
        .update({ 
          status: 'pending', 
          error_message: 'SMTP nicht konfiguriert',
          processed_at: new Date().toISOString()
        })
        .eq('id', savedMessage[0].id)
    }

    // Erfolgreiche Antwort
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Vielen Dank! Deine Nachricht wurde erfolgreich übermittelt. Wir werden uns in Kürze bei dir melden.' 
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (err) {
    console.error('Unerwarteter Fehler:', err)
    
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuche es später erneut.' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}) 
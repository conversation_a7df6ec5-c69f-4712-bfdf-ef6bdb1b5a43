// supabase/functions/translate-with-deepseek/index.ts

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts' // CORS Helper

console.log('Function translate-with-deepseek starting...');

// Funktion zur Interaktion mit der Deepseek API für Übersetzungen
async function translateWithDeepseek(
  text: string,
  targetLang: string,
  sourceLang?: string,
  useStream: boolean = false
): Promise<object | { error: string } | ReadableStream<Uint8Array>> {
  console.log('translateWithDeepseek aufgerufen. Stream-Modus:', useStream);
  const deepseekApiKey = Deno.env.get('DEEPSEEK_API_KEY');

  if (!deepseekApiKey) {
    console.error('DEEPSEEK_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: Deepseek API Key fehlt.' };
  }

  try {
    // <PERSON><PERSON><PERSON> einen Prompt für die Übersetzung
    const systemPrompt = 'Du bist ein professioneller Übersetzer. Übersetze den Text präzise und behalte dabei den Stil, Ton und die Formatierung bei.';

    // Bestimme die Quell- und Zielsprache für den Prompt
    const sourceLanguageName = getLanguageName(sourceLang || 'auto');
    const targetLanguageName = getLanguageName(targetLang);

    let userPrompt = '';
    if (sourceLang && sourceLang !== 'auto') {
      userPrompt = `Übersetze den folgenden Text von ${sourceLanguageName} nach ${targetLanguageName}. Gib NUR den übersetzten Text zurück, ohne zusätzliche Erklärungen oder Kommentare:\n\n${text}`;
    } else {
      userPrompt = `Übersetze den folgenden Text nach ${targetLanguageName}. Erkenne automatisch die Quellsprache. Gib NUR den übersetzten Text zurück, ohne zusätzliche Erklärungen oder Kommentare:\n\n${text}`;
    }

    console.log(`Sende Übersetzungsanfrage an Deepseek API für Text (${text.length} Zeichen) nach ${targetLang}...`);

    // Erstelle den Request-Body für die Deepseek API
    // Berechne eine angemessene Anzahl von Tokens, aber beschränke auf den gültigen Bereich (1-8192)
    const estimatedTokens = Math.min(8000, Math.max(1000, Math.min(text.length * 2, 8000)));

    const payload = {
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.1, // Niedrige Temperatur für konsistente Übersetzungen
      max_tokens: estimatedTokens, // Genug Tokens für die Übersetzung, aber im gültigen Bereich
      stream: useStream // Stream-Modus basierend auf Parameter
    };

    // Sende die Anfrage an die Deepseek API
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekApiKey}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Deepseek API Fehler (${response.status}): ${errorText}`);
      return {
        error: `Fehler bei der Übersetzung (${response.status}): ${errorText}`
      };
    }

    // Wenn Stream-Modus aktiviert ist, gib den Stream direkt zurück
    if (useStream) {
      console.log('Stream-Modus: Gebe Deepseek API Stream zurück');

      // Transformiere den Stream, um die Daten zu verarbeiten
      const transformStream = new TransformStream({
        start(controller) {
          // Sende eine initiale Nachricht mit Metadaten
          const initialMessage = {
            type: 'meta',
            detectedSourceLanguage: sourceLang || detectLanguage(text),
            targetLanguage: targetLang,
            timestamp: new Date().toISOString(),
            status: 'started'
          };
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(initialMessage)}\n\n`));
        },
        async transform(chunk, controller) {
          // Verarbeite die Chunk-Daten vom Deepseek-Stream
          const text = new TextDecoder().decode(chunk);

          // Versuche, die Zeilen zu parsen (Format: "data: {...}")
          const lines = text.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                // Extrahiere den JSON-Teil
                const jsonStr = line.substring(6);
                if (jsonStr === '[DONE]') {
                  // Stream ist beendet
                  const doneMessage = {
                    type: 'meta',
                    status: 'done'
                  };
                  controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(doneMessage)}\n\n`));
                  continue;
                }

                const data = JSON.parse(jsonStr);
                if (data.choices && data.choices.length > 0) {
                  const delta = data.choices[0].delta;
                  if (delta && delta.content) {
                    // Sende den Textinhalt als Chunk
                    const contentMessage = {
                      type: 'content',
                      content: delta.content
                    };
                    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(contentMessage)}\n\n`));
                  }
                }
              } catch (e) {
                console.error('Fehler beim Parsen des Stream-Chunks:', e);
                // Sende den Chunk trotzdem weiter, falls es kein JSON ist
                controller.enqueue(chunk);
              }
            }
          }
        }
      });

      // Verbinde den Response-Stream mit unserem TransformStream
      return response.body!.pipeThrough(transformStream);
    }

    // Nicht-Stream-Modus: Verarbeite die Antwort wie bisher
    const data = await response.json();
    console.log('Deepseek API Antwort erhalten:', JSON.stringify(data).substring(0, 200) + '...');

    // Extrahiere den übersetzten Text aus der Antwort
    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
      const translatedText = data.choices[0].message.content.trim();

      // Versuche, die Quellsprache zu erkennen (falls nicht angegeben)
      let detectedSourceLanguage = sourceLang || 'auto';
      if (detectedSourceLanguage === 'auto') {
        // Hier könnten wir eine einfache Spracherkennung implementieren
        // Für jetzt verwenden wir einen Platzhalter
        detectedSourceLanguage = detectLanguage(text);
      }

      return {
        translatedText: translatedText,
        detectedSourceLanguage: detectedSourceLanguage,
        // Zusätzliche Metadaten
        model: data.model,
        timestamp: new Date().toISOString()
      };
    } else {
      return { error: 'Keine Übersetzung in der API-Antwort gefunden.' };
    }
  } catch (error) {
    console.error('Fehler bei der Anfrage an die Deepseek API:', error);
    return { error: `Fehler bei der Kommunikation mit der Übersetzungs-API: ${error.message}` };
  }
}

// Einfache Funktion zur Spracherkennung (Platzhalter)
function detectLanguage(text: string): string {
  // Hier könnte eine komplexere Spracherkennung implementiert werden
  // Für jetzt verwenden wir eine sehr einfache Heuristik

  // Häufige deutsche Wörter
  const germanWords = ['der', 'die', 'das', 'und', 'ist', 'in', 'zu', 'den', 'mit', 'für', 'auf', 'dem', 'nicht', 'ein', 'eine', 'sich', 'von', 'des', 'aus'];

  // Häufige englische Wörter
  const englishWords = ['the', 'and', 'to', 'of', 'in', 'is', 'for', 'with', 'on', 'that', 'by', 'this', 'are', 'not', 'from', 'or', 'an', 'as', 'at'];

  // Zähle Wörter
  const words = text.toLowerCase().split(/\s+/);
  let germanCount = 0;
  let englishCount = 0;

  for (const word of words) {
    if (germanWords.includes(word)) germanCount++;
    if (englishWords.includes(word)) englishCount++;
  }

  // Bestimme die wahrscheinlichste Sprache
  if (germanCount > englishCount) return 'de';
  if (englishCount > germanCount) return 'en';

  // Fallback
  return 'en';
}

// Hilfsfunktion zum Konvertieren von Sprachcodes in Sprachnamen
function getLanguageName(langCode: string): string {
  const languages: Record<string, string> = {
    'de': 'Deutsch',
    'en': 'Englisch',
    'fr': 'Französisch',
    'es': 'Spanisch',
    'it': 'Italienisch',
    'zh': 'Chinesisch',
    'ru': 'Russisch',
    'ar': 'Arabisch',
    'pt': 'Portugiesisch',
    'hi': 'Hindi',
    'ja': 'Japanisch',
    'ko': 'Koreanisch',
    'tr': 'Türkisch',
    'nl': 'Niederländisch',
    'pl': 'Polnisch',
    'sv': 'Schwedisch',
    'auto': 'automatisch erkannte Sprache'
  };

  return languages[langCode] || langCode;
}

// Hauptfunktion zum Verarbeiten der HTTP-Anfragen
serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Nur POST-Anfragen erlauben
    if (req.method !== 'POST') {
      console.log(`[Serve] Methode ${req.method} nicht erlaubt.`);
      return new Response(
        JSON.stringify({ error: 'Nur POST-Anfragen sind erlaubt.' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Request-Body parsen
    const requestData = await req.json();
    console.log('[Serve] Request-Daten empfangen:', JSON.stringify(requestData).substring(0, 200) + '...');

    // Validiere die Eingabedaten
    if (!requestData.text) {
      console.log('[Serve] Fehlende Eingabedaten: text');
      return new Response(
        JSON.stringify({ error: 'Der zu übersetzende Text muss angegeben werden.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Standardwerte für Ziel- und Quellsprache
    const targetLang = requestData.targetLang || 'de';
    const sourceLang = requestData.sourceLang || undefined; // Optional
    const useStream = requestData.stream === true; // Stream-Modus aktivieren, wenn explizit angefordert

    // Text übersetzen
    const result = await translateWithDeepseek(requestData.text, targetLang, sourceLang, useStream);

    // Wenn es ein Stream ist, gib ihn direkt zurück
    if (result instanceof ReadableStream) {
      console.log('[Serve] Stream-Antwort wird zurückgegeben.');
      return new Response(result, {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      });
    }

    // Prüfe auf Fehler
    if ('error' in result) {
      console.error('[Serve] Fehler bei der Übersetzung:', result.error);
      return new Response(
        JSON.stringify({ error: result.error }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Erfolgreiche Antwort (nicht-Stream)
    console.log('[Serve] Text erfolgreich übersetzt.');
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('[Serve] Unerwarteter Fehler:', error);
    return new Response(
      JSON.stringify({ error: `Unerwarteter Fehler: ${error.message}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

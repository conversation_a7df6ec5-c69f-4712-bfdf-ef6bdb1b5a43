// supabase/functions/generate-job-keywords/index.ts

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts' // CORS Helper
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

console.log('Function generate-job-keywords starting...');

// Funktion zur Interaktion mit Deepseek API
async function generateJobKeywords(profileData: any): Promise<object | { error: string }> {
  console.log('generateJobKeywords aufgerufen.');
  const deepseekApiKey = Deno.env.get('DEEPSEEK_API_KEY');
  const modelName = Deno.env.get('DEEPSEEK_MODEL') || 'deepseek-chat'; // Standardmodell

  if (!deepseekApiKey) {
    console.error('DEEPSEEK_API_KEY ist nicht gesetzt.');
    return { error: 'Serverkonfigurationsfehler: Deepseek API Key fehlt.' };
  }

  try {
    // Erstelle einen optimierten Prompt für die Analyse
    const prompt = createAnalysisPrompt(profileData);

    // Sende Anfrage an Deepseek API
    console.log('Sende Anfrage an Deepseek API...');
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekApiKey}`,
      },
      body: JSON.stringify({
        model: modelName,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3, // Niedrigere Temperatur für konsistentere Ergebnisse
        max_tokens: 1024,
        response_format: { type: "json_object" }, // Stellt sicher, dass Deepseek JSON zurückgibt
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Fehler bei der Anfrage an die Deepseek API:', errorData);
      return { error: `Fehler bei der Kommunikation mit der KI: ${response.status} ${response.statusText}` };
    }

    const data = await response.json();
    console.log('Antwort von Deepseek erhalten.');

    // Extrahiere den Antworttext
    if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
      console.error('Unerwartetes Antwortformat von Deepseek:', data);
      return { error: 'Die KI hat keine gültige Antwort zurückgegeben.' };
    }

    const content = data.choices[0].message.content;
    console.log('Extrahierter Inhalt:', content);

    // Parse JSON aus der Antwort
    try {
      const parsedData = JSON.parse(content);
      console.log('JSON erfolgreich geparsed:', parsedData);
      return parsedData;
    } catch (parseError) {
      console.error('Fehler beim Parsen des JSON aus der Deepseek-Antwort:', parseError);
      console.error('Empfangener String:', content);
      return { error: `Fehler beim Verarbeiten der KI-Antwort (JSON ungültig): ${parseError.message}` };
    }
  } catch (error) {
    console.error('Fehler bei der Anfrage an die Deepseek API:', error);
    return { error: `Fehler bei der Kommunikation mit der KI: ${error.message}` };
  }
}

// Erstellt einen optimierten Prompt für die Analyse
function createAnalysisPrompt(profileData: any): string {
  // Extrahiere relevante Daten aus dem Profil
  const skills = profileData.skills || [];
  const workExperience = profileData.workExperience || [];
  const education = profileData.education || [];
  const experienceSummary = profileData.experienceSummary || '';

  // Formatiere die Berufserfahrung
  let experienceText = '';
  if (workExperience.length > 0) {
    experienceText = 'Berufserfahrung:\n';
    workExperience.forEach((exp: any) => {
      const startDate = exp.startDate ? new Date(exp.startDate).getFullYear() : 'Unbekannt';
      const endDate = exp.endDate ? new Date(exp.endDate).getFullYear() : 'Heute';
      experienceText += `- ${exp.position} bei ${exp.company} (${startDate} - ${endDate})\n`;
      if (exp.description) {
        experienceText += `  ${exp.description}\n`;
      }
    });
  }

  // Formatiere die Ausbildung
  let educationText = '';
  if (education.length > 0) {
    educationText = 'Ausbildung:\n';
    education.forEach((edu: any) => {
      const startDate = edu.startDate ? new Date(edu.startDate).getFullYear() : 'Unbekannt';
      const endDate = edu.endDate ? new Date(edu.endDate).getFullYear() : 'Heute';
      educationText += `- ${edu.degree} in ${edu.fieldOfStudy} an ${edu.institution} (${startDate} - ${endDate})\n`;
    });
  }

  // Formatiere die Fähigkeiten
  const skillsText = skills.length > 0 ? `Fähigkeiten: ${skills.join(', ')}\n` : '';

  // Erstelle den Prompt
  return `
Analysiere die folgenden Profildaten einer Person und generiere eine Liste von relevanten Berufsbezeichnungen als Schlüsselwörter, die für die Jobsuche verwendet werden können.

${skillsText}
${experienceText}
${educationText}
${experienceSummary ? `Zusammenfassung der Erfahrung: ${experienceSummary}\n` : ''}

Basierend auf diesen Informationen, generiere eine Liste von Berufsbezeichnungen, die zu diesem Profil passen könnten.
Berücksichtige dabei sowohl die aktuelle Position als auch mögliche verwandte Berufe, für die die Person qualifiziert sein könnte.

Gib das Ergebnis als JSON-Objekt mit dem folgenden Format zurück:
{
  "jobKeywords": [
    "Berufsbezeichnung1",
    "Berufsbezeichnung2",
    ...
  ]
}

Wichtige Regeln:
1. Gib nur deutsche Berufsbezeichnungen zurück
2. Beschränke die Liste auf maximal 15 relevante Berufsbezeichnungen
3. Sortiere die Berufsbezeichnungen nach Relevanz (die relevantesten zuerst)
4. Berücksichtige sowohl die aktuelle Position als auch verwandte Berufe
5. Berücksichtige auch Berufsbezeichnungen für Quereinsteiger, wenn die Fähigkeiten passen
6. Gib NUR das JSON-Objekt zurück, ohne zusätzlichen Text
`;
}

serve(async (req) => {
  console.log(`[Serve] ${req.method} Anfrage empfangen für ${req.url}`);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('[Serve] Beantworte OPTIONS Anfrage.');
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Nur POST-Anfragen akzeptieren
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Nur POST-Anfragen werden unterstützt' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Request-Body parsen
    const requestData = await req.json();
    console.log('[Serve] Request-Daten empfangen:', JSON.stringify(requestData).substring(0, 200) + '...');

    // Validiere Eingabedaten
    if (!requestData || !requestData.profileData || !requestData.userId) {
      return new Response(
        JSON.stringify({ error: 'Fehlende Profildaten oder userId in der Anfrage' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Überprüfe den Premium-Status des Benutzers
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Überprüfe, ob der Benutzer Premium-Zugriff hat oder temporären Zugriff auf das Feature
    console.log('[Serve] Überprüfe Premium-Status für Benutzer:', requestData.userId);
    const { data: premiumStatus, error: premiumError } = await supabaseClient.functions.invoke(
      'check-premium-status',
      {
        body: { userId: requestData.userId, feature: 'aiJobSearch' },
      }
    );

    if (premiumError) {
      console.error('[Serve] Fehler bei der Überprüfung des Premium-Status:', premiumError);
      return new Response(
        JSON.stringify({ error: 'Fehler bei der Überprüfung des Premium-Status' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Wenn der Benutzer keinen Zugriff hat, gib einen Fehler zurück
    if (!premiumStatus.hasAccess) {
      console.log(`[Serve] Benutzer ${requestData.userId} hat keinen Zugriff auf aiJobSearch`);
      return new Response(
        JSON.stringify({
          error: 'Premium-Zugriff erforderlich',
          isPremium: premiumStatus.isPremium,
          requiresUpgrade: true
        }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Generiere Berufsbezeichnungen
    const result = await generateJobKeywords(requestData.profileData);

    // Prüfe auf Fehler
    if ('error' in result) {
      console.error('[Serve] Fehler bei der Generierung von Berufsbezeichnungen:', result.error);
      return new Response(
        JSON.stringify({ error: result.error }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Erfolgreiche Antwort
    console.log('[Serve] Berufsbezeichnungen erfolgreich generiert:', JSON.stringify(result).substring(0, 200) + '...');
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('[Serve] Unerwarteter Fehler:', error);
    return new Response(
      JSON.stringify({ error: `Unerwarteter Fehler: ${error.message}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

/*
// Example Usage from Flutter (using Supabase client):
try {
  final response = await supabase.functions.invoke(
    'generate-job-keywords',
    body: {'profileData': userProfile.toJson()}, // Send profile data in the body
  );

  if (response.error != null) {
    print('Supabase function error: ${response.error!.message}');
    // Handle error
  } else {
    print('Supabase function success: ${response.data}');
    // Process the job keywords (response.data['jobKeywords'])
    final jobKeywords = (response.data['jobKeywords'] as List<dynamic>).cast<String>();
    // Update profile state...
  }
} catch (e) {
  print('Error invoking Supabase function: $e');
  // Handle error
}
*/

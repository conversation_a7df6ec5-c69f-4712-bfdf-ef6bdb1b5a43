package com.einsteinai.app

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log
import androidx.core.content.FileProvider
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.File

class FileProviderPlugin : FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private val TAG = "FileProviderPlugin"

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.einsteinai.app/file_provider")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "openPdfWithProvider" -> {
                val filePath = call.argument<String>("filePath")
                if (filePath != null) {
                    val success = openPdfWithProvider(filePath)
                    result.success(success)
                } else {
                    result.error("INVALID_ARGUMENT", "Dateipfad fehlt", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun openPdfWithProvider(filePath: String): Boolean {
        return try {
            Log.d(TAG, "Versuche Datei zu öffnen: $filePath")

            val file = File(filePath)
            if (!file.exists()) {
                Log.e(TAG, "Datei existiert nicht: $filePath")
                return false
            }

            Log.d(TAG, "Datei existiert, Größe: ${file.length()} Bytes")

            // Prüfe, ob die Datei eine .enc-Endung hat (verschlüsselt)
            if (filePath.endsWith(".enc")) {
                Log.w(TAG, "Verschlüsselte Datei kann nicht direkt geöffnet werden: $filePath")
                return false
            }

            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            Log.d(TAG, "FileProvider URI erstellt: $uri")

            // Erstelle den Intent zum Öffnen der PDF
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "application/pdf")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // Prüfe, ob es eine App gibt, die den Intent behandeln kann
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)

            if (activities.isEmpty()) {
                Log.e(TAG, "Keine App gefunden, die PDFs öffnen kann")

                // Versuche einen generischen Intent
                val genericIntent = Intent(Intent.ACTION_VIEW).apply {
                    data = uri
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                val genericActivities = packageManager.queryIntentActivities(genericIntent, PackageManager.MATCH_DEFAULT_ONLY)
                if (genericActivities.isEmpty()) {
                    Log.e(TAG, "Auch keine App für generischen Intent gefunden")
                    return false
                }

                Log.d(TAG, "Starte generischen Intent")
                context.startActivity(genericIntent)
                return true
            }

            // Logge alle gefundenen Apps
            activities.forEach {
                Log.d(TAG, "App gefunden: ${it.activityInfo.packageName}")
            }

            Log.d(TAG, "Starte Intent zum Öffnen der PDF")
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Fehler beim Öffnen der Datei", e)
            e.printStackTrace()
            false
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}

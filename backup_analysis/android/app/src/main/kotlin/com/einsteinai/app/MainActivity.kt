package com.einsteinai.app

import android.os.Bundle
import android.view.WindowManager
import android.view.View
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.os.Build
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.util.Log

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.einsteinai.app/system_ui"
    private lateinit var emailMethodChannel: EmailMethodChannel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.i("MainActivity", "onCreate aufgerufen")

        try {
            // WICHTIG: Wir wollen die Navigationsleiste während der normalen App-Nutzung sichtbar lassen
            // Daher setzen wir nur die Flags, die für die App-Darstellung wichtig sind, aber nicht die Navigationsleiste verstecken
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            )

            // Setze die Hintergrundfarbe der Navigationsleiste
            window.navigationBarColor = Color.BLACK

            // Setze die Statusleiste auf transparent
            window.statusBarColor = Color.TRANSPARENT

            // Setze einen Listener für Systemleisten-Änderungen
            window.decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                Log.i("MainActivity", "SystemUiVisibility geändert: $visibility")
            }

            Log.i("MainActivity", "onCreate erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in onCreate: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        Log.i("MainActivity", "configureFlutterEngine aufgerufen")

        try {
            // Registriere den FileProviderPlugin
            flutterEngine.plugins.add(FileProviderPlugin())

            // Registriere den E-Mail-MethodChannel
            emailMethodChannel = EmailMethodChannel(this)
            emailMethodChannel.configureFlutterEngine(flutterEngine)

            // Registriere den MethodChannel für die Steuerung der Navigationsleiste
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
                Log.i("MainActivity", "MethodChannel aufgerufen: ${call.method}")

                when (call.method) {
                    "hideNavigationBar" -> {
                        Log.i("MainActivity", "hideNavigationBar Methode aufgerufen")
                        hideSystemUI()
                        result.success(true)
                    }
                    "showNavigationBar" -> {
                        Log.i("MainActivity", "showNavigationBar Methode aufgerufen")
                        showSystemUI()
                        result.success(true)
                    }
                    else -> {
                        Log.w("MainActivity", "Nicht implementierte Methode: ${call.method}")
                        result.notImplemented()
                    }
                }
            }

            // Registriere den MethodChannel für Werbeanzeigen
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.einsteinai.app/ads").setMethodCallHandler { call, result ->
                Log.i("MainActivity", "Ads MethodChannel aufgerufen: ${call.method}")

                when (call.method) {
                    "setImmersiveModeForAds" -> {
                        Log.i("MainActivity", "setImmersiveModeForAds Methode aufgerufen")
                        val enabled = call.argument<Boolean>("enabled") ?: false
                        setImmersiveModeForAds(enabled)
                        result.success(true)
                    }
                    else -> {
                        Log.w("MainActivity", "Nicht implementierte Ads-Methode: ${call.method}")
                        result.notImplemented()
                    }
                }
            }

            Log.i("MainActivity", "configureFlutterEngine erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in configureFlutterEngine: ${e.message}")
            e.printStackTrace()
        }
    }

    // Wird aufgerufen, wenn die App in den Vordergrund kommt
    override fun onResume() {
        super.onResume()
        Log.i("MainActivity", "onResume aufgerufen")
    }

    // Wird aufgerufen, wenn die App in den Hintergrund geht
    override fun onPause() {
        super.onPause()
        Log.i("MainActivity", "onPause aufgerufen")
    }

    // Methode zum Verstecken der Systemleisten (nur Statusleiste, NICHT Navigationsleiste)
    private fun hideSystemUI() {
        Log.i("MainActivity", "hideSystemUI aufgerufen - ABER NAVIGATIONSLEISTE BLEIBT SICHTBAR")

        runOnUiThread {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")

                    // Für Android 11+ (API 30+) verwenden wir die neue API
                    window.setDecorFitsSystemWindows(true) // Wichtig für normalen Modus
                    window.insetsController?.let {
                        // Verstecke NUR die Statusleiste, NICHT die Navigationsleiste
                        it.hide(WindowInsets.Type.statusBars())
                        // Setze das Verhalten auf normal
                        it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_BARS_BY_TOUCH
                    }

                    // Setze die Hintergrundfarbe der Navigationsleiste auf schwarz (sichtbar)
                    window.navigationBarColor = Color.BLACK

                    // Setze die Statusleiste auf transparent
                    window.statusBarColor = Color.TRANSPARENT

                    // FLAG_KEEP_SCREEN_ON kann nützlich sein
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                    // KEINE Flags für Vollbildmodus setzen
                    window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)

                } else {
                    Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")

                    // Für ältere Android-Versionen verwenden wir die alte API
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = (
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    )

                    // FLAG_KEEP_SCREEN_ON kann nützlich sein
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                    // KEINE Flags für Vollbildmodus setzen
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                }

                // KEIN Listener für SystemUiVisibility-Änderungen, da wir die Navigationsleiste immer sichtbar halten wollen

                Log.i("MainActivity", "hideSystemUI erfolgreich abgeschlossen")
            } catch (e: Exception) {
                Log.e("MainActivity", "Fehler in hideSystemUI: ${e.message}", e)
            }
        }
    }

    // Methode zum Anzeigen der Systemleisten
    private fun showSystemUI() {
        Log.i("MainActivity", "showSystemUI aufgerufen")

        runOnUiThread {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")

                    // Für Android 11+ (API 30+) verwenden wir die neue API
                    window.setDecorFitsSystemWindows(true)
                    window.insetsController?.let {
                        // Zeige sowohl Navigationsleiste als auch Statusleiste
                        it.show(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                        // Setze das Verhalten zurück auf normal
                        it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_BARS_BY_TOUCH
                    }

                    // Setze die Farben zurück
                    window.navigationBarColor = Color.BLACK
                    window.statusBarColor = Color.TRANSPARENT

                    // Entferne FLAG_KEEP_SCREEN_ON
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                    // Entferne zusätzliche Flags für Vollbildmodus
                    window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)

                } else {
                    Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")

                    // Für ältere Android-Versionen verwenden wir die alte API
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = (
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    )

                    // Entferne FLAG_KEEP_SCREEN_ON
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                }

                Log.i("MainActivity", "showSystemUI erfolgreich abgeschlossen")
            } catch (e: Exception) {
                Log.e("MainActivity", "Fehler in showSystemUI: ${e.message}", e)
            }
        }
    }

    // Methode zum Setzen des Immersive Mode für Werbeanzeigen
    private fun setImmersiveModeForAds(enabled: Boolean) {
        Log.i("MainActivity", "setImmersiveModeForAds aufgerufen: $enabled")
        
        // Der Immersive Mode wird am besten direkt im AndroidManifest.xml konfiguriert.
        // Diese Methode dient nur zur Bestätigung und zum Logging.
        // Die Reflection-basierte Methode ist fehleranfällig und wird entfernt.
        Log.i("MainActivity", "Immersive Mode für Werbeanzeigen ist im Manifest konfiguriert: $enabled")
        
        // Optional: Zusätzliche UI-Anpassungen für Werbeanzeigen
        if (enabled) {
            Log.i("MainActivity", "Immersive Mode für Werbeanzeigen aktiviert")
        } else {
            Log.i("MainActivity", "Immersive Mode für Werbeanzeigen deaktiviert")
        }
    }
}
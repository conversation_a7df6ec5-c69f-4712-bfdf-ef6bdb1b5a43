package com.bewerbungki.app

import android.os.Bundle
import android.view.WindowManager
import android.view.View
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.os.Build
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.util.Log

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.bewerbungki.app/system_ui"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.i("MainActivity", "onCreate aufgerufen")

        try {
            // WICHTIG: Wir wollen die Navigationsleiste während der normalen App-Nutzung sichtbar lassen
            // Daher setzen wir nur die Flags, die für die App-Darstellung wichtig sind, aber nicht die Navigationsleiste verstecken
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            )

            // Setze die Hintergrundfarbe der Navigationsleiste
            window.navigationBarColor = Color.BLACK

            // Setze die Statusleiste auf transparent
            window.statusBarColor = Color.TRANSPARENT

            // Setze einen Listener für Systemleisten-Änderungen
            window.decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                Log.i("MainActivity", "SystemUiVisibility geändert: $visibility")
            }

            Log.i("MainActivity", "onCreate erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in onCreate: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        Log.i("MainActivity", "configureFlutterEngine aufgerufen")
        
        try {
            // System UI MethodChannel
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
                Log.i("MainActivity", "MethodChannel aufgerufen: ${call.method}")
                
                when (call.method) {
                    "hideNavigationBar" -> {
                        Log.i("MainActivity", "hideNavigationBar Methode aufgerufen")
                        hideSystemUI()
                        result.success(null)
                    }
                    "showNavigationBar" -> {
                        Log.i("MainActivity", "showNavigationBar Methode aufgerufen")
                        showSystemUI()
                        result.success(null)
                    }
                    else -> {
                        Log.w("MainActivity", "Nicht implementierte Methode: ${call.method}")
                        result.notImplemented()
                    }
                }
            }
            
            Log.i("MainActivity", "configureFlutterEngine erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in configureFlutterEngine: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.i("MainActivity", "onResume aufgerufen")
    }

    override fun onPause() {
        super.onPause()
        Log.i("MainActivity", "onPause aufgerufen")
    }

    private fun hideSystemUI() {
        Log.i("MainActivity", "hideSystemUI aufgerufen - ABER NAVIGATIONSLEISTE BLEIBT SICHTBAR")
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")
                
                val controller = window.insetsController
                if (controller != null) {
                    // Verstecke nur die Statusleiste, nicht die Navigationsleiste
                    controller.hide(WindowInsets.Type.statusBars())
                    controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                }
                
                // Setze zusätzliche Flags für bessere Immersion
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                )
                
                // Setze die Statusleiste auf transparent
                window.statusBarColor = Color.TRANSPARENT
                
                // Behalte die Navigationsleiste sichtbar mit schwarzer Farbe
                window.navigationBarColor = Color.BLACK
                
            } else {
                Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")
                
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_FULLSCREEN // Verstecke nur die Statusleiste
                    // NICHT: View.SYSTEM_UI_FLAG_HIDE_NAVIGATION - damit bleibt die Navigationsleiste sichtbar
                )
                
                // Setze die Farben
                window.statusBarColor = Color.TRANSPARENT
                window.navigationBarColor = Color.BLACK
            }
            
            Log.i("MainActivity", "hideSystemUI erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in hideSystemUI: ${e.message}", e)
        }
    }

    private fun showSystemUI() {
        Log.i("MainActivity", "showSystemUI aufgerufen")
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")
                
                val controller = window.insetsController
                if (controller != null) {
                    // Zeige alle System-Bars
                    controller.show(WindowInsets.Type.systemBars())
                    controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_BARS_BY_TOUCH
                }
                
                // Entferne die NO_LIMITS Flags
                window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                
                // Setze normale Farben
                window.statusBarColor = Color.TRANSPARENT
                window.navigationBarColor = Color.BLACK
                
            } else {
                Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")
                
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
                
                // Setze normale Farben
                window.statusBarColor = Color.TRANSPARENT
                window.navigationBarColor = Color.BLACK
            }
            
            Log.i("MainActivity", "showSystemUI erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in showSystemUI: ${e.message}", e)
        }
    }
}
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Standortberechtigungen -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Optional: Hintergrund-Standort (nur wenn wirklich nötig!) -->
    <!-- <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <application
        android:label="ki_test"
        android:icon="@mipmap/ic_launcher"
        android:usesCleartextTraffic="false"
        android:networkSecurityConfig="@xml/network_security_config">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            
            android:immersive="true"
            android:enableOnBackInvokedCallback="true"
            android:screenOrientation="portrait">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <!-- Intent filter für Firebase E-Mail-Link Anmeldung (Wieder aktiviert für app_links) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <!-- Akzeptiere Links, die mit deiner Firebase Hosting Domain beginnen -->
                <data android:host="ai-job-assistent.web.app" android:scheme="https"/>
            </intent-filter>

            <!-- Supabase OAuth Callback Intent Filter -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Akzeptiert Deeplinks mit deinem App-Schema -->
                <data android:scheme="com.einsteinai.app" android:host="login-callback" />
            </intent-filter>

            <!-- Zusätzlicher Intent Filter für Google OAuth -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="com.einsteinai.app" />
            </intent-filter>

        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
         <meta-data
             android:name="flutterEmbedding"
             android:value="2" />

         <!-- Füge deine AdMob App ID hier ein -->
         <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-5967659832492607~5579319641"/>

         <!-- Theme-Überschreibungen für Google Mobile Ads Activities -->
         <meta-data
            android:name="com.google.android.gms.ads.ACTIVITY_THEME"
            android:resource="@style/AdTheme" />

         <!-- Aktiviere Immersive Mode für Werbeanzeigen -->
         <meta-data
            android:name="com.google.android.gms.ads.IMMERSIVE_MODE"
            android:value="true" />

         <!-- Google Play Services Optimierungen -->
         <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
         
         <!-- Deaktiviere Phenotype für bessere Performance -->
         <meta-data
            android:name="com.google.android.gms.phenotype.service.ENABLED"
            android:value="false" />

        <!-- FileProvider für das sichere Teilen von Dateien -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- ProfileInstaller deaktivieren für schnelleren App-Start -->
        <provider
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:authorities="${applicationId}.profileinstaller"
            android:enabled="false"
            android:exported="false" />
     </application>
     <!-- Required to query activities that can process text or send emails, see:
          https://developer.android.com/training/package-visibility,
          https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT, and
          https://developer.android.com/reference/android/content/Intent#ACTION_SENDTO.

          PROCESS_TEXT is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
          SENDTO is required by flutter_email_sender to find email clients. -->
     <queries>
         <intent>
             <action android:name="android.intent.action.PROCESS_TEXT"/>
             <data android:mimeType="text/plain"/>
         </intent>
         <!-- NEU: Erlaube die Abfrage von E-Mail-Apps -->
         <intent>
            <action android:name="android.intent.action.SENDTO"/>
            <data android:scheme="mailto"/>
         </intent>
     </queries>
 </manifest>

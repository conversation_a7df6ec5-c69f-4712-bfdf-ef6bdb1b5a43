<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Basis-Konfiguration für alle Domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </base-config>

    <!-- Spezifische Domains mit erhöhter Sicherheit -->
    <domain-config cleartextTrafficPermitted="false">
        <!-- Supabase -->
        <domain includeSubdomains="true">vpttdxibvjrfjzbtktqg.supabase.co</domain>
        <!-- DeepSeek API -->
        <domain includeSubdomains="true">api.deepseek.com</domain>
        <!-- Agentur für Arbeit API -->
        <domain includeSubdomains="true">rest.arbeitsagentur.de</domain>
        <!-- Andere APIs -->
        <domain includeSubdomains="true">api.groq.com</domain>
        <domain includeSubdomains="true">api.openai.com</domain>
        <domain includeSubdomains="true">api.mistral.ai</domain>

        <!-- TLS-Einstellungen für erhöhte Sicherheit -->
        <!-- Erfordert mindestens TLS 1.2 -->
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </domain-config>

    <!-- Debug-Konfiguration für lokale Entwicklung (nur im Debug-Build aktiv) -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>
</network-security-config>
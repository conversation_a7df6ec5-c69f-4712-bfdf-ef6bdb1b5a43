<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Erlaubt den Zugriff auf Dateien im internen App-Speicher -->
    <files-path name="files" path="." />
    <cache-path name="cache" path="." />
    <!-- Erlaubt den Zugriff auf Dateien im Flutter-Speicherbereich -->
    <files-path name="flutter_files" path="../app_flutter/" />
    <!-- Spezifisch für den CV-Speicherort -->
    <files-path name="cv_storage" path="../app_flutter/cv_storage/" />
    <!-- <PERSON><PERSON><PERSON> Speicherbereich für verschlüsselte Dateien -->
    <files-path name="secure_files" path="../app_flutter/secure_files/" />
    <files-path name="secure_cv" path="../app_flutter/secure_files/secure_cv/" />
    <!-- Temporärer Speicher für entschlüsselte Dateien -->
    <cache-path name="temp" path="." />
    <!-- Externer Speicher -->
    <external-path name="external" path="." />
    <external-files-path name="external_files" path="." />
    <external-cache-path name="external_cache" path="." />
</paths>

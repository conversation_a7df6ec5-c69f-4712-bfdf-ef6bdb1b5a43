<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Globale Theme-Überschreibungen für alle Activities -->
    <style name="Theme.App.Fullscreen" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
    
    <!-- Überschreibung für Google Mobile Ads Activities -->
    <style name="Theme.GoogleMobileAds" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
    
    <!-- Überschreibung für Google Mobile Ads Fullscreen Activities -->
    <style name="Theme.GoogleMobileAds.FullscreenActivity" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
</resources>

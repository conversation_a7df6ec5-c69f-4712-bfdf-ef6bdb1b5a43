plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.google.gms.google-services' // Wieder aktiviert für Google Sign-In
    id 'dev.flutter.flutter-gradle-plugin'
}

// Firebase Plugins entfernt

def kotlin_version = '2.0.0'  // Aktualisiert auf 2.0.0

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new RuntimeException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Wir verwenden jetzt den deklarativen plugin-Block statt apply
// apply plugin: 'com.android.application'
// apply plugin: 'kotlin-android'
// apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    namespace "com.bewerbungki.app"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility rootProject.ext.java_version
        targetCompatibility rootProject.ext.java_version
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += ["-Xsuppress-version-warnings"]
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.einsteinai.app"
        minSdkVersion 23  // Auf 23 erhöht für Firebase Auth
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Multidex aktivieren
        multiDexEnabled true

        // Renderscript Optimierungen
        renderscriptTargetApi 21
        renderscriptSupportModeEnabled true

        manifestPlaceholders = [
            'appAuthRedirectScheme': 'com.bewerbungki.app'
        ]
    }

    buildTypes {
        release {
            // Optimierungen aktiviert für Release-Builds
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Zusätzliche Optimierungen für Release-Builds
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            pseudoLocalesEnabled false
            zipAlignEnabled true

            // ABI Filter auskommentiert für Split-APK-Kompatibilität
            // ndk {
            //     abiFilters "armeabi-v7a", "arm64-v8a"
            // }
        }

        debug {
            // Debug-Optimierungen
            crunchPngs false
            minifyEnabled false
            shrinkResources false
            debuggable true
        }
    }

    // Dex-Optionen für schnelleres Debugging
    dexOptions {
        preDexLibraries true
        maxProcessCount 8
        javaMaxHeapSize "2g"
    }
}

// Flutter wird jetzt durch das Flutter-Plugin gesteuert
// flutter {
//     source '../..'
// }

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation "androidx.multidex:multidex:2.0.1"

    // Firebase Abhängigkeiten entfernt
    // implementation platform('com.google.firebase:firebase-bom:32.7.2')
    // implementation 'com.google.firebase:firebase-analytics'
    // implementation 'com.google.firebase:firebase-auth'
    // implementation 'com.google.android.recaptcha:recaptcha:18.4.0' // Falls ReCaptcha benötigt wird

    // Play Services Dependencies
    implementation 'com.google.android.gms:play-services-base:18.3.0'
    implementation 'com.google.android.gms:play-services-auth:21.0.0'
}

# Flutter-spezifische ProGuard-Regeln
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.plugin.editing.** { *; }

# Kotlin-spezifische Regeln
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# Supabase-spezifische Regeln
-keep class io.github.jan.supabase.** { *; }
-keep class com.supabase.** { *; }
-dontwarn io.github.jan.supabase.**
-dontwarn com.supabase.**

# Gson-spezifische Regeln
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# OkHttp-spezifische Regeln
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Retrofit-spezifische Regeln (falls verwendet)
-keepattributes Signature
-keepattributes *Annotation*
-keep class retrofit2.** { *; }
-dontwarn retrofit2.**
-keepattributes Exceptions

# JSON-Serialisierung
-keepattributes *Annotation*
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Google Play Services und Firebase
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

# Google Sign-In spezifische Regeln
-keep class com.google.android.gms.auth.** { *; }
-keep class com.google.android.gms.common.** { *; }
-keep class com.google.android.gms.tasks.** { *; }
-dontwarn com.google.android.gms.auth.**
-dontwarn com.google.android.gms.common.**
-dontwarn com.google.android.gms.tasks.**

# Allgemeine Android-Regeln
-keepattributes SourceFile,LineNumberTable
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions

# Enum-Klassen
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Serializable-Klassen
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Parcelable-Implementierungen
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# Native Methoden
-keepclasseswithmembernames class * {
    native <methods>;
}

# View-Konstruktoren
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Activity-Klassen
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# WebView-spezifische Regeln
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, jav.lang.String);
}

# Crashlytics (falls aktiviert)
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception

# R8 Optimierungen
-allowaccessmodification
-repackageclasses

# Debugging-Informationen beibehalten
-keepattributes SourceFile,LineNumberTable

# Warnung-Unterdrückung für bekannte Bibliotheken
-dontwarn javax.annotation.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# Spezifische App-Klassen (anpassen nach Bedarf)
-keep class com.einsteinai.app.** { *; }

# Reflection-basierte Bibliotheken
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes RuntimeInvisibleParameterAnnotations

# Annotation-Prozessoren
-keep class * extends java.lang.annotation.Annotation { *; }

# Lambda-Ausdrücke (für ältere Android-Versionen)
-keepclassmembers class * {
    *** lambda$*(...); 
}

# Zusätzliche Sicherheitsregeln
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Ende der ProGuard-Regeln
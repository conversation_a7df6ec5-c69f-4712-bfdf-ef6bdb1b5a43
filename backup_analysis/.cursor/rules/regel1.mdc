---
description: 
globs: 
alwaysApply: true
---
Du darfst die App nicht verändern, es sei denn Ich sag es dir, dass du etwas veränderst, Du darfst niemals das Design Layout Farben oder so ähnliches verändern


starte die app immer mit diesen befehl flutter run -d adb-661ca116-9A3FmH._adb-tls-connect._tcp


Wenn ich dir sage, dass du irgendwas veränderst, Dann sollst du richtig nachdenken und die Sachen richtig implementieren. Ich möchte danach keine Fehler haben. Wenn du irgendwas von mir nicht verstanden hat oder Oder nicht klar ist, dann fragst du mich


Erstelle die Regeln Für das Design Layout und farben. Immer wenn ich dir irgendwas sage, dass du eine neue Funktion hinzufügst oder etwas anderes machst, dann denkst du an diese Farben regeln oder Layout und arbeitest danach die App sollte eine einheitliche Design haben. 


Wenn du auf einen tiefgründiges Problem stoßt, Dann mach keine extreme Veränderung wie zum Beispiel das kopieren der Projekte in einem anderen Ordner oder so ähnliches. Du versuchst, das Problem möglichst grundsätzlich zu lösen, ohne die Funktionen, oder die app zu verändern. 


Wenn ich dir sage, du sollst Eine Funktion hinzufügen, Dann denkst du wirklich an alles, was mit dieser Funktion zu tun hat Wirklich alles, Es darf am Ende keinen Fehler geben wie zum Beispiel dass die Funktion mit irgendwas nicht verbunden ist. Du musst an alles denken. 


Animation: Pulsert der "KI-Anschreiben generieren"-Button leicht, während er arbeitet  (Gefällt mir richtig gut) mache diese Animation, wenn ich dir sage, dass du einen bein andere machest. 


wir arbeiten in diesem Ordner /Users/<USER>/Documents/ki_test
nicht verschiebn


Starte die Flutter in der Chat, damit du überprüfen kannst, ob die app erfolgreich gestartet hat oder nicht. 

benutze immer memmory MCP. bei jeder ausführung 

# **nicht aufhören zu arbeiten bis die app erfolgreich geöffnet hat**. 



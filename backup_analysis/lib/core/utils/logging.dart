import 'package:flutter/foundation.dart';

// Eine einfache Protokollierungsklasse für die Anwendung
class Logger {
  final String tag;

  Logger(this.tag);

  // Debug-Meldung protokollieren (nur im Debug-Modus)
  void d(String message, {Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      print('🔍 D/$tag: $message');
      if (error != null) print('  ┗ Error: $error');
      if (stackTrace != null) print('  ┗ StackTrace: $stackTrace');
    }
  }

  // Informationsmeldung protokollieren (in allen Modi)
  void i(String message, {Object? error, StackTrace? stackTrace}) {
    print('ℹ️ I/$tag: $message');
    if (error != null) print('  ┗ Error: $error');
    if (stackTrace != null) print('  ┗ StackTrace: $stackTrace');
  }

  // Warnmeldung protokollieren (in allen Modi)
  void w(String message, {Object? error, StackTrace? stackTrace}) {
    print('⚠️ W/$tag: $message');
    if (error != null) print('  ┗ Error: $error');
    if (stackTrace != null) print('  ┗ StackTrace: $stackTrace');
  }

  // Fehlermeldung protokollieren (in allen Modi)
  void e(String message, {Object? error, StackTrace? stackTrace}) {
    print('❌ E/$tag: $message');
    if (error != null) print('  ┗ Error: $error');
    if (stackTrace != null) print('  ┗ StackTrace: $stackTrace');
  }
}

// Globale Instanzen von Loggern
final Map<String, Logger> _loggers = {};

// Logger-Initialisierung
void initializeLogging() {
  debugPrint('Logging-System initialisiert');
}

// Logger-Instanz abrufen oder erstellen
Logger getLogger(String tag) {
  return _loggers.putIfAbsent(tag, () => Logger(tag));
}
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/auth/screens/login_screen.dart'
    show LoginScreen, isSubmittingGoogleProvider;
import 'package:ki_test/src/presentation/auth/screens/signup_screen.dart';
import 'package:ki_test/src/presentation/auth/screens/forgot_password_screen.dart';
import 'package:ki_test/src/presentation/job_search/screens/job_search_screen.dart';
import 'package:ki_test/src/presentation/profile/screens/profile_screen.dart';
import 'package:ki_test/src/presentation/favorites/screens/favorites_screen.dart';
import 'package:ki_test/src/presentation/applied_jobs/screens/applied_jobs_screen.dart';
import 'package:ki_test/src/presentation/settings/screens/settings_screen.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import 'package:flutter/services.dart';
import 'package:ki_test/src/application/providers/auto_onboarding_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ki_test/src/presentation/splash/splash_screen.dart';
import 'package:ki_test/src/presentation/onboarding/screens/simple_onboarding_screen.dart';
import 'package:ki_test/src/presentation/onboarding/screens/cv_upload_screen.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as sp_flutter;

import 'src/core/utils/logging.dart';
import 'src/core/config/app_config.dart';
import 'src/core/utils/env_config.dart';
// Für deutsche Datumsformatierung

import 'package:ki_test/src/presentation/profile/screens/change_password_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_comparison_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_benefits_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/subscription_plans_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_upgrade_screen.dart';
import 'package:ki_test/src/presentation/premium/screens/premium_management_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/core/l10n/app_localizations.dart'; // Für Lokalisierung
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart'; // Für Lokalisierungs-Wrapper
import 'package:ki_test/src/application/providers/locale_provider.dart'; // Für Locale-Provider
import 'package:ki_test/src/presentation/test/test_style_screen.dart'; // Für Test-Seite

import 'package:ki_test/src/core/widgets/safe_area_wrapper.dart'; // Für Safe Area Wrapper
import 'package:ki_test/src/core/utils/app_responsive.dart'; // Für App Responsive
import 'package:flutter_phoenix/flutter_phoenix.dart'; // Für App-Neustart
import 'src/application/services/premium_activation_service.dart'; // Für Premium-Aktivierung
// Für Bewerbungszähler
import 'package:ki_test/src/application/providers/services_providers.dart'; // Für SubscriptionManagementService

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Supabase Auth State Stream Provider
final supabaseAuthStateProvider = StreamProvider<sp_flutter.AuthState>((ref) {
  return sp_flutter.Supabase.instance.client.auth.onAuthStateChange;
});

// SharedPreferences Provider für die direkte Verwendung
final sharedPrefsProvider = FutureProvider<SharedPreferences>((ref) async {
  return await SharedPreferences.getInstance();
});

// NEU: ChangeNotifier für GoRouter Refresh
class GoRouterNotifier extends ChangeNotifier {
  final Ref _ref;
  late final ProviderSubscription<AsyncValue<sp_flutter.AuthState>>
  _authStateSubscription;

  GoRouterNotifier(this._ref) {
    // Höre auf den Supabase Auth State Stream direkt
    _authStateSubscription = _ref.listen(supabaseAuthStateProvider, (
      previous,
      next,
    ) {
      // Benachrichtige GoRouter bei jeder Änderung im Auth State
      notifyListeners();

      // 🎯 DEBUG: Alle Auth Events loggen
      final log = getLogger('GoRouterNotifier');
      log.i('🔍 Auth Event: ${next.asData?.value.event} - Session: ${next.asData?.value.session?.user.id}');

      // Wenn der Benutzer sich anmeldet, starte AutoOnboardingService
      if (next.asData?.value.event == sp_flutter.AuthChangeEvent.signedIn) {
        log.i('🚀 Auth State: User hat sich angemeldet, prüfe AutoOnboardingService');

        // Verwende einen Timer um sicherzustellen, dass der Context verfügbar ist
        Timer(const Duration(milliseconds: 500), () async {
          try {
            final context = navigatorKey.currentContext;
            if (context != null && context.mounted) {
              log.i('🎯 Auth State: Context verfügbar, starte AutoOnboardingService');
              final autoOnboardingService = _ref.read(autoOnboardingProvider);
              await autoOnboardingService.onUserSignedIn(context);
              log.i('🎯 Auth State: AutoOnboardingService abgeschlossen');
            } else {
              log.w('⚠️ Auth State: Context nicht verfügbar für AutoOnboardingService');
            }
          } catch (e) {
            log.e('❌ Auth State: Fehler beim AutoOnboardingService: $e');
          }
        });
      }

      // Wenn der Benutzer sich abmeldet, setze die Ladezustände zurück
      if (next.asData?.value.event == sp_flutter.AuthChangeEvent.signedOut) {
        // Importiere die Provider aus der login_screen.dart
        _ref.read(isSubmittingGoogleProvider.notifier).state = false;

        // WICHTIG: Setze den server_onboarding_check_done Flag zurück
        // damit bei der nächsten Anmeldung der AutoOnboardingService wieder ausgelöst wird
        SharedPreferences.getInstance().then((prefs) {
          prefs.setBool('server_onboarding_check_done', false);
          log.i('🔄 server_onboarding_check_done Flag zurückgesetzt bei Abmeldung');
        });

        // WICHTIG: notifyListeners() bei Abmeldung um UI zu aktualisieren
        notifyListeners();
      }

      // 🎯 KRITISCHER FIX: AutoOnboardingService bei Google-Anmeldung aufrufen
      if (next.asData?.value.event == sp_flutter.AuthChangeEvent.signedIn) {
        log.i('🚀 Auth State: User hat sich angemeldet, prüfe AutoOnboardingService');

        // WICHTIG: Sofort notifyListeners() aufrufen um UI zu aktualisieren
        notifyListeners();

        // Verzögere den Aufruf um sicherzustellen, dass die Session vollständig ist
        Future.delayed(const Duration(milliseconds: 1500), () async {
          try {
            final context = navigatorKey.currentContext;
            if (context != null && context.mounted) {
              log.i('🎯 Auth State: Context verfügbar, starte AutoOnboardingService');

              // Rufe AutoOnboardingService auf
              final autoOnboardingService = _ref.read(autoOnboardingProvider);
              await autoOnboardingService.onUserSignedIn(context);
              log.i('🎯 Auth State: AutoOnboardingService abgeschlossen');

              // Nochmals notifyListeners() nach AutoOnboardingService
              notifyListeners();
            } else {
              log.w('⚠️ Auth State: Context nicht verfügbar für AutoOnboardingService');
            }
          } catch (e) {
            log.e('🎯 Auth State: Fehler beim AutoOnboardingService: $e');
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _authStateSubscription.close();
    super.dispose();
  }
}

// NEU: Provider für den GoRouterNotifier
final goRouterNotifierProvider = Provider<GoRouterNotifier>((ref) {
  return GoRouterNotifier(ref);
});

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Logging initialisieren (früh, um auch Initialisierungsfehler zu loggen)
  initializeLogging();
  final log = getLogger('main');

  log.i('App wird gestartet...');

  // Firebase-Initialisierung entfernt

  try {
    // Optimiere System UI Einstellungen
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    // Performance-Optimierung für Bildschirmaktualisierung
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // Initialisiere EnvConfig
    log.i('Initialisiere EnvConfig...');
    try {
      // Verwende einen hartcodierten Anon-Key für die Entwicklung
      // In einer Produktionsumgebung sollte dieser Schlüssel sicher gespeichert werden
      const supabaseAnonKey =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZwdHRkeGlidmpyZmp6YnRrdHFnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxODYzNzQsImV4cCI6MjA2MDc2MjM3NH0.WRijbwvWdLPVo0fhk_G9ppQR0nznCTZf7BvFDF55psg';

      log.i('Supabase initialisieren...');
      log.i('Supabase Anon Key: ${supabaseAnonKey.substring(0, 10)}...');

      // Die Session wird standardmäßig in SharedPreferences gespeichert
      // und bleibt zwischen App-Neustarts erhalten
      await sp_flutter.Supabase.initialize(
        url: EnvConfig.supabaseUrl,
        anonKey: supabaseAnonKey,
      );

      log.i('Supabase erfolgreich initialisiert.');

      // Initialisiere EnvConfig nach Supabase
      await EnvConfig.init();
    } catch (e) {
      log.e('Fehler bei der Initialisierung von Supabase: $e');
      // Zeige einen Dialog an, der den Benutzer über das Problem informiert
      runApp(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text(
                    'Fehler bei der Initialisierung',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      'Die App konnte nicht initialisiert werden. Bitte stelle sicher, dass die .env-Datei korrekt konfiguriert ist und der Supabase Anon Key vorhanden ist.\n\nFehler: $e',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton(
                    onPressed: () {
                      // App neu starten
                      Phoenix.rebirth(navigatorKey.currentContext!);
                    },
                    child: const Text('App neu starten'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      return; // Beende die Initialisierung
    }

    // --- Restliche Initialisierung ---
    // await AdService.initialize(); // Doppelter Aufruf? Entfernt, falls oben schon gemacht.

    // final prefs = await SharedPreferences.getInstance(); // Entfernt, da nicht mehr direkt hier benötigt
    // final bool showOnboarding = prefs.getBool('showOnboarding') ?? true; // Entfernt
    // log.d('Onboarding-Status: $showOnboarding'); // Entfernt

    // --- AppConfig --- // Wird jetzt in PrelaunchApp geladen

    // Den Rest der main-Funktion in einer runScoped App ausführen
    // um sicherzustellen, dass Provider verfügbar sind, wenn GoRouter erstellt wird
    // Verwende Phoenix für App-Neustart
    runApp(
      Phoenix(child: ProviderScope(child: PrelaunchApp())),
    ); // Lädt MyApp indirekt
    log.i('App erfolgreich gestartet.');
  } catch (e, stackTrace) {
    log.e(
      'Fehler während der Initialisierung:',
      error: e,
      stackTrace: stackTrace,
    );
    runApp(ErrorApp(error: e));
  }
}

// NEU: Widget, das ProviderScope sicherstellt, bevor MyApp/GoRouter initialisiert wird
class PrelaunchApp extends ConsumerStatefulWidget {
  const PrelaunchApp({super.key});

  @override
  ConsumerState<PrelaunchApp> createState() => _PrelaunchAppState();
}

class _PrelaunchAppState extends ConsumerState<PrelaunchApp> {
  @override
  void initState() {
    super.initState();
    final log = getLogger('PrelaunchApp');
    log.i('🚀 PrelaunchApp initState: Starte Premium-Aktivierung Timer');

    // Aktiviere Premium-Status nach einem längeren Delay, um sicherzustellen,
    // dass alle Provider initialisiert sind und der Benutzer angemeldet ist
    Future.delayed(const Duration(seconds: 3), () {
      log.i('⏰ PrelaunchApp: 3-Sekunden Timer abgelaufen, starte Premium-Aktivierung');
      _activatePremium();
    });

    // Aktiviere Premium-Status erneut nach 10 Sekunden, falls der erste Versuch fehlschlägt
    Future.delayed(const Duration(seconds: 10), () {
      log.i('⏰ PrelaunchApp: 10-Sekunden Timer abgelaufen, starte Premium-Aktivierung (Fallback)');
      _activatePremium();
    });
  }

  // Aktiviert den Premium-Status und synchronisiert das Abonnement
  Future<void> _activatePremium() async {
    final log = getLogger('PrelaunchApp');
    log.i('🎯 _activatePremium() aufgerufen - starte Subscription-Synchronisation');

    try {
      // Zuerst das Abonnement synchronisieren
      final subscriptionService = ref.read(
        subscriptionManagementServiceProvider,
      );
      log.i('📋 SubscriptionManagementService geladen, rufe syncSubscription() auf');
      final syncResult = await subscriptionService.syncSubscription();

      if (syncResult) {
        log.i('✅ Abonnement erfolgreich synchronisiert');
      } else {
        log.w(
          '⚠️ Abonnement konnte nicht synchronisiert werden, aktiviere Premium-Status manuell',
        );

        // Fallback: Premium-Status manuell aktivieren
        final premiumService = ref.read(premiumActivationServiceProvider);
        await premiumService.activatePremium();
        log.i('🔄 Premium-Status manuell aktiviert (Fallback)');
      }
    } catch (e, stackTrace) {
      log.e(
        '❌ Fehler bei der Premium-Aktivierung',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialisiere hier ggf. Dinge, die ref benötigen, *bevor* GoRouter erstellt wird
    // z.B. den GoRouterNotifier lauschen lassen
    ref.watch(goRouterNotifierProvider);

    // Lade AppConfig hier, da wir jetzt im ProviderScope sind
    final appConfig = AppConfig.fromEnvironment();
    // Der Onboarding-Status wird jetzt direkt im GoRouter Redirect gelesen,
    // daher keine Übergabe an MyApp mehr nötig.

    return MyApp(appConfig: appConfig); // showOnboarding entfernt
  }
}

// MyApp ist jetzt ein ConsumerWidget, um auf den Router zuzugreifen
class MyApp extends ConsumerWidget {
  final AppConfig appConfig;
  // final bool showOnboarding; // Entfernt

  const MyApp({
    super.key,
    required this.appConfig /*, required this.showOnboarding*/,
  }); // showOnboarding entfernt

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // GoRouter hier erstellen, da wir jetzt `ref` haben
    final GoRouter router = GoRouter(
      navigatorKey: navigatorKey, // Optional, aber nützlich
      initialLocation: '/splash', // Starte immer beim Splash Screen
      routes: <RouteBase>[
        GoRoute(
          path: '/',
          builder: (BuildContext context, GoRouterState state) {
            return const AppShell();
          },
          routes: <RouteBase>[
            GoRoute(
              path: 'job-detail/:refnr',
              builder: (BuildContext context, GoRouterState state) {
                final refnr = state.pathParameters['refnr']!;
                final jobEntity = state.extra as JobEntity?;
                return JobDetailScreen(
                  jobRefnr: refnr,
                  jobTitle: jobEntity?.title ?? 'Jobdetails',
                  jobEntity: jobEntity,
                );
              },
            ),
            GoRoute(
              path: 'profile',
              builder: (BuildContext context, GoRouterState state) {
                return const ProfileScreen();
              },
            ),
            GoRoute(
              path: 'settings',
              builder: (BuildContext context, GoRouterState state) {
                return const SettingsScreen();
              },
            ),
            GoRoute(
              path: 'change-password',
              builder: (BuildContext context, GoRouterState state) {
                return const ChangePasswordScreen();
              },
            ),
            GoRoute(
              path: 'premium',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumComparisonScreen();
              },
            ),
            GoRoute(
              path: 'premium-benefits',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumBenefitsScreen();
              },
            ),
            GoRoute(
              path: 'subscription-plans',
              builder: (BuildContext context, GoRouterState state) {
                return const SubscriptionPlansScreen();
              },
            ),

            GoRoute(
              path: 'premium-upgrade',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumUpgradeScreen();
              },
            ),

            GoRoute(
              path: 'premium-management',
              builder: (BuildContext context, GoRouterState state) {
                return const PremiumManagementScreen();
              },
            ),
            GoRoute(
              path: 'test-style',
              builder: (BuildContext context, GoRouterState state) {
                return const TestStyleScreen();
              },
            ),
          ],
        ),
        GoRoute(
          path: '/login',
          builder: (BuildContext context, GoRouterState state) {
            return const LoginScreen();
          },
        ),
        GoRoute(
          path: '/register',
          builder: (BuildContext context, GoRouterState state) {
            return const SignupScreen();
          },
        ),
        GoRoute(
          path: '/forgot-password',
          builder: (BuildContext context, GoRouterState state) {
            return const ForgotPasswordScreen();
          },
        ),
        GoRoute(
          path: '/onboarding',
          builder: (BuildContext context, GoRouterState state) {
            // Verwende den neuen Onboarding-Screen
            return const SimpleOnboardingScreen();
          },
          routes: <RouteBase>[
            GoRoute(
              path: 'cv-upload',
              builder: (BuildContext context, GoRouterState state) {
                return const CvUploadScreen();
              },
            ),
          ],
        ),
        // Spezielle Route für den Onboarding-Screen, die nicht von der Umleitung betroffen ist
        GoRoute(
          path: '/view-onboarding',
          builder: (BuildContext context, GoRouterState state) {
            // Verwende den neuen Onboarding-Screen im Ansichtsmodus
            return const SimpleOnboardingScreen();
          },
        ),
        GoRoute(
          path: '/splash',
          builder: (BuildContext context, GoRouterState state) {
            return const SplashScreen();
          },
        ),
      ],
      redirect: (BuildContext context, GoRouterState state) async {
        // Prüfe direkt den Supabase Session Status
        final session = sp_flutter.Supabase.instance.client.auth.currentSession;

        // Prüfe auch den Anmeldestatus in den SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final hasExistingAccount =
            prefs.getBool('has_existing_account') ?? false;

        // Wenn der Benutzer eine aktive Sitzung hat UND in den SharedPreferences als angemeldet markiert ist,
        // gilt er als angemeldet. Beide Bedingungen müssen erfüllt sein, um Probleme nach Kontolöschung zu vermeiden.
        final bool isLoggedIn = session != null && hasExistingAccount;
        final bool isOnAuthRoute =
            state.matchedLocation == '/login' ||
            state.matchedLocation == '/register' ||
            state.matchedLocation == '/forgot-password';
        // LOKALE ONBOARDING-LOGIK: Verwende nur lokale SharedPreferences
        // Keine Server-Checks mehr - nur lokale Speicherung
        bool isOnboardingComplete = true; // Default: Onboarding ist abgeschlossen
        final bool isSplash = state.matchedLocation == '/splash';
        final bool isOnboarding = state.matchedLocation == '/onboarding';
        final bool isViewOnboarding =
            state.matchedLocation == '/view-onboarding';

        final log = getLogger('GoRouterRedirect');
        log.d(
          'Redirect check: location=${state.matchedLocation}, isLoggedIn=$isLoggedIn, isOnboardingComplete=$isOnboardingComplete',
        );

        // Wenn auf Splash, lasse den Splash Screen die Navigation machen
        if (isSplash) {
          log.d('Redirect: Auf Splash, keine Umleitung.');
          return null;
        }

        // Wenn nicht eingeloggt
        if (!isLoggedIn) {
          // Wenn nicht bereits auf einer Auth-Route, leite zu Login
          if (!isOnAuthRoute) {
            log.d('Redirect: Nicht eingeloggt, nicht auf Auth-Route -> /login');
            return '/login';
          }
          // Ansonsten bleibe auf der Auth-Route
          log.d(
            'Redirect: Nicht eingeloggt, aber auf Auth-Route, keine Umleitung.',
          );
          return null;
        }

        // Wenn eingeloggt
        // Wir haben den Anmeldestatus bereits oben geprüft

        // Wenn der Benutzer angemeldet ist - AutoOnboardingService übernimmt Navigation
        if (isLoggedIn) {
          // KEINE GOROUTER NAVIGATION - AutoOnboardingService im Login Screen übernimmt das
          log.i('🚀 GoRouter: User angemeldet, AutoOnboardingService übernimmt Navigation');
          return null; // Keine GoRouter Navigation
        }

        // Wenn auf der View-Onboarding-Route, keine Umleitung
        if (isViewOnboarding) {
          log.d(
            'Redirect: Auf View-Onboarding-Route, keine Umleitung (Ansichtsmodus).',
          );
          return null;
        }

        // Standard: keine Umleitung
        log.d('Redirect: Keine spezielle Umleitung erforderlich.');
        return null;
      },
      errorBuilder:
          (context, state) => Scaffold(
            body: Center(child: Text('Seite nicht gefunden: ${state.error}')),
          ),
      // NEU: refreshListenable verwendet den Notifier
      refreshListenable: ref.watch(goRouterNotifierProvider),
    );

    // Aktuelle Locale aus dem Provider lesen
    final currentLocale = ref.watch(localeProvider);

    // Nutze MaterialApp.router
    return MaterialApp.router(
      routerConfig: router,
      title: 'Bewerbung KI',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: ThemeMode.dark,
      localizationsDelegates: [
        AppLocalizations.delegate, // Für unsere eigenen Übersetzungen
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: supportedLocales, // Aus dem locale_provider
      locale: currentLocale, // Dynamisch aus dem Provider
      builder: (context, child) {
        // Verwende MediaQuery, um die Textgröße zu fixieren und sicherzustellen,
        // dass die App auf allen Geräten konsistent aussieht
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(1.0),
            // Stelle sicher, dass die App auf allen Geräten richtig angezeigt wird
            devicePixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          child: child!,
        );
      },
    );
  }
}

class AppShell extends ConsumerStatefulWidget {
  const AppShell({super.key});

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  int _selectedIndex = 0;

  // Liste der Widgets passend zu den Navigationsitems
  static const List<Widget> _widgetOptions = <Widget>[
    JobSearchScreen(),
    FavoritesScreen(), // Favoriten jetzt an Index 1
    AppliedJobsScreen(), // Beworbene Jobs jetzt an Index 2
    ProfileScreen(), // Profil jetzt an Index 3
    SettingsScreen(), // Einstellungen jetzt an Index 4
  ];

  void _onItemTapped(int index) {
    // Für alle Tabs (Jobs, Favoriten, Profil, Einstellungen): Navigation direkt erlauben
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // extendBody: false, damit der Body nicht hinter die BottomNavigationBar erweitert wird
      extendBody: false,
      // Verwende SafeAreaWrapper für den Body
      body: SafeAreaWrapper(
        // bottom: true, damit der untere Rand berücksichtigt wird
        bottom: true,
        child: Center(
          child: IndexedStack(index: _selectedIndex, children: _widgetOptions),
        ),
      ),
      // Verwende BottomNavigationBar mit angepassten Eigenschaften für verschiedene Gerätetypen
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          // Füge einen leichten Schatten hinzu, um die Navigationsleiste hervorzuheben
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // Entspricht etwa 0.1 Opazität
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          items: <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Container(
                height: 36.0, // Größere Höhe für mehr Abstand
                padding: const EdgeInsets.only(
                  bottom: 6.0,
                ), // Mehr Abstand zum Text
                margin: const EdgeInsets.only(top: 8.0), // Abstand nach oben
                alignment: Alignment.center,
                child: const Icon(Icons.search),
              ),
              label: AppLocalizationsWrapper.of(context).jobSearchTitle,
            ),
            BottomNavigationBarItem(
              icon: Container(
                height: 36.0,
                padding: const EdgeInsets.only(bottom: 6.0),
                margin: const EdgeInsets.only(top: 8.0),
                alignment: Alignment.center,
                child: const Icon(Icons.favorite_border),
              ),
              label: AppLocalizationsWrapper.of(context).favoritesTitle,
            ),
            BottomNavigationBarItem(
              icon: Container(
                height: 36.0,
                padding: const EdgeInsets.only(bottom: 6.0),
                margin: const EdgeInsets.only(top: 8.0),
                alignment: Alignment.center,
                child: const Icon(Icons.work_outline),
              ),
              label: 'Beworbene Jobs',
            ),
            BottomNavigationBarItem(
              icon: Container(
                height: 36.0,
                padding: const EdgeInsets.only(bottom: 6.0),
                margin: const EdgeInsets.only(top: 8.0),
                alignment: Alignment.center,
                child: const Icon(Icons.person_outline),
              ),
              label: AppLocalizationsWrapper.of(context).profileTitle,
            ),
            BottomNavigationBarItem(
              icon: Container(
                height: 36.0,
                padding: const EdgeInsets.only(bottom: 6.0),
                margin: const EdgeInsets.only(top: 8.0),
                alignment: Alignment.center,
                child: const Icon(Icons.settings_outlined),
              ),
              label: AppLocalizationsWrapper.of(context).settingsTitle,
            ),
          ],
          currentIndex: _selectedIndex,
          selectedItemColor: Colors.amber[700], // Etwas kräftigere Farbe
          unselectedItemColor:
              Colors.grey[500], // Helleres Grau für besseren Kontrast
          onTap: _onItemTapped,
          type: BottomNavigationBarType.fixed,
          elevation:
              0.0, // Kein eigener Schatten, da wir einen Container mit Schatten verwenden
          backgroundColor:
              Theme.of(context).bottomNavigationBarTheme.backgroundColor,
          // Verbesserte Textgrößen für bessere Lesbarkeit
          selectedLabelStyle: TextStyle(
            fontSize: AppResponsive.value(
              context: context,
              mobile: 11.0,
              tablet: 13.0,
              desktop: 15.0,
            ),
            fontWeight:
                FontWeight.w600, // Deutlich stärker für bessere Sichtbarkeit
            height: 1.0,
            letterSpacing: 0.3, // Mehr Abstand zwischen Buchstaben
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: AppResponsive.value(
              context: context,
              mobile:
                  10.0, // Etwas kleiner für besseren Kontrast zum ausgewählten Element
              tablet: 12.0,
              desktop: 14.0,
            ),
            fontWeight: FontWeight.w400,
            height: 1.0,
            letterSpacing: 0.2,
          ),
          iconSize: AppResponsive.value(
            context: context,
            mobile: 26.0, // Größere Icons für bessere Sichtbarkeit
            tablet: 30.0,
            desktop: 34.0,
          ),
        ),
      ),
    );
  }
}

// --- Platzhalter für fehlende Screens ---

// --- Ende: Code für den AI Search Provider ---

// Eine einfache App zur Fehleranzeige
class ErrorApp extends StatelessWidget {
  final Object error;
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(child: Text('Fehler bei der App-Initialisierung: $error')),
      ),
    );
  }
}

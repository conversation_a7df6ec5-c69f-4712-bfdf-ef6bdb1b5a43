import 'package:flutter/material.dart';
import '../core/theme/app_theme.dart'; // Korrigierter Importpfad

// TextEditingController extension to check if a controller is disposed
extension TextEditingControllerExtension on TextEditingController {
  /// Checks if this TextEditingController is already disposed
  bool get isDisposed {
    try {
      // If accessing text throws an exception, the controller is disposed
      // ignore: unused_local_variable
      final _ = text;
      return false; // No error = not disposed
    } catch (e) {
      return true; // Error = disposed
    }
  }
}

// BuildContext extension for showing SnackBars
extension BuildContextSnackbarExtension on BuildContext {
  /// Zeigt eine standard Erfolgs-SnackBar an.
  void showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor, // Verwende Theme-Farbe
        behavior:
            SnackBarBehavior
                .floating, // Floating statt fixed für bessere Positionierung
        duration: const Duration(seconds: 3),
        margin: EdgeInsets.only(
          bottom:
              MediaQuery.of(this).viewPadding.bottom > 0
                  ? MediaQuery.of(this).viewPadding.bottom
                  : 16,
          left: 8,
          right: 8,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Zeigt eine standard Fehler-SnackBar an.
  void showErrorSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor, // Verwende Theme-Farbe
        behavior:
            SnackBarBehavior
                .floating, // Floating statt fixed für bessere Positionierung
        duration: const Duration(seconds: 3),
        margin: EdgeInsets.only(
          bottom:
              MediaQuery.of(this).viewPadding.bottom > 0
                  ? MediaQuery.of(this).viewPadding.bottom
                  : 16,
          left: 8,
          right: 8,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

// BuildContext extension for refreshing Riverpod providers (optional, falls benötigt)
/*
extension BuildContextRiverpodExtension on BuildContext {
  /// Refreshes the provided provider.
  /// Requires access to a Ref instance, typically via HookConsumerWidget or Consumer.
  /// This might be better handled directly within the widget build method.
  Future<T> refresh<T>(ProviderListenable<T> provider) async {
    // This approach is generally discouraged within extensions as it couples
    // the extension tightly with Riverpod's Ref.
    // It's better to call ref.refresh() directly in the widget.
    final container = ProviderScope.containerOf(this);
    return container.refresh(provider);
  }
}
*/

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

/// Extension für BuildContext, um Localizations über .loc aufzurufen
extension BuildContextLocalizationsExtension on BuildContext {
  /// Gibt die lokalisierten Strings zurück
  AppLocalizationsWrapper get loc => AppLocalizationsWrapper.of(this);
  
  /// Kurze Methode für die Navigation mit go_router
  void go(String route) => GoRouter.of(this).go(route);
} 
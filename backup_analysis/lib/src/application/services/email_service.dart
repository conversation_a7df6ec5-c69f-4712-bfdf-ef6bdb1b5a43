// import 'package:flutter/foundation.dart'; // Unused
import 'package:ki_test/src/application/services/gmail_service.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/entities/application_entity.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Ergebnis des E-Mail-Versands
class EmailSendResult {
  final bool success;
  final String? applicationId;
  final String? emailId;
  final String? errorMessage;

  EmailSendResult({
    required this.success,
    this.applicationId,
    this.emailId,
    this.errorMessage,
  });

  factory EmailSendResult.fromJson(Map<String, dynamic> json) {
    return EmailSendResult(
      success: json['success'] ?? false,
      applicationId: json['applicationId'],
      emailId: json['emailId'],
      errorMessage: json['error'],
    );
  }

  factory EmailSendResult.error(String message) {
    return EmailSendResult(success: false, errorMessage: message);
  }
}

/// Service für den E-Mail-Versand
class EmailService {
  final SupabaseClient _client;
  final GmailService _gmailService;
  final _log = getLogger('EmailService');

  EmailService(this._client, this._gmailService);

  /// Sendet eine Bewerbungs-E-Mail über Gmail oder Supabase Edge Function
  ///
  /// [recipientEmail] - E-Mail-Adresse des Empfängers
  /// [jobTitle] - Titel der Stelle
  /// [coverLetter] - Text des Anschreibens
  /// [cvFileId] - ID der Lebenslauf-Datei in Supabase Storage (optional)
  /// [companyName] - Name des Unternehmens (optional)
  /// [jobId] - ID des Jobs für Tracking (optional)
  /// [useGmail] - Ob Gmail statt Supabase verwendet werden soll (default: true)
  Future<EmailSendResult> sendApplicationEmail({
    required String recipientEmail,
    required String jobTitle,
    required String coverLetter,
    String? cvFileId,
    String? companyName,
    String? jobId,
    bool useGmail = true,
  }) async {
    _log.i('Sende Bewerbungs-E-Mail an $recipientEmail für Stelle: $jobTitle');

    try {
      // Prüfe, ob der Benutzer angemeldet ist
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        _log.w('Benutzer ist nicht angemeldet');
        return EmailSendResult.error(
          'Du musst angemeldet sein, um eine Bewerbung zu senden.',
        );
      }

      // Versuche zuerst, die E-Mail über Gmail zu senden, wenn aktiviert
      if (useGmail) {
        try {
          _log.i('Versuche, E-Mail über Gmail zu senden...');
          final gmailResult = await _gmailService.sendApplicationEmail(
            recipientEmail: recipientEmail,
            jobTitle: jobTitle,
            coverLetter: coverLetter,
            cvFilePath: cvFileId,
            companyName: companyName,
            jobId: jobId,
          );

          if (gmailResult.success) {
            _log.i('E-Mail erfolgreich über Gmail gesendet');

            // Speichere die Bewerbung in Supabase, wenn Gmail erfolgreich war
            if (gmailResult.applicationEntity != null) {
              await _saveApplicationToSupabase(gmailResult.applicationEntity!);
            }

            return EmailSendResult(
              success: true,
              applicationId: gmailResult.applicationEntity?.id,
              emailId: gmailResult.applicationEntity?.id,
            );
          } else {
            _log.w(
              'Gmail-Versand fehlgeschlagen: ${gmailResult.errorMessage}. Versuche Fallback...',
            );
            // Wenn Gmail fehlschlägt, verwenden wir den Supabase-Fallback
          }
        } catch (e) {
          _log.w('Fehler beim Gmail-Versand: $e. Versuche Fallback...');
          // Bei Fehlern verwenden wir den Supabase-Fallback
        }
      }

      // Fallback: Verwende Supabase Edge Function
      _log.i('Verwende Supabase Edge Function als Fallback...');

      // Bereite die Anfrage vor
      final payload = {
        'userId': currentUser.id,
        'recipientEmail': recipientEmail,
        'jobTitle': jobTitle,
        'coverLetter': coverLetter,
        if (cvFileId != null) 'cvFileId': cvFileId,
        if (companyName != null) 'companyName': companyName,
        if (jobId != null) 'jobId': jobId,
      };

      // Rufe die Edge Function auf
      _log.i('Rufe Supabase Function "send-application-email" auf...');
      final response = await _client.functions.invoke(
        'send-application-email',
        body: payload,
      );

      // Verarbeite die Antwort
      _log.i('Antwort von Supabase Function: Status ${response.status}');

      if (response.status != 200) {
        String errorMessage = 'Fehler beim Senden der E-Mail';
        try {
          final errorData = response.data as Map<String, dynamic>;
          errorMessage = errorData['error'] ?? errorMessage;
        } catch (e) {
          // Ignoriere Fehler beim Parsen
        }

        _log.e('Fehler beim Senden der E-Mail: $errorMessage');
        return EmailSendResult.error(errorMessage);
      }

      // Erfolg
      final result = EmailSendResult.fromJson(
        response.data as Map<String, dynamic>,
      );
      _log.i(
        'E-Mail erfolgreich gesendet. ApplicationID: ${result.applicationId}',
      );
      return result;
    } catch (e, stackTrace) {
      _log.e('Fehler beim Senden der E-Mail', error: e, stackTrace: stackTrace);
      return EmailSendResult.error(
        'Fehler beim Senden der E-Mail: ${e.toString()}',
      );
    }
  }

  /// Speichert eine Bewerbung in Supabase
  Future<void> _saveApplicationToSupabase(ApplicationEntity application) async {
    try {
      await _client.from('applications').insert({
        'id': application.id,
        'user_id': application.userId,
        'job_id': application.jobId,
        'job_title': application.jobTitle,
        'company_name': application.companyName,
        'recipient_email': application.recipientEmail,
        'status': application.status,
        'cover_letter': application.coverLetter,
        'sent_at': application.sentAt.toIso8601String(),
        'last_updated': application.lastUpdated.toIso8601String(),
      });
      _log.i('Bewerbung in Supabase gespeichert: ${application.id}');
    } catch (e) {
      _log.e('Fehler beim Speichern der Bewerbung in Supabase: $e');
    }
  }

  /// Ruft alle Bewerbungen des aktuellen Benutzers ab
  Future<List<Map<String, dynamic>>> getUserApplications() async {
    try {
      final response = await _client
          .from('applications')
          .select()
          .order('sent_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen der Bewerbungen',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Ruft eine bestimmte Bewerbung anhand der ID ab
  Future<Map<String, dynamic>?> getApplicationById(String applicationId) async {
    try {
      final response =
          await _client
              .from('applications')
              .select()
              .eq('id', applicationId)
              .single();

      return response;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen der Bewerbung',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Aktualisiert den Status einer Bewerbung
  Future<bool> updateApplicationStatus(
    String applicationId,
    String status,
  ) async {
    try {
      await _client
          .from('applications')
          .update({'status': status})
          .eq('id', applicationId);

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Aktualisieren des Bewerbungsstatus',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }
}

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service zur automatischen Aktivierung des Premium-Status
class PremiumActivationService {
  final Ref _ref;
  final _log = getLogger('PremiumActivationService');

  // Schlüssel für die letzte Aktivierung in SharedPreferences
  static const String _lastActivationKey = 'last_premium_activation';

  PremiumActivationService(this._ref);

  /// Aktiviert den Premium-Status für 30 Tage, wenn der Benutzer angemeldet ist.
  /// Diese Methode ignoriert alle Prüfungen und aktiviert den Premium-Status immer.
  Future<void> activatePremium() async {
    try {
      _log.i('Starte Premium-Aktivierung...');

      // Prüfe, ob ein Benutzer angemeldet ist
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer für Premium-Aktivierung');
        return;
      }

      _log.d('Benutzer ist angemeldet: ${currentUser.email}');

      // Setze das Ablaufdatum auf 30 Tage in der Zukunft
      final expiryDate = DateTime.now().add(const Duration(days: 30));

      _log.d('Setze Premium-Status auf aktiv bis $expiryDate');

      // Warte auf den UserProfileProvider, um sicherzustellen, dass er geladen ist
      final userProfileState = _ref.read(userProfileProvider);
      if (userProfileState is AsyncLoading) {
        _log.d('UserProfile wird noch geladen, warte...');
        // Warte kurz und versuche es erneut
        await Future.delayed(const Duration(seconds: 2));
      }

      // Aktualisiere den Premium-Status
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(isPremium: true, premiumExpiryDate: expiryDate);

      // Speichere den Zeitpunkt der Aktivierung
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_lastActivationKey, now);

      _log.i('Premium-Status für einen Tag aktiviert, gültig bis $expiryDate');
    } catch (e, stackTrace) {
      _log.e(
        'Fehler bei der Premium-Aktivierung',
        error: e,
        stackTrace: stackTrace,
      );

      // Versuche es nach einer Verzögerung erneut
      _log.d('Versuche erneut in 3 Sekunden...');
      await Future.delayed(const Duration(seconds: 3));
      await _forceActivatePremium();
    }
  }

  /// Aktiviert den Premium-Status direkt, ohne Prüfungen
  Future<void> _forceActivatePremium() async {
    try {
      _log.i('Erzwinge Premium-Aktivierung...');

      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer für erzwungene Premium-Aktivierung');
        return;
      }

      // Setze das Ablaufdatum auf 30 Tage in der Zukunft
      final expiryDate = DateTime.now().add(const Duration(days: 30));

      // Aktualisiere den Premium-Status direkt
      final notifier = _ref.read(userProfileProvider.notifier);
      await notifier.updatePremiumStatus(
        isPremium: true,
        premiumExpiryDate: expiryDate,
      );

      _log.i('Premium-Status erfolgreich erzwungen, gültig bis $expiryDate');
    } catch (e, stackTrace) {
      _log.e(
        'Fehler bei der erzwungenen Premium-Aktivierung',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
}

/// Provider für den PremiumActivationService
final premiumActivationServiceProvider = Provider<PremiumActivationService>((
  ref,
) {
  return PremiumActivationService(ref);
});

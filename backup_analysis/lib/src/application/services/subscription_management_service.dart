import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/models/subscription_plan.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service zur Verwaltung von Abonnements
class SubscriptionManagementService {
  final Ref _ref;
  final _log = getLogger('SubscriptionManagementService');
  final _supabase = Supabase.instance.client;

  SubscriptionManagementService(this._ref);

  /// Gibt das aktuelle Abonnement zurück
  Future<SubscriptionPlan?> getCurrentSubscription() async {
    try {
      final userProfileState = await _ref.read(userProfileProvider.future);

      if (userProfileState.isPremium == true) {
        return SubscriptionPlan(
          planType: userProfileState.premiumPlanType ?? 'basic',
          expiryDate: userProfileState.premiumExpiryDate,
          isActive: true,
        );
      }

      return SubscriptionPlan(
        planType: 'basic',
        expiryDate: null,
        isActive: false,
      );
    } catch (e) {
      _log.e('Fehler beim Abrufen des Abonnements', error: e);
      return null;
    }
  }

  /// Gibt die verbleibenden Bewerbungen zurück
  Future<Map<String, dynamic>> getRemainingApplications() async {
    try {
      final userProfileState = await _ref.read(userProfileProvider.future);
      final planType = userProfileState.premiumPlanType ?? 'basic';

      // Wenn der Benutzer ein Unlimited-Abonnement hat, gibt es keine Begrenzung
      if (planType == 'unlimited') {
        return {'remaining': 999999, 'total': 999999, 'unlimited': true};
      }

      // Anzahl der Bewerbungen je nach Plan-Typ
      final int totalApplications = planType == 'pro' ? 150 : 30;

      // Verbleibende Bewerbungen aus dem Benutzerprofil oder aus Supabase abrufen
      final int usedApplications = userProfileState.usedApplications ?? 0;
      final int remaining = totalApplications - usedApplications;

      return {
        'remaining': remaining < 0 ? 0 : remaining,
        'total': totalApplications,
        'unlimited': false,
      };
    } catch (e) {
      _log.e('Fehler beim Abrufen der verbleibenden Bewerbungen', error: e);
      return {'remaining': 0, 'total': 0, 'unlimited': false};
    }
  }

  /// Ändert das Abonnement des Benutzers
  Future<bool> changePlan(String newPlanType) async {
    try {
      final userProfileNotifier = _ref.read(userProfileProvider.notifier);

      // Setze das Ablaufdatum auf 1 Monat in der Zukunft
      final expiryDate = DateTime.now().add(const Duration(days: 30));

      // Aktualisiere den Premium-Status über den UserProfileProvider
      await userProfileNotifier.updatePremiumStatus(
        isPremium: newPlanType != 'basic',
        premiumPlanType: newPlanType,
        premiumExpiryDate: expiryDate,
      );

      // Setze die verbleibenden Bewerbungen zurück
      if (newPlanType == 'basic') {
        await userProfileNotifier.updateUsedApplications(0);
      }

      return true;
    } catch (e) {
      _log.e('Fehler beim Ändern des Abonnements', error: e);
      return false;
    }
  }

  /// Prüft, ob der Benutzer ein aktives Abonnement hat
  Future<bool> hasActiveSubscription() async {
    try {
      final userProfileState = await _ref.read(userProfileProvider.future);

      if (userProfileState.isPremium != true) {
        return false;
      }

      // Prüfe, ob das Abonnement abgelaufen ist
      final expiryDate = userProfileState.premiumExpiryDate;
      if (expiryDate == null) {
        return false;
      }

      return expiryDate.isAfter(DateTime.now());
    } catch (e) {
      _log.e('Fehler beim Prüfen des Abonnements', error: e);
      return false;
    }
  }

  /// Prüft, ob der Benutzer noch Bewerbungen übrig hat
  Future<bool> hasRemainingApplications() async {
    try {
      final remainingApplications = await getRemainingApplications();

      // Wenn der Benutzer ein Unlimited-Abonnement hat, gibt es keine Begrenzung
      if (remainingApplications['unlimited'] == true) {
        return true;
      }

      return remainingApplications['remaining'] > 0;
    } catch (e) {
      _log.e('Fehler beim Prüfen der verbleibenden Bewerbungen', error: e);
      return false;
    }
  }

  /// Zählt eine Bewerbung
  Future<bool> countApplication() async {
    try {
      final userProfileState = await _ref.read(userProfileProvider.future);
      final planType = userProfileState.premiumPlanType ?? 'basic';

      // Wenn der Benutzer ein Unlimited-Abonnement hat, muss nichts gezählt werden
      if (planType == 'unlimited') {
        return true;
      }

      // Erhöhe die Anzahl der verwendeten Bewerbungen
      final int usedApplications = userProfileState.usedApplications ?? 0;
      await _ref
          .read(userProfileProvider.notifier)
          .updateUsedApplications(usedApplications + 1);

      return true;
    } catch (e) {
      _log.e('Fehler beim Zählen der Bewerbung', error: e);
      return false;
    }
  }

  /// Zeigt einen Dialog an, wenn der Benutzer keine Bewerbungen mehr übrig hat
  Future<void> showNoApplicationsRemainingDialog(BuildContext context) async {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Keine Bewerbungen mehr übrig'),
            content: const Text(
              'Du hast keine Bewerbungen mehr übrig. Upgrade auf ein höheres Abonnement, um mehr Bewerbungen zu erhalten.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Abbrechen'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamed('/premium-management');
                },
                child: const Text('Upgrade'),
              ),
            ],
          ),
    );
  }

  /// Kündigt das aktuelle Abonnement des Benutzers
  Future<bool> cancelSubscription() async {
    try {
      // Hole den aktuellen UserProfile-Status
      final userProfileState = _ref.read(userProfileProvider);

      // Warte, falls der Status noch geladen wird
      if (userProfileState is AsyncLoading) {
        await Future.delayed(const Duration(seconds: 1));
      }

      // Hole den aktuellen Wert
      final userProfile = userProfileState.value;

      // Prüfe, ob der Benutzer ein Premium-Abonnement hat
      if (userProfile?.isPremium != true) {
        _log.w('Kein aktives Abonnement zum Kündigen vorhanden');
        return false;
      }

      // Hole die Benutzer-ID
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        _log.e('Benutzer ist nicht angemeldet');
        return false;
      }

      // Suche nach aktiven Abonnements in der Datenbank
      final response =
          await _supabase
              .from('subscriptions')
              .select()
              .eq('user_id', userId)
              .eq('status', 'active')
              .limit(1)
              .maybeSingle();

      if (response == null) {
        _log.w('Kein aktives Abonnement in der Datenbank gefunden');

        // Aktualisiere trotzdem den Premium-Status im UserProfile
        await _ref
            .read(userProfileProvider.notifier)
            .updatePremiumStatus(isPremium: false, planType: 'basic');

        return true;
      }

      // Markiere das Abonnement als gekündigt
      await _supabase
          .from('subscriptions')
          .update({
            'status': 'cancelled',
            'cancelled_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', response['id']);

      _log.i('Abonnement ${response['id']} erfolgreich gekündigt');

      // Optional: Aktualisiere den Premium-Status im UserProfile
      // Wir behalten den Premium-Status bis zum Ablaufdatum bei

      return true;
    } catch (e) {
      _log.e('Fehler beim Kündigen des Abonnements', error: e);
      return false;
    }
  }
}

/// Provider für den SubscriptionManagementService
final subscriptionManagementServiceProvider =
    Provider<SubscriptionManagementService>((ref) {
      return SubscriptionManagementService(ref);
    });

/// Provider für die verbleibenden Bewerbungen
final remainingApplicationsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return subscriptionService.getRemainingApplications();
});

/// Provider für den Abonnement-Status
final subscriptionStatusProvider = FutureProvider<SubscriptionPlan?>((
  ref,
) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return subscriptionService.getCurrentSubscription();
});

/// Provider für den Simulator-Service
final subscriptionSimulatorServiceProvider =
    Provider<SubscriptionSimulatorService>((ref) {
      return SubscriptionSimulatorService(ref);
    });

/// Service zum Simulieren von Abonnements (nur für Testzwecke)
class SubscriptionSimulatorService {
  final Ref _ref;
  final _log = getLogger('SubscriptionSimulatorService');

  SubscriptionSimulatorService(this._ref);

  /// Simuliert ein Abonnement
  Future<bool> simulateSubscription({
    required String planType,
    int? remainingApplications,
    int durationInDays = 30,
  }) async {
    try {
      final userProfileNotifier = _ref.read(userProfileProvider.notifier);

      // Setze das Ablaufdatum auf die angegebene Anzahl von Tagen in der Zukunft
      final expiryDate = DateTime.now().add(Duration(days: durationInDays));

      // Aktualisiere den Premium-Status über den UserProfileProvider
      await userProfileNotifier.updatePremiumStatus(
        isPremium: planType != 'basic',
        planType: planType,
        premiumExpiryDate: expiryDate,
      );

      // Setze die verbleibenden Bewerbungen
      // Für Basic-Plan immer auf 0 verwendete Bewerbungen zurücksetzen
      if (planType == 'basic') {
        await _ref.read(userProfileProvider.notifier).updateUsedApplications(0);
        _log.i(
          'Basic-Plan: Bewerbungszähler auf 0 verwendete Bewerbungen zurückgesetzt',
        );
      }
      // Für andere Pläne den angegebenen Wert verwenden
      else if (remainingApplications != null) {
        final totalApplications =
            planType == 'pro' ? 150 : 999999; // Unlimited für andere Pläne
        final usedApplications = totalApplications - remainingApplications;
        await _ref
            .read(userProfileProvider.notifier)
            .updateUsedApplications(
              usedApplications < 0 ? 0 : usedApplications,
            );
      } else {
        await _ref.read(userProfileProvider.notifier).updateUsedApplications(0);
      }

      return true;
    } catch (e) {
      _log.e('Fehler bei der Simulation', error: e);
      return false;
    }
  }
}

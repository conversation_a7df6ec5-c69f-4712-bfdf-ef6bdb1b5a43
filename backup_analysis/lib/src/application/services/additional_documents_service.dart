import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:ki_test/src/domain/models/additional_document.dart';

/// Service für das Management zusätzlicher Dokumente
class AdditionalDocumentsService {
  static const String _bucketName = 'additional-documents';
  static const int _maxFileSize = 5 * 1024 * 1024; // 5MB
  static const int _maxDocumentsPerUser = 5;

  final SupabaseClient _supabase = Supabase.instance.client;

  /// Lädt ein zusätzliches Dokument hoch
  Future<DocumentUploadResult> uploadDocument({
    required String filePath,
    required String fileName,
    required ValueNotifier<DocumentUploadProgress>? progressNotifier,
  }) async {
    try {
      // Validierung
      final validationResult = await _validateFile(filePath, fileName);
      if (!validationResult.success) {
        return validationResult;
      }

      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return const DocumentUploadResult(
          success: false,
          errorMessage: 'Benutzer nicht angemeldet',
        );
      }

      // Prü<PERSON> bestehender Dokumente
      final existingCount = await _getDocumentCount(currentUser.id);
      if (existingCount >= _maxDocumentsPerUser) {
        return DocumentUploadResult(
          success: false,
          errorMessage: 'Maximale Anzahl von $_maxDocumentsPerUser Dokumenten erreicht',
        );
      }

      progressNotifier?.value = DocumentUploadProgress(
        fileName: fileName,
        progress: 0.1,
        status: DocumentUploadStatus.preparing,
      );

      // Datei lesen
      final file = File(filePath);
      final fileBytes = await file.readAsBytes();
      final fileSize = fileBytes.length;

      // Storage-Pfad erstellen
      final fileExtension = path.extension(fileName);
      final cleanFileName = _sanitizeFileName(fileName);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final storagePath = '${currentUser.id}/additional_documents/${timestamp}_$cleanFileName';

      progressNotifier?.value = DocumentUploadProgress(
        fileName: fileName,
        progress: 0.3,
        status: DocumentUploadStatus.uploading,
      );

      // Upload zu Supabase Storage
      await _supabase.storage.from(_bucketName).uploadBinary(
        storagePath,
        fileBytes,
        fileOptions: FileOptions(
          cacheControl: '3600',
          upsert: false,
          contentType: _getMimeType(fileExtension),
        ),
      );

      progressNotifier?.value = DocumentUploadProgress(
        fileName: fileName,
        progress: 0.7,
        status: DocumentUploadStatus.processing,
      );

      // Metadaten in Datenbank speichern
      final documentData = {
        'user_id': currentUser.id,
        'file_name': fileName,
        'file_path': storagePath,
        'file_size': fileSize,
        'file_type': _getMimeType(fileExtension),
        'upload_date': DateTime.now().toIso8601String(),
        'is_active_for_applications': false,
      };

      final response = await _supabase
          .from('user_additional_documents')
          .insert(documentData)
          .select()
          .single();

      final document = AdditionalDocument.fromJson(response);

      progressNotifier?.value = DocumentUploadProgress(
        fileName: fileName,
        progress: 1.0,
        status: DocumentUploadStatus.completed,
      );

      return DocumentUploadResult(
        success: true,
        documentId: document.id,
        document: document,
      );
    } catch (e) {
      debugPrint('Fehler beim Upload des Dokuments: $e');
      
      progressNotifier?.value = DocumentUploadProgress(
        fileName: fileName,
        progress: 0.0,
        status: DocumentUploadStatus.failed,
        errorMessage: e.toString(),
      );

      return DocumentUploadResult(
        success: false,
        errorMessage: 'Upload fehlgeschlagen: ${e.toString()}',
      );
    }
  }

  /// Lädt alle Dokumente eines Benutzers
  Future<List<AdditionalDocument>> getUserDocuments() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('Benutzer nicht angemeldet');
      }

      final response = await _supabase
          .from('user_additional_documents')
          .select()
          .eq('user_id', currentUser.id)
          .order('upload_date', ascending: false);

      return response
          .map<AdditionalDocument>((json) => AdditionalDocument.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Fehler beim Laden der Dokumente: $e');
      rethrow;
    }
  }

  /// Aktualisiert den Status eines Dokuments für Bewerbungen
  Future<bool> updateDocumentApplicationStatus({
    required String documentId,
    required bool isActive,
  }) async {
    try {
      await _supabase
          .from('user_additional_documents')
          .update({
            'is_active_for_applications': isActive,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', documentId);

      return true;
    } catch (e) {
      debugPrint('Fehler beim Aktualisieren des Dokument-Status: $e');
      return false;
    }
  }

  /// Löscht ein Dokument
  Future<bool> deleteDocument(String documentId) async {
    try {
      // Erst Dokument-Info laden
      final response = await _supabase
          .from('user_additional_documents')
          .select('file_path')
          .eq('id', documentId)
          .single();

      final filePath = response['file_path'] as String;

      // Datei aus Storage löschen
      await _supabase.storage.from(_bucketName).remove([filePath]);

      // Eintrag aus Datenbank löschen
      await _supabase
          .from('user_additional_documents')
          .delete()
          .eq('id', documentId);

      return true;
    } catch (e) {
      debugPrint('Fehler beim Löschen des Dokuments: $e');
      return false;
    }
  }

  /// Lädt eine Datei herunter
  Future<Uint8List?> downloadDocument(String documentId) async {
    try {
      final response = await _supabase
          .from('user_additional_documents')
          .select('file_path')
          .eq('id', documentId)
          .single();

      final filePath = response['file_path'] as String;

      return await _supabase.storage.from(_bucketName).download(filePath);
    } catch (e) {
      debugPrint('Fehler beim Herunterladen des Dokuments: $e');
      return null;
    }
  }

  /// Lädt aktive Dokumente für Bewerbungen
  Future<List<AdditionalDocument>> getActiveDocumentsForApplications() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return [];
      }

      final response = await _supabase
          .from('user_additional_documents')
          .select()
          .eq('user_id', currentUser.id)
          .eq('is_active_for_applications', true)
          .order('upload_date', ascending: false);

      return response
          .map<AdditionalDocument>((json) => AdditionalDocument.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Fehler beim Laden aktiver Dokumente: $e');
      return [];
    }
  }

  // Private Hilfsmethoden

  Future<DocumentUploadResult> _validateFile(String filePath, String fileName) async {
    final file = File(filePath);
    
    // Datei existiert?
    if (!await file.exists()) {
      return const DocumentUploadResult(
        success: false,
        errorMessage: 'Datei nicht gefunden',
      );
    }

    // Dateigröße prüfen
    final fileSize = await file.length();
    if (fileSize > _maxFileSize) {
      return DocumentUploadResult(
        success: false,
        errorMessage: 'Datei zu groß (max. ${(_maxFileSize / 1024 / 1024).toStringAsFixed(1)} MB)',
      );
    }

    // Dateityp prüfen
    final extension = path.extension(fileName).toLowerCase().replaceFirst('.', '');
    if (!SupportedFileType.allowedExtensions.contains(extension)) {
      return DocumentUploadResult(
        success: false,
        errorMessage: 'Dateityp nicht unterstützt (erlaubt: ${SupportedFileType.allowedExtensions.join(', ')})',
      );
    }

    return const DocumentUploadResult(success: true);
  }

  Future<int> _getDocumentCount(String userId) async {
    final response = await _supabase
        .from('user_additional_documents')
        .select('id')
        .eq('user_id', userId);
    
    return response.length;
  }

  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\-_\.]'), '_')
        .replaceAll(RegExp(r'_+'), '_');
  }

  String _getMimeType(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }
}

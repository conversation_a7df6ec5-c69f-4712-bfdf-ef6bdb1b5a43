import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/entities/extracted_job_text.dart';
import '../../core/logging/app_logger.dart';

/// Service für die Extraktion und Speicherung von Job-Texten
/// Implementiert intelligentes Caching und Deduplizierung
class TextExtractionService {
  final SupabaseClient _supabaseClient;
  final AppLogger _log = AppLogger('TextExtractionService');
  
  // Cache für bereits extrahierte Texte (In-Memory)
  final Map<String, ExtractedJobText> _memoryCache = {};
  
  // HTTP Client mit Timeout
  final http.Client _httpClient = http.Client();
  
  TextExtractionService(this._supabaseClient);

  /// Extrahiert und speichert Job-Text mit intelligentem Caching
  Future<ExtractedJobText?> extractAndCacheJobText(
    JobEntity job, {
    String? sourceType, // 'favorite' oder 'applied_job'
    DateTime? sourceTimestamp, // Wann zu Favoriten/beworben hinzugefügt
  }) async {
    try {
      // 1. Prüfe Memory-Cache
      if (_memoryCache.containsKey(job.id)) {
        _log.i('Job ${job.id} aus Memory-Cache geladen');
        await _updateLastAccessed(job.id);
        return _memoryCache[job.id];
      }

      // 2. Prüfe Supabase-Cache
      final cachedText = await _getCachedText(job.id);
      if (cachedText != null) {
        _log.i('Job ${job.id} aus Supabase-Cache geladen');
        _memoryCache[job.id] = cachedText;
        await _updateLastAccessed(job.id);
        return cachedText;
      }

      // 3. Extrahiere neuen Text
      _log.i('Extrahiere neuen Text für Job ${job.id}');
      final extractedText = await _extractTextFromJob(job);
      
      if (extractedText != null) {
        // 4. Speichere in Supabase und Memory-Cache
        await _saveCachedText(extractedText, sourceType: sourceType, sourceTimestamp: sourceTimestamp);
        _memoryCache[job.id] = extractedText;
        _log.i('Job ${job.id} erfolgreich extrahiert und gecacht');
      }

      return extractedText;
    } catch (e, stackTrace) {
      _log.e('Fehler bei Text-Extraktion für Job ${job.id}: $e', stackTrace);
      return null;
    }
  }

  /// Extrahiert Text aus JobEntity oder URL
  Future<ExtractedJobText?> _extractTextFromJob(JobEntity job) async {
    String extractedText = '';
    List<String> requirements = [];
    List<String> benefits = [];
    Map<String, dynamic> contactInfo = {};

    // 1. Verwende bereits vorhandene Beschreibung aus JobEntity
    if (job.description.isNotEmpty) {
      extractedText = _cleanHtmlText(job.description);
      _log.d('Text aus JobEntity.description extrahiert: ${extractedText.length} Zeichen');
    }

    // 2. Falls URL vorhanden, versuche zusätzliche Extraktion
    if (job.sourceUrl != null && job.sourceUrl!.isNotEmpty) {
      try {
        final webText = await _extractTextFromUrl(job.sourceUrl!);
        if (webText.isNotEmpty) {
          // Kombiniere Texte, aber vermeide Duplikate
          final combinedText = _combineTexts(extractedText, webText);
          if (combinedText.length > extractedText.length) {
            extractedText = combinedText;
            _log.d('Text aus URL erweitert: ${extractedText.length} Zeichen');
          }
        }
      } catch (e) {
        _log.w('URL-Extraktion fehlgeschlagen für ${job.sourceUrl}: $e');
      }
    }

    // 3. Strukturiere den Text
    requirements = _extractRequirements(extractedText);
    benefits = _extractBenefits(extractedText);
    contactInfo = _extractContactInfo(extractedText);

    // 4. Erstelle ExtractedJobText
    if (extractedText.isNotEmpty) {
      return ExtractedJobText(
        jobId: job.id,
        sourceUrl: job.sourceUrl,
        extractedText: extractedText,
        jobTitle: job.title,
        companyName: job.companyName,
        location: job.location,
        requirements: requirements,
        benefits: benefits,
        contactInfo: contactInfo,
        contentHash: _generateContentHash(extractedText),
        contentSize: utf8.encode(extractedText).length,
      );
    }

    return null;
  }

  /// Extrahiert Text aus URL
  Future<String> _extractTextFromUrl(String url) async {
    try {
      final response = await _httpClient.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; JobBot/1.0)',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final document = html_parser.parse(response.body);
        return _extractTextFromHtml(document);
      } else {
        _log.w('HTTP ${response.statusCode} für URL: $url');
        return '';
      }
    } catch (e) {
      _log.w('Fehler beim Laden der URL $url: $e');
      return '';
    }
  }

  /// Extrahiert relevanten Text aus HTML-Dokument
  String _extractTextFromHtml(dom.Document document) {
    // Entferne Script und Style Tags
    document.querySelectorAll('script, style, nav, header, footer').forEach((element) {
      element.remove();
    });

    // Suche nach Job-relevanten Bereichen
    final jobSelectors = [
      '.job-description',
      '.job-content',
      '.stellenbeschreibung',
      '.job-details',
      '.position-description',
      '[class*="job"]',
      '[class*="stelle"]',
      'main',
      'article',
    ];

    String extractedText = '';
    
    for (final selector in jobSelectors) {
      final elements = document.querySelectorAll(selector);
      if (elements.isNotEmpty) {
        extractedText = elements.map((e) => e.text).join('\n\n');
        if (extractedText.length > 500) break; // Genug Text gefunden
      }
    }

    // Fallback: Gesamter Body-Text
    if (extractedText.length < 500) {
      final body = document.querySelector('body');
      if (body != null) {
        extractedText = body.text ?? '';
      }
    }

    return _cleanHtmlText(extractedText);
  }

  /// Bereinigt HTML-Text
  String _cleanHtmlText(String text) {
    return text
        .replaceAll(RegExp(r'<[^>]*>'), '') // HTML-Tags entfernen
        // Entferne HTML-Entities
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&hellip;', '...')
        .replaceAll('&mdash;', '—')
        .replaceAll('&ndash;', '–')
        // Entferne JavaScript-Fragmente und Browser-Meldungen
        .replaceAll(RegExp(r'JavaScript.*?aktivieren[^.]*\.?'), '')
        .replaceAll(RegExp(r'Browser.*?unterstützt[^.]*\.?'), '')
        .replaceAll(RegExp(r'Cookies.*?aktiviert[^.]*\.?'), '')
        .replaceAll(RegExp(r'deaktiviert'), '')
        // Entferne Platzhalter und komische Zeichen
        .replaceAll(RegExp(r'\$\d+'), '') // $1, $2, etc.
        .replaceAll(RegExp(r'\\\$\d+'), '') // \$1, \$2, etc.
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '') // Kontrollzeichen
        // Entferne weitere HTML-Artefakte
        .replaceAll(RegExp(r'&[a-zA-Z]+;'), '') // Alle HTML-Entities
        .replaceAll(RegExp(r'&#\d+;'), '') // Numerische HTML-Entities
        // Entferne JavaScript-Reste
        .replaceAll(RegExp(r'function\s*\([^)]*\)\s*\{[^}]*\}'), '')
        .replaceAll(RegExp(r'var\s+\w+\s*='), '')
        .replaceAll(RegExp(r'document\.\w+'), '')
        .replaceAll(RegExp(r'\s+'), ' ') // Mehrfache Leerzeichen
        .replaceAll(RegExp(r'\n\s*\n'), '\n\n') // Mehrfache Zeilenumbrüche
        .trim();
  }

  /// Kombiniert zwei Texte und entfernt Duplikate
  String _combineTexts(String text1, String text2) {
    if (text1.isEmpty) return text2;
    if (text2.isEmpty) return text1;
    
    // Einfache Duplikat-Erkennung basierend auf gemeinsamen Sätzen
    final sentences1 = text1.split(RegExp(r'[.!?]+'));
    final sentences2 = text2.split(RegExp(r'[.!?]+'));
    
    final uniqueSentences = <String>{};
    uniqueSentences.addAll(sentences1.where((s) => s.trim().length > 20));
    uniqueSentences.addAll(sentences2.where((s) => s.trim().length > 20));
    
    return uniqueSentences.join('. ').trim();
  }

  /// Extrahiert Anforderungen aus Text
  List<String> _extractRequirements(String text) {
    final requirements = <String>[];
    final patterns = [
      RegExp(r'(?:Anforderungen?|Requirements?|Qualifikationen?)[:\-]?\s*(.+?)(?=\n\n|\n[A-Z]|$)', 
             caseSensitive: false, dotAll: true),
      RegExp(r'(?:Sie bringen mit|Ihr Profil|Wir erwarten)[:\-]?\s*(.+?)(?=\n\n|\n[A-Z]|$)', 
             caseSensitive: false, dotAll: true),
    ];

    for (final pattern in patterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final content = match.group(1)?.trim();
        if (content != null && content.isNotEmpty) {
          // Aufteilen in einzelne Punkte
          final points = content.split(RegExp(r'[•\-\*]\s*|(?:\n|^)\s*[\d]+\.?\s*'))
              .where((point) => point.trim().length > 10)
              .map((point) => point.trim())
              .toList();
          requirements.addAll(points);
        }
      }
    }

    return requirements.take(10).toList(); // Maximal 10 Anforderungen
  }

  /// Extrahiert Benefits aus Text
  List<String> _extractBenefits(String text) {
    final benefits = <String>[];
    final patterns = [
      RegExp(r'(?:Benefits?|Wir bieten|Vorteile|Leistungen)[:\-]?\s*(.+?)(?=\n\n|\n[A-Z]|$)', 
             caseSensitive: false, dotAll: true),
      RegExp(r'(?:Das erwartet Sie|Ihre Vorteile)[:\-]?\s*(.+?)(?=\n\n|\n[A-Z]|$)', 
             caseSensitive: false, dotAll: true),
    ];

    for (final pattern in patterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final content = match.group(1)?.trim();
        if (content != null && content.isNotEmpty) {
          final points = content.split(RegExp(r'[•\-\*]\s*|(?:\n|^)\s*[\d]+\.?\s*'))
              .where((point) => point.trim().length > 10)
              .map((point) => point.trim())
              .toList();
          benefits.addAll(points);
        }
      }
    }

    return benefits.take(10).toList(); // Maximal 10 Benefits
  }

  /// Extrahiert Kontaktinformationen
  Map<String, dynamic> _extractContactInfo(String text) {
    final contactInfo = <String, dynamic>{};
    
    // E-Mail-Adressen
    final emailPattern = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
    final emails = emailPattern.allMatches(text).map((m) => m.group(0)).toList();
    if (emails.isNotEmpty) {
      contactInfo['emails'] = emails;
    }

    // Telefonnummern (deutsche Formate)
    final phonePattern = RegExp(r'(?:\+49|0)[1-9]\d{1,4}[\s\-/]?\d{1,8}');
    final phones = phonePattern.allMatches(text).map((m) => m.group(0)).toList();
    if (phones.isNotEmpty) {
      contactInfo['phones'] = phones;
    }

    return contactInfo;
  }

  /// Generiert Content-Hash für Änderungserkennung
  String _generateContentHash(String content) {
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Lädt gecachten Text aus Supabase
  Future<ExtractedJobText?> _getCachedText(String jobId) async {
    try {
      final response = await _supabaseClient
          .from('job_text_cache')
          .select()
          .eq('job_id', jobId)
          .maybeSingle();

      if (response != null) {
        return ExtractedJobText.fromJson(response);
      }
      return null;
    } catch (e) {
      _log.e('Fehler beim Laden des gecachten Texts für Job $jobId: $e');
      return null;
    }
  }

  /// Speichert extrahierten Text in Supabase
  Future<void> _saveCachedText(
    ExtractedJobText extractedText, {
    String? sourceType,
    DateTime? sourceTimestamp,
  }) async {
    try {
      // Speichere den extrahierten Text
      await _supabaseClient.from('job_text_cache').upsert({
        'job_id': extractedText.jobId,
        'source_url': extractedText.sourceUrl,
        'extracted_text': extractedText.extractedText,
        'job_title': extractedText.jobTitle,
        'company_name': extractedText.companyName,
        'location': extractedText.location,
        'requirements': extractedText.requirements,
        'benefits': extractedText.benefits,
        'contact_info': extractedText.contactInfo,
        'content_hash': extractedText.contentHash,
        'content_size': extractedText.contentSize,
        'extraction_method': 'html_parser',
      });

      // Aktualisiere Quell-Timestamps falls verfügbar
      if (sourceType != null && sourceTimestamp != null) {
        await _supabaseClient.rpc('update_job_text_cache_source_timestamps', params: {
          'p_job_id': extractedText.jobId,
          'p_source_type': sourceType,
          'p_source_timestamp': sourceTimestamp.toIso8601String(),
        });
      }
      _log.i('Text für Job ${extractedText.jobId} in Supabase gespeichert');
    } catch (e) {
      _log.e('Fehler beim Speichern des Texts für Job ${extractedText.jobId}: $e');
    }
  }

  /// Aktualisiert last_accessed Zeitstempel
  Future<void> _updateLastAccessed(String jobId) async {
    try {
      await _supabaseClient.rpc('update_job_text_cache_access', params: {
        'p_job_id': jobId,
      });
    } catch (e) {
      _log.w('Fehler beim Aktualisieren von last_accessed für Job $jobId: $e');
    }
  }

  /// Bereinigt Memory-Cache
  void clearMemoryCache() {
    _memoryCache.clear();
    _log.i('Memory-Cache geleert');
  }

  /// Bereinigt abgelaufene Einträge
  Future<int> cleanupExpiredEntries() async {
    try {
      final result = await _supabaseClient.rpc('cleanup_expired_job_text_cache');
      final deletedCount = result as int? ?? 0;
      _log.i('$deletedCount abgelaufene Cache-Einträge bereinigt');
      return deletedCount;
    } catch (e) {
      _log.e('Fehler bei der Cache-Bereinigung: $e');
      return 0;
    }
  }

  /// Dispose-Methode für Cleanup
  void dispose() {
    _httpClient.close();
    clearMemoryCache();
  }
}

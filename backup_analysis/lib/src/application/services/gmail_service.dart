import 'dart:convert';
import 'dart:io';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/gmail/v1.dart' as gmail;
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/entities/application_entity.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:extension_google_sign_in_as_googleapis_auth/extension_google_sign_in_as_googleapis_auth.dart';
import 'package:ki_test/src/utils/cv_storage_helper.dart';
import 'package:ki_test/src/application/services/additional_documents_service.dart';

/// Service für den Zugriff auf die Gmail API
class GmailService {
  final _log = getLogger('GmailService');
  final AdditionalDocumentsService _additionalDocumentsService = AdditionalDocumentsService();

  // Google Sign-In Instanz mit den benötigten Scopes für Gmail
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/gmail.compose',
    ],
    // Füge die Client-ID hinzu, um sicherzustellen, dass die Authentifizierung korrekt funktioniert
    clientId:
        Platform.isAndroid
            ? null // Auf Android wird die Client-ID aus google-services.json gelesen
            : '341017562213-tmfi513m0ja67bjhaqdb23cdqp46dqoe.apps.googleusercontent.com', // Nur für iOS
  );

  /// Sendet eine Bewerbungs-E-Mail über die Gmail API
  Future<ApplicationResult> sendApplicationEmail({
    required String recipientEmail,
    required String jobTitle,
    required String coverLetter,
    String? cvFilePath,
    String? companyName,
    String? jobId,
  }) async {
    try {
      _log.i(
        'Sende Bewerbungs-E-Mail an $recipientEmail für Stelle: $jobTitle',
      );

      // Überprüfe den Lebenslauf-Pfad
      if (cvFilePath != null && cvFilePath.isNotEmpty) {
        final cvFile = File(cvFilePath);
        if (await cvFile.exists()) {
          _log.i(
            'Lebenslauf gefunden: $cvFilePath (${await cvFile.length()} Bytes)',
          );
        } else {
          _log.w('Lebenslauf-Datei existiert nicht: $cvFilePath');

          // Versuche, den Lebenslauf mit dem CvStorageHelper zu finden
          try {
            final localCvPath = await CvStorageHelper.ensureLocalCvAvailable(
              null, // userId wird aus dem Pfad extrahiert
              cvFilePath,
              null, // keine Download-URL
            );

            if (localCvPath != null) {
              cvFilePath = localCvPath;
              _log.i('Lebenslauf mit CvStorageHelper gefunden: $cvFilePath');
            } else {
              _log.w('Konnte Lebenslauf nicht finden oder herunterladen');
            }
          } catch (cvError) {
            _log.e('Fehler beim Suchen des Lebenslaufs: $cvError');
          }
        }
      } else {
        _log.w('Kein Lebenslauf-Pfad angegeben');
      }

      // 1. Stelle sicher, dass der Benutzer bei Google angemeldet ist
      final GoogleSignInAccount? googleUser = await _ensureGoogleSignIn();
      if (googleUser == null) {
        return ApplicationResult(
          success: false,
          errorMessage:
              'Nicht bei Google angemeldet. Bitte melde dich an, um E-Mails zu senden.',
        );
      }

      // 2. Authentifiziere mit der Gmail API
      // Verwende die extension_google_sign_in_as_googleapis_auth, um einen authentifizierten HTTP-Client zu erstellen
      final httpClient = await _googleSignIn.authenticatedClient();
      if (httpClient == null) {
        _log.e('Konnte keinen authentifizierten HTTP-Client erstellen');
        return ApplicationResult(
          success: false,
          errorMessage:
              'Authentifizierungsfehler: Konnte keinen authentifizierten HTTP-Client erstellen',
        );
      }

      // 3. Erstelle die Gmail API-Instanz mit dem authentifizierten Client
      final gmailApi = gmail.GmailApi(httpClient);

      // 4. Erstelle die E-Mail-Nachricht
      final message = await _createEmailMessage(
        senderEmail: googleUser.email,
        senderName: googleUser.displayName ?? googleUser.email.split('@')[0],
        recipientEmail: recipientEmail,
        subject: 'Bewerbung für: $jobTitle',
        body: coverLetter,
        cvFilePath: cvFilePath,
      );

      // 5. Sende die E-Mail
      final response = await gmailApi.users.messages.send(
        message,
        'me', // 'me' bezieht sich auf den aktuell angemeldeten Benutzer
      );

      _log.i('E-Mail erfolgreich gesendet. Message ID: ${response.id}');

      // 6. Erstelle ein ApplicationEntity-Objekt für die Datenbank
      return ApplicationResult(
        success: true,
        applicationEntity: ApplicationEntity(
          id: response.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
          userId: googleUser.id,
          jobId: jobId ?? 'unknown',
          jobTitle: jobTitle,
          companyName: companyName ?? 'Unbekannt',
          recipientEmail: recipientEmail,
          status: 'gesendet',
          coverLetter: coverLetter,
          sentAt: DateTime.now(),
          lastUpdated: DateTime.now(),
        ),
      );
    } catch (e, stack) {
      _log.e('Fehler beim Senden der E-Mail: $e\n$stack');
      return ApplicationResult(
        success: false,
        errorMessage: 'Fehler beim Senden der E-Mail: ${e.toString()}',
      );
    }
  }

  /// Stellt sicher, dass der Benutzer bei Google angemeldet ist
  Future<GoogleSignInAccount?> _ensureGoogleSignIn() async {
    // Prüfe, ob der Benutzer bereits angemeldet ist
    GoogleSignInAccount? account = _googleSignIn.currentUser;

    // Wenn nicht, versuche eine stille Anmeldung
    account ??= await _googleSignIn.signInSilently();

    // Wenn das nicht funktioniert, fordere eine interaktive Anmeldung an
    if (account == null) {
      try {
        account = await _googleSignIn.signIn();
      } catch (e) {
        _log.e('Fehler bei der Google-Anmeldung: $e');
        return null;
      }
    }

    return account;
  }

  /// Erstellt eine E-Mail-Nachricht im RFC 2822-Format mit optionalem Anhang
  Future<gmail.Message> _createEmailMessage({
    required String senderEmail,
    required String senderName,
    required String recipientEmail,
    required String subject,
    required String body,
    String? cvFilePath,
  }) async {
    // Generiere eine eindeutige Boundary für den MIME-Multipart
    final boundary = 'boundary_${DateTime.now().millisecondsSinceEpoch}';

    // Sammle alle Anhänge (CV + zusätzliche Dokumente)
    List<Map<String, dynamic>> attachments = [];

    // 1. Lebenslauf hinzufügen
    if (cvFilePath != null && cvFilePath.isNotEmpty) {
      final file = File(cvFilePath);
      if (await file.exists()) {
        try {
          final attachmentBytes = await file.readAsBytes();
          attachments.add({
            'bytes': attachmentBytes,
            'filename': 'Lebenslauf.pdf',
            'contentType': 'application/pdf',
          });
          _log.i('Lebenslauf wird angehängt: Lebenslauf.pdf (${attachmentBytes.length} Bytes)');
        } catch (e) {
          _log.e('Fehler beim Lesen des Lebenslaufs: $e');
        }
      } else {
        _log.w('Lebenslauf-Datei existiert nicht: $cvFilePath');
      }
    }

    // 2. Zusätzliche Dokumente laden und hinzufügen
    try {
      final additionalDocs = await _additionalDocumentsService.getActiveDocumentsForApplications();
      _log.i('Gefundene zusätzliche Dokumente: ${additionalDocs.length}');

      for (final doc in additionalDocs) {
        try {
          final docBytes = await _additionalDocumentsService.downloadDocument(doc.id);
          if (docBytes != null) {
            attachments.add({
              'bytes': docBytes,
              'filename': doc.fileName,
              'contentType': doc.fileType,
            });
            _log.i('Zusätzliches Dokument wird angehängt: ${doc.fileName} (${docBytes.length} Bytes)');
          }
        } catch (e) {
          _log.w('Fehler beim Laden des Dokuments ${doc.fileName}: $e');
        }
      }
    } catch (e) {
      _log.w('Fehler beim Laden zusätzlicher Dokumente: $e');
    }

    final hasAttachments = attachments.isNotEmpty;

    // Kodiere den Betreff für nicht-ASCII-Zeichen
    String encodedSubject = _encodeHeaderValue(subject);
    _log.i('Betreff kodiert: $encodedSubject');

    // Erstelle die E-Mail-Header
    final headers = {
      'From': '$senderName <$senderEmail>',
      'To': recipientEmail,
      'Subject': encodedSubject,
      'MIME-Version': '1.0',
    };

    // Füge Content-Type je nach Anhängen hinzu
    if (hasAttachments) {
      headers['Content-Type'] = 'multipart/mixed; boundary=$boundary';
    } else {
      headers['Content-Type'] = 'text/plain; charset=UTF-8';
    }

    // Erstelle den E-Mail-Text
    String emailText = headers.entries
        .map((entry) => '${entry.key}: ${entry.value}')
        .join('\r\n');

    // Wenn es Anhänge gibt, erstelle eine Multipart-Nachricht
    if (hasAttachments) {
      emailText += '\r\n\r\n--$boundary\r\n';
      emailText += 'Content-Type: text/plain; charset=UTF-8\r\n\r\n';
      emailText += '$body\r\n\r\n';

      // Füge alle Anhänge hinzu
      for (final attachment in attachments) {
        final attachmentBytes = attachment['bytes'] as List<int>;
        final filename = attachment['filename'] as String;
        final contentType = attachment['contentType'] as String;

        emailText += '--$boundary\r\n';
        emailText += 'Content-Type: $contentType\r\n';
        emailText += 'Content-Disposition: attachment; filename="$filename"\r\n';
        emailText += 'Content-Transfer-Encoding: base64\r\n\r\n';

        // Kodiere den Anhang als Base64 und füge ihn hinzu
        final base64Attachment = base64.encode(attachmentBytes);

        // Teile den Base64-String in Zeilen von 76 Zeichen auf (RFC 2045)
        for (int i = 0; i < base64Attachment.length; i += 76) {
          int end = i + 76;
          if (end > base64Attachment.length) {
            end = base64Attachment.length;
          }
          emailText += '${base64Attachment.substring(i, end)}\r\n';
        }
        emailText += '\r\n';
      }

      // Schließe die Multipart-Nachricht ab
      emailText += '--$boundary--\r\n';
    } else {
      // Einfache Textnachricht ohne Anhang
      emailText += '\r\n\r\n$body';
    }

    // Kodiere die E-Mail als Base64 mit UTF-8
    final encodedEmail = base64Url.encode(utf8.encode(emailText));

    // Erstelle die Gmail-Nachricht
    return gmail.Message(raw: encodedEmail);
  }

  /// Kodiert einen Header-Wert gemäß RFC 2047 für nicht-ASCII-Zeichen
  String _encodeHeaderValue(String value) {
    // Prüfe, ob der Wert überhaupt nicht-ASCII-Zeichen enthält
    bool containsNonAscii = value.codeUnits.any((c) => c > 127);
    if (!containsNonAscii) {
      return value; // Keine Kodierung nötig
    }

    // Verwende MIME-Kodierung für nicht-ASCII-Zeichen
    // Format: =?charset?encoding?encoded-text?=
    // Wir verwenden UTF-8 als Zeichensatz und B für Base64-Kodierung

    // Teile den String in Wörter auf und kodiere jedes Wort einzeln
    List<String> words = value.split(' ');
    List<String> encodedWords = [];

    for (String word in words) {
      if (word.codeUnits.any((c) => c > 127)) {
        // Nur Wörter mit nicht-ASCII-Zeichen kodieren
        final encodedBytes = utf8.encode(word);
        final base64Value = base64.encode(encodedBytes);
        encodedWords.add("=?UTF-8?B?$base64Value?=");
      } else {
        // ASCII-Wörter unverändert lassen
        encodedWords.add(word);
      }
    }

    // Füge die kodierten Wörter wieder zusammen
    return encodedWords.join(' ');
  }
}

/// Ergebnis einer Bewerbung
class ApplicationResult {
  final bool success;
  final String? errorMessage;
  final ApplicationEntity? applicationEntity;

  ApplicationResult({
    required this.success,
    this.errorMessage,
    this.applicationEntity,
  });
}

/// Provider für den GmailService
final gmailServiceProvider = Provider<GmailService>((ref) {
  return GmailService();
});

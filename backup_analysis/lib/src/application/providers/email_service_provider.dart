import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/services/email_service.dart';

/// Provider für den E-Mail-Service
final emailServiceProvider = Provider<EmailService>((ref) {
  final supabase = ref.watch(supabaseClientProvider);
  final gmailService = ref.watch(gmailServiceProvider);
  return EmailService(supabase, gmailService);
});

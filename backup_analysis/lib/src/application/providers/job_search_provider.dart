import 'dart:async';
import 'dart:math';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart';
import '../../infrastructure/api/agentur_arbeit_api_client.dart';
import '../../infrastructure/api/job_api_client.dart';
import 'user_profile_provider.dart';
import '../../core/utils/logging.dart';
import 'services_providers.dart';

// -- Zustand für die Jobsuche --
class JobSearchState {
  final List<JobEntity> jobs;
  final bool isLoading;
  final bool isLoadingMore;
  final bool isSearching; // Neues Flag für Suche im Hintergrund
  final String? error;
  final int currentPage;
  final bool hasReachedMax;
  final String? currentKeywords; // Aktuelles Suchwort
  final Map<String, String> currentFilters; // Aktive Filter
  final String? currentSortOption; // Aktuelle Sortierung
  final bool isAusbildungMode; // Neues Flag für Ausbildungsmodus

  JobSearchState({
    this.jobs = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isSearching = false, // Standardwert für isSearching
    this.error,
    this.currentPage = 1,
    this.hasReachedMax = false,
    this.currentKeywords,
    this.currentFilters = const {},
    this.currentSortOption,
    this.isAusbildungMode = false, // Standard: reguläre Jobsuche
  });

  JobSearchState copyWith({
    List<JobEntity>? jobs,
    bool? isLoading,
    bool? isLoadingMore,
    bool? isSearching,
    String? error,
    int? currentPage,
    bool? hasReachedMax,
    String? currentKeywords,
    Map<String, String>? currentFilters,
    String? currentSortOption,
    bool? isAusbildungMode,
    bool clearError = false,
  }) {
    return JobSearchState(
      jobs: jobs ?? this.jobs,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isSearching: isSearching ?? this.isSearching,
      error: clearError ? null : error ?? this.error,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentKeywords: currentKeywords ?? this.currentKeywords,
      currentFilters: currentFilters ?? this.currentFilters,
      currentSortOption: currentSortOption ?? this.currentSortOption,
      isAusbildungMode: isAusbildungMode ?? this.isAusbildungMode,
    );
  }
}

// Provider für den Zustand des Filter-Panels
final jobSearchFilterPanelStateProvider = StateProvider<bool>((ref) => true);

// -- Notifier für die Jobsuche --
class JobSearchNotifier extends StateNotifier<JobSearchState> {
  final Ref _ref;
  JobApiClient? _jobApiClient; // Abhängigkeit vom API-Client, jetzt optional
  UserProfile _currentUserProfile = UserProfile.empty();
  Timer? _debounce;
  static const int _pageSize = 50;

  JobSearchNotifier(this._ref) : super(JobSearchState()) {
    // Initial den richtigen API-Client basierend auf dem isAusbildungMode-Status setzen
    _updateApiClient();

    _ref.listen<AsyncValue<UserProfile>>(userProfileProvider, (_, next) {
      next.whenData((profile) {
        // Aktualisiere das Profil, aber triggere nicht automatisch eine Suche
        // wenn wir bereits eine Standortsuche gestartet haben
        _currentUserProfile = profile;

        // Debug-Ausgabe der Profildaten
        final log = getLogger('JobSearchNotifier');
        log.d("Profil aktualisiert: ID=${profile.id}");
        log.d("Profil Skills: ${profile.skills?.join(', ') ?? 'keine'}");
        log.d(
          "Profil Berufserfahrung: ${profile.workExperience?.length ?? 0} Einträge",
        );
        log.d(
          "Profil Berufsbezeichnungen: ${profile.jobKeywords?.join(', ') ?? 'keine'}",
        );

        // Wenn wir gerade nach Profil sortieren oder Berufsbezeichnungen aktualisiert wurden, aktualisiere die Sortierung
        if (!state.isLoading) {
          // Zeige Debug-Informationen zu den Berufsbezeichnungen
          if (profile.jobKeywords?.isNotEmpty ?? false) {
            log.d("Berufsbezeichnungen im Profil gefunden:");
            for (final keyword in profile.jobKeywords!) {
              log.d(" - $keyword");
            }

            log.d(
              "Profil wurde aktualisiert mit Berufsbezeichnungen - Starte neue Suche mit Profil-Sortierung",
            );

            // Immer nach Profil sortieren, wenn Berufsbezeichnungen vorhanden sind
            performSearch(
              keywords: state.currentKeywords,
              filters: state.currentFilters,
              sortOption: 'profil', // Erzwinge Sortierung nach Profil
            );
          } else if (state.currentSortOption == 'profil') {
            // Wenn keine Berufsbezeichnungen vorhanden sind, aber nach Profil sortiert wird,
            // trotzdem die Sortierung aktualisieren
            log.d(
              "Profil wurde aktualisiert ohne Berufsbezeichnungen, aber Sortierung nach Profil ist aktiv - Aktualisiere Suche",
            );

            performSearch(
              keywords: state.currentKeywords,
              filters: state.currentFilters,
              sortOption: 'profil',
            );
          } else {
            log.d(
              "UserProfile loaded and updated in JobSearchNotifier. No automatic search triggered.",
            );
          }
        } else {
          log.d(
            "UserProfile loaded but JobSearchNotifier is currently loading. No automatic search triggered.",
          );
        }
      });
    });
  }

  // Methode zum Aktualisieren des API-Clients basierend auf dem isAusbildungMode-Flag
  void _updateApiClient() {
    if (state.isAusbildungMode) {
      _jobApiClient = _ref.read(ausbildungArbeitApiClientProvider);
    } else {
      _jobApiClient = _ref.read(agenturArbeitApiClientProvider);
    }
  }

  // Methode zum Umschalten zwischen Jobsuche und Ausbildungssuche
  void toggleAusbildungMode({bool? isAusbildungMode}) {
    final newAusbildungMode = isAusbildungMode ?? !state.isAusbildungMode;

    // Wenn sich der Modus nicht ändert, nichts tun
    if (newAusbildungMode == state.isAusbildungMode) return;

    // Debug-Ausgabe
    final log = getLogger('JobSearchNotifier.toggleAusbildungMode');
    log.d("Wechsel zu ${newAusbildungMode ? 'Ausbildungssuche' : 'Jobsuche'}");

    // State aktualisieren
    state = state.copyWith(
      isAusbildungMode: newAusbildungMode,
      isLoading: true,
      jobs: [],
      currentPage: 1,
      hasReachedMax: false,
      clearError: true,
    );

    // API-Client aktualisieren
    _updateApiClient();

    // Neue Suche auslösen
    performSearch(
      keywords: state.currentKeywords,
      filters: state.currentFilters,
      sortOption: state.currentSortOption,
    );
  }

  void triggerSearch({
    String? keywords,
    Map<String, String>? filters,
    String? sortOption,
  }) {
    // Debug-Ausgabe
    final log = getLogger('JobSearchNotifier.triggerSearch');
    log.d("triggerSearch aufgerufen mit:");
    log.d("- Keywords: $keywords");
    log.d("- Filters: $filters");
    log.d("- SortOption: $sortOption");

    // Effektive Werte bestimmen (übergebene oder aus dem State)
    final effectiveFilters = filters ?? state.currentFilters;
    final effectiveSortOption = sortOption ?? state.currentSortOption;
    final effectiveKeywords = keywords ?? state.currentKeywords;

    // Prüfen, ob sich die Werte tatsächlich geändert haben (tieferer Vergleich)
    final bool filtersChanged =
        filters != null &&
        filters.toString() !=
            state.currentFilters.toString(); // Tieferer Vergleich
    final bool sortOptionChanged =
        sortOption != null && sortOption != state.currentSortOption;
    final bool keywordsChanged =
        keywords != null && keywords != state.currentKeywords;
    final bool anyParamChanged =
        filtersChanged || sortOptionChanged || keywordsChanged;

    log.d("Änderungen erkannt:");
    log.d("- Filters geändert: $filtersChanged");
    log.d("- SortOption geändert: $sortOptionChanged");
    log.d("- Keywords geändert: $keywordsChanged");
    log.d("- Irgendein Parameter geändert: $anyParamChanged");

    // Wenn sich nichts geändert hat, keine neue Suche auslösen
    if (!anyParamChanged && !state.isLoading) {
      log.d("Keine Änderungen erkannt - keine neue Suche nötig");
      return;
    }

    // Wenn ein Debounce-Timer aktiv ist, abbrechen
    if (_debounce?.isActive ?? false) {
      log.d("Aktiver Debounce-Timer abgebrochen");
      _debounce?.cancel();
    }

    // Zustand SOFORT aktualisieren, aber isLoading nur auf true setzen, wenn keine Jobs vorhanden sind
    // Dadurch bleibt die bestehende Liste sichtbar, während neue Ergebnisse geladen werden
    if (anyParamChanged) {
      state = state.copyWith(
        // Nur isLoading=true setzen, wenn keine Jobs vorhanden sind oder es sich um eine komplett neue Suche handelt
        isLoading:
            state.jobs.isEmpty || (keywordsChanged && keywords.isEmpty == true),
        isSearching: true, // Neues Flag für Suche im Hintergrund
        currentKeywords: effectiveKeywords,
        currentFilters: effectiveFilters,
        currentSortOption: effectiveSortOption,
        clearError: true, // Fehler löschen, da neue Suche startet
      );

      log.d("State aktualisiert:");
      log.d("- isLoading: ${state.isLoading}");
      log.d("- isSearching: ${state.isSearching}");
      log.d("- currentFilters: ${state.currentFilters}");
      log.d("- currentSortOption: ${state.currentSortOption}");
    }

    // Bei Änderung des Umkreises (distance) sofort suchen, ohne Debounce
    if (filtersChanged &&
        filters.containsKey('distance') &&
        filters['distance'] != state.currentFilters['distance']) {
      log.d("Umkreis geändert - führe Suche sofort aus");
      performSearch(
        keywords: effectiveKeywords,
        filters: effectiveFilters,
        sortOption: effectiveSortOption,
      );
    } else {
      // Sonst mit längerem Debounce suchen (500ms statt 300ms)
      log.d("Starte Debounce-Timer (500ms)");
      _debounce = Timer(const Duration(milliseconds: 500), () {
        log.d("Debounce-Timer abgelaufen - führe Suche aus");
        performSearch(
          keywords: effectiveKeywords,
          filters: effectiveFilters,
          sortOption: effectiveSortOption,
        );
      });
    }
  }

  Future<void> performSearch({
    String? keywords,
    Map<String, String>? filters,
    String? sortOption,
  }) async {
    // Wenn kein API-Client vorhanden ist, abbrechen
    if (_jobApiClient == null) {
      state = state.copyWith(
        error: "API-Client ist nicht initialisiert",
        isLoading: false,
        isSearching: false,
      );
      return;
    }

    // WICHTIG: Wenn direkt performSearch aufgerufen wird, müssen wir die übergebenen Parameter verwenden
    // und nicht die aus dem State, da sie sonst ignoriert werden
    final effectiveFilters = filters ?? state.currentFilters;
    final effectiveSortOption = sortOption ?? state.currentSortOption;
    final effectiveKeywords = keywords ?? state.currentKeywords;

    // Debug-Ausgabe
    final log = getLogger('JobSearchNotifier.performSearch');
    log.d("performSearch aufgerufen mit:");
    log.d("- Keywords: $keywords (effektiv: $effectiveKeywords)");
    log.d("- Filters: $filters (effektiv: $effectiveFilters)");
    log.d("- SortOption: $sortOption (effektiv: $effectiveSortOption)");
    log.d("- isAusbildungMode: ${state.isAusbildungMode}");

    // Rest der Methode bleibt unverändert...
    // ...

    // Setze State-Werte, die für die API-Anfrage benötigt werden
    state = state.copyWith(
      isLoading: true,
      currentPage: 1,
      currentFilters: effectiveFilters,
      currentKeywords: effectiveKeywords,
      currentSortOption: effectiveSortOption,
      clearError: true,
    );

    try {
      // Anpassung an die API: Jobs suchen mit dem aktuellen API-Client
      final jobs = await _jobApiClient!.searchJobs(
        userProfile: _currentUserProfile,
        keywords: effectiveKeywords,
        page: 1,
        limit: _pageSize,
        location: effectiveFilters['location'],
        distance: effectiveFilters['distance'],
        industry: effectiveFilters['industry'],
      );

      // Verarbeite die Jobs nach lokalen Filterkriterien
      final processedJobs = await _processJobs(jobs, effectiveFilters);

      state = state.copyWith(
        jobs: processedJobs,
        isLoading: false,
        isSearching: false,
        hasReachedMax: jobs.length < _pageSize,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
        isSearching: false,
      );
    }
  }

  Future<void> loadMoreJobs() async {
    if (state.isLoading ||
        state.isLoadingMore ||
        state.isSearching ||
        state.hasReachedMax) {
      return;
    }

    // Wichtig: Setze isLoadingMore auf true, aber behalte die bestehenden Jobs bei
    state = state.copyWith(isLoadingMore: true, clearError: true);
    final nextPage = state.currentPage + 1;

    // Speichere die aktuellen Jobs, um sie später wiederherzustellen, falls ein Fehler auftritt
    final currentJobs = state.jobs;

    try {
      final moreJobs = await _jobApiClient!.searchJobs(
        userProfile: _currentUserProfile,
        keywords: state.currentKeywords, // Aktuelle Keywords verwenden
        page: nextPage,
        limit: _pageSize,
        location: state.currentFilters['location'], // Aktuelle Filter verwenden
        industry: state.currentFilters['industry'],
        distance: state.currentFilters['distance'],
      );

      // Keine Ergebnisse mehr verfügbar
      if (moreJobs.isEmpty) {
        state = state.copyWith(isLoadingMore: false, hasReachedMax: true);
        return;
      }

      // Jobs sortieren und/oder filtern basierend auf den aktuellen State-Einstellungen
      final processedNewJobs = await _processJobs(
        moreJobs,
        state.currentFilters,
      ); // _processJobs berücksichtigt state.currentFilters

      // Kombiniere alte und neue gefilterte Jobs
      var combinedJobs = [...currentJobs, ...processedNewJobs];

      // Sortieren basierend auf der ausgewählten Option
      final loadMoreLog = getLogger('JobSearchNotifier.loadMoreJobs');

      switch (state.currentSortOption) {
        case 'profil':
          loadMoreLog.d(
            "Sortiere ${combinedJobs.length} kombinierte Jobs nach Profil-Relevanz...",
          );
          if (_currentUserProfile != UserProfile.empty()) {
            // Hier nur die neuen Jobs sortieren und dann mit den alten kombinieren
            final sortedNewJobs = await _sortByProfileMatching(
              processedNewJobs,
              _currentUserProfile,
            );

            // Füge die sortierten neuen Jobs zu den bestehenden hinzu
            // Anstatt die gesamte Liste neu zu sortieren
            combinedJobs = [...currentJobs, ...sortedNewJobs];

            loadMoreLog.d("Sortierung nach Profil-Relevanz abgeschlossen.");
          } else {
            loadMoreLog.w(
              "Profil noch nicht geladen, Sortierung übersprungen.",
            );
          }
          break;

        case 'entfernung':
          loadMoreLog.d("Sortiere neue Jobs nach Entfernung...");
          // Nur die neuen Jobs sortieren
          final sortedNewJobs = _sortByDistance(processedNewJobs);

          // Füge die sortierten neuen Jobs zu den bestehenden hinzu
          combinedJobs = [...currentJobs, ...sortedNewJobs];

          loadMoreLog.d("Sortierung nach Entfernung abgeschlossen.");
          break;

        default:
          loadMoreLog.d("Standard-Sortierung beibehalten (API-Sortierung).");
          break;
      }

      // Finalen State setzen mit der kombinierten Liste
      // Wichtig: Wir ersetzen nicht die gesamte Liste, sondern fügen nur neue Jobs hinzu
      state = state.copyWith(
        jobs: List<JobEntity>.from(combinedJobs),
        isLoadingMore: false,
        hasReachedMax: moreJobs.length < _pageSize,
        currentPage: nextPage,
      );
    } catch (e) {
      // print('Error loading more jobs: $e\n$stackTrace');
      // Bei Fehler den vorherigen Zustand wiederherstellen
      state = state.copyWith(
        error: e.toString(),
        isLoadingMore: false,
        jobs: currentJobs, // Wichtig: Behalte die bestehenden Jobs bei
      );

      // 3 Sekunden warten und dann automatisch erneut versuchen, wenn API-Limit erreicht wurde
      if (e.toString().contains('rate limit') ||
          e.toString().contains('too many requests')) {
        Timer(const Duration(seconds: 3), () {
          loadMoreJobs(); // Erneut versuchen
        });
      }
    }
  }

  void applyFilters(Map<String, String> newFilters) {
    // Aktuelle Filter als Basis verwenden
    final combinedFilters = {...state.currentFilters};

    // Debug-Ausgabe
    final log = getLogger('JobSearchNotifier.applyFilters');
    log.d("Aktuelle Filter vor Update: $combinedFilters");
    log.d("Neue Filter: $newFilters");

    // Prüfe jeden neuen Filter
    newFilters.forEach((key, value) {
      if (value.isEmpty) {
        // Wenn der Wert leer ist, entferne den Filter
        combinedFilters.remove(key);
      } else {
        // Sonst aktualisiere/füge ihn hinzu
        combinedFilters[key] = value;
      }
    });

    log.d("Aktualisierte Filter: $combinedFilters");
    log.d("Aktuelle Sortierung: ${state.currentSortOption}");

    // Führe die Suche mit aktualisierten Filtern und aktueller Sortierung durch
    performSearch(
      filters: combinedFilters,
      sortOption:
          state
              .currentSortOption, // Wichtig: Behalte die aktuelle Sortierung bei
    );
  }

  void applySortOption(String sortOption) {
    // Debug-Ausgabe
    final log = getLogger('JobSearchNotifier.applySortOption');
    log.d("Aktualisiere Sortierung zu: $sortOption");
    log.d("Aktuelle Filter: ${state.currentFilters}");

    // Sortierung ändern, aber aktuelle Filter beibehalten
    performSearch(
      sortOption: sortOption,
      filters:
          state.currentFilters, // Wichtig: Behalte die aktuellen Filter bei
    );
  }

  // Methode zur Verarbeitung der Jobs basierend auf Filtern und Sortieroptionen
  Future<List<JobEntity>> _processJobs(
    List<JobEntity> jobs,
    Map<String, String> filters,
  ) async {
    List<JobEntity> processedJobs = List.from(jobs);

    // Hole aktuelle Filter und Sortieroption aus dem State
    final sortOption = state.currentSortOption;
    final bool onlyHelperJobs = filters['onlyHelperJobs'] == 'true';
    final bool noExperienceJobsFilter = filters['noExperienceJobs'] == 'true';

    // print("### _processJobs: Verarbeite ${jobs.length} Jobs mit Filtern: $filters, Sortierung: $sortOption ###");

    // Wenn die Liste leer ist, nichts zu tun
    if (processedJobs.isEmpty) return [];

    // Schritt 1: Filtern nach Helferjobs ODER Jobs ohne Erfahrung (wenn Filter aktiv)
    if (onlyHelperJobs || noExperienceJobsFilter) {
      List<JobEntity> helperJobs = [];
      List<JobEntity> noExperienceJobsFound = []; // Umbenannt für Klarheit

      for (var job in processedJobs) {
        final isHelper = await _isHelperJob(
          job,
        ); // Muss async sein wegen _checkPartialMatch
        final isNoExperience = await _isNoExperienceJob(
          job,
        ); // Muss async sein wegen _checkPartialMatch

        if (onlyHelperJobs && isHelper) {
          helperJobs.add(job);
        } else if (noExperienceJobsFilter && (isNoExperience || isHelper)) {
          // Helfer implizit ohne Erfahrung
          // Verhindere Duplikate, falls ein Job beides ist UND beide Filter aktiv wären
          if (!helperJobs.contains(job)) {
            noExperienceJobsFound.add(job);
          }
        }
      }

      // Kombiniere die gefilterten Listen
      if (onlyHelperJobs) {
        processedJobs = helperJobs;
        // print("### Nach Filter 'onlyHelperJobs' verbleiben ${processedJobs.length} Jobs ###");
      } else {
        // noExperienceJobsFilter muss true sein
        processedJobs = noExperienceJobsFound;
        // print("### Nach Filter 'noExperienceJobs' verbleiben ${processedJobs.length} Jobs ###");
      }
    }

    // Wenn nach Filterung die Liste leer ist, return
    if (processedJobs.isEmpty) {
      // print("### _processJobs: Nach Filterung keine Jobs mehr übrig. ###");
      return [];
    }

    // Schritt 2: Sortieren (falls aktiviert) - ENTFERNT! Sortierung passiert jetzt in performSearch/loadMoreJobs
    /*
    final log = getLogger('JobSearchNotifier._processJobs');
    log.d("_processJobs: Prüfe Sortierung. Aktuelle Option: $sortOption");
    if (sortOption == 'profil' && _currentUserProfile != null) {
      // print("### _processJobs: Sortiere ${processedJobs.length} Jobs nach Profil-Übereinstimmung... ###");
      processedJobs = await _sortByProfileMatching(processedJobs, _currentUserProfile!);
      // print("### _processJobs: Sortierung abgeschlossen. ###");
    } else {
       // print("### _processJobs: Keine Sortierung nach Profil angewendet. ###");
    }
    */

    // print("### _processJobs: Abschluss (nur Filterung) mit ${processedJobs.length} Jobs. ###");
    return processedJobs;
  }

  Future<bool> _isHelperJob(JobEntity job) async {
    final titleLower = job.title.toLowerCase();
    final descLower = job.description.toLowerCase();
    final combinedText = [titleLower, descLower];

    // Direkte Phrasen-Prüfung
    final helperPhrases = [
      'helfer',
      'aushilfe',
      'hilfskraft',
      'mitarbeiter produktion',
      'produktionshelfer',
      'lagerhelfer',
      'kommissionierer',
      'verpacker',
      'sortierer',
    ];
    if (helperPhrases.any(
      (phrase) => combinedText.any((text) => text.contains(phrase)),
    )) {
      _logMatch(
        job,
        'Helper Phrase Match',
        helperPhrases.firstWhere(
          (phrase) => combinedText.any((text) => text.contains(phrase)),
        ),
      );
      return true;
    }

    // Keyword-Prüfung (ganze Wörter)
    final helperKeywordsExact =
        _helperKeywords; // Aus der Liste am Ende der Datei
    final wordsInJob =
        combinedText.expand((text) => text.split(RegExp(r'\W+'))).toSet();
    if (helperKeywordsExact.any((keyword) => wordsInJob.contains(keyword))) {
      _logMatch(
        job,
        'Helper Keyword Exact Match',
        helperKeywordsExact.firstWhere(
          (keyword) => wordsInJob.contains(keyword),
        ),
      );
      return true;
    }

    // Partielle Keyword-Prüfung (falls keine exakten Treffer)
    if (await _checkPartialMatch(
      job,
      combinedText,
      helperKeywordsExact,
      'Helper Keyword Partial Match',
    )) {
      return true;
    }

    return false;
  }

  Future<bool> _isNoExperienceJob(JobEntity job) async {
    final titleLower = job.title.toLowerCase();
    final descriptionLower = job.description.toLowerCase();
    final combinedText = [titleLower, descriptionLower];

    // Erweiterte Liste der Jobs, die typischerweise keine Erfahrung erfordern
    final List<String> noExperienceJobs = [
      // Erweiterte Liste der häufigsten Jobs ohne Erfahrung
      'helfer',
      'aushilfe',
      'küchenhilfe',
      'servicekraft',
      'kellner',
      'spülkraft',
      'reinigungskraft',
      'putzkraft',
      'lagerhelfer',
      'produktionshelfer',
      'fahrer',
      'lieferfahrer', 'auslieferer', 'verkäufer', 'kassierer', 'regalauffüller',
      'minijob',
      'nebenjob',
      'wäscher',
      'sortierer',
      'packer',
      'kommissionierer',
      'zimmermädchen',
      'roomboy',
      'telefonist',
      'promoter',
      'hostess',
      'bedienung',
      'mitarbeiter', 'aushilfskraft', 'teilzeitkraft', 'kurierfahrer', 'bote',
      'lagerist',
      'produktionsmitarbeiter',
      'hilfskraft',
      'assistent',
      'werkstudent',
      'ferienjob',
      'studentische',
      'studentischer',
      'ferienkraft',
      'saisonkraft',
    ];

    // Prüfe auf typische Berufe ohne Erfahrung - SOWOHL IM TITEL ALS AUCH IN DER BESCHREIBUNG
    for (final job in noExperienceJobs) {
      if (titleLower.contains(job) || descriptionLower.contains(job)) {
        // print("### Job ohne Erfahrung erkannt (Berufsbezeichnung gefunden): '$job'");
        return true;
      }
    }

    // Verbesserte Prüfung für Metadaten mit stärkerer Gewichtung
    final berufserfahrungMeta =
        (job.metadata['berufserfahrung'] ?? '').toLowerCase();
    final ausbildungMeta = (job.metadata['ausbildung'] ?? '').toLowerCase();
    final qualifikationMeta =
        (job.metadata['qualifikation'] ?? '').toLowerCase();

    if (berufserfahrungMeta.contains('keine') ||
        berufserfahrungMeta.contains('nicht erforderlich') ||
        berufserfahrungMeta.contains('ohne') ||
        berufserfahrungMeta.contains('wünschenswert') ||
        berufserfahrungMeta.contains('von vorteil') ||
        ausbildungMeta.contains('keine') ||
        ausbildungMeta.contains('nicht erforderlich') ||
        ausbildungMeta.contains('ohne') ||
        ausbildungMeta.contains('wünschenswert') ||
        ausbildungMeta.contains('von vorteil') ||
        qualifikationMeta.contains('keine') ||
        qualifikationMeta.contains('nicht erforderlich') ||
        qualifikationMeta.contains('wünschenswert') ||
        qualifikationMeta.contains('von vorteil')) {
      // print("### Job ohne Erfahrung erkannt durch Metadaten: Berufserfahrung='$berufserfahrungMeta', Ausbildung='$ausbildungMeta', Qualifikation='$qualifikationMeta'");
      return true;
    }

    // Erweiterte Liste der Phrasen, die auf Jobs ohne Erfahrung hindeuten
    final noExperiencePhrases = [
      'keine vorkenntnisse',
      'keine erfahrung',
      'ohne vorkenntnisse',
      'ohne erfahrung',
      'einarbeitung erfolgt',
      'wir lernen sie an',
      'berufseinsteiger',
      'quereinsteiger',
      'studentenjob',
      'nebenjob',
      'minijob',
      'geringfügige beschäftigung',
      'teilzeit - vormittag',
      'teilzeit - nachmittag',
      'teilzeit - abend',
      'helfer',
      'keine ausbildung',
      'keine berufserfahrung',
      'keine kenntnisse',
      'keine qualifikation',
      'angelernt',
      'einarbeitung',
      'schulung',
      'training on the job',
      'einweisung',
      'keine vorkenntnisse erforderlich',
      'keine erfahrung erforderlich',
      'keine ausbildung erforderlich',
      'erfahrung nicht erforderlich',
      'ausbildung nicht erforderlich',
      'vorkenntnisse nicht erforderlich',
      'erfahrung von vorteil',
      'erfahrung wünschenswert',
      'ausbildung wünschenswert',
      'einstieg',
      'einstiegsmöglichkeit',
      'einstiegsposition',
      'entry level',
    ];

    if (noExperiencePhrases.any(
      (phrase) => combinedText.any((text) => text.contains(phrase)),
    )) {
      _logMatch(
        job,
        'No Experience Phrase Match',
        noExperiencePhrases.firstWhere(
          (phrase) => combinedText.any((text) => text.contains(phrase)),
        ),
      );
      return true;
    }

    // Keyword-Prüfung (ganze Wörter)
    final noExperienceKeywordsExact = [
      'einsteiger',
      'anfänger',
      'ungelernt',
      'schüler',
      'praktikum',
      'aushilfe',
      'basis',
      'training',
      'junior',
      'student',
      'teilzeit',
      'minijob',
      'nebenjob',
      'ferienjob',
      'aushilfsjob',
      'quereinsteiger',
      'berufseinsteiger',
      'einstieg',
      'angelernt',
      'anlernen',
      'einarbeiten',
      'schulung',
      'einweisung',
    ];

    final wordsInJob =
        combinedText.expand((text) => text.split(RegExp(r'\W+'))).toSet();
    if (noExperienceKeywordsExact.any(
      (keyword) => wordsInJob.contains(keyword),
    )) {
      _logMatch(
        job,
        'No Experience Keyword Exact Match',
        noExperienceKeywordsExact.firstWhere(
          (keyword) => wordsInJob.contains(keyword),
        ),
      );
      return true;
    }

    // Partielle Keyword-Prüfung (falls keine exakten Treffer)
    if (await _checkPartialMatch(
      job,
      combinedText,
      noExperienceKeywordsExact,
      'No Experience Keyword Partial Match',
    )) {
      return true;
    }

    // Prüfung auf Erfahrungsalternativen in der Beschreibung
    if (_pruefeErfahrungsalternativen(descriptionLower)) {
      // print("### Job ohne Erfahrung erkannt durch Erfahrungsalternativen in der Beschreibung");
      return true;
    }

    // Prüfung auf Formulierungen, die auf Einstiegsjobs hindeuten
    if (descriptionLower.contains('erste berufserfahrung') ||
        descriptionLower.contains('erste erfahrung') ||
        descriptionLower.contains('erste schritte') ||
        descriptionLower.contains('beruflicher einstieg') ||
        descriptionLower.contains('berufseinstieg')) {
      // print("### Job ohne Erfahrung erkannt durch Einstiegsformulierungen");
      return true;
    }

    return false;
  }

  Future<bool> _checkPartialMatch(
    JobEntity job,
    List<String> textsToSearch,
    List<String> keywords,
    String matchType,
  ) async {
    final minMatchLength = 3; // Reduzierte Mindestlänge für Teilübereinstimmung

    // Asynchron über Keywords iterieren
    for (final keyword in keywords) {
      if (keyword.length >= minMatchLength) {
        // Nur sinnvolle Keywords prüfen
        // Überprüfe, ob ein Text das Keyword enthält (partiell)
        if (textsToSearch.any((text) => text.contains(keyword))) {
          // Zusätzliche Prüfung: Verhindere Treffer bei allgemeinen Präfixen/Suffixen
          // (Beispiel: 'lager' in 'Lagereinrichtung' vs. 'Lagerhelfer')
          final potentialMatch = textsToSearch.firstWhere(
            (text) => text.contains(keyword),
          );
          final matchIndex = potentialMatch.indexOf(keyword);
          final wordBefore =
              matchIndex > 0
                  ? potentialMatch
                      .substring(0, matchIndex)
                      .split(RegExp(r'\W+'))
                      .last
                  : '';
          final wordAfter =
              (matchIndex + keyword.length < potentialMatch.length)
                  ? potentialMatch
                      .substring(matchIndex + keyword.length)
                      .split(RegExp(r'\W+'))
                      .first
                  : '';

          // Ist das gefundene Wort wahrscheinlich das gesuchte Keyword (oder Teil eines längeren Wortes)?
          // Einfache Heuristik: Prüfen, ob das Keyword am Anfang/Ende eines Wortes steht
          bool likelyStandalone =
              (matchIndex == 0 ||
                  !RegExp(r'\w').hasMatch(potentialMatch[matchIndex - 1])) &&
              (matchIndex + keyword.length == potentialMatch.length ||
                  !RegExp(
                    r'\w',
                  ).hasMatch(potentialMatch[matchIndex + keyword.length]));

          // Erweiterte Liste von bekannten Präfixen/Suffixen für Jobs ohne Erfahrung
          bool isKnownPrefixSuffix =
              [
                'helfer',
                'kraft',
                'hilfe',
                'arbeit',
                'job',
                'stelle',
                'mitarbeiter',
                'aushilfe',
                'kräfte',
                'personal',
                'tätigkeit',
                'position',
                'anstellung',
                'beschäftigung',
                'einstieg',
                'anfänger',
                'einsteiger',
                'student',
              ].contains(wordAfter.toLowerCase()) ||
              [
                'produktions',
                'lager',
                'versand',
                'küchen',
                'reinigungs',
                'service',
                'aushilfs',
                'hilfs',
                'einstiegs',
                'anfänger',
                'einsteiger',
                'student',
              ].contains(wordBefore.toLowerCase());

          // Zusätzliche Prüfung für Kombinationen, die auf Jobs ohne Erfahrung hindeuten
          bool isNoExperienceContext =
              potentialMatch.contains('keine $keyword') ||
              potentialMatch.contains('ohne $keyword') ||
              potentialMatch.contains('$keyword nicht erforderlich') ||
              potentialMatch.contains('$keyword nicht notwendig') ||
              potentialMatch.contains('$keyword von vorteil') ||
              potentialMatch.contains('$keyword wünschenswert');

          if (likelyStandalone ||
              isKnownPrefixSuffix ||
              isNoExperienceContext) {
            _logMatch(job, matchType, keyword);
            return true; // Frühzeitiger Ausstieg bei Treffer
          }
        }
      }
    }
    return false; // Kein Treffer nach Prüfung aller Keywords
  }

  void _logMatch(JobEntity job, String matchType, String matchedTerm) {
    // ... existing code ...
  }

  // Methode zum Sortieren von Jobs nach Entfernung (die nächsten zuerst)
  List<JobEntity> _sortByDistance(List<JobEntity> jobs) {
    final log = getLogger('JobSearchNotifier._sortByDistance');
    log.d("Sortiere ${jobs.length} Jobs nach Entfernung...");

    // Extrahiere Entfernungsinformationen aus den Job-Metadaten
    final jobsWithDistance = <JobEntity>[];

    for (final job in jobs) {
      // Füge alle Jobs zur Liste hinzu
      jobsWithDistance.add(job);
    }

    // Sortiere die Jobs nach Entfernung (falls vorhanden)
    jobsWithDistance.sort((a, b) {
      // Versuche, die Entfernung aus dem Titel oder der Beschreibung zu extrahieren
      final distanceA = _extractDistanceFromJob(a);
      final distanceB = _extractDistanceFromJob(b);

      // Wenn beide Jobs eine Entfernung haben, vergleiche sie
      if (distanceA != null && distanceB != null) {
        return distanceA.compareTo(distanceB);
      }

      // Wenn nur ein Job eine Entfernung hat, bevorzuge diesen
      if (distanceA != null) return -1;
      if (distanceB != null) return 1;

      // Wenn keine Entfernung verfügbar ist, sortiere nach Datum (neueste zuerst)
      return b.publishedDate.compareTo(a.publishedDate);
    });

    log.d("Sortierung nach Entfernung abgeschlossen.");
    return jobsWithDistance;
  }

  // Hilfsmethode zum Extrahieren der Entfernung aus einem Job
  double? _extractDistanceFromJob(JobEntity job) {
    // Versuche, die Entfernung aus dem Titel zu extrahieren
    final titleMatch = RegExp(r'(\d+[.,]?\d*)\s*km').firstMatch(job.title);
    if (titleMatch != null) {
      final distanceStr = titleMatch.group(1)?.replaceAll(',', '.');
      final distance = double.tryParse(distanceStr ?? '');
      if (distance != null) {
        return distance;
      }
    }

    // Versuche, die Entfernung aus der Beschreibung zu extrahieren
    final descMatch = RegExp(r'(\d+[.,]?\d*)\s*km').firstMatch(job.description);
    if (descMatch != null) {
      final distanceStr = descMatch.group(1)?.replaceAll(',', '.');
      final distance = double.tryParse(distanceStr ?? '');
      if (distance != null) {
        return distance;
      }
    }

    // Versuche, die Entfernung aus dem Standort zu extrahieren
    // Manche Jobs haben die Entfernung im Format "Standort (XX km)"
    final locationMatch = RegExp(
      r'\((\d+[.,]?\d*)\s*km\)',
    ).firstMatch(job.location);
    if (locationMatch != null) {
      final distanceStr = locationMatch.group(1)?.replaceAll(',', '.');
      final distance = double.tryParse(distanceStr ?? '');
      if (distance != null) {
        return distance;
      }
    }

    // Versuche, die Entfernung aus dem Unternehmen zu extrahieren
    // Manche Jobs haben die Entfernung im Format "Unternehmen (XX km)"
    // Hinweis: JobEntity hat kein company-Feld, daher wird dieser Teil übersprungen

    // Keine Entfernung gefunden
    return null;
  }

  // Methode zum Sortieren von Jobs basierend auf der Übereinstimmung mit dem Nutzerprofil
  Future<List<JobEntity>> _sortByProfileMatching(
    List<JobEntity> jobs,
    UserProfile userProfile,
  ) async {
    final log = getLogger('JobSearchNotifier._sortByProfileMatching');

    // KORREKTUR: Prüfe, ob das Profil NICHT leer ist UND relevante Daten enthält
    // if (userProfile == null) return jobs; // Alte Prüfung
    if (userProfile == UserProfile.empty()) {
      // Neue Prüfung: Ist es das leere Platzhalter-Profil?
      log.d(
        "Nutzerprofil ist noch leer (UserProfile.empty). Sortierung nach Profil wird übersprungen.",
      );
      return jobs;
    }

    final skillsList = userProfile.skills ?? [];
    final workExperienceList = userProfile.workExperience ?? [];
    final jobKeywords =
        userProfile.jobKeywords ?? []; // KI-generierte Berufsbezeichnungen

    // Debug-Ausgabe der vorhandenen Daten
    log.d("Profil-Daten für Sortierung:");
    log.d("Skills: ${skillsList.join(', ')}");
    log.d("Berufserfahrung: ${workExperienceList.length} Einträge");
    log.d("Berufsbezeichnungen: ${jobKeywords.join(', ')}");

    // Wir sortieren, wenn mindestens eine der Kategorien vorhanden ist
    bool hasProfileData =
        skillsList.isNotEmpty ||
        workExperienceList.isNotEmpty ||
        jobKeywords.isNotEmpty;

    if (!hasProfileData) {
      log.d(
        "Keine Skills, Berufserfahrung oder Berufsbezeichnungen im Profil vorhanden. Sortierung nach Profil wird übersprungen.",
      );
      return jobs;
    }

    log.d("Starte Sortierung nach Profil für ${jobs.length} Jobs.");

    // 1. Berufserfahrung nach Startdatum sortieren (neueste zuerst)
    List<WorkExperience> sortedExperience = List.from(workExperienceList);
    sortedExperience.sort((a, b) => b.startDate.compareTo(a.startDate));

    final WorkExperience? latestExperience =
        sortedExperience.isNotEmpty ? sortedExperience.first : null;
    final List<WorkExperience> olderExperiences =
        sortedExperience.length > 1 ? sortedExperience.sublist(1) : [];

    // Funktion zum Extrahieren von Keywords aus Text
    Set<String> extractKeywords(String text) {
      return text
          .toLowerCase()
          .split(RegExp(r'\W+'))
          .where((kw) => kw.length > 2)
          .toSet();
    }

    // Keywords aus Profil vorbereiten
    final latestExpKeywordsPosition =
        latestExperience != null
            ? extractKeywords(latestExperience.position)
            : <String>{};
    final latestExpKeywordsDesc =
        latestExperience != null
            ? extractKeywords(latestExperience.description ?? '')
            : <String>{};

    final olderExpKeywordsPosition =
        olderExperiences.expand((exp) => extractKeywords(exp.position)).toSet();
    final olderExpKeywordsDesc =
        olderExperiences
            .expand((exp) => extractKeywords(exp.description ?? ''))
            .toSet();

    final skillKeywords =
        skillsList
            .map((s) => s.toLowerCase())
            .where((s) => s.length > 2)
            .toSet();

    // LOGGING: Profil-Keywords ausgeben
    log.d(
      "Profil-Keywords - Neueste Pos: ${latestExpKeywordsPosition.take(5)}...",
    );
    log.d(
      "Profil-Keywords - Neueste Desc: ${latestExpKeywordsDesc.take(5)}...",
    );
    log.d(
      "Profil-Keywords - Ältere Pos: ${olderExpKeywordsPosition.take(5)}...",
    );
    log.d("Profil-Keywords - Ältere Desc: ${olderExpKeywordsDesc.take(5)}...");
    log.d("Profil-Keywords - Skills: ${skillKeywords.take(5)}...");
    log.d(
      "Profil-Keywords - Berufsbezeichnungen: ${(userProfile.jobKeywords ?? []).take(5)}...",
    );

    // Map zur Speicherung des Scores für jeden Job
    final jobScores =
        <String, double>{}; // Verwende double für feinere Gewichtung

    // 2. & 3. Berechne Score für jeden Job mit Gewichtung
    for (final job in jobs) {
      double score = 0.0;
      final jobTitleLower = job.title.toLowerCase();
      final jobDescLower = job.description.toLowerCase();
      final jobTitleKeywords = extractKeywords(jobTitleLower);
      final jobDescKeywords = extractKeywords(jobDescLower);

      // Gewichtung für Treffer
      const double latestExpTitleWeight = 5.0;
      const double latestExpDescWeight = 3.0;
      const double olderExpTitleWeight = 3.0;
      const double olderExpDescWeight = 2.0;
      const double skillTitleWeight = 2.0;
      const double skillDescWeight = 1.0;
      const double jobTitleKeywordWeight =
          6.0; // Höchste Gewichtung für KI-generierte Berufsbezeichnungen
      const double workExperienceMatchWeight =
          8.0; // Sehr hohe Gewichtung für Übereinstimmungen mit Berufserfahrung

      // Funktion zum Hinzufügen von Punkten für Keyword-Übereinstimmungen
      void addScoreForMatches(
        Set<String> profileKeywords,
        Set<String> jobKeywords,
        double weight,
      ) {
        final intersection = profileKeywords.intersection(jobKeywords);
        score += intersection.length * weight;
      }

      // --- Punkte für neueste Erfahrung ---
      if (latestExperience != null) {
        // Prüfe, ob der Jobtitel direkt mit der Position übereinstimmt
        final positionLower = latestExperience.position.toLowerCase();

        // Direkte Übereinstimmung mit der Position gibt sehr viele Punkte
        if (jobTitleLower.contains(positionLower) && positionLower.length > 3) {
          score += workExperienceMatchWeight * 20;
          log.d(
            "Direkte Übereinstimmung mit der Position '${latestExperience.position}': +${workExperienceMatchWeight * 20} Punkte",
          );

          // Prüfe auf Wortgrenzen für noch genauere Matches
          final wordBoundaryPattern = RegExp(
            r'\b' + RegExp.escape(positionLower) + r'\b',
          );
          if (wordBoundaryPattern.hasMatch(jobTitleLower)) {
            score += workExperienceMatchWeight * 30;
            log.d(
              "Exakter Wortmatch mit der Position '${latestExperience.position}': +${workExperienceMatchWeight * 30} Punkte",
            );
          }
        }

        // Normale Keyword-Matches
        addScoreForMatches(
          latestExpKeywordsPosition,
          jobTitleKeywords,
          latestExpTitleWeight,
        );
        addScoreForMatches(
          latestExpKeywordsDesc,
          jobDescKeywords,
          latestExpDescWeight,
        );
        // Weniger Punkte, wenn Positions-Keyword in Beschreibung oder umgekehrt
        addScoreForMatches(
          latestExpKeywordsPosition,
          jobDescKeywords,
          latestExpDescWeight * 0.5,
        );
        addScoreForMatches(
          latestExpKeywordsDesc,
          jobTitleKeywords,
          latestExpTitleWeight * 0.5,
        );
      }

      // --- Punkte für ältere Erfahrungen ---
      if (olderExperiences.isNotEmpty) {
        // Prüfe jede ältere Berufserfahrung
        for (final experience in olderExperiences) {
          final positionLower = experience.position.toLowerCase();

          // Direkte Übereinstimmung mit der Position gibt viele Punkte (etwas weniger als neueste Erfahrung)
          if (jobTitleLower.contains(positionLower) &&
              positionLower.length > 3) {
            score += workExperienceMatchWeight * 15;
            log.d(
              "Direkte Übereinstimmung mit älterer Position '${experience.position}': +${workExperienceMatchWeight * 15} Punkte",
            );

            // Prüfe auf Wortgrenzen für noch genauere Matches
            final wordBoundaryPattern = RegExp(
              r'\b' + RegExp.escape(positionLower) + r'\b',
            );
            if (wordBoundaryPattern.hasMatch(jobTitleLower)) {
              score += workExperienceMatchWeight * 20;
              log.d(
                "Exakter Wortmatch mit älterer Position '${experience.position}': +${workExperienceMatchWeight * 20} Punkte",
              );
            }

            // Wenn wir einen Match gefunden haben, können wir die Schleife verlassen
            break;
          }
        }

        // Normale Keyword-Matches
        addScoreForMatches(
          olderExpKeywordsPosition,
          jobTitleKeywords,
          olderExpTitleWeight,
        );
        addScoreForMatches(
          olderExpKeywordsDesc,
          jobDescKeywords,
          olderExpDescWeight,
        );
        addScoreForMatches(
          olderExpKeywordsPosition,
          jobDescKeywords,
          olderExpDescWeight * 0.5,
        );
        addScoreForMatches(
          olderExpKeywordsDesc,
          jobTitleKeywords,
          olderExpTitleWeight * 0.5,
        );
      }

      // --- Punkte für Skills ---
      if (skillKeywords.isNotEmpty) {
        addScoreForMatches(skillKeywords, jobTitleKeywords, skillTitleWeight);
        addScoreForMatches(skillKeywords, jobDescKeywords, skillDescWeight);
      }

      // --- Punkte für KI-generierte Berufsbezeichnungen ---
      if ((userProfile.jobKeywords ?? []).isNotEmpty) {
        // Debug-Ausgabe der Berufsbezeichnungen
        log.d("Verarbeite Job: ${job.title}");
        log.d(
          "Verfügbare Berufsbezeichnungen: ${(userProfile.jobKeywords ?? []).join(', ')}",
        );

        // Prüfe, ob der Jobtitel oder die Beschreibung eine der KI-generierten Berufsbezeichnungen enthält
        double keywordScore = 0.0;

        // Prüfe auf exakte Übereinstimmungen mit den Berufsbezeichnungen im Profil
        for (final keyword in (userProfile.jobKeywords ?? [])) {
          final keywordLower = keyword.toLowerCase();

          // Direkter Treffer im Titel gibt viele Punkte
          if (jobTitleLower.contains(keywordLower)) {
            keywordScore +=
                jobTitleKeywordWeight *
                60; // Erhöht von 50 auf 60 für bessere Gewichtung
            log.d(
              "Exakter Treffer im Titel für '$keywordLower': +${jobTitleKeywordWeight * 60} Punkte",
            );
            // Match gefunden
          }

          // Prüfe auch auf Wortgrenzen für noch genauere Matches
          final wordBoundaryPattern = RegExp(
            r'\b' + RegExp.escape(keywordLower) + r'\b',
          );
          if (wordBoundaryPattern.hasMatch(jobTitleLower)) {
            keywordScore +=
                jobTitleKeywordWeight *
                80; // Noch höhere Gewichtung für exakte Wortmatches (von 70 auf 80)
            log.d(
              "Exakter Wortmatch im Titel für '$keywordLower': +${jobTitleKeywordWeight * 80} Punkte",
            );
          }

          // Besonders hohe Gewichtung, wenn der Jobtitel mit dem Keyword beginnt
          if (jobTitleLower.startsWith(keywordLower)) {
            keywordScore +=
                jobTitleKeywordWeight *
                100; // Sehr hohe Gewichtung für Keyword am Anfang des Titels
            log.d(
              "Jobtitel beginnt mit '$keywordLower': +${jobTitleKeywordWeight * 100} Punkte",
            );
          }
        }

        // Prüfe auf Treffer in der Beschreibung
        for (final keyword in (userProfile.jobKeywords ?? [])) {
          final keywordLower = keyword.toLowerCase();

          // Treffer in der Beschreibung gibt weniger Punkte
          if (jobDescLower.contains(keywordLower)) {
            keywordScore += jobTitleKeywordWeight * 25; // Erhöht von 15 auf 25
            log.d(
              "Exakter Treffer in der Beschreibung für '$keywordLower': +${jobTitleKeywordWeight * 25} Punkte",
            );
            // Match gefunden
          }

          // Prüfe auch auf Wortgrenzen in der Beschreibung
          final wordBoundaryPattern = RegExp(
            r'\b' + RegExp.escape(keywordLower) + r'\b',
          );
          if (wordBoundaryPattern.hasMatch(jobDescLower)) {
            keywordScore +=
                jobTitleKeywordWeight *
                35; // Höhere Gewichtung für exakte Wortmatches in der Beschreibung
            log.d(
              "Exakter Wortmatch in der Beschreibung für '$keywordLower': +${jobTitleKeywordWeight * 35} Punkte",
            );
          }
        }

        // Prüfe auf Teilübereinstimmungen (z.B. "Erzieher" in "Erziehungshelfer")
        for (final keyword in (userProfile.jobKeywords ?? [])) {
          final keywordLower = keyword.toLowerCase();

          // Entferne Suffixe wie "/in", "/r", etc.
          String cleanKeyword = keywordLower;
          if (cleanKeyword.contains("/")) {
            cleanKeyword = cleanKeyword.substring(0, cleanKeyword.indexOf("/"));
          }

          // Prüfe auf Teilübereinstimmungen im Titel
          if (cleanKeyword.length > 3) {
            if (jobTitleLower.contains(cleanKeyword)) {
              keywordScore +=
                  jobTitleKeywordWeight * 20; // Erhöht von 12 auf 20
              log.d(
                "Teilübereinstimmung im Titel für '$cleanKeyword': +${jobTitleKeywordWeight * 20} Punkte",
              );

              // Zusätzliche Prüfung: Ist das Keyword am Anfang des Titels?
              if (jobTitleLower.startsWith(cleanKeyword)) {
                keywordScore +=
                    jobTitleKeywordWeight * 15; // Bonus für Keyword am Anfang
                log.d(
                  "Keyword '$cleanKeyword' am Anfang des Titels: +${jobTitleKeywordWeight * 15} Punkte",
                );
              }

              // Zusätzliche Prüfung: Ist das Keyword ein eigenständiges Wort im Titel?
              final wordBoundaryPattern = RegExp(
                r'\b' + RegExp.escape(cleanKeyword) + r'\b',
              );
              if (wordBoundaryPattern.hasMatch(jobTitleLower)) {
                keywordScore +=
                    jobTitleKeywordWeight * 15; // Bonus für eigenständiges Wort
                log.d(
                  "Keyword '$cleanKeyword' ist eigenständiges Wort im Titel: +${jobTitleKeywordWeight * 15} Punkte",
                );
              }
            } else if (jobDescLower.contains(cleanKeyword)) {
              keywordScore += jobTitleKeywordWeight * 10; // Erhöht von 6 auf 10
              log.d(
                "Teilübereinstimmung in der Beschreibung für '$cleanKeyword': +${jobTitleKeywordWeight * 10} Punkte",
              );

              // Zusätzliche Prüfung: Ist das Keyword ein eigenständiges Wort in der Beschreibung?
              final wordBoundaryPattern = RegExp(
                r'\b' + RegExp.escape(cleanKeyword) + r'\b',
              );
              if (wordBoundaryPattern.hasMatch(jobDescLower)) {
                keywordScore +=
                    jobTitleKeywordWeight * 8; // Bonus für eigenständiges Wort
                log.d(
                  "Keyword '$cleanKeyword' ist eigenständiges Wort in der Beschreibung: +${jobTitleKeywordWeight * 8} Punkte",
                );
              }
            }
          }
        }

        // Prüfe auf Wortstämme
        for (final keyword in userProfile.jobKeywords ?? []) {
          final keywordLower = keyword.toLowerCase();

          // Entferne Suffixe wie "/in", "/r", etc.
          String cleanKeyword = keywordLower;
          if (cleanKeyword.contains("/")) {
            cleanKeyword = cleanKeyword.substring(0, cleanKeyword.indexOf("/"));
          }

          // Prüfe auf Wortstämme (mindestens 3 Zeichen)
          if (cleanKeyword.length > 3) {
            // Verwende einen längeren Wortstamm für bessere Relevanz
            final wordStem = cleanKeyword.substring(
              0,
              cleanKeyword.length > 5
                  ? 5
                  : cleanKeyword.length, // Erhöht von 4 auf 5 Zeichen
            );

            if (jobTitleLower.contains(wordStem)) {
              keywordScore += jobTitleKeywordWeight * 10; // Erhöht von 6 auf 10
              log.d(
                "Wortstamm im Titel für '$wordStem': +${jobTitleKeywordWeight * 10} Punkte",
              );

              // Zusätzliche Prüfung: Ist der Wortstamm am Anfang eines Wortes im Titel?
              final wordStartPattern = RegExp(r'\b' + RegExp.escape(wordStem));
              if (wordStartPattern.hasMatch(jobTitleLower)) {
                keywordScore +=
                    jobTitleKeywordWeight *
                    8; // Bonus für Wortstamm am Wortanfang
                log.d(
                  "Wortstamm '$wordStem' am Anfang eines Wortes im Titel: +${jobTitleKeywordWeight * 8} Punkte",
                );
              }
            }

            if (jobDescLower.contains(wordStem)) {
              keywordScore += jobTitleKeywordWeight * 5; // Erhöht von 3 auf 5
              log.d(
                "Wortstamm in der Beschreibung für '$wordStem': +${jobTitleKeywordWeight * 5} Punkte",
              );

              // Zusätzliche Prüfung: Ist der Wortstamm am Anfang eines Wortes in der Beschreibung?
              final wordStartPattern = RegExp(r'\b' + RegExp.escape(wordStem));
              if (wordStartPattern.hasMatch(jobDescLower)) {
                keywordScore +=
                    jobTitleKeywordWeight *
                    4; // Bonus für Wortstamm am Wortanfang
                log.d(
                  "Wortstamm '$wordStem' am Anfang eines Wortes in der Beschreibung: +${jobTitleKeywordWeight * 4} Punkte",
                );
              }
            }
          }
        }

        // Spezielle Prüfung für pädagogische Berufe
        if ((userProfile.jobKeywords ?? []).any(
          (k) =>
              k.toLowerCase().contains('erzieher') ||
              k.toLowerCase().contains('pädagog') ||
              k.toLowerCase().contains('betreu') ||
              k.toLowerCase().contains('kinder'),
        )) {
          // Prüfe auf pädagogische Begriffe im Titel oder der Beschreibung
          final paedagogischeBegriffe = [
            'kind',
            'kinder',
            'jugend',
            'pädagog',
            'erzieh',
            'betreu',
            'sozial',
            'bildung',
            'schul',
            'kita',
            'kindergarten',
            'betreuung',
            'unterricht',
            'lehre',
            'ausbildung',
          ];

          for (final begriff in paedagogischeBegriffe) {
            if (jobTitleLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 10; // Erhöht von 5 auf 10
              log.d(
                "Pädagogischer Begriff im Titel gefunden: '$begriff': +${jobTitleKeywordWeight * 10} Punkte",
              );
              break;
            } else if (jobDescLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 5; // Erhöht von 2.5 auf 5
              log.d(
                "Pädagogischer Begriff in der Beschreibung gefunden: '$begriff': +${jobTitleKeywordWeight * 5} Punkte",
              );
              break;
            }
          }
        }

        // Spezielle Prüfung für Küchen- und Servicekräfte
        if ((userProfile.jobKeywords ?? []).any(
          (k) =>
              k.toLowerCase().contains('küche') ||
              k.toLowerCase().contains('service') ||
              k.toLowerCase().contains('gastronomie'),
        )) {
          final gastronomieBegriffe = [
            'küche',
            'service',
            'gastro',
            'restaurant',
            'hotel',
            'catering',
            'speise',
            'essen',
            'kochen',
            'zubereitung',
            'bedienung',
            'kellner',
          ];

          for (final begriff in gastronomieBegriffe) {
            if (jobTitleLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 10; // Erhöht von 5 auf 10
              log.d(
                "Gastronomie-Begriff im Titel gefunden: '$begriff': +${jobTitleKeywordWeight * 10} Punkte",
              );
              break;
            } else if (jobDescLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 5; // Erhöht von 2.5 auf 5
              log.d(
                "Gastronomie-Begriff in der Beschreibung gefunden: '$begriff': +${jobTitleKeywordWeight * 5} Punkte",
              );
              break;
            }
          }
        }

        // Spezielle Prüfung für Verwaltungs- und Büroberufe
        if ((userProfile.jobKeywords ?? []).any(
          (k) =>
              k.toLowerCase().contains('verwaltung') ||
              k.toLowerCase().contains('büro') ||
              k.toLowerCase().contains('assistent'),
        )) {
          final verwaltungsBegriffe = [
            'büro',
            'verwaltung',
            'assistent',
            'sekretär',
            'sekretariat',
            'sachbearbeiter',
            'administration',
            'empfang',
            'rezeption',
            'office',
            'kaufmann',
            'kauffrau',
            'buchhaltung',
            'finanzen',
            'personal',
            'hr',
            'human resources',
          ];

          for (final begriff in verwaltungsBegriffe) {
            if (jobTitleLower.contains(begriff)) {
              keywordScore +=
                  jobTitleKeywordWeight * 15; // Erhöht von 10 auf 15
              log.d(
                "Verwaltungs-Begriff im Titel gefunden: '$begriff': +${jobTitleKeywordWeight * 15} Punkte",
              );
              break;
            } else if (jobDescLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 8; // Erhöht von 5 auf 8
              log.d(
                "Verwaltungs-Begriff in der Beschreibung gefunden: '$begriff': +${jobTitleKeywordWeight * 8} Punkte",
              );
              break;
            }
          }
        }

        // Spezielle Prüfung für IT- und Medienberufe
        if ((userProfile.jobKeywords ?? []).any(
          (k) =>
              k.toLowerCase().contains('it') ||
              k.toLowerCase().contains('medien') ||
              k.toLowerCase().contains('media') ||
              k.toLowerCase().contains('content') ||
              k.toLowerCase().contains('digital') ||
              k.toLowerCase().contains('marketing') ||
              k.toLowerCase().contains('social') ||
              k.toLowerCase().contains('design') ||
              k.toLowerCase().contains('editor'),
        )) {
          final itMedienBegriffe = [
            'it',
            'edv',
            'computer',
            'software',
            'hardware',
            'system',
            'netzwerk',
            'support',
            'medien',
            'media',
            'content',
            'digital',
            'marketing',
            'social',
            'design',
            'editor',
            'video',
            'audio',
            'grafik',
            'web',
            'online',
            'internet',
            'multimedia',
            'gestaltung',
            'produktion',
          ];

          for (final begriff in itMedienBegriffe) {
            if (jobTitleLower.contains(begriff)) {
              keywordScore +=
                  jobTitleKeywordWeight * 20; // Hohe Gewichtung für IT/Medien
              log.d(
                "IT/Medien-Begriff im Titel gefunden: '$begriff': +${jobTitleKeywordWeight * 20} Punkte",
              );
              break;
            } else if (jobDescLower.contains(begriff)) {
              keywordScore += jobTitleKeywordWeight * 10;
              log.d(
                "IT/Medien-Begriff in der Beschreibung gefunden: '$begriff': +${jobTitleKeywordWeight * 10} Punkte",
              );
              break;
            }
          }
        }

        // Füge die Punkte zum Gesamtscore hinzu
        score += keywordScore;
        log.d("Gesamtpunkte für Berufsbezeichnungen: $keywordScore");
      }

      jobScores[job.id] = score;
      // log.d("Job '${job.title.substring(0, min(20, job.title.length))}' Score: $score"); // Optional: Score für jeden Job loggen
    }

    // 4. Sortiere Jobs basierend auf dem Score und der Entfernung
    jobs.sort((a, b) {
      final scoreA = jobScores[a.id] ?? 0.0;
      final scoreB = jobScores[b.id] ?? 0.0;

      // Extrahiere die Entfernungen
      final distanceA = _extractDistanceFromJob(a);
      final distanceB = _extractDistanceFromJob(b);

      // Wenn die Scores sehr unterschiedlich sind (mehr als 200 Punkte Unterschied),
      // dann sortiere nur nach Score - erhöht von 100 auf 200 für bessere Relevanz
      if ((scoreB - scoreA).abs() > 200) {
        return scoreB.compareTo(scoreA);
      }

      // Wenn die Scores ähnlich sind, aber einer deutlich höher ist (50-200 Punkte),
      // dann berücksichtige die Entfernung, aber mit geringerem Einfluss
      if ((scoreB - scoreA).abs() > 50) {
        // Wenn beide Jobs eine Entfernung haben
        if (distanceA != null && distanceB != null) {
          // Berechne einen kombinierten Score mit geringerem Einfluss der Entfernung
          final combinedScoreA = scoreA - (distanceA * 0.3);
          final combinedScoreB = scoreB - (distanceB * 0.3);
          return combinedScoreB.compareTo(combinedScoreA);
        }
      }

      // Wenn die Scores sehr ähnlich sind (weniger als 50 Punkte Unterschied),
      // dann berücksichtige die Entfernung stärker
      if (distanceA != null && distanceB != null) {
        // Berechne einen kombinierten Score mit stärkerem Einfluss der Entfernung
        final combinedScoreA = scoreA - (distanceA * 0.8);
        final combinedScoreB = scoreB - (distanceB * 0.8);
        return combinedScoreB.compareTo(combinedScoreA);
      }

      // Wenn nur ein Job eine Entfernung hat, bevorzuge diesen bei ähnlichem Score
      if (distanceA != null && distanceB == null && scoreA >= scoreB * 0.9) {
        return -1; // A hat Entfernung, B nicht, und Scores sind ähnlich
      }
      if (distanceB != null && distanceA == null && scoreB >= scoreA * 0.9) {
        return 1; // B hat Entfernung, A nicht, und Scores sind ähnlich
      }

      // Ansonsten sortiere nach Score, bei gleichem Score nach Datum
      if (scoreB.compareTo(scoreA) == 0) {
        return b.publishedDate.compareTo(a.publishedDate);
      }
      return scoreB.compareTo(scoreA);
    });

    final maxScore =
        jobScores.values.isNotEmpty ? jobScores.values.reduce(max) : 0.0;
    log.d("Jobs nach Profil sortiert. Höchster Score: $maxScore");

    // LOGGING: Top 5 Jobs nach Sortierung ausgeben
    log.d("Top 5 Jobs nach Sortierung:");
    for (int i = 0; i < min(5, jobs.length); i++) {
      final job = jobs[i];
      log.d(
        "  #${i + 1}: ${job.title.substring(0, min(50, job.title.length))}... (Score: ${jobScores[job.id]?.toStringAsFixed(2) ?? 'N/A'})",
      );
    }

    // Optional: Nur Jobs mit Score > 0 anzeigen?
    // jobs.removeWhere((job) => (jobScores[job.id] ?? 0.0) == 0.0);
    // log.d("Nach Filterung (Score > 0) verbleiben ${jobs.length} Jobs.");

    return jobs;
  }

  // Methode zum Anzeigen eines Hinweises, wenn das Profil nicht vollständig ist
  // Aktuell nicht verwendet, aber für zukünftige Implementierung vorbereitet
  /*
  Future<void> _showProfileCompletionHint() async {
    // Wir könnten hier einen Timer verwenden, um den Hinweis nicht zu oft anzuzeigen
    final prefs = await SharedPreferences.getInstance();
    final lastShown = prefs.getInt('profile_hint_last_shown') ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    // Zeige den Hinweis nur einmal pro Tag
    if (now - lastShown > const Duration(days: 1).inMilliseconds) {
      // Im nächsten Update könnte hier eine Notification angezeigt werden
      final log = getLogger('JobSearchNotifier');
      log.i('HINWEIS: Vervollständige dein Profil, um passendere Jobangebote zu erhalten.');
      await prefs.setInt('profile_hint_last_shown', now);
    }
  }
  */

  // Hilfsmethode zur Prüfung von Erfahrungsalternativen
  bool _pruefeErfahrungsalternativen(String beschreibung) {
    // Prüft, ob die Stellenbeschreibung Alternativen zur Berufserfahrung anbietet

    // Typische Ausdrücke, die auf Alternativen hindeuten
    final alternativAusdruecke = [
      'oder vergleichbare',
      'oder entsprechende',
      'oder ähnliche',
      'alternativ',
      'vorzugsweise',
      'idealerweise',
      'wünschenswert',
      'von vorteil',
      'bevorzugt',
      'gerne auch',
      'oder gleichwertige',
      'oder adäquate',
    ];

    // Überprüfe Kombinationen: "Erfahrung" nahe bei alternativen Ausdrücken
    bool hatAlternative = false;

    for (var ausdruck in alternativAusdruecke) {
      // Wenn die Stellenbeschreibung sowohl "erfahrung" als auch einen alternativen Ausdruck enthält
      if (beschreibung.contains(ausdruck)) {
        // Prüfe, ob der alternative Ausdruck in Bezug auf Erfahrung verwendet wird
        final index1 = beschreibung.indexOf(ausdruck);
        final index2 = beschreibung.indexOf('erfahrung');

        // Wenn beide Begriffe vorhanden sind und nicht zu weit voneinander entfernt
        if (index2 != -1 && (index1 - index2).abs() < 100) {
          hatAlternative = true;
          break;
        }
      }
    }

    // Überprüfe spezifische Muster wie "oder entsprechende Berufserfahrung"
    return hatAlternative ||
        beschreibung.contains('oder berufserfahrung') ||
        beschreibung.contains('berufserfahrung wünschenswert');
  }

  // --- Helper Keywords (Wird von _isHelperJob verwendet) ---

  static const List<String> _helperKeywords = [
    // Keywords für Helferjobs
    // ... existing code ...
  ];
}

// -- Provider --

// Definition für den API-Key wird nicht mehr benötigt
// const String _apiKeyEnvVariable = 'ARBEITSAGENTUR_API_KEY';

// Stellt den JobApiClient bereit
final jobApiClientProvider = Provider<JobApiClient>((ref) {
  // Verwende immer den AgenturArbeitApiClient
  final log = getLogger('jobApiClientProvider');
  log.i("Verwende Agentur für Arbeit API Client.");
  return AgenturArbeitApiClient();

  // --- Alte Logik mit Fallback (nicht mehr nötig) ---
  /*
  final apiKey = const String.fromEnvironment(_apiKeyEnvVariable);
  if (apiKey.isNotEmpty) {
    // print("INFO: Verwende Agentur für Arbeit API Client.");
    return AgenturArbeitApiClient(apiKey: apiKey);
  } else {
    // print("WARNUNG: Keinen Agentur für Arbeit API-Key gefunden ($\"{_apiKeyEnvVariable}\"). Verwende MockJobApiClient.");
    return MockJobApiClient();
  }
  */
  // ------------------------------------------------
});

// Stellt den StateNotifier für die Jobsuche bereit
final jobSearchProvider =
    StateNotifierProvider<JobSearchNotifier, JobSearchState>((ref) {
      final apiClient = ref.watch(jobApiClientProvider);
      return JobSearchNotifier(ref);
    });

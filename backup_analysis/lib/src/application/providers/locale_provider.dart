import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Provider für die aktuelle Locale (Sprache) der App
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

/// Notifier für die Verwaltung der Locale (Sprache)
class LocaleNotifier extends StateNotifier<Locale> {
  /// Standardsprache ist Deutsch und kann nicht geändert werden
  LocaleNotifier() : super(const Locale('de'));

  /// Diese Methode ist nur noch ein Stub und ändert die Sprache nicht mehr
  Future<void> setLocale(Locale locale) async {
    // Sprache wird immer auf Deutsch gesetzt, unabhängig vom Parameter
    state = const Locale('de');
  }
}

/// Liste der unterstützten Sprachen - nur Deutsch
final supportedLocales = [
  const Locale('de'), // Deuts<PERSON>
];

/// Mapping von Sprachcodes zu Sprachnamen - nur Deutsch
final Map<String, String> languageNames = {'de': 'Deutsch'};

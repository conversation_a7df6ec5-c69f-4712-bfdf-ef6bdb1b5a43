import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/services/additional_documents_service.dart';
import 'package:ki_test/src/domain/models/additional_document.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';

/// Provider für die Liste der Dokumente
final additionalDocumentsProvider =
    AsyncNotifierProvider<AdditionalDocumentsNotifier, List<AdditionalDocument>>(
  () => AdditionalDocumentsNotifier(),
);

/// Provider für Upload-Progress
final documentUploadProgressProvider =
    StateProvider<DocumentUploadProgress?>((ref) => null);

/// Provider für CV-Bewerbungsstatus (ob CV bei Bewerbungen angehängt werden soll)
final cvApplicationStatusProvider = StateProvider<bool>((ref) => true);

/// Notifier für zusätzliche Dokumente
class AdditionalDocumentsNotifier extends AsyncNotifier<List<AdditionalDocument>> {
  late AdditionalDocumentsService _service;

  @override
  Future<List<AdditionalDocument>> build() async {
    _service = ref.read(additionalDocumentsServiceProvider);
    return await _loadDocuments();
  }

  /// Lädt alle Dokumente neu
  Future<List<AdditionalDocument>> _loadDocuments() async {
    try {
      return await _service.getUserDocuments();
    } catch (e) {
      debugPrint('Fehler beim Laden der Dokumente: $e');
      return [];
    }
  }

  /// Lädt ein neues Dokument hoch
  Future<DocumentUploadResult> uploadDocument({
    required String filePath,
    required String fileName,
  }) async {
    // Progress-Notifier erstellen
    final progressNotifier = ValueNotifier<DocumentUploadProgress>(
      DocumentUploadProgress(
        fileName: fileName,
        progress: 0.0,
        status: DocumentUploadStatus.preparing,
      ),
    );

    // Progress-Provider aktualisieren
    ref.read(documentUploadProgressProvider.notifier).state = progressNotifier.value;

    // Progress-Listener hinzufügen
    progressNotifier.addListener(() {
      ref.read(documentUploadProgressProvider.notifier).state = progressNotifier.value;
    });

    try {
      final result = await _service.uploadDocument(
        filePath: filePath,
        fileName: fileName,
        progressNotifier: progressNotifier,
      );

      if (result.success) {
        // Liste neu laden
        await refresh();
      }

      // Progress zurücksetzen
      Future.delayed(const Duration(seconds: 2), () {
        ref.read(documentUploadProgressProvider.notifier).state = null;
      });

      return result;
    } finally {
      progressNotifier.dispose();
    }
  }

  /// Aktualisiert den Bewerbungs-Status eines Dokuments
  Future<bool> updateDocumentApplicationStatus({
    required String documentId,
    required bool isActive,
  }) async {
    final success = await _service.updateDocumentApplicationStatus(
      documentId: documentId,
      isActive: isActive,
    );

    if (success) {
      // Lokalen State aktualisieren
      final currentState = state.valueOrNull ?? [];
      final updatedDocuments = currentState.map((doc) {
        if (doc.id == documentId) {
          return doc.copyWith(
            isActiveForApplications: isActive,
            updatedAt: DateTime.now(),
          );
        }
        return doc;
      }).toList();

      state = AsyncData(updatedDocuments);
    }

    return success;
  }

  /// Löscht ein Dokument
  Future<bool> deleteDocument(String documentId) async {
    final success = await _service.deleteDocument(documentId);

    if (success) {
      // Lokalen State aktualisieren
      final currentState = state.valueOrNull ?? [];
      final updatedDocuments = currentState
          .where((doc) => doc.id != documentId)
          .toList();

      state = AsyncData(updatedDocuments);
    }

    return success;
  }

  /// Lädt ein Dokument herunter
  Future<Uint8List?> downloadDocument(String documentId) async {
    return await _service.downloadDocument(documentId);
  }

  /// Lädt aktive Dokumente für Bewerbungen
  Future<List<AdditionalDocument>> getActiveDocumentsForApplications() async {
    return await _service.getActiveDocumentsForApplications();
  }

  /// Aktualisiert die Dokumentenliste
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _loadDocuments());
  }
}

/// Provider für aktive Dokumente (für Bewerbungen)
final activeDocumentsProvider = FutureProvider<List<AdditionalDocument>>((ref) async {
  final service = ref.read(additionalDocumentsServiceProvider);
  return await service.getActiveDocumentsForApplications();
});

/// Provider für Dokumenten-Anzahl
final documentsCountProvider = Provider<int>((ref) {
  final documents = ref.watch(additionalDocumentsProvider);
  return documents.valueOrNull?.length ?? 0;
});

/// Provider für verfügbare Upload-Slots
final availableUploadSlotsProvider = Provider<int>((ref) {
  const maxDocuments = 5;
  final currentCount = ref.watch(documentsCountProvider);
  return maxDocuments - currentCount;
});

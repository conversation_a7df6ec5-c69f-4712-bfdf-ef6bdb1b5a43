import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart'; // NEU: Supabase importiert
import 'dart:async';
// import 'dart:convert'; // Unused // Für JSON-Verarbeitung
import '../../domain/entities/job_entity.dart'; // Korrigierter Importpfad
import '../../domain/entities/extracted_job_text.dart'; // NEU: Für Text-Extraktion
import '../services/text_extraction_service.dart'; // NEU: Text-Extraktion Service
import 'services_providers.dart'; // NEU: Import für supabaseClientProvider

/// Verwaltet den Zustand der favorisierten Jobs.
/// Speichert aktuell nur die IDs der Jobs im Speicher.

// StateNotifier, der die Liste der favorisierten JobEntity-Objekte verwaltet
class FavoritesNotifier extends StateNotifier<AsyncValue<List<JobEntity>>> {
  final SupabaseClient _supabaseClient; // NEU
  final TextExtractionService _textExtractionService; // NEU: Text-Extraktion
  StreamSubscription<AuthState>? _authStateSubscription; // NEU
  StreamSubscription<dynamic>? _favoritesSubscription;
  User? _currentUser; // NEU (Supabase User)

  // Konstante für den Tabellennamen in Supabase
  static const String _tableName = 'user_favorites';

  // Konstruktor angepasst
  FavoritesNotifier(this._supabaseClient, this._textExtractionService) : super(const AsyncValue.loading()) {
    _listenToAuthState();
  }

  // Hört auf Änderungen des Supabase Auth-Status
  void _listenToAuthState() {
    _authStateSubscription?.cancel(); // Altes Abo zuerst kündigen
    _authStateSubscription = _supabaseClient.auth.onAuthStateChange.listen(
      (authState) {
        final user = authState.session?.user;
        _currentUser = user; // Speichere den Supabase User
        _favoritesSubscription
            ?.cancel(); // Favoriten-Abo kündigen, wenn Auth-Status sich ändert

        if (user != null) {
          print(
            "[FavoritesNotifier] Supabase User eingeloggt (${user.id}), lade Favoriten...",
          );
          _listenToFavorites(user.id); // Verwende Supabase User ID
        } else {
          print(
            "[FavoritesNotifier] Supabase User ausgeloggt, Favoriten zurücksetzen.",
          );
          state = const AsyncValue.data([]); // Setze auf leere Liste
        }
      },
      onError: (error) {
        // Fehler im Auth-Stream behandeln
        print("[FavoritesNotifier] Fehler im Supabase Auth Stream: $error");
        state = AsyncValue.error(error, StackTrace.current);
        _currentUser = null; // Sicherstellen, dass User null ist
        _favoritesSubscription?.cancel(); // Auch hier Favoriten-Abo kündigen
      },
    );
  }

  // Hört auf Änderungen im Favoriten-Dokument des Benutzers
  void _listenToFavorites(String userId) {
    state = const AsyncValue.loading();

    // Einmalig laden, da Supabase keine Echtzeit-Updates für Tabellen bietet
    _loadFavorites(userId);

    // Subscription für zukünftige manuelle Updates
    _favoritesSubscription = Stream.periodic(
      const Duration(seconds: 30),
    ).listen((_) {
      _loadFavorites(userId);
    });
  }

  // Lädt die Favoriten aus Supabase
  Future<void> _loadFavorites(String userId) async {
    try {
      // Lade Favoriten aus Supabase und sortiere absteigend nach updated_at
      final response = await _supabaseClient
          .from(_tableName)
          .select('*')
          .eq('user_id', userId)
          .order(
            'updated_at',
            ascending: false,
          ); // Sortiere nach updated_at absteigend

      if (response.isNotEmpty) {
        // Konvertiere jede Zeile in ein JobEntity-Objekt
        final favoriteJobs = <JobEntity>[];

        for (final row in response) {
          try {
            // Verwende job_data als Quelle für das JobEntity
            final jobData = row['job_data'];
            if (jobData != null) {
              final job = JobEntity.fromJson(
                Map<String, dynamic>.from(jobData),
              );
              favoriteJobs.add(job);
            }
          } catch (e) {
            print(
              "Fehler beim Deserialisieren eines Favoriten-Jobs: $e, Row: $row",
            );
          }
        }

        state = AsyncValue.data(favoriteJobs);
        print(
          "Favoriten (JobEntity) aus Supabase geladen: ${favoriteJobs.length} Stück",
        );
      } else {
        state = const AsyncValue.data([]);
        print("Keine Favoriten für User $userId gefunden.");
      }
    } catch (error) {
      print("Fehler beim Laden der Favoriten (JobEntity) aus Supabase: $error");
      state = AsyncValue.error(error, StackTrace.current);
    }
  }

  // Fügt einen Job zu den Favoriten hinzu (verwendet Supabase User ID)
  Future<void> addFavorite(JobEntity job) async {
    if (_currentUser == null) {
      print("[FavoritesNotifier] addFavorite abgebrochen: Kein Supabase User.");
      return;
    }
    final userId = _currentUser!.id; // Supabase ID verwenden
    final jobMap = job.toJson(); // JobEntity in Map umwandeln
    final now =
        DateTime.now().toIso8601String(); // Aktuelles Datum für updated_at

    // Stelle sicher, dass keine ungültigen Werte hinzugefügt werden
    if (jobMap.containsValue(null)) {
      print(
        "Warnung: Job-Map enthält null-Werte, überspringe Hinzufügen: $jobMap",
      );
      // return; // Oder Fehler werfen / null-Werte entfernen?
    }

    try {
      // NEU: Text-Extraktion im Hintergrund starten (non-blocking)
      _extractJobTextInBackground(job, DateTime.now());

      // Prüfe, ob bereits ein Eintrag für diesen Benutzer und Job existiert
      final existingData =
          await _supabaseClient
              .from(_tableName)
              .select('id')
              .eq('user_id', userId)
              .eq('job_id', job.id)
              .maybeSingle();

      if (existingData != null) {
        // Eintrag existiert bereits, aktualisiere ihn
        await _supabaseClient
            .from(_tableName)
            .update({'job_data': jobMap, 'updated_at': now})
            .eq('id', existingData['id']);
      } else {
        // Kein Eintrag vorhanden, erstelle einen neuen
        await _supabaseClient.from(_tableName).insert({
          'user_id': userId,
          'job_id': job.id,
          'job_data': jobMap,
          'is_applied': false,
          'notes': '',
          'updated_at': now, // Aktuelles Datum für neue Einträge
        });
      }

      print(
        "Job ${job.id} zu Supabase-Favoriten für User $userId hinzugefügt.",
      );

      // Aktualisiere den State
      _loadFavorites(userId);
    } catch (e) {
      print("Fehler beim Hinzufügen des Favoriten zu Supabase: $e");
    }
  }

  // Stellt einen gelöschten Favoriten wieder her
  Future<void> restoreFavorite(JobEntity job) async {
    // Verwende die vorhandene addFavorite Methode, damit wir den Code nicht duplizieren
    await addFavorite(job);
  }

  // Entfernt einen Job aus den Favoriten (verwendet Supabase User ID)
  Future<void> removeFavorite(String jobId) async {
    if (_currentUser == null) {
      print(
        "[FavoritesNotifier] removeFavorite abgebrochen: Kein Supabase User.",
      );
      return;
    }
    final userId = _currentUser!.id; // Supabase ID verwenden

    try {
      // Lösche den Eintrag direkt
      await _supabaseClient
          .from(_tableName)
          .delete()
          .eq('user_id', userId)
          .eq('job_id', jobId);

      print("Job $jobId aus Supabase-Favoriten für User $userId entfernt.");

      // Aktualisiere den State
      _loadFavorites(userId);
    } catch (e) {
      print("Fehler beim Entfernen des Favoriten aus Supabase: $e");
    }
  }

  // Prüft, ob ein Job ein Favorit ist (aus dem aktuellen State)
  bool isFavorite(String jobId) {
    return state.maybeWhen(
      data: (favs) => favs.any((job) => job.id == jobId),
      orElse: () => false,
    );
  }

  /// NEU: Extrahiert Job-Text im Hintergrund (non-blocking)
  void _extractJobTextInBackground(JobEntity job, DateTime favoriteAddedAt) {
    // Starte Text-Extraktion asynchron ohne auf das Ergebnis zu warten
    _textExtractionService.extractAndCacheJobText(
      job,
      sourceType: 'favorite',
      sourceTimestamp: favoriteAddedAt,
    ).then((extractedText) {
      if (extractedText != null) {
        print("Text für Job ${job.id} erfolgreich extrahiert und gecacht (${(extractedText).getSizeInKB().toStringAsFixed(1)}KB)");
      } else {
        print("Text-Extraktion für Job ${job.id} fehlgeschlagen");
      }
    }).catchError((error) {
      print("Fehler bei Text-Extraktion für Job ${job.id}: $error");
    });
  }

  /// NEU: Lädt extrahierten Text für einen Job (falls verfügbar)
  Future<ExtractedJobText?> getExtractedText(String jobId) async {
    try {
      return await _textExtractionService.extractAndCacheJobText(
        // Erstelle ein minimales JobEntity nur für Cache-Lookup
        JobEntity(
          id: jobId,
          title: '',
          companyName: '',
          location: '',
          publishedDate: DateTime.now(),
          description: '',
        ),
      );
    } catch (e) {
      print("Fehler beim Laden des extrahierten Texts für Job $jobId: $e");
      return null;
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _favoritesSubscription?.cancel();
    _textExtractionService.dispose(); // NEU: Service cleanup
    print("[FavoritesNotifier] Disposed.");
    super.dispose();
  }
}

/// NEU: Provider für TextExtractionService
final textExtractionServiceProvider = Provider<TextExtractionService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  return TextExtractionService(supabaseClient);
});

/// Der Provider, der die Instanz von FavoritesNotifier bereitstellt
final favoritesProvider =
    StateNotifierProvider<FavoritesNotifier, AsyncValue<List<JobEntity>>>((
      ref,
    ) {
      // Greife auf den SupabaseClient und TextExtractionService über Provider zu
      final supabaseClient = ref.watch(supabaseClientProvider);
      final textExtractionService = ref.watch(textExtractionServiceProvider);
      return FavoritesNotifier(supabaseClient, textExtractionService);
    });

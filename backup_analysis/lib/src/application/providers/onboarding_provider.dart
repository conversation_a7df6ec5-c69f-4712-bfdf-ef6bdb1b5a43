
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/utils/logging.dart';

const _onboardingCompleteKey = 'onboarding_complete';

// State Notifier, der den Onboarding-Status verwaltet
class OnboardingNotifier extends StateNotifier<bool> {
  OnboardingNotifier(this._prefs) : super(false) {
    _loadStatus();
  }

  final SharedPreferences _prefs;
  final _log = getLogger('OnboardingNotifier');

  // Lädt den Status aus SharedPreferences beim Initialisieren
  Future<void> _loadStatus() async {
    try {
      // Prüfe, ob der Provider noch mounted ist
      if (!mounted) {
        _log.w("Provider wurde vor Supabase-Abfrage disposed");
        return;
      }

      // Prüfe, ob der Benutzer in Supabase angemeldet ist
      final supabase = Supabase.instance.client;
      final currentUser = supabase.auth.currentUser;

      // Wenn ein Benutzer angemeldet ist, lade den Onboarding-Status aus Supabase
      if (currentUser != null) {
        // Setze has_existing_account auf true
        await _prefs.setBool('has_existing_account', true);

        try {
          // Lade den Onboarding-Status aus Supabase
          final profileResponse =
              await supabase
                  .from('profiles')
                  .select('onboarding_complete')
                  .eq('id', currentUser.id)
                  .maybeSingle();

          if (profileResponse != null &&
              profileResponse['onboarding_complete'] != null) {
            // Verwende den Onboarding-Status aus Supabase
            final onboardingComplete =
                profileResponse['onboarding_complete'] as bool;
            if (mounted) {
              state = onboardingComplete;
            }
            await _prefs.setBool(_onboardingCompleteKey, onboardingComplete);
            _log.i(
              "Onboarding-Status aus Supabase geladen: $onboardingComplete für Benutzer ${currentUser.id}",
            );
          } else {
            // Wenn kein Profil gefunden wurde oder onboarding_complete null ist,
            // belasse den Status auf false für neue Benutzer
            if (mounted) {
              state = false;
            }
            await _prefs.setBool(_onboardingCompleteKey, false);
            _log.i(
              "Kein Onboarding-Status in Supabase gefunden, setze auf false für Benutzer ${currentUser.id}",
            );
          }
        } catch (e) {
          _log.e(
            "Fehler beim Laden des Onboarding-Status aus Supabase",
            error: e,
          );
          // Bei Fehler den Status auf false setzen, damit der Onboarding-Screen angezeigt wird
          if (mounted) {
            state = false;
          }
          await _prefs.setBool(_onboardingCompleteKey, false);
        }

        return;
      }

      // Wenn kein Benutzer angemeldet ist, prüfe die lokalen Einstellungen
      final hasExistingAccount =
          _prefs.getBool('has_existing_account') ?? false;

      // Wenn der Benutzer bereits einen Account hat, überspringe das Onboarding immer
      if (hasExistingAccount) {
        state = true;
        await _prefs.setBool(_onboardingCompleteKey, true);
        _log.i(
          "Onboarding übersprungen für bestehenden Benutzer (has_existing_account=true)",
        );
        return;
      }

      // Für neue Benutzer oder wenn der Status nicht aus Supabase geladen werden konnte:
      // Mehrfache Versuche mit Verzögerung
      int attempts = 0;
      bool? savedState;

      while (attempts < 3) {
        await _prefs.reload();
        savedState = _prefs.getBool(_onboardingCompleteKey);
        if (savedState != null) break;

        attempts++;
        if (attempts < 3) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      if (savedState == null) {
        // Kein Wert gespeichert - initialisiere mit false für neue Benutzer
        state = false;
        await _prefs.setBool(_onboardingCompleteKey, false);
        await _prefs.reload(); // Nochmal neu laden nach dem Setzen
      } else {
        state = savedState;
      }

      _log.d(
        "Validierter Onboarding-Status: $state (Gespeichert: ${_prefs.getBool(_onboardingCompleteKey)})",
      );
    } catch (e) {
      _log.e("Kritischer Fehler beim Laden des Onboarding-Status", error: e);
      state = false;
      await _prefs.setBool(_onboardingCompleteKey, false);
    }
  }

  // Markiert das Onboarding als abgeschlossen und speichert es
  Future<void> completeOnboarding() async {
    try {
      _log.i("Onboarding wird als abgeschlossen markiert...");
      state = true;

      // Mehrfacher Speicherversuch mit Verzögerung
      int attempts = 0;
      bool success = false;

      while (attempts < 3 && !success) {
        await _prefs.setBool(_onboardingCompleteKey, true);
        await _prefs.setBool(
          'has_existing_account',
          true,
        ); // Wichtig: Setze auch has_existing_account
        await _prefs.reload();

        if (_prefs.getBool(_onboardingCompleteKey) == true &&
            _prefs.getBool('has_existing_account') == true) {
          success = true;
          _log.i("Onboarding-Status lokal gespeichert");
        } else {
          attempts++;
          if (attempts < 3) {
            _log.w(
              "Speicherversuch $attempts fehlgeschlagen, versuche erneut...",
            );
            await Future.delayed(const Duration(milliseconds: 200));
          }
        }
      }

      if (!success) {
        throw Exception(
          "Onboarding status konnte nach 3 Versuchen nicht gespeichert werden",
        );
      }

      // Speichere den Onboarding-Status auch in Supabase, wenn der Benutzer angemeldet ist
      final supabase = Supabase.instance.client;
      final currentUser = supabase.auth.currentUser;

      if (currentUser != null) {
        try {
          _log.i(
            "Speichere Onboarding-Status in Supabase für Benutzer ${currentUser.id}...",
          );

          // Prüfe, ob der Benutzer bereits ein Profil hat
          final existingProfile =
              await supabase
                  .from('profiles')
                  .select('id, data, onboarding_complete')
                  .eq('id', currentUser.id)
                  .maybeSingle();

          if (existingProfile != null) {
            // Aktualisiere direkt die Tabellenspalte onboarding_complete
            await supabase
                .from('profiles')
                .update({'onboarding_complete': true})
                .eq('id', currentUser.id);

            _log.i(
              "Onboarding-Status in Tabellenspalte gespeichert für Benutzer ${currentUser.id}",
            );

            // Aktualisiere auch die Profildaten, wenn vorhanden
            if (existingProfile.containsKey('data') &&
                existingProfile['data'] != null) {
              final data = existingProfile['data'] as Map<String, dynamic>;

              // Füge den Onboarding-Status hinzu
              data['onboardingComplete'] = true;

              // Speichere das aktualisierte Profil
              await supabase.from('profiles').upsert({
                'id': currentUser.id,
                'data': data,
                'onboarding_complete':
                    true, // Stelle sicher, dass beide Felder aktualisiert werden
              });

              _log.i(
                "Onboarding-Status in Profildaten gespeichert für Benutzer ${currentUser.id}",
              );
            }
          } else {
            // Erstelle ein neues Profil mit onboarding_complete = true
            final newProfile = {
              'id': currentUser.id,
              'email': currentUser.email,
              'onboardingComplete': true,
            };

            await supabase.from('profiles').upsert({
              'id': currentUser.id,
              'data': newProfile,
              'onboarding_complete': true,
            });

            _log.i(
              "Neues Profil mit abgeschlossenem Onboarding erstellt für Benutzer ${currentUser.id}",
            );
          }
        } catch (e) {
          _log.e(
            "Fehler beim Speichern des Onboarding-Status in Supabase",
            error: e,
          );
          // Fehler beim Speichern in Supabase sollte den lokalen Status nicht beeinflussen
        }
      } else {
        _log.w(
          "Kein angemeldeter Benutzer gefunden, Onboarding-Status wird nur lokal gespeichert",
        );
      }

      _log.i(
        "Onboarding status saved: $state (Verifiziert: ${_prefs.getBool(_onboardingCompleteKey)})",
      );
    } catch (e) {
      _log.e(
        "Kritischer Fehler beim Speichern des Onboarding-Status",
        error: e,
      );
      state = false;
      await _prefs.setBool(_onboardingCompleteKey, false);
      await _prefs.reload();
    }
  }

  // Setzt den Onboarding-Status zurück (nützlich für Tests)
  Future<void> resetOnboarding() async {
    state = false;
    await _prefs.remove(_onboardingCompleteKey);
    _log.i("Onboarding status reset.");
  }
}

// Eine einfache Implementierung von SharedPreferences für den Fallback-Fall
class TempPreferences implements SharedPreferences {
  final Map<String, Object> _values = {};

  @override
  Set<String> getKeys() => _values.keys.toSet();

  @override
  Object? get(String key) => _values[key];

  @override
  bool? getBool(String key) => _values[key] as bool?;

  @override
  double? getDouble(String key) => _values[key] as double?;

  @override
  int? getInt(String key) => _values[key] as int?;

  @override
  String? getString(String key) => _values[key] as String?;

  @override
  List<String>? getStringList(String key) => _values[key] as List<String>?;

  @override
  bool containsKey(String key) => _values.containsKey(key);

  @override
  Future<bool> clear() async {
    _values.clear();
    return true;
  }

  @override
  Future<bool> remove(String key) async {
    _values.remove(key);
    return true;
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setString(String key, String value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> commit() async => true;

  @override
  Future<bool> reload() async => true;
}

// Provider, der SharedPreferences bereitstellt (asynchron)
final sharedPreferencesProvider = FutureProvider<SharedPreferences>((
  ref,
) async {
  return await SharedPreferences.getInstance();
});

// Ein Fallback-Provider mit Standardwert
final cachedPrefsProvider = Provider<SharedPreferences?>((ref) {
  final prefsAsync = ref.watch(sharedPreferencesProvider);
  return prefsAsync.when(
    data: (prefs) => prefs,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Provider für den OnboardingNotifier mit sicherem Fallback
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, bool>((
  ref,
) {
  final prefs = ref.watch(cachedPrefsProvider);
  if (prefs == null) {
    // Temporärer In-Memory-Provider mit Standardwert true
    final tempPrefs = TempPreferences();
    tempPrefs.setBool(
      _onboardingCompleteKey,
      true,
    ); // Annahme: Onboarding ist abgeschlossen
    return OnboardingNotifier(tempPrefs);
  }
  return OnboardingNotifier(prefs);
});

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'applied_jobs_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'services_providers.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';

/// Cache für beworbene Jobs, um Flackern zu vermeiden
final _appliedJobsCache = StateProvider<List<JobEntity>>((ref) => []);

/// Provider, der die vollständigen JobEntity-Objekte für beworbene Jobs bereitstellt
final appliedJobEntitiesProvider = FutureProvider<List<JobEntity>>((ref) async {
  final log = getLogger('appliedJobEntitiesProvider');

  // Beobachte den Zustand der beworbenen Jobs (IDs)
  final appliedJobsAsyncValue = ref.watch(appliedJobsProvider);

  // Hole den aktuellen Cache
  final cachedJobs = ref.read(_appliedJobsCache);

  // Wenn keine beworbenen Jobs vorhanden sind, gib eine leere Liste zurück
  if (appliedJobsAsyncValue is! AsyncData<List<String>>) {
    log.i('Keine beworbenen Jobs vorhanden oder noch am Laden');
    // Wenn wir einen Cache haben, verwenden wir diesen
    if (cachedJobs.isNotEmpty) {
      log.i('Verwende Cache mit ${cachedJobs.length} Jobs während des Ladens');
      return cachedJobs;
    }
    return [];
  }

  final appliedJobIds = appliedJobsAsyncValue.value;

  // Wenn keine beworbenen Jobs vorhanden sind, gib eine leere Liste zurück
  if (appliedJobIds.isEmpty) {
    log.i('Keine beworbenen Jobs vorhanden');
    // Cache leeren, wenn keine Jobs mehr vorhanden sind
    if (cachedJobs.isNotEmpty) {
      ref.read(_appliedJobsCache.notifier).state = [];
    }
    return [];
  }

  // Wenn wir einen Cache haben und die IDs übereinstimmen, verwenden wir den Cache
  if (cachedJobs.isNotEmpty &&
      cachedJobs.length == appliedJobIds.length &&
      cachedJobs.every((job) => appliedJobIds.contains(job.id))) {
    log.i('Verwende Cache mit ${cachedJobs.length} Jobs (IDs unverändert)');
    return cachedJobs;
  }

  log.i(
    'Lade ${appliedJobIds.length} beworbene Jobs aus Supabase im Hintergrund',
  );

  try {
    // Hole die JobEntity-Objekte aus Supabase
    final supabaseClient = ref.read(supabaseClientProvider);
    final List<JobEntity> jobs = [];

    // Optimierte Abfrage: Lade alle Jobs in einer einzigen Abfrage
    // Supabase unterstützt zwar kein direktes "whereIn", aber wir können
    // die "in"-Bedingung mit einem Filter verwenden
    if (appliedJobIds.isNotEmpty) {
      final response = await supabaseClient
          .from('jobs')
          .select()
          .filter('id', 'in', appliedJobIds);

      for (final item in response) {
        jobs.add(JobEntity.fromJson(item));
      }
    }

    log.i('${jobs.length} beworbene Jobs geladen');

    // Aktualisiere den Cache mit den neuen Jobs
    ref.read(_appliedJobsCache.notifier).state = jobs;

    // Wenn wir bereits einen Cache haben, aber die neuen Jobs anders sind,
    // geben wir trotzdem den Cache zurück, um Flackern zu vermeiden
    if (cachedJobs.isNotEmpty) {
      log.i(
        'Verwende Cache mit ${cachedJobs.length} Jobs während der Aktualisierung',
      );
      return cachedJobs;
    }

    return jobs;
  } catch (e) {
    log.e('Fehler beim Laden der beworbenen Jobs: $e');
    // Bei einem Fehler verwenden wir den Cache, wenn vorhanden
    if (cachedJobs.isNotEmpty) {
      log.i('Verwende Cache mit ${cachedJobs.length} Jobs nach Fehler');
      return cachedJobs;
    }
    return [];
  }
});

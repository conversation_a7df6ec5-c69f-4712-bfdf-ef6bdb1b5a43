import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
// Für Datumsformatierung

import '../../domain/models/user_profile.dart'; // Korrigierter Importpfad
import '../../domain/models/job_preferences.dart'; // Import für JobPreferences

// Datenmodell für den Onboarding-Zustand
@immutable
class OnboardingState {
  final String? name;
  final String? phoneNumber;
  final List<String> skills;
  final WorkExperience? latestExperience; // Nur die letzte Erfahrung
  final Education? latestEducation; // Nur der letzte Abschluss
  final JobPreferences jobPreferences;
  final String? cvFilePath;

  const OnboardingState({
    this.name,
    this.phoneNumber,
    this.skills = const [],
    this.latestExperience,
    this.latestEducation,
    this.jobPreferences = const JobPreferences(), // Default leer
    this.cvFilePath,
  });

  // Methode zum Kopieren und Aktualisieren
  OnboardingState copyWith({
    String? name,
    String? phoneNumber,
    List<String>? skills,
    WorkExperience? latestExperience,
    ValueGetter<WorkExperience?>?
    latestExperienceUpdater, // Für optionale Updates
    Education? latestEducation,
    ValueGetter<Education?>? latestEducationUpdater, // Für optionale Updates
    JobPreferences? jobPreferences,
    String? cvFilePath,
    ValueGetter<String?>? cvFilePathUpdater, // Für optionale Updates
  }) {
    return OnboardingState(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      skills: skills ?? this.skills,
      latestExperience:
          latestExperienceUpdater != null
              ? latestExperienceUpdater()
              : latestExperience ?? this.latestExperience,
      latestEducation:
          latestEducationUpdater != null
              ? latestEducationUpdater()
              : latestEducation ?? this.latestEducation,
      jobPreferences: jobPreferences ?? this.jobPreferences,
      cvFilePath:
          cvFilePathUpdater != null
              ? cvFilePathUpdater()
              : cvFilePath ?? this.cvFilePath,
    );
  }
}

// StateNotifier für den Onboarding-Prozess
class OnboardingStateNotifier extends StateNotifier<OnboardingState> {
  OnboardingStateNotifier() : super(const OnboardingState());

  // --- Update-Methoden für jeden Schritt ---

  void updateName(String name) {
    state = state.copyWith(name: name);
  }

  void updatePhoneNumber(String phone) {
    state = state.copyWith(phoneNumber: phone);
  }

  void updateSkills(List<String> skills) {
    state = state.copyWith(skills: skills);
  }

  void updateLatestExperience({
    String? position,
    String? company,
    DateTime? startDate,
    DateTime? endDate,
    bool isCurrent = false,
  }) {
    // Erstelle ein neues WorkExperience Objekt oder null
    WorkExperience? experience;
    if (position != null &&
        position.isNotEmpty &&
        company != null &&
        company.isNotEmpty &&
        startDate != null) {
      experience = WorkExperience(
        position: position,
        company: company,
        startDate: startDate,
        endDate:
            isCurrent ? null : endDate, // Setze endDate auf null, wenn aktuell
        description: '', // Beschreibung wird im Onboarding nicht abgefragt
      );
    }
    // Verwende ValueGetter, um sicherzustellen, dass null korrekt gesetzt wird, wenn keine Daten vorhanden sind
    state = state.copyWith(latestExperienceUpdater: () => experience);
  }

  void updateLatestEducation({
    String? institution,
    String? degree,
    String? fieldOfStudy,
    DateTime? startDate,
    DateTime? endDate,
    bool isCurrent = false,
  }) {
    Education? education;
    if (institution != null &&
        institution.isNotEmpty &&
        degree != null &&
        degree.isNotEmpty &&
        startDate != null) {
      education = Education(
        institution: institution,
        degree: degree,
        fieldOfStudy: fieldOfStudy?.isNotEmpty == true ? fieldOfStudy! : '',
        startDate: startDate,
        endDate: isCurrent ? null : endDate,
        // description: '', // Nicht im Education-Modell
      );
    }
    state = state.copyWith(latestEducationUpdater: () => education);
  }

  void updateJobPreferences({
    String? targetPosition,
    String? industry,
    String? locationPreference,
    double? desiredSalary,
    String? employmentType,
  }) {
    state = state.copyWith(
      jobPreferences: state.jobPreferences.copyWith(
        targetPosition: targetPosition,
        industry: industry,
        locationPreference: locationPreference,
        desiredSalary: desiredSalary,
        employmentType: employmentType,
      ),
    );
  }

  void updateCvPath(String? path) {
    // Verwende ValueGetter, um null korrekt zu setzen
    state = state.copyWith(cvFilePathUpdater: () => path);
  }

  // Methode zum Zurücksetzen des Zustands (optional)
  void resetState() {
    state = const OnboardingState();
  }
}

// Provider für den OnboardingStateNotifier
final onboardingStateProvider =
    StateNotifierProvider<OnboardingStateNotifier, OnboardingState>((ref) {
      return OnboardingStateNotifier();
    });

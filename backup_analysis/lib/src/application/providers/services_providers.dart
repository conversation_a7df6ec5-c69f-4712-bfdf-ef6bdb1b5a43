import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:ki_test/src/infrastructure/services/payment_service.dart';
import 'package:ki_test/src/infrastructure/services/ad_service.dart';
import 'user_profile_provider.dart'; // Import für UserProfileNotifier
import 'package:supabase_flutter/supabase_flutter.dart'; // NEU: Supabase Import
import 'package:ki_test/src/infrastructure/services/supabase_service.dart';
import 'package:ki_test/src/application/services/email_service.dart'; // Import für EmailService
import 'package:ki_test/src/application/services/gmail_service.dart'; // Import für GmailService
import 'package:ki_test/src/application/services/translation_service.dart';
import 'package:ki_test/src/infrastructure/services/in_app_auth_service.dart'; // Import für InAppAuthService
import 'package:ki_test/src/infrastructure/services/application_counter_service.dart'; // Import für ApplicationCounterService
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart'; // Import für SubscriptionManagementService
import 'package:ki_test/src/infrastructure/services/premium_feature_manager.dart'; // Import für PremiumFeatureManager
import 'package:ki_test/src/infrastructure/api/agentur_arbeit_api_client.dart'; // Import für AgenturArbeitApiClient
import 'package:ki_test/src/infrastructure/api/ausbildung_arbeit_api_client.dart'; // Import für AusbildungArbeitApiClient
import 'package:ki_test/src/infrastructure/api/job_api_client.dart'; // Import für JobApiClient
import 'package:ki_test/src/application/services/additional_documents_service.dart'; // Import für AdditionalDocumentsService

/// Provider für den AgenturArbeitApiClient (Jobsuche)
final agenturArbeitApiClientProvider = Provider<JobApiClient>((ref) {
  return AgenturArbeitApiClient();
});

/// Provider für den AusbildungArbeitApiClient (Ausbildungssuche)
final ausbildungArbeitApiClientProvider = Provider<JobApiClient>((ref) {
  return AusbildungArbeitApiClient();
});

/// Provider für den PaymentService.
///
/// Dieser Provider stellt sicher, dass der PaymentService mit dem notwendigen
/// UserProfileNotifier und SupabaseSubscriptionService initialisiert wird.
final paymentServiceProvider = Provider<PaymentService>((ref) {
  final userProfileNotifier = ref.watch(userProfileProvider.notifier);

  // Korrigiere den Konstruktoraufruf: nur die benötigten Parameter übergeben
  final service = PaymentService(InAppPurchase.instance, userProfileNotifier);

  return service;
});

/// Provider für den AdService.
final adServiceProvider = Provider<AdService>((ref) {
  return AdService();
});

/// Provider für den SupabaseService
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return SupabaseService(client);
});

/// NEU: Provider für den Supabase Client
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

/// Provider für den TranslationService
final translationServiceProvider = Provider<TranslationService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return TranslationService(client);
});

/// Provider für den InAppAuthService
final inAppAuthServiceProvider = Provider<InAppAuthService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return InAppAuthService(client);
});

/// Provider für den Premium-Status des Benutzers
final isPremiumProvider = Provider<bool>((ref) {
  // Lese den Premium-Status aus dem UserProfile
  final userProfileState = ref.watch(userProfileProvider);

  // Wenn das UserProfile geladen ist und der Benutzer Premium ist, gib true zurück
  return userProfileState.maybeWhen(
    data: (userProfile) {
      // Prüfe, ob der Benutzer Premium ist und ob das Ablaufdatum in der Zukunft liegt
      if (userProfile.isPremium ?? false) {
        // Wenn kein Ablaufdatum gesetzt ist oder das Ablaufdatum in der Zukunft liegt
        if (userProfile.premiumExpiryDate == null ||
            userProfile.premiumExpiryDate!.isAfter(DateTime.now())) {
          return true;
        }
      }
      return false;
    },
    // Standardmäßig nicht Premium
    orElse: () => false,
  );
});

/// Provider für den Premium-Override-Status
/// Dieser Provider wird in PremiumFeature-Widgets verwendet, um zu entscheiden,
/// ob Premium-Features angezeigt werden sollen.
/// Standardmäßig gibt er den Wert von isPremiumProvider zurück.
final premiumOverrideProvider = Provider<bool>((ref) {
  // Verwende den isPremiumProvider, um den tatsächlichen Premium-Status zu erhalten
  return ref.watch(isPremiumProvider);
});

// Beispiel: Provider für SupabaseSubscriptionService (falls benötigt)
// Dieser Provider muss *vor* dem paymentServiceProvider definiert sein, wenn er direkt gelesen wird.
/*
final supabaseSubscriptionServiceProvider = Provider<SupabaseSubscriptionService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider); // Oder supabaseServiceProvider
  // Stelle sicher, dass SupabaseSubscriptionService existiert und korrekt importiert wird
  return SupabaseSubscriptionService(supabaseClient);
});
*/

/// Provider für den ApplicationCounterService
final applicationCounterServiceProvider = Provider<ApplicationCounterService>((
  ref,
) {
  final client = ref.watch(supabaseClientProvider);
  return ApplicationCounterService(client, ref);
});

/// Provider für den SubscriptionManagementService
final subscriptionManagementServiceProvider =
    Provider<SubscriptionManagementService>((ref) {
      final client = ref.watch(supabaseClientProvider);
      return SubscriptionManagementService(client, ref);
    });

/// Provider für die verbleibenden Bewerbungen
final remainingApplicationsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.getRemainingApplications();
});

/// Provider für das nächste Reset-Datum für kostenlose Bewerbungen
final nextFreeResetDateProvider = FutureProvider<DateTime?>((ref) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.getNextFreeResetDate();
});

/// Provider für den Premium-Status (Future-basiert)
final hasPremiumSubscriptionProvider = FutureProvider<bool>((ref) async {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  return await subscriptionService.hasPremiumSubscription();
});

/// Provider für den PremiumFeatureManager
final premiumFeatureManagerProvider = Provider<PremiumFeatureManager>((ref) {
  final subscriptionService = ref.watch(subscriptionManagementServiceProvider);
  final adService = ref.watch(adServiceProvider);
  return PremiumFeatureManager(subscriptionService, adService, ref);
});

/// Provider für den GmailService
final gmailServiceProvider = Provider<GmailService>((ref) {
  return GmailService();
});

/// Provider für den EmailService
final emailServiceProvider = Provider<EmailService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  final gmailService = ref.watch(gmailServiceProvider);
  return EmailService(client, gmailService);
});

/// Provider für den AdditionalDocumentsService
final additionalDocumentsServiceProvider = Provider<AdditionalDocumentsService>((ref) {
  return AdditionalDocumentsService();
});

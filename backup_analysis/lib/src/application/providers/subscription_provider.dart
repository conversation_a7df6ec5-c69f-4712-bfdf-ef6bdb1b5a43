import 'dart:async';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/models/subscription.dart';
import '../services/logger.dart';
import 'services_providers.dart';

/// Provider für den aktuellen Abonnement-Status des Benutzers
final subscriptionProvider = StateNotifierProvider<SubscriptionNotifier, AsyncValue<Subscription?>>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  return SubscriptionNotifier(supabaseClient);
});

/// State-Notifier zur Verwaltung des Abonnement-Status
class SubscriptionNotifier extends StateNotifier<AsyncValue<Subscription?>> {
  final SupabaseClient _supabaseClient;
  StreamSubscription? _subscriptionStream;
  final Logger _log = Logger('SubscriptionNotifier');

  SubscriptionNotifier(this._supabaseClient) : super(const AsyncValue.loading()) {
    _initialize();
  }

  /// Initialisiert den Notifier und lädt das aktuelle Abonnement
  Future<void> _initialize() async {
    try {
      // Prüfen, ob ein Benutzer angemeldet ist
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        state = const AsyncValue.data(null);
        return;
      }

      // Abonnement aus der Datenbank laden
      await _loadSubscription(userId);

      // Auf Änderungen der Abonnements in Echtzeit hören
      _subscribeToSubscriptionChanges(userId);
    } catch (e, stackTrace) {
      _log.e('Fehler beim Initialisieren des Subscription Providers: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Lädt das aktuelle Abonnement aus der Datenbank
  Future<void> _loadSubscription(String userId) async {
    try {
      state = const AsyncValue.loading();

      final response = await _supabaseClient
          .from('subscriptions')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (response != null) {
        final subscription = Subscription.fromJson(response);
        state = AsyncValue.data(subscription);
        _log.i('Abonnement geladen: ${subscription.status}, aktiv: ${subscription.isActive}');
      } else {
        state = const AsyncValue.data(null);
        _log.i('Kein Abonnement gefunden');
      }
    } catch (e, stackTrace) {
      _log.e('Fehler beim Laden des Abonnements: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Abonniert Echtzeit-Updates für Änderungen am Abonnement
  void _subscribeToSubscriptionChanges(String userId) {
    _subscriptionStream?.cancel();

    _subscriptionStream = _supabaseClient
        .from('subscriptions')
        .stream(primaryKey: ['id'])
        .eq('user_id', userId)
        .listen((List<Map<String, dynamic>> data) {
          if (data.isNotEmpty) {
            final subscription = Subscription.fromJson(data.first);
            state = AsyncValue.data(subscription);
            _log.i('Abonnement aktualisiert: ${subscription.status}, aktiv: ${subscription.isActive}');
          } else {
            state = const AsyncValue.data(null);
          }
        }, onError: (error) {
          _log.e('Fehler im Subscription Stream: $error');
        });
  }

  /// Erstellt oder aktualisiert ein Abonnement nach einem erfolgreichen Kauf
  Future<void> createOrUpdateSubscription({
    required String productId,
    required String platform,
    required DateTime expiresAt,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('Benutzer nicht angemeldet');
      }

      // Prüfen, ob bereits ein Abonnement existiert
      final currentSubscription = state.asData?.value;
      
      if (currentSubscription != null) {
        // Bestehendes Abonnement aktualisieren
        final updatedSubscription = currentSubscription.copyWith(
          status: 'active',
          productId: productId,
          platform: platform,
          expiresAt: expiresAt,
          cancelledAt: null,
          updatedAt: DateTime.now(),
        );

        await _supabaseClient
            .from('subscriptions')
            .update(updatedSubscription.toJson())
            .eq('id', currentSubscription.id);

        state = AsyncValue.data(updatedSubscription);
        _log.i('Abonnement aktualisiert: Läuft ab am ${expiresAt.toIso8601String()}');
      } else {
        // Neues Abonnement erstellen
        final newSubscription = Subscription(
          id: '', // ID wird von Supabase generiert
          userId: userId,
          status: 'active',
          productId: productId,
          platform: platform,
          expiresAt: expiresAt,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final response = await _supabaseClient
            .from('subscriptions')
            .insert(newSubscription.toJson())
            .select()
            .single();

        final createdSubscription = Subscription.fromJson(response);
        state = AsyncValue.data(createdSubscription);
        _log.i('Neues Abonnement erstellt: Läuft ab am ${expiresAt.toIso8601String()}');
      }
    } catch (e, stackTrace) {
      _log.e('Fehler beim Erstellen/Aktualisieren des Abonnements: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Markiert ein Abonnement als gekündigt
  Future<void> cancelSubscription() async {
    try {
      final subscription = state.asData?.value;
      if (subscription == null) {
        throw Exception('Kein aktives Abonnement vorhanden');
      }

      final cancelledSubscription = subscription.copyWith(
        status: 'cancelled',
        cancelledAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _supabaseClient
          .from('subscriptions')
          .update(cancelledSubscription.toJson())
          .eq('id', subscription.id);

      state = AsyncValue.data(cancelledSubscription);
      _log.i('Abonnement gekündigt');
    } catch (e, stackTrace) {
      _log.e('Fehler beim Kündigen des Abonnements: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Markiert ein Abonnement als abgelaufen
  Future<void> expireSubscription() async {
    try {
      final subscription = state.asData?.value;
      if (subscription == null) {
        throw Exception('Kein aktives Abonnement vorhanden');
      }

      final expiredSubscription = subscription.copyWith(
        status: 'expired',
        updatedAt: DateTime.now(),
      );

      await _supabaseClient
          .from('subscriptions')
          .update(expiredSubscription.toJson())
          .eq('id', subscription.id);

      state = AsyncValue.data(expiredSubscription);
      _log.i('Abonnement als abgelaufen markiert');
    } catch (e, stackTrace) {
      _log.e('Fehler beim Markieren des Abonnements als abgelaufen: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Prüft, ob der Benutzer ein aktives Abonnement hat
  bool get hasActiveSubscription {
    final subscription = state.asData?.value;
    return subscription?.isActive ?? false;
  }

  @override
  void dispose() {
    _subscriptionStream?.cancel();
    super.dispose();
  }
} 
 
 
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/utils/logging.dart';

/// Provider für automatische Onboarding-Prüfung nach der Anmeldung
final autoOnboardingProvider = Provider<AutoOnboardingService>((ref) {
  return AutoOnboardingService(ref);
});

/// Service für automatische Onboarding-Prüfung
class AutoOnboardingService {
  final Ref _ref;
  final _log = getLogger('AutoOnboardingService');
  bool _isChecking = false;

  AutoOnboardingService(this._ref);

  /// Prüft automatisch nach der Anmeldung ob Onboarding nötig ist
  Future<void> checkOnboardingAfterLogin(BuildContext context) async {
    if (_isChecking) {
      _log.d('Onboarding-Prüfung läuft bereits, überspringe');
      return;
    }

    _isChecking = true;
    
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter User, keine Onboarding-Prüfung');
        return;
      }

      _log.i('🔍 AutoOnboardingService: Starte automatische Onboarding-Prüfung für User: ${currentUser.id}');

      // Kurze Verzögerung um sicherzustellen, dass die Anmeldung abgeschlossen ist
      await Future.delayed(const Duration(milliseconds: 500));

      // LOKALE PRÜFUNG: Prüfe ob Onboarding bereits gezeigt wurde
      _log.i('📱 AutoOnboardingService: Prüfe lokalen Onboarding-Status...');
      final prefs = await SharedPreferences.getInstance();
      final onboardingShown = prefs.getBool('onboarding_shown_${currentUser.id}') ?? false;

      _log.i('✅ AutoOnboardingService: Lokaler Onboarding-Status für ${currentUser.id}: $onboardingShown');

      if (!onboardingShown && context.mounted) {
        _log.i('🎯 AutoOnboardingService: Erstes Mal - zeige Onboarding-Screen');

        // Markiere Onboarding als gezeigt BEVOR Navigation
        await prefs.setBool('onboarding_shown_${currentUser.id}', true);
        _log.i('💾 AutoOnboardingService: Onboarding-Status lokal gespeichert');

        // Navigiere zum Onboarding-Screen
        context.go('/onboarding');
        _log.i('✅ AutoOnboardingService: Navigation zum Onboarding-Screen abgeschlossen');
      } else if (context.mounted) {
        _log.i('✅ AutoOnboardingService: Onboarding bereits gezeigt - navigiere zur Haupt-App');

        // Navigiere zur Haupt-App wenn Onboarding bereits gezeigt wurde
        context.go('/');
        _log.i('✅ AutoOnboardingService: Navigation zur Haupt-App abgeschlossen');
      }

    } catch (e) {
      _log.e('Fehler bei lokaler Onboarding-Prüfung', error: e);
    } finally {
      _isChecking = false;
    }
  }

  /// Prüft ob User gerade angemeldet wurde (für automatische Trigger)
  Future<void> onUserSignedIn(BuildContext context) async {
    _log.i('🚀 AutoOnboardingService: User hat sich angemeldet, starte automatische Onboarding-Prüfung');

    // Warte kurz bis die Anmeldung vollständig abgeschlossen ist
    await Future.delayed(const Duration(milliseconds: 1000));

    if (context.mounted) {
      _log.i('🔍 AutoOnboardingService: Context ist mounted, starte checkOnboardingAfterLogin');
      await checkOnboardingAfterLogin(context);
    } else {
      _log.w('⚠️ AutoOnboardingService: Context ist nicht mehr mounted');
    }
  }

  /// Reset für Tests
  void reset() {
    _isChecking = false;
  }

  /// Setzt den lokalen Onboarding-Status für einen User zurück (für Tests)
  Future<void> resetOnboardingForUser(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('onboarding_shown_$userId');
    _log.i('🔄 AutoOnboardingService: Onboarding-Status für User $userId zurückgesetzt');
  }

  /// Setzt den lokalen Onboarding-Status für den aktuellen User zurück
  Future<void> resetOnboardingForCurrentUser() async {
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser != null) {
      await resetOnboardingForUser(currentUser.id);
    }
  }
}



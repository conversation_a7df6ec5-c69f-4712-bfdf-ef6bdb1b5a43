import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:flutter/foundation.dart';

/// VERALTET - NICHT MEHR VERWENDEN
/// 
/// Dieser Service wird nicht mehr aktiv genutzt.
/// Alle API-Aufrufe wurden in Firebase Cloud Functions verlagert.
/// Dies geschieht aus Sicherheitsgründen, um API-Schlüssel nicht im Client-Code zu speichern.
/// 
/// Für neue Implementierungen bitte den CloudFunctionsService verwenden.
@Deprecated('Bitte CloudFunctionsService verwenden. API-Schlüssel sind jetzt in Firebase Cloud Functions.')
class DeepseekService {
  Future<String> generateContent({
    required UserProfile userProfile,
    required JobEntity jobPosting,
    String? hints,
    String? stylePreference,
    bool includeExperience = true,
  }) async {
    debugPrint('⚠️ WARNUNG: DeepseekService.generateContent() ist veraltet und sollte nicht mehr verwendet werden.');
    debugPrint('Bitte verwenden Sie stattdessen CloudFunctionsService.generateCoverLetter().');
    
    // Dies ist ein Platzhalter. Die tatsächliche Logik zur Generierung
    // des Anschreibens mit der Deepseek API muss hier implementiert werden.
    print('--- Generating Content with Deepseek (Placeholder) ---');
    print('User Profile: ${userProfile.name}');
    print('Job Posting (Entity): ${jobPosting.title}');
    print('Hints: $hints');
    print('Style Preference: $stylePreference');
    print('Include Experience: $includeExperience');
    
    // Simuliere eine Netzwerkverzögerung
    await Future.delayed(const Duration(seconds: 2));
    
    return Future.value("Sehr geehrte Damen und Herren,\n\nhiermit bewerbe ich mich auf die ausgeschriebene Stelle als ${jobPosting.title}.\n\n(Dies ist ein automatisch generierter Platzhaltertext von DeepseekService.)\n\nMit freundlichen Grüßen,\n${userProfile.name}");
  }

  /// Generiert Text mit der Deepseek API
  /// VERALTET - Bitte verwenden Sie stattdessen CloudFunctionsService
  /// [prompt] ist der Prompt für die KI
  Future<String> generateText(String prompt) async {
    debugPrint('⚠️ WARNUNG: DeepseekService.generateText() ist veraltet und sollte nicht mehr verwendet werden.');
    debugPrint('Bitte verwenden Sie stattdessen CloudFunctionsService.generateCoverLetter().');
    
    const String deepSeekEndpoint = "https://api.deepseek.com/v1/chat/completions";
    const String modelToUse = "deepseek-chat";
    
    // Gebe direkt den Beispieltext zurück, ohne API-Aufruf zu versuchen
    debugPrint("⚠️ DeepSeek API wurde deaktiviert, verwende Platzhalter-Antwort");
    return '''
EMAIL: nicht gefunden
ANSCHREIBEN:
Sehr geehrte Damen und Herren,

Dies ist ein veralterter Service. Bitte verwenden Sie die neue Firebase Cloud Functions Implementation.

Mit freundlichen Grüßen,
Ihr App-Team''';
  }

  // Fügen Sie hier ggf. weitere Methoden für Deepseek hinzu
}
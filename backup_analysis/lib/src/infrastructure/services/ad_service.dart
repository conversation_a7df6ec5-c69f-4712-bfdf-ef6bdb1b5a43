import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'dart:io';
import 'dart:async';

import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/core/utils/system_ui_controller.dart';
import 'package:ki_test/src/presentation/common/widgets/ad_loading_animation.dart';

// Test Ad Unit IDs provided by Google
// Android Rewarded: ca-app-pub-3940256099942544/5224354917
// iOS Rewarded: ca-app-pub-3940256099942544/1712485313
// Verwende Test-IDs während der Entwicklung!
final String _rewardedAdUnitId =
    Platform.isAndroid
        ? 'ca-app-pub-5967659832492607/4314339432'
        : 'ca-app-pub-3940256099942544/1712485313'; // Google Test ID (iOS)

class AdService {
  static final log = getLogger('AdService');

  RewardedAd? _rewardedAd;
  bool _isShowingAd =
      false; // Verhindert das Anzeigen mehrerer Anzeigen gleichzeitig

  // Neue Variable für die zweite Werbung während der Generierung
  RewardedAd? _secondRewardedAd;
  bool _isShowingSecondAd =
      false; // Verhindert das Anzeigen mehrerer zweiten Anzeigen gleichzeitig

  // Flag, ob Werbung aktiviert ist (für Basic-Nutzer)
  // Startet deaktiviert und wird erst durch enableAds() aktiviert
  bool _adsEnabled = false;

  // Overlay für die Ladeanimation
  OverlayEntry? _loadingOverlay;
  BuildContext? _lastContext;

  // Zeitstempel für intelligente Werbung-Logik
  DateTime? _firstAdStartTime;
  DateTime? _firstAdEndTime;
  bool _shouldShowSecondAd = false;

  // Aktiviert Werbung (für Basic-Nutzer)
  void enableAds() {
    _adsEnabled = true;
    log.i('Werbung aktiviert (Basic-Nutzer)');
  }

  // Deaktiviert Werbung (für Premium-Nutzer)
  void disableAds() {
    _adsEnabled = false;
    log.i('Werbung deaktiviert (Premium-Nutzer)');
  }

  // Gibt zurück, ob Werbung aktiviert ist
  bool get areAdsEnabled => _adsEnabled;

  // Gibt zurück, ob eine zweite Werbung angezeigt werden soll (basierend auf der Dauer der ersten)
  bool get shouldShowSecondAd => _shouldShowSecondAd;

  // Setzt das Flag für die zweite Werbung zurück
  void resetSecondAdFlag() {
    _shouldShowSecondAd = false;
    _firstAdStartTime = null;
    _firstAdEndTime = null;
  }

  /// Zeigt eine Ladeanimation an, während die Werbung geladen wird
  void showLoadingAnimation(BuildContext context) {
    // Speichere den Context für spätere Verwendung
    _lastContext = context;

    // Entferne vorherige Overlays, falls vorhanden
    removeLoadingAnimation();

    // Erstelle ein neues Overlay
    _loadingOverlay = OverlayEntry(
      builder: (context) => AdLoadingAnimation(
        // Verwende Theme-Farben für die Ladeanimation
        backgroundColor: Theme.of(context).colorScheme.surface.withOpacity(0.85),
        loaderColor: Theme.of(context).colorScheme.primary,
        textColor: Theme.of(context).colorScheme.onSurface,
      ),
    );

    // Füge das Overlay zum Overlay-Stack hinzu
    Overlay.of(context).insert(_loadingOverlay!);

    log.i('Ladeanimation für Werbung angezeigt');
  }

  /// Entfernt die Ladeanimation
  void removeLoadingAnimation() {
    if (_loadingOverlay != null) {
      _loadingOverlay!.remove();
      _loadingOverlay = null;
      log.i('Ladeanimation für Werbung entfernt');
    }
  }

  // Initialisiert das Mobile Ads SDK. Muss beim App-Start aufgerufen werden.
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
    log.i("Google Mobile Ads SDK initialisiert.");

    // Setze die Test-Geräte-IDs
    RequestConfiguration configuration = RequestConfiguration(
      testDeviceIds: [
        '2077ef9a63d2b398840261c8221a0c9b', // Beispiel-Test-Geräte-ID
      ],
    );
    MobileAds.instance.updateRequestConfiguration(configuration);

    // Setze die Immersive Mode für Werbeanzeigen
    if (Platform.isAndroid) {
      try {
        // Verwende den Method Channel, um die Immersive Mode für Werbeanzeigen zu setzen
        const MethodChannel channel = MethodChannel('com.einsteinai.app/ads');
        await channel.invokeMethod('setImmersiveModeForAds', {'enabled': true});
        log.i('Immersive Mode für Werbeanzeigen aktiviert');
      } catch (e) {
        log.e('Fehler beim Setzen der Immersive Mode für Werbeanzeigen: $e');
      }
    }
  }

  // Lädt eine Rewarded Ad und zeigt sie direkt an, wenn sie geladen ist.
  // Akzeptiert Callbacks für Erfolg (Belohnung erhalten) und verschiedene Fehlerfälle.
  Future<void> loadAndShowRewardedAd({
    VoidCallback? onAdLoadedCallback,
    VoidCallback? onAdFailedToLoadCallback,
    VoidCallback? onAdFailedToShowCallback,
    VoidCallback? onAdDismissedCallback,
    required VoidCallback onUserEarnedRewardCallback,
    BuildContext? context,
  }) async {
    log.d("loadAndShowRewardedAd aufgerufen.");

    // Prüfe, ob Werbung aktiviert ist
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert (Premium-Nutzer). Überspringe Rewarded Ad.");
      onAdFailedToLoadCallback?.call();
      return;
    }

    if (_isShowingAd) {
      log.w(
        "Versuch, eine neue Anzeige zu laden/zeigen, während bereits eine angezeigt wird. Abgebrochen.",
      );
      return;
    }

    // Setze Flag sofort, um parallele Aufrufe zu verhindern
    _isShowingAd = true;

    // Zeige Ladeanimation an, wenn ein Context übergeben wurde
    if (context != null) {
      showLoadingAnimation(context);
    }

    try {
      // Verstecke die Navigationsleiste VOR dem Laden der Anzeige
      // Dies ist wichtig, damit die Anzeige im Vollbildmodus geladen wird
      await SystemUiController.hideNavigationBar();
      log.i('Navigationsleiste vor dem Laden der Anzeige versteckt');

      await RewardedAd.load(
        adUnitId: _rewardedAdUnitId,
        // Angepasste Anfrage, um sicherzustellen, dass die Anzeige korrekt angezeigt wird
        request: const AdRequest(
          // Keine nicht-personalisierte Werbung anfordern, um Größenprobleme zu vermeiden
          nonPersonalizedAds: false,
          // Hinzugefügt: Berücksichtigung der Navigationsleiste und Immersive Mode
          keywords: ['premium', 'job', 'application'],
        ),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            log.i('Rewarded ad geladen.');
            _rewardedAd = ad;

            // Setze Startzeitpunkt für intelligente Logik
            _firstAdStartTime = DateTime.now();
            log.i('Erste Werbung startet: ${_firstAdStartTime?.toIso8601String()}');

            // Entferne die Ladeanimation
            removeLoadingAnimation();

            onAdLoadedCallback?.call(); // Optionalen Callback aufrufen

            // FullScreenContentCallback setzen, NACHDEM die Ad geladen wurde
            _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
              onAdShowedFullScreenContent: (RewardedAd ad) {
                log.i('Rewarded ad im Vollbild angezeigt.');
                // Flag ist bereits gesetzt

                // Verstecke die Navigationsleiste erneut, wenn die Anzeige angezeigt wird
                // Dies ist wichtig, da die Anzeige möglicherweise die Einstellung zurücksetzt
                SystemUiController.hideNavigationBar();

                // Setze einen Timer, um die Navigationsleiste SEHR häufig zu verstecken
                // Dies ist wichtig, da die Werbeanzeige die Einstellung zurücksetzen könnte
                Timer.periodic(const Duration(milliseconds: 100), (timer) {
                  if (!_isShowingAd) {
                    // Wenn die Anzeige nicht mehr angezeigt wird, stoppe den Timer
                    timer.cancel();
                    return;
                  }

                  // Verstecke die Navigationsleiste erneut mit BEIDEN Methoden
                  SystemUiController.hideNavigationBar();

                  // Zusätzlich direkt die SystemChrome-API verwenden
                  SystemChrome.setEnabledSystemUIMode(
                    SystemUiMode.immersiveSticky,
                  );

                  log.d('Navigationsleiste aggressiv versteckt (Timer)');
                });

                log.i(
                  'Navigationsleiste erneut versteckt, wenn Anzeige im Vollbild',
                );
              },
              onAdDismissedFullScreenContent: (RewardedAd ad) async {
                log.i('Rewarded ad geschlossen (dismissed).');

                // Setze Endzeitpunkt und berechne Dauer für intelligente Logik
                _firstAdEndTime = DateTime.now();
                int firstAdDuration = 0;
                if (_firstAdStartTime != null && _firstAdEndTime != null) {
                  firstAdDuration = _firstAdEndTime!.difference(_firstAdStartTime!).inSeconds;
                  log.i('Erste Werbung dauerte $firstAdDuration Sekunden');

                  // Intelligente Logik: Wenn Werbung < 20 Sekunden, zweite Werbung vorschlagen
                  if (firstAdDuration < 20) {
                    _shouldShowSecondAd = true;
                    log.i('Erste Werbung war kurz (<20s), zweite Werbung wird empfohlen');
                  } else {
                    _shouldShowSecondAd = false;
                    log.i('Erste Werbung war lang (≥20s), keine zweite Werbung nötig');
                  }
                }

                ad.dispose();
                _rewardedAd = null;
                _isShowingAd = false;

                // WICHTIG: Stelle den normalen Modus wieder her mit dem SystemUiController
                // Warte auf die Wiederherstellung, bevor der Callback aufgerufen wird
                await SystemUiController.showNavigationBar();

                // Kurze Verzögerung, um sicherzustellen, dass die Navigationsleiste wirklich angezeigt wird
                await Future.delayed(const Duration(milliseconds: 200));

                // Stelle sicher, dass die Navigationsleiste wirklich angezeigt wird
                await SystemChrome.setEnabledSystemUIMode(
                  SystemUiMode.edgeToEdge,
                  overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                );

                // Setze einen Timer, um sicherzustellen, dass die Navigationsleiste auch nach einiger Zeit noch sichtbar ist
                Timer(const Duration(milliseconds: 1000), () {
                  SystemChrome.setEnabledSystemUIMode(
                    SystemUiMode.edgeToEdge,
                    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                  );
                  log.i(
                    'Navigationsleiste nach 1 Sekunde erneut wiederhergestellt',
                  );
                });

                log.i(
                  'Navigationsleiste nach dem Schließen der Anzeige wiederhergestellt',
                );

                onAdDismissedCallback?.call(); // NEU: Callback aufrufen
              },
              onAdFailedToShowFullScreenContent: (
                RewardedAd ad,
                AdError error,
              ) async {
                log.e('Fehler beim Anzeigen der Rewarded ad: $error');

                ad.dispose();
                _rewardedAd = null;
                _isShowingAd = false;

                // Stelle den normalen Modus wieder her mit dem SystemUiController
                await SystemUiController.showNavigationBar();
                log.i('Navigationsleiste nach Fehler wiederhergestellt');

                onAdFailedToShowCallback
                    ?.call(); // Optionalen Callback aufrufen
              },
              // Hinzugefügt: Callback für Impression
              onAdImpression: (RewardedAd ad) {
                log.i('Rewarded ad Impression registriert.');

                // Verstecke die Navigationsleiste erneut bei Impression
                // Dies ist ein zusätzlicher Sicherheitsschritt
                SystemUiController.hideNavigationBar();
              },
            );

            // Anzeige sofort zeigen
            log.d("Versuche, die geladene Anzeige anzuzeigen...");
            _rewardedAd!.show(
              onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
                log.i('Belohnung verdient: ${reward.amount} ${reward.type}');
                onUserEarnedRewardCallback(); // Notwendigen Callback aufrufen
              },
            );
          },
          onAdFailedToLoad: (LoadAdError error) async {
            log.e('Rewarded ad konnte nicht geladen werden: $error');
            _rewardedAd = null;
            _isShowingAd = false; // Sicherstellen, dass Flag zurückgesetzt wird

            // Entferne die Ladeanimation
            removeLoadingAnimation();

            // Stelle den normalen Modus wieder her mit dem SystemUiController
            await SystemUiController.showNavigationBar();
            log.i('Navigationsleiste nach Ladefehler wiederhergestellt');

            onAdFailedToLoadCallback?.call(); // Optionalen Callback aufrufen
          },
        ),
      );
    } catch (e, s) {
      // Bei Ausnahmen Flag zurücksetzen
      _isShowingAd = false;
      log.e(
        'Unerwarteter Fehler beim Laden der Rewarded ad',
        error: e,
        stackTrace: s,
      );

      // Entferne die Ladeanimation
      removeLoadingAnimation();

      // Stelle den normalen Modus wieder her mit dem SystemUiController
      await SystemUiController.showNavigationBar();
      log.i('Navigationsleiste nach Ausnahmefehler wiederhergestellt');

      onAdFailedToLoadCallback?.call(); // Fehler-Callback aufrufen
    }
  }

  // Lädt und zeigt eine zweite Rewarded Ad während der Generierung an - OHNE VERZÖGERUNG
  Future<void> loadAndShowSecondRewardedAd({
    VoidCallback? onAdLoadedCallback,
    VoidCallback? onAdFailedToLoadCallback,
    VoidCallback? onAdFailedToShowCallback,
    VoidCallback? onAdDismissedCallback,
    required VoidCallback onUserEarnedRewardCallback,
    BuildContext? context,
    bool forceShow = false, // Neuer Parameter zum Erzwingen der Anzeige
  }) async {
    log.d("loadAndShowSecondRewardedAd aufgerufen - SOFORTIGE ANZEIGE.");

    // Prüfe, ob Werbung aktiviert ist
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert (Premium-Nutzer). Überspringe zweite Rewarded Ad.");
      onAdFailedToLoadCallback?.call();
      return;
    }

    // Intelligente Logik: Prüfe, ob zweite Werbung angezeigt werden soll
    if (!forceShow && !_shouldShowSecondAd) {
      log.i("Zweite Werbung wird übersprungen - erste Werbung war lang genug (≥20s)");
      onAdFailedToLoadCallback?.call();
      return;
    }

    if (_isShowingSecondAd) {
      log.w(
        "Versuch, eine neue zweite Anzeige zu laden/zeigen, während bereits eine angezeigt wird. Abgebrochen.",
      );
      return;
    }

    // Setze Flag sofort, um parallele Aufrufe zu verhindern
    _isShowingSecondAd = true;

    // Zeige Ladeanimation an, wenn ein Context übergeben wurde
    if (context != null) {
      showLoadingAnimation(context);
    }

    try {
      // Verstecke die Navigationsleiste VOR dem Laden der Anzeige
      await SystemUiController.hideNavigationBar();
      log.i('Navigationsleiste vor dem Laden der zweiten Anzeige versteckt');

      await RewardedAd.load(
        adUnitId: _rewardedAdUnitId,
        // Angepasste Anfrage für die zweite Werbung mit anderen Keywords
        request: const AdRequest(
          // Keine nicht-personalisierte Werbung anfordern, um Größenprobleme zu vermeiden
          nonPersonalizedAds: false,
          // Andere Keywords für unterschiedliche Werbung
          keywords: ['career', 'resume', 'professional'],
        ),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            log.i('Zweite Rewarded ad geladen - wird SOFORT angezeigt.');
            _secondRewardedAd = ad;

            // Entferne die Ladeanimation
            removeLoadingAnimation();

            onAdLoadedCallback?.call(); // Optionalen Callback aufrufen

            // FullScreenContentCallback setzen, NACHDEM die Ad geladen wurde
            _secondRewardedAd!
                .fullScreenContentCallback = FullScreenContentCallback(
              onAdShowedFullScreenContent: (RewardedAd ad) {
                log.i('Zweite Rewarded ad im Vollbild angezeigt.');
                // Flag ist bereits gesetzt

                // Verstecke die Navigationsleiste erneut, wenn die Anzeige angezeigt wird
                // Dies ist wichtig, da die Anzeige möglicherweise die Einstellung zurücksetzt
                SystemUiController.hideNavigationBar();

                // Setze einen Timer, um die Navigationsleiste regelmäßig zu verstecken
                // Dies ist wichtig, da die Werbeanzeige die Einstellung zurücksetzen könnte
                Timer.periodic(const Duration(milliseconds: 500), (timer) {
                  if (!_isShowingSecondAd) {
                    // Wenn die Anzeige nicht mehr angezeigt wird, stoppe den Timer
                    timer.cancel();
                    return;
                  }

                  // Verstecke die Navigationsleiste erneut
                  SystemUiController.hideNavigationBar();
                  log.d(
                    'Navigationsleiste periodisch versteckt für zweite Anzeige (Timer)',
                  );
                });

                log.i(
                  'Navigationsleiste erneut versteckt für zweite Anzeige im Vollbild',
                );
              },
              onAdDismissedFullScreenContent: (RewardedAd ad) async {
                log.i('Zweite Rewarded ad geschlossen (dismissed).');

                ad.dispose();
                _secondRewardedAd = null;
                _isShowingSecondAd = false;

                // Setze das Flag für die zweite Werbung zurück
                resetSecondAdFlag();

                // WICHTIG: Stelle den normalen Modus wieder her mit dem SystemUiController
                // Warte auf die Wiederherstellung, bevor der Callback aufgerufen wird
                await SystemUiController.showNavigationBar();

                // Kurze Verzögerung, um sicherzustellen, dass die Navigationsleiste wirklich angezeigt wird
                await Future.delayed(const Duration(milliseconds: 200));

                // Stelle sicher, dass die Navigationsleiste wirklich angezeigt wird
                await SystemChrome.setEnabledSystemUIMode(
                  SystemUiMode.edgeToEdge,
                  overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                );

                // Setze einen Timer, um sicherzustellen, dass die Navigationsleiste auch nach einiger Zeit noch sichtbar ist
                Timer(const Duration(milliseconds: 1000), () {
                  SystemChrome.setEnabledSystemUIMode(
                    SystemUiMode.edgeToEdge,
                    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                  );
                  log.i(
                    'Navigationsleiste nach 1 Sekunde erneut wiederhergestellt (zweite Anzeige)',
                  );
                });

                log.i(
                  'Navigationsleiste nach dem Schließen der zweiten Anzeige wiederhergestellt',
                );

                onAdDismissedCallback?.call(); // Callback aufrufen
              },
              onAdFailedToShowFullScreenContent: (
                RewardedAd ad,
                AdError error,
              ) async {
                log.e('Fehler beim Anzeigen der zweiten Rewarded ad: $error');

                ad.dispose();
                _secondRewardedAd = null;
                _isShowingSecondAd = false;

                // Stelle den normalen Modus wieder her mit dem SystemUiController
                await SystemUiController.showNavigationBar();
                log.i(
                  'Navigationsleiste nach Fehler bei zweiter Anzeige wiederhergestellt',
                );

                onAdFailedToShowCallback
                    ?.call(); // Optionalen Callback aufrufen
              },
              // Hinzugefügt: Callback für Impression
              onAdImpression: (RewardedAd ad) {
                log.i('Zweite Rewarded ad Impression registriert.');

                // Verstecke die Navigationsleiste erneut bei Impression
                // Dies ist ein zusätzlicher Sicherheitsschritt
                SystemUiController.hideNavigationBar();
              },
            );

            // Anzeige SOFORT zeigen ohne Verzögerung mit microtask
            log.d("Zeige die geladene zweite Anzeige SOFORT an...");
            // Verwende microtask für garantiert sofortige Ausführung
            Future.microtask(() {
              if (_secondRewardedAd != null) {
                log.i(
                  "Zeige zweite Werbung in Mikrotask - GARANTIERT KEINE VERZÖGERUNG",
                );
                _secondRewardedAd!.show(
                  onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
                    log.i(
                      'Belohnung für zweite Anzeige verdient: ${reward.amount} ${reward.type}',
                    );
                    onUserEarnedRewardCallback(); // Notwendigen Callback aufrufen
                  },
                );
              }
            });
          },
          onAdFailedToLoad: (LoadAdError error) async {
            log.e('Zweite Rewarded ad konnte nicht geladen werden: $error');
            _secondRewardedAd = null;
            _isShowingSecondAd = false; // Flag zurücksetzen

            // Entferne die Ladeanimation
            removeLoadingAnimation();

            // Stelle den normalen Modus wieder her mit dem SystemUiController
            await SystemUiController.showNavigationBar();
            log.i(
              'Navigationsleiste nach Ladefehler bei zweiter Anzeige wiederhergestellt',
            );

            onAdFailedToLoadCallback?.call(); // Optionalen Callback aufrufen
          },
        ),
      );
    } catch (e, s) {
      // Bei Ausnahmen Flag zurücksetzen
      _isShowingSecondAd = false;
      log.e(
        'Unerwarteter Fehler beim Laden der zweiten Rewarded ad',
        error: e,
        stackTrace: s,
      );

      // Entferne die Ladeanimation
      removeLoadingAnimation();

      // Stelle den normalen Modus wieder her mit dem SystemUiController
      await SystemUiController.showNavigationBar();
      log.i(
        'Navigationsleiste nach Ausnahmefehler bei zweiter Anzeige wiederhergestellt',
      );

      onAdFailedToLoadCallback?.call(); // Fehler-Callback aufrufen
    }
  }

  // Lädt und zeigt maximal zwei verschiedene Werbungen nacheinander an
  // Intelligente Logik: Wenn erste Werbung ≥20s, keine zweite Werbung
  // Wenn erste Werbung <20s, dann zweite Werbung anzeigen
  Future<void> loadAndShowConsecutiveAds({
    VoidCallback? onFirstAdLoadedCallback,
    VoidCallback? onFirstAdFailedToLoadCallback,
    VoidCallback? onFirstAdFailedToShowCallback,
    VoidCallback? onFirstAdDismissedCallback,
    required VoidCallback onFirstUserEarnedRewardCallback,
    VoidCallback? onSecondAdLoadedCallback,
    VoidCallback? onSecondAdFailedToLoadCallback,
    VoidCallback? onSecondAdFailedToShowCallback,
    VoidCallback? onSecondAdDismissedCallback,
    required VoidCallback onSecondUserEarnedRewardCallback,
    BuildContext? context,
  }) async {
    log.d(
      "loadAndShowConsecutiveAds aufgerufen - Zeige maximal zwei Werbungen mit Gesamtdauer von ca. 40 Sekunden.",
    );

    // Prüfe, ob Werbung aktiviert ist
    if (!_adsEnabled) {
      log.i("Werbung ist deaktiviert (Premium-Nutzer). Überspringe consecutive Ads.");
      onFirstAdFailedToLoadCallback?.call();
      onSecondAdFailedToLoadCallback?.call();
      return;
    }

    // Zeige Ladeanimation an, wenn ein Context übergeben wurde
    if (context != null) {
      showLoadingAnimation(context);
    }

    // Zeitstempel für den Start der ersten Werbung
    DateTime? firstAdStartTime;
    DateTime? firstAdEndTime;

    // Lade die zweite Werbung im Voraus, damit sie sofort bereit ist
    RewardedAd? preloadedSecondAd;
    bool secondAdLoaded = false;

    // Komplette Vorbereitung der zweiten Werbung, bevor die erste überhaupt angezeigt wird
    try {
      // Warte auf das Laden der zweiten Werbung, bevor die erste angezeigt wird
      Completer<void> secondAdLoadCompleter = Completer<void>();

      // Starte das Laden der zweiten Werbung im Hintergrund
      RewardedAd.load(
        adUnitId: _rewardedAdUnitId,
        request: const AdRequest(
          nonPersonalizedAds: false,
          keywords: ['career', 'resume', 'professional'],
        ),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            log.i(
              'Zweite Rewarded ad VOLLSTÄNDIG im Voraus geladen - bereit für sofortige Anzeige.',
            );
            preloadedSecondAd = ad;
            secondAdLoaded = true;
            onSecondAdLoadedCallback?.call();

            // Setze FullScreenContentCallback für die vorgeladene Anzeige
            preloadedSecondAd!
                .fullScreenContentCallback = FullScreenContentCallback(
              onAdShowedFullScreenContent: (RewardedAd ad) {
                log.i('Zweite Rewarded ad im Vollbild angezeigt.');

                // Verstecke die Navigationsleiste erneut, wenn die Anzeige angezeigt wird
                // Dies ist wichtig, da die Anzeige möglicherweise die Einstellung zurücksetzt
                SystemUiController.hideNavigationBar();

                // Setze einen Timer, um die Navigationsleiste regelmäßig zu verstecken
                // Dies ist wichtig, da die Werbeanzeige die Einstellung zurücksetzen könnte
                Timer.periodic(const Duration(milliseconds: 500), (timer) {
                  if (!_isShowingSecondAd) {
                    // Wenn die Anzeige nicht mehr angezeigt wird, stoppe den Timer
                    timer.cancel();
                    return;
                  }

                  // Verstecke die Navigationsleiste erneut
                  SystemUiController.hideNavigationBar();
                  log.d(
                    'Navigationsleiste periodisch versteckt für vorgeladene Anzeige (Timer)',
                  );
                });

                log.i(
                  'Navigationsleiste erneut versteckt für zweite Anzeige im Vollbild (consecutive)',
                );
              },
              onAdDismissedFullScreenContent: (RewardedAd ad) async {
                log.i('Zweite Rewarded ad geschlossen (dismissed).');

                ad.dispose();
                _secondRewardedAd = null;
                _isShowingSecondAd = false;

                // WICHTIG: Stelle den normalen Modus wieder her mit dem SystemUiController
                // Warte auf die Wiederherstellung, bevor der Callback aufgerufen wird
                await SystemUiController.showNavigationBar();

                // Kurze Verzögerung, um sicherzustellen, dass die Navigationsleiste wirklich angezeigt wird
                await Future.delayed(const Duration(milliseconds: 200));

                // Stelle sicher, dass die Navigationsleiste wirklich angezeigt wird
                await SystemChrome.setEnabledSystemUIMode(
                  SystemUiMode.edgeToEdge,
                  overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                );

                // Setze einen Timer, um sicherzustellen, dass die Navigationsleiste auch nach einiger Zeit noch sichtbar ist
                Timer(const Duration(milliseconds: 1000), () {
                  SystemChrome.setEnabledSystemUIMode(
                    SystemUiMode.edgeToEdge,
                    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
                  );
                  log.i(
                    'Navigationsleiste nach 1 Sekunde erneut wiederhergestellt (consecutive)',
                  );
                });

                log.i(
                  'Navigationsleiste nach dem Schließen der zweiten Anzeige wiederhergestellt (consecutive)',
                );

                onSecondAdDismissedCallback?.call();
              },
              onAdFailedToShowFullScreenContent: (
                RewardedAd ad,
                AdError error,
              ) async {
                log.e('Fehler beim Anzeigen der zweiten Rewarded ad: $error');

                ad.dispose();
                _secondRewardedAd = null;
                _isShowingSecondAd = false;

                // Stelle den normalen Modus wieder her mit dem SystemUiController
                await SystemUiController.showNavigationBar();
                log.i(
                  'Navigationsleiste nach Fehler bei zweiter Anzeige wiederhergestellt (consecutive)',
                );

                onSecondAdFailedToShowCallback?.call();
              },
              onAdImpression: (RewardedAd ad) {
                log.i('Zweite Rewarded ad Impression registriert.');

                // Verstecke die Navigationsleiste erneut bei Impression
                // Dies ist ein zusätzlicher Sicherheitsschritt
                SystemUiController.hideNavigationBar();
              },
            );

            // Markiere das Laden als abgeschlossen
            secondAdLoadCompleter.complete();
          },
          onAdFailedToLoad: (LoadAdError error) {
            log.e(
              'Zweite Rewarded ad konnte nicht im Voraus geladen werden: $error',
            );
            onSecondAdFailedToLoadCallback?.call();

            // Auch bei Fehler als abgeschlossen markieren
            secondAdLoadCompleter.complete();
          },
        ),
      );

      // Warte maximal 5 Sekunden auf das Laden der zweiten Werbung
      // Erhöhte Timeout-Zeit, um sicherzustellen, dass die zweite Werbung geladen wird
      await secondAdLoadCompleter.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          log.w('Timeout beim Laden der zweiten Werbung - fahre trotzdem fort');
        },
      );
    } catch (e) {
      log.e('Fehler beim Vorladen der zweiten Werbung: $e');
      // Fahre trotzdem fort, auch wenn das Vorladen fehlschlägt
    }

    // Erste Werbung laden und anzeigen
    await loadAndShowRewardedAd(
      onAdLoadedCallback: () {
        // Zeitstempel für den Start der ersten Werbung setzen
        firstAdStartTime = DateTime.now();
        log.i(
          'Erste Rewarded Ad geladen und wird angezeigt. Start: ${firstAdStartTime?.toIso8601String()}',
        );

        // Entferne die Ladeanimation (falls sie noch angezeigt wird)
        removeLoadingAnimation();

        onFirstAdLoadedCallback?.call();
      },
      onAdFailedToLoadCallback: () {
        // Entferne die Ladeanimation
        removeLoadingAnimation();

        onFirstAdFailedToLoadCallback?.call();
      },
      onAdFailedToShowCallback: onFirstAdFailedToShowCallback,
      onAdDismissedCallback: () {
        // Zeitstempel für das Ende der ersten Werbung setzen
        firstAdEndTime = DateTime.now();
        log.i(
          'Erste Rewarded Ad geschlossen. Ende: ${firstAdEndTime?.toIso8601String()}',
        );

        // Berechne die Dauer der ersten Werbung
        int firstAdDuration = 0;
        if (firstAdStartTime != null && firstAdEndTime != null) {
          firstAdDuration =
              firstAdEndTime!.difference(firstAdStartTime!).inSeconds;
          log.i('Erste Werbung dauerte $firstAdDuration Sekunden');
        }

        // Wenn die erste Werbung geschlossen wird, rufe den Callback auf
        onFirstAdDismissedCallback?.call();

        // Zeige die zweite Werbung nur an, wenn die erste Werbung weniger als 20 Sekunden gedauert hat
        // und die Gesamtdauer von 40 Sekunden noch nicht erreicht ist
        if (firstAdDuration < 20 &&
            secondAdLoaded &&
            preloadedSecondAd != null) {
          log.i(
            "Erste Werbung dauerte weniger als 20 Sekunden, zeige zweite Werbung an",
          );
          _secondRewardedAd = preloadedSecondAd;
          _isShowingSecondAd = true;

          // Anzeige SOFORT zeigen ohne jegliche Verzögerung
          // Verwende direkte Ausführung ohne Verzögerung mit microtask
          // Dies stellt sicher, dass die Anzeige in der nächsten Mikrotask-Queue angezeigt wird,
          // was praktisch sofort ist, aber nach dem Schließen der ersten Werbung
          Future.microtask(() {
            if (_secondRewardedAd != null) {
              log.i(
                "Zeige zweite Werbung in Mikrotask - GARANTIERT KEINE VERZÖGERUNG",
              );
              _secondRewardedAd!.show(
                onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
                  log.i(
                    'Belohnung für zweite Anzeige verdient: ${reward.amount} ${reward.type}',
                  );
                  onSecondUserEarnedRewardCallback();
                },
              );
            }
          });
        } else {
          // Wenn die erste Werbung länger als 20 Sekunden gedauert hat, zeige keine zweite Werbung an
          if (firstAdDuration >= 20) {
            log.i(
              "Erste Werbung dauerte $firstAdDuration Sekunden (≥20s), keine zweite Werbung nötig",
            );

            // Wenn die zweite Werbung bereits geladen wurde, entsorge sie
            if (preloadedSecondAd != null) {
              preloadedSecondAd!.dispose();
              log.i("Vorgeladene zweite Werbung entsorgt, da nicht benötigt");
            }
          } else if (!secondAdLoaded) {
            // Fallback: Wenn die zweite Werbung noch nicht geladen ist, lade sie jetzt
            log.i("Zweite Werbung noch nicht vorgeladen, lade sie jetzt");
            loadAndShowSecondRewardedAd(
              onAdLoadedCallback: onSecondAdLoadedCallback,
              onAdFailedToLoadCallback: onSecondAdFailedToLoadCallback,
              onAdFailedToShowCallback: onSecondAdFailedToShowCallback,
              onAdDismissedCallback: onSecondAdDismissedCallback,
              onUserEarnedRewardCallback: onSecondUserEarnedRewardCallback,
            );
          }
        }
      },
      onUserEarnedRewardCallback: onFirstUserEarnedRewardCallback,
    );
  }

  // Gibt Ressourcen frei, wenn der Service nicht mehr benötigt wird.
  void dispose() {
    // Nichts mehr zu disposen hier, da _rewardedAd entfernt wurde.
    // Das Ad-Objekt wird nach dem Anzeigen oder bei Fehlern direkt disposed.
  }
}

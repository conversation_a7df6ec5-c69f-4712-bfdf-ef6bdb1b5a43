import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../src/application/providers/user_profile_provider.dart';
import '../src/domain/models/subscription.dart';

/// Provider für den SubscriptionService
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  final service = SubscriptionService(ref, supabaseClient);
  ref.onDispose(() {
    service.dispose();
  });
  return service;
});

/// Provider für den Supabase-Client
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

/// Service zur Verwaltung von In-App-Käufen und Abonnements
class SubscriptionService {
  final InAppPurchase _iap;
  final SupabaseClient _supabase;
  final Ref _ref;
  final Logger _log = Logger('SubscriptionService');
  
  // Produkt-IDs für In-App-Käufe
  final String _premiumMonthlyId = Platform.isAndroid 
      ? 'premium_monthly_subscription'
      : 'premium_monthly_subscription';
  
  final String _premiumYearlyId = Platform.isAndroid 
      ? 'premium_yearly_subscription'
      : 'premium_yearly_subscription';
  
  final bool _debugMode = kDebugMode;
  
  // Status und Streams
  bool _isAvailable = false;
  bool _isPurchasePending = false;
  List<ProductDetails> _products = [];
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  
  // Getter für öffentliche Eigenschaften
  bool get isAvailable => _isAvailable;
  bool get isPurchasePending => _isPurchasePending;
  List<ProductDetails> get products => _products;
  
  /// Konstruktor mit Initialisierung
  SubscriptionService(this._iap, this._supabase, this._ref) {
    _initialize();
  }
  
  /// Initialisiert den Service
  Future<void> _initialize() async {
    _log.info('Initialisiere SubscriptionService');
    
    // Prüfen, ob In-App-Käufe verfügbar sind
    _isAvailable = await _iap.isAvailable();
    
    if (!_isAvailable) {
      _log.warning('In-App-Käufe sind nicht verfügbar');
      return;
    }
    
    // Purchase-Updates abonnieren
    _subscription = _iap.purchaseStream.listen(
      _handlePurchaseUpdate,
      onDone: () {
        _subscription?.cancel();
      },
      onError: (error) {
        _log.severe('Fehler im Kauf-Stream: $error');
      }
    );
    
    // Produkte laden
    await _loadProducts();
    
    // Bestehende Käufe wiederherstellen
    if (Platform.isIOS) {
      await _restorePurchases();
    }
    
    // In Debug-Modus: Status abrufen
    if (_debugMode) {
      await checkSubscriptionStatus();
    }
  }
  
  /// Lädt die verfügbaren Produkte
  Future<void> _loadProducts() async {
    try {
      final productIds = <String>{_premiumMonthlyId, _premiumYearlyId};
      
      final response = await _iap.queryProductDetails(productIds);
      
      if (response.error != null) {
        _log.severe('Fehler beim Laden der Produkte: ${response.error}');
        return;
      }
      
      _products = response.productDetails;
      
      if (_products.isEmpty) {
        _log.warning('Keine Produkte gefunden. Produktids: $productIds');
      } else {
        _log.info('${_products.length} Produkte geladen');
        for (final product in _products) {
          _log.info('Produkt: ${product.id} - ${product.title} - ${product.price}');
        }
      }
    } catch (e) {
      _log.severe('Fehler beim Laden der Produkte: $e');
    }
  }
  
  /// Kauft ein Premium-Abonnement
  Future<bool> purchasePremiumSubscription({bool monthly = true}) async {
    if (_debugMode) {
      return await _simulateSuccessfulPurchase(
        productId: monthly ? _premiumMonthlyId : _premiumYearlyId,
        isMonthly: monthly,
      );
    }
    
    if (!_isAvailable) {
      _log.severe('In-App-Käufe nicht verfügbar');
      return false;
    }
    
    if (_isPurchasePending) {
      _log.warning('Es läuft bereits ein Kaufvorgang');
      return false;
    }
    
    _isPurchasePending = true;
    
    try {
      final productId = monthly ? _premiumMonthlyId : _premiumYearlyId;
      final product = _findProductById(productId);
      
      if (product == null) {
        _log.severe('Produkt $productId nicht gefunden');
        _isPurchasePending = false;
        return false;
      }
      
      _log.info('Starte Kauf von ${product.id}');
      
      // Kauf starten
      final purchaseParam = PurchaseParam(
        productDetails: product,
        applicationUserName: null,
      );
      
      // Abonnement kaufen
      if (!await _iap.buyNonConsumable(purchaseParam: purchaseParam)) {
        _log.severe('Kauf konnte nicht gestartet werden');
        _isPurchasePending = false;
        return false;
      }
      
      _log.info('Kauf gestartet, warte auf Bestätigung...');
      return true;
      
    } catch (e) {
      _log.severe('Fehler beim Kauf: $e');
      _isPurchasePending = false;
      return false;
    }
  }

  /// Simuliert einen erfolgreichen Kauf (nur im Debug-Modus)
  Future<bool> _simulateSuccessfulPurchase({
    required String productId, 
    required bool isMonthly
  }) async {
    _log.info('DEBUG: Simuliere erfolgreichen Kauf von $productId');
    
    try {
      // Sicherstellen, dass der Benutzer angemeldet ist
      final user = _supabase.auth.currentUser;
      if (user == null) {
        _log.severe('DEBUG: Fehler beim Simulieren des Kaufs - Benutzer nicht angemeldet');
        return false;
      }
      
      // Ablaufdatum berechnen (1 Monat oder 1 Jahr ab jetzt)
      final now = DateTime.now();
      final expiryDate = isMonthly 
          ? now.add(const Duration(days: 30)) 
          : now.add(const Duration(days: 365));
      
      // Daten für Supabase vorbereiten
      final subscriptionData = {
        'user_id': user.id,
        'status': 'active',
        'subscription_id': 'sim_${DateTime.now().millisecondsSinceEpoch}',
        'product_id': productId,
        'platform': Platform.isAndroid ? 'android' : 'ios',
        'purchased_at': now.toIso8601String(),
        'expires_at': expiryDate.toIso8601String(),
        'metadata': {
          'simulated': true,
          'debug_purchase': true
        }
      };
      
      // In Supabase speichern
      final response = await _supabase
          .from('subscriptions')
          .insert(subscriptionData)
          .select('id')
          .single();
      
      _log.info('DEBUG: Simulierter Kauf in Supabase gespeichert: ${response['id']}');
      
      // Lokales UserProfile aktualisieren
      await _updateUserProfileSubscriptionStatus(
        isPremium: true,
        expiryDate: expiryDate,
      );
      
      _log.info('DEBUG: UserProfile aktualisiert, Premium ist jetzt aktiv bis $expiryDate');
      return true;
      
    } catch (e) {
      _log.severe('DEBUG: Fehler beim Simulieren des Kaufs: $e');
      return false;
    }
  }
  
  /// Wiederherstellt Käufe (besonders für iOS wichtig)
  Future<void> _restorePurchases() async {
    _log.info('Stelle Käufe wieder her');
    
    try {
      await _iap.restorePurchases();
    } catch (e) {
      _log.severe('Fehler beim Wiederherstellen der Käufe: $e');
    }
  }
  
  /// Verarbeitet Kauf-Updates
  Future<void> _handlePurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) async {
    _log.info('Kauf-Updates empfangen: ${purchaseDetailsList.length}');
    
    for (final purchaseDetails in purchaseDetailsList) {
      await _processPurchase(purchaseDetails);
    }
  }
  
  /// Verarbeitet einen einzelnen Kauf
  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    _log.info('Verarbeite Kauf: ${purchaseDetails.productID} (Status: ${purchaseDetails.status})');
    
    if (purchaseDetails.status == PurchaseStatus.pending) {
      _log.info('Kauf ist noch in Bearbeitung');
      _isPurchasePending = true;
    } else {
      _isPurchasePending = false;
      
      if (purchaseDetails.status == PurchaseStatus.error) {
        _log.severe('Kaufvorgang fehlgeschlagen: ${purchaseDetails.error?.message}');
      } else if (purchaseDetails.status == PurchaseStatus.purchased || 
                purchaseDetails.status == PurchaseStatus.restored) {
        await _verifyAndDeliverPurchase(purchaseDetails);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        _log.info('Kauf wurde abgebrochen');
      }
      
      // Abschluss bestätigen (wichtig für iOS)
      if (purchaseDetails.pendingCompletePurchase) {
        await _iap.completePurchase(purchaseDetails);
      }
    }
  }
  
  /// Verifiziert und liefert einen Kauf aus
  Future<void> _verifyAndDeliverPurchase(PurchaseDetails purchaseDetails) async {
    final productId = purchaseDetails.productID;
    _log.info('Verifiziere und liefere Kauf aus: $productId');
    
    try {
      // Hier könnte eine Server-seitige Verifizierung erfolgen
      // Für jetzt gehen wir davon aus, dass der Kauf gültig ist
      
      final user = _supabase.auth.currentUser;
      if (user == null) {
        _log.severe('Kauf kann nicht geliefert werden - Benutzer nicht angemeldet');
        return;
      }
      
      // Ablaufdatum berechnen basierend auf Produkt-ID
      final now = DateTime.now();
      final bool isMonthly = productId == _premiumMonthlyId;
      final expiryDate = isMonthly 
          ? now.add(const Duration(days: 30)) 
          : now.add(const Duration(days: 365));
      
      // Daten für Supabase vorbereiten
      final subscriptionData = {
        'user_id': user.id,
        'status': 'active',
        'subscription_id': purchaseDetails.purchaseID ?? 'unknown_${DateTime.now().millisecondsSinceEpoch}',
        'product_id': productId,
        'platform': Platform.isAndroid ? 'android' : 'ios',
        'purchased_at': now.toIso8601String(),
        'expires_at': expiryDate.toIso8601String(),
        'receipt_data': purchaseDetails.verificationData.serverVerificationData,
        'metadata': {
          'local_verification': true,
          'purchase_verified': true
        }
      };
      
      // In Supabase speichern
      // Existierende aktive Abonnements deaktivieren
      // Transaktion sollte idealerweise auf Serverseite passieren
      
      await _supabase.from('subscriptions').insert(subscriptionData);
      
      _log.info('Kaufdaten in Supabase gespeichert');
      
      // Lokales UserProfile aktualisieren
      await _updateUserProfileSubscriptionStatus(
        isPremium: true,
        expiryDate: expiryDate,
      );
      
      _log.info('UserProfile aktualisiert, Premium ist jetzt aktiv bis $expiryDate');
      
    } catch (e) {
      _log.severe('Fehler beim Verifizieren/Liefern des Kaufs: $e');
    }
  }
  
  /// Aktualisiert den Abonnementstatus im UserProfile
  Future<void> _updateUserProfileSubscriptionStatus({
    required bool isPremium,
    required DateTime expiryDate,
  }) async {
    try {
      await _ref.read(userProfileProvider.notifier).updatePremiumStatus(
        isPremium: isPremium,
        subscriptionEndDate: expiryDate,
      );
      _log.info('Premium-Status im UserProfile aktualisiert: isPremium=$isPremium, expiryDate=$expiryDate');
    } catch (e) {
      _log.severe('Fehler beim Aktualisieren des Premium-Status: $e');
    }
  }
  
  /// Prüft und aktualisiert den Abonnementstatus eines Benutzers
  Future<SubscriptionStatus> checkSubscriptionStatus() async {
    _log.info('Prüfe Abonnementstatus');
    
    final user = _supabase.auth.currentUser;
    if (user == null) {
      _log.warning('Kann Abonnementstatus nicht prüfen - Benutzer nicht angemeldet');
      return SubscriptionStatus(isActive: false);
    }
    
    try {
      // Aktives Abonnement aus Supabase abrufen
      final response = await _supabase
          .from('subscriptions')
          .select()
          .eq('user_id', user.id)
          .eq('status', 'active')
          .order('expires_at', ascending: false)
          .limit(1)
          .maybeSingle();
      
      if (response == null) {
        _log.info('Kein aktives Abonnement gefunden');
        
        // UserProfile aktualisieren, falls es fälschlicherweise aktiv ist
        await _updateUserProfileSubscriptionStatus(
          isPremium: false,
          expiryDate: DateTime.now(),
        );
        
        return SubscriptionStatus(isActive: false);
      }
      
      // Subscription-Objekt aus der Antwort erstellen
      final subscription = Subscription.fromJson(response);
      
      // Prüfen, ob das Abonnement abgelaufen ist
      final isExpired = subscription.expiresAt?.isBefore(DateTime.now()) ?? true;
      
      if (isExpired) {
        _log.info('Abonnement ist abgelaufen');
        
        // Abonnement als abgelaufen markieren
        await _supabase
            .from('subscriptions')
            .update({'status': 'expired'})
            .eq('id', subscription.id);
        
        // UserProfile aktualisieren
        await _updateUserProfileSubscriptionStatus(
          isPremium: false,
          expiryDate: DateTime.now(),
        );
        
        return SubscriptionStatus(isActive: false);
      }
      
      _log.info('Aktives Abonnement gefunden: gültig bis ${subscription.expiresAt}');
      
      // UserProfile aktualisieren, um sicherzustellen, dass es synchron ist
      await _updateUserProfileSubscriptionStatus(
        isPremium: true,
        expiryDate: subscription.expiresAt ?? DateTime.now().add(const Duration(days: 30)),
      );
      
      return SubscriptionStatus(
        isActive: true,
        expiresAt: subscription.expiresAt,
        subscription: subscription,
      );
      
    } catch (e) {
      _log.severe('Fehler beim Prüfen des Abonnementstatus: $e');
      return SubscriptionStatus(isActive: false, error: e.toString());
    }
  }
  
  /// Sucht ein Produkt anhand der ID
  ProductDetails? _findProductById(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }
  
  /// Entfernt alle Ressourcen
  void dispose() {
    _log.info('Beende SubscriptionService');
    _subscription?.cancel();
  }
}

/// Status-Objekt für ein Abonnement 
class SubscriptionStatus {
  final bool isActive;
  final DateTime? expiresAt;
  final Subscription? subscription;
  final String? error;
  
  SubscriptionStatus({
    required this.isActive,
    this.expiresAt,
    this.subscription,
    this.error,
  });
  
  @override
  String toString() {
    return 'SubscriptionStatus(isActive: $isActive, expiresAt: $expiresAt, error: $error)';
  }
} 
 
 
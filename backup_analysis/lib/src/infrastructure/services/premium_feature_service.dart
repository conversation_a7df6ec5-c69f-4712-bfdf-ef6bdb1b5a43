import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/infrastructure/services/subscription_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';

/// Definiert die verfügbaren Premium-Features in der App
enum PremiumFeature {
  /// KI-Anschreiben-Generator
  aiCoverLetter,

  /// KI-optimierte Jobsuche
  aiJobSearch,

  /// Unbegrenztes Speichern von Favoriten
  unlimitedFavorites,

  /// Werbungsfreie Erfahrung
  adFree,

  /// Zugriff auf Premium-Insights und Statistiken
  insights,
}

/// Service zur Verwaltung von Premium-Features
class PremiumFeatureService {
  final SubscriptionService _subscriptionService;
  final Ref _ref;
  final _log = getLogger('PremiumFeatureService');

  // Cache für Feature-Berechtigungen
  final Map<PremiumFeature, bool> _featureAccessCache = {};
  DateTime _cacheExpiry = DateTime.now();

  // Maximale Anzahl von Favoriten für nicht-Premium-Nutzer
  static const int maxFavoritesForNonPremium = 10;

  // Maximale Anzahl von Bewerbungen pro Monat für Basic-Nutzer
  static const int maxApplicationsForBasic = 30;

  // Maximale Anzahl von Bewerbungen pro Monat für Pro-Nutzer
  static const int maxApplicationsForPro = 150;

  // Schlüssel für Timestamp zur Anzeige von Premium-Prompts
  static const String _lastPromptShownKey = 'last_premium_prompt_shown';

  // Mindestzeitintervall zwischen Premium-Prompts (1 Tag)
  static const Duration _minimumPromptInterval = Duration(days: 1);

  PremiumFeatureService(this._subscriptionService, this._ref) {
    _log.i('PremiumFeatureService initialisiert');
    _resetCache();
  }

  /// Setzt den Feature-Cache zurück
  void _resetCache() {
    _featureAccessCache.clear();
    _cacheExpiry = DateTime.now();
  }

  /// Überprüft, ob der Cache noch gültig ist
  bool _isCacheValid() {
    return _cacheExpiry.isAfter(DateTime.now());
  }

  /// Überprüft, ob ein Benutzer Zugriff auf ein bestimmtes Premium-Feature hat
  Future<bool> hasAccessTo(PremiumFeature feature) async {
    // Cache verwenden, wenn gültig
    if (_isCacheValid() && _featureAccessCache.containsKey(feature)) {
      return _featureAccessCache[feature]!;
    }

    try {
      // Prüft, ob der Benutzer generell Premium-Zugriff hat
      final hasPremium = await _subscriptionService.hasPremiumAccess();

      bool hasAccess = false;

      // Hole den aktuellen Abonnementplan des Benutzers
      final subscription = await _subscriptionService.getCurrentSubscription();
      final planType = subscription?.planType ?? 'basic';

      if (hasPremium) {
        // Premium-Nutzer haben Zugriff auf Features basierend auf ihrem Plan
        switch (feature) {
          case PremiumFeature.unlimitedFavorites:
            // Nur Unlimited-Plan hat unbegrenzte Favoriten
            hasAccess = planType == 'unlimited';
            break;
          case PremiumFeature.adFree:
            // Pro und Unlimited haben keine Werbung
            hasAccess = planType == 'pro' || planType == 'unlimited';
            break;
          case PremiumFeature.aiCoverLetter:
            // Alle Premium-Pläne haben Zugriff auf KI-Anschreiben
            hasAccess = true;
            break;
          case PremiumFeature.aiJobSearch:
            // Alle Premium-Pläne haben Zugriff auf KI-Jobsuche
            hasAccess = true;
            break;
          case PremiumFeature.insights:
            // Nur Pro und Unlimited haben Zugriff auf Insights
            hasAccess = planType == 'pro' || planType == 'unlimited';
            break;
        }
      } else {
        // Für Nicht-Premium-Nutzer: Prüfe feature-spezifische Regeln
        switch (feature) {
          case PremiumFeature.unlimitedFavorites:
            // Nicht-Premium-Nutzer haben keinen Zugriff auf unbegrenzte Favoriten
            hasAccess = false;
            break;
          case PremiumFeature.adFree:
            // Nicht-Premium-Nutzer haben keinen Zugriff auf werbefreie Nutzung
            hasAccess = false;
            break;
          case PremiumFeature.aiCoverLetter:
            // Prüfe, ob der Benutzer temporären Zugriff auf den KI-Anschreiben-Generator hat
            final currentUser = await _subscriptionService.getCurrentUser();
            hasAccess = await _subscriptionService.hasValidAdAccessForUser(
              currentUser?.id ?? '',
              specificFeature: feature.name,
            );
            break;
          case PremiumFeature.aiJobSearch:
            // Prüfe, ob der Benutzer temporären Zugriff auf die KI-Jobsuche hat
            final currentUser = await _subscriptionService.getCurrentUser();
            hasAccess = await _subscriptionService.hasValidAdAccessForUser(
              currentUser?.id ?? '',
              specificFeature: feature.name,
            );
            break;
          case PremiumFeature.insights:
            // Premium-Insights sind nur für Premium-Benutzer verfügbar
            hasAccess = false;
            break;
        }
      }

      // Ergebnis im Cache speichern (1 Stunde gültig)
      _featureAccessCache[feature] = hasAccess;
      _cacheExpiry = DateTime.now().add(const Duration(hours: 1));

      return hasAccess;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler bei der Überprüfung des Feature-Zugriffs',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Überprüft, ob ein Benutzer die maximale Anzahl von Favoriten erreicht hat
  Future<bool> hasReachedFavoritesLimit(int currentFavoritesCount) async {
    if (await hasAccessTo(PremiumFeature.unlimitedFavorites)) {
      return false; // Premium-Nutzer haben keine Begrenzung
    }

    return currentFavoritesCount >= maxFavoritesForNonPremium;
  }

  /// Zeigt einen Premium-Upgrade-Dialog an
  Future<bool> showPremiumUpgradeDialog(
    BuildContext context,
    PremiumFeature feature,
  ) async {
    // Prüfen, ob kürzlich bereits ein Prompt angezeigt wurde
    final prefs = await SharedPreferences.getInstance();
    final lastPromptTime = prefs.getInt(_lastPromptShownKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    if (now - lastPromptTime < _minimumPromptInterval.inMilliseconds) {
      _log.d('Premium-Prompt wurde kürzlich angezeigt, überspringe');
      return false;
    }

    // Featuretext für den Dialog
    String featureText;
    String benefitText;

    switch (feature) {
      case PremiumFeature.aiCoverLetter:
        featureText = 'KI-Anschreiben-Generator';
        benefitText =
            'Lass dir automatisch personalisierte Anschreiben erstellen, die perfekt auf den Job und dein Profil abgestimmt sind.';
        break;
      case PremiumFeature.aiJobSearch:
        featureText = 'KI-optimierte Jobsuche';
        benefitText =
            'Finde Jobs, die perfekt zu deinen Fähigkeiten und Erfahrungen passen, mit unserer KI-gestützten Suche.';
        break;
      case PremiumFeature.unlimitedFavorites:
        featureText = 'Unbegrenzte Favoriten';
        benefitText =
            'Speichere unbegrenzt viele Jobs als Favoriten und behalte alle interessanten Stellen im Blick.';
        break;
      case PremiumFeature.adFree:
        featureText = 'Werbefreie Nutzung';
        benefitText =
            'Genieße eine störungsfreie Erfahrung ohne Werbeeinblendungen.';
        break;
      case PremiumFeature.insights:
        featureText = 'Premium-Insights';
        benefitText =
            'Erhalte detaillierte Statistiken und Einblicke zu deiner Jobsuche und deinen Bewerbungen.';
        break;
      default:
        featureText = 'Premium-Feature';
        benefitText =
            'Schalte alle Premium-Funktionen frei für ein verbessertes Bewerbungserlebnis.';
    }

    // Dialog anzeigen
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Upgrade auf Premium für $featureText'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(benefitText),
                const SizedBox(height: 16),
                const Text('Mit Premium erhältst du:'),
                const SizedBox(height: 8),
                _buildBulletPoint('KI-generierte Anschreiben'),
                _buildBulletPoint('KI-optimierte Jobsuche'),
                _buildBulletPoint('Unbegrenzte Favoriten'),
                _buildBulletPoint('Keine Werbung'),
                _buildBulletPoint('Premium-Insights und Statistiken'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Später'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  // Timestamp speichern
                  prefs.setInt(_lastPromptShownKey, now);
                },
                child: const Text('Upgrade auf Premium'),
              ),
            ],
          ),
    );

    if (result == true) {
      // Zur Premium-Vergleichsseite navigieren
      GoRouter.of(context).push('/premium');
      return true;
    }

    return false;
  }

  /// Zeigt einen alternativen Dialog an, der Werbung als Option anbietet
  Future<String?> showPremiumOrAdDialog(
    BuildContext context,
    PremiumFeature feature,
  ) async {
    // Featuretext für den Dialog
    String featureText;

    switch (feature) {
      case PremiumFeature.aiCoverLetter:
        featureText = 'KI-Anschreiben-Generator';
        break;
      case PremiumFeature.aiJobSearch:
        featureText = 'KI-optimierte Jobsuche';
        break;
      case PremiumFeature.unlimitedFavorites:
        featureText = 'Unbegrenzte Favoriten';
        break;
      case PremiumFeature.adFree:
        featureText = 'Werbefreie Nutzung';
        break;
      case PremiumFeature.insights:
        featureText = 'Premium-Insights';
        break;
      default:
        featureText = 'Premium-Feature';
    }

    // Dialog anzeigen
    return await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Zugriff auf $featureText'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Für $featureText benötigst du Premium-Zugriff.'),
                const SizedBox(height: 16),
                const Text('Du hast folgende Möglichkeiten:'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop('cancel'),
                child: const Text('Abbrechen'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop('ad'),
                child: const Text('Werbung ansehen für 24h Zugriff'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop('premium'),
                child: const Text('Premium abonnieren'),
              ),
            ],
          ),
    );
  }

  /// Gewährt temporären Zugriff auf ein Feature nach dem Ansehen einer Werbung
  Future<bool> grantAdBasedAccess(PremiumFeature feature) async {
    final currentUser = await _subscriptionService.getCurrentUser();
    if (currentUser == null) {
      _log.d('Kein angemeldeter Benutzer für Ad-basierten Zugriff');
      return false;
    }

    try {
      final adAccess = await _subscriptionService.grantAdBasedAccess(
        userId: currentUser.id,
        feature: feature.name,
      );

      if (adAccess != null) {
        // Cache zurücksetzen
        _resetCache();
        return true;
      }

      return false;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Gewähren des Ad-basierten Zugriffs',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  // Hilfsmethode für Aufzählungspunkte im Dialog
  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }
}

/// Provider für den PremiumFeatureService
final premiumFeatureServiceProvider = Provider<PremiumFeatureService>((ref) {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return PremiumFeatureService(subscriptionService, ref);
});

/// Convenience-Provider für schnellen Zugriff auf Feature-Berechtigungen
final featureAccessProvider = FutureProvider.family<bool, PremiumFeature>((
  ref,
  feature,
) async {
  final premiumFeatureService = ref.watch(premiumFeatureServiceProvider);
  return await premiumFeatureService.hasAccessTo(feature);
});

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/models/subscription_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service zur Verwaltung der Bewerbungszähler
class ApplicationCounterService {
  final SupabaseClient _supabase;
  final Ref _ref;
  final _log = getLogger('ApplicationCounterService');

  // Supabase-Tabellennamen
  static const String _subscriptionsTable = 'subscriptions';
  static const String _applicationCountersTable = 'application_counters';

  ApplicationCounterService(this._supabase, this._ref);

  /// Gibt die verbleibenden Bewerbungen für den aktuellen Benutzer zurück
  Future<Map<String, dynamic>> getRemainingApplications() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      // Aktives Abonnement abfragen
      final subscription = await _getCurrentSubscription(currentUser.id);

      if (subscription == null) {
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      // Wenn der Plan Unlimited ist, unbegrenzte Bewerbungen zurückgeben
      if (subscription.planType == 'unlimited') {
        return {
          'remaining': -1, // -1 bedeutet unbegrenzt
          'total': -1,
          'unlimited': true,
        };
      }

      // Bewerbungszähler abfragen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, einen neuen erstellen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
        );

        return {
          'remaining': totalApplications,
          'total': totalApplications,
          'unlimited': false,
        };
      }

      return {
        'remaining': counter['remaining_applications'] ?? 0,
        'total': counter['total_applications'] ?? 0,
        'unlimited': false,
      };
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen der verbleibenden Bewerbungen',
        error: e,
        stackTrace: stackTrace,
      );
      return {'remaining': 0, 'total': 0, 'unlimited': false};
    }
  }

  /// Inkrementiert den Bewerbungszähler des Benutzers
  Future<bool> incrementApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Aktives Abonnement abfragen
      final subscription = await _getCurrentSubscription(currentUser.id);

      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Wenn der Plan Unlimited ist, nichts tun
      if (subscription.planType == 'unlimited') {
        return true;
      }

      // Bewerbungszähler abfragen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, einen neuen erstellen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications:
              totalApplications - 1, // Eine Bewerbung abziehen
        );
        return true;
      }

      // Prüfen, ob noch Bewerbungen übrig sind
      final remainingApplications = counter['remaining_applications'] ?? 0;
      if (remainingApplications <= 0) {
        _log.d('Keine Bewerbungen mehr übrig');
        return false;
      }

      // Bewerbungszähler aktualisieren
      await _supabase
          .from(_applicationCountersTable)
          .update({
            'remaining_applications': remainingApplications - 1,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser.id);

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Inkrementieren des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Setzt den Bewerbungszähler des Benutzers zurück
  Future<bool> resetApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Aktives Abonnement abfragen
      final subscription = await _getCurrentSubscription(currentUser.id);

      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Wenn der Plan Unlimited ist, nichts tun
      if (subscription.planType == 'unlimited') {
        return true;
      }

      final totalApplications = _getTotalApplicationsForPlan(
        subscription.planType,
      );

      // Bewerbungszähler abfragen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, einen neuen erstellen
      if (counter == null) {
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
        );
        return true;
      }

      // Bewerbungszähler zurücksetzen
      await _supabase
          .from(_applicationCountersTable)
          .update({
            'remaining_applications': totalApplications,
            'total_applications': totalApplications,
            'reset_date': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser.id);

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Zurücksetzen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Gibt das aktuelle Abonnement des Benutzers zurück
  Future<SubscriptionModel?> _getCurrentSubscription(String userId) async {
    try {
      final response =
          await _supabase
              .from(_subscriptionsTable)
              .select()
              .eq('user_id', userId)
              .eq('status', 'active')
              .gt('end_date', DateTime.now().toIso8601String())
              .order('end_date', ascending: false)
              .limit(1)
              .maybeSingle();

      if (response != null) {
        return SubscriptionModel(
          subscriptionId: response['id'],
          userId: response['user_id'],
          subscriptionType: response['subscription_type'],
          planType: response['plan_type'] ?? 'basic',
          isPremium: true,
          autoRenew: response['auto_renew'] ?? false,
          remainingApplications: response['remaining_applications'],
          totalApplications: response['total_applications'],
          applicationsResetDate:
              response['applications_reset_date'] != null
                  ? DateTime.parse(response['applications_reset_date'])
                  : null,
          startDate:
              response['start_date'] != null
                  ? DateTime.parse(response['start_date'])
                  : null,
          endDate:
              response['end_date'] != null
                  ? DateTime.parse(response['end_date'])
                  : null,
        );
      }

      return null;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des aktuellen Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Gibt den Bewerbungszähler des Benutzers zurück
  Future<Map<String, dynamic>?> _getApplicationCounter(String userId) async {
    try {
      final response =
          await _supabase
              .from(_applicationCountersTable)
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      return response;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Erstellt einen neuen Bewerbungszähler
  Future<bool> _createApplicationCounter({
    required String userId,
    required int totalApplications,
    required int remainingApplications,
  }) async {
    try {
      await _supabase.from(_applicationCountersTable).insert({
        'user_id': userId,
        'total_applications': totalApplications,
        'remaining_applications': remainingApplications,
        'reset_date': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Erstellen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Gibt die Gesamtanzahl der Bewerbungen für einen Plan zurück
  int _getTotalApplicationsForPlan(String? planType) {
    switch (planType?.toLowerCase()) {
      case 'basic':
        return 30;
      case 'pro':
        return 150;
      case 'premium':
      case 'unlimited':
        return -1; // -1 bedeutet unbegrenzt
      default:
        return 5; // Fallback
    }
  }
}

/// Provider für den ApplicationCounterService
final applicationCounterServiceProvider = Provider<ApplicationCounterService>((
  ref,
) {
  final supabase = Supabase.instance.client;
  return ApplicationCounterService(supabase, ref);
});

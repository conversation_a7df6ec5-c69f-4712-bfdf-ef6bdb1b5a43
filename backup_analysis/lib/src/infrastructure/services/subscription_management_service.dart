import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/domain/models/subscription_model.dart';
import 'package:ki_test/src/domain/models/subscription_plan.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service zur Verwaltung von Abonnements
class SubscriptionManagementService {
  final SupabaseClient _supabase;
  final Ref _ref;
  final _log = getLogger('SubscriptionManagementService');

  // Supabase-Tabellennamen
  static const String _subscriptionsTable = 'subscriptions';
  static const String _applicationCountersTable = 'application_counters';

  // Anzahl der kostenlosen Bewerbungen für neue Nutzer
  static const int _freeTierApplications = 5;

  SubscriptionManagementService(this._supabase, this._ref);

  /// Gibt das aktuelle Abonnement des Benutzers zurück
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return null;
      }

      final response =
          await _supabase
              .from(_subscriptionsTable)
              .select()
              .eq('user_id', currentUser.id)
              .eq('status', 'active')
              .gt('end_date', DateTime.now().toIso8601String())
              .order('end_date', ascending: false)
              .limit(1)
              .maybeSingle();

      if (response != null) {
        return SubscriptionModel.fromJson(response);
      }

      // Wenn kein aktives Abonnement gefunden wurde, erstelle ein Basic-Abonnement
      return await _createDefaultSubscription(currentUser.id);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des aktuellen Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Erstellt ein Standard-Abonnement für neue Benutzer
  Future<SubscriptionModel?> _createDefaultSubscription(String userId) async {
    try {
      // PRÜFUNG HINZUGEFÜGT: Zuerst prüfen, ob überhaupt schon ein Abonnement für diesen Benutzer existiert
      final existingSubscription =
          await _supabase
              .from(_subscriptionsTable)
              .select('id')
              .eq('user_id', userId)
              .maybeSingle();

      // Wenn bereits ein Abonnement existiert (egal welcher Status), keines erstellen.
      if (existingSubscription != null) {
        _log.w(
          "Ein Abonnement für Benutzer $userId existiert bereits. Überspringe die Erstellung eines neuen Standard-Abonnements.",
        );
        // Hier könnte man optional das bestehende reaktivieren, aber vorerst ist das Überspringen sicherer.
        return null;
      }

      // Prüfe, ob bereits ein Bewerbungszähler existiert
      final counterExists = await _checkIfCounterExists(userId);

      // Wenn kein Zähler existiert, erstelle einen mit den kostenlosen Bewerbungen
      if (!counterExists) {
        await _createApplicationCounter(
          userId: userId,
          totalApplications: _freeTierApplications,
          remainingApplications: _freeTierApplications,
        );
      }

      // Erstelle ein Free-Abonnement
      final endDate = DateTime.now().add(const Duration(days: 30));

      final subscriptionData = {
        'user_id': userId,
        'subscription_type': 'free',
        'plan_type': 'free',
        'status': 'active',
        'is_premium': false,
        'auto_renew': false,
        'start_date': DateTime.now().toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await _supabase
              .from(_subscriptionsTable)
              .insert(subscriptionData)
              .select()
              .single();

      // Aktiviere Werbung für Basic-Nutzer
      final adService = _ref.read(adServiceProvider);
      adService.enableAds();

      return SubscriptionModel.fromJson(response);
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Erstellen des Standard-Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Prüft, ob bereits ein Bewerbungszähler für den Benutzer existiert
  Future<bool> _checkIfCounterExists(String userId) async {
    try {
      final response =
          await _supabase
              .from(_applicationCountersTable)
              .select('id')
              .eq('user_id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      _log.e('Fehler beim Prüfen des Bewerbungszählers', error: e);
      return false;
    }
  }

  /// Erstellt einen neuen Bewerbungszähler
  Future<bool> _createApplicationCounter({
    required String userId,
    required int totalApplications,
    required int remainingApplications,
    bool isFreeOrBasic = false,
  }) async {
    try {
      final counterData = {
        'user_id': userId,
        'total_applications': totalApplications,
        'remaining_applications': remainingApplications,
        'reset_date': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Prüfe, ob die free_reset_date-Spalte existiert, bevor wir sie setzen
      if (isFreeOrBasic) {
        try {
          // Versuche, die Spaltenstruktur der Tabelle zu prüfen
          final tableInfo =
              await _supabase
                  .from(_applicationCountersTable)
                  .select('*')
                  .limit(1)
                  .maybeSingle();

          // Wenn die Tabelle die Spalte enthält oder leer ist, füge sie zum Insert hinzu
          if (tableInfo == null || tableInfo.containsKey('free_reset_date')) {
            counterData['free_reset_date'] = DateTime.now().toIso8601String();
            _log.i("free_reset_date-Spalte wird beim Erstellen gesetzt");
          } else {
            _log.w("free_reset_date-Spalte nicht gefunden, wird übersprungen");
          }
        } catch (columnError) {
          _log.w("Fehler beim Prüfen der Spaltenstruktur: $columnError");
          // Wir versuchen trotzdem, den Zähler zu erstellen, ohne free_reset_date zu setzen
        }
      }

      await _supabase.from(_applicationCountersTable).insert(counterData);

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Erstellen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Aktualisiert den Bewerbungszähler
  Future<bool> updateApplicationCounter({
    required int totalApplications,
    required int remainingApplications,
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Prüfe, ob bereits ein Zähler existiert
      final counterExists = await _checkIfCounterExists(currentUser.id);

      if (counterExists) {
        // Aktualisiere den bestehenden Zähler
        await _supabase
            .from(_applicationCountersTable)
            .update({
              'total_applications': totalApplications,
              'remaining_applications': remainingApplications,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', currentUser.id);
      } else {
        // Erstelle einen neuen Zähler
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: remainingApplications,
        );
      }

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Aktualisieren des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Setzt den Bewerbungszähler manuell zurück
  Future<bool> resetApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'reset_application_counter',
          params: {'p_user_id': currentUser.id},
        );

        if (response != null) {
          final bool success = response as bool;

          if (success) {
            _log.i('Bewerbungszähler erfolgreich zurückgesetzt');
            return true;
          } else {
            _log.w('Bewerbungszähler konnte nicht zurückgesetzt werden');
          }
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Manuell den Bewerbungszähler zurücksetzen
      final subscription = await getCurrentSubscription();
      if (subscription != null) {
        return await _manuallyResetCounter(
          currentUser.id,
          subscription.planType,
        );
      }

      return false;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Zurücksetzen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Aktualisiert das Abonnement des Benutzers
  Future<bool> updateSubscription({
    required SubscriptionPlanType planType,
    required bool isPremium,
    int? durationInDays = 30,
    String? durationType = 'month', // 'month', 'quarter', 'year'
  }) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Berechne das Ablaufdatum basierend auf dem Typ und der Dauer
      DateTime expiryDate;
      if (durationType == 'year') {
        expiryDate = DateTime.now().add(Duration(days: 365));
      } else if (durationType == 'quarter') {
        expiryDate = DateTime.now().add(Duration(days: 90));
      } else {
        // Standard: Monat
        expiryDate = DateTime.now().add(Duration(days: durationInDays ?? 30));
      }

      // Hole den Plan
      final plan = SubscriptionPlans.getPlan(planType);

      // Aktualisiere den Premium-Status über den UserProfileNotifier
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: isPremium,
            premiumExpiryDate: isPremium ? expiryDate : null,
            planType: planType.toString().split('.').last,
          );

      // Aktualisiere den Bewerbungszähler
      if (!isPremium && plan.applicationsPerMonth != null) {
        // Verwende die serverseitige Funktion, um den Bewerbungszähler zurückzusetzen
        await resetApplicationCounter();
      }

      // Aktiviere oder deaktiviere Werbung basierend auf dem Plan-Typ
      final adService = _ref.read(adServiceProvider);
      if (plan.showsAds) {
        adService.enableAds();
      } else {
        adService.disableAds();
      }

      // Aktualisiere das Abonnement in der Datenbank
      await _supabase
          .from(_subscriptionsTable)
          .update({
            'plan_type': planType.toString().split('.').last,
            'is_premium': isPremium,
            'status': 'active',
            'end_date': expiryDate.toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser.id)
          .eq('status', 'active');

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Aktualisieren des Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Prüft, ob der Benutzer ein Premium-Abonnement hat
  Future<bool> hasPremiumSubscription() async {
    final subscription = await getCurrentSubscription();
    return subscription?.isPremium ?? false;
  }

  /// Synchronisiert das Abonnement zwischen der App und dem Server
  Future<bool> syncSubscription() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Hole das aktuelle Abonnement aus der Datenbank
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Aktualisiere den Premium-Status über den UserProfileNotifier
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: subscription.isPremium,
            premiumExpiryDate: subscription.endDate,
            planType: subscription.planType,
          );

      // Aktiviere oder deaktiviere Werbung basierend auf dem Plan-Typ
      final adService = _ref.read(adServiceProvider);
      if (subscription.planType?.toLowerCase() == 'free' ||
          subscription.planType?.toLowerCase() == 'basic') {
        adService.enableAds();
        _log.i('Werbung aktiviert für Plan: ${subscription.planType}');
      } else {
        adService.disableAds();
        _log.i('Werbung deaktiviert für Plan: ${subscription.planType}');
      }

      // Überprüfe und aktualisiere den Bewerbungszähler
      try {
        // Wenn der Plan nicht Premium oder Unlimited ist, überprüfe den Bewerbungszähler
        if (subscription.planType?.toLowerCase() != 'premium' &&
            subscription.planType?.toLowerCase() != 'unlimited') {
          // Wenn es ein kostenloses Abonnement ist, überprüfe den wöchentlichen Zähler
          if (subscription.planType?.toLowerCase() == 'free' ||
              subscription.planType?.toLowerCase() == 'basic') {
            try {
              // Überprüfe und aktualisiere den kostenlosen Bewerbungszähler
              final freeCounterResult =
                  await checkAndResetFreeApplicationCounter();
              if (freeCounterResult) {
                _log.i(
                  'Kostenloser Bewerbungszähler erfolgreich überprüft und aktualisiert',
                );
              }
            } catch (freeError) {
              _log.w(
                'Fehler beim Überprüfen des kostenlosen Bewerbungszählers: $freeError',
              );
            }
          } else {
            // Für andere Pläne (z.B. Pro) verwende die normale Überprüfung
            try {
              // Versuche zuerst, die serverseitige Funktion zu verwenden
              try {
                final response = await _supabase.rpc(
                  'check_user_application_counters',
                  params: {'p_user_id': currentUser.id},
                );

                if (response == true) {
                  _log.i(
                    'Bewerbungszähler erfolgreich überprüft und aktualisiert',
                  );
                }
              } catch (rpcError) {
                _log.w(
                  'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
                );

                // Fallback: Manuell den Bewerbungszähler überprüfen und aktualisieren
                await _manuallyCheckAndUpdateCounter(
                  currentUser.id,
                  subscription,
                );
              }
            } catch (e) {
              _log.w('Fehler beim Überprüfen des Bewerbungszählers: $e');

              // Fallback: Manuell den Bewerbungszähler zurücksetzen
              await _manuallyResetCounter(
                currentUser.id,
                subscription.planType,
              );

              _log.i(
                'Bewerbungszähler zurückgesetzt für Plan ${subscription.planType}',
              );
            }
          }
        } else {
          _log.i(
            'Premium/Unlimited-Plan: Keine Überprüfung des Bewerbungszählers notwendig',
          );
        }
      } catch (e) {
        _log.w('Fehler beim Überprüfen des Bewerbungszählers: $e');
      }

      _log.i('Abonnement erfolgreich synchronisiert');
      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Synchronisieren des Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Kündigt das aktuelle Abonnement des Benutzers
  Future<bool> cancelSubscription() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.e('Benutzer ist nicht angemeldet');
        return false;
      }

      // Suche nach aktiven Abonnements in der Datenbank
      final response =
          await _supabase
              .from(_subscriptionsTable)
              .select()
              .eq('user_id', currentUser.id)
              .eq('status', 'active')
              .limit(1)
              .maybeSingle();

      if (response == null) {
        _log.w('Kein aktives Abonnement in der Datenbank gefunden');

        // Aktualisiere trotzdem den Premium-Status im UserProfile
        await _ref
            .read(userProfileProvider.notifier)
            .updatePremiumStatus(isPremium: false, planType: 'basic');

        return true;
      }

      // Markiere das Abonnement als gekündigt
      await _supabase
          .from(_subscriptionsTable)
          .update({
            'status': 'cancelled',
            'cancelled_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', response['id']);

      _log.i('Abonnement ${response['id']} erfolgreich gekündigt');

      // Optional: Aktualisiere den Premium-Status im UserProfile
      // Wir behalten den Premium-Status bis zum Ablaufdatum bei

      return true;
    } catch (e) {
      _log.e('Fehler beim Kündigen des Abonnements', error: e);
      return false;
    }
  }

  /// Gibt die verbleibenden Bewerbungen zurück
  Future<Map<String, dynamic>> getRemainingApplications() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'get_remaining_applications',
          params: {'p_user_id': currentUser.id},
        );

        if (response != null) {
          // Konvertiere die Antwort in ein Map
          final Map<String, dynamic> result = Map<String, dynamic>.from(
            response,
          );
          _log.i('Verbleibende Bewerbungen abgerufen: $result');
          return result;
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Abfrage.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Abfrage fort
      }

      // Fallback: Versuche, die Bewerbungen lokal abzurufen
      return _getApplicationsLocally();
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen der verbleibenden Bewerbungen',
        error: e,
        stackTrace: stackTrace,
      );

      // Fallback: Versuche, die Bewerbungen lokal abzurufen
      return _getApplicationsLocally();
    }
  }

  /// Fallback-Methode, um die verbleibenden Bewerbungen lokal abzurufen
  Future<Map<String, dynamic>> _getApplicationsLocally() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      // Aktives Abonnement abfragen
      final subscription = await getCurrentSubscription();

      if (subscription == null) {
        return {'remaining': 0, 'total': 0, 'unlimited': false};
      }

      // Wenn der Plan Premium oder Unlimited ist, unbegrenzte Bewerbungen zurückgeben
      if (subscription.planType?.toLowerCase() == 'unlimited' ||
          subscription.planType?.toLowerCase() == 'premium') {
        return {
          'remaining': -1, // -1 bedeutet unbegrenzt
          'total': -1,
          'unlimited': true,
        };
      }

      // Bewerbungszähler abfragen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, einen neuen erstellen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
        );

        return {
          'remaining': totalApplications,
          'total': totalApplications,
          'unlimited': false,
        };
      }

      return {
        'remaining': counter['remaining_applications'] ?? 0,
        'total': counter['total_applications'] ?? 0,
        'unlimited': false,
      };
    } catch (e) {
      _log.e(
        'Fehler beim lokalen Abrufen der verbleibenden Bewerbungen',
        error: e,
      );
      return {'remaining': 0, 'total': 0, 'unlimited': false};
    }
  }

  /// Gibt den Bewerbungszähler des Benutzers zurück
  Future<Map<String, dynamic>?> _getApplicationCounter(String userId) async {
    try {
      final response =
          await _supabase
              .from(_applicationCountersTable)
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      return response;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Gibt die Gesamtanzahl der Bewerbungen für einen Plan zurück
  int _getTotalApplicationsForPlan(String? planType) {
    switch (planType?.toLowerCase()) {
      case 'free':
        return 5; // 5 kostenlose Bewerbungen pro Woche
      case 'basic': // Für Abwärtskompatibilität
        return 5; // Früher 30, jetzt 5 wie Free
      case 'pro':
        return 150;
      case 'premium':
      case 'unlimited':
        return -1; // -1 bedeutet unbegrenzt
      default:
        return _freeTierApplications; // Fallback für neue Nutzer
    }
  }

  /// Inkrementiert den Bewerbungszähler des Benutzers
  /// DEAKTIVIERT ZUM TESTEN - MÖGLICHE URSACHE FÜR DOPPELTEN GUTHABEN-ABZUG
  Future<bool> incrementApplicationCounter() async {
    _log.i(
      'incrementApplicationCounter() DEAKTIVIERT - Teste ob das die Ursache für doppelten Abzug ist',
    );

    // Simuliere Erfolg ohne tatsächlichen Abzug
    return true;

    /* ORIGINAL CODE DEAKTIVIERT:
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'increment_application_counter',
          params: {'p_user_id': currentUser.id},
        );

        if (response != null) {
          final bool success = response as bool;

          if (success) {
            _log.i('Bewerbungszähler erfolgreich inkrementiert');
            return true;
          } else {
            _log.w('Bewerbungszähler konnte nicht inkrementiert werden');
          }
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Versuche, den Bewerbungszähler lokal zu inkrementieren
      return _incrementApplicationCounterLocally();
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Inkrementieren des Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );

      // Fallback: Versuche, den Bewerbungszähler lokal zu inkrementieren
      return _incrementApplicationCounterLocally();
    }
    */
  }

  /// Fallback-Methode, um den Bewerbungszähler lokal zu inkrementieren
  Future<bool> _incrementApplicationCounterLocally() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return false;
      }

      // Aktives Abonnement abfragen
      final subscription = await getCurrentSubscription();

      if (subscription == null) {
        return false;
      }

      // Wenn der Plan Premium oder Unlimited ist, nichts tun
      if (subscription.planType?.toLowerCase() == 'unlimited' ||
          subscription.planType?.toLowerCase() == 'premium') {
        return true;
      }

      // Bewerbungszähler abfragen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, einen neuen erstellen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications:
              totalApplications - 1, // Eine Bewerbung abziehen
        );
        return true;
      }

      // Prüfen, ob noch Bewerbungen übrig sind
      final remainingApplications = counter['remaining_applications'] ?? 0;
      if (remainingApplications <= 0) {
        _log.d('Keine Bewerbungen mehr übrig');
        return false;
      }

      // Bewerbungszähler aktualisieren
      await _supabase
          .from(_applicationCountersTable)
          .update({
            'remaining_applications': remainingApplications - 1,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser.id);

      return true;
    } catch (e) {
      _log.e(
        'Fehler beim lokalen Inkrementieren des Bewerbungszählers',
        error: e,
      );
      return false;
    }
  }

  /// Ändert das Abonnement des Benutzers
  Future<bool> changePlan(String newPlanType) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Validiere den Plan-Typ
      switch (newPlanType.toLowerCase()) {
        case 'basic':
        case 'pro':
        case 'unlimited':
          // Gültige Plan-Typen
          break;
        default:
          _log.e('Ungültiger Plan-Typ: $newPlanType');
          return false;
      }

      // Hole das aktuelle Abonnement, um das Ablaufdatum zu überprüfen
      final currentSubscription =
          await _supabase
              .from(_subscriptionsTable)
              .select('end_date')
              .eq('user_id', currentUser.id)
              .eq('status', 'active')
              .maybeSingle();

      // Verwende das Ablaufdatum aus der Datenbank, wenn vorhanden
      DateTime expiryDate;
      if (currentSubscription != null &&
          currentSubscription['end_date'] != null) {
        try {
          expiryDate = DateTime.parse(currentSubscription['end_date']);
          _log.i('Verwende Ablaufdatum aus der Datenbank: $expiryDate');
        } catch (e) {
          // Fallback: 30 Tage in der Zukunft
          expiryDate = DateTime.now().add(const Duration(days: 30));
          _log.w(
            'Fehler beim Parsen des Ablaufdatums, verwende Fallback: $expiryDate',
          );
        }
      } else {
        // Fallback: 30 Tage in der Zukunft
        expiryDate = DateTime.now().add(const Duration(days: 30));
        _log.i(
          'Kein Ablaufdatum in der Datenbank gefunden, verwende Fallback: $expiryDate',
        );
      }

      // Aktualisiere den Premium-Status über den UserProfileNotifier
      await _ref
          .read(userProfileProvider.notifier)
          .updatePremiumStatus(
            isPremium: newPlanType != 'basic',
            premiumExpiryDate: expiryDate,
            planType: newPlanType,
          );

      // Wenn der Plan nicht Premium oder Unlimited ist, setze den Bewerbungszähler zurück
      if (newPlanType.toLowerCase() == 'premium' ||
          newPlanType.toLowerCase() == 'unlimited') {
        _log.i(
          'Premium/Unlimited-Plan: Bewerbungszähler auf unbegrenzt gesetzt',
        );
      } else {
        // Verwende die serverseitige Funktion, um den Bewerbungszähler zurückzusetzen
        await resetApplicationCounter();

        if (newPlanType.toLowerCase() == 'free' ||
            newPlanType.toLowerCase() == 'basic') {
          _log.i('Free/Basic-Plan: Bewerbungszähler auf 5 zurückgesetzt');
        } else if (newPlanType.toLowerCase() == 'pro') {
          _log.i('Pro-Plan: Bewerbungszähler auf 150 zurückgesetzt');
        }
      }

      // Aktiviere oder deaktiviere Werbung basierend auf dem Plan-Typ
      final adService = _ref.read(adServiceProvider);
      if (newPlanType.toLowerCase() == 'basic') {
        adService.enableAds();
      } else {
        adService.disableAds();
      }

      // Aktualisiere das Abonnement in der Datenbank
      await _supabase
          .from(_subscriptionsTable)
          .update({
            'plan_type': newPlanType,
            'is_premium': newPlanType != 'basic',
            'status': 'active',
            'end_date': expiryDate.toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser.id)
          .eq('status', 'active');

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Ändern des Abonnements',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Manuell den Bewerbungszähler überprüfen und aktualisieren
  Future<bool> _manuallyCheckAndUpdateCounter(
    String userId,
    SubscriptionModel subscription,
  ) async {
    try {
      // Prüfe, ob bereits ein Zähler existiert
      final counter = await _getApplicationCounter(userId);

      // Wenn kein Zähler existiert, erstelle einen neuen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: userId,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
        );
        return true;
      }

      // Prüfe, ob der Zähler zurückgesetzt werden muss
      final resetDate =
          counter['reset_date'] != null
              ? DateTime.parse(counter['reset_date'])
              : null;

      // Wenn kein Reset-Datum existiert oder es mehr als 30 Tage her ist, setze den Zähler zurück
      if (resetDate == null ||
          DateTime.now().difference(resetDate).inDays > 30) {
        return await _manuallyResetCounter(userId, subscription.planType);
      }

      return true;
    } catch (e) {
      _log.e(
        'Fehler beim manuellen Überprüfen und Aktualisieren des Bewerbungszählers',
        error: e,
      );
      return false;
    }
  }

  /// Manuell den Bewerbungszähler zurücksetzen
  Future<bool> _manuallyResetCounter(String userId, String? planType) async {
    try {
      final totalApplications = _getTotalApplicationsForPlan(planType);
      final isFreeOrBasic =
          planType?.toLowerCase() == 'free' ||
          planType?.toLowerCase() == 'basic';

      // Prüfe, ob bereits ein Zähler existiert
      final counterExists = await _checkIfCounterExists(userId);

      if (counterExists) {
        // Aktualisiere den bestehenden Zähler
        final updateData = {
          'total_applications': totalApplications,
          'remaining_applications': totalApplications,
          'reset_date': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        // Prüfe, ob die free_reset_date-Spalte existiert, bevor wir sie aktualisieren
        try {
          // Versuche, die Spaltenstruktur der Tabelle zu prüfen
          final tableInfo =
              await _supabase
                  .from(_applicationCountersTable)
                  .select('*')
                  .limit(1)
                  .maybeSingle();

          // Wenn die Tabelle die Spalte enthält, füge sie zum Update hinzu
          if (tableInfo != null &&
              isFreeOrBasic &&
              tableInfo.containsKey('free_reset_date')) {
            updateData['free_reset_date'] = DateTime.now().toIso8601String();
            _log.i("free_reset_date-Spalte gefunden und wird aktualisiert");
          } else if (isFreeOrBasic) {
            _log.w("free_reset_date-Spalte nicht gefunden, wird übersprungen");
          }
        } catch (columnError) {
          _log.w("Fehler beim Prüfen der Spaltenstruktur: $columnError");
          // Wir fahren trotzdem fort, ohne die free_reset_date zu setzen
        }

        await _supabase
            .from(_applicationCountersTable)
            .update(updateData)
            .eq('user_id', userId);
      } else {
        // Erstelle einen neuen Zähler
        await _createApplicationCounter(
          userId: userId,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
          isFreeOrBasic: isFreeOrBasic,
        );
      }

      return true;
    } catch (e) {
      _log.e(
        'Fehler beim manuellen Zurücksetzen des Bewerbungszählers',
        error: e,
      );
      return false;
    }
  }

  /// Überprüft und aktualisiert den Bewerbungszähler für kostenlose Benutzer
  Future<bool> checkAndResetFreeApplicationCounter() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return false;
      }

      // Hole das aktuelle Abonnement
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return false;
      }

      // Nur für kostenlose Abonnements
      if (subscription.planType?.toLowerCase() != 'free' &&
          subscription.planType?.toLowerCase() != 'basic') {
        return true;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'check_free_application_counter',
          params: {'p_user_id': currentUser.id},
        );

        if (response == true) {
          _log.i(
            'Kostenloser Bewerbungszähler erfolgreich überprüft und aktualisiert',
          );
          return true;
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Manuell den Bewerbungszähler überprüfen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, erstelle einen neuen
      if (counter == null) {
        final totalApplications = _getTotalApplicationsForPlan(
          subscription.planType,
        );
        await _createApplicationCounter(
          userId: currentUser.id,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
          isFreeOrBasic: true,
        );
        return true;
      }

      // Prüfe, ob der Zähler zurückgesetzt werden muss
      DateTime? freeResetDate;

      // Prüfe, ob die free_reset_date-Spalte existiert
      if (counter.containsKey('free_reset_date') &&
          counter['free_reset_date'] != null) {
        freeResetDate = DateTime.parse(counter['free_reset_date']);
        _log.i("free_reset_date gefunden: $freeResetDate");
      } else {
        // Wenn die Spalte nicht existiert, verwende das normale reset_date
        freeResetDate =
            counter['reset_date'] != null
                ? DateTime.parse(counter['reset_date'])
                : null;
        _log.w(
          "free_reset_date nicht gefunden, verwende reset_date: $freeResetDate",
        );
      }

      // Wenn kein Reset-Datum existiert oder es mehr als 7 Tage her ist, setze den Zähler zurück
      if (freeResetDate == null ||
          DateTime.now().difference(freeResetDate).inDays >= 7) {
        return await _manuallyResetCounter(
          currentUser.id,
          subscription.planType,
        );
      }

      return true;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Überprüfen des kostenlosen Bewerbungszählers',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Gibt das nächste Reset-Datum für kostenlose Bewerbungen zurück
  Future<DateTime?> getNextFreeResetDate() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _log.d('Kein angemeldeter Benutzer gefunden');
        return null;
      }

      // Hole das aktuelle Abonnement
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log.d('Kein aktives Abonnement gefunden');
        return null;
      }

      // Nur für kostenlose Abonnements
      if (subscription.planType?.toLowerCase() != 'free' &&
          subscription.planType?.toLowerCase() != 'basic') {
        return null;
      }

      try {
        // Versuche zuerst, die serverseitige Funktion zu verwenden
        final response = await _supabase.rpc(
          'get_next_free_reset_date',
          params: {'p_user_id': currentUser.id},
        );

        if (response != null) {
          return DateTime.parse(response.toString());
        }
      } catch (rpcError) {
        _log.w(
          'Fehler bei der RPC-Funktion: $rpcError. Verwende lokale Methode.',
        );
        // Bei Fehler mit der RPC-Funktion, fahre mit der lokalen Methode fort
      }

      // Fallback: Manuell das nächste Reset-Datum berechnen
      final counter = await _getApplicationCounter(currentUser.id);

      // Wenn kein Zähler existiert, gib das aktuelle Datum zurück
      if (counter == null) {
        return DateTime.now();
      }

      // Berechne das nächste Reset-Datum
      DateTime baseDate;

      // Prüfe, ob die free_reset_date-Spalte existiert
      if (counter.containsKey('free_reset_date') &&
          counter['free_reset_date'] != null) {
        baseDate = DateTime.parse(counter['free_reset_date']);
        _log.i("free_reset_date gefunden: $baseDate");
      } else {
        // Wenn die Spalte nicht existiert, verwende das normale reset_date oder das aktuelle Datum
        baseDate =
            counter['reset_date'] != null
                ? DateTime.parse(counter['reset_date'])
                : DateTime.now();
        _log.w(
          "free_reset_date nicht gefunden, verwende alternatives Datum: $baseDate",
        );
      }

      // Das nächste Reset-Datum ist 7 Tage nach dem letzten Reset
      return baseDate.add(const Duration(days: 7));
    } catch (e) {
      _log.e(
        'Fehler beim Abrufen des nächsten Reset-Datums für kostenlose Bewerbungen',
        error: e,
      );
      return null;
    }
  }
}

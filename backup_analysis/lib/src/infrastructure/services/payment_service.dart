import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// WICHTIG: Definiere deine Produkt-ID hier (muss mit Play Console übereinstimmen)
// Für Android - Produkt-IDs für verschiedene Abonnementtypen
const String _androidBasicProductId = 'basic_monthly_subscription';
const String _androidProProductId = 'pro_monthly_subscription';
const String _androidUnlimitedProductId = 'unlimited_monthly_subscription';
// Standardmäßig verwenden wir das Basic-Abonnement
const String _androidPremiumProductId = _androidBasicProductId;

// Für iOS - Produkt-IDs für verschiedene Abonnementtypen
const String _iosBasicProductId = 'basic_monthly_subscription';
const String _iosProProductId = 'pro_monthly_subscription';
const String _iosUnlimitedProductId = 'unlimited_monthly_subscription';
// Standardmäßig verwenden wir das Basic-Abonnement
const String _iosPremiumProductId = _iosBasicProductId;

// Die zu verwendende Produkt-ID basierend auf der Plattform
final String _premiumProductId =
    Platform.isAndroid ? _androidPremiumProductId : _iosPremiumProductId;

// Debug-Modus für Testzwecke
const bool _debugModeEnabled =
    kDebugMode; // Nur im Debug-Modus aktiviert, in Release-Builds deaktiviert

// Logger für besseres Debugging
final _log = getLogger('PaymentService');

// Provider für den PaymentService
final paymentServiceProvider = Provider<PaymentService>((ref) {
  _log.i("paymentServiceProvider wird erstellt");
  final userProfileNotifier = ref.watch(userProfileProvider.notifier);

  // Wichtig: Der SupabaseSubscriptionService wird NICHT hier gelesen.
  // Er muss später über den Setter injiziert werden.

  final paymentService = PaymentService(
    InAppPurchase.instance,
    userProfileNotifier,
    // null, // Übergebe hier null, da es später gesetzt wird.
  );

  _log.i("paymentServiceProvider erstellt. Initialisierung läuft...");
  // Die Initialisierung wird im Konstruktor von PaymentService aufgerufen.

  // Optional: Den SupabaseSubscriptionService hier holen und setzen, wenn keine Zirkelbezüge bestehen
  // try {
  //   final subService = ref.read(supabaseSubscriptionServiceProvider);
  //   paymentService.setSubscriptionService(subService);
  // } catch (e) {
  //   _log.w("SupabaseSubscriptionService konnte beim Erstellen des PaymentServiceProviders nicht sofort injiziert werden: $e");
  // }

  return paymentService;
});

class PaymentService {
  final InAppPurchase _inAppPurchase;
  final UserProfileNotifier _userProfileNotifier;
  // SupabaseSubscriptionService wird optional gehalten und über Setter injiziert
  // SupabaseSubscriptionService? _subscriptionService;

  // Produkt-IDs für verschiedene Plattformen (innerhalb der Klasse zur Kapselung)
  // static const String _androidPremiumProductId = 'premium_monthly_subscription';
  // static const String _iosPremiumProductId = 'premium_monthly_subscription';

  // Debug-Modus für Testzwecke (innerhalb der Klasse)
  // static const bool _debugModeEnabled = false;

  // Logger-Instanz (kann innerhalb der Klasse bleiben oder global)
  // final _log = getLogger('PaymentService');

  // Verfügbare Produkte
  List<ProductDetails>? _availableProducts;
  ProductDetails? _premiumProduct;

  // Stream-Abonnement für Kaufupdates
  StreamSubscription<List<PurchaseDetails>>?
  _purchaseStreamSubscription; // Umbenannt zur Klarheit

  // Callback für Kaufprozess
  Completer<bool>? _purchaseCompleter; // Verwende Completer statt Callback

  bool _isInitialized = false; // Flag für Initialisierung
  bool _isAvailable = false; // Flag für IAP Verfügbarkeit

  PaymentService(
    this._inAppPurchase,
    this._userProfileNotifier,
    // this._subscriptionService, // Entfernt aus Konstruktor
  ) {
    _log.i("PaymentService Instanz wird erstellt.");
    _initializeService(); // Initialisierung starten
  }

  // Factory-Konstruktor wird nicht mehr benötigt, da Provider direkt die Klasse erstellt.
  // factory PaymentService.factory(...) { ... }

  // Setter für den Subscription-Service (wird vom Provider aufgerufen, falls nötig)
  // void setSubscriptionService(SupabaseSubscriptionService service) {
  //   _log.i("SupabaseSubscriptionService wird gesetzt.");
  //   _subscriptionService = service;
  // }

  /// Initialisiert den Payment Service nur einmal.
  Future<void> _initializeService() async {
    if (_isInitialized) return; // Verhindert mehrfache Initialisierung
    _isInitialized = true; // Setze Flag sofort
    _log.i('Initialisiere Payment Service...');

    _isAvailable = await _inAppPurchase.isAvailable();
    if (!_isAvailable) {
      _log.e('In-App-Kauf ist nicht verfügbar auf diesem Gerät');
      return;
    }
    _log.i("In-App Purchase ist verfügbar.");

    // Starte das Abhören von Kaufupdates
    _purchaseStreamSubscription = _inAppPurchase.purchaseStream.listen(
      _listenToPurchaseUpdated,
      onDone: () {
        _log.i("Purchase Stream wurde geschlossen.");
        _purchaseStreamSubscription?.cancel();
        _purchaseStreamSubscription = null;
      },
      onError: (error) {
        _log.e('Fehler im Kauf-Stream: $error');
        // Informiere ggf. den laufenden Kaufprozess über den Fehler
        if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
          _purchaseCompleter!.completeError(error);
          _purchaseCompleter = null;
        }
      },
    );
    _log.i("Purchase Stream Listener registriert.");

    // Lade die verfügbaren Produkte
    await _loadProducts();
    _log.i("Initialisierung abgeschlossen.");
  }

  /// Lädt die verfügbaren Premium-Produkte.
  Future<void> _loadProducts() async {
    _log.i('Lade verfügbare Produkte...');
    if (!_isAvailable) {
      // Prüfe Verfügbarkeit erneut
      _log.w("Kann Produkte nicht laden, da IAP nicht verfügbar ist.");
      return;
    }

    try {
      final productIds = <String>{
        _premiumProductId, // Verwende die plattformspezifische ID
      };
      _log.d("Frage Produktdetails für IDs ab: $productIds");

      final response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        _log.e('Fehler beim Laden der Produkte: ${response.error}');
        _availableProducts = []; // Setze leere Liste bei Fehler
        _premiumProduct = null;
        return;
      }

      if (response.productDetails.isEmpty) {
        _log.w('Keine Produkte für IDs $productIds gefunden.');
        _availableProducts = [];
        _premiumProduct = null;
        return;
      }

      _availableProducts = response.productDetails;
      _log.i(
        '${_availableProducts!.length} Produkte geladen: ${_availableProducts!.map((p) => p.id).join(', ')}',
      );

      // Speichere das Premium-Produkt
      _premiumProduct = _availableProducts!.firstWhere(
        (product) => product.id == _premiumProductId,
        // orElse: () => null, // Rückgabe null, wenn nicht gefunden
      );

      if (_premiumProduct != null) {
        _log.i(
          'Premium-Produkt gefunden: ${_premiumProduct!.title} (${_premiumProduct!.id})',
        );
      } else {
        _log.w(
          "Premium-Produkt mit ID '$_premiumProductId' nicht unter den geladenen Produkten.",
        );
      }
    } catch (e, stack) {
      _log.e('Schwerer Fehler beim Laden der Produkte: $e', stackTrace: stack);
      _availableProducts = [];
      _premiumProduct = null;
    }
  }

  /// Kauft ein Premium-Abonnement.
  Future<bool> buyPremiumSubscription({String? planType}) async {
    _log.i('Starte Premium-Kauf-Prozess für Plan: $planType...');

    // Stelle sicher, dass initialisiert wurde
    if (!_isInitialized) await _initializeService();

    if (_debugModeEnabled) {
      _log.i('DEBUG-MODUS: Simuliere erfolgreichen Kauf.');
      _log.w(
        'HINWEIS: In der Release-Version wird ein echtes Zahlungsverfahren benötigt.',
      );
      return await _simulateSuccessfulPurchase(planType: planType);
    }

    if (!_isAvailable) {
      _log.e('Kauf nicht möglich: IAP nicht verfügbar.');
      return false;
    }

    // Produkte laden, falls noch nicht geschehen oder fehlgeschlagen
    if (_premiumProduct == null) {
      _log.w("Premium-Produkt nicht geladen, versuche erneutes Laden...");
      await _loadProducts();
    }

    // Immer noch keine Produkte?
    if (_premiumProduct == null) {
      _log.e(
        'Kauf nicht möglich: Premium-Produkt konnte nicht geladen oder gefunden werden.',
      );
      return false;
    }

    _log.i('Versuche Produkt zu kaufen: ${_premiumProduct!.id}');

    // Verhindere parallele Käufe
    if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
      _log.w("Ein Kaufprozess läuft bereits.");
      return await _purchaseCompleter!
          .future; // Warte auf den laufenden Prozess
    }

    _purchaseCompleter = Completer<bool>(); // Neuen Completer erstellen

    try {
      // Starte den Kaufprozess
      final purchaseParam = PurchaseParam(productDetails: _premiumProduct!);

      // Für Abonnements verwenden wir die spezielle Methode für Abonnements
      _log.d("Rufe _inAppPurchase.buyNonConsumable auf...");

      // Für Android verwenden wir die Methode für Abonnements
      if (Platform.isAndroid) {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        // Für iOS verwenden wir die Methode für Abonnements
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }

      _log.d("Kauf-Methode aufgerufen, warte auf Stream-Antwort...");

      // Warte auf das Ergebnis des Kaufs via Stream (max. 2 Minuten)
      final result = await _purchaseCompleter!.future.timeout(
        const Duration(minutes: 2),
        onTimeout: () {
          _log.e('Kaufprozess-Timeout nach 2 Minuten.');
          if (!_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
          _purchaseCompleter = null; // Reset Completer nach Timeout
          return false;
        },
      );
      _purchaseCompleter = null; // Reset Completer nach Abschluss
      return result;
    } catch (e, stack) {
      _log.e('Fehler beim Starten des Kaufs: $e', stackTrace: stack);
      if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
        _purchaseCompleter!.complete(false); // Schließe als fehlgeschlagen ab
      }
      _purchaseCompleter = null; // Reset Completer bei Fehler
      return false;
    }
  }

  /// Simuliert einen erfolgreichen Kauf (nur für Entwicklung).
  Future<bool> _simulateSuccessfulPurchase({String? planType}) async {
    // Sicherheitscheck: Nur im Debug-Modus erlauben
    if (!kDebugMode) {
      _log.e(
        'SICHERHEITSWARNUNG: Simulierter Kauf wurde im Release-Modus versucht!',
      );
      return false;
    }

    final selectedPlanType = planType?.toLowerCase() ?? 'basic';
    _log.i(
      'Simuliere Premium-Kauf für Plan: $selectedPlanType... (NUR FÜR ENTWICKLUNG)',
    );
    _log.w(
      'WARNUNG: Diese Funktion ist nur für Entwicklungszwecke gedacht und sollte in Produktionsumgebungen nicht verfügbar sein.',
    );
    await Future.delayed(
      const Duration(seconds: 2),
    ); // Simuliere Netzwerklatenz

    try {
      // Setze Premium-Status ohne echten Kauf
      final now = DateTime.now();

      // Bestimme die Laufzeit basierend auf dem Plantyp
      Duration duration;
      switch (selectedPlanType) {
        case 'basic':
          duration = const Duration(days: 30); // 1 Monat
          break;
        case 'pro':
          duration = const Duration(days: 30); // 1 Monat
          break;
        case 'unlimited':
          duration = const Duration(days: 30); // 1 Monat
          break;
        default:
          duration = const Duration(days: 30); // Standardmäßig 1 Monat
      }

      final expiryDate = now.add(duration);

      // Formatiere das Datum im richtigen Format
      final formattedExpiryDate = DateTime(
        expiryDate.year,
        expiryDate.month,
        expiryDate.day,
        0,
        0,
        0,
        0,
        0,
      );

      _log.d(
        "Aktualisiere Premium-Status im UserProfileNotifier (simuliert)...",
      );
      _log.d("Ablaufdatum: ${formattedExpiryDate.toIso8601String()}");

      // Aktualisiere auch die Supabase-Datenbank
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null) {
        try {
          // Erstelle ein neues Abonnement in der Datenbank
          await Supabase.instance.client.from('subscriptions').upsert({
            'user_id': currentUser.id,
            'subscription_type': 'monthly',
            'plan_type': selectedPlanType,
            'payment_provider': 'google_play',
            'transaction_id': 'sim_${DateTime.now().millisecondsSinceEpoch}',
            'is_premium':
                selectedPlanType == 'premium' ||
                selectedPlanType == 'unlimited',
            'auto_renew': true,
            'status': 'active',
            'start_date': now.toIso8601String(),
            'end_date': formattedExpiryDate.toIso8601String(),
            'created_at': now.toIso8601String(),
            'updated_at': now.toIso8601String(),
          });
          _log.i('Simuliertes Abonnement in Supabase erstellt');
        } catch (e) {
          _log.e(
            'Fehler beim Erstellen des simulierten Abonnements in Supabase: $e',
          );
        }
      }

      // Aktualisiere das UserProfile
      final isPremium =
          selectedPlanType == 'premium' || selectedPlanType == 'unlimited';
      await _userProfileNotifier.updatePremiumStatus(
        isPremium: isPremium,
        premiumExpiryDate: formattedExpiryDate,
        planType: selectedPlanType,
      );

      _log.i('Simulierter Kauf erfolgreich abgeschlossen.');
      return true;
    } catch (e, stack) {
      _log.e('Fehler bei simuliertem Kauf: $e', stackTrace: stack);
      return false;
    }
  }

  /// Wiederherstellung früherer Käufe.
  Future<bool> restorePurchases() async {
    _log.i('Versuche Käufe wiederherzustellen...');
    if (!_isAvailable) {
      _log.e("Kann Käufe nicht wiederherstellen, IAP nicht verfügbar.");
      return false;
    }
    try {
      await _inAppPurchase.restorePurchases();
      _log.i(
        "Wiederherstellungsanfrage gesendet. Ergebnis kommt über den Stream.",
      );
      // Der Erfolg wird über den _listenToPurchaseUpdated Stream signalisiert.
      // Wir können hier nicht direkt auf Erfolg warten.
      return true; // Signalisiert, dass der Versuch gestartet wurde.
    } catch (e, stack) {
      _log.e(
        "Fehler beim Starten der Wiederherstellung: $e",
        stackTrace: stack,
      );
      return false;
    }
  }

  /// Verarbeitet Kauf-Updates aus dem Stream.
  Future<void> _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    _log.i(
      "Kauf-Update erhalten. Anzahl Details: ${purchaseDetailsList.length}",
    );

    // Verwende for-Schleife statt forEach für bessere Lesbarkeit und Linting-Kompatibilität
    for (final purchaseDetails in purchaseDetailsList) {
      _log.d(
        "Verarbeite Kauf-Detail: ID=${purchaseDetails.purchaseID}, Produkt=${purchaseDetails.productID}, Status=${purchaseDetails.status}, Error=${purchaseDetails.error}",
      );

      if (purchaseDetails.status == PurchaseStatus.pending) {
        _log.i("Kauf für ${purchaseDetails.productID} ist ausstehend...");
        // Zeige ggf. eine Ladeanzeige an
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          _log.e(
            "Kauffehler: ${purchaseDetails.error?.message} (Code: ${purchaseDetails.error?.code})",
          );
          // Informiere den laufenden Kaufprozess über den Fehler
          if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
          // Zeige Fehlermeldung an
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          _log.i(
            "Kauf/Wiederherstellung erfolgreich für ${purchaseDetails.productID}. Status: ${purchaseDetails.status}",
          );

          // 1. Verifiziere den Kauf (Client- oder Serverseitig)
          bool isValid = await _verifyPurchase(purchaseDetails);
          _log.d("Kauf verifiziert: $isValid");

          if (isValid) {
            _log.i("Kauf gültig. Gewähre Premium-Zugang.");
            // 2. Gewähre Premium-Zugang (Update UserProfile)
            try {
              final expiryDate = _calculateExpiryDate(
                purchaseDetails,
              ); // Berechne Ablaufdatum (Beispiel)
              await _userProfileNotifier.updatePremiumStatus(
                isPremium: true,
                premiumExpiryDate: expiryDate,
                // Optional: Speichere Transaktionsdetails
                transactionId: purchaseDetails.purchaseID,
                purchaseDate:
                    purchaseDetails.transactionDate != null
                        ? DateTime.fromMillisecondsSinceEpoch(
                          int.parse(purchaseDetails.transactionDate!),
                        )
                        : DateTime.now(),
              );
              _log.i("Premium-Status erfolgreich im UserProfile aktualisiert.");
              // Informiere den laufenden Kaufprozess über den Erfolg
              if (_purchaseCompleter != null &&
                  !_purchaseCompleter!.isCompleted) {
                _purchaseCompleter!.complete(true);
              }
            } catch (e, stack) {
              _log.e(
                "Fehler beim Aktualisieren des Premium-Status nach Kauf: $e",
                stackTrace: stack,
              );
              // Informiere den laufenden Kaufprozess über den Fehler
              if (_purchaseCompleter != null &&
                  !_purchaseCompleter!.isCompleted) {
                _purchaseCompleter!.complete(false);
              }
            }
          } else {
            _log.w(
              "Kaufverifizierung fehlgeschlagen für ${purchaseDetails.purchaseID}.",
            );
            // Informiere den laufenden Kaufprozess über den Fehler
            if (_purchaseCompleter != null &&
                !_purchaseCompleter!.isCompleted) {
              _purchaseCompleter!.complete(false);
            }
          }

          // 3. Schließe den Kauf ab (wichtig!)
          if (purchaseDetails.pendingCompletePurchase) {
            _log.d("Schließe Kauf ${purchaseDetails.purchaseID} ab...");
            try {
              await _inAppPurchase.completePurchase(purchaseDetails);
              _log.i(
                "Kauf ${purchaseDetails.purchaseID} erfolgreich abgeschlossen.",
              );
            } catch (e, stack) {
              _log.e(
                "Fehler beim Abschließen des Kaufs ${purchaseDetails.purchaseID}: $e",
                stackTrace: stack,
              );
            }
          }
        } else if (purchaseDetails.status == PurchaseStatus.canceled) {
          _log.i("Kauf für ${purchaseDetails.productID} wurde abgebrochen.");
          // Informiere den laufenden Kaufprozess über den Abbruch
          if (_purchaseCompleter != null && !_purchaseCompleter!.isCompleted) {
            _purchaseCompleter!.complete(false);
          }
        }

        // Setze den Completer zurück, wenn er nicht mehr benötigt wird
        // Überprüfung hinzugefügt, ob der Completer für diesen spezifischen Kauf war (optional, aber sicherer)
        if (_purchaseCompleter != null &&
            purchaseDetails.purchaseID ==
                _purchaseCompleter?.hashCode.toString()) {
          // Beispielhafte Verknüpfung
          // _purchaseCompleter = null;
        }
      }
    }
  }

  /// Verifiziert einen Kauf (Beispielimplementierung - **MUSS** serverseitig erfolgen!).
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    _log.i("Verifiziere Kauf: ${purchaseDetails.purchaseID}");
    // ***** WICHTIGE SICHERHEITSHINWEIS *****
    // Die Verifizierung MUSS auf deinem Server stattfinden!
    // Sende purchaseDetails.verificationData an deinen Server.
    // Dein Server prüft dann die Signatur/den Beleg bei Google/Apple.
    // Gib nur true zurück, wenn dein Server die Gültigkeit bestätigt.
    // Die folgende Implementierung ist NUR für Tests und UNSICHER!
    _log.w(
      "WARNUNG: Clientseitige Kaufverifizierung ist UNSICHER und nur für Tests!",
    );
    if (kDebugMode || _debugModeEnabled) {
      // Im Debug-Modus einfach true zurückgeben
      _log.d("Debug-Modus: Kauf als gültig angenommen.");
      return true;
    } else {
      // In Produktion IMMER false zurückgeben, da Serverprüfung fehlt!
      _log.e(
        "FEHLER: Serverseitige Kaufverifizierung ist nicht implementiert!",
      );
      return false;
    }

    // Beispiel für Daten, die zum Server gesendet werden müssten:
    // final verificationData = purchaseDetails.verificationData;
    // final serverVerificationResult = await myBackend.verifyPurchase(verificationData);
    // return serverVerificationResult;
  }

  /// Berechnet das Ablaufdatum (Beispiel - abhängig vom Produkt).
  DateTime? _calculateExpiryDate(PurchaseDetails purchaseDetails) {
    // Diese Logik hängt stark vom gekauften Produkt ab (Abo-Dauer etc.)
    // Hier nur ein Beispiel: 30 Tage ab Kaufdatum
    if (purchaseDetails.transactionDate != null) {
      final purchaseTime = DateTime.fromMillisecondsSinceEpoch(
        int.parse(purchaseDetails.transactionDate!),
      );
      return purchaseTime.add(const Duration(days: 30));
    }
    return DateTime.now().add(const Duration(days: 30)); // Fallback
  }

  /// Gibt den Payment Service frei und hebt Abonnements auf.
  void dispose() {
    _log.i("Disposing Payment Service...");
    _purchaseStreamSubscription?.cancel();
    _purchaseStreamSubscription = null;
    // Apple-spezifische Bereinigung (falls verwendet)
    if (Platform.isIOS) {
      // var iosPlatformAddition = _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      // iosPlatformAddition.setDelegate(null);
    }
    _log.i("Payment Service disposed.");
  }
}

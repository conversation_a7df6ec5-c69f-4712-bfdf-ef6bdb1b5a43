
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service für die In-App-Authentifizierung mit verschiedenen Providern
class InAppAuthService {
  final SupabaseClient _supabase;
  final _log = getLogger('InAppAuthService');

  InAppAuthService(this._supabase);

  /// Führt die Google-Anmeldung direkt in der App durch
  Future<AuthResponse?> signInWithGoogleInApp(BuildContext context) async {
    try {
      _log.i('<PERSON>e Google Sign-In in der App...');

      // Prüfe, ob wir auf dem Web sind
      if (kIsWeb) {
        // Für Web verwenden wir den Standard-OAuth-Flow
        _log.i('Web-Plattform erkannt, verwende OAuth-Flow...');

        // URL für den OAuth-Flow generieren
        final String authUrl = await _generateOAuthUrl(OAuthProvider.google);

        // In-App-Browser öffnen
        _log.i(
          'Öffne Google OAuth-URL im Browser mit callbackUrlScheme: com.einsteinai.app',
        );
        final result = await FlutterWebAuth2.authenticate(
          url: authUrl,
          callbackUrlScheme: 'com.einsteinai.app',
        );

        // Extrahiere den Authentifizierungscode aus der Callback-URL
        final Uri resultUri = Uri.parse(result);
        final String? code = resultUri.queryParameters['code'];

        if (code == null) {
          _log.w('Kein Authentifizierungscode in der Callback-URL gefunden');
          return null;
        }

        // Tausche den Code gegen eine Session aus
        await _supabase.auth.exchangeCodeForSession(code);

        // Hole die aktuelle Session nach dem Austausch
        final currentSession = _supabase.auth.currentSession;
        if (currentSession == null) {
          _log.w('Keine Session nach Code-Austausch gefunden');
          return null;
        }

        // Erstelle eine AuthResponse mit der aktuellen Session
        final AuthResponse response = AuthResponse(
          session: currentSession,
          user: currentSession.user,
        );

        _log.i('Google Sign-In erfolgreich: ${response.user?.email}');

        // Markiere den Benutzer als bestehenden Account
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_existing_account', true);

        return response;
      } else {
        // Für mobile Plattformen verwenden wir das Google Sign-In Plugin
        _log.i('Mobile Plattform erkannt, verwende Google Sign-In Plugin...');

        final GoogleSignIn googleSignIn = GoogleSignIn(
          scopes: ['email', 'profile'],
          // Deaktiviere die automatische Auswahl des letzten Accounts
          signInOption: SignInOption.standard,
          // Erzwinge die Anzeige des Account-Auswahldialogs
          forceCodeForRefreshToken: true,
        );

        final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
        if (googleUser == null) {
          _log.w('Google Sign-In wurde vom Benutzer abgebrochen');
          return null;
        }

        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;

        final String? accessToken = googleAuth.accessToken;
        final String? idToken = googleAuth.idToken;

        if (accessToken == null) {
          _log.e('Kein Access Token von Google erhalten');
          return null;
        }

        // Authentifiziere mit Supabase
        final AuthResponse response = await _supabase.auth.signInWithIdToken(
          provider: OAuthProvider.google,
          idToken: idToken!,
          accessToken: accessToken,
        );

        _log.i('Google Sign-In erfolgreich: ${response.user?.email}');

        // Markiere den Benutzer als bestehenden Account
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_existing_account', true);

        return response;
      }
    } catch (e) {
      _log.e('Fehler bei Google Sign-In: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler bei der Google-Anmeldung: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  /// Führt die OAuth-Anmeldung mit einem bestimmten Provider durch
  Future<AuthResponse?> signInWithProvider(
    BuildContext context,
    OAuthProvider provider,
  ) async {
    try {
      _log.i('Starte OAuth Sign-In mit Provider: ${provider.toString()}');

      // Für OAuth-Provider auf Web verwenden wir den Standard-OAuth-Flow
      if (kIsWeb) {
        _log.i('Web-Plattform erkannt, verwende OAuth-Flow...');

        // URL für den OAuth-Flow generieren
        final String authUrl = await _generateOAuthUrl(provider);

        // In-App-Browser öffnen
        _log.i(
          'Öffne OAuth-URL im Browser mit callbackUrlScheme: com.einsteinai.app',
        );
        final result = await FlutterWebAuth2.authenticate(
          url: authUrl,
          callbackUrlScheme: 'com.einsteinai.app',
        );

        // Extrahiere den Authentifizierungscode aus der Callback-URL
        final Uri resultUri = Uri.parse(result);
        final String? code = resultUri.queryParameters['code'];

        if (code == null) {
          _log.w('Kein Authentifizierungscode in der Callback-URL gefunden');
          return null;
        }

        // Tausche den Code gegen eine Session aus
        await _supabase.auth.exchangeCodeForSession(code);

        // Hole die aktuelle Session nach dem Austausch
        final currentSession = _supabase.auth.currentSession;
        if (currentSession == null) {
          _log.w('Keine Session nach Code-Austausch gefunden');
          return null;
        }

        // Erstelle eine AuthResponse mit der aktuellen Session
        final AuthResponse response = AuthResponse(
          session: currentSession,
          user: currentSession.user,
        );

        _log.i('OAuth Sign-In erfolgreich: ${response.user?.email}');

        // Markiere den Benutzer als bestehenden Account
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_existing_account', true);

        return response;
      } else {
        // Für mobile Plattformen verwenden wir den nativen OAuth-Flow
        _log.i('Mobile Plattform erkannt, verwende nativen OAuth-Flow...');

        final bool success = await _supabase.auth.signInWithOAuth(
          provider,
          redirectTo: 'com.einsteinai.app://callback',
        );

        if (success) {
          // Warte kurz und hole dann die Session
          await Future.delayed(const Duration(seconds: 1));
          final currentSession = _supabase.auth.currentSession;
          
          if (currentSession != null) {
            final AuthResponse response = AuthResponse(
              session: currentSession,
              user: currentSession.user,
            );

            _log.i('OAuth Sign-In erfolgreich: ${response.user?.email}');

            // Markiere den Benutzer als bestehenden Account
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('has_existing_account', true);

            return response;
          }
        }

        _log.w('OAuth Sign-In fehlgeschlagen oder keine Session erhalten');
        return null;
      }
    } catch (e) {
      _log.e('Fehler bei OAuth Sign-In: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler bei der OAuth-Anmeldung: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  /// Generiert eine OAuth-URL für den angegebenen Provider
  Future<String> _generateOAuthUrl(OAuthProvider provider) async {
    final String providerStr = provider.toString().split('.').last;
    final String redirectUrl = Uri.encodeComponent('com.einsteinai.app://callback');

    // Supabase-URL und Anon-Key aus der Instanz abrufen
    final String supabaseUrl = 'https://vpttdxibvjrfjzbtktqg.supabase.co';
    final String supabaseAnonKey =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WRijbwvWdLPVo0fhk_G9ppQR0nznCTZf7BvFDF55psg';

    _log.i(
      'Generiere OAuth-URL für $providerStr mit Redirect-URL: $redirectUrl',
    );

    // OAuth-URL generieren
    return '$supabaseUrl/auth/v1/authorize?provider=$providerStr&redirect_to=$redirectUrl&scopes=email,profile&response_type=code&client_id=$supabaseAnonKey';
  }
}

/// Provider für den InAppAuthService
final inAppAuthServiceProvider = Provider<InAppAuthService>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  return InAppAuthService(supabaseClient);
});

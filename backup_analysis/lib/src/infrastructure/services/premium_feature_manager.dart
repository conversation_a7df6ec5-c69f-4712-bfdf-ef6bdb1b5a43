import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/ad_service.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';
import 'package:ki_test/src/presentation/common/widgets/premium_dialog.dart';

/// Definiert die verfügbaren Premium-Features in der App
enum PremiumFeature {
  /// KI-Anschreiben-Generator
  aiCoverLetter,

  /// KI-optimierte Jobsuche
  aiJobSearch,

  /// Unbegrenztes Speichern von Favoriten
  unlimitedFavorites,

  /// Werbungsfreie Erfahrung
  adFree,

  /// Zugriff auf Premium-Insights und Statistiken
  insights,

  /// Übersetzungsfunktion
  translation,
}

/// Service zur Verwaltung von Premium-Features
class PremiumFeatureManager {
  final SubscriptionManagementService _subscriptionService;
  final AdService _adService;
  final Ref _ref;
  final _log = getLogger('PremiumFeatureManager');

  PremiumFeatureManager(this._subscriptionService, this._adService, this._ref);

  /// Prüft, ob der Benutzer Zugriff auf ein Premium-Feature hat
  Future<bool> hasAccessTo(PremiumFeature feature) async {
    try {
      // Prüfe, ob der Benutzer Premium ist
      final isPremium = await _subscriptionService.hasPremiumSubscription();

      if (isPremium) {
        // Premium-Nutzer haben Zugriff auf alle Features
        return true;
      }

      // Basic-Nutzer haben keinen Zugriff auf Premium-Features
      return false;
    } catch (e) {
      _log.e('Fehler beim Prüfen des Zugriffs auf Premium-Feature', error: e);
      return false;
    }
  }

  /// Zeigt einen Dialog an, wenn der Benutzer versucht, ein Premium-Feature zu nutzen
  Future<bool> showPremiumDialogIfNeeded(
    BuildContext context,
    PremiumFeature feature, {
    String? title,
    String? message,
  }) async {
    try {
      // Prüfe, ob der Benutzer Zugriff auf das Feature hat
      final hasAccess = await hasAccessTo(feature);

      if (hasAccess) {
        // Benutzer hat Zugriff, kein Dialog nötig
        return true;
      }

      // Benutzer hat keinen Zugriff, zeige Dialog
      final result = await PremiumDialog.show(
        context,
        title: title ?? _getTitleForFeature(feature),
        message: message ?? _getMessageForFeature(feature),
      );

      if (result == 'ad') {
        // Benutzer hat sich für Werbung entschieden
        return true;
      } else if (result == 'premium') {
        // Benutzer hat sich für Premium entschieden
        return false;
      } else {
        // Benutzer hat abgebrochen
        return false;
      }
    } catch (e) {
      _log.e('Fehler beim Anzeigen des Premium-Dialogs', error: e);
      return false;
    }
  }

  /// Gibt den Titel für ein Premium-Feature zurück
  String _getTitleForFeature(PremiumFeature feature) {
    switch (feature) {
      case PremiumFeature.aiCoverLetter:
        return 'KI-Anschreiben-Generator';
      case PremiumFeature.aiJobSearch:
        return 'KI-optimierte Jobsuche';
      case PremiumFeature.unlimitedFavorites:
        return 'Unbegrenzte Favoriten';
      case PremiumFeature.adFree:
        return 'Werbefreie Erfahrung';
      case PremiumFeature.insights:
        return 'Premium-Insights';
      case PremiumFeature.translation:
        return 'Übersetzungsfunktion';
      default:
        return 'Premium-Funktion';
    }
  }

  /// Gibt die Nachricht für ein Premium-Feature zurück
  String _getMessageForFeature(PremiumFeature feature) {
    switch (feature) {
      case PremiumFeature.aiCoverLetter:
        return 'Der KI-Anschreiben-Generator ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um ihn einmalig zu nutzen?';
      case PremiumFeature.aiJobSearch:
        return 'Die KI-optimierte Jobsuche ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?';
      case PremiumFeature.unlimitedFavorites:
        return 'Unbegrenzte Favoriten sind für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?';
      case PremiumFeature.adFree:
        return 'Eine werbefreie Erfahrung ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben?';
      case PremiumFeature.insights:
        return 'Premium-Insights sind für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?';
      case PremiumFeature.translation:
        return 'Die Übersetzungsfunktion ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?';
      default:
        return 'Diese Funktion ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?';
    }
  }
}

import 'dart:developer' as dev;

import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../src/application/providers/user_profile_provider.dart';
import '../src/domain/models/subscription.dart';

/// Service zur Verwaltung von Abonnements über Supabase Edge Functions
class SupabaseSubscriptionService {
  final SupabaseClient _supabaseClient;
  final UserProfileNotifier _userProfileNotifier;

  /// Pfad zur Edge-Funktion für Abonnement-Management
  static const String _subscriptionFunctionPath = 'manage-subscription';
  
  /// Tabelle für Abonnements in Supabase
  static const String _subscriptionsTable = 'subscriptions';

  SupabaseSubscriptionService(this._supabaseClient, this._userProfileNotifier);

  /// Log-Funktion für bessere Fehlerverfolgung
  void _log(String message, {bool isError = false}) {
    if (kDebugMode) {
      if (isError) {
        dev.log('[SupabaseSubscriptionService ERROR] $message', name: 'SubscriptionService');
      } else {
        dev.log('[SupabaseSubscriptionService] $message', name: 'SubscriptionService');
      }
    }
  }

  /// Überprüft einen Kauf und aktualisiert das Abonnement in Supabase
  /// 
  /// [receipt] - Die Kaufquittung oder Token vom Store
  /// [platform] - 'ios', 'android' oder andere Plattform
  /// [productId] - Die ID des gekauften Produkts
  Future<bool> verifyPurchase({
    required String receipt, 
    required String platform, 
    required String productId
  }) async {
    try {
      // Prüfen, ob der Nutzer angemeldet ist
      final currentUser = _supabaseClient.auth.currentUser;
      if (currentUser == null) {
        _log('Benutzer nicht angemeldet', isError: true);
        return false;
      }

      // Kaufdaten vorbereiten
      final data = {
        'action': 'verify_purchase',
        'receipt': receipt,
        'platform': platform,
        'product_id': productId,
        'user_id': currentUser.id,
      };

      // Edge Function aufrufen
      final response = await _callSubscriptionFunction(data);
      if (response == null) return false;

      // Antwort auswerten
      final verified = response['verified'] ?? false;
      
      if (verified) {
        // Wenn der Kauf verifiziert wurde, den Premium-Status aktualisieren
        _log('Kauf erfolgreich verifiziert, aktualisiere Premium-Status');
        
        // Ablaufdatum aus der Antwort extrahieren
        final expiresAtStr = response['expires_at'] as String?;
        final DateTime expiresAt = expiresAtStr != null 
            ? DateTime.parse(expiresAtStr) 
            : DateTime.now().add(const Duration(days: 30)); // Fallback

        // Benutzerprofil aktualisieren
        await _userProfileNotifier.updatePremiumStatus(
          isPremium: true, 
          premiumExpiryDate: expiresAt,
        );
        return true;
      } else {
        _log('Kauf konnte nicht verifiziert werden', isError: true);
        return false;
      }
    } catch (e, stackTrace) {
      _log('Fehler bei Kaufverifizierung: $e\n$stackTrace', isError: true);
      return false;
    }
  }

  /// Überprüft, ob der aktuelle Benutzer ein aktives Abonnement hat
  Future<SubscriptionCheck> checkSubscription() async {
    try {
      final currentUser = _supabaseClient.auth.currentUser;
      if (currentUser == null) {
        _log('Benutzer nicht angemeldet', isError: true);
        return SubscriptionCheck(hasSubscription: false);
      }

      final data = {
        'action': 'check_subscription',
        'user_id': currentUser.id,
      };

      final response = await _callSubscriptionFunction(data);
      if (response == null) {
        return SubscriptionCheck(hasSubscription: false);
      }

      // Prüfen, ob ein aktives Abonnement existiert
      final hasSubscription = response['has_subscription'] ?? false;
      
      // Wenn ein Abonnement existiert, die Daten extrahieren
      Subscription? subscription;
      if (response['subscription'] != null) {
        subscription = Subscription.fromJson(response['subscription']);
      }

      // Premium-Status im Benutzerprofil aktualisieren, wenn nötig
      if (hasSubscription && subscription != null) {
        await _userProfileNotifier.updatePremiumStatus(
          isPremium: true, 
          premiumExpiryDate: subscription.expiresAt,
        );
      } else if (!hasSubscription) {
        await _userProfileNotifier.updatePremiumStatus(
          isPremium: false, 
          premiumExpiryDate: null,
        );
      }

      return SubscriptionCheck(
        hasSubscription: hasSubscription,
        subscription: subscription,
      );
    } catch (e, stackTrace) {
      _log('Fehler bei Abonnementprüfung: $e\n$stackTrace', isError: true);
      return SubscriptionCheck(hasSubscription: false);
    }
  }

  /// Erstellt ein neues Abonnement für den aktuellen Benutzer
  Future<Subscription?> createSubscription({
    required String productId,
    required String platform,
    required DateTime expiresAt,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        _log('Abonnement kann nicht erstellt werden: Kein angemeldeter Benutzer', isError: true);
        return null;
      }

      // Prüfen, ob bereits ein Abonnement existiert
      final existingSubscription = await getCurrentSubscription();
      
      if (existingSubscription != null) {
        // Bestehendes Abonnement aktualisieren
        return await updateSubscription(
          subscriptionId: existingSubscription.id,
          productId: productId,
          platform: platform,
          expiresAt: expiresAt,
        );
      }
      
      // Startdatum ist jetzt
      final now = DateTime.now();
      
      // Neues Abonnement-Modell erstellen
      final subscriptionData = {
        'user_id': userId,
        'status': 'active',
        'product_id': productId,
        'platform': platform,
        'expires_at': expiresAt.toIso8601String(),
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      // In Supabase speichern
      final response = await _supabaseClient
          .from(_subscriptionsTable)
          .insert(subscriptionData)
          .select()
          .single();

      // Subscription-Modell aus der Antwort erstellen
      final subscription = Subscription.fromJson(response);
      
      // UserProfile aktualisieren
      await _userProfileNotifier.updatePremiumStatus(
        isPremium: true,
        premiumExpiryDate: subscription.expiresAt,
      );
      
      _log('Abonnement erfolgreich erstellt: ${subscription.id}, läuft ab am ${DateFormat('dd.MM.yyyy').format(expiresAt)}');
      return subscription;
    } catch (e, stackTrace) {
      _log('Fehler beim Erstellen des Abonnements: $e\n$stackTrace', isError: true);
      return null;
    }
  }

  /// Holt das aktuelle Abonnement des angemeldeten Benutzers
  Future<Subscription?> getCurrentSubscription() async {
    try {
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        _log('Abonnement kann nicht abgerufen werden: Kein angemeldeter Benutzer', isError: true);
        return null;
      }

      // Aktives Abonnement suchen (sortiert nach Erstellungsdatum, neuestes zuerst)
      final response = await _supabaseClient
          .from(_subscriptionsTable)
          .select()
          .eq('user_id', userId)
          .eq('status', 'active')
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();

      if (response == null) {
        _log('Kein aktives Abonnement gefunden für Benutzer $userId');
        return null;
      }

      final subscription = Subscription.fromJson(response);
      
      // Prüfen, ob das Abonnement abgelaufen ist und ggf. aktualisieren
      if (subscription.expiresAt != null && DateTime.now().isAfter(subscription.expiresAt!)) {
        _log('Abonnement ist abgelaufen, aktualisiere Status...');
        await expireSubscription(subscriptionId: subscription.id);
        return null;
      }
      
      return subscription;
    } catch (e, stackTrace) {
      _log('Fehler beim Abrufen des aktuellen Abonnements: $e\n$stackTrace', isError: true);
      return null;
    }
  }

  /// Aktualisiert ein bestehendes Abonnement
  Future<Subscription?> updateSubscription({
    required String subscriptionId,
    String? productId,
    String? platform,
    DateTime? expiresAt,
    String? status,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        _log('Abonnement kann nicht aktualisiert werden: Kein angemeldeter Benutzer', isError: true);
        return null;
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      if (productId != null) updateData['product_id'] = productId;
      if (platform != null) updateData['platform'] = platform;
      if (expiresAt != null) updateData['expires_at'] = expiresAt.toIso8601String();
      if (status != null) updateData['status'] = status;

      // In Supabase aktualisieren
      final response = await _supabaseClient
          .from(_subscriptionsTable)
          .update(updateData)
          .eq('id', subscriptionId)
          .eq('user_id', userId)
          .select()
          .single();

      // Subscription-Modell aus der Antwort erstellen
      final subscription = Subscription.fromJson(response);
      
      // UserProfile aktualisieren, wenn der Status oder das Ablaufdatum geändert wurde
      final isPremiumActive = subscription.status == 'active';
      
      await _userProfileNotifier.updatePremiumStatus(
        isPremium: isPremiumActive,
        premiumExpiryDate: isPremiumActive ? subscription.expiresAt : null,
      );
      
      if (expiresAt != null) {
        _log('Abonnement aktualisiert, neues Ablaufdatum: ${DateFormat('dd.MM.yyyy').format(expiresAt)}');
      } else {
        _log('Abonnement aktualisiert: ${subscription.id}');
      }
      
      return subscription;
    } catch (e, stackTrace) {
      _log('Fehler beim Aktualisieren des Abonnements: $e\n$stackTrace', isError: true);
      return null;
    }
  }

  /// Kündigt das aktive Abonnement des Benutzers
  Future<bool> cancelSubscription() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        _log('Kein aktives Abonnement zum Kündigen gefunden', isError: true);
        return false;
      }

      // Abonnement aktualisieren: Status auf gekündigt setzen
      final updatedSubscription = await updateSubscription(
        subscriptionId: subscription.id,
        status: 'cancelled',
      );
      
      if (updatedSubscription != null) {
        _log('Abonnement erfolgreich gekündigt: ${subscription.id}');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      _log('Fehler beim Kündigen des Abonnements: $e\n$stackTrace', isError: true);
      return false;
    }
  }

  /// Markiert ein Abonnement als abgelaufen
  Future<bool> expireSubscription({required String subscriptionId}) async {
    try {
      // Abonnement aktualisieren: Status auf abgelaufen setzen
      final updatedSubscription = await updateSubscription(
        subscriptionId: subscriptionId,
        status: 'expired',
      );
      
      if (updatedSubscription != null) {
        _log('Abonnement als abgelaufen markiert: $subscriptionId');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      _log('Fehler beim Markieren des Abonnements als abgelaufen: $e\n$stackTrace', isError: true);
      return false;
    }
  }

  /// Hilfsmethode zum Aufrufen der Supabase Edge Function
  Future<Map<String, dynamic>?> _callSubscriptionFunction(Map<String, dynamic> data) async {
    try {
      _log('Rufe Edge Function auf: ${data['action']}');
      
      final response = await _supabaseClient.functions.invoke(
        _subscriptionFunctionPath,
        body: data,
      );

      if (response.status != 200) {
        _log('Edge Function Fehler: Status ${response.status}', isError: true);
        return null;
      }

      final responseData = response.data as Map<String, dynamic>?;
      if (responseData == null) {
        _log('Leere Antwort von Edge Function', isError: true);
        return null;
      }

      _log('Edge Function Antwort erhalten: ${responseData['message'] ?? 'OK'}');
      return responseData;
    } catch (e, stackTrace) {
      _log('Edge Function Aufruf fehlgeschlagen: $e\n$stackTrace', isError: true);
      return null;
    }
  }
}

/// Hilfsklasse zur Kapselung der Ergebnisse einer Abonnementprüfung
class SubscriptionCheck {
  final bool hasSubscription;
  final Subscription? subscription;

  SubscriptionCheck({
    required this.hasSubscription,
    this.subscription,
  });
} 
 
 
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'job_analysis_service.dart';

/// Provider für den JobAnalysisService
final jobAnalysisServiceProvider = Provider<JobAnalysisService>((ref) {
  // FirebaseFunctions Instanz mit europäischer Region
  final functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  return JobAnalysisService(functions: functions);
}); 
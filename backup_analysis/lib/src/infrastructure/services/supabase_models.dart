// Definitionen für Supabase Service Modelle

// Ergebnisklasse für die Anschreiben-Generierung
class GenerateCoverLetterResult {
  final String coverLetter;
  final String? extractedEmail; // Kann null sein
  final String? modelType; // Verwendetes KI-Modell (deepseek oder mistral)

  GenerateCoverLetterResult({
    required this.coverLetter,
    this.extractedEmail,
    this.modelType,
  });

  factory GenerateCoverLetterResult.fromJson(Map<String, dynamic> json) {
    return GenerateCoverLetterResult(
      coverLetter:
          json['coverLetter'] as String? ?? '', // Fallback auf Leerstring
      extractedEmail: json['extractedEmail'] as String?, // Kann null sein
      modelType:
          json['modelType']
              as String?, // Kann null sein, default ist 'deepseek'
    );
  }
}

// Definiere die Struktur für Fehler von der Edge Function
class FunctionError implements Exception {
  final String message;
  FunctionError(this.message);

  @override
  String toString() => message;
}

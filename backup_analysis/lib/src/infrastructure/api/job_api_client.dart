import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart';

// Abstrakte Klasse für den Job API Client
abstract class JobApiClient {
  Future<List<JobEntity>> searchJobs({
    required UserProfile userProfile,
    String? keywords,
    int page = 1,
    int limit = 20,
    // Weitere Filterparameter hier hinzufügen
    String? location,
    String? industry,
    String? distance,
  });

  // Methode zum Abrufen detaillierter Job-Informationen
  Future<JobEntity?> getJobDetails(String refnr, {String? hashId});

  // ----- Deklaration der Detail-Methode (WIRD NICHT MEHR VERWENDET) -----
  // Future<JobEntity?> getJobDetails(String refnr);
  // --------------------------------------------------------------------
}

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart'; // Import für Datum (wird unten gebraucht)
import '../../domain/entities/job_entity.dart';
import '../../domain/models/user_profile.dart'; // Kann entfernt werden, wenn nicht benötigt
import 'job_api_client.dart'; // Korrekter Import für die abstrakte Klasse
import '../../core/utils/logging.dart'; // Import für Logger

/// Implementierung des JobApiClient für die Jobsuche-API der Bundesagentur für Arbeit.
class AgenturArbeitApiClient implements JobApiClient {
  // Feste Client ID für die API
  static const String _jobClientId = 'jobboerse-jobsuche';

  // Logger für diese Klasse
  final _log = getLogger('AgenturArbeitApiClient');

  // ----- Korrekter Endpunkt aus Doku -----
  static const String _jobApiUrl =
      'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobs';
  // ---------------------------------------

  // Kein API-Key mehr im Konstruktor nötig
  AgenturArbeitApiClient();

  @override
  Future<List<JobEntity>> searchJobs({
    required UserProfile
    userProfile, // Wird aktuell nicht verwendet, könnte aber für Profil-Matching nützlich sein
    String? keywords,
    int page = 1, // Wird direkt verwendet (1-basiert laut Beispiel)
    int limit = 20,
    String? location,
    String? industry, // API hat evtl. spezifische Branchencodes
    String? distance,
  }) async {
    // Parameter für die API zusammenstellen
    final queryParams = <String, String>{
      'page': page.toString(), // Direkt verwenden
      'size': limit.toString(),
      // Nur hinzufügen, wenn nicht null oder leer
      if (keywords != null && keywords.isNotEmpty) 'was': keywords,
      if (location != null && location.isNotEmpty) 'wo': location,
      if (distance != null && distance.isNotEmpty)
        'umkreis': distance, // Entfernungsparameter hinzufügen
      // if (industry != null && industry.isNotEmpty) 'branche': industry, // Falls relevant
    };

    final uri = Uri.parse(_jobApiUrl).replace(queryParameters: queryParams);
    _log.i('Agentur API Anfrage: ${uri.toString()}');
    _log.i('Agentur API Parameter: $queryParams');
    _log.i('Agentur API Distance: $distance km');

    // Zähler für Wiederholungsversuche
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    while (retryCount <= maxRetries) {
      try {
        // Verwende einen http.Client statt der statischen Methode für mehr Kontrolle
        final client = http.Client();
        try {
          final response = await client
              .get(
                uri,
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json',
                  'X-API-Key':
                      _jobClientId, // Verwende den Job-Client-ID für die normale Jobsuche
                  'User-Agent': 'EinsteinAI-App/1.0',
                },
              )
              .timeout(const Duration(seconds: 15)); // Timeout nach 15 Sekunden

          if (response.statusCode == 200) {
            final data = json.decode(utf8.decode(response.bodyBytes));

            // Datenextraktion
            final List<dynamic> jobResults = data['stellenangebote'] ?? [];

            _log.i(
              'Agentur API: ${jobResults.length} Ergebnisse auf Seite $page erhalten.',
            );

            return jobResults.map((jobData) {
              final rawHashId = jobData['hashId']?.toString();

              return JobEntity(
                id: jobData['refnr']?.toString() ?? '',
                hashId: rawHashId,
                title: jobData['beruf'] ?? 'Kein Beruf angegeben',
                companyName:
                    jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
                location: jobData['arbeitsort']?['ort'] ?? 'Kein Ort',
                descriptionSnippet: _extractDescriptionSnippet(jobData),
                publishedDate:
                    _parseDate(jobData['modifikationsTimestamp']) ??
                    DateTime.now(),
                sourceUrl: jobData['externeUrl']?.toString() ?? 'https://www.arbeitsagentur.de/jobsuche/jobdetail/${jobData['refnr'] ?? ''}',
                description: _extractFullDescription(jobData),
                metadata: {
                  'erfordertErfahrung': jobData['_erfordertErfahrung'] ?? false,
                  'berufserfahrung':
                      jobData['berufserfahrung']?.toString() ?? '',
                  'ausbildung': jobData['ausbildung']?.toString() ?? '',
                  'qualifikation': jobData['qualifikation']?.toString() ?? '',
                  'arbeitszeit': jobData['arbeitszeitmodell']?.toString() ?? '',
                  'befristung': jobData['befristung']?.toString() ?? '',
                },
              );
            }).toList();
          } else if (response.statusCode >= 500 || response.statusCode == 429) {
            // Server-Fehler (5xx) oder Rate-Limiting (429) - wiederholen
            if (retryCount < maxRetries) {
              retryCount++;
              _log.w(
                'API-Fehler ${response.statusCode}, Wiederholungsversuch $retryCount in ${retryDelay.inSeconds} Sekunden...',
              );
              await Future.delayed(retryDelay * retryCount);
              continue; // Nächster Versuch
            } else {
              _log.e(
                'Maximale Anzahl von Wiederholungsversuchen erreicht. Status: ${response.statusCode}',
              );
              throw Exception(
                'Fehler bei der Anfrage an die Agentur für Arbeit API (${response.statusCode})',
              );
            }
          } else {
            // Andere HTTP-Fehler
            _log.e('Agentur API Fehler: Status ${response.statusCode}');
            _log.e('Body: ${response.body}');
            throw Exception(
              'Fehler bei der Anfrage an die Agentur für Arbeit API (${response.statusCode})',
            );
          }
        } finally {
          client.close(); // Client immer schließen
        }
      } catch (e, stackTrace) {
        // Prüfen, ob es sich um einen Netzwerkfehler handelt
        final errorString = e.toString().toLowerCase();
        final isNetworkError =
            errorString.contains('socket') ||
            errorString.contains('connection') ||
            errorString.contains('network') ||
            errorString.contains('host lookup') ||
            errorString.contains('handshake');

        if (isNetworkError && retryCount < maxRetries) {
          retryCount++;
          _log.w(
            'Netzwerkfehler: $e - Wiederholungsversuch $retryCount in ${retryDelay.inSeconds} Sekunden...',
          );
          await Future.delayed(retryDelay * retryCount);
          continue; // Nächster Versuch
        } else {
          // Bei anderen Fehlern oder wenn max. Versuche erreicht sind
          _log.e('Fehler bei der API-Kommunikation: $e\n$stackTrace');
          if (isNetworkError) {
            throw Exception(
              'Verbindungsfehler: Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es später erneut. Details: $e',
            );
          } else {
            throw Exception('Fehler bei der API-Verarbeitung: $e');
          }
        }
      }
    }

    // Sollte nie erreicht werden, aber für Compiler notwendig
    throw Exception('Unerwarteter Fehler bei der Jobsuche');
  }

  // --- Methode zum Abrufen der Job-Details (WIRD NICHT MEHR VERWENDET) ---
  /*
  Future<JobEntity?> getJobDetails(String refnr) async {
    if (refnr.isEmpty) return null;

    // Baue die URL für den Detail-Endpunkt zusammen - VERSUCH MIT refnr!
    final uri = Uri.parse('https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v1/jobdetails/$refnr');
    print('Agentur API Detail Anfrage (Versuch mit refnr): ${uri.toString()}');

    try {
      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'X-API-Key': _clientId,
        },
      );

      if (response.statusCode == 200) {
        final jobData = json.decode(utf8.decode(response.bodyBytes));

        // Mappe die Detail-Antwort auf JobEntity
        return JobEntity(
          id: jobData['refnr']?.toString() ?? refnr,
          hashId: jobData['hashId']?.toString(), // hashId aus Details (falls vorhanden)
          title: jobData['titel'] ?? 'Kein Titel',
          companyName: jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
          location: jobData['arbeitgeberAdresse']?['ort'] ?? 'Kein Ort',
          descriptionSnippet: _parseHtmlSnippet(jobData['stellenbeschreibung']),
          fullDescription: _parseHtmlDescription(jobData['stellenbeschreibung'] ?? 'Keine Beschreibung vorhanden.'),
          postedDate: _parseDate(jobData['aktuelleVeroeffentlichungsdatum'] ?? jobData['modifikationsTimestamp']),
          sourceUrl: jobData['allianzpartnerUrl'] ?? jobData['arbeitgeberdarstellungUrl'],
        );
      } else if (response.statusCode == 404) {
         print('Agentur API Detail Fehler: Job mit ID $refnr nicht gefunden (404).');
         return null;
      } else {
        print('Agentur API Detail Fehler: Status ${response.statusCode}');
        print('Body: ${response.body}');
        // Wir werfen hier KEINE Exception, damit die App nicht abstürzt, geben aber null zurück
        // throw Exception('Fehler bei der Detail-Anfrage an die Agentur für Arbeit API (${response.statusCode})');
        return null;
      }
    } catch (e, stackTrace) {
      print('Fehler bei der Detail-API-Kommunikation: $e\n$stackTrace');
      return null;
    }
  }
  */
  // --------------------------------------------------------------------

  // Neue, modernisierte Methode zum Abrufen der Job-Details
  @override
  Future<JobEntity?> getJobDetails(String refnr, {String? hashId}) async {
    if (refnr.isEmpty) return null;

    // Versuch 1: Mit refnr (Referenznummer)
    final uriWithRefnr = Uri.parse(
      'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails/$refnr',
    );
    _log.i(
      'Agentur API Detail Anfrage (mit refnr): ${uriWithRefnr.toString()}',
    );

    try {
      // Erste Anfrage mit refnr
      var response = await http.get(
        uriWithRefnr,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-API-Key': _jobClientId,
          'User-Agent': 'EinsteinAI-App/1.0',
        },
      );

      // Wenn refnr nicht funktioniert und hashId vorhanden ist, versuche es mit hashId
      if (response.statusCode != 200 && hashId != null && hashId.isNotEmpty) {
        final uriWithHashId = Uri.parse(
          'https://rest.arbeitsagentur.de/jobboerse/jobsuche-service/pc/v4/jobdetails?hashId=$hashId',
        );
        _log.i(
          'Agentur API Detail Anfrage (mit hashId): ${uriWithHashId.toString()}',
        );

        response = await http.get(
          uriWithHashId,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-API-Key': _jobClientId,
            'User-Agent': 'EinsteinAI-App/1.0',
          },
        );
      }

      if (response.statusCode == 200) {
        final jobData = json.decode(utf8.decode(response.bodyBytes));

        _log.i(
          "Job-Details erfolgreich abgerufen: ${jobData['refnr'] ?? refnr}",
        );

        // Vollständige Beschreibung sammeln
        final List<String> descriptionParts = [];

        // Alle relevanten Beschreibungsfelder auslesen
        if (jobData['stellenbeschreibung'] != null) {
          descriptionParts.add(jobData['stellenbeschreibung']);
        }

        if (jobData['taetigkeitsbeschreibung'] != null) {
          descriptionParts.add(
            "Tätigkeitsbeschreibung: ${jobData['taetigkeitsbeschreibung']}",
          );
        }

        if (jobData['anforderungen'] != null) {
          descriptionParts.add("Anforderungen: ${jobData['anforderungen']}");
        }

        if (jobData['arbeitgeberdarstellungbeschreibung'] != null) {
          descriptionParts.add(
            "Über uns: ${jobData['arbeitgeberdarstellungbeschreibung']}",
          );
        }

        // Metadaten hinzufügen für bessere KI-Analyse
        final Map<String, dynamic> metadata = {
          'ausbildung': jobData['ausbildung']?.toString() ?? '',
          'befristung': jobData['befristung']?.toString() ?? '',
          'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
          'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? '',
          'branche': jobData['branche']?.toString() ?? '',
          'qualifikation': jobData['qualifikation']?.toString() ?? '',
        };

        // Kombinierte Beschreibung (für KI-Analyse)
        final fullDescription = descriptionParts.join("\n\n");

        // Mappe die Detail-Antwort auf JobEntity
        return JobEntity(
          id: jobData['refnr']?.toString() ?? refnr,
          hashId: jobData['hashId']?.toString() ?? hashId,
          title: jobData['beruf'] ?? jobData['titel'] ?? 'Kein Titel',
          companyName: jobData['arbeitgeber'] ?? 'Unbekannter Arbeitgeber',
          location: _extractLocation(jobData) ?? 'Kein Ort',
          descriptionSnippet: _parseHtmlSnippet(fullDescription),
          description:
              _parseHtmlDescription(fullDescription) ??
              "Keine detaillierte Beschreibung verfügbar",
          publishedDate:
              _parseDate(
                jobData['aktuelleVeroeffentlichungsdatum'] ??
                    jobData['modifikationsTimestamp'],
              ) ??
              DateTime.now(),
          sourceUrl: jobData['externeUrl']?.toString() ?? 
              'https://www.arbeitsagentur.de/jobsuche/jobdetail/${jobData['refnr'] ?? ''}',
          metadata: metadata,
        );
      } else {
        _log.e('Agentur API Detail Fehler: Status ${response.statusCode}');
        _log.e('Body: ${response.body}');
        return null;
      }
    } catch (e, stackTrace) {
      _log.e('Fehler bei der Detail-API-Kommunikation: $e\n$stackTrace');
      return null;
    }
  }

  // Hilfsmethode zum Extrahieren des Arbeitsortes aus verschiedenen möglichen Feldern
  String? _extractLocation(Map<String, dynamic> jobData) {
    // Versuche verschiedene mögliche Feldnamen für den Ort
    if (jobData['arbeitsort']?['ort'] != null) {
      return jobData['arbeitsort']['ort'];
    } else if (jobData['arbeitgeberAdresse']?['ort'] != null) {
      return jobData['arbeitgeberAdresse']['ort'];
    } else if (jobData['arbeitsortAngabe'] != null) {
      return jobData['arbeitsortAngabe'];
    }
    return null;
  }

  // --- Hilfsfunktionen (Beispiele, müssen angepasst werden) ---

  DateTime? _parseDate(String? dateString) {
    if (dateString == null) return null;
    try {
      return DateTime.tryParse(dateString) ??
          DateFormat('yyyy-MM-dd').tryParse(dateString) ??
          DateFormat('dd.MM.yyyy').tryParse(dateString);
    } catch (_) {
      _log.w("Konnte Datum nicht parsen: $dateString");
      return null;
    }
  }

  String? _parseHtmlSnippet(String? htmlDescription) {
    if (htmlDescription == null) return null;
    // Einfache Methode: Ersten Teil nehmen, HTML-Tags grob entfernen
    // Für eine bessere Darstellung sollte ein HTML-Parser verwendet werden!
    String plainText =
        htmlDescription
            .replaceAll(RegExp(r'<[^>]*>'), ' ')
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();
    return plainText.length > 200
        ? '${plainText.substring(0, 200)}...'
        : plainText;
  }

  String? _parseHtmlDescription(String? htmlDescription) {
    if (htmlDescription == null) return null;
    // TODO: Hier idealerweise HTML parsen und formatieren oder
    // ein Widget verwenden, das HTML anzeigen kann (z.B. flutter_html)
    // Fürs Erste geben wir den rohen HTML-Text zurück (oder bereinigt)
    return htmlDescription
        .replaceAll(RegExp(r'<[^>]*>'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  // Verbesserte Hilfsfunktionen
  String? _extractDescriptionSnippet(Map<String, dynamic> jobData) {
    List<String> snippetSources = [
      jobData['kurzbezeichnung']?.toString() ?? '',
      jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      jobData['arbeitsortzusatz']?.toString() ?? '',
      jobData['befristung']?.toString() ?? '',
      jobData['branche']?.toString() ?? '',
      jobData['ausbildung']?.toString() ?? '',
      jobData['berufserfahrung']?.toString() ?? '',
      jobData['qualifikation']?.toString() ?? '',
    ];

    String combinedText = snippetSources
        .where((text) => text.isNotEmpty)
        .join(' - ');
    return combinedText.isNotEmpty
        ? _parseHtmlSnippet(combinedText)
        : 'Keine Beschreibung verfügbar';
  }

  String _extractFullDescription(Map<String, dynamic> jobData) {
    // Systematisch nach allen relevanten Feldern suchen
    final Map<String, String> keywordFields = {
      'beruf': jobData['beruf']?.toString() ?? '',
      'kurzbezeichnung': jobData['kurzbezeichnung']?.toString() ?? '',
      'taetigkeitsbeschreibung':
          jobData['taetigkeitsbeschreibung']?.toString() ?? '',
      'arbeitgeberdarstellungbeschreibung':
          jobData['arbeitgeberdarstellungbeschreibung']?.toString() ?? '',
      'stellenbeschreibung': jobData['stellenbeschreibung']?.toString() ?? '',
      'branche': jobData['branche']?.toString() ?? '',
      'ausbildung': jobData['ausbildung']?.toString() ?? '',
      'qualifikation': jobData['qualifikation']?.toString() ?? '',
      'befristung': jobData['befristung']?.toString() ?? '',
      'berufserfahrung': jobData['berufserfahrung']?.toString() ?? '',
      'fuehrungsverantwortung':
          jobData['fuehrungsverantwortung']?.toString() ?? '',
      'arbeitszeitmodell': jobData['arbeitszeitmodell']?.toString() ?? '',
    };

    // Spezieller Check für berufserfahrung - dieses Feld direkt auswerten
    String berufserfahrung = keywordFields['berufserfahrung'] ?? '';
    bool erfordertErfahrung =
        berufserfahrung.toLowerCase().contains('notwendig') ||
        berufserfahrung.toLowerCase().contains('erforderlich') ||
        berufserfahrung.toLowerCase().contains('vorausgesetzt');

    // Zusätzliches Feld im metadata speichern
    jobData['_erfordertErfahrung'] = erfordertErfahrung;

    // Zusammenführen der Texte und Felder formatieren
    List<String> formattedSections = [];
    keywordFields.forEach((key, value) {
      if (value.isNotEmpty) {
        formattedSections.add("$key: $value");
      }
    });

    String combinedText = formattedSections.join('\n\n');
    return combinedText.isNotEmpty
        ? combinedText
        : 'Keine detaillierte Beschreibung verfügbar';
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../services/logger_service.dart';
import '../../application/providers/services_providers.dart';

/// Provider für das SupabaseRepository
final supabaseRepositoryProvider = Provider<SupabaseRepository>((ref) {
  final supabaseClient = ref.watch(supabaseClientProvider);
  return SupabaseRepository(supabaseClient);
});

/// Repository für die Interaktion mit Supabase
class SupabaseRepository {
  /// Supabase-Client für DB-Zugriff
  final SupabaseClient _supabaseClient;
  
  /// Logger für das Repository
  final LoggerService _log = LoggerService('SupabaseRepository');
  
  /// Standard-Konstruktor
  SupabaseRepository(this._supabaseClient);

  /// Erstellt die notwendigen Tabellen und RLS-Richtlinien
  /// Sollte beim Start der App geprüft und ggf. ausgeführt werden
  Future<void> createSchemaIfNeeded() async {
    try {
      _log.d('Überprüfe und erstelle Datenbankschema, falls erforderlich');
      
      // Prüfe, ob die subscriptions-Tabelle existiert
      final response = await _supabaseClient.from('subscriptions').select('id').limit(1).maybeSingle();
      
      // Wenn keine Ausnahme geworfen wurde, existiert die Tabelle bereits
      _log.d('Datenbankschema bereits vorhanden: ${response != null}');
      return;
    } catch (e) {
      // Fehler bedeutet wahrscheinlich, dass die Tabelle nicht existiert
      _log.w('Tabelle "subscriptions" existiert nicht. Erstelle Schema: $e');
      await _createSubscriptionsTable();
    }
  }

  /// Erstellt die subscriptions-Tabelle und die zugehörigen RLS-Richtlinien
  Future<void> _createSubscriptionsTable() async {
    try {
      // Verwende SQL für die Tabellenerstellung
      await _supabaseClient.rpc('create_subscriptions_schema', params: {});
      _log.i('Subscriptions-Tabelle erfolgreich erstellt');
    } catch (e) {
      _log.e('Fehler beim Erstellen der Subscriptions-Tabelle', e);
      throw Exception('Fehler beim Erstellen des Datenbankschemas: $e');
    }
  }

  /// Führt einen SQL-Befehl zur Erstellung einer Tabelle aus
  Future<void> executeTableCreation(String sql) async {
    try {
      // HINWEIS: Direktes Ausführen von SQL-Befehlen ist in Supabase Flutter nicht möglich
      // Stattdessen sollte eine RPC-Funktion oder ein Edge-Function verwendet werden
      _log.e('Direktes Ausführen von SQL-Befehlen wird nicht unterstützt');
      throw UnimplementedError('Direktes Ausführen von SQL wird nicht unterstützt. Verwende stored procedures.');
    } catch (e) {
      _log.e('Fehler beim Ausführen des SQL-Befehls', e);
      rethrow;
    }
  }
} 
 
 
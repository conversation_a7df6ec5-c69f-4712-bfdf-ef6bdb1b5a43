import 'dart:convert';
// import 'package:flutter/foundation.dart'; // Unused
import 'package:http/http.dart' as http;
import 'package:ki_test/src/core/security/ssl_pinning.dart';
import 'package:ki_test/src/core/security/secure_storage_manager.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Eine Klasse für sichere API-Aufrufe
/// 
/// Verwendet SSL-Pinning und sichere Speicherung von API-Schlüsseln
class SecureApiClient {
  final _log = getLogger('SecureApiClient');
  final SecureStorageManager _secureStorage = SecureStorageManager();
  
  // Singleton-Instanz
  static final SecureApiClient _instance = SecureApiClient._internal();
  
  // Factory-Konstruktor
  factory SecureApiClient() {
    return _instance;
  }
  
  // Privater Konstruktor für Singleton
  SecureApiClient._internal();
  
  /// Führt einen sicheren GET-Request durch
  Future<http.Response> get(String url, {Map<String, String>? headers}) async {
    try {
      final secureHeaders = await _prepareHeaders(headers);
      _log.d('Sending secure GET request to $url');
      return await secureGet(url, headers: secureHeaders);
    } catch (e) {
      _log.e('Error during secure GET request to $url', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren POST-Request durch
  Future<http.Response> post(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    try {
      final secureHeaders = await _prepareHeaders(headers);
      _log.d('Sending secure POST request to $url');
      return await securePost(url, headers: secureHeaders, body: body, encoding: encoding);
    } catch (e) {
      _log.e('Error during secure POST request to $url', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren PUT-Request durch
  Future<http.Response> put(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    try {
      final secureHeaders = await _prepareHeaders(headers);
      _log.d('Sending secure PUT request to $url');
      return await securePut(url, headers: secureHeaders, body: body, encoding: encoding);
    } catch (e) {
      _log.e('Error during secure PUT request to $url', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren DELETE-Request durch
  Future<http.Response> delete(String url, {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    try {
      final secureHeaders = await _prepareHeaders(headers);
      _log.d('Sending secure DELETE request to $url');
      return await secureDelete(url, headers: secureHeaders, body: body, encoding: encoding);
    } catch (e) {
      _log.e('Error during secure DELETE request to $url', error: e);
      rethrow;
    }
  }
  
  /// Bereitet die Header für einen sicheren Request vor
  Future<Map<String, String>> _prepareHeaders(Map<String, String>? headers) async {
    final secureHeaders = headers ?? {};
    
    // Füge Content-Type hinzu, falls nicht vorhanden
    if (!secureHeaders.containsKey('Content-Type')) {
      secureHeaders['Content-Type'] = 'application/json';
    }
    
    // Füge Authorization-Header hinzu, falls ein Token vorhanden ist
    final token = await _secureStorage.getUserToken();
    if (token != null && token.isNotEmpty && !secureHeaders.containsKey('Authorization')) {
      secureHeaders['Authorization'] = 'Bearer $token';
    }
    
    return secureHeaders;
  }
  
  /// Führt einen sicheren API-Aufruf an DeepSeek durch
  Future<Map<String, dynamic>> callDeepSeekApi(String endpoint, Map<String, dynamic> data) async {
    try {
      final apiKey = await _secureStorage.getDeepSeekApiKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('DeepSeek API-Schlüssel nicht gefunden');
      }
      
      final url = 'https://api.deepseek.com/$endpoint';
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      };
      
      final response = await post(url, headers: headers, body: jsonEncode(data));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _log.e('DeepSeek API-Fehler: ${response.statusCode} - ${response.body}');
        throw Exception('DeepSeek API-Fehler: ${response.statusCode}');
      }
    } catch (e) {
      _log.e('Fehler beim Aufruf der DeepSeek API', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren API-Aufruf an Groq durch
  Future<Map<String, dynamic>> callGroqApi(String endpoint, Map<String, dynamic> data) async {
    try {
      final apiKey = await _secureStorage.getGroqApiKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('Groq API-Schlüssel nicht gefunden');
      }
      
      final url = 'https://api.groq.com/$endpoint';
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      };
      
      final response = await post(url, headers: headers, body: jsonEncode(data));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _log.e('Groq API-Fehler: ${response.statusCode} - ${response.body}');
        throw Exception('Groq API-Fehler: ${response.statusCode}');
      }
    } catch (e) {
      _log.e('Fehler beim Aufruf der Groq API', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren API-Aufruf an Mistral durch
  Future<Map<String, dynamic>> callMistralApi(String endpoint, Map<String, dynamic> data) async {
    try {
      final apiKey = await _secureStorage.getMistralApiKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('Mistral API-Schlüssel nicht gefunden');
      }
      
      final url = 'https://api.mistral.ai/$endpoint';
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      };
      
      final response = await post(url, headers: headers, body: jsonEncode(data));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _log.e('Mistral API-Fehler: ${response.statusCode} - ${response.body}');
        throw Exception('Mistral API-Fehler: ${response.statusCode}');
      }
    } catch (e) {
      _log.e('Fehler beim Aufruf der Mistral API', error: e);
      rethrow;
    }
  }
  
  /// Führt einen sicheren API-Aufruf an die Agentur für Arbeit durch
  Future<Map<String, dynamic>> callArbeitsagenturApi(String endpoint, Map<String, dynamic> data) async {
    try {
      final url = 'https://rest.arbeitsagentur.de/$endpoint';
      final headers = {
        'Content-Type': 'application/json',
      };
      
      final response = await post(url, headers: headers, body: jsonEncode(data));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        _log.e('Arbeitsagentur API-Fehler: ${response.statusCode} - ${response.body}');
        throw Exception('Arbeitsagentur API-Fehler: ${response.statusCode}');
      }
    } catch (e) {
      _log.e('Fehler beim Aufruf der Arbeitsagentur API', error: e);
      rethrow;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:ki_test/src/core/constants/screen_size_constants.dart';
import 'package:ki_test/src/core/utils/responsive_utils.dart';

/// Extensions für BuildContext zur einfacheren Verwendung von Bildschirmgrößen
extension ScreenSizeExtensions on BuildContext {
  /// Gibt die Bildschirmbreite zurück
  double get screenWidth => MediaQuery.of(this).size.width;
  
  /// Gibt die Bildschirmhöhe zurück
  double get screenHeight => MediaQuery.of(this).size.height;
  
  /// Gibt die Bildschirmgröße zurück
  Size get screenSize => MediaQuery.of(this).size;
  
  /// Gibt die Bildschirmorientierung zurück
  Orientation get orientation => MediaQuery.of(this).orientation;
  
  /// Gibt zurück, ob das Gerät im Portraitmodus ist
  bool get isPortrait => orientation == Orientation.portrait;
  
  /// Gibt zurück, ob das Gerät im Landscapemodus ist
  bool get isLandscape => orientation == Orientation.landscape;
  
  /// Gibt die Höhe der Statusleiste zurück
  double get statusBarHeight => MediaQuery.of(this).padding.top;
  
  /// Gibt die Höhe der Navigationsleiste zurück
  double get navigationBarHeight => MediaQuery.of(this).padding.bottom;
  
  /// Gibt die Höhe der Tastatur zurück
  double get keyboardHeight => MediaQuery.of(this).viewInsets.bottom;
  
  /// Gibt zurück, ob die Tastatur sichtbar ist
  bool get isKeyboardVisible => keyboardHeight > 0;
  
  /// Gibt die verfügbare Höhe zurück (Bildschirmhöhe - Statusleiste - Navigationsleiste)
  double get availableHeight => screenHeight - statusBarHeight - navigationBarHeight;
  
  /// Gibt die verfügbare Breite zurück
  double get availableWidth => screenWidth;
  
  /// Gibt den Gerätetyp basierend auf der Bildschirmbreite zurück
  DeviceType get deviceType => ResponsiveUtils().getDeviceType(this);
  
  /// Gibt zurück, ob das Gerät ein Mobiltelefon ist
  bool get isMobile => deviceType == DeviceType.mobile;
  
  /// Gibt zurück, ob das Gerät ein Tablet ist
  bool get isTablet => deviceType == DeviceType.tablet;
  
  /// Gibt zurück, ob das Gerät ein Desktop ist
  bool get isDesktop => deviceType == DeviceType.desktop;
  
  /// Gibt einen Wert basierend auf dem Gerätetyp zurück
  T deviceValue<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
  
  /// Gibt einen Wert basierend auf der Bildschirmorientierung zurück
  T orientationValue<T>({
    required T portrait,
    required T landscape,
  }) {
    return isPortrait ? portrait : landscape;
  }
  
  /// Gibt einen Prozentwert der Bildschirmbreite zurück
  double widthPercent(double percent) {
    return screenWidth * percent;
  }
  
  /// Gibt einen Prozentwert der Bildschirmhöhe zurück
  double heightPercent(double percent) {
    return screenHeight * percent;
  }
  
  /// Gibt einen responsiven Wert basierend auf der Bildschirmbreite zurück
  double responsiveWidth(double value) {
    return value * (screenWidth / 375);
  }
  
  /// Gibt einen responsiven Wert basierend auf der Bildschirmhöhe zurück
  double responsiveHeight(double value) {
    return value * (screenHeight / 812);
  }
  
  /// Gibt einen responsiven Wert für Padding zurück
  EdgeInsets get responsivePadding => deviceValue(
    mobile: const EdgeInsets.all(ScreenSizeConstants.mobileHorizontalPadding),
    tablet: const EdgeInsets.all(ScreenSizeConstants.tabletHorizontalPadding),
    desktop: const EdgeInsets.all(ScreenSizeConstants.desktopHorizontalPadding),
  );
  
  /// Gibt einen responsiven Wert für horizontales Padding zurück
  EdgeInsets get responsiveHorizontalPadding => deviceValue(
    mobile: const EdgeInsets.symmetric(horizontal: ScreenSizeConstants.mobileHorizontalPadding),
    tablet: const EdgeInsets.symmetric(horizontal: ScreenSizeConstants.tabletHorizontalPadding),
    desktop: const EdgeInsets.symmetric(horizontal: ScreenSizeConstants.desktopHorizontalPadding),
  );
  
  /// Gibt einen responsiven Wert für vertikales Padding zurück
  EdgeInsets get responsiveVerticalPadding => deviceValue(
    mobile: const EdgeInsets.symmetric(vertical: ScreenSizeConstants.mobileVerticalPadding),
    tablet: const EdgeInsets.symmetric(vertical: ScreenSizeConstants.tabletVerticalPadding),
    desktop: const EdgeInsets.symmetric(vertical: ScreenSizeConstants.desktopVerticalPadding),
  );
  
  /// Gibt einen responsiven Wert für den Abstand zwischen Elementen zurück
  double get responsiveSpacing => deviceValue(
    mobile: ScreenSizeConstants.mobileItemSpacing,
    tablet: ScreenSizeConstants.tabletItemSpacing,
    desktop: ScreenSizeConstants.desktopItemSpacing,
  );
  
  /// Gibt einen responsiven Wert für die Schriftgröße von Überschriften zurück
  double get responsiveHeadlineSize => deviceValue(
    mobile: ScreenSizeConstants.mobileHeadlineSize,
    tablet: ScreenSizeConstants.tabletHeadlineSize,
    desktop: ScreenSizeConstants.desktopHeadlineSize,
  );
  
  /// Gibt einen responsiven Wert für die Schriftgröße von Textkörpern zurück
  double get responsiveBodySize => deviceValue(
    mobile: ScreenSizeConstants.mobileBodySize,
    tablet: ScreenSizeConstants.tabletBodySize,
    desktop: ScreenSizeConstants.desktopBodySize,
  );
  
  /// Gibt einen responsiven Wert für die Größe von Icons zurück
  double get responsiveIconSize => deviceValue(
    mobile: ScreenSizeConstants.mobileIconSize,
    tablet: ScreenSizeConstants.tabletIconSize,
    desktop: ScreenSizeConstants.desktopIconSize,
  );
  
  /// Gibt einen responsiven Wert für die Höhe von Buttons zurück
  double get responsiveButtonHeight => deviceValue(
    mobile: ScreenSizeConstants.mobileButtonHeight,
    tablet: ScreenSizeConstants.tabletButtonHeight,
    desktop: ScreenSizeConstants.desktopButtonHeight,
  );
  
  /// Gibt einen responsiven Wert für die Breite von Buttons zurück
  double get responsiveButtonWidth => deviceValue(
    mobile: ScreenSizeConstants.mobileButtonWidth,
    tablet: ScreenSizeConstants.tabletButtonWidth,
    desktop: ScreenSizeConstants.desktopButtonWidth,
  );
  
  /// Gibt einen responsiven Wert für die Höhe von Input-Feldern zurück
  double get responsiveInputHeight => deviceValue(
    mobile: ScreenSizeConstants.mobileInputHeight,
    tablet: ScreenSizeConstants.tabletInputHeight,
    desktop: ScreenSizeConstants.desktopInputHeight,
  );
  
  /// Gibt einen responsiven Wert für das Padding von Karten zurück
  double get responsiveCardPadding => deviceValue(
    mobile: ScreenSizeConstants.mobileCardPadding,
    tablet: ScreenSizeConstants.tabletCardPadding,
    desktop: ScreenSizeConstants.desktopCardPadding,
  );
  
  /// Gibt einen responsiven Wert für den Border-Radius von Karten zurück
  double get responsiveCardRadius => deviceValue(
    mobile: ScreenSizeConstants.mobileCardRadius,
    tablet: ScreenSizeConstants.tabletCardRadius,
    desktop: ScreenSizeConstants.desktopCardRadius,
  );
}

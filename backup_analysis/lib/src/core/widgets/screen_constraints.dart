import 'package:flutter/material.dart';
import 'package:ki_test/src/core/constants/screen_size_constants.dart';
import 'package:ki_test/src/core/utils/responsive_utils.dart';

/// Ein Widget, das die Größe seines Kindes basierend auf dem Gerätetyp begrenzt
class ScreenConstraints extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;
  
  /// Maximale Breite für mobile Geräte
  final double? maxMobileWidth;
  
  /// Maximale Breite für Tablets
  final double? maxTabletWidth;
  
  /// Maximale Breite für Desktop
  final double? maxDesktopWidth;
  
  /// Minimale Breite für alle Gerätetypen
  final double? minWidth;
  
  /// Horizontales Padding
  final double horizontalPadding;
  
  /// Ob das Widget zentriert werden soll
  final bool centered;

  const ScreenConstraints({
    super.key,
    required this.child,
    this.maxMobileWidth,
    this.maxTabletWidth,
    this.maxDesktopWidth,
    this.minWidth,
    this.horizontalPadding = 0.0,
    this.centered = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double? maxWidth;
    switch (deviceType) {
      case DeviceType.mobile:
        maxWidth = maxMobileWidth ?? ScreenSizeConstants.maxMobileContainerWidth;
        break;
      case DeviceType.tablet:
        maxWidth = maxTabletWidth ?? ScreenSizeConstants.maxTabletContainerWidth;
        break;
      case DeviceType.desktop:
        maxWidth = maxDesktopWidth ?? ScreenSizeConstants.maxDesktopContainerWidth;
        break;
    }
    
    Widget result = Container(
      constraints: BoxConstraints(
        maxWidth: maxWidth,
        minWidth: minWidth ?? ScreenSizeConstants.minContainerWidth,
      ),
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: child,
    );
    
    if (centered) {
      result = Center(child: result);
    }
    
    return result;
  }
}

/// Ein Widget, das sein Kind in einem Container mit responsiver Größe rendert
class ResponsiveBox extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;
  
  /// Breite für mobile Geräte
  final double? mobileWidth;
  
  /// Höhe für mobile Geräte
  final double? mobileHeight;
  
  /// Breite für Tablets
  final double? tabletWidth;
  
  /// Höhe für Tablets
  final double? tabletHeight;
  
  /// Breite für Desktop
  final double? desktopWidth;
  
  /// Höhe für Desktop
  final double? desktopHeight;
  
  /// Maximale Breite
  final double? maxWidth;
  
  /// Maximale Höhe
  final double? maxHeight;
  
  /// Minimale Breite
  final double? minWidth;
  
  /// Minimale Höhe
  final double? minHeight;
  
  /// Padding
  final EdgeInsetsGeometry? padding;
  
  /// Margin
  final EdgeInsetsGeometry? margin;
  
  /// Hintergrundfarbe
  final Color? color;
  
  /// Dekoration
  final BoxDecoration? decoration;
  
  /// Ob das Widget zentriert werden soll
  final bool centered;
  
  /// Alignment
  final Alignment? alignment;

  const ResponsiveBox({
    super.key,
    required this.child,
    this.mobileWidth,
    this.mobileHeight,
    this.tabletWidth,
    this.tabletHeight,
    this.desktopWidth,
    this.desktopHeight,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.centered = false,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double? width;
    double? height;
    
    switch (deviceType) {
      case DeviceType.mobile:
        width = mobileWidth;
        height = mobileHeight;
        break;
      case DeviceType.tablet:
        width = tabletWidth ?? mobileWidth;
        height = tabletHeight ?? mobileHeight;
        break;
      case DeviceType.desktop:
        width = desktopWidth ?? tabletWidth ?? mobileWidth;
        height = desktopHeight ?? tabletHeight ?? mobileHeight;
        break;
    }
    
    Widget result = Container(
      width: width,
      height: height,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? double.infinity,
        maxHeight: maxHeight ?? double.infinity,
        minWidth: minWidth ?? 0,
        minHeight: minHeight ?? 0,
      ),
      padding: padding,
      margin: margin,
      color: decoration == null ? color : null,
      decoration: decoration,
      alignment: alignment,
      child: child,
    );
    
    if (centered) {
      result = Center(child: result);
    }
    
    return result;
  }
}

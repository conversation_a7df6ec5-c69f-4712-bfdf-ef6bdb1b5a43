import 'package:flutter/material.dart';
import 'package:ki_test/src/core/utils/responsive_utils.dart';

/// Ein Widget, das verschiedene Layouts basierend auf der Bildschirmgröße rendert
class ResponsiveBuilder extends StatelessWidget {
  /// Builder-Funktion für mobile Geräte
  final Widget Function(BuildContext context) mobileBuilder;
  
  /// Builder-Funktion für Tablets (optional)
  final Widget Function(BuildContext context)? tabletBuilder;
  
  /// Builder-Funktion für Desktop (optional)
  final Widget Function(BuildContext context)? desktopBuilder;

  const ResponsiveBuilder({
    super.key,
    required this.mobileBuilder,
    this.tabletBuilder,
    this.desktopBuilder,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobileBuilder(context);
      case DeviceType.tablet:
        return (tabletBuilder ?? mobileBuilder)(context);
      case DeviceType.desktop:
        return (desktopBuilder ?? tabletBuilder ?? mobileBuilder)(context);
    }
  }
}

/// Ein Widget, das verschiedene Layouts basierend auf der Bildschirmausrichtung rendert
class OrientationBuilder extends StatelessWidget {
  /// Builder-Funktion für Portraitmodus
  final Widget Function(BuildContext context) portraitBuilder;
  
  /// Builder-Funktion für Landscapemodus
  final Widget Function(BuildContext context) landscapeBuilder;

  const OrientationBuilder({
    super.key,
    required this.portraitBuilder,
    required this.landscapeBuilder,
  });

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;
    
    if (orientation == Orientation.portrait) {
      return portraitBuilder(context);
    } else {
      return landscapeBuilder(context);
    }
  }
}

/// Ein Widget, das ein responsives Padding basierend auf der Bildschirmgröße anwendet
class ResponsivePadding extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;
  
  /// Padding für mobile Geräte
  final EdgeInsets mobilePadding;
  
  /// Padding für Tablets (optional)
  final EdgeInsets? tabletPadding;
  
  /// Padding für Desktop (optional)
  final EdgeInsets? desktopPadding;
  
  /// Ob die Navigationsleiste berücksichtigt werden soll
  final bool includeNavigationBar;

  const ResponsivePadding({
    super.key,
    required this.child,
    required this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.includeNavigationBar = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    final mediaQuery = MediaQuery.of(context);
    
    EdgeInsets basePadding;
    switch (deviceType) {
      case DeviceType.mobile:
        basePadding = mobilePadding;
        break;
      case DeviceType.tablet:
        basePadding = tabletPadding ?? mobilePadding;
        break;
      case DeviceType.desktop:
        basePadding = desktopPadding ?? tabletPadding ?? mobilePadding;
        break;
    }
    
    // Berücksichtige die Navigationsleiste, falls gewünscht
    if (includeNavigationBar) {
      basePadding = basePadding.copyWith(
        bottom: basePadding.bottom + mediaQuery.padding.bottom,
      );
    }
    
    return Padding(
      padding: basePadding,
      child: child,
    );
  }
}

/// Ein Widget, das eine responsive Größe basierend auf der Bildschirmgröße anwendet
class ResponsiveContainer extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;
  
  /// Breite für mobile Geräte (in Prozent der Bildschirmbreite)
  final double? mobileWidthPercent;
  
  /// Höhe für mobile Geräte (in Prozent der Bildschirmhöhe)
  final double? mobileHeightPercent;
  
  /// Breite für Tablets (in Prozent der Bildschirmbreite)
  final double? tabletWidthPercent;
  
  /// Höhe für Tablets (in Prozent der Bildschirmhöhe)
  final double? tabletHeightPercent;
  
  /// Breite für Desktop (in Prozent der Bildschirmbreite)
  final double? desktopWidthPercent;
  
  /// Höhe für Desktop (in Prozent der Bildschirmhöhe)
  final double? desktopHeightPercent;
  
  /// Maximale Breite
  final double? maxWidth;
  
  /// Maximale Höhe
  final double? maxHeight;
  
  /// Minimale Breite
  final double? minWidth;
  
  /// Minimale Höhe
  final double? minHeight;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.mobileWidthPercent,
    this.mobileHeightPercent,
    this.tabletWidthPercent,
    this.tabletHeightPercent,
    this.desktopWidthPercent,
    this.desktopHeightPercent,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    final screenSize = MediaQuery.of(context).size;
    
    double? width;
    double? height;
    
    switch (deviceType) {
      case DeviceType.mobile:
        width = mobileWidthPercent != null ? screenSize.width * mobileWidthPercent! : null;
        height = mobileHeightPercent != null ? screenSize.height * mobileHeightPercent! : null;
        break;
      case DeviceType.tablet:
        width = tabletWidthPercent != null ? screenSize.width * tabletWidthPercent! : 
               mobileWidthPercent != null ? screenSize.width * mobileWidthPercent! : null;
        height = tabletHeightPercent != null ? screenSize.height * tabletHeightPercent! : 
                mobileHeightPercent != null ? screenSize.height * mobileHeightPercent! : null;
        break;
      case DeviceType.desktop:
        width = desktopWidthPercent != null ? screenSize.width * desktopWidthPercent! : 
               tabletWidthPercent != null ? screenSize.width * tabletWidthPercent! : 
               mobileWidthPercent != null ? screenSize.width * mobileWidthPercent! : null;
        height = desktopHeightPercent != null ? screenSize.height * desktopHeightPercent! : 
                tabletHeightPercent != null ? screenSize.height * tabletHeightPercent! : 
                mobileHeightPercent != null ? screenSize.height * mobileHeightPercent! : null;
        break;
    }
    
    // Begrenze die Größe, falls nötig
    if (maxWidth != null && width != null && width > maxWidth!) {
      width = maxWidth;
    }
    if (maxHeight != null && height != null && height > maxHeight!) {
      height = maxHeight;
    }
    if (minWidth != null && width != null && width < minWidth!) {
      width = minWidth;
    }
    if (minHeight != null && height != null && height < minHeight!) {
      height = minHeight;
    }
    
    return Container(
      width: width,
      height: height,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? double.infinity,
        maxHeight: maxHeight ?? double.infinity,
        minWidth: minWidth ?? 0,
        minHeight: minHeight ?? 0,
      ),
      child: child,
    );
  }
}

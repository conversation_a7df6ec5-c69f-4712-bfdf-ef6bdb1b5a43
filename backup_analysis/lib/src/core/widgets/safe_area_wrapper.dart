import 'package:flutter/material.dart';

/// Ein SafeArea-Wrapper, der die Android-Navigationsleiste berücksichtigt
class SafeAreaWrapper extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;

  /// Ob die obere Kante berücksichtigt werden soll
  final bool top;

  /// Ob die untere Kante berücksichtigt werden soll
  final bool bottom;

  /// Ob die linke Kante berücksichtigt werden soll
  final bool left;

  /// Ob die rechte Kante berücksichtigt werden soll
  final bool right;

  /// Ob die Navigationsleiste berücksichtigt werden soll
  final bool includeNavigationBar;

  /// Minimaler Abstand zur unteren Kante
  final double minimumBottomPadding;

  const SafeAreaWrapper({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
    this.includeNavigationBar = true,
    this.minimumBottomPadding = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    // Verwende SafeArea für die oberen, linken und rechten Ränder
    Widget safeAreaWidget = SafeArea(
      top: top,
      bottom: false, // Wir handhaben den unteren Rand separat
      left: left,
      right: right,
      child: child,
    );

    // Wenn bottom: true, füge Padding für die BottomNavigationBar hinzu
    if (bottom) {
      // Verwende NavigationBarAvoider, um Platz für die BottomNavigationBar zu schaffen
      return safeAreaWidget;
    } else {
      // Keine Anpassung für den unteren Rand nötig
      return safeAreaWidget;
    }
  }
}

/// Ein Widget, das sicherstellt, dass sein Inhalt nicht unter der BottomNavigationBar versteckt wird
class NavigationBarAvoider extends StatelessWidget {
  /// Das Kind-Widget
  final Widget child;

  /// Zusätzliches Padding zur unteren Kante
  final double additionalBottomPadding;

  const NavigationBarAvoider({
    super.key,
    required this.child,
    this.additionalBottomPadding = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    // Berechne das Padding basierend auf der BottomNavigationBar
    final totalPadding = kBottomNavigationBarHeight + additionalBottomPadding;

    return Padding(
      padding: EdgeInsets.only(bottom: totalPadding),
      child: child,
    );
  }
}

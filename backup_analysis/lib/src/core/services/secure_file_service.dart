import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/core/services/secure_storage_service.dart';
import 'package:flutter/foundation.dart';

/// Ein Service für die sichere Speicherung von Dateien
///
/// Verschlüsselt Dateien vor der Speicherung und entschlüsselt sie beim Lesen
class SecureFileService {
  final SecureStorageService _secureStorage;
  final _log = getLogger('SecureFileService');

  // Schlüssel für den Verschlüsselungsschlüssel in SecureStorage
  static const String _encryptionKeyKey = 'file_encryption_key';

  // Ordnername für verschlüsselte Dateien
  static const String _secureFilesFolder = 'secure_files';

  // Ordnername für verschlüsselte Lebensläufe
  static const String _secureCvFolder = 'secure_cv';

  // Singleton-Instanz
  static final SecureFileService _instance = SecureFileService._internal();

  // Factory-Konstruktor
  factory SecureFileService() {
    return _instance;
  }

  // Privater Konstruktor für Singleton
  SecureFileService._internal() : _secureStorage = SecureStorageService();

  /// Generiert einen sicheren Verschlüsselungsschlüssel und speichert ihn
  Future<String> _getOrCreateEncryptionKey() async {
    // Versuche, den vorhandenen Schlüssel zu lesen
    String? encryptionKey = await _secureStorage.getSecureValue(
      _encryptionKeyKey,
    );

    if (encryptionKey == null || encryptionKey.isEmpty) {
      // Generiere einen neuen Schlüssel, wenn keiner vorhanden ist
      final random = List<int>.generate(
        32,
        (_) => DateTime.now().microsecondsSinceEpoch % 256,
      );
      encryptionKey = base64.encode(random);

      // Speichere den neuen Schlüssel
      await _secureStorage.saveSecureValue(_encryptionKeyKey, encryptionKey);
      _log.i('Neuer Verschlüsselungsschlüssel generiert und gespeichert');
    }

    return encryptionKey;
  }

  /// Erstellt einen Dateinamen für eine verschlüsselte Datei
  String _getSecureFileName(String originalFileName, String userId) {
    final fileExtension =
        originalFileName.contains('.')
            ? '.${originalFileName.split('.').last}'
            : '';
    final secureFileName =
        '${userId}_${DateTime.now().millisecondsSinceEpoch}$fileExtension.enc';
    return secureFileName;
  }

  /// Verschlüsselt Daten mit dem gespeicherten Schlüssel
  Future<Uint8List> _encryptData(Uint8List data) async {
    final key = await _getOrCreateEncryptionKey();
    final keyBytes = utf8.encode(key);
    final iv = List<int>.generate(
      16,
      (_) => DateTime.now().microsecondsSinceEpoch % 256,
    );

    // Einfache XOR-Verschlüsselung (für Produktionscode sollte eine stärkere Verschlüsselung verwendet werden)
    final encryptedData = List<int>.filled(data.length, 0);
    for (int i = 0; i < data.length; i++) {
      encryptedData[i] = data[i] ^ keyBytes[i % keyBytes.length];
    }

    // Füge IV am Anfang der verschlüsselten Daten hinzu
    final result = Uint8List(iv.length + encryptedData.length);
    result.setRange(0, iv.length, iv);
    result.setRange(iv.length, result.length, encryptedData);

    return result;
  }

  /// Entschlüsselt Daten mit dem gespeicherten Schlüssel
  Future<Uint8List> _decryptData(Uint8List encryptedData) async {
    final key = await _getOrCreateEncryptionKey();
    final keyBytes = utf8.encode(key);

    // Extrahiere IV aus den verschlüsselten Daten
    final iv = encryptedData.sublist(0, 16);
    final data = encryptedData.sublist(16);

    // Einfache XOR-Entschlüsselung
    final decryptedData = List<int>.filled(data.length, 0);
    for (int i = 0; i < data.length; i++) {
      decryptedData[i] = data[i] ^ keyBytes[i % keyBytes.length];
    }

    return Uint8List.fromList(decryptedData);
  }

  /// Speichert eine Datei sicher verschlüsselt
  Future<String> saveSecureFile(
    String sourcePath,
    String userId, {
    String folder = '',
  }) async {
    try {
      final file = File(sourcePath);
      if (!await file.exists()) {
        throw Exception('Quelldatei existiert nicht: $sourcePath');
      }

      // Lese die Datei
      final fileData = await file.readAsBytes();

      // Verschlüssele die Daten
      final encryptedData = await _encryptData(fileData);

      // Erstelle den Zielordner
      final appDir = await getApplicationDocumentsDirectory();
      final baseDir = Directory('${appDir.path}/$_secureFilesFolder');
      if (!await baseDir.exists()) {
        await baseDir.create(recursive: true);
      }

      // Erstelle den Unterordner, falls angegeben
      final targetDir =
          folder.isNotEmpty ? Directory('${baseDir.path}/$folder') : baseDir;
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // Erstelle den Dateinamen
      final fileName = _getSecureFileName(file.path.split('/').last, userId);
      final targetPath = '${targetDir.path}/$fileName';

      // Speichere die verschlüsselte Datei
      final targetFile = File(targetPath);
      await targetFile.writeAsBytes(encryptedData);

      _log.i('Datei sicher gespeichert: $targetPath');
      return targetPath;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim sicheren Speichern der Datei',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Liest eine sicher gespeicherte Datei
  Future<Uint8List> readSecureFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Verschlüsselte Datei existiert nicht: $filePath');
      }

      // Lese die verschlüsselte Datei
      final encryptedData = await file.readAsBytes();

      // Entschlüssele die Daten
      final decryptedData = await _decryptData(encryptedData);

      _log.i('Datei sicher gelesen: $filePath');
      return decryptedData;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim sicheren Lesen der Datei',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Speichert einen Lebenslauf sicher verschlüsselt
  Future<String> saveSecureCv(String sourcePath, String userId) async {
    return saveSecureFile(sourcePath, userId, folder: _secureCvFolder);
  }

  /// Liest einen sicher gespeicherten Lebenslauf
  Future<File?> getSecureCv(String userId) async {
    try {
      // Finde den Ordner für sichere Lebensläufe
      final appDir = await getApplicationDocumentsDirectory();
      final cvDir = Directory(
        '${appDir.path}/$_secureFilesFolder/$_secureCvFolder',
      );

      if (!await cvDir.exists()) {
        _log.d('Kein sicherer Lebenslauf-Ordner gefunden');
        return null;
      }

      // Liste alle Dateien im Ordner
      final files = await cvDir.list().toList();

      // Finde die neueste Datei, die mit der userId beginnt
      File? latestCvFile;
      DateTime latestTimestamp = DateTime(1970);

      for (final entity in files) {
        if (entity is File && entity.path.contains(userId)) {
          final fileName = entity.path.split('/').last;
          final parts = fileName.split('_');
          if (parts.length > 1) {
            try {
              final timestamp = int.parse(parts[1].split('.').first);
              final fileDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
              if (fileDate.isAfter(latestTimestamp)) {
                latestTimestamp = fileDate;
                latestCvFile = entity;
              }
            } catch (e) {
              // Ignoriere Dateien mit ungültigem Namensformat
              continue;
            }
          }
        }
      }

      if (latestCvFile == null) {
        _log.d('Kein sicherer Lebenslauf für Benutzer $userId gefunden');
        return null;
      }

      // Entschlüssele den Lebenslauf in eine temporäre Datei
      final encryptedData = await latestCvFile.readAsBytes();
      final decryptedData = await _decryptData(encryptedData);

      // Erstelle eine temporäre Datei für den entschlüsselten Lebenslauf
      final tempDir = await getTemporaryDirectory();
      final originalFileName = latestCvFile.path.split('/').last.split('.')[0];
      final tempFile = File(
        '${tempDir.path}/${originalFileName}_decrypted.pdf',
      );
      await tempFile.writeAsBytes(decryptedData);

      _log.i(
        'Lebenslauf sicher gelesen und temporär entschlüsselt: ${tempFile.path}',
      );
      return tempFile;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Abrufen des sicheren Lebenslaufs',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// Löscht einen sicher gespeicherten Lebenslauf
  Future<bool> deleteSecureCv(String userId) async {
    try {
      // Finde den Ordner für sichere Lebensläufe
      final appDir = await getApplicationDocumentsDirectory();
      final cvDir = Directory(
        '${appDir.path}/$_secureFilesFolder/$_secureCvFolder',
      );

      if (!await cvDir.exists()) {
        _log.d('Kein sicherer Lebenslauf-Ordner gefunden');
        return true; // Nichts zu löschen
      }

      // Liste alle Dateien im Ordner
      final files = await cvDir.list().toList();

      // Lösche alle Dateien, die mit der userId beginnen
      bool allDeleted = true;
      for (final entity in files) {
        if (entity is File && entity.path.contains(userId)) {
          try {
            await entity.delete();
            _log.i('Sicherer Lebenslauf gelöscht: ${entity.path}');
          } catch (e) {
            _log.e(
              'Fehler beim Löschen des sicheren Lebenslaufs: ${entity.path}',
              error: e,
            );
            allDeleted = false;
          }
        }
      }

      return allDeleted;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Löschen des sicheren Lebenslaufs',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Entschlüsselt eine verschlüsselte Datei und gibt die entschlüsselte Datei zurück
  Future<File?> decryptFile(String encryptedFilePath) async {
    try {
      final file = File(encryptedFilePath);
      if (!await file.exists()) {
        _log.e('Verschlüsselte Datei existiert nicht: $encryptedFilePath');
        return null;
      }

      // Prüfe, ob die Datei verschlüsselt ist
      if (!encryptedFilePath.endsWith('.enc')) {
        _log.w('Datei scheint nicht verschlüsselt zu sein: $encryptedFilePath');
        return file; // Gib die Originaldatei zurück, wenn sie nicht verschlüsselt ist
      }

      // Lese die verschlüsselte Datei
      final encryptedData = await file.readAsBytes();

      // Entschlüssele die Daten
      final decryptedData = await _decryptData(encryptedData);

      // Erstelle eine temporäre Datei für die entschlüsselten Daten
      final tempDir = await getTemporaryDirectory();
      final originalFileName =
          encryptedFilePath.split('/').last.split('.enc').first;

      // Bestimme die Dateiendung basierend auf den ersten Bytes (Magic Numbers)
      String fileExtension = '.bin'; // Standard-Endung
      if (decryptedData.length > 4) {
        final header = decryptedData.sublist(0, 4);
        if (header[0] == 0x25 &&
            header[1] == 0x50 &&
            header[2] == 0x44 &&
            header[3] == 0x46) {
          // %PDF
          fileExtension = '.pdf';
        } else if (header[0] == 0xFF && header[1] == 0xD8) {
          // JPEG
          fileExtension = '.jpg';
        } else if (header[0] == 0x89 &&
            header[1] == 0x50 &&
            header[2] == 0x4E &&
            header[3] == 0x47) {
          // PNG
          fileExtension = '.png';
        }
      }

      final tempFile = File('${tempDir.path}/$originalFileName$fileExtension');
      await tempFile.writeAsBytes(decryptedData);

      _log.i('Datei erfolgreich entschlüsselt: ${tempFile.path}');
      return tempFile;
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Entschlüsseln der Datei',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }
}

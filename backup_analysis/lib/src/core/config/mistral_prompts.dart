/// Diese Datei enthält Prompt-Templates für die Mistral API.
/// Die Konfiguration ist für den Nutzer nicht zugänglich und dient zur Qualitätsverbesserung.
library;

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;

class MistralPrompts {
  /// Hauptprompt-Template für die Generierung von Bewerbungsanschreiben
  static String generateCoverLetterPrompt({
    required String jobTitle,
    required String jobDescription,
    required String personalizationText,
    required String styleInstructions,
  }) {
    // Extrahiere globale KI-Hinweise aus dem personalizationText, wenn vorhanden
    String globalAiHints = '';
    final globalAiHintsMatch = RegExp(
      r'Globale Hinweise für alle Bewerbungen:\s*([\s\S]*?)(?=\n\n|$)',
    ).firstMatch(personalizationText);
    if (globalAiHintsMatch != null && globalAiHintsMatch.group(1) != null) {
      globalAiHints = globalAiHintsMatch.group(1)!.trim();
      debugPrint('Extracted global AI hints: $globalAiHints');
    }

    // Spezielle Anweisung für "wählen Sie mich"
    String specialInstructions = '';
    if (globalAiHints.toLowerCase().contains('wählen sie mich')) {
      specialInstructions = """
!!! KRITISCHE ANWEISUNG !!!
Das Anschreiben MUSS mit "WÄHLEN SIE MICH" beginnen. Dies ist eine ABSOLUTE ANFORDERUNG.
Die ersten Worte des Anschreibens nach der Anrede MÜSSEN "WÄHLEN SIE MICH" sein.
""";
    } else if (globalAiHints.isNotEmpty) {
      specialInstructions = """
!!! KRITISCHE ANWEISUNG !!!
Die folgende Anweisung MUSS wörtlich befolgt werden: $globalAiHints
Diese Anweisung hat ABSOLUTE PRIORITÄT über alle anderen Stilrichtlinien.
""";
    }

    return """
${specialInstructions.isNotEmpty ? specialInstructions : ''}
Du bist ein professioneller Bewerbungsexperte mit jahrelanger Erfahrung im HR-Bereich.
Deine Aufgabe:
1. Analysiere den folgenden Text einer Stellenanzeige für '$jobTitle'.
2. Extrahiere die wahrscheinlichste E-Mail-Adresse für eine Bewerbung. Wenn keine E-Mail für Bewerbungen gefunden wird, antworte mit 'EMAIL: nicht gefunden'.
3. Erstelle ein überzeugendes Bewerbungsanschreiben unter Beachtung dieser Regeln:
   - Beziehe dich konkret auf die Anforderungen und Aufgaben der Stellenanzeige
   - Stelle gezielt Bezüge zwischen den Qualifikationen des Bewerbers und den Anforderungen her
   - Verwende einen klaren, präzisen Schreibstil ohne Übertreibungen
   - Strukturiere das Anschreiben mit einer überzeugenden Einleitung, relevantem Hauptteil und starkem Abschluss
   - Füge eine formell korrekte Anrede und Grußformel hinzu
   - Halte das Anschreiben auf etwa eine A4-Seite begrenzt
   - Betone Motivation und Mehrwert für das Unternehmen
   - Vermeide Standardfloskeln, Rechtschreibfehler und Passivsätze
   - Füge in der Signatur den Namen des Bewerbers ein, wenn dieser angegeben wurde
4. Beginne niemals die Bewerbung mit "mit großer Interesse" oder ähnlichem
5. Wenn der Bewerber nicht die Fähigkeit hat, die Anforderungen der Stellenanzeige zu erfüllen, schreibe das nicht extra, sondern verschöne diese Lücke
6. Füge KEINE Hinweise, Tipps oder Metakommentare in die Bewerbung ein - der Text soll NUR das fertige Anschreiben sein
7. Erkläre nichts über das Anschreiben selbst und füge keine Kommentare wie "Hier ist dein Anschreiben" hinzu
8. Liefere ausschließlich den Bewerbungstext ohne jegliche Erklärungen, Ratschläge oder Hinweise
9. Schreibe nicht, dass etwas mich reizt, oder typische KI-generierte Sätze
10. WICHTIGE REGEL: Füge KEINE Absender-Kontaktinformationen (E-Mail, Telefon, Adresse) in das Anschreiben ein. Der Name am Ende ist erlaubt.
11. WICHTIGE REGEL: Wenn Qualifikationen fehlen, erwähne dies NICHT direkt. Formuliere stattdessen positiv, z.B. durch Betonung der Lernbereitschaft oder der Übertragbarkeit vorhandener Fähigkeiten auf die neuen Anforderungen.
12. verwende nicht diese formulierung "besonders reizt mich sehr" verwende stattdessen "mich spricht...an" oder andere verariante als "besonders reizt mich sehr"

${globalAiHints.isNotEmpty ? 'WICHTIG - GLOBALE ANWEISUNGEN FÜR DIESE BEWERBUNG:\n$globalAiHints\n\nDiese globalen Anweisungen MÜSSEN in der Bewerbung berücksichtigt werden und haben höchste Priorität!\n\n' : ''}
${personalizationText.isNotEmpty ? 'Persönliche Informationen des Bewerbers:\n$personalizationText\n\n' : ''}
${styleInstructions.isNotEmpty ? 'Gewünschte Stilrichtung:\n$styleInstructions\n\n' : ''}

${globalAiHints.isNotEmpty ? 'NOCHMALS ZUR ERINNERUNG - DIESE GLOBALEN ANWEISUNGEN MÜSSEN BERÜCKSICHTIGT WERDEN:\n$globalAiHints\n\n' : ''}

--- Extrahierter Text der Stellenanzeige ---
$jobDescription
--- Ende Text ---

Antworte NUR im folgenden Format:
EMAIL: [extrahierte E-Mail oder 'nicht gefunden']
ANSCHREIBEN:
[generiertes Anschreiben]
""";
  }

  /// Erweiterte Anweisungen für verschiedene Bewerbungsstile
  static Map<String, String> styleInstructions = {
    'Professionell': """
Schreibe in einem professionellen, formellen Stil mit:
- Sachlicher, klarer Sprache ohne Umschweife
- Angemessener formeller Distanz
- Präzisen Formulierungen und Fachbegriffen
- Höflichem, respektvollem Ton
""",
    'Kreativ': """
Schreibe in einem kreativen, innovativen Stil mit:
- Originellen Formulierungen und bildlicher Sprache
- Persönlicher Note und authentischer Stimme
- Überraschenden Perspektiven und einprägsamen Wendungen
- Dynamischem, enthusiastischem Ausdruck (ohne übertrieben zu wirken)
""",
    'Technisch': """
Schreibe in einem technischen, sachlichen Stil mit:
- Präzisen Fachbegriffen und technischem Vokabular
- Fokus auf technische Fähigkeiten und Kenntnisse
- Logischer, strukturierter Argumentation
- Betonung von Problemlösungskompetenz und analytischem Denken
""",
    'Relevant': """
Schreibe eine zielgerichtete Bewerbung mit intelligenter Qualifikationsauswahl:
- Analysiere die Stellenbeschreibung und identifiziere die wichtigsten Anforderungen
- Wähle NUR die passendsten Qualifikationen und Erfahrungen aus dem Lebenslauf aus
- Erwähne andere Qualifikationen nur kurz oder gar nicht, wenn sie nicht relevant sind
- Fokussiere auf die Übereinstimmung zwischen Stellenanforderungen und Bewerberprofil
- Vermeide irrelevante Details oder Qualifikationen, die nicht zur Position passen
- Erstelle eine präzise, relevante Bewerbung ohne überflüssige Informationen
""",
    'Passend zu meinem Stil': """
Analysiere die persönlichen Informationen des Bewerbers und erstelle ein Anschreiben, das:
- Zum individuellen Profil und Werdegang passt
- Die Stärken und Erfahrungen authentisch hervorhebt
- Die persönliche Kommunikationsweise widerspiegelt
- Einen natürlichen, aber professionellen Ton trifft
""",
  };

  /// Optimiert vorhandene personalisierte Informationen für den Prompt
  static String optimizePersonalization(Map<String, String?> userInfo) {
    StringBuffer buffer = StringBuffer();

    if (userInfo['name']?.isNotEmpty == true) {
      buffer.writeln("Name: ${userInfo['name']}");
    }

    if (userInfo['email']?.isNotEmpty == true) {
      buffer.writeln("E-Mail: ${userInfo['email']}");
    }

    if (userInfo['experience']?.isNotEmpty == true) {
      buffer.writeln("Berufliche Erfahrung:");
      buffer.writeln(userInfo['experience']);
    }

    if (userInfo['education']?.isNotEmpty == true) {
      buffer.writeln("Ausbildung und Studium:");
      buffer.writeln(userInfo['education']);
    }

    if (userInfo['skills']?.isNotEmpty == true) {
      buffer.writeln("Kenntnisse und Fähigkeiten:");
      buffer.writeln(userInfo['skills']);
    }

    if (userInfo['interests']?.isNotEmpty == true) {
      buffer.writeln("Interessen und Motivation:");
      buffer.writeln(userInfo['interests']);
    }

    // Extrahiere globale KI-Hinweise, wenn vorhanden
    if (userInfo['globalAiHints']?.isNotEmpty == true) {
      buffer.writeln("Globale Hinweise für alle Bewerbungen:");
      buffer.writeln(userInfo['globalAiHints']);
    }

    // Extrahiere zusätzliche Hinweise, wenn vorhanden
    if (userInfo['additionalHints']?.isNotEmpty == true) {
      buffer.writeln("Zusätzliche Hinweise für diese spezifische Bewerbung:");
      buffer.writeln(userInfo['additionalHints']);
    }

    return buffer.toString().trim();
  }

  /// Generiert einen personalisierten Schreibstil-Prompt basierend auf dem Benutzerprofil
  /// Diese Methode wird aufgerufen, wenn der Benutzer "Passend zu meinem Stil" auswählt
  /// Die Methode ruft die Supabase-Funktion 'analyze-profile-style' auf,
  /// die die Profildaten analysiert und einen personalisierten Prompt zurückgibt
  static Future<String> generatePersonalizedStylePrompt({
    required Map<String, dynamic> profileData,
  }) async {
    // Standardprompt als Fallback
    final String defaultPrompt = """
Analysiere das Profil des Bewerbers und erstelle einen personalisierten Schreibstil, der folgende Faktoren berücksichtigt:
- Beruflicher Hintergrund und Erfahrungsniveau
- Branche und Fachgebiet
- Persönlichkeit und Kommunikationsstil
- Karriereziele und Motivation
""";

    try {
      // Aufruf der Supabase-Funktion 'analyze-profile-style'
      final response = await Supabase.instance.client.functions.invoke(
        'analyze-profile-style',
        body: {'profileData': profileData},
      );

      // Überprüfen, ob der Aufruf erfolgreich war
      if (response.status == 200 && response.data != null) {
        // Extrahieren des personalisierten Prompts aus der Antwort
        final stylePrompt = response.data['stylePrompt'];

        // Wenn ein Prompt zurückgegeben wurde, verwenden wir diesen
        if (stylePrompt != null && stylePrompt.toString().isNotEmpty) {
          return stylePrompt.toString();
        }
      }

      // Wenn der Aufruf fehlschlägt oder kein Prompt zurückgegeben wird,
      // verwenden wir den Standardprompt
      return defaultPrompt;
    } catch (e) {
      // Bei einem Fehler loggen wir diesen und verwenden den Standardprompt
      debugPrint(
        'Fehler beim Aufruf der Supabase-Funktion "analyze-profile-style": $e',
      );
      return defaultPrompt;
    }
  }
}

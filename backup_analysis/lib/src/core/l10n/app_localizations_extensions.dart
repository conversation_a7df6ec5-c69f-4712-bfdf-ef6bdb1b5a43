import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

/// Extension für AppLocalizationsWrapper um fehlende Lokalisierungen zu ergänzen
extension AppLocalizationsWrapperExtensions on AppLocalizationsWrapper {
  // CV-Section
  String get cvSectionTitle => translate('resume') ?? 'Lebenslauf';
  String get uploadResume => translate('uploadResume') ?? 'Lebenslauf hochladen';
  String get uploadedResume => translate('uploadedResume') ?? 'Hochgeladener Lebenslauf';
  String get viewResume => translate('viewResume') ?? 'Lebenslauf ansehen';
  String get updateResume => translate('updateResume') ?? 'Lebenslauf aktualisieren';
  
  // Skills-Section
  String get skillsSectionTitle => translate('skills') ?? 'Fähigkeiten';
  String get addSkillLabel => translate('addSkill') ?? 'Fähigkeit hinzufügen';
  
  // Berufserfahrungs-Section
  String get workExperienceSectionTitle => translate('workExperience') ?? 'Berufserfahrung';
  
  // Ausbildungs-Section
  String get educationSectionTitle => translate('education') ?? 'Ausbildung';
  
  // Job-Präferenzen-Section
  String get jobPreferencesSectionTitle => translate('jobPreferences') ?? 'Berufswünsche';
  String get jobPreferencesLabel => translate('jobPreferencesLabel') ?? 'Welche Positionen interessieren Sie?';
  
  // Interessen-Section
  String get interestsSectionTitle => translate('interests') ?? 'Interessen';
  String get interestsLabel => translate('interestsLabel') ?? 'Womit beschäftigen Sie sich gerne?';
} 
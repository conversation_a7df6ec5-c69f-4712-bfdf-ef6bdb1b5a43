import 'package:flutter/material.dart';
import 'app_localizations.dart';

/// Eine Wrapper-Klasse für AppLocalizations, die einen Fallback-Mechanismus implementiert.
/// Wenn eine Übersetzung in der gewählten Sprache nicht verfügbar ist, wird auf die englische Übersetzung zurückgegriffen.
class AppLocalizationsWrapper {
  final AppLocalizations _localizations;

  AppLocalizationsWrapper(this._localizations);

  /// Erstellt eine Instanz von AppLocalizationsWrapper mit der aktuellen Lokalisierung.
  static AppLocalizationsWrapper of(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return AppLocalizationsWrapper(localizations);
  }

  /// Gibt die Übersetzung für den angegebenen Schlüssel zurück.
  /// Wenn die Übersetzung nicht verfügbar ist, wird der Schlüssel zurückgegeben.
  String translate(String key, {List<dynamic>? args}) {
    try {
      final value = _getTranslation(key, _localizations);
      if (value != null) {
        return value;
      }
    } catch (e) {
      // Ignoriere Fehler
    }

    // Wenn keine Übersetzung gefunden wurde, geben wir den Schlüssel zurück
    return key;
  }

  /// Hilfsmethode zum Abrufen einer Übersetzung aus einer AppLocalizations-Instanz.
  String? _getTranslation(String key, AppLocalizations localizations) {
    switch (key) {
      case 'appTitle':
        return localizations.appTitle;
      case 'jobSearchTitle':
        return localizations.jobSearchTitle;
      case 'favoritesTitle':
        return localizations.favoritesTitle;
      case 'profileTitle':
        return localizations.profileTitle;
      case 'settingsTitle':
        return localizations.settingsTitle;
      case 'loginTitle':
        return localizations.loginTitle;
      case 'signupTitle':
        return localizations.signupTitle;
      case 'forgotPasswordTitle':
        return localizations.forgotPasswordTitle;
      case 'email':
        return localizations.email;
      case 'password':
        return localizations.password;
      case 'login':
        return localizations.login;
      case 'signup':
        return localizations.signup;
      case 'forgotPassword':
        return localizations.forgotPassword;
      case 'resetPassword':
        return localizations.resetPassword;
      case 'search':
        return localizations.search;
      case 'filter':
        return localizations.filter;
      case 'location':
        return localizations.location;
      case 'distance':
        return localizations.distance;
      case 'jobTitle':
        return localizations.jobTitle;
      case 'company':
        return localizations.company;
      case 'apply':
        return localizations.apply;
      case 'save':
        return localizations.save;
      case 'cancel':
        return localizations.cancel;
      case 'delete':
        return localizations.delete;
      case 'edit':
        return localizations.edit;
      case 'addToFavorites':
        return localizations.addToFavorites;
      case 'removeFromFavorites':
        return localizations.removeFromFavorites;
      case 'noResults':
        return localizations.noResults;
      case 'loading':
        return localizations.loading;
      case 'error':
        return localizations.error;
      case 'retry':
        return localizations.retry;
      case 'logout':
        return localizations.logout;
      case 'accountSettings':
        return localizations.accountSettings;
      case 'appSettings':
        return localizations.appSettings;
      case 'securitySettings':
        return localizations.securitySettings;
      case 'infoHelp':
        return localizations.infoHelp;
      case 'editProfile':
        return localizations.editProfile;
      case 'changePassword':
        return localizations.changePassword;
      case 'premiumBenefits':
        return localizations.premiumBenefits;
      case 'appearance':
        return localizations.appearance;
      case 'clearCache':
        return localizations.clearCache;
      case 'language':
        return localizations.language;
      case 'aboutApp':
        return localizations.aboutApp;
      case 'helpFeedback':
        return localizations.helpFeedback;
      case 'privacyPolicy':
        return localizations.privacyPolicy;
      case 'translateContent':
        return localizations.translateContent;
      case 'originalContent':
        return localizations.originalContent;
      case 'translationError':
        return localizations.translationError;
      case 'translationSuccess':
        return localizations.translationSuccess;
      case 'selectLanguage':
        return localizations.selectLanguage;
      case 'german':
        return localizations.german;
      case 'english':
        return localizations.english;
      case 'french':
        return localizations.french;
      case 'spanish':
        return localizations.spanish;
      case 'italian':
        return localizations.italian;
      case 'languageChanged':
        return localizations.languageChanged;
      case 'restartApp':
        return localizations.restartApp;
      case 'restartRequired':
        return localizations.restartRequired;
      case 'restartAppMessage':
        return localizations.restartAppMessage;
      case 'detectedLanguage':
        return localizations.detectedLanguage;
      case 'translatingTo':
        return localizations.translatingTo;
      case 'translationInProgress':
        return localizations.translationInProgress;
      case 'welcome':
        return localizations.welcome;
      case 'findJobWithAI':
        return localizations.findJobWithAI;
      case 'setupProfile':
        return localizations.setupProfile;
      case 'aboutYou':
        return localizations.aboutYou;
      case 'profileHelpInfo':
        return localizations.profileHelpInfo;
      case 'fullName':
        return localizations.fullName;
      case 'emailFromLogin':
        return localizations.emailFromLogin;
      case 'phoneOptional':
        return localizations.phoneOptional;
      case 'yourSkills':
        return localizations.yourSkills;
      case 'skillsDescription':
        return localizations.skillsDescription;
      case 'skill':
        return localizations.skill;
      case 'removeSkill':
        return localizations.removeSkill;
      case 'addSkill':
        return localizations.addSkill;
      case 'minSkillRequired':
        return localizations.minSkillRequired;
      case 'lastPosition':
        return localizations.lastPosition;
      case 'experienceDescription':
        return localizations.experienceDescription;
      case 'position':
        return localizations.position;
      case 'firm':
        return localizations.firm;
      case 'startDate':
        return localizations.startDate;
      case 'endDate':
        return localizations.endDate;
      case 'currentlyWorking':
        return localizations.currentlyWorking;
      case 'backToLogin':
        return localizations.backToLogin;
      case 'generateAICoverLetter':
        return localizations.generateAICoverLetter;
      case 'generatingCoverLetter':
        return localizations.generatingCoverLetter;
      case 'coverLetterGenerated':
        return localizations.coverLetterGenerated;
      case 'solveCaptcha':
        return localizations.solveCaptcha;
      case 'loadingAd':
        return localizations.loadingAd;
      case 'confirm':
        return localizations.confirm;
      case 'ok':
        return localizations.ok;
      case 'termsAndPrivacyNotice':
        return localizations.termsAndPrivacyNotice;
      case 'apiCallsDisabled':
        return localizations.apiCallsDisabled;
      case 'aiTest':
        return localizations.aiTest;
      case 'aiChangeActive':
        return localizations.aiChangeActive;
      case 'personalData':
        return localizations.personalData;
      case 'resume':
        return localizations.resume;
      case 'skills':
        return localizations.skills;
      case 'workExperience':
        return localizations.workExperience;
      case 'education':
        return localizations.education;
      case 'aiPersonalization':
        return localizations.aiPersonalization;
      case 'address':
        return localizations.address;
      case 'localSelectionRemoved':
        return localizations.localSelectionRemoved;
      case 'generateJobTitles':
        return localizations.generateJobTitles;
      case 'generatingJobTitles':
        return localizations.generatingJobTitles;
      case 'jobTitlesGenerated':
        return localizations.jobTitlesGenerated;
      case 'jobTitlesError':
        return localizations.jobTitlesError;
      case 'deleteSkill':
        return localizations.deleteSkill;
      case 'deleteWorkExperience':
        return localizations.deleteWorkExperience;
      case 'deleteEducation':
        return localizations.deleteEducation;
      case 'generatedJobTitles':
        return localizations.generatedJobTitles;
      case 'name':
        return localizations.name;
      case 'phone':
        return localizations.phone;
      case 'desiredPosition':
        return localizations.desiredPosition;
      case 'salaryClaim':
        return localizations.salaryClaim;
      case 'preferredLocation':
        return localizations.preferredLocation;
      case 'notSpecified':
        return localizations.notSpecified;
      case 'noWorkExperience':
        return localizations.noWorkExperience;
      case 'position':
        return localizations.position;
      case 'company':
        return localizations.company;
      case 'description':
        return localizations.description;
      case 'startDate':
        return localizations.startDate;
      case 'endDate':
        return localizations.endDate;
      case 'deleteEntry':
        return localizations.deleteEntry;
      case 'startDateError':
        return localizations.startDateError;
      case 'noEducation':
        return localizations.noEducation;
      case 'institution':
        return localizations.institution;
      case 'degree':
        return localizations.degree;
      case 'fieldOfStudy':
        return localizations.fieldOfStudy;
      case 'noSkills':
        return localizations.noSkills;
      case 'addSkill':
        return localizations.addSkill;
      case 'deleteSkill':
        return localizations.deleteSkill;
      case 'enterSkill':
        return localizations.enterSkill;
      case 'selectPdf':
        return localizations.selectPdf;
      case 'selectNewPdf':
        return localizations.selectNewPdf;
      case 'fileSelectionError':
        return localizations.fileSelectionError;
      case 'cannotOpenResume':
        return localizations.cannotOpenResume;
      case 'removeSelection':
        return localizations.removeSelection;
      case 'relevance':
        return localizations.relevance;
      case 'distance':
        return localizations.distance;
      case 'date':
        return localizations.date;
      case 'enterKeyword':
        return localizations.enterKeyword;
      case 'radius':
        return localizations.radius;
      case 'km':
        return localizations.km;
      case 'yourProfessions':
        return localizations.yourProfessions;
      case 'includeWorkExperience':
        return localizations.includeWorkExperience;
      case 'preferredWritingStyle':
        return localizations.preferredWritingStyle;
      case 'interestingHobbies':
        return localizations.interestingHobbies;
      case 'aiPersonalization':
        return localizations.aiPersonalization;
      case 'premiumBenefits':
        return localizations.premiumBenefits;
      case 'changePassword':
        return localizations.changePassword;
      case 'currentPassword':
        return localizations.currentPassword;
      case 'newPassword':
        return localizations.newPassword;
      case 'confirmPassword':
        return localizations.confirmPassword;
      case 'passwordChanged':
        return localizations.passwordChanged;
      case 'passwordError':
        return localizations.passwordError;
      case 'passwordMismatch':
        return localizations.passwordMismatch;
      case 'passwordTooShort':
        return localizations.passwordTooShort;
      case 'withoutExperience':
        return localizations.withoutExperience;
      case 'noJobsFound':
        return localizations.noJobsFound;
      case 'experienceSummary':
        return localizations.experienceSummary;
      case 'aiPersonalizationHint':
        return localizations.aiPersonalizationHint;
      case 'savePassword':
        return localizations.savePassword;
      case 'enterNewPassword':
        return localizations.enterNewPassword;
      case 'passwordMinLength':
        return localizations.passwordMinLength;
      case 'passwordRequireUppercase':
        return localizations.passwordRequireUppercase;
      case 'passwordRequireNumber':
        return localizations.passwordRequireNumber;
      case 'confirmNewPassword':
        return localizations.confirmNewPassword;
      case 'noUserLoggedIn':
        return localizations.noUserLoggedIn;
      case 'passwordUpdateError':
        // Diese Methode benötigt einen Parameter, daher können wir sie hier nicht direkt zurückgeben
        return null;
      case 'unexpectedError':
        return localizations.unexpectedError;
      case 'passwordUpdatedSuccess':
        return localizations.passwordUpdatedSuccess;
      case 'selectDate':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'deleteDate':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'resumeFileName':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'autofillFromResume':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'enabled':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'disabled':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'skillsInApplication':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'workExperienceInApplication':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      case 'educationInApplication':
        return null; // Wird direkt über die Wrapper-Klasse bereitgestellt
      default:
        return null;
    }
  }

  // Direkter Zugriff auf die Übersetzungen
  String get appTitle => translate('appTitle');
  String get jobSearchTitle => translate('jobSearchTitle');
  String get favoritesTitle => translate('favoritesTitle');
  String get profileTitle => translate('profileTitle');
  String get settingsTitle => translate('settingsTitle');
  String get loginTitle => translate('loginTitle');
  String get signupTitle => translate('signupTitle');
  String get forgotPasswordTitle => translate('forgotPasswordTitle');
  String get email => translate('email');
  String get password => translate('password');
  String get login => translate('login');
  String get signup => translate('signup');
  String get forgotPassword => translate('forgotPassword');
  String get resetPassword => translate('resetPassword');
  String get search => translate('search');
  String get filter => translate('filter');
  String get location => translate('location');
  String get distance => translate('distance');
  String get jobTitle => translate('jobTitle');
  String get company => translate('company');
  String get apply => translate('apply');
  String get save => translate('save');
  String get cancel => translate('cancel');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get addToFavorites => translate('addToFavorites');
  String get removeFromFavorites => translate('removeFromFavorites');
  String get noResults => translate('noResults');
  String get loading => translate('loading');
  String get error => translate('error');
  String get retry => translate('retry');
  String get logout => translate('logout');
  String get accountSettings => translate('accountSettings');
  String get appSettings => translate('appSettings');
  String get securitySettings => translate('securitySettings');
  String get infoHelp => translate('infoHelp');
  String get editProfile => translate('editProfile');
  String get changePassword => translate('changePassword');
  String get premiumBenefits => translate('premiumBenefits');
  String get appearance => translate('appearance');
  String get clearCache => translate('clearCache');
  String get language => translate('language');
  String get aboutApp => translate('aboutApp');
  String get helpFeedback => translate('helpFeedback');
  String get privacyPolicy => translate('privacyPolicy');
  String get translateContent => translate('translateContent');
  String get originalContent => translate('originalContent');
  String get translationError => translate('translationError');
  String get translationSuccess => translate('translationSuccess');
  String get selectLanguage => translate('selectLanguage');
  String get german => translate('german');
  String get english => translate('english');
  String get french => translate('french');
  String get spanish => translate('spanish');
  String get italian => translate('italian');
  String get languageChanged => translate('languageChanged');
  String get restartApp => translate('restartApp');
  String get restartRequired => translate('restartRequired');
  String get restartAppMessage => translate('restartAppMessage');
  String get detectedLanguage => translate('detectedLanguage');
  String get translatingTo => translate('translatingTo');
  String get translationInProgress => translate('translationInProgress');
  String get welcome => translate('welcome');
  String get findJobWithAI => translate('findJobWithAI');
  String get setupProfile => translate('setupProfile');
  String get aboutYou => translate('aboutYou');
  String get profileHelpInfo => translate('profileHelpInfo');
  String get fullName => translate('fullName');
  String get emailFromLogin => translate('emailFromLogin');
  String get phoneOptional => translate('phoneOptional');
  String get yourSkills => translate('yourSkills');
  String get skillsDescription => translate('skillsDescription');
  String get skill => translate('skill');
  String get removeSkill => translate('removeSkill');
  String get addSkill => translate('addSkill');
  String get minSkillRequired => translate('minSkillRequired');
  String get lastPosition => translate('lastPosition');
  String get experienceDescription => translate('experienceDescription');
  String get position => translate('position');
  String get firm => translate('firm');
  String get startDate => translate('startDate');
  String get endDate => translate('endDate');
  String get currentlyWorking => translate('currentlyWorking');
  String get backToLogin => translate('backToLogin');
  String get generateAICoverLetter => translate('generateAICoverLetter');
  String get generatingCoverLetter => translate('generatingCoverLetter');
  String get coverLetterGenerated => translate('coverLetterGenerated');
  String get solveCaptcha => translate('solveCaptcha');
  String get loadingAd => translate('loadingAd');
  String get confirm => translate('confirm');
  String get ok => translate('ok');
  String get termsAndPrivacyNotice => translate('termsAndPrivacyNotice');
  String get apiCallsDisabled => translate('apiCallsDisabled');
  String get aiTest => translate('aiTest');
  String get aiChangeActive => translate('aiChangeActive');
  String get personalData => translate('personalData');
  String get resume => translate('resume');
  String get skills => translate('skills');
  String get workExperience => translate('workExperience');
  String get education => translate('education');
  String get aiPersonalization => translate('aiPersonalization');
  String get address => translate('address');
  String get localSelectionRemoved => translate('localSelectionRemoved');
  String get generateJobTitles => translate('generateJobTitles');
  String get generatingJobTitles => translate('generatingJobTitles');
  String get jobTitlesGenerated => translate('jobTitlesGenerated');
  String get jobTitlesError => translate('jobTitlesError');
  String get deleteSkill => translate('deleteSkill');
  String get deleteWorkExperience => translate('deleteWorkExperience');
  String get deleteEducation => translate('deleteEducation');
  String get generatedJobTitles => translate('generatedJobTitles');
  String get name => translate('name');
  String get phone => translate('phone');
  String get desiredPosition => translate('desiredPosition');
  String get salaryClaim => translate('salaryClaim');
  String get preferredLocation => translate('preferredLocation');
  String get notSpecified => translate('notSpecified');
  String get noWorkExperience => translate('noWorkExperience');
  String get description => translate('description');
  String get deleteEntry => translate('deleteEntry');
  String get startDateError => translate('startDateError');
  String get noEducation => translate('noEducation');
  String get institution => translate('institution');
  String get degree => translate('degree');
  String get fieldOfStudy => translate('fieldOfStudy');
  String get noSkills => translate('noSkills');
  String get enterSkill => translate('enterSkill');
  String get selectPdf => translate('selectPdf');
  String get selectNewPdf => translate('selectNewPdf');
  String get fileSelectionError => translate('fileSelectionError');
  String get cannotOpenResume => translate('cannotOpenResume');
  String get removeSelection => translate('removeSelection');
  String get relevance => translate('relevance');
  String get date => translate('date');
  String get enterKeyword => translate('enterKeyword');
  String get radius => translate('radius');
  String get km => translate('km');
  String get yourProfessions => translate('yourProfessions');
  String get includeWorkExperience => translate('includeWorkExperience');
  String get preferredWritingStyle => translate('preferredWritingStyle');
  String get interestingHobbies => translate('interestingHobbies');
  String get currentPassword => translate('currentPassword');
  String get newPassword => translate('newPassword');
  String get confirmPassword => translate('confirmPassword');
  String get passwordChanged => translate('passwordChanged');
  String get passwordError => translate('passwordError');
  String get passwordMismatch => translate('passwordMismatch');
  String get passwordTooShort => translate('passwordTooShort');
  String get withoutExperience => translate('withoutExperience');
  String get noJobsFound => translate('noJobsFound');
  String get experienceSummary => translate('experienceSummary');
  String get aiPersonalizationHint => translate('aiPersonalizationHint');
  String get savePassword => translate('savePassword');
  String get enterNewPassword => translate('enterNewPassword');
  String get passwordMinLength => translate('passwordMinLength');
  String get passwordRequireUppercase => translate('passwordRequireUppercase');
  String get passwordRequireNumber => translate('passwordRequireNumber');
  String get confirmNewPassword => translate('confirmNewPassword');
  String get noUserLoggedIn => translate('noUserLoggedIn');
  String passwordUpdateError(String message) =>
      _localizations.passwordUpdateError(message);
  String get unexpectedError => translate('unexpectedError');
  String get passwordUpdatedSuccess => translate('passwordUpdatedSuccess');
  String get selectDate => translate('selectDate');
  String get deleteDate => translate('deleteDate');
  String get resumeFileName => translate('resumeFileName');
  String get autofillFromResume => translate('autofillFromResume');
  String get enabled => translate('enabled');
  String get disabled => translate('disabled');
  String get skillsInApplication => translate('skillsInApplication');
  String get workExperienceInApplication =>
      translate('workExperienceInApplication');
  String get educationInApplication => translate('educationInApplication');
}

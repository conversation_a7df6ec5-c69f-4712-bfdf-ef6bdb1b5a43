import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:ki_test/src/core/security/secure_storage_manager.dart';

/// Konfigurationsklasse für Umgebungsvariablen und Einstellungen
class EnvConfig {
  // Supabase Konfiguration
  static const String supabaseUrl = 'https://vpttdxibvjrfjzbtktqg.supabase.co';

  // Statische Getter für API-Schlüssel (werden aus dem sicheren Speicher geladen)
  static Future<String> getSupabaseAnonKey() async {
    final secureStorage = SecureStorageManager();
    final key = await secureStorage.getSupabaseAnonKey();
    if (key != null && key.isNotEmpty) {
      return key;
    }

    // Versuche, den Schlüssel aus .env zu laden
    final envKey = dotenv.env['SUPABASE_ANON_KEY'];
    if (envKey != null && envKey.isNotEmpty) {
      // Speichere den Schlüssel für zukünftige Verwendung
      await secureStorage.saveSupabaseAnonKey(envKey);
      return envKey;
    }

    // Wenn kein <PERSON>hlüssel gefunden wurde, wirf eine Exception
    throw Exception(
      'Supabase Anon Key nicht gefunden. Bitte stelle sicher, dass der Schlüssel in der .env-Datei oder im sicheren Speicher vorhanden ist.',
    );
  }

  static Future<String> getDeepSeekApiKey() async {
    final secureStorage = SecureStorageManager();
    final key = await secureStorage.getDeepSeekApiKey();
    if (key != null && key.isNotEmpty) {
      return key;
    }

    // Fallback: Versuche, den Schlüssel aus .env zu laden
    final envKey = dotenv.env['DEEPSEEK_API_KEY'];
    if (envKey != null && envKey.isNotEmpty) {
      // Speichere den Schlüssel für zukünftige Verwendung
      await secureStorage.saveDeepSeekApiKey(envKey);
      return envKey;
    }

    // Wenn kein Schlüssel gefunden wurde, gib einen leeren String zurück
    return '';
  }

  static Future<String> getGroqApiKey() async {
    final secureStorage = SecureStorageManager();
    final key = await secureStorage.getGroqApiKey();
    if (key != null && key.isNotEmpty) {
      return key;
    }

    // Fallback: Versuche, den Schlüssel aus .env zu laden
    final envKey = dotenv.env['GROQ_API_KEY'];
    if (envKey != null && envKey.isNotEmpty) {
      // Speichere den Schlüssel für zukünftige Verwendung
      await secureStorage.saveGroqApiKey(envKey);
      return envKey;
    }

    // Wenn kein Schlüssel gefunden wurde, gib einen leeren String zurück
    return '';
  }

  static Future<String> getMistralApiKey() async {
    final secureStorage = SecureStorageManager();
    final key = await secureStorage.getMistralApiKey();
    if (key != null && key.isNotEmpty) {
      return key;
    }

    // Fallback: Versuche, den Schlüssel aus .env zu laden
    final envKey = dotenv.env['MISTRAL_API_KEY'];
    if (envKey != null && envKey.isNotEmpty) {
      // Speichere den Schlüssel für zukünftige Verwendung
      await secureStorage.saveMistralApiKey(envKey);
      return envKey;
    }

    // Wenn kein Schlüssel gefunden wurde, gib einen leeren String zurück
    return '';
  }

  // In-App-Kauf Konfiguration
  static const String androidPremiumMonthlyId = 'premium_monthly_subscription';
  static const String iosPremiumMonthlyId = 'premium_monthly_subscription';
  static const String androidPremiumYearlyId = 'premium_yearly_subscription';
  static const String iosPremiumYearlyId = 'premium_yearly_subscription';

  // Debug-Modus Einstellungen
  static bool get isDebugMode => kDebugMode;

  // Supabase Edge Functions
  static const String subscriptionFunctionPath = 'manage-subscription';
  static const String ocrFunctionPath = 'perform-ocr-on-pdf';
  static const String generateCoverLetterPath = 'generate-cover-letter';
  static const String checkPremiumStatusPath = 'check-premium-status';
  static const String analyzeCvPath = 'analyze-cv';

  // Tabellennamen
  static const String subscriptionsTable = 'subscriptions';
  static const String purchasesTable = 'purchases';
  static const String adAccessTable = 'ad_access';
  static const String applicationCountersTable = 'application_counters';
  static const String userFavoritesTable = 'user_favorites';
  static const String appliedJobsTable = 'applied_jobs';

  // Storage-Buckets
  static const String cvBackupsBucket = 'cv-backups';
  static const String cvUploadsBucket = 'cv-uploads';

  // Initialisiert die Umgebungsvariablen
  static Future<void> init() async {
    try {
      // Lade .env-Datei, falls vorhanden
      await dotenv.load();

      // Initialisiere den sicheren Speicher mit Werten aus .env
      final secureStorage = SecureStorageManager();

      // Supabase Anon Key
      final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];
      if (supabaseAnonKey != null && supabaseAnonKey.isNotEmpty) {
        await secureStorage.saveSupabaseAnonKey(supabaseAnonKey);
      }

      // DeepSeek API Key
      final deepSeekApiKey = dotenv.env['DEEPSEEK_API_KEY'];
      if (deepSeekApiKey != null && deepSeekApiKey.isNotEmpty) {
        await secureStorage.saveDeepSeekApiKey(deepSeekApiKey);
      }

      // Groq API Key
      final groqApiKey = dotenv.env['GROQ_API_KEY'];
      if (groqApiKey != null && groqApiKey.isNotEmpty) {
        await secureStorage.saveGroqApiKey(groqApiKey);
      }

      // Mistral API Key
      final mistralApiKey = dotenv.env['MISTRAL_API_KEY'];
      if (mistralApiKey != null && mistralApiKey.isNotEmpty) {
        await secureStorage.saveMistralApiKey(mistralApiKey);
      }
    } catch (e) {
      // Ignoriere Fehler beim Laden der .env-Datei
      if (kDebugMode) {
        // print('Fehler beim Laden der Umgebungsvariablen: $e');
      }
    }
  }
}

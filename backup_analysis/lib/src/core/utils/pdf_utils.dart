import 'dart:io';
import 'package:flutter/foundation.dart'; // für compute
import 'package:syncfusion_flutter_pdf/pdf.dart';

// Diese Funktion wird im Isolate ausgeführt
Future<String> _extractTextFromPdfIsolate(String filePath) async {
  debugPrint('[PDF Isolate] Starting text extraction for: $filePath');
  try {
    // Lade das PDF-Dokument aus der Datei
    final file = File(filePath);
    if (!await file.exists()) {
      debugPrint('[PDF Isolate] Error: File not found.');
      throw Exception("PDF file not found at path: $filePath");
    }
    debugPrint('[PDF Isolate] File exists. Reading bytes...');
    final List<int> bytes = await file.readAsBytes();
    debugPrint('[PDF Isolate] Read ${bytes.length} bytes. Loading PdfDocument...');
    final PdfDocument document = PdfDocument(inputBytes: bytes);
    debugPrint('[PDF Isolate] PdfDocument loaded. Extracting text...');

    // Extrahiere den Text aus allen Seiten
    String text = PdfTextExtractor(document).extractText();
    debugPrint('[PDF Isolate] Text extraction complete. Extracted text length: ${text.length}');

    // Gib das Dokument frei
    debugPrint('[PDF Isolate] Disposing document...');
    document.dispose();
    debugPrint('[PDF Isolate] Document disposed. Returning text.');

    return text;
  } catch (e, stackTrace) { // Füge stackTrace hinzu für mehr Details
    // Logge den Fehler oder leite ihn weiter
    // debugPrint('Error extracting text from PDF: $e'); // Alte Log-Nachricht
    debugPrint('[PDF Isolate] Error during extraction: $e');
    debugPrint('[PDF Isolate] StackTrace: $stackTrace'); // Logge den StackTrace
    // Wir geben hier einen leeren String zurück oder werfen den Fehler erneut,
    // je nachdem, wie der Aufrufer damit umgehen soll.
    // Fürs Erste geben wir einen leeren String zurück, um die App nicht abstürzen zu lassen.
    // Der Aufrufer sollte prüfen, ob der String leer ist.
    return '';
     // Alternativ: throw Exception('Failed to extract text from PDF: $e');
  }
}

/// Extrahiert Text aus einer PDF-Datei unter Verwendung eines separaten Isolates.
///
/// [filePath]: Der Pfad zur lokalen PDF-Datei.
/// Gibt den extrahierten Text als String zurück. Gibt einen leeren String zurück,
/// wenn ein Fehler auftritt oder die Datei nicht gefunden wird.
Future<String> extractTextFromPdf(String filePath) async {
  // Führe die rechenintensive Extraktion in einem separaten Isolate aus
  return await compute(_extractTextFromPdfIsolate, filePath);
} 
import 'dart:io';
import 'dart:convert';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// DeviceManager ist verantwortlich für die Verwaltung der Geräte-ID und
/// die Zuordnung von Geräten zu Benutzerprofilen.
class DeviceManager {
  static final DeviceManager _instance = DeviceManager._internal();
  final Logger _log = Logger('DeviceManager');

  factory DeviceManager() => _instance;

  DeviceManager._internal();

  static const String _deviceIdKey = 'device_id';
  static const String _lastUserIdKey = 'last_user_id';

  /// Generiert oder holt eine eindeutige Geräte-ID
  Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString(_deviceIdKey);

    if (deviceId == null) {
      deviceId = await _generateDeviceId();
      await prefs.setString(_deviceIdKey, deviceId);
      _log.info('Neue Geräte-ID generiert: $deviceId');
    } else {
      _log.fine('Bestehende Geräte-ID verwendet: $deviceId');
    }

    return deviceId;
  }

  /// Generiert eine eindeutige Geräte-ID basierend auf Geräteinformationen
  Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();
    final uuid = const Uuid();
    String deviceData = '';

    try {
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        deviceData =
            '${webInfo.browserName}-${webInfo.platform}-${webInfo.userAgent}';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceData =
            '${androidInfo.brand}-${androidInfo.model}-${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceData =
            '${iosInfo.model}-${iosInfo.systemName}-${iosInfo.identifierForVendor}';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        deviceData = '${windowsInfo.computerName}-${windowsInfo.productId}';
      } else if (Platform.isMacOS) {
        final macOsInfo = await deviceInfo.macOsInfo;
        deviceData = '${macOsInfo.computerName}-${macOsInfo.model}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        deviceData = '${linuxInfo.name}-${linuxInfo.machineId}';
      }
    } catch (e) {
      _log.warning('Fehler beim Abrufen von Geräteinformationen: $e');
      deviceData = 'unknown-device-${DateTime.now().millisecondsSinceEpoch}';
    }

    // Generiere eine UUID basierend auf den Gerätedaten
    // Verwende URL-Namespace (Konstante aus Uuid-Paket)
    return uuid.v5('6ba7b811-9dad-11d1-80b4-00c04fd430c8', deviceData);
  }

  /// Speichert die Benutzer-ID des zuletzt angemeldeten Benutzers
  Future<void> saveLastUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastUserIdKey, userId);
    _log.fine('Letzte Benutzer-ID gespeichert: $userId');
  }

  /// Holt die Benutzer-ID des zuletzt angemeldeten Benutzers
  Future<String?> getLastUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString(_lastUserIdKey);
    _log.fine('Letzte Benutzer-ID abgerufen: $userId');
    return userId;
  }

  /// Registriert das aktuelle Gerät für einen Benutzer in Supabase
  Future<void> registerDeviceForUser(String userId) async {
    try {
      final deviceId = await getDeviceId();
      final client = Supabase.instance.client;

      // Prüfe, ob der Benutzer angemeldet ist
      if (client.auth.currentUser?.id != userId) {
        _log.warning('Benutzer nicht angemeldet oder ID-Konflikt');
        return;
      }

      // Hole die aktuellen Geräte-IDs des Benutzers
      final response =
          await client
              .from('profiles')
              .select('device_ids')
              .eq('id', userId)
              .maybeSingle();

      List<String> deviceIds = [];
      if (response != null && response['device_ids'] != null) {
        if (response['device_ids'] is List) {
          deviceIds = List<String>.from(response['device_ids']);
        } else if (response['device_ids'] is String) {
          // Versuche, JSON zu parsen, falls es als String gespeichert ist
          try {
            final List<dynamic> parsedIds = jsonDecode(response['device_ids']);
            deviceIds = parsedIds.map((id) => id.toString()).toList();
          } catch (e) {
            _log.warning('Fehler beim Parsen der Geräte-IDs: $e');
          }
        }
      }

      // Füge die Geräte-ID hinzu, wenn sie noch nicht vorhanden ist
      if (!deviceIds.contains(deviceId)) {
        deviceIds.add(deviceId);

        // Aktualisiere die Geräte-IDs in Supabase
        await client
            .from('profiles')
            .update({'device_ids': deviceIds})
            .eq('id', userId);

        _log.info('Gerät für Benutzer $userId registriert: $deviceId');
      } else {
        _log.fine('Gerät bereits für Benutzer $userId registriert: $deviceId');
      }

      // Speichere die Benutzer-ID lokal
      await saveLastUserId(userId);
    } catch (e) {
      _log.severe('Fehler bei der Geräteregistrierung: $e');
    }
  }

  /// Prüft, ob das aktuelle Gerät für einen Benutzer registriert ist
  Future<bool> isDeviceRegisteredForUser(String userId) async {
    try {
      final deviceId = await getDeviceId();
      final client = Supabase.instance.client;

      // Hole die Geräte-IDs des Benutzers
      final response =
          await client
              .from('profiles')
              .select('device_ids')
              .eq('id', userId)
              .maybeSingle();

      if (response != null && response['device_ids'] != null) {
        if (response['device_ids'] is List) {
          final deviceIds = List<String>.from(response['device_ids']);
          return deviceIds.contains(deviceId);
        } else if (response['device_ids'] is String) {
          // Versuche, JSON zu parsen, falls es als String gespeichert ist
          try {
            final List<dynamic> parsedIds = jsonDecode(response['device_ids']);
            final deviceIds = parsedIds.map((id) => id.toString()).toList();
            return deviceIds.contains(deviceId);
          } catch (e) {
            _log.warning('Fehler beim Parsen der Geräte-IDs: $e');
          }
        }
      }
    } catch (e) {
      _log.severe('Fehler bei der Prüfung der Geräteregistrierung: $e');
    }

    return false;
  }

  /// Entfernt das aktuelle Gerät aus der Liste der registrierten Geräte eines Benutzers
  Future<void> unregisterDeviceForUser(String userId) async {
    try {
      final deviceId = await getDeviceId();
      final client = Supabase.instance.client;

      // Hole die aktuellen Geräte-IDs des Benutzers
      final response =
          await client
              .from('profiles')
              .select('device_ids')
              .eq('id', userId)
              .maybeSingle();

      if (response != null && response['device_ids'] != null) {
        List<String> deviceIds = [];
        if (response['device_ids'] is List) {
          deviceIds = List<String>.from(response['device_ids']);
        } else if (response['device_ids'] is String) {
          // Versuche, JSON zu parsen, falls es als String gespeichert ist
          try {
            final List<dynamic> parsedIds = jsonDecode(response['device_ids']);
            deviceIds = parsedIds.map((id) => id.toString()).toList();
          } catch (e) {
            _log.warning('Fehler beim Parsen der Geräte-IDs: $e');
            return;
          }
        }

        // Entferne die Geräte-ID, wenn sie vorhanden ist
        if (deviceIds.contains(deviceId)) {
          deviceIds.remove(deviceId);

          // Aktualisiere die Geräte-IDs in Supabase
          await client
              .from('profiles')
              .update({'device_ids': deviceIds})
              .eq('id', userId);

          _log.info('Gerät für Benutzer $userId entfernt: $deviceId');
        }
      }
    } catch (e) {
      _log.severe('Fehler bei der Geräteabmeldung: $e');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Zentrale Klasse für die Behandlung von Authentifizierungsfehlern
/// Bietet benutzerfreundliche Fehlermeldungen und einheitliche Behandlung
class AuthErrorHandler {
  /// Konvertiert AuthException in benutzerfreundliche deutsche Fehlermeldungen
  static String getLocalizedErrorMessage(AuthException error) {
    final message = error.message.toLowerCase();
    
    // Login-Fehler
    if (message.contains('invalid login credentials') ||
        message.contains('invalid email or password') ||
        message.contains('email not confirmed') ||
        message.contains('invalid credentials')) {
      return 'E-Mail oder Passwort ist falsch. Bitte überprüfe deine Eingaben.';
    }
    
    // Registrierungs-Fehler
    if (message.contains('user already registered') ||
        message.contains('already been registered') ||
        message.contains('email address is already registered') ||
        message.contains('signup_disabled')) {
      return 'Diese E-Mail-Adresse ist bereits registriert.';
    }
    
    // Passwort-Fehler
    if (message.contains('password is too weak') ||
        message.contains('password should be at least')) {
      return 'Das Passwort ist zu schwach. Verwende mindestens 6 Zeichen.';
    }
    
    // E-Mail-Fehler
    if (message.contains('invalid email') ||
        message.contains('email is invalid')) {
      return 'Ungültige E-Mail-Adresse.';
    }
    
    // Rate Limiting
    if (message.contains('email rate limit exceeded') ||
        message.contains('too many requests')) {
      return 'Zu viele Anfragen. Bitte warte 5-10 Minuten und versuche es erneut.';
    }
    
    if (message.contains('for security purposes, you can only request this after')) {
      final match = RegExp(r'after (\d+) seconds').firstMatch(message);
      final seconds = match?.group(1) ?? '60';
      return 'Aus Sicherheitsgründen kannst du erst nach $seconds Sekunden eine neue Anfrage stellen.';
    }
    
    // Netzwerk-Fehler
    if (message.contains('network error') ||
        message.contains('connection failed') ||
        message.contains('timeout')) {
      return 'Netzwerkfehler. Bitte überprüfe deine Internetverbindung.';
    }
    
    // OAuth-Fehler
    if (message.contains('oauth') ||
        message.contains('provider')) {
      return 'Fehler bei der Anmeldung mit dem externen Anbieter. Bitte versuche es erneut.';
    }
    
    // E-Mail-Bestätigung
    if (message.contains('email not confirmed') ||
        message.contains('confirm your email')) {
      return 'Bitte bestätige deine E-Mail-Adresse über den Link in deinem Postfach.';
    }
    
    // Fallback für unbekannte Fehler
    return 'Ein Fehler ist aufgetreten: ${error.message}';
  }
  
  /// Zeigt eine benutzerfreundliche Fehlermeldung als SnackBar an
  static void showErrorSnackBar(BuildContext context, AuthException error) {
    if (!context.mounted) return;
    
    final message = getLocalizedErrorMessage(error);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Zeigt eine allgemeine Fehlermeldung als SnackBar an
  static void showGenericErrorSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Zeigt eine Erfolgsmeldung als SnackBar an
  static void showSuccessSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Prüft, ob ein Fehler eine bereits existierende E-Mail betrifft
  static bool isEmailAlreadyRegisteredError(AuthException error) {
    final message = error.message.toLowerCase();
    return message.contains('user already registered') ||
           message.contains('already been registered') ||
           message.contains('email address is already registered') ||
           message.contains('signup_disabled');
  }
  
  /// Prüft, ob ein Fehler ein Rate-Limiting-Problem ist
  static bool isRateLimitError(AuthException error) {
    final message = error.message.toLowerCase();
    return message.contains('email rate limit exceeded') ||
           message.contains('too many requests') ||
           message.contains('for security purposes, you can only request this after');
  }
  
  /// Extrahiert die Wartezeit aus Rate-Limiting-Fehlern
  static int? getRateLimitWaitTime(AuthException error) {
    final message = error.message;
    final match = RegExp(r'after (\d+) seconds').firstMatch(message);
    if (match != null) {
      return int.tryParse(match.group(1) ?? '');
    }
    return null;
  }
}

/// Erweiterte Fehlermeldungen für spezifische Szenarien
class AuthErrorMessages {
  static const String invalidCredentials = 
      'E-Mail oder Passwort ist falsch. Bitte überprüfe deine Eingaben.';
  
  static const String emailAlreadyRegistered = 
      'Diese E-Mail-Adresse ist bereits registriert.';
  
  static const String weakPassword = 
      'Das Passwort ist zu schwach. Verwende mindestens 6 Zeichen mit Buchstaben und Zahlen.';
  
  static const String invalidEmail = 
      'Ungültige E-Mail-Adresse. Bitte überprüfe das Format.';
  
  static const String networkError = 
      'Netzwerkfehler. Bitte überprüfe deine Internetverbindung und versuche es erneut.';
  
  static const String emailNotConfirmed = 
      'Bitte bestätige deine E-Mail-Adresse über den Link in deinem Postfach.';
  
  static const String rateLimitExceeded = 
      'Zu viele Anfragen. Bitte warte einen Moment und versuche es erneut.';
  
  static const String passwordResetSent = 
      'Falls ein Account mit dieser E-Mail existiert, wurde eine E-Mail zum Zurücksetzen gesendet.';
  
  static const String registrationSuccess = 
      'Registrierung erfolgreich! Bitte prüfe dein E-Mail-Postfach und klicke auf den Bestätigungslink.';
  
  static const String loginSuccess = 
      'Anmeldung erfolgreich!';
  
  static const String unexpectedError = 
      'Ein unerwarteter Fehler ist aufgetreten. Bitte versuche es später erneut.';
}

import 'package:shared_preferences/shared_preferences.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Eine Klasse zum Verwalten des Suchverlaufs
class SearchHistoryManager {
  static const String _searchHistoryKey = 'job_search_history';
  static const int _maxHistoryItems =
      4; // Maximale Anzahl der gespeicherten Suchbegriffe
  static final _log = getLogger('SearchHistoryManager');

  /// Speichert einen Suchbegriff im Verlauf
  static Future<bool> addSearchTerm(String term) async {
    if (term.isEmpty) return false;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Lade den aktuellen Verlauf
      final List<String> history = await getSearchHistory();

      // Entferne den Begriff, falls er bereits existiert (um Duplikate zu vermeiden)
      history.removeWhere((item) => item.toLowerCase() == term.toLowerCase());

      // Füge den neuen Begriff am Anfang hinzu
      history.insert(0, term);

      // Begrenze die Anzahl der Einträge
      if (history.length > _maxHistoryItems) {
        history.removeRange(_maxHistoryItems, history.length);
      }

      // Speichere den aktualisierten Verlauf
      return await prefs.setStringList(_searchHistoryKey, history);
    } catch (e) {
      _log.e('Fehler beim Speichern des Suchverlaufs: $e');
      return false;
    }
  }

  /// Gibt den Suchverlauf zurück
  static Future<List<String>> getSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      _log.e('Fehler beim Laden des Suchverlaufs: $e');
      return [];
    }
  }

  /// Löscht den gesamten Suchverlauf
  static Future<bool> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_searchHistoryKey);
    } catch (e) {
      _log.e('Fehler beim Löschen des Suchverlaufs: $e');
      return false;
    }
  }

  /// Entfernt einen bestimmten Suchbegriff aus dem Verlauf
  static Future<bool> removeSearchTerm(String term) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Lade den aktuellen Verlauf
      final List<String> history = await getSearchHistory();

      // Entferne den Begriff
      history.removeWhere((item) => item.toLowerCase() == term.toLowerCase());

      // Speichere den aktualisierten Verlauf
      return await prefs.setStringList(_searchHistoryKey, history);
    } catch (e) {
      _log.e('Fehler beim Entfernen des Suchbegriffs: $e');
      return false;
    }
  }
}

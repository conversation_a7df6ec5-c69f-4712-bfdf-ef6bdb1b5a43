import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logging/logging.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// ProfileBackupManager ist verantwortlich für die lokale und Cloud-Sicherung und Wiederherstellung von Profildaten.
class ProfileBackupManager {
  static final ProfileBackupManager _instance =
      ProfileBackupManager._internal();
  final Logger _log = Logger('ProfileBackupManager');

  // Logger-Methoden-Wrapper für Kompatibilität
  void _logInfo(String message) => _log.info(message);
  void _logWarning(String message) => _log.warning(message);
  void _logError(String message) => _log.severe(message);
  void _logDebug(String message) => _log.fine(message);

  factory ProfileBackupManager() => _instance;

  ProfileBackupManager._internal();

  static const String _lastBackupTimestampKey = 'last_profile_backup_timestamp';
  static const String _backupFileNamePrefix = 'profile_backup_';

  /// Erstellt ein Backup des Benutzerprofils
  Future<bool> backupProfile(UserProfile profile) async {
    try {
      if (profile.id == null || profile.id!.isEmpty) {
        _logWarning('Kann kein Backup erstellen: Profil-ID ist leer');
        return false;
      }

      // Konvertiere das Profil in JSON
      final profileJson = profile.toJson();
      final profileJsonString = jsonEncode(profileJson);

      // Speichere das Backup in einer Datei
      final backupFile = await _getBackupFile(profile.id!);
      await backupFile.writeAsString(profileJsonString);

      // Aktualisiere den Zeitstempel des letzten Backups
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        _lastBackupTimestampKey,
        DateTime.now().millisecondsSinceEpoch,
      );

      _logInfo('Profil-Backup erstellt für ID: ${profile.id}');

      // Versuche, das Backup auch in Supabase zu speichern
      try {
        final client = Supabase.instance.client;

        // Prüfe, ob der Benutzer angemeldet ist
        if (client.auth.currentUser?.id != profile.id) {
          _logWarning(
            'Benutzer nicht angemeldet oder ID-Konflikt, kein Supabase-Backup erstellt',
          );
          return true; // Lokales Backup wurde trotzdem erstellt
        }

        // Speichere das Backup in Supabase
        await client.from('profile_backups').insert({
          'profile_id': profile.id,
          'data': profileJson,
          'created_at': DateTime.now().toIso8601String(),
        });

        _logInfo('Profil-Backup in Supabase gespeichert für ID: ${profile.id}');
      } catch (e) {
        _logWarning('Fehler beim Speichern des Profil-Backups in Supabase: $e');
        // Ignoriere den Fehler, da das lokale Backup bereits erstellt wurde
      }

      return true;
    } catch (e) {
      _logError('Fehler beim Erstellen des Profil-Backups: $e');
      return false;
    }
  }

  /// Stellt ein Benutzerprofil aus einem Backup wieder her
  Future<UserProfile?> restoreProfile(String userId) async {
    try {
      // Versuche zuerst, das Backup aus Supabase zu laden
      try {
        final client = Supabase.instance.client;

        // Prüfe, ob der Benutzer angemeldet ist
        if (client.auth.currentUser?.id != userId) {
          _logWarning(
            'Benutzer nicht angemeldet oder ID-Konflikt, kein Supabase-Backup geladen',
          );
        } else {
          // Hole das neueste Backup aus Supabase
          final response = await client
              .from('profile_backups')
              .select('data, created_at')
              .eq('profile_id', userId)
              .order('created_at', ascending: false)
              .limit(1);

          if (response.isNotEmpty && response[0]['data'] != null) {
            final data = response[0]['data'];
            Map<String, dynamic> profileData;

            if (data is Map) {
              profileData = Map<String, dynamic>.from(data);
            } else if (data is String) {
              profileData = jsonDecode(data) as Map<String, dynamic>;
            } else {
              _logWarning(
                'Unerwarteter Datentyp für Backup-Daten: ${data.runtimeType}',
              );
              // Fahre mit lokalem Backup fort
              throw Exception('Unerwarteter Datentyp für Backup-Daten');
            }

            // Erstelle ein UserProfile aus den Daten
            final profile = UserProfile.fromJson(profileData);

            // Prüfe, ob die ID im Backup mit der angeforderten ID übereinstimmt
            if (profile.id != userId) {
              _logWarning(
                'ID-Konflikt im Supabase-Backup: ${profile.id} != $userId',
              );
              // Fahre mit lokalem Backup fort
              throw Exception('ID-Konflikt im Supabase-Backup');
            }

            // Aktualisiere das lokale Backup
            await backupProfile(profile);

            _logInfo(
              'Profil aus Supabase-Backup wiederhergestellt für ID: $userId',
            );
            return profile;
          }
        }
      } catch (e) {
        _logWarning('Fehler beim Laden des Profil-Backups aus Supabase: $e');
        // Fahre mit lokalem Backup fort
      }

      // Wenn kein Supabase-Backup geladen werden konnte, versuche es mit dem lokalen Backup
      // Prüfe, ob ein lokales Backup existiert
      final backupFile = await _getBackupFile(userId);
      if (!await backupFile.exists()) {
        _logWarning('Kein lokales Backup gefunden für Benutzer-ID: $userId');
        return null;
      }

      // Lese das Backup aus der Datei
      final profileJsonString = await backupFile.readAsString();
      final profileJson = jsonDecode(profileJsonString) as Map<String, dynamic>;

      // Konvertiere das JSON in ein UserProfile-Objekt
      final profile = UserProfile.fromJson(profileJson);

      // Prüfe, ob die ID im Backup mit der angeforderten ID übereinstimmt
      if (profile.id != userId) {
        _logWarning('ID-Konflikt im lokalen Backup: ${profile.id} != $userId');
        return null;
      }

      _logInfo('Profil aus lokalem Backup wiederhergestellt für ID: $userId');
      return profile;
    } catch (e) {
      _logError('Fehler bei der Wiederherstellung des Profil-Backups: $e');
      return null;
    }
  }

  /// Prüft, ob ein Backup für einen Benutzer existiert
  Future<bool> hasBackup(String userId) async {
    bool hasLocalBackup = false;
    bool hasCloudBackup = false;

    // Prüfe lokales Backup
    try {
      final backupFile = await _getBackupFile(userId);
      hasLocalBackup = await backupFile.exists();
      if (hasLocalBackup) {
        _logInfo('Lokales Backup gefunden für Benutzer-ID: $userId');
      }
    } catch (e) {
      _logError('Fehler bei der Prüfung des lokalen Profil-Backups: $e');
      // Fahre mit der Prüfung des Cloud-Backups fort
    }

    // Wenn bereits ein lokales Backup gefunden wurde, können wir direkt zurückkehren
    if (hasLocalBackup) {
      return true;
    }

    // Prüfe Cloud-Backup
    try {
      final client = Supabase.instance.client;

      // Prüfe, ob der Benutzer angemeldet ist
      if (client.auth.currentUser?.id != userId) {
        _logWarning(
          'Benutzer nicht angemeldet oder ID-Konflikt, kein Supabase-Backup geprüft',
        );
      } else {
        // Prüfe, ob ein Backup in Supabase existiert
        final response = await client
            .from('profile_backups')
            .select('id')
            .eq('profile_id', userId)
            .limit(1);

        hasCloudBackup = response.isNotEmpty;
        if (hasCloudBackup) {
          _logInfo('Supabase-Backup gefunden für Benutzer-ID: $userId');
        }
      }
    } catch (e) {
      _logError('Fehler bei der Prüfung des Supabase-Profil-Backups: $e');
      // Ignoriere den Fehler, wenn bereits ein lokales Backup gefunden wurde
    }

    return hasLocalBackup || hasCloudBackup;
  }

  /// Löscht ein Backup für einen Benutzer
  Future<bool> deleteBackup(String userId) async {
    bool localSuccess = false;
    bool cloudSuccess = false;

    try {
      // Lösche das lokale Backup
      final backupFile = await _getBackupFile(userId);
      if (await backupFile.exists()) {
        await backupFile.delete();
        _logInfo('Lokales Profil-Backup gelöscht für ID: $userId');
        localSuccess = true;
      } else {
        _logInfo('Kein lokales Profil-Backup gefunden für ID: $userId');
        localSuccess =
            true; // Es gab nichts zu löschen, also ist es erfolgreich
      }
    } catch (e) {
      _logError('Fehler beim Löschen des lokalen Profil-Backups: $e');
      // Fahre mit dem Löschen des Cloud-Backups fort
    }

    try {
      // Lösche das Backup in Supabase
      final client = Supabase.instance.client;

      // Prüfe, ob der Benutzer angemeldet ist
      if (client.auth.currentUser?.id != userId) {
        _logWarning(
          'Benutzer nicht angemeldet oder ID-Konflikt, kein Supabase-Backup gelöscht',
        );
        cloudSuccess =
            true; // Wir können nicht löschen, aber das ist kein Fehler
      } else {
        // Lösche alle Backups für diesen Benutzer in Supabase
        await client.from('profile_backups').delete().eq('profile_id', userId);
        _logInfo('Supabase Profil-Backups gelöscht für ID: $userId');
        cloudSuccess = true;
      }
    } catch (e) {
      _logError('Fehler beim Löschen der Profil-Backups in Supabase: $e');
      // Ignoriere den Fehler, wenn das lokale Backup erfolgreich gelöscht wurde
    }

    return localSuccess || cloudSuccess;
  }

  /// Holt den Zeitstempel des letzten Backups
  Future<DateTime?> getLastBackupTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastBackupTimestampKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      _logError('Fehler beim Abrufen des Backup-Zeitstempels: $e');
      return null;
    }
  }

  /// Holt die Backup-Datei für einen Benutzer
  Future<File> _getBackupFile(String userId) async {
    final directory = await _getBackupDirectory();
    return File('${directory.path}/$_backupFileNamePrefix$userId.json');
  }

  /// Holt das Verzeichnis für Backups
  Future<Directory> _getBackupDirectory() async {
    if (kIsWeb) {
      throw UnsupportedError(
        'Lokale Datei-Backups werden im Web nicht unterstützt',
      );
    }

    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/profile_backups');

    // Erstelle das Verzeichnis, wenn es nicht existiert
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    return backupDir;
  }

  /// Löscht alle Backups
  Future<void> deleteAllBackups() async {
    // Lösche lokale Backups
    try {
      final backupDir = await _getBackupDirectory();
      if (await backupDir.exists()) {
        await backupDir.delete(recursive: true);
        _logInfo('Alle lokalen Profil-Backups gelöscht');
      }
    } catch (e) {
      _logError('Fehler beim Löschen aller lokalen Profil-Backups: $e');
    }

    // Lösche Cloud-Backups
    try {
      final client = Supabase.instance.client;

      // Prüfe, ob der Benutzer angemeldet ist
      if (client.auth.currentUser == null) {
        _logWarning('Benutzer nicht angemeldet, keine Supabase-Backups gelöscht');
        return;
      }

      // Lösche alle Backups für den aktuellen Benutzer in Supabase
      await client
          .from('profile_backups')
          .delete()
          .eq('profile_id', client.auth.currentUser!.id);
      _logInfo(
        'Alle Supabase Profil-Backups für den aktuellen Benutzer gelöscht',
      );
    } catch (e) {
      _logError('Fehler beim Löschen aller Supabase-Profil-Backups: $e');
    }
  }
}

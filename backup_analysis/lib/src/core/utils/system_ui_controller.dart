import 'dart:io';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Eine Klasse zur Steuerung der System-UI-Elemente wie Navigationsleiste und Statusleiste
class SystemUiController {
  static final log = getLogger('SystemUiController');

  /// Versteckt die Navigationsleiste NICHT mehr - stellt sicher, dass sie immer sichtbar ist
  ///
  /// Diese Methode wurde geändert, um die Navigationsleiste IMMER sichtbar zu halten,
  /// auch während der Werbeanzeige.
  static Future<void> hideNavigationBar() async {
    if (!Platform.isAndroid) {
      log.w('hideNavigationBar() wird nur auf Android unterstützt');
      return;
    }

    log.i('WICHTIG: Navigationsleiste bleibt absichtlich sichtbar');

    try {
      // <PERSON><PERSON> sicher, dass die Navigationsleiste immer sichtbar ist
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      // Kurze Verzögerung, um sicherzustellen, dass die Änderung wirksam wird
      await Future.delayed(const Duration(milliseconds: 100));

      // Setze die Overlay-Stile für sichtbare Navigationsleiste
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          // Schwarze Farbe für die Navigationsleiste
          systemNavigationBarColor: Colors.black,
          systemNavigationBarDividerColor: Colors.transparent,
          statusBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.light,
        ),
      );

      // Verwende den Method Channel als zusätzliche Unterstützung
      // Dies ist wichtig, da es direkt mit der nativen Android-API kommuniziert
      try {
        const MethodChannel channel = MethodChannel(
          'com.einsteinai.app/system_ui',
        );
        // Wir rufen showNavigationBar auf, um sicherzustellen, dass die Navigationsleiste sichtbar ist
        await channel.invokeMethod('showNavigationBar');
        log.i('Method Channel für showNavigationBar erfolgreich aufgerufen');
      } catch (e) {
        // Ignoriere Fehler beim Method Channel
        log.d('Method Channel für showNavigationBar nicht verfügbar: $e');
      }

      // Stelle sicher, dass der UI-Modus wirklich auf edgeToEdge gesetzt ist
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      log.i('Navigationsleiste bleibt sichtbar');
    } catch (e) {
      log.e('Fehler beim Anzeigen der Navigationsleiste: $e');
    }
  }

  /// Zeigt die Navigationsleiste wieder an (nur Android)
  ///
  /// Diese Methode stellt die Navigationsleiste wieder her, nachdem sie mit
  /// [hideNavigationBar] versteckt wurde.
  static Future<void> showNavigationBar() async {
    if (!Platform.isAndroid) {
      log.w('showNavigationBar() wird nur auf Android unterstützt');
      return;
    }

    log.i('Zeige Navigationsleiste wieder an');

    try {
      // WICHTIG: Stelle sicher, dass alle Overlays angezeigt werden
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      // Kurze Verzögerung, um sicherzustellen, dass die Änderung wirksam wird
      await Future.delayed(const Duration(milliseconds: 100));

      // Stelle den normalen UI-Modus wieder her. EdgeToEdge ist oft gewünscht.
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

      // Kurze Verzögerung, um sicherzustellen, dass die Änderung wirksam wird
      await Future.delayed(const Duration(milliseconds: 100));

      // Optional: Setze deine Standard-Overlay-Stile zurück
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          // Deine normalen App-Farben/Stile
          systemNavigationBarColor: Colors.black,
          statusBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.light,
        ),
      );

      // Verwende den Method Channel als zusätzliche Unterstützung
      // Dies ist wichtig, da es direkt mit der nativen Android-API kommuniziert
      try {
        const MethodChannel channel = MethodChannel(
          'com.einsteinai.app/system_ui',
        );
        await channel.invokeMethod('showNavigationBar');
        log.i('Method Channel für showNavigationBar erfolgreich aufgerufen');
      } catch (e) {
        // Ignoriere Fehler beim Method Channel
        log.d('Method Channel für showNavigationBar nicht verfügbar: $e');
      }

      // Stelle sicher, dass der UI-Modus wirklich auf edgeToEdge gesetzt ist
      // Dies ist ein zusätzlicher Sicherheitsschritt
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      // Setze einen Timer, um sicherzustellen, dass die Navigationsleiste wirklich angezeigt wird
      Timer(const Duration(milliseconds: 500), () {
        SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
          overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
        );
        log.i('Navigationsleiste nach Verzögerung erneut wiederhergestellt');
      });

      log.i('Navigationsleiste erfolgreich wiederhergestellt');
    } catch (e) {
      log.e('Fehler beim Anzeigen der Navigationsleiste: $e');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:ki_test/src/core/constants/screen_size_constants.dart';
import 'package:ki_test/src/core/utils/responsive_utils.dart';

/// Eine Klasse für App-spezifische responsive Anpassungen
class AppResponsive {
  /// Singleton-Instanz
  static final AppResponsive _instance = AppResponsive._internal();
  factory AppResponsive() => _instance;
  AppResponsive._internal();

  /// Gibt einen Wert basierend auf dem Gerätetyp zurück
  static T value<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
  
  /// Gibt einen Wert basierend auf der Bildschirmorientierung zurück
  static T orientationValue<T>({
    required BuildContext context,
    required T portrait,
    required T landscape,
  }) {
    final orientation = MediaQuery.of(context).orientation;
    
    return orientation == Orientation.portrait ? portrait : landscape;
  }
  
  /// Gibt einen responsiven Wert für die Schriftgröße zurück
  static double fontSize(BuildContext context, double size) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return size;
      case DeviceType.tablet:
        return size * 1.1;
      case DeviceType.desktop:
        return size * 1.2;
    }
  }
  
  /// Gibt einen responsiven Wert für die Icongrößen zurück
  static double iconSize(BuildContext context, double size) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return size;
      case DeviceType.tablet:
        return size * 1.2;
      case DeviceType.desktop:
        return size * 1.5;
    }
  }
  
  /// Gibt einen responsiven Wert für Padding zurück
  static EdgeInsets padding(BuildContext context, {
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double multiplier;
    switch (deviceType) {
      case DeviceType.mobile:
        multiplier = 1.0;
        break;
      case DeviceType.tablet:
        multiplier = 1.5;
        break;
      case DeviceType.desktop:
        multiplier = 2.0;
        break;
    }
    
    return EdgeInsets.only(
      left: (left ?? horizontal ?? all ?? 0) * multiplier,
      top: (top ?? vertical ?? all ?? 0) * multiplier,
      right: (right ?? horizontal ?? all ?? 0) * multiplier,
      bottom: (bottom ?? vertical ?? all ?? 0) * multiplier,
    );
  }
  
  /// Gibt einen responsiven Wert für den Abstand zwischen Elementen zurück
  static double spacing(BuildContext context, double value) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return value;
      case DeviceType.tablet:
        return value * 1.5;
      case DeviceType.desktop:
        return value * 2.0;
    }
  }
  
  /// Gibt einen responsiven Wert für die Größe von Buttons zurück
  static Size buttonSize(BuildContext context, {double? width, double? height}) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double widthMultiplier;
    double heightMultiplier;
    
    switch (deviceType) {
      case DeviceType.mobile:
        widthMultiplier = 1.0;
        heightMultiplier = 1.0;
        break;
      case DeviceType.tablet:
        widthMultiplier = 1.2;
        heightMultiplier = 1.1;
        break;
      case DeviceType.desktop:
        widthMultiplier = 1.5;
        heightMultiplier = 1.2;
        break;
    }
    
    return Size(
      (width ?? ScreenSizeConstants.mobileButtonWidth) * widthMultiplier,
      (height ?? ScreenSizeConstants.mobileButtonHeight) * heightMultiplier,
    );
  }
  
  /// Gibt einen responsiven Wert für die Größe von Input-Feldern zurück
  static double inputHeight(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return ScreenSizeConstants.mobileInputHeight;
      case DeviceType.tablet:
        return ScreenSizeConstants.tabletInputHeight;
      case DeviceType.desktop:
        return ScreenSizeConstants.desktopInputHeight;
    }
  }
  
  /// Gibt einen responsiven Wert für den Border-Radius zurück
  static double borderRadius(BuildContext context, double value) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return value;
      case DeviceType.tablet:
        return value * 1.2;
      case DeviceType.desktop:
        return value * 1.5;
    }
  }
  
  /// Gibt einen responsiven Wert für die Größe von Karten zurück
  static double cardPadding(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return ScreenSizeConstants.mobileCardPadding;
      case DeviceType.tablet:
        return ScreenSizeConstants.tabletCardPadding;
      case DeviceType.desktop:
        return ScreenSizeConstants.desktopCardPadding;
    }
  }
  
  /// Gibt einen responsiven Wert für die maximale Breite von Containern zurück
  static double maxContainerWidth(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return ScreenSizeConstants.maxMobileContainerWidth;
      case DeviceType.tablet:
        return ScreenSizeConstants.maxTabletContainerWidth;
      case DeviceType.desktop:
        return ScreenSizeConstants.maxDesktopContainerWidth;
    }
  }
  
  /// Gibt einen responsiven Wert für horizontales Padding zurück
  static EdgeInsets horizontalPadding(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double padding;
    switch (deviceType) {
      case DeviceType.mobile:
        padding = ScreenSizeConstants.mobileHorizontalPadding;
        break;
      case DeviceType.tablet:
        padding = ScreenSizeConstants.tabletHorizontalPadding;
        break;
      case DeviceType.desktop:
        padding = ScreenSizeConstants.desktopHorizontalPadding;
        break;
    }
    
    return EdgeInsets.symmetric(horizontal: padding);
  }
  
  /// Gibt einen responsiven Wert für vertikales Padding zurück
  static EdgeInsets verticalPadding(BuildContext context) {
    final deviceType = ResponsiveUtils().getDeviceType(context);
    
    double padding;
    switch (deviceType) {
      case DeviceType.mobile:
        padding = ScreenSizeConstants.mobileVerticalPadding;
        break;
      case DeviceType.tablet:
        padding = ScreenSizeConstants.tabletVerticalPadding;
        break;
      case DeviceType.desktop:
        padding = ScreenSizeConstants.desktopVerticalPadding;
        break;
    }
    
    return EdgeInsets.symmetric(vertical: padding);
  }
  
  /// Gibt ein responsives Padding zurück, das die Navigationsleiste berücksichtigt
  static EdgeInsets safeAreaPadding(BuildContext context, {
    double? left,
    double? top,
    double? right,
    double? bottom,
    bool includeNavigationBar = true,
  }) {
    final mediaQuery = MediaQuery.of(context);
    
    return EdgeInsets.only(
      left: (left ?? 0) + mediaQuery.padding.left,
      top: (top ?? 0) + mediaQuery.padding.top,
      right: (right ?? 0) + mediaQuery.padding.right,
      bottom: (bottom ?? 0) + (includeNavigationBar ? mediaQuery.padding.bottom : 0),
    );
  }
}

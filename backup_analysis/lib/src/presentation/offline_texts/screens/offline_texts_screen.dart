import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../application/providers/services_providers.dart';
import '../../../domain/entities/extracted_job_text.dart';
import '../../../core/theme/app_theme.dart';

/// Screen zur Anzeige aller extrahierten Offline-Texte
class OfflineTextsScreen extends HookConsumerWidget {
  const OfflineTextsScreen({super.key});

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Material(
            color: Colors.black.withValues(alpha: 0.5),
            child: Center(
              child: GestureDetector(
                onTap: () {}, // Verhindert das Schließen beim <PERSON> auf den Dialog
                child: Container(
                  margin: const EdgeInsets.all(32),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1A1A1A),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppTheme.primaryLightColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Info',
                              style: TextStyle(
                                color: AppTheme.primaryLightColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Manchmal werden Jobbeschreibungen gelöscht und du hast ein Vorstellungsgespräch, aber weißt nicht mehr welche Anforderungen das waren. Hier findest du alle offline gespeicherten Stellenausschreibungen. 😁',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final extractedTexts = useState<List<ExtractedJobText>>([]);
    final isLoading = useState(true);
    final error = useState<String?>(null);

    // Such-Controller und State
    final searchController = useTextEditingController();
    final filteredTexts = useState<List<ExtractedJobText>>([]);
    final isSearchActive = useState(false);

    // Lade extrahierte Texte beim ersten Aufbau
    useEffect(() {
      _loadExtractedTexts(ref, extractedTexts, isLoading, error);
      return null;
    }, []);

    // Filter-Logik
    void applySearch() {
      final query = searchController.text.toLowerCase().trim();

      if (query.isEmpty) {
        // Keine Suche - zeige alle Texte (bereits von Supabase sortiert)
        filteredTexts.value = extractedTexts.value;
        isSearchActive.value = false;
        return;
      }

      isSearchActive.value = true;
      final filtered = extractedTexts.value.where((text) {
        return text.jobTitle.toLowerCase().contains(query) ||
               text.companyName.toLowerCase().contains(query) ||
               text.extractedText.toLowerCase().contains(query);
      }).toList();

      // Die gefilterten Ergebnisse behalten die ursprüngliche Sortierung
      // (da extractedTexts.value bereits sortiert ist)
      filteredTexts.value = filtered;
    }

    // Listener für Textänderungen
    useEffect(() {
      void listener() => applySearch();
      searchController.addListener(listener);
      return () => searchController.removeListener(listener);
    }, [searchController]);

    // Aktualisiere gefilterte Texte wenn sich die Original-Texte ändern
    useEffect(() {
      // Die Daten sind bereits von Supabase sortiert (neueste zuerst)
      // Setze filteredTexts auf die bereits sortierten extractedTexts
      filteredTexts.value = extractedTexts.value;

      // Wenn eine Suche aktiv ist, wende sie erneut an
      if (searchController.text.isNotEmpty) {
        applySearch();
      }
      return null;
    }, [extractedTexts.value]);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: AppTheme.backgroundDarkColor,
      appBar: AppBar(
        title: const Text(
          'Offline Seiten',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Aktualisieren',
            onPressed: () => _loadExtractedTexts(ref, extractedTexts, isLoading, error),
          ),
        ],
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: SafeArea(
        top: true,
        bottom: false,
        child: _buildBody(
          context,
          filteredTexts.value,
          isLoading.value,
          error.value,
          searchController,
          isSearchActive.value,
        ),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    List<ExtractedJobText> texts,
    bool isLoading,
    String? error,
    TextEditingController searchController,
    bool isSearchActive,
  ) {
    if (isLoading) {
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.amber),
              SizedBox(height: 16),
              Text(
                'Lade extrahierte Texte...',
                style: TextStyle(color: Colors.white70, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    if (error != null) {
      return Container(
        color: AppTheme.backgroundDarkColor,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text(
                'Fehler beim Laden der Texte',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: const TextStyle(color: Colors.white70),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _loadExtractedTexts,
                child: const Text('Erneut versuchen'),
              ),
            ],
          ),
        ),
      );
    }

    if (texts.isEmpty) {
      return Container(
        color: AppTheme.backgroundDarkColor,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.text_snippet_outlined, size: 64, color: Colors.white54),
              const SizedBox(height: 16),
              const Text(
                'Keine extrahierten Texte gefunden',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  'Füge Jobs zu Favoriten hinzu oder bewerbe dich,\num automatisch Texte zu extrahieren.',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white70),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: AppTheme.backgroundDarkColor,
      child: Column(
        children: [
          // Statistiken Header mit Profile-Style Design
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.08),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header mit Gradient
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withValues(alpha: 0.1),
                        AppTheme.primaryLightColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.offline_bolt,
                          color: AppTheme.primaryLightColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Text(
                                    'Offline Seiten',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w700,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                GestureDetector(
                                  onTap: () => _showInfoDialog(context),
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Icon(
                                      Icons.help_outline,
                                      color: AppTheme.primaryLightColor,
                                      size: 22,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${texts.length} Jobs offline verfügbar',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Suchfeld
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: TextField(
              controller: searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Jobs durchsuchen...',
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppTheme.primaryLightColor,
                  size: 20,
                ),
                suffixIcon: isSearchActive
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withValues(alpha: 0.7),
                          size: 20,
                        ),
                        onPressed: () {
                          searchController.clear();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          // Liste der extrahierten Texte
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              itemCount: texts.length,
              itemBuilder: (context, index) {
                final text = texts[index];
                return _buildTextItem(context, text);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextItem(BuildContext context, ExtractedJobText text) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showJobDetailModal(context, text),
          borderRadius: BorderRadius.circular(20),
          child: Column(
            children: [
              // Header mit Gradient
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.primaryColor.withValues(alpha: 0.1),
                      AppTheme.primaryLightColor.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.work_outline,
                        color: AppTheme.primaryLightColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        text.jobTitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.white54,
                    ),
                  ],
                ),
              ),
              // Content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.business,
                          size: 16,
                          color: AppTheme.primaryLightColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            text.companyName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: AppTheme.primaryLightColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            text.location ?? 'Standort unbekannt',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppTheme.primaryLightColor, width: 1),
                          ),
                          child: Text(
                            'Offline verfügbar',
                            style: TextStyle(
                              color: AppTheme.primaryLightColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Formatierte Text-Sektion mit Original-Formatierung
  Widget _buildFormattedTextSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colors.amber, Colors.orange],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.amber.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(icon, size: 20, color: Colors.black),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a2e).withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amber.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SelectableText(
            content.isNotEmpty ? _formatJobDescription(content) : 'Kein Text verfügbar',
            style: TextStyle(
              color: content.isNotEmpty ? Colors.white : Colors.white54,
              fontSize: 15,
              height: 1.6,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ],
    );
  }

  /// Zeigt Job-Details in einem Modal-Dialog an
  void _showJobDetailModal(BuildContext context, ExtractedJobText text) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: const Color(0xFF1A1A1A),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: BorderSide(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header mit Profile-Style Gradient
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withValues(alpha: 0.1),
                        AppTheme.primaryLightColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.work_outline,
                          color: AppTheme.primaryLightColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          text.jobTitle,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),

                // Scrollbarer Inhalt
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Firma und Standort
                        Row(
                          children: [
                            Icon(
                              Icons.business,
                              size: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                text.companyName,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              text.location ?? 'Standort unbekannt',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),

                        // Stellenbeschreibung
                        _buildModalSection(
                          context,
                          'Stellenbeschreibung',
                          text.extractedText,
                          Icons.description,
                        ),

                        // Anforderungen
                        if (text.requirements.isNotEmpty) ...[
                          const SizedBox(height: AppTheme.spacingMedium),
                          _buildModalListSection(
                            context,
                            'Anforderungen',
                            text.requirements,
                            Icons.checklist,
                          ),
                        ],

                        // Benefits
                        if (text.benefits.isNotEmpty) ...[
                          const SizedBox(height: AppTheme.spacingMedium),
                          _buildModalListSection(
                            context,
                            'Benefits',
                            text.benefits,
                            Icons.star,
                          ),
                        ],

                        // Kontaktinformationen
                        if (text.hasContactInfo()) ...[
                          const SizedBox(height: AppTheme.spacingMedium),
                          _buildModalContactSection(context, text),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Formatiert Job-Beschreibung für bessere Lesbarkeit
  String _formatJobDescription(String content) {
    // Entferne komische Zeichen und Platzhalter
    String formatted = content
        // Entferne $1, $2, etc. Platzhalter (WICHTIG: Escape $ richtig)
        .replaceAll(RegExp(r'\$\d+'), '')
        .replaceAll(RegExp(r'\\\$\d+'), '') // Escaped $ Zeichen
        // Entferne HTML-Entities
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&hellip;', '...')
        // Entferne JavaScript-Fragmente und Browser-Meldungen
        .replaceAll(RegExp(r'JavaScript.*?aktivieren[^.]*\.?'), '')
        .replaceAll(RegExp(r'deaktiviert\$?\d*'), '')
        .replaceAll(RegExp(r'Browser.*?unterstützt[^.]*\.?'), '')
        .replaceAll(RegExp(r'Cookies.*?aktiviert[^.]*\.?'), '')
        // Entferne RegExp-Artefakte aus der Formatierung
        .replaceAll(RegExp(r'\\\$\d'), '') // \$1, \$2 aus RegExp
        .replaceAll(RegExp(r'\\n\\n'), '\n\n') // Escaped newlines
        // Entferne übermäßige Leerzeichen und Zeilenumbrüche
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'\n\s*\n'), '\n\n')
        .trim();

    // Entferne alle $ Zeichen komplett (das verursacht die komischen Zeichen)
    formatted = formatted
        .replaceAll(RegExp(r'\$'), '') // Alle $ Zeichen entfernen
        .replaceAll(RegExp(r'\d+'), '') // Alle einzelnen Zahlen entfernen die übrig bleiben
        // Einfache Absatz-Formatierung ohne RegExp-Gruppen
        .replaceAll('. ', '.\n\n')
        .replaceAll('! ', '!\n\n')
        .replaceAll('? ', '?\n\n');

    // Entferne leere Zeilen am Anfang und Ende
    formatted = formatted.trim();

    return formatted;
  }

  /// Modal-Sektion für Text-Inhalte
  Widget _buildModalSection(BuildContext context, String title, String content, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundDarkColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.05),
                  AppTheme.primaryLightColor.withValues(alpha: 0.02),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, size: 20, color: AppTheme.primaryLightColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryLightColor,
                  ),
                ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              content.isNotEmpty ? _formatJobDescription(content) : 'Kein Text verfügbar',
              style: TextStyle(
                color: content.isNotEmpty ? Colors.white : Colors.white54,
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Modal-Listen-Sektion für Anforderungen und Benefits
  Widget _buildModalListSection(BuildContext context, String title, List<String> items, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundDarkColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.05),
                  AppTheme.primaryLightColor.withValues(alpha: 0.02),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, size: 20, color: AppTheme.primaryLightColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryLightColor,
                  ),
                ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: items.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '• ',
                      style: TextStyle(
                        color: AppTheme.primaryLightColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        item,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Modal-Kontakt-Sektion
  Widget _buildModalContactSection(BuildContext context, ExtractedJobText text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundDarkColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.05),
                  AppTheme.primaryLightColor.withValues(alpha: 0.02),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.contact_mail, size: 20, color: AppTheme.primaryLightColor),
                const SizedBox(width: 12),
                Text(
                  'Kontaktinformationen',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryLightColor,
                  ),
                ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (text.contactInfo['email'] != null)
                  _buildContactRow(context, 'E-Mail:', text.contactInfo['email'], Icons.email),
                if (text.contactInfo['phone'] != null)
                  _buildContactRow(context, 'Telefon:', text.contactInfo['phone'], Icons.phone),
                if (text.contactInfo['website'] != null)
                  _buildContactRow(context, 'Website:', text.contactInfo['website'], Icons.web),
                if (text.contactInfo['address'] != null)
                  _buildContactRow(context, 'Adresse:', text.contactInfo['address'], Icons.location_on),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Kontakt-Zeile für Modal
  Widget _buildContactRow(BuildContext context, String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: AppTheme.primaryLightColor),
          const SizedBox(width: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: SelectableText(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Dunkle Listen-Sektion für Anforderungen und Benefits
  Widget _buildDarkListSection(String title, List<String> items, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [color, color.withOpacity(0.7)],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(icon, size: 20, color: Colors.white),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a2e).withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.only(top: 6, right: 12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, color.withOpacity(0.7)],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.5),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// Dunkle Kontakt-Sektion
  Widget _buildDarkContactSection(ExtractedJobText text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colors.green, Colors.teal],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Row(
            children: [
              Icon(Icons.contact_mail, size: 20, color: Colors.white),
              SizedBox(width: 12),
              Text(
                'Kontaktinformationen',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a2e).withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (text.getEmails().isNotEmpty) ...[
                const Row(
                  children: [
                    Icon(Icons.email, size: 16, color: Colors.green),
                    SizedBox(width: 8),
                    Text(
                      'E-Mails:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...text.getEmails().map((email) => Padding(
                  padding: const EdgeInsets.only(left: 24, bottom: 4),
                  child: SelectableText(
                    email,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                )),
                const SizedBox(height: 12),
              ],
              if (text.getPhones().isNotEmpty) ...[
                const Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: Colors.green),
                    SizedBox(width: 8),
                    Text(
                      'Telefon:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...text.getPhones().map((phone) => Padding(
                  padding: const EdgeInsets.only(left: 24, bottom: 4),
                  child: SelectableText(
                    phone,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                )),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Dunkle Metadaten-Sektion
  Widget _buildDarkMetadataSection(ExtractedJobText text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[600]!, Colors.grey[700]!],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Row(
            children: [
              Icon(Icons.info_outline, size: 20, color: Colors.white),
              SizedBox(width: 12),
              Text(
                'Metadaten',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a2e).withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDarkMetadataRow('Job-ID:', text.jobId),
              _buildDarkMetadataRow('Größe:', '${text.getSizeInKB().toStringAsFixed(1)} KB'),
              _buildDarkMetadataRow('Extrahiert am:', text.extractedAt?.toString().split('.')[0] ?? 'Unbekannt'),
              if (text.lastAccessed != null)
                _buildDarkMetadataRow('Letzter Zugriff:', text.lastAccessed!.toString().split('.')[0]),
              if (text.sourceUrl != null)
                _buildDarkMetadataRow('Quelle:', text.sourceUrl!, isUrl: true),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDarkMetadataRow(String label, String value, {bool isUrl = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: TextStyle(
                fontSize: 13,
                color: isUrl ? Colors.cyan : Colors.white,
                decoration: isUrl ? TextDecoration.underline : null,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getTotalSizeInKB(List<ExtractedJobText> texts) {
    return texts.fold(0.0, (sum, text) => sum + text.getSizeInKB());
  }

  Future<void> _loadExtractedTexts(
    WidgetRef ref,
    ValueNotifier<List<ExtractedJobText>> extractedTexts,
    ValueNotifier<bool> isLoading,
    ValueNotifier<String?> error,
  ) async {
    try {
      isLoading.value = true;
      error.value = null;

      final supabaseClient = ref.read(supabaseClientProvider);
      
      // Lade alle extrahierten Texte aus Supabase mit korrekter Sortierung
      final response = await supabaseClient.rpc('get_offline_jobs_sorted');

      final texts = <ExtractedJobText>[];
      for (final row in response) {
        try {
          final text = ExtractedJobText.fromJson(row);
          texts.add(text);
        } catch (e) {
          print('Fehler beim Parsen von extrahiertem Text: $e');
        }
      }

      extractedTexts.value = texts;
    } catch (e) {
      error.value = e.toString();
      print('Fehler beim Laden der extrahierten Texte: $e');
    } finally {
      isLoading.value = false;
    }
  }
}

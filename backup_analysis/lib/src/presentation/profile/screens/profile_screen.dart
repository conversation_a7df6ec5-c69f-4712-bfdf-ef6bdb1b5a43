import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'dart:io';
import 'dart:async';

// <PERSON><PERSON><PERSON> sicher, dass BuildContext explizit ist

// --- Bereinigt: Nicht verwendete Imports entfernt ---

import 'package:ki_test/src/presentation/widgets/error_message.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/extensions/flutter_extensions.dart';
import 'package:ki_test/src/presentation/profile/widgets/dropdown_profile_section.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import 'package:ki_test/src/presentation/profile/models/profile_models.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ui_helpers.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_app_bar.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_editable_widgets.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_skills_widgets.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ai_personalization.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ai_personalization_display.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_display_widgets.dart';
import 'package:ki_test/src/presentation/profile/widgets/additional_documents_section.dart';
import 'package:ki_test/src/presentation/profile/services/profile_cv_service.dart';

// --- Hilfsklassen für bearbeitbare Listen-Einträge ---









// --- Top-Level Helper Widgets und Funktionen (Angepasst für dunkles Theme) ---













// --- AppBar Widget (angepasst für dunkles Theme) ---

// --- Haupt-Screen --- (Hintergrund hinzugefügt)
class ProfileScreen extends HookConsumerWidget {
  const ProfileScreen({super.key});

  static void initializeStates(
    UserProfile profile,
    ValueNotifier<List<TextEditingController>> skillsState,
    ValueNotifier<List<EditableWorkExperience>> workExperienceState,
    ValueNotifier<List<EditableEducation>> educationState,
    ProfileControllers controllers,
  ) {
    debugPrint("### initializeStates called ###");
    controllers.nameController.text = profile.name ?? '';
    controllers.emailController.text = profile.email ?? '';
    controllers.phoneController.text = profile.phoneNumber ?? '';

    final jobPrefs = profile.jobPreferencesObj;
    controllers.targetPositionController.text = jobPrefs?.targetPosition ?? '';
    controllers.industryController.text = jobPrefs?.industry ?? '';
    controllers.locationController.text = jobPrefs?.locationPreference ?? '';
    controllers.salaryController.text =
        jobPrefs?.desiredSalary?.toStringAsFixed(0) ?? ''; // No decimals
    controllers.employmentTypeController.text = jobPrefs?.employmentType ?? '';

    controllers.globalAiHintsController.text = profile.globalAiHints ?? '';
    controllers.preferredWritingStyleController.text =
        profile.preferredWritingStyle ?? 'Professionell';
    controllers.applicationLengthController.text =
        profile.applicationLength ?? 'Standard';
    controllers.includeExperience =
        profile.includeExperienceInApplication ?? true;

    // Dispose alte Controller
    for (var controller in skillsState.value) {
      controller.dispose();
    }

    // Initialisiere Skills
    skillsState.value =
        (profile.skills ?? [])
            .map((s) => TextEditingController(text: s))
            .toList();
    debugPrint(
      "### initializeStates: ${profile.skills?.length ?? 0} Skills initialisiert ###",
    );

    // Dispose alte WorkExperience-States
    for (var state in workExperienceState.value) {
      state.dispose();
    }

    // Initialisiere WorkExperience
    if (profile.workExperience?.isNotEmpty ?? false) {
      workExperienceState.value =
          profile.workExperience!
              .map((exp) => EditableWorkExperience.fromWorkExperience(exp))
              .toList();
      debugPrint(
        "### initializeStates: ${profile.workExperience!.length} WorkExperience-Einträge initialisiert ###",
      );
    } else {
      workExperienceState.value = [];
      debugPrint(
        "### initializeStates: Keine WorkExperience-Einträge vorhanden ###",
      );
    }

    // Dispose alte Education-States
    for (var state in educationState.value) {
      state.dispose();
    }

    // Initialisiere Education
    if (profile.education?.isNotEmpty ?? false) {
      educationState.value =
          profile.education!
              .map((edu) => EditableEducation.fromEducation(edu))
              .toList();
      debugPrint(
        "### initializeStates: ${profile.education!.length} Education-Einträge initialisiert ###",
      );
    } else {
      educationState.value = [];
      debugPrint(
        "### initializeStates: Keine Education-Einträge vorhanden ###",
      );
    }

    debugPrint("### initializeStates: Initialization complete. ###");
  }

  static UserProfile _collectProfileData(
    UserProfile currentProfile,
    ProfileControllers controllers,
    ValueNotifier<List<TextEditingController>> editableSkills,
    ValueNotifier<List<EditableWorkExperience>> editableWorkExperienceStates,
    ValueNotifier<List<EditableEducation>> editableEducationStates,
    String? cvDownloadUrl, // Geändert von cvFilePath
  ) {
    final updatedWorkExperience =
        editableWorkExperienceStates.value
            .where(
              (state) =>
                  state.positionController.text.trim().isNotEmpty &&
                  state.companyController.text.trim().isNotEmpty,
            )
            .map(
              (state) => WorkExperience(
                position: state.positionController.text.trim(),
                company: state.companyController.text.trim(),
                startDate:
                    state.startDate ??
                    DateTime.now(), // Fallback, sollte nicht passieren
                endDate: state.endDate,
                description: state.descriptionController.text.trim(),
              ),
            )
            .toList();

    final updatedEducation =
        editableEducationStates.value
            .where(
              (state) =>
                  state.institutionController.text.trim().isNotEmpty &&
                  state.degreeController.text.trim().isNotEmpty,
            )
            .map(
              (state) => Education(
                institution: state.institutionController.text.trim(),
                degree: state.degreeController.text.trim(),
                startDate: state.startDate ?? DateTime.now(), // Fallback
                endDate: state.endDate,
                fieldOfStudy:
                    state.fieldOfStudyController.text.trim().isEmpty
                        ? ''
                        : state.fieldOfStudyController.text.trim(),
              ),
            )
            .toList();

    return currentProfile.copyWith(
      name: controllers.nameController.text.trim(),
      email: controllers.emailController.text.trim(),
      phoneNumber:
          controllers.phoneController.text.trim().isEmpty
              ? null
              : controllers.phoneController.text.trim(),
      jobPreferencesObj: currentProfile.jobPreferencesObj?.copyWith(
        targetPosition:
            controllers.targetPositionController.text.trim().isEmpty
                ? null
                : controllers.targetPositionController.text.trim(),
        industry:
            controllers.industryController.text.trim().isEmpty
                ? null
                : controllers.industryController.text.trim(),
        locationPreference:
            controllers.locationController.text.trim().isEmpty
                ? null
                : controllers.locationController.text.trim(),
        desiredSalary:
            int.tryParse(
              controllers.salaryController.text.replaceAll(
                RegExp(r'[^0-9]'),
                '',
              ),
            )?.toDouble(),
        employmentType:
            controllers.employmentTypeController.text.trim().isEmpty
                ? null
                : controllers.employmentTypeController.text.trim(),
      ),
      skills:
          editableSkills.value
              .map((c) => c.text.trim())
              .where((s) => s.isNotEmpty)
              .toList(), // Skills hier hinzufügen
      workExperience: updatedWorkExperience,
      education: updatedEducation,
      globalAiHints:
          controllers.globalAiHintsController.text.trim().isEmpty
              ? null
              : controllers.globalAiHintsController.text.trim(),
      preferredWritingStyle:
          controllers.preferredWritingStyleController.text.trim(),
      applicationLength:
          controllers.applicationLengthController.text.trim(),
      includeExperienceInApplication: controllers.includeExperience,
      cvDownloadUrl: cvDownloadUrl, // Geändert
      cvFilePath: null,
    );
  }

  // --- CV-Update-Dialog wurde in ProfileCvService extrahiert ---

  // Helper-Methode für Währungsformatierung
  String _formatCurrency(double? amount) {
    if (amount == null) return 'Nicht angegeben';
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.')} €';
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    debugPrint('ProfileScreen build!');
    final profileState = ref.watch(userProfileProvider);
    final isEditing = useState<bool>(false);
    final isSaving = useState<bool>(false);
    final errorState = useState<String?>(null);
    final controllers = useMemoized(() => ProfileControllers(), const []);
    final editableSkills = useState<List<TextEditingController>>([]);
    final editableWorkExperienceStates =
        useState<List<EditableWorkExperience>>([]);
    final editableEducationStates = useState<List<EditableEducation>>([]);
    final isControllerReady = useState<bool>(false);
    final lastAddedSkillIndex = useState<int>(-1);
    final lastAddedWorkIndex = useState<int>(-1);
    final lastAddedEducationIndex = useState<int>(-1);
    final localCvFilePath = useState<String?>(
      null,
    ); // State für den *lokal* ausgewählten Pfad
    final isUploadingCv = useState<bool>(false); // State für den Upload-Vorgang

    useEffect(() {
      return () {
        controllers.dispose();
        for (var controller in editableSkills.value) {
          controller.dispose();
        }
        for (var state in editableWorkExperienceStates.value) {
          state.dispose();
        }
        for (var state in editableEducationStates.value) {
          state.dispose();
        }
      };
    }, [controllers]); // Dependency only on controllers object identity

    useEffect(() {
      UserProfile? profileData;
      if (profileState is AsyncData<UserProfile>) {
        profileData = profileState.value;
      } else if (profileState is AsyncError &&
          profileState.hasValue &&
          profileState.value is UserProfile) {
        profileData = profileState.value as UserProfile;
      }

      if (profileData != null) {
        if (!isControllerReady.value || !isEditing.value) {
          initializeStates(
            profileData,
            editableSkills,
            editableWorkExperienceStates,
            editableEducationStates,
            controllers,
          );
          final initialDownloadUrl = profileData.cvDownloadUrl;
          debugPrint(
            "Initial CV Download URL from profile: $initialDownloadUrl",
          );
          if (context.mounted) {
            isControllerReady.value = true;
          }
        }
      } else {
        if (isControllerReady.value) {
          isControllerReady.value = false;
        }
      }
      return null;
    }, [profileState, isEditing.value]);

    // Upload-Methode - jetzt delegiert an ProfileCvService
    Future<void> uploadAndProcessCv(File file) async {
      await ProfileCvService.uploadAndProcessCv(
        context,
        ref,
        file,
        isUploadingCv,
      );
    }

    Future<void> saveProfile() async {
      final currentProfileData = profileState.valueOrNull;
      if (currentProfileData == null) {
        context.showErrorSnackBar('Profil konnte nicht geladen werden.');
        return;
      }
      if (!isControllerReady.value && isEditing.value) {
        context.showErrorSnackBar(
          'Profil-Daten sind noch nicht bereit zum Bearbeiten.',
        );
        return;
      }

      if (controllers.nameController.text.trim().isEmpty) {
        context.showErrorSnackBar('Name darf nicht leer sein.');
        return;
      }

      errorState.value = null;
      isSaving.value = true;
      String? finalCvDownloadUrl = currentProfileData.cvDownloadUrl;

      try {
        final updatedProfileData = _collectProfileData(
          currentProfileData,
          controllers,
          editableSkills,
          editableWorkExperienceStates,
          editableEducationStates,
          finalCvDownloadUrl,
        );
        await ref
            .read(userProfileProvider.notifier)
            .saveProfileChanges(updatedProfileData);
        isEditing.value = false;
        context.showSuccessSnackBar('Profil erfolgreich gespeichert!');
      } catch (e, stackTrace) {
        debugPrint("Error saving profile: $e\nStack trace: $stackTrace");
        errorState.value = "Fehler beim Speichern des Profils: ${e.toString()}";
        context.showErrorSnackBar(errorState.value!);
      } finally {
        if (context.mounted) {
          isSaving.value = false;
        }
      }
    }

    void cancelEditing() {
      final currentProfile = profileState.valueOrNull;
      if (currentProfile != null) {
        initializeStates(
          currentProfile,
          editableSkills,
          editableWorkExperienceStates,
          editableEducationStates,
          controllers,
        );
        localCvFilePath.value = currentProfile.cvFilePath;
        errorState.value = null;
      }
      isEditing.value = false;
    }

    void toggleEdit() {
      if (isEditing.value) {
        cancelEditing();
      } else {
        UserProfile? profileData = profileState.valueOrNull;
        localCvFilePath.value = profileData?.cvFilePath;
        isEditing.value = true;
        isControllerReady.value = false;
      }
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: ProfileAppBar(
        isEditing: isEditing.value,
        onToggleEdit: toggleEdit,
        onSave: saveProfile,
        isUploadingCv: isUploadingCv,
      ),
      body: Container(
        // HIER WIRD DER CONTAINER MIT DEM GRADIENTEN ENTFERNT
        /*
          decoration: const BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0,
              colors: [
                Color(0xFF3A1A7E),
                AppTheme.backgroundDarkColor,
              ],
              stops: [0.0, 1.0],
            ),
          ),
          */
        // ENDE ENTFERNTER CONTAINER
        // Der Inhalt (profileState.when) bleibt direkt im body des Scaffolds
        child: profileState.when(
          loading:
              () => const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
          error: (err, stack) {
            UserProfile? cachedProfile;
            if (err is UserProfile) cachedProfile = err;
            if (cachedProfile != null) {
              return Column(
          children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      top: kToolbarHeight + AppTheme.spacingMedium,
                      left: AppTheme.spacingMedium,
                      right: AppTheme.spacingMedium,
                    ),
                    child: ErrorMessage(
                      message:
                          "Fehler beim Laden: ${err.toString().split(':').first}. Zeige Cache.",
                      onRetry: () => ref.refresh(userProfileProvider),
                    ),
                  ),
                  Expanded(
                    child: _buildProfileView(
                      context,
                      ref,
                      cachedProfile,
                      isEditing,
                      isSaving,
                      errorState,
                      controllers,
                      editableSkills,
                      editableWorkExperienceStates,
                      editableEducationStates,
                      isControllerReady,
                      lastAddedSkillIndex,
                      lastAddedWorkIndex,
                      lastAddedEducationIndex,
                      saveProfile,
                      cancelEditing,
                      localCvFilePath,
                      isUploadingCv,
                      uploadAndProcessCv,
                      AsyncValue.data(cachedProfile),
                    ),
                  ),
                ],
              );
            } else {
              return Center(
                child: ErrorMessage(
                  message:
                      "Fehler beim Laden des Profils: ${err.toString().split(':').first}",
                  onRetry: () => ref.refresh(userProfileProvider),
                ),
              );
            }
          },
          data: (userProfile) {
            if ((!isControllerReady.value && isEditing.value) ||
                (profileState.isLoading && !isEditing.value)) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.white),
              );
            }

            return SafeArea(
              top: true,
              bottom: false,
              child: _buildProfileView(
                context,
                ref,
                userProfile,
                isEditing,
                isSaving,
                errorState,
                controllers,
                editableSkills,
                editableWorkExperienceStates,
                editableEducationStates,
                isControllerReady,
                lastAddedSkillIndex,
                lastAddedWorkIndex,
                lastAddedEducationIndex,
                saveProfile,
                cancelEditing,
                localCvFilePath,
                isUploadingCv,
                uploadAndProcessCv,
                profileState,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileView(
    BuildContext context,
    WidgetRef ref,
    UserProfile userProfile,
    ValueNotifier<bool> isEditing,
    ValueNotifier<bool> isSaving,
    ValueNotifier<String?> errorState,
    ProfileControllers controllers,
    ValueNotifier<List<TextEditingController>> editableSkills,
    ValueNotifier<List<EditableWorkExperience>> editableWorkExperienceStates,
    ValueNotifier<List<EditableEducation>> editableEducationStates,
    ValueNotifier<bool> isControllerReady,
    ValueNotifier<int> lastAddedSkillIndex,
    ValueNotifier<int> lastAddedWorkIndex,
    ValueNotifier<int> lastAddedEducationIndex,
    VoidCallback onSaveChanges,
    VoidCallback onCancelEditing,
    ValueNotifier<String?> localCvFilePath,
    ValueNotifier<bool> isUploadingCv,
    Function(File) uploadAndProcessCvCallback, // Korrigierter Typ
    AsyncValue<UserProfile> profileState,
  ) {
    final formKey = useMemoized(() => GlobalKey<FormState>(), [
      isEditing.value,
    ]);

    return Form(
      key: formKey,
      child: SingleChildScrollView(
        // Füge ein Padding am unteren Rand hinzu, um Platz für die Navigation zu schaffen
        padding: const EdgeInsets.only(
          left: AppTheme.spacingMedium,
          right: AppTheme.spacingMedium,
          bottom:
              kBottomNavigationBarHeight -
              32, // Stark verringerter Abstand zur Navigation
          top: AppTheme.spacingSmall,
        ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            if (errorState.value != null)
                        Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                          child: Text(
                  errorState.value!,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
              ),

            if (isEditing.value) ...[
              _buildModernEditableSection(
                context,
                title: AppLocalizationsWrapper.of(context).personalData,
                icon: Icons.person_outline,
                children: [
                  buildModernTextField(
                    context,
                    controller: controllers.nameController,
                    label: AppLocalizationsWrapper.of(context).fullName,
                    icon: Icons.person_pin_outlined,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.emailController,
                    label: AppLocalizationsWrapper.of(context).email,
                    icon: Icons.email_outlined,
                    readOnly: true,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.phoneController,
                    label: AppLocalizationsWrapper.of(context).phoneOptional,
                    icon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.addressController,
                    label: AppLocalizationsWrapper.of(context).address,
                    icon: Icons.home_outlined,
                        ),
                    ],
                  ),
              _buildModernEditableSection(
                context,
                title: AppLocalizationsWrapper.of(context).resume,
                icon: Icons.upload_file,
                children: [
                  EditableCvSection(
                    initialDownloadUrl: userProfile.cvDownloadUrl,
                    locallySelectedFilePath: localCvFilePath.value,
                    onFilePicked: (path) {
                      if (path != null) {
                        localCvFilePath.value = path;
                        // Rufe die Callback-Funktion mit dem profileState auf
                        uploadAndProcessCvCallback(File(path));
                      } else {
                        localCvFilePath.value = null;
                      }
                    },
                    onClearSelection: () {
                      localCvFilePath.value = null;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Lokale Auswahl entfernt.'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  // Button "Berufsbezeichnungen generieren" wurde entfernt, da die Generierung jetzt automatisch nach dem Hochladen eines Lebenslaufs erfolgt
                ],
              ),
              DropdownProfileSection(
                title: 'Zusätzliche Dokumente',
                icon: Icons.folder_outlined,
                isExpanded: false,
                child: const AdditionalDocumentsSection(),
              ),
              DropdownProfileSection(
                title: 'Fähigkeiten',
                icon: Icons.lightbulb_outline,
                isExpanded: false,
                onEdit: () {
                  // Neue Fähigkeit hinzufügen
                  final newSkillController = TextEditingController();
                  editableSkills.value = List.from(editableSkills.value)..add(newSkillController);
                  lastAddedSkillIndex.value = editableSkills.value.length - 1;
                },
                child: EditableSkillsListRefactored(
                  key: ValueKey(
                    'skills_${editableSkills.value.length}_${userProfile.id}',
                  ),
                  initialSkills: editableSkills.value,
                  lastAddedIndex: lastAddedSkillIndex.value,
                  onRemoveSkill: (index) {
                    confirmModernRemoval(
                      context,
                      'Fähigkeit wirklich löschen?',
                      () {
                        if (index >= 0 &&
                            index < editableSkills.value.length) {
                          editableSkills.value[index].dispose();
                        }
                        editableSkills.value = List.from(editableSkills.value)
                          ..removeAt(index);
                        lastAddedSkillIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              DropdownProfileSection(
                title: 'Berufserfahrung',
                icon: Icons.work_history_outlined,
                isExpanded: false,
                onEdit: () {
                  // Neue Berufserfahrung hinzufügen
                  final newExperience = EditableWorkExperience(
                    positionController: TextEditingController(),
                    companyController: TextEditingController(),
                    descriptionController: TextEditingController(),
                    startDate: DateTime.now(),
                  );
                  editableWorkExperienceStates.value = List.from(editableWorkExperienceStates.value)..add(newExperience);
                  lastAddedWorkIndex.value = editableWorkExperienceStates.value.length - 1;
                },
                child: EditableWorkExperienceListRefactored(
                  key: ValueKey(
                    'work_${editableWorkExperienceStates.value.length}_${userProfile.id}',
                  ),
                  initialExperiences: editableWorkExperienceStates.value,
                  lastAddedIndex: lastAddedWorkIndex.value,
                  onExperienceChanged: (index, updatedExperience) {
                    final currentStates = List<EditableWorkExperience>.from(
                      editableWorkExperienceStates.value,
                    );
                    if (index >= 0 && index < currentStates.length) {
                      currentStates[index] = updatedExperience;
                      editableWorkExperienceStates.value = currentStates;
                    }
                  },
                  onRemoveExperience: (index) {
                    confirmModernRemoval(
                      context,
                      'Berufserfahrung wirklich löschen?',
                      () {
                        if (index >= 0 &&
                            index <
                                editableWorkExperienceStates.value.length) {
                          editableWorkExperienceStates.value[index].dispose();
                        }
                        editableWorkExperienceStates.value = List.from(
                          editableWorkExperienceStates.value,
                        )..removeAt(index);
                        lastAddedWorkIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              DropdownProfileSection(
                title: 'Ausbildung',
                icon: Icons.school_outlined,
                isExpanded: false,
                onEdit: () {
                  // Neue Ausbildung hinzufügen
                  final newEducation = EditableEducation(
                    institutionController: TextEditingController(),
                    degreeController: TextEditingController(),
                    fieldOfStudyController: TextEditingController(),
                    startDate: DateTime.now(),
                  );
                  editableEducationStates.value = List.from(editableEducationStates.value)..add(newEducation);
                  lastAddedEducationIndex.value = editableEducationStates.value.length - 1;
                },
                child: EditableEducationListRefactored(
                  key: ValueKey(
                    'edu_${editableEducationStates.value.length}_${userProfile.id}',
                  ),
                  initialEducations: editableEducationStates.value,
                  lastAddedIndex: lastAddedEducationIndex.value,
                  onEducationChanged: (index, updatedEducation) {
                    final currentStates = List<EditableEducation>.from(
                      editableEducationStates.value,
                    );
                    if (index >= 0 && index < currentStates.length) {
                      currentStates[index] = updatedEducation;
                      editableEducationStates.value = currentStates;
                    }
                  },
                  onRemoveEducation: (index) {
                    confirmModernRemoval(
            context,
                      'Ausbildung wirklich löschen?',
                      () {
                        if (index >= 0 &&
                            index < editableEducationStates.value.length) {
                          editableEducationStates.value[index].dispose();
                        }
                        editableEducationStates.value = List.from(
                          editableEducationStates.value,
                        )..removeAt(index);
                        lastAddedEducationIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              _buildModernEditableSection(
                context,
                title: 'KI Personalisierung',
                icon: Icons.auto_awesome,
                children: [
                  EditableKiPersonalizationNew(
                    controllers: controllers,
                    initialIncludeExperience: controllers.includeExperience,
                    onIncludeExperienceChanged: (value) {
                      controllers.includeExperience = value;
                    },
                    preferredWritingStyleController:
                        controllers.preferredWritingStyleController,
                    onStyleChanged: (newValue) async {
                      controllers.preferredWritingStyleController.text =
                          newValue ?? 'Professionell';

                      // Wenn "Passend zu meinem Stil" ausgewählt wurde, generiere den personalisierten Schreibstil-Prompt
                      if (newValue == 'Passend zu meinem Stil') {
                        // Speichere den BuildContext für spätere Verwendung
                        final currentContext = context;

                        try {
                          // Zeige Ladeindikator
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            const SnackBar(
                              content: Text('Analysiere deinen Schreibstil...'),
                              duration: Duration(seconds: 2),
                            ),
                          );

                          // Rufe die Supabase-Funktion auf, um den personalisierten Schreibstil zu generieren
                          final supabaseService = ref.read(
                            supabaseServiceProvider,
                          );
                          final stylePrompt =
                              await supabaseService.analyzeProfileStyle();

                          // Prüfe, ob der Widget noch mounted ist, bevor wir den BuildContext verwenden
                          if (currentContext.mounted &&
                              stylePrompt.isNotEmpty) {
                            ScaffoldMessenger.of(currentContext).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Dein persönlicher Schreibstil wurde erfolgreich analysiert!',
                                ),
                                backgroundColor: Colors.green,
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        } catch (e) {
                          debugPrint(
                            'Fehler bei der Generierung des personalisierten Schreibstils: $e',
                          );
                          // Prüfe, ob der Widget noch mounted ist, bevor wir den BuildContext verwenden
                          if (currentContext.mounted) {
                            ScaffoldMessenger.of(currentContext).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Fehler bei der Analyse deines Schreibstils: ${e.toString()}',
                                ),
                                backgroundColor: Colors.red,
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        }
                      }
                    },
                    applicationLengthController:
                        controllers.applicationLengthController,
                    onApplicationLengthChanged: (newValue) async {
                      if (newValue != null) {
                        controllers.applicationLengthController.text =
                            newValue;

                        // 🔥 WICHTIG: Speichere das Profil sofort!
                        debugPrint('🔥 APPLICATION LENGTH CHANGED TO: $newValue');

                        // Aktualisiere das Profil mit der neuen Bewerbungslänge
                        final currentProfile = ref.read(userProfileProvider).value;
                        if (currentProfile != null) {
                          final updatedProfile = currentProfile.copyWith(
                            applicationLength: newValue,
                          );

                          try {
                            await ref.read(userProfileProvider.notifier).saveProfileChanges(updatedProfile);
                            debugPrint('🔥 APPLICATION LENGTH SAVED SUCCESSFULLY: $newValue');
                          } catch (e) {
                            debugPrint('❌ ERROR SAVING APPLICATION LENGTH: $e');
                          }
                        }
                      }
                    },
                  ),
                ],
              ),
            ] else ...[
              _buildModernDisplaySection(
                context,
                title: 'Persönliche Daten',
                icon: Icons.person_outline,
      children: [
                  buildModernInfoRow('Name', userProfile.name ?? ''),
                  buildModernInfoRow('E-Mail', userProfile.email ?? ''),
                  if (userProfile.phoneNumber != null)
                    buildModernInfoRow('Telefon', userProfile.phoneNumber!),
                  const SizedBox(height: AppTheme.spacingSmall),
                  buildModernInfoRow(
                    'Gewünschte Position',
                    userProfile.jobPreferencesObj?.targetPosition ??
                        'Nicht angegeben',
                  ),
                  buildModernInfoRow(
                    'Gehaltsvorstellung',
                    _formatCurrency(
                      userProfile.jobPreferencesObj?.desiredSalary,
                    ),
                  ),
                  buildModernInfoRow(
                    'Bevorzugter Ort',
                    userProfile.jobPreferencesObj?.locationPreference ??
                        'Nicht angegeben',
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Lebenslauf',
                icon: Icons.description_outlined,
                // Übergibt beide Werte an das Display Widget
                children: [
                  DisplayCvSection(
                    cvDownloadUrl: userProfile.cvDownloadUrl,
                    cvFilePath: userProfile.cvFilePath,
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Zusätzliche Dokumente',
                icon: Icons.folder_outlined,
                children: [
                  const DisplayAdditionalDocumentsSection(),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Fähigkeiten',
                icon: Icons.lightbulb_outline,
                children: [
                  DisplaySkillsListRefactored(
                    skills: userProfile.skills ?? [],
                  ),
                  // Button "Berufsbezeichnungen generieren" wurde entfernt, da die Generierung jetzt automatisch nach dem Hochladen eines Lebenslaufs erfolgt
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Berufserfahrung',
                icon: Icons.work_history_outlined,
                children: [
                  DisplayWorkExperienceList(
                    experiences: userProfile.workExperience ?? [],
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Ausbildung',
                icon: Icons.school_outlined,
                children: [
                  DisplayEducationList(
                    educations: userProfile.education ?? [],
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'KI Personalisierung',
                icon: Icons.auto_awesome,
                children: [
                  DisplayKiPersonalization(
                    cvAutoFillEnabled: false, // Fallback
                    skillsInApplication: true, // Fallback
                    workExperienceInApplication:
                        userProfile.includeExperienceInApplication ?? true,
                    educationInApplication:
                        true, // Verwende direkt boolean statt Getter
                    isPersonalized:
                        userProfile.preferredWritingStyle != null &&
                        userProfile.preferredWritingStyle!.isNotEmpty,
                    applicationLength: userProfile.applicationLength ?? 'Standard',
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// --- Section Widgets (Editable & Display) ---
Widget _buildModernEditableSection(
  BuildContext context, {
  required String title,
  required IconData icon,
  required List<Widget> children,
  VoidCallback? onAdd,
}) {
  return ModernSectionCardWidget(
    title: title,
    icon: icon,
    onAdd: onAdd,
    isEditing: true,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    ),
  );
}

Widget _buildModernDisplaySection(
  BuildContext context, {
  required String title,
  required IconData icon,
  required List<Widget> children,
}) {
  final nonEmptyChildren =
      children
          .where((child) => !(child is SizedBox && child.height == 0))
          .toList();
  if (nonEmptyChildren.isEmpty) return const SizedBox.shrink();
  return ModernSectionCardWidget(
    title: title,
    icon: icon,
    isEditing: false,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: nonEmptyChildren,
    ),
  );
}

// --- Ende der ProfileScreen Implementierung ---
//
// Alle Widget-Definitionen wurden in separate Module extrahiert:
// - profile_models.dart: Helper-Klassen und Datenmodelle
// - profile_ui_helpers.dart: UI-Helper-Funktionen
// - profile_app_bar.dart: AppBar-Widget
// - profile_editable_widgets.dart: Editierbare Listen-Widgets
// - profile_skills_widgets.dart: Skills-Widgets
// - profile_ai_personalization.dart: KI-Personalisierung
// - profile_ai_personalization_display.dart: KI-Display-Widget
// - profile_display_widgets.dart: Display-Widgets
//
// Diese modulare Struktur verbessert:
// ✅ Wartbarkeit durch kleinere, fokussierte Dateien
// ✅ Wiederverwendbarkeit der Widget-Komponenten
// ✅ Testbarkeit durch isolierte Module
// ✅ Entwicklerfreundlichkeit durch klare Trennung der Verantwortlichkeiten

// --- EditableCvSection wurde in profile_display_widgets.dart extrahiert ---

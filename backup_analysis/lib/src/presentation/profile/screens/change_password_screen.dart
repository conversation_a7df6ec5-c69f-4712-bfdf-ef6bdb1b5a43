import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:firebase_auth/firebase_auth.dart'; // Entfernt
import 'package:supabase_flutter/supabase_flutter.dart'; // NEU: Supabase importiert
import '../../../core/theme/app_theme.dart';
import '../../../extensions/flutter_extensions.dart'; // Für Snackbars
import '../../../core/l10n/app_localizations_wrapper.dart';

class ChangePasswordScreen extends HookConsumerWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final currentPasswordController = useTextEditingController(); // Vorerst nicht benötigt für Supabase
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // final isCurrentPasswordVisible = useState(false); // Vorerst nicht benötigt
    final isNewPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final isSubmitting = useState(false);
    final errorState = useState<String?>(null);

    Future<void> changePassword() async {
      if (!(formKey.currentState?.validate() ?? false)) return;

      // Prüfung auf Übereinstimmung bleibt
      if (newPasswordController.text != confirmPasswordController.text) {
        errorState.value = AppLocalizationsWrapper.of(context).passwordMismatch;
        return;
      }

      errorState.value = null;
      isSubmitting.value = true;

      // --- Re-Authentifizierung entfernt ---

      // --- Passwort direkt über Supabase aktualisieren ---
      // Speichere den Kontext für spätere Verwendung
      final localContext = context;

      try {
        // Hole den Supabase Client
        final supabase = Supabase.instance.client;

        // Prüfe, ob ein Benutzer angemeldet ist (Sicherheitscheck)
        if (supabase.auth.currentUser == null) {
          errorState.value =
              AppLocalizationsWrapper.of(localContext).noUserLoggedIn;
          isSubmitting.value = false;
          return;
        }

        // Sende das Update an Supabase
        await supabase.auth.updateUser(
          UserAttributes(password: newPasswordController.text),
        );

        print("Passwort erfolgreich über Supabase geändert.");
        if (context.mounted) {
          context.showSuccessSnackBar(
            AppLocalizationsWrapper.of(localContext).passwordUpdatedSuccess,
          );
          Navigator.of(context).pop(); // Zurück zum vorherigen Screen
        }
      } on AuthException catch (e) {
        print("Supabase Fehler beim Ändern des Passworts: ${e.message}");
        // TODO: Prüfe spezifische Supabase AuthException Codes für 'weak-password' etc. falls nötig.
        errorState.value = AppLocalizationsWrapper.of(
          localContext,
        ).passwordUpdateError(e.message);
      } catch (e) {
        print("Allgemeiner Fehler beim Ändern des Passworts: $e");
        errorState.value =
            AppLocalizationsWrapper.of(localContext).unexpectedError;
      } finally {
        if (context.mounted) {
          isSubmitting.value = false;
        }
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizationsWrapper.of(context).changePassword),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Fehleranzeige
              if (errorState.value != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusMedium,
                    ),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          errorState.value!,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Neues Passwort
              TextFormField(
                controller: newPasswordController,
                obscureText: !isNewPasswordVisible.value,
                decoration: InputDecoration(
                  labelText: AppLocalizationsWrapper.of(context).newPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isNewPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        () =>
                            isNewPasswordVisible.value =
                                !isNewPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizationsWrapper.of(context).enterNewPassword;
                  }
                  if (value.length < 8) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordMinLength;
                  }
                  if (!RegExp(r'[A-Z]').hasMatch(value)) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordRequireUppercase;
                  }
                  if (!RegExp(r'[0-9]').hasMatch(value)) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).passwordRequireNumber;
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingMedium),

              // Neues Passwort bestätigen
              TextFormField(
                controller: confirmPasswordController,
                obscureText: !isConfirmPasswordVisible.value,
                decoration: InputDecoration(
                  labelText:
                      AppLocalizationsWrapper.of(context).confirmPassword,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isConfirmPasswordVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed:
                        () =>
                            isConfirmPasswordVisible.value =
                                !isConfirmPasswordVisible.value,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizationsWrapper.of(
                      context,
                    ).confirmNewPassword;
                  }
                  if (value != newPasswordController.text) {
                    return AppLocalizationsWrapper.of(context).passwordMismatch;
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppTheme.spacingXLarge),

              // Speicherbutton
              ElevatedButton(
                onPressed: isSubmitting.value ? null : changePassword,
                child:
                    isSubmitting.value
                        ? const SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(color: Colors.white),
                        )
                        : Text(
                          AppLocalizationsWrapper.of(context).savePassword,
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'dart:io';
// NEU: Import für min
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path/path.dart' as p; // Alias 'p' hinzugefügt
import 'dart:async';
import 'dart:ui' as ui; // Für ImageFilter, Shader und TextDirection
import 'package:flutter/foundation.dart' show listEquals;
import 'package:ki_test/src/utils/cv_storage_helper.dart';
import 'package:flutter/services.dart'; // Für MethodChannel
import 'package:path_provider/path_provider.dart'; // Für getTemporaryDirectory
import 'package:ki_test/src/core/services/secure_file_service.dart'; // Für SecureFileService

// <PERSON><PERSON><PERSON> sicher, dass BuildContext explizit ist

// --- KORRIGIERTE/NEUE IMPORTE ---
import 'package:ki_test/src/application/providers/services_providers.dart'; // Korrekter Pfad
import 'package:ki_test/src/domain/models/extracted_cv_data.dart'; // NEU
// --- ENDE KORRIGIERTE/NEUE IMPORTE ---

import 'package:ki_test/src/presentation/widgets/error_message.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/extensions/flutter_extensions.dart';
import 'package:ki_test/src/presentation/profile/widgets/dropdown_profile_section.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import 'package:ki_test/src/presentation/profile/models/profile_models.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ui_helpers.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_app_bar.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_editable_widgets.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_skills_widgets.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ai_personalization.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ai_personalization_display.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_display_widgets.dart';

// --- Hilfsklassen für bearbeitbare Listen-Einträge ---

// --- Top-Level Helper Widgets und Funktionen (Angepasst für dunkles Theme) ---

// --- AppBar Widget (angepasst für dunkles Theme) ---

// --- Haupt-Screen --- (Hintergrund hinzugefügt)
class ProfileScreen extends HookConsumerWidget {
  const ProfileScreen({super.key});

  static void initializeStates(
    UserProfile profile,
    ValueNotifier<List<TextEditingController>> skillsState,
    ValueNotifier<List<EditableWorkExperience>> workExperienceState,
    ValueNotifier<List<EditableEducation>> educationState,
    ProfileControllers controllers,
  ) {
    debugPrint("### initializeStates called ###");
    controllers.nameController.text = profile.name ?? '';
    controllers.emailController.text = profile.email ?? '';
    controllers.phoneController.text = profile.phoneNumber ?? '';

    final jobPrefs = profile.jobPreferencesObj;
    controllers.targetPositionController.text = jobPrefs?.targetPosition ?? '';
    controllers.industryController.text = jobPrefs?.industry ?? '';
    controllers.locationController.text = jobPrefs?.locationPreference ?? '';
    controllers.salaryController.text =
        jobPrefs?.desiredSalary?.toStringAsFixed(0) ?? ''; // No decimals
    controllers.employmentTypeController.text = jobPrefs?.employmentType ?? '';

    controllers.globalAiHintsController.text = profile.globalAiHints ?? '';
    controllers.preferredWritingStyleController.text =
        profile.preferredWritingStyle ?? 'Professionell';
    controllers.includeExperience =
        profile.includeExperienceInApplication ?? true;

    // Dispose alte Controller
    for (var controller in skillsState.value) {
      controller.dispose();
    }

    // Initialisiere Skills
    skillsState.value =
        (profile.skills ?? [])
            .map((s) => TextEditingController(text: s))
            .toList();
    debugPrint(
      "### initializeStates: ${profile.skills?.length ?? 0} Skills initialisiert ###",
    );

    // Dispose alte WorkExperience-States
    for (var state in workExperienceState.value) {
      state.dispose();
    }

    // Initialisiere WorkExperience
    if (profile.workExperience?.isNotEmpty ?? false) {
      workExperienceState.value =
          profile.workExperience!
              .map((exp) => EditableWorkExperience.fromWorkExperience(exp))
              .toList();
      debugPrint(
        "### initializeStates: ${profile.workExperience!.length} WorkExperience-Einträge initialisiert ###",
      );
    } else {
      workExperienceState.value = [];
      debugPrint(
        "### initializeStates: Keine WorkExperience-Einträge vorhanden ###",
      );
    }

    // Dispose alte Education-States
    for (var state in educationState.value) {
      state.dispose();
    }

    // Initialisiere Education
    if (profile.education?.isNotEmpty ?? false) {
      educationState.value =
          profile.education!
              .map((edu) => EditableEducation.fromEducation(edu))
              .toList();
      debugPrint(
        "### initializeStates: ${profile.education!.length} Education-Einträge initialisiert ###",
      );
    } else {
      educationState.value = [];
      debugPrint(
        "### initializeStates: Keine Education-Einträge vorhanden ###",
      );
    }

    debugPrint("### initializeStates: Initialization complete. ###");
  }

  static UserProfile _collectProfileData(
    UserProfile currentProfile,
    ProfileControllers controllers,
    ValueNotifier<List<TextEditingController>> editableSkills,
    ValueNotifier<List<EditableWorkExperience>> editableWorkExperienceStates,
    ValueNotifier<List<EditableEducation>> editableEducationStates,
    String? cvDownloadUrl, // Geändert von cvFilePath
  ) {
    final updatedWorkExperience =
        editableWorkExperienceStates.value
            .where(
              (state) =>
                  state.positionController.text.trim().isNotEmpty &&
                  state.companyController.text.trim().isNotEmpty,
            )
            .map(
              (state) => WorkExperience(
                position: state.positionController.text.trim(),
                company: state.companyController.text.trim(),
                startDate:
                    state.startDate ??
                    DateTime.now(), // Fallback, sollte nicht passieren
                endDate: state.endDate,
                description: state.descriptionController.text.trim(),
              ),
            )
            .toList();

    final updatedEducation =
        editableEducationStates.value
            .where(
              (state) =>
                  state.institutionController.text.trim().isNotEmpty &&
                  state.degreeController.text.trim().isNotEmpty,
            )
            .map(
              (state) => Education(
                institution: state.institutionController.text.trim(),
                degree: state.degreeController.text.trim(),
                startDate: state.startDate ?? DateTime.now(), // Fallback
                endDate: state.endDate,
                fieldOfStudy:
                    state.fieldOfStudyController.text.trim().isEmpty
                        ? ''
                        : state.fieldOfStudyController.text.trim(),
              ),
            )
            .toList();

    return currentProfile.copyWith(
      name: controllers.nameController.text.trim(),
      email: controllers.emailController.text.trim(),
      phoneNumber:
          controllers.phoneController.text.trim().isEmpty
              ? null
              : controllers.phoneController.text.trim(),
      jobPreferencesObj: currentProfile.jobPreferencesObj?.copyWith(
        targetPosition:
            controllers.targetPositionController.text.trim().isEmpty
                ? null
                : controllers.targetPositionController.text.trim(),
        industry:
            controllers.industryController.text.trim().isEmpty
                ? null
                : controllers.industryController.text.trim(),
        locationPreference:
            controllers.locationController.text.trim().isEmpty
                ? null
                : controllers.locationController.text.trim(),
        desiredSalary:
            int.tryParse(
              controllers.salaryController.text.replaceAll(
                RegExp(r'[^0-9]'),
                '',
              ),
            )?.toDouble(),
        employmentType:
            controllers.employmentTypeController.text.trim().isEmpty
                ? null
                : controllers.employmentTypeController.text.trim(),
      ),
      skills:
          editableSkills.value
              .map((c) => c.text.trim())
              .where((s) => s.isNotEmpty)
              .toList(), // Skills hier hinzufügen
      workExperience: updatedWorkExperience,
      education: updatedEducation,
      globalAiHints:
          controllers.globalAiHintsController.text.trim().isEmpty
              ? null
              : controllers.globalAiHintsController.text.trim(),
      preferredWritingStyle:
          controllers.preferredWritingStyleController.text.trim(),
      includeExperienceInApplication: controllers.includeExperience,
      cvDownloadUrl: cvDownloadUrl, // Geändert
      cvFilePath: null,
    );
  }

  // --- Methode verschoben ---
  // Neue Methode für den Bestätigungsdialog (jetzt als Klassenmethode)
  Future<void> _showUpdateConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    UserProfile currentProfile,
    ExtractedCvData extractedData,
    // Parameter hinzugefügt, da wir aus statischem Kontext raus sind:
    ValueNotifier<List<TextEditingController>> editableSkills,
    ValueNotifier<List<EditableWorkExperience>> editableWorkExperienceStates,
    ValueNotifier<List<EditableEducation>> editableEducationStates,
    ProfileControllers controllers,
  ) async {
    // Prüfen, ob relevante Unterschiede bestehen (vereinfacht)
    bool hasNewSkills =
        !listEquals(
          extractedData.skills.toSet().toList()..sort(),
          (currentProfile.skills ?? []).toSet().toList()..sort(),
        );

    // Wir betrachten jetzt auch leere Listen als "neue Daten", damit wir auch Daten löschen können
    bool hasNewExperience = true; // Immer aktualisieren, auch wenn leer
    bool hasNewEducation = true; // Immer aktualisieren, auch wenn leer

    // Baue eine Nachricht für den Dialog
    String changesSummary = 'Folgende Änderungen werden vorgenommen:\n';

    if (hasNewSkills) {
      if (extractedData.skills.isEmpty) {
        changesSummary += '- Alle Fähigkeiten werden entfernt\n';
      } else {
        changesSummary += '- Fähigkeiten werden aktualisiert\n';
      }
    }

    // Prüfe, ob Berufserfahrungen vorhanden sind
    if (extractedData.workExperience.isEmpty) {
      if (currentProfile.workExperience?.isNotEmpty ?? false) {
        changesSummary += '- Alle Berufserfahrungen werden entfernt\n';
      } else {
        changesSummary += '- Keine Berufserfahrungen gefunden\n';
      }
    } else {
      changesSummary +=
          '- Berufserfahrungen werden aktualisiert (${extractedData.workExperience.length} Einträge)\n';
    }

    // Prüfe, ob Ausbildungen vorhanden sind
    if (extractedData.education.isEmpty) {
      if (currentProfile.education?.isNotEmpty ?? false) {
        changesSummary += '- Alle Ausbildungen werden entfernt\n';
      } else {
        changesSummary += '- Keine Ausbildungen gefunden\n';
      }
    } else {
      changesSummary +=
          '- Ausbildungen werden aktualisiert (${extractedData.education.length} Einträge)\n';
    }

    // Wir haben die Nachricht bereits oben erstellt

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Benutzer muss eine Wahl treffen
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: AppTheme.surfaceDarkColor.withValues(
            alpha: 0.95,
          ), // Darker background
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
          title: const Text(
            'Profil aktualisieren?',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Text(
              changesSummary,
              style: const TextStyle(color: Colors.white70),
            ),
          ),
          actions: <Widget>[
            TextButton(
              style: Theme.of(dialogContext).textButtonTheme.style?.copyWith(
                foregroundColor: WidgetStateProperty.all(Colors.grey),
              ),
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('Nein, alte Daten behalten'),
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryLightColor,
              ),
              child: const Text('Ja, aktualisieren'),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      debugPrint("[ProfileScreen] Benutzer bestätigt Profil-Update.");
      try {
        // Erstelle aktualisiertes Profil
        final updatedProfile = currentProfile.copyWith(
          // Überschreibe die Daten aus dem Lebenslauf
          skills: hasNewSkills ? extractedData.skills : currentProfile.skills,
          // Nur überschreiben, wenn nicht leer oder wenn vorher Daten vorhanden waren
          workExperience:
              extractedData.workExperience.isNotEmpty ||
                      (currentProfile.workExperience?.isNotEmpty ?? false)
                  ? extractedData.workExperience
                  : currentProfile.workExperience,
          // Nur überschreiben, wenn nicht leer oder wenn vorher Daten vorhanden waren
          education:
              extractedData.education.isNotEmpty ||
                      (currentProfile.education?.isNotEmpty ?? false)
                  ? extractedData.education
                  : currentProfile.education,
          // Behalte andere Felder wie Name, E-Mail, Job-Präferenzen etc. bei
        );

        // Speichere das komplett aktualisierte Profil
        await ref
            .read(userProfileProvider.notifier)
            .saveProfileChanges(updatedProfile);

        // Zeige Erfolgsmeldung für die Profilaktualisierung
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Profil erfolgreich mit Daten aus Lebenslauf aktualisiert.',
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Aktualisiere die lokalen Controller nach dem Speichern
        initializeStates(
          updatedProfile,
          editableSkills,
          editableWorkExperienceStates,
          editableEducationStates,
          controllers,
        );

        // Automatisch Berufsbezeichnungen generieren
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Generiere Berufsbezeichnungen...'),
              duration: Duration(seconds: 2),
            ),
          );

          // Rufe die Funktion zum Generieren der Berufsbezeichnungen auf
          final success =
              await ref.read(userProfileProvider.notifier).updateJobKeywords();

          // Zeige eine Erfolgsmeldung oder Fehlermeldung
          if (context.mounted) {
            if (success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Berufsbezeichnungen erfolgreich generiert!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 3),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Fehler beim Generieren der Berufsbezeichnungen.',
                  ),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 3),
                ),
              );
            }
          }
        }
      } catch (e, stack) {
        debugPrint(
          "[ProfileScreen] Fehler beim Speichern des aktualisierten Profils: $e\n$stack",
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Fehler beim Aktualisieren des Profils: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      debugPrint("[ProfileScreen] Benutzer lehnt Profil-Update ab.");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profil wurde nicht aktualisiert.')),
      );
    }
  }
  // --- Ende verschobene Methode ---

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    debugPrint('ProfileScreen build!');
    final profileState = ref.watch(userProfileProvider);
    final isEditing = useState<bool>(false);
    final isSaving = useState<bool>(false);
    final errorState = useState<String?>(null);
    final controllers = useMemoized(() => ProfileControllers(), const []);
    final editableSkills = useState<List<TextEditingController>>([]);
    final editableWorkExperienceStates = useState<List<EditableWorkExperience>>(
      [],
    );
    final editableEducationStates = useState<List<EditableEducation>>([]);
    final isControllerReady = useState<bool>(false);
    final lastAddedSkillIndex = useState<int>(-1);
    final lastAddedWorkIndex = useState<int>(-1);
    final lastAddedEducationIndex = useState<int>(-1);
    final localCvFilePath = useState<String?>(
      null,
    ); // State für den *lokal* ausgewählten Pfad
    final isUploadingCv = useState<bool>(false); // State für den Upload-Vorgang

    useEffect(() {
      return () {
        controllers.dispose();
        for (var controller in editableSkills.value) {
          controller.dispose();
        }
        for (var state in editableWorkExperienceStates.value) {
          state.dispose();
        }
        for (var state in editableEducationStates.value) {
          state.dispose();
        }
      };
    }, [controllers]); // Dependency only on controllers object identity

    useEffect(() {
      UserProfile? profileData;
      if (profileState is AsyncData<UserProfile>) {
        profileData = profileState.value;
      } else if (profileState is AsyncError &&
          profileState.hasValue &&
          profileState.value is UserProfile) {
        profileData = profileState.value as UserProfile;
      }

      if (profileData != null) {
        if (!isControllerReady.value || !isEditing.value) {
          initializeStates(
            profileData,
            editableSkills,
            editableWorkExperienceStates,
            editableEducationStates,
            controllers,
          );
          final initialDownloadUrl = profileData.cvDownloadUrl;
          debugPrint(
            "Initial CV Download URL from profile: $initialDownloadUrl",
          );
          if (context.mounted) {
            isControllerReady.value = true;
          }
        }
      } else {
        if (isControllerReady.value) {
          isControllerReady.value = false;
        }
      }
      return null;
    }, [profileState, isEditing.value]);

    // Upload-Methode angepasst
    Future<void> uploadAndProcessCv(File file) async {
      // --- NEU: Klare Statusmeldungen ---
      // Funktion zum Anzeigen von Snackbars
      void showStatusSnackBar(
        String message, {
        Color? backgroundColor,
        Duration duration = const Duration(seconds: 3),
      }) {
        // Kürzere Dauer
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: backgroundColor,
            duration: duration,
          ),
        );
      }

      isUploadingCv.value = true;
      // showStatusSnackBar('Lebenslauf wird im Hintergrund analysiert'); // Erste Nachricht (wird ersetzt)
      showStatusSnackBar(
        'Dein Lebenslauf wird vorbereitet...',
      ); // Benutzerfreundlicher

      try {
        // Basisdaten holen
        final fileName = p.basename(file.path);
        final currentUser = Supabase.instance.client.auth.currentUser;
        final userId = currentUser?.id ?? '';
        if (userId.isEmpty) throw Exception('Benutzer nicht angemeldet.');

        // Verwende die UserProfileProvider-Methode für den Upload
        showStatusSnackBar('Sichere Übertragung startet...');

        // Rufe die Methode im UserProfileProvider auf, die den Upload und die Analyse durchführt
        showStatusSnackBar('Analyse deines Dokuments beginnt...');
        final extractedData = await ref
            .read(userProfileProvider.notifier)
            .uploadAndAnalyzeCv(file.path, fileName);

        debugPrint(
          "[ProfileScreen] CV Upload zu Supabase Storage erfolgreich.",
        );

        // Prüfe, ob Daten extrahiert wurden
        if (extractedData != null) {
          debugPrint(
            "[ProfileScreen] Daten erfolgreich aus dem Lebenslauf extrahiert.",
          );

          // Zeige den Bestätigungsdialog an
          final currentProfile = ref.read(userProfileProvider).valueOrNull;
          if (currentProfile != null && context.mounted) {
            await _showUpdateConfirmationDialog(
              context,
              ref,
              currentProfile,
              extractedData,
              editableSkills,
              editableWorkExperienceStates,
              editableEducationStates,
              controllers,
            );
          } else {
            showStatusSnackBar(
              'Lebenslauf verarbeitet, aber Profil nicht verfügbar für Aktualisierung.',
              backgroundColor: Colors.orange,
            );
          }
        } else {
          showStatusSnackBar(
            'Lebenslauf hochgeladen, aber keine Daten konnten extrahiert werden.',
            backgroundColor: Colors.orange,
          );
        }
        // Erfolgsmeldung wird jetzt im Dialog oder nach dem Dialog behandelt, falls das Profil nicht aktualisiert wird.
        // showStatusSnackBar('Lebenslauf erfolgreich verarbeitet!', backgroundColor: Colors.green, duration: const Duration(seconds: 5));
      } catch (e, stackTrace) {
        debugPrint("[ProfileScreen] Fehler im Upload/Processing Flow: $e");
        debugPrint(stackTrace.toString());
        // Zeige benutzerfreundliche Fehler-SnackBar
        // Extrahiere nur die Kernaussage des Fehlers für die Anzeige
        String errorMessage = e.toString();
        if (e is Exception) {
          errorMessage = e.toString().replaceFirst('Exception: ', '');
        }
        showStatusSnackBar(
          'Fehler: $errorMessage',
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 6),
        );
      } finally {
        isUploadingCv.value = false;
        // Die letzte SnackBar (Erfolg oder Fehler) bleibt länger sichtbar (6s / im Dialog behandelt)
      }
    }

    Future<void> saveProfile() async {
      final currentProfileData = profileState.valueOrNull;
      if (currentProfileData == null) {
        context.showErrorSnackBar('Profil konnte nicht geladen werden.');
        return;
      }
      if (!isControllerReady.value && isEditing.value) {
        context.showErrorSnackBar(
          'Profil-Daten sind noch nicht bereit zum Bearbeiten.',
        );
        return;
      }

      if (controllers.nameController.text.trim().isEmpty) {
        context.showErrorSnackBar('Name darf nicht leer sein.');
        return;
      }

      errorState.value = null;
      isSaving.value = true;
      String? finalCvDownloadUrl = currentProfileData.cvDownloadUrl;

      try {
        final updatedProfileData = _collectProfileData(
          currentProfileData,
          controllers,
          editableSkills,
          editableWorkExperienceStates,
          editableEducationStates,
          finalCvDownloadUrl,
        );
        await ref
            .read(userProfileProvider.notifier)
            .saveProfileChanges(updatedProfileData);
        isEditing.value = false;
        context.showSuccessSnackBar('Profil erfolgreich gespeichert!');
      } catch (e, stackTrace) {
        debugPrint("Error saving profile: $e\nStack trace: $stackTrace");
        errorState.value = "Fehler beim Speichern des Profils: ${e.toString()}";
        context.showErrorSnackBar(errorState.value!);
      } finally {
        if (context.mounted) {
          isSaving.value = false;
        }
      }
    }

    void cancelEditing() {
      final currentProfile = profileState.valueOrNull;
      if (currentProfile != null) {
        initializeStates(
          currentProfile,
          editableSkills,
          editableWorkExperienceStates,
          editableEducationStates,
          controllers,
        );
        localCvFilePath.value = currentProfile.cvFilePath;
        errorState.value = null;
      }
      isEditing.value = false;
    }

    void toggleEdit() {
      if (isEditing.value) {
        cancelEditing();
      } else {
        UserProfile? profileData = profileState.valueOrNull;
        localCvFilePath.value = profileData?.cvFilePath;
        isEditing.value = true;
        isControllerReady.value = false;
      }
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: ProfileAppBar(
        isEditing: isEditing.value,
        onToggleEdit: toggleEdit,
        onSave: saveProfile,
        isUploadingCv: isUploadingCv,
      ),
      body: Container(
        // HIER WIRD DER CONTAINER MIT DEM GRADIENTEN ENTFERNT
        /*
          decoration: const BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0,
              colors: [
                Color(0xFF3A1A7E),
                AppTheme.backgroundDarkColor,
              ],
              stops: [0.0, 1.0],
            ),
          ),
          */
        // ENDE ENTFERNTER CONTAINER
        // Der Inhalt (profileState.when) bleibt direkt im body des Scaffolds
        child: profileState.when(
          loading:
              () => const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
          error: (err, stack) {
            UserProfile? cachedProfile;
            if (err is UserProfile) cachedProfile = err;
            if (cachedProfile != null) {
              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      top: kToolbarHeight + AppTheme.spacingMedium,
                      left: AppTheme.spacingMedium,
                      right: AppTheme.spacingMedium,
                    ),
                    child: ErrorMessage(
                      message:
                          "Fehler beim Laden: ${err.toString().split(':').first}. Zeige Cache.",
                      onRetry: () => ref.refresh(userProfileProvider),
                    ),
                  ),
                  Expanded(
                    child: _buildProfileView(
                      context,
                      ref,
                      cachedProfile,
                      isEditing,
                      isSaving,
                      errorState,
                      controllers,
                      editableSkills,
                      editableWorkExperienceStates,
                      editableEducationStates,
                      isControllerReady,
                      lastAddedSkillIndex,
                      lastAddedWorkIndex,
                      lastAddedEducationIndex,
                      saveProfile,
                      cancelEditing,
                      localCvFilePath,
                      isUploadingCv,
                      uploadAndProcessCv,
                      AsyncValue.data(cachedProfile),
                    ),
                  ),
                ],
              );
            } else {
              return Center(
                child: ErrorMessage(
                  message:
                      "Fehler beim Laden des Profils: ${err.toString().split(':').first}",
                  onRetry: () => ref.refresh(userProfileProvider),
                ),
              );
            }
          },
          data: (userProfile) {
            if ((!isControllerReady.value && isEditing.value) ||
                (profileState.isLoading && !isEditing.value)) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.white),
              );
            }

            return SafeArea(
              top: true,
              bottom: false,
              child: _buildProfileView(
                context,
                ref,
                userProfile,
                isEditing,
                isSaving,
                errorState,
                controllers,
                editableSkills,
                editableWorkExperienceStates,
                editableEducationStates,
                isControllerReady,
                lastAddedSkillIndex,
                lastAddedWorkIndex,
                lastAddedEducationIndex,
                saveProfile,
                cancelEditing,
                localCvFilePath,
                isUploadingCv,
                uploadAndProcessCv,
                profileState,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileView(
    BuildContext context,
    WidgetRef ref,
    UserProfile userProfile,
    ValueNotifier<bool> isEditing,
    ValueNotifier<bool> isSaving,
    ValueNotifier<String?> errorState,
    ProfileControllers controllers,
    ValueNotifier<List<TextEditingController>> editableSkills,
    ValueNotifier<List<EditableWorkExperience>> editableWorkExperienceStates,
    ValueNotifier<List<EditableEducation>> editableEducationStates,
    ValueNotifier<bool> isControllerReady,
    ValueNotifier<int> lastAddedSkillIndex,
    ValueNotifier<int> lastAddedWorkIndex,
    ValueNotifier<int> lastAddedEducationIndex,
    VoidCallback onSaveChanges,
    VoidCallback onCancelEditing,
    ValueNotifier<String?> localCvFilePath,
    ValueNotifier<bool> isUploadingCv,
    Function(File) uploadAndProcessCvCallback, // Korrigierter Typ
    AsyncValue<UserProfile> profileState,
  ) {
    final formKey = useMemoized(() => GlobalKey<FormState>(), [
      isEditing.value,
    ]);

    return Form(
      key: formKey,
      child: SingleChildScrollView(
        // Füge ein Padding am unteren Rand hinzu, um Platz für die Navigation zu schaffen
        padding: const EdgeInsets.only(
          left: AppTheme.spacingMedium,
          right: AppTheme.spacingMedium,
          bottom:
              kBottomNavigationBarHeight -
              32, // Stark verringerter Abstand zur Navigation
          top: AppTheme.spacingSmall,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (errorState.value != null)
              Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                child: Text(
                  errorState.value!,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
              ),

            if (isEditing.value) ...[
              _buildModernEditableSection(
                context,
                title: AppLocalizationsWrapper.of(context).personalData,
                icon: Icons.person_outline,
                children: [
                  buildModernTextField(
                    context,
                    controller: controllers.nameController,
                    label: AppLocalizationsWrapper.of(context).fullName,
                    icon: Icons.person_pin_outlined,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.emailController,
                    label: AppLocalizationsWrapper.of(context).email,
                    icon: Icons.email_outlined,
                    readOnly: true,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.phoneController,
                    label: AppLocalizationsWrapper.of(context).phoneOptional,
                    icon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  buildModernTextField(
                    context,
                    controller: controllers.addressController,
                    label: AppLocalizationsWrapper.of(context).address,
                    icon: Icons.home_outlined,
                  ),
                ],
              ),
              _buildModernEditableSection(
                context,
                title: AppLocalizationsWrapper.of(context).resume,
                icon: Icons.upload_file,
                children: [
                  _EditableCvSection(
                    initialDownloadUrl: userProfile.cvDownloadUrl,
                    locallySelectedFilePath: localCvFilePath.value,
                    onFilePicked: (path) {
                      if (path != null) {
                        localCvFilePath.value = path;
                        // Rufe die Callback-Funktion mit dem profileState auf
                        uploadAndProcessCvCallback(File(path));
                      } else {
                        localCvFilePath.value = null;
                      }
                    },
                    onClearSelection: () {
                      localCvFilePath.value = null;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Lokale Auswahl entfernt.'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  // Button "Berufsbezeichnungen generieren" wurde entfernt, da die Generierung jetzt automatisch nach dem Hochladen eines Lebenslaufs erfolgt
                ],
              ),
              DropdownProfileSection(
                title: 'Fähigkeiten',
                icon: Icons.lightbulb_outline,
                isExpanded: false,
                onEdit: () {
                  // Neue Fähigkeit hinzufügen
                  final newSkillController = TextEditingController();
                  editableSkills.value = List.from(editableSkills.value)
                    ..add(newSkillController);
                  lastAddedSkillIndex.value = editableSkills.value.length - 1;
                },
                child: EditableSkillsListRefactored(
                  key: ValueKey(
                    'skills_${editableSkills.value.length}_${userProfile.id}',
                  ),
                  initialSkills: editableSkills.value,
                  lastAddedIndex: lastAddedSkillIndex.value,
                  onRemoveSkill: (index) {
                    confirmModernRemoval(
                      context,
                      'Fähigkeit wirklich löschen?',
                      () {
                        if (index >= 0 && index < editableSkills.value.length) {
                          editableSkills.value[index].dispose();
                        }
                        editableSkills.value = List.from(editableSkills.value)
                          ..removeAt(index);
                        lastAddedSkillIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              DropdownProfileSection(
                title: 'Berufserfahrung',
                icon: Icons.work_history_outlined,
                isExpanded: false,
                onEdit: () {
                  // Neue Berufserfahrung hinzufügen
                  final newExperience = EditableWorkExperience(
                    positionController: TextEditingController(),
                    companyController: TextEditingController(),
                    descriptionController: TextEditingController(),
                    startDate: DateTime.now(),
                  );
                  editableWorkExperienceStates.value = List.from(
                    editableWorkExperienceStates.value,
                  )..add(newExperience);
                  lastAddedWorkIndex.value =
                      editableWorkExperienceStates.value.length - 1;
                },
                child: EditableWorkExperienceListRefactored(
                  key: ValueKey(
                    'work_${editableWorkExperienceStates.value.length}_${userProfile.id}',
                  ),
                  initialExperiences: editableWorkExperienceStates.value,
                  lastAddedIndex: lastAddedWorkIndex.value,
                  onExperienceChanged: (index, updatedExperience) {
                    final currentStates = List<EditableWorkExperience>.from(
                      editableWorkExperienceStates.value,
                    );
                    if (index >= 0 && index < currentStates.length) {
                      currentStates[index] = updatedExperience;
                      editableWorkExperienceStates.value = currentStates;
                    }
                  },
                  onRemoveExperience: (index) {
                    confirmModernRemoval(
                      context,
                      'Berufserfahrung wirklich löschen?',
                      () {
                        if (index >= 0 &&
                            index < editableWorkExperienceStates.value.length) {
                          editableWorkExperienceStates.value[index].dispose();
                        }
                        editableWorkExperienceStates.value = List.from(
                          editableWorkExperienceStates.value,
                        )..removeAt(index);
                        lastAddedWorkIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              DropdownProfileSection(
                title: 'Ausbildung',
                icon: Icons.school_outlined,
                isExpanded: false,
                onEdit: () {
                  // Neue Ausbildung hinzufügen
                  final newEducation = EditableEducation(
                    institutionController: TextEditingController(),
                    degreeController: TextEditingController(),
                    fieldOfStudyController: TextEditingController(),
                    startDate: DateTime.now(),
                  );
                  editableEducationStates.value = List.from(
                    editableEducationStates.value,
                  )..add(newEducation);
                  lastAddedEducationIndex.value =
                      editableEducationStates.value.length - 1;
                },
                child: EditableEducationListRefactored(
                  key: ValueKey(
                    'edu_${editableEducationStates.value.length}_${userProfile.id}',
                  ),
                  initialEducations: editableEducationStates.value,
                  lastAddedIndex: lastAddedEducationIndex.value,
                  onEducationChanged: (index, updatedEducation) {
                    final currentStates = List<EditableEducation>.from(
                      editableEducationStates.value,
                    );
                    if (index >= 0 && index < currentStates.length) {
                      currentStates[index] = updatedEducation;
                      editableEducationStates.value = currentStates;
                    }
                  },
                  onRemoveEducation: (index) {
                    confirmModernRemoval(
                      context,
                      'Ausbildung wirklich löschen?',
                      () {
                        if (index >= 0 &&
                            index < editableEducationStates.value.length) {
                          editableEducationStates.value[index].dispose();
                        }
                        editableEducationStates.value = List.from(
                          editableEducationStates.value,
                        )..removeAt(index);
                        lastAddedEducationIndex.value = -1;
                      },
                    );
                  },
                ),
              ),
              _buildModernEditableSection(
                context,
                title: 'KI Personalisierung',
                icon: Icons.auto_awesome,
                children: [
                  EditableKiPersonalizationNew(
                    controllers: controllers,
                    initialIncludeExperience: controllers.includeExperience,
                    onIncludeExperienceChanged: (value) {
                      controllers.includeExperience = value;
                    },
                    preferredWritingStyleController:
                        controllers.preferredWritingStyleController,
                    onStyleChanged: (newValue) async {
                      controllers.preferredWritingStyleController.text =
                          newValue ?? 'Professionell';

                      // Wenn "Passend zu meinem Stil" ausgewählt wurde, generiere den personalisierten Schreibstil-Prompt
                      if (newValue == 'Passend zu meinem Stil') {
                        // Speichere den BuildContext für spätere Verwendung
                        final currentContext = context;

                        try {
                          // Zeige Ladeindikator
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            const SnackBar(
                              content: Text('Analysiere deinen Schreibstil...'),
                              duration: Duration(seconds: 2),
                            ),
                          );

                          // Rufe die Supabase-Funktion auf, um den personalisierten Schreibstil zu generieren
                          final supabaseService = ref.read(
                            supabaseServiceProvider,
                          );
                          final stylePrompt =
                              await supabaseService.analyzeProfileStyle();

                          // Prüfe, ob der Widget noch mounted ist, bevor wir den BuildContext verwenden
                          if (currentContext.mounted &&
                              stylePrompt.isNotEmpty) {
                            ScaffoldMessenger.of(currentContext).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Dein persönlicher Schreibstil wurde erfolgreich analysiert!',
                                ),
                                backgroundColor: Colors.green,
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        } catch (e) {
                          debugPrint(
                            'Fehler bei der Generierung des personalisierten Schreibstils: $e',
                          );
                          // Prüfe, ob der Widget noch mounted ist, bevor wir den BuildContext verwenden
                          if (currentContext.mounted) {
                            ScaffoldMessenger.of(currentContext).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Fehler bei der Analyse deines Schreibstils: ${e.toString()}',
                                ),
                                backgroundColor: Colors.red,
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        }
                      }
                    },
                  ),
                ],
              ),
            ] else ...[
              _buildModernDisplaySection(
                context,
                title: 'Persönliche Daten',
                icon: Icons.person_outline,
                children: [
                  buildModernInfoRow('Name', userProfile.name ?? ''),
                  buildModernInfoRow('E-Mail', userProfile.email ?? ''),
                  if (userProfile.phoneNumber != null)
                    buildModernInfoRow('Telefon', userProfile.phoneNumber!),
                  const SizedBox(height: AppTheme.spacingSmall),
                  buildModernInfoRow(
                    'Gewünschte Position',
                    userProfile.jobPreferencesObj?.targetPosition ??
                        'Nicht angegeben',
                  ),
                  buildModernInfoRow(
                    'Gehaltsvorstellung',
                    _formatCurrency(
                      userProfile.jobPreferencesObj?.desiredSalary,
                    ),
                  ),
                  buildModernInfoRow(
                    'Bevorzugter Ort',
                    userProfile.jobPreferencesObj?.locationPreference ??
                        'Nicht angegeben',
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Lebenslauf',
                icon: Icons.description_outlined,
                // Übergibt beide Werte an das Display Widget
                children: [
                  DisplayCvSection(
                    cvDownloadUrl: userProfile.cvDownloadUrl,
                    cvFilePath: userProfile.cvFilePath,
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Fähigkeiten',
                icon: Icons.lightbulb_outline,
                children: [
                  DisplaySkillsListRefactored(skills: userProfile.skills ?? []),
                  // Button "Berufsbezeichnungen generieren" wurde entfernt, da die Generierung jetzt automatisch nach dem Hochladen eines Lebenslaufs erfolgt
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Berufserfahrung',
                icon: Icons.work_history_outlined,
                children: [
                  DisplayWorkExperienceList(
                    experiences: userProfile.workExperience ?? [],
                  ),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'Ausbildung',
                icon: Icons.school_outlined,
                children: [
                  DisplayEducationList(educations: userProfile.education ?? []),
                ],
              ),
              _buildModernDisplaySection(
                context,
                title: 'KI Personalisierung',
                icon: Icons.auto_awesome,
                children: [
                  DisplayKiPersonalization(
                    cvAutoFillEnabled: false, // Fallback
                    skillsInApplication: true, // Fallback
                    workExperienceInApplication:
                        userProfile.includeExperienceInApplication ?? true,
                    educationInApplication:
                        true, // Verwende direkt boolean statt Getter
                    isPersonalized:
                        userProfile.preferredWritingStyle != null &&
                        userProfile.preferredWritingStyle!.isNotEmpty,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// --- Section Widgets (Editable & Display) ---
Widget _buildModernEditableSection(
  BuildContext context, {
  required String title,
  required IconData icon,
  required List<Widget> children,
  VoidCallback? onAdd,
}) {
  return ModernSectionCardWidget(
    title: title,
    icon: icon,
    onAdd: onAdd,
    isEditing: true,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    ),
  );
}

Widget _buildModernDisplaySection(
  BuildContext context, {
  required String title,
  required IconData icon,
  required List<Widget> children,
}) {
  final nonEmptyChildren =
      children
          .where((child) => !(child is SizedBox && child.height == 0))
          .toList();
  if (nonEmptyChildren.isEmpty) return const SizedBox.shrink();
  return ModernSectionCardWidget(
    title: title,
    icon: icon,
    isEditing: false,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: nonEmptyChildren,
    ),
  );
}

// --- Listen Widgets ---

class _EditableEducationListRefactoredState
    extends ConsumerState<_EditableEducationListRefactored> {
  late List<EditableEducation> _editableEducations = [];
  final Map<int, GlobalKey> _itemKeys = {};
  int _highlightedIndex = -1;

  @override
  void initState() {
    super.initState();
    _highlightedIndex = widget.lastAddedIndex;
    _updateEditableEducations(widget.initialEducations);
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToHighlighted());
  }

  @override
  void didUpdateWidget(_EditableEducationListRefactored oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!listEquals(widget.initialEducations, oldWidget.initialEducations)) {
      _updateEditableEducations(widget.initialEducations);
    }
    if (widget.lastAddedIndex != oldWidget.lastAddedIndex) {
      if (mounted) {
        setState(() {
          _highlightedIndex = widget.lastAddedIndex;
        });
      }
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _scrollToHighlighted(),
      );
    }
  }

  @override
  void dispose() {
    for (var state in _editableEducations) {
      state.dispose();
    }
    super.dispose();
  }

  void _updateEditableEducations(List<EditableEducation> newEducations) {
    if (mounted) {
      for (var state in _editableEducations) {
        state.dispose();
      }
    }

    _editableEducations =
        newEducations
            .map(
              (edu) => EditableEducation(
                institutionController: TextEditingController(
                  text: edu.institutionController.text,
                ),
                degreeController: TextEditingController(
                  text: edu.degreeController.text,
                ),
                fieldOfStudyController: TextEditingController(
                  text: edu.fieldOfStudyController.text,
                ),
                startDate: edu.startDate,
                endDate: edu.endDate,
              ),
            )
            .toList();

    _itemKeys.clear();
    for (int i = 0; i < _editableEducations.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    if (mounted) {
      setState(() {});
    }
  }

  void _scrollToHighlighted() {
    if (!mounted) return;
    if (_highlightedIndex >= 0 &&
        _highlightedIndex < _editableEducations.length) {
      final key = _itemKeys[_highlightedIndex];
      if (key?.currentContext != null) {
        Scrollable.ensureVisible(
          key!.currentContext!,
          alignment: 0.5,
          duration: const Duration(milliseconds: 300),
        ).then((_) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted && _highlightedIndex == widget.lastAddedIndex) {
              setState(() {
                _highlightedIndex = -1;
              });
            }
          });
        });
      }
    } else if (_highlightedIndex != -1) {
      if (mounted) {
        setState(() {
          _highlightedIndex = -1;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (_editableEducations.isEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              AppLocalizationsWrapper.of(context).noEducation,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ..._editableEducations.asMap().entries.map((entry) {
          int index = entry.key;
          EditableEducation state = entry.value;
          bool isHighlighted = index == _highlightedIndex;
          final itemKey = _itemKeys[index]!;

          return Container(
            key: itemKey,
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(12),
              border:
                  isHighlighted
                      ? Border.all(
                        color: AppTheme.primaryLightColor,
                        width: 1.5,
                      )
                      : Border.all(color: Colors.transparent, width: 0.5),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildModernTextField(
                        context,
                        controller: state.institutionController,
                        label: AppLocalizationsWrapper.of(context).institution,
                        icon: Icons.school_outlined,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.degreeController,
                        label: AppLocalizationsWrapper.of(context).degree,
                        icon: Icons.grade_outlined,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.fieldOfStudyController,
                        label: AppLocalizationsWrapper.of(context).fieldOfStudy,
                        icon: Icons.book_outlined,
                      ),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: buildModernDateField(
                              context: context,
                              label:
                                  AppLocalizationsWrapper.of(context).startDate,
                              selectedDate: state.startDate,
                              isOptional: false,
                              onTap:
                                  () => selectModernDate(
                                    context,
                                    initialDate: state.startDate,
                                    firstDate: DateTime(1980),
                                    lastDate:
                                        state.endDate ??
                                        DateTime.now().add(
                                          const Duration(days: 365 * 10),
                                        ),
                                    onDateSelected: (pickedDate) {
                                      if (state.endDate != null &&
                                          pickedDate.isAfter(state.endDate!)) {
                                        context.showErrorSnackBar(
                                          AppLocalizationsWrapper.of(
                                            context,
                                          ).startDateError,
                                        );
                                      } else {
                                        widget.onEducationChanged(
                                          index,
                                          state.copyWith(startDate: pickedDate),
                                        );
                                      }
                                    },
                                  ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: buildModernDateField(
                              context: context,
                              label:
                                  AppLocalizationsWrapper.of(context).endDate,
                              selectedDate: state.endDate,
                              isOptional: true,
                              onTap:
                                  () => selectModernDate(
                                    context,
                                    initialDate:
                                        state.endDate ?? DateTime.now(),
                                    firstDate: state.startDate,
                                    lastDate: DateTime.now().add(
                                      const Duration(days: 365 * 10),
                                    ),
                                    onDateSelected: (pickedDate) {
                                      widget.onEducationChanged(
                                        index,
                                        state.copyWith(endDate: pickedDate),
                                      );
                                    },
                                  ),
                              onClear: () {
                                widget.onEducationChanged(
                                  index,
                                  state.copyWith(clearEndDate: true),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    icon: const Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(),
                    onPressed: () => widget.onRemoveEducation(index),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }
}

// Editierbare Skills (kompakt, Button immer rechts mittig)
class _EditableSkillsListRefactored extends HookWidget {
  final List<TextEditingController> initialSkills;
  final Function(int) onRemoveSkill;
  final int lastAddedIndex;

  const _EditableSkillsListRefactored({
    required this.initialSkills,
    required this.onRemoveSkill,
  });

  @override
  Widget build(BuildContext context) {
    final itemKeys = useState<Map<int, GlobalKey>>({});
    final highlightedIndex = useState<int>(lastAddedIndex);
    final isMounted = useIsMounted();

    useEffect(() {
      final currentKeys = <int, GlobalKey>{};
      for (int i = 0; i < initialSkills.length; i++) {
        currentKeys[i] = itemKeys.value[i] ?? GlobalKey();
      }
      itemKeys.value = currentKeys;
      return null;
    }, [initialSkills]);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (isMounted() &&
            lastAddedIndex >= 0 &&
            lastAddedIndex < initialSkills.length) {
          highlightedIndex.value = lastAddedIndex;
          final key = itemKeys.value[lastAddedIndex];
          if (key?.currentContext != null) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              alignment: 0.5,
              duration: const Duration(milliseconds: 300),
            ).then((_) {
              Future.delayed(const Duration(seconds: 2), () {
                if (isMounted() && highlightedIndex.value == lastAddedIndex) {
                  highlightedIndex.value = -1;
                }
              });
            });
          }
        } else if (lastAddedIndex == -1 && highlightedIndex.value != -1)
          highlightedIndex.value = -1;
      });
      return null;
    }, [lastAddedIndex, initialSkills.length]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (initialSkills.isEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              AppLocalizationsWrapper.of(context).noSkills,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children:
              initialSkills.asMap().entries.map((entry) {
                int index = entry.key;
                TextEditingController controller = entry.value;
                bool isHighlighted = index == highlightedIndex.value;
                final itemKey = itemKeys.value[index] ?? GlobalKey();
                if (!itemKeys.value.containsKey(index)) {
                  itemKeys.value = {...itemKeys.value, index: itemKey};
                }

                return Container(
                  key: itemKey,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors:
                          isHighlighted
                              ? [
                                AppTheme.primaryLightColor.withValues(
                                  alpha: 0.25,
                                ),
                                AppTheme.primaryLightColor.withValues(
                                  alpha: 0.15,
                                ),
                              ]
                              : [
                                AppTheme.primaryLightColor.withValues(
                                  alpha: 0.15,
                                ),
                                AppTheme.primaryLightColor.withValues(
                                  alpha: 0.08,
                                ),
                              ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isHighlighted
                              ? AppTheme.primaryLightColor.withValues(
                                alpha: 0.5,
                              )
                              : AppTheme.primaryLightColor.withValues(
                                alpha: 0.3,
                              ),
                      width: 0.8,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                      BoxShadow(
                        color: AppTheme.primaryLightColor.withValues(
                          alpha: 0.08,
                        ),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                      if (isHighlighted)
                        BoxShadow(
                          color: AppTheme.primaryLightColor.withValues(
                            alpha: 0.15,
                          ),
                          blurRadius: 6,
                          spreadRadius: 0.5,
                        ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryLightColor.withValues(
                            alpha: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          Icons.psychology_outlined,
                          color: AppTheme.primaryLightColor,
                          size: 10,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: IntrinsicWidth(
                          child: TextField(
                            controller: controller,
                            decoration: InputDecoration(
                              hintText:
                                  AppLocalizationsWrapper.of(
                                    context,
                                  ).enterSkill,
                              hintStyle: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color: Colors.white.withValues(alpha: 0.5),
                                fontWeight: FontWeight.w400,
                                fontSize: 11,
                              ),
                              border: InputBorder.none,
                              isDense: true,
                              contentPadding: EdgeInsets.zero,
                            ),
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(4),
                          onTap: () => onRemoveSkill(index),
                          child: Icon(Icons.close, color: Colors.red, size: 10),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}

// Display Skills
class _DisplaySkillsListRefactored extends StatelessWidget {
  final List<String> skills;
  const _DisplaySkillsListRefactored({required this.skills});

  @override
  Widget build(BuildContext context) {
    if (skills.isEmpty) {
      return const Text(
        'Keine Fähigkeiten angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: skills.map((skill) => _buildSkillChip(context, skill)).toList(),
    );
  }

  Widget _buildSkillChip(BuildContext context, String skill) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryLightColor.withValues(alpha: 0.15),
            AppTheme.primaryLightColor.withValues(alpha: 0.08),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
          width: 0.8,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
          BoxShadow(
            color: AppTheme.primaryLightColor.withValues(alpha: 0.08),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              Icons.psychology_outlined,
              color: AppTheme.primaryLightColor,
              size: 10,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            skill,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }
}

// --- Display Listen Widgets (Angepasst für dunkles Theme) ---

// Display CV (Angepasst, um Download URL zu nutzen)
class _DisplayCvSection extends StatelessWidget {
  final String? cvDownloadUrl;
  final String? cvFilePath; // cvFilePath nur noch als Fallback für den Namen
  const _DisplayCvSection();

  // Methode zum Öffnen einer Datei mit dem nativen FileProvider
  Future<bool> _openFileWithProvider(String filePath) async {
    try {
      // Prüfe, ob die Datei existiert
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('Datei existiert nicht: $filePath');
        return false;
      }

      // Prüfe, ob die Datei eine PDF-Datei ist
      final bytes = await file.readAsBytes();
      if (bytes.length < 5 || !_isPdfContent(bytes)) {
        debugPrint('Datei ist kein gültiges PDF: $filePath');
        return false;
      }

      // Versuche, die Datei mit dem nativen FileProvider zu öffnen
      const platform = MethodChannel('com.einsteinai.app/file_provider');
      final result = await platform.invokeMethod('openPdfWithProvider', {
        'filePath': filePath,
      });

      if (result == true) {
        debugPrint('Datei erfolgreich mit nativem FileProvider geöffnet');
        return true;
      }

      // Fallback: Versuche, die Datei mit url_launcher zu öffnen
      final uri = Uri.file(filePath);
      if (await canLaunchUrl(uri)) {
        debugPrint('Öffne Datei mit url_launcher: $uri');
        await launchUrl(uri);
        return true;
      }

      debugPrint('Konnte Datei nicht öffnen: $filePath');
      return false;
    } catch (e) {
      debugPrint('Fehler beim Aufrufen der nativen Methode: $e');
      return false;
    }
  }

  // Prüft, ob der Inhalt ein PDF ist (einfache Signaturprüfung)
  bool _isPdfContent(List<int> bytes) {
    if (bytes.length < 5) return false;

    // PDF-Dateien beginnen mit der Signatur "%PDF-"
    final signature = String.fromCharCodes(bytes.sublist(0, 5));
    return signature == "%PDF-";
  }

  Future<void> _launchCvUrl(BuildContext context) async {
    // Zeige Ladeindikator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Lebenslauf wird vorbereitet...'),
        duration: Duration(seconds: 1),
        behavior: SnackBarBehavior.fixed,
      ),
    );

    try {
      // Versuche zuerst, den lokalen Lebenslauf zu öffnen
      final userId = Supabase.instance.client.auth.currentUser?.id;

      debugPrint('Versuche Lebenslauf zu öffnen für Benutzer: $userId');
      debugPrint('Download URL: $cvDownloadUrl');
      debugPrint('Lokaler Pfad: $cvFilePath');

      // Stelle sicher, dass der Lebenslauf lokal verfügbar ist
      final localPath = await CvStorageHelper.ensureLocalCvAvailable(
        userId,
        cvFilePath,
        cvDownloadUrl,
      );

      debugPrint('Lokaler Pfad nach ensureLocalCvAvailable: $localPath');

      if (localPath != null) {
        final file = File(localPath);
        if (await file.exists()) {
          debugPrint(
            'Datei existiert: ${file.path}, Größe: ${await file.length()} Bytes',
          );
          //
          // Prüfe, ob die Datei verschlüsselt ist
          final isEncrypted = file.path.endsWith('.enc');
          if (isEncrypted) {
            debugPrint('Datei ist verschlüsselt, versuche zu entschlüsseln');

            // Versuche, die Datei zu entschlüsseln
            try {
              final secureFileService = SecureFileService();
              final decryptedFile = await secureFileService.decryptFile(
                file.path,
              );

              if (decryptedFile != null) {
                debugPrint(
                  'Datei erfolgreich entschlüsselt: ${decryptedFile.path}',
                );

                // Versuche, die entschlüsselte Datei zu öffnen
                final success = await _openFileWithProvider(decryptedFile.path);
                if (success) {
                  debugPrint('Entschlüsselte Datei erfolgreich geöffnet');
                  return;
                }
              } else {
                debugPrint('Entschlüsselung fehlgeschlagen');
              }
            } catch (e) {
              debugPrint('Fehler bei der Entschlüsselung: $e');
            }
          }
          //
          // Versuche, die Datei direkt zu öffnen
          debugPrint('Versuche, die Datei direkt zu öffnen: ${file.path}');
          final success = await _openFileWithProvider(file.path);
          if (success) {
            debugPrint('Datei erfolgreich geöffnet');
            return;
          }

          // Wenn die direkte Öffnung fehlschlägt, versuche es mit einer Kopie
          debugPrint(
            'Direkte Öffnung fehlgeschlagen, versuche es mit einer Kopie',
          );

          // Erstelle eine temporäre Kopie im Cache-Verzeichnis
          final tempDir = await getTemporaryDirectory();
          final tempFile = File('${tempDir.path}/temp_lebenslauf.pdf');

          try {
            // Kopiere die Datei in das temporäre Verzeichnis
            await file.copy(tempFile.path);
            debugPrint(
              'Datei in temporäres Verzeichnis kopiert: ${tempFile.path}',
            );
            //
            // Versuche, die temporäre Datei zu öffnen
            final tempSuccess = await _openFileWithProvider(tempFile.path);
            if (tempSuccess) {
              debugPrint('Temporäre Datei erfolgreich geöffnet');
              return;
            }

            // Wenn das nicht funktioniert, versuche es mit dem FileProvider
            debugPrint(
              'Temporäre Datei konnte nicht geöffnet werden, versuche es mit FileProvider',
            );

            final packageName = 'com.einsteinai.app';
            final fileProviderAuthority = '$packageName.fileprovider';

            // Erstelle eine Content-URI mit dem FileProvider für die temporäre Datei
            final contentUri = Uri.parse(
              'content://$fileProviderAuthority/temp/temp_lebenslauf.pdf',
            );

            debugPrint('Content URI: $contentUri');

            if (await canLaunchUrl(contentUri)) {
              debugPrint('Öffne Content URI');
              await launchUrl(contentUri, mode: LaunchMode.externalApplication);
              return;
            }
            //
            // Wenn das nicht funktioniert, versuche es mit einem Intent
            debugPrint(
              'Content URI konnte nicht geöffnet werden, versuche es mit Intent',
            );

            final intent = 'android.intent.action.VIEW';
            final uriString =
                'content://$fileProviderAuthority/temp/temp_lebenslauf.pdf';
            final mimeType = 'application/pdf';

            final url = Uri.parse(
              'intent:#Intent;action=$intent;type=$mimeType;S.uri=$uriString;end',
            );

            debugPrint('Intent URL: $url');

            if (await canLaunchUrl(url)) {
              debugPrint('Öffne Intent URL');
              await launchUrl(url);
              return;
            }
          } catch (e) {
            debugPrint(
              'Fehler beim Kopieren oder Öffnen der temporären Datei: $e',
            );
          }
        } else {
          debugPrint('Datei existiert nicht: $localPath');
        }
      } else {
        debugPrint('Lokaler Pfad ist null');
      }
      //
      // Fallback: Versuche die Download-URL zu öffnen
      if (cvDownloadUrl != null) {
        debugPrint('Versuche, die Download-URL zu öffnen: $cvDownloadUrl');
        final uri = Uri.tryParse(cvDownloadUrl!);
        if (uri != null && await canLaunchUrl(uri)) {
          debugPrint('Öffne Download-URL');
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          debugPrint('Konnte Download-URL nicht öffnen');
          throw Exception('Konnte URL nicht öffnen: $cvDownloadUrl');
        }
      } else {
        debugPrint('Keine Download-URL verfügbar');
        throw Exception('Keine Download-URL verfügbar.');
      }
    } catch (e) {
      debugPrint('Fehler beim Öffnen des Lebenslaufs: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Konnte Lebenslauf nicht öffnen: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.fixed,
          ),
        );
      }
    }
  }

  //
  @override
  Widget build(BuildContext context) {
    if (cvDownloadUrl == null) {
      return const Text(
        'Kein Lebenslauf hochgeladen',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    // Verwende einen generischen Namen für den Lebenslauf, um Sicherheitsrisiken zu vermeiden
    // Keine Extraktion des tatsächlichen Dateinamens aus der URL mehr, um Sicherheitsrisiken zu vermeiden

    return Row(
      children: [
        const Icon(
          Icons.description_outlined,
          color: AppTheme.primaryLightColor,
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: Text(
            'Lebenslauf', // Generischer Name statt des tatsächlichen Dateinamens
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: Colors.white),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSmall,
            vertical: AppTheme.spacingXSmall / 2,
          ),
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            border: Border.all(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
          child: TextButton.icon(
            icon: const Icon(
              Icons.open_in_new,
              size: 16,
            ), // Icon geändert zu "öffnen"
            label: const Text('Anzeigen'), // Text geändert
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              minimumSize: Size.zero,
              visualDensity: VisualDensity.compact,
            ),
            onPressed: () => _launchCvUrl(context), // Ruft die neue Methode auf
          ),
        ),
      ],
    );
  }
}

// Editierbares CV (Angepasst)
class _EditableCvSection extends StatelessWidget {
  final String? initialDownloadUrl; // Geändert von initialFilePath
  final String?
  locallySelectedFilePath; // Neuer Parameter für die lokale Auswahl
  final Function(String?) onFilePicked;
  final VoidCallback
  onClearSelection; // Neuer Callback zum Löschen der lokalen Auswahl

  const _EditableCvSection({
    this.initialDownloadUrl, // Geändert
    this.locallySelectedFilePath,
    required this.onFilePicked,
    required this.onClearSelection,
  });

  // Getter für cvFilePath - gibt den lokalen Pfad zurück
  String? get cvFilePath => locallySelectedFilePath;

  @override
  Widget build(BuildContext context) {
    // Bestimme den anzuzeigenden Dateinamen (generisch, um Sicherheitsrisiken zu vermeiden)
    String displayFileName = 'Kein Lebenslauf ausgewählt oder hochgeladen';
    bool hasFile = false;
    if (locallySelectedFilePath != null) {
      displayFileName = 'Lebenslauf ausgewählt';
      hasFile = true;
    } else if (initialDownloadUrl != null) {
      displayFileName = 'Lebenslauf hochgeladen';
      hasFile = true;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Zeige den aktuellen Status (ausgewählt oder hochgeladen)
        Padding(
          padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
          child: Row(
            children: [
              Icon(
                hasFile
                    ? Icons.check_circle_outline
                    : Icons.radio_button_unchecked,
                size: 18,
                color: hasFile ? Colors.greenAccent : Colors.grey,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  displayFileName,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: hasFile ? Colors.white70 : Colors.grey,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // Button zum Löschen der *lokalen* Auswahl, falls vorhanden
              if (locallySelectedFilePath != null)
                IconButton(
                  icon: Icon(
                    Icons.close,
                    size: 18,
                    color: Theme.of(
                      context,
                    ).colorScheme.error.withValues(alpha: 0.7),
                  ),
                  tooltip: AppLocalizationsWrapper.of(context).removeSelection,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onClearSelection,
                ),
            ],
          ),
        ),
        // Button zum Auswählen einer neuen Datei
        ElevatedButton.icon(
          icon: const Icon(Icons.upload_file),
          label: Text(
            hasFile
                ? AppLocalizationsWrapper.of(context).selectNewPdf
                : AppLocalizationsWrapper.of(context).selectPdf,
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryLightColor.withValues(alpha: 0.3),
            foregroundColor: Colors.white,
            side: BorderSide(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.5),
            ),
          ),
          onPressed: () async {
            try {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['pdf'],
              );
              if (result != null && result.files.isNotEmpty) {
                final file = result.files.first;
                if (file.path != null) onFilePicked(file.path);
              }
            } catch (e) {
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      '${AppLocalizationsWrapper.of(context).fileSelectionError} ${e.toString()}',
                    ),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.fixed,
                  ),
                );
              }
            }
          },
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Text(
          'Hinweis: Das Auswählen einer neuen PDF ersetzt beim Speichern die vorherige.',
          style: Theme.of(
            context,
          ).textTheme.labelSmall?.copyWith(fontStyle: FontStyle.normal),
        ),
      ],
    );
  }
}

// Editierbare Job Präferenzen
class _EditableJobPreferences extends StatelessWidget {
  final ProfileControllers controllers;
  const _EditableJobPreferences({required this.controllers});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildModernTextField(
          context,
          controller: controllers.targetPositionController,
          label: 'Angestrebte Position',
          icon: Icons.work_outline,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.industryController,
          label: 'Branche',
          icon: Icons.category_outlined,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.locationController,
          label: 'Gewünschter Standort',
          icon: Icons.location_on_outlined,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.salaryController,
          label: 'Gehaltsvorstellung (€)',
          icon: Icons.euro_outlined,
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.employmentTypeController,
          label: 'Beschäftigungsart (z.B. Vollzeit, Teilzeit)',
          icon: Icons.schedule_outlined,
        ),
      ],
    );
  }
}

// Display Job Präferenzen
class _DisplayJobPreferences extends StatelessWidget {
  final dynamic preferences;
  const _DisplayJobPreferences({required this.preferences});

  @override
  Widget build(BuildContext context) {
    final hasPreferences =
        (preferences.targetPosition?.isNotEmpty ?? false) ||
        (preferences.industry?.isNotEmpty ?? false) ||
        (preferences.locationPreference?.isNotEmpty ?? false) ||
        preferences.desiredSalary != null ||
        (preferences.employmentType?.isNotEmpty ?? false);
    if (!hasPreferences) {
      return const Text(
        'Keine Jobpräferenzen angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }
    final salary =
        preferences.desiredSalary != null
            ? '${preferences.desiredSalary!.toStringAsFixed(0)} €'
            : 'Nicht angegeben';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (preferences.targetPosition?.isNotEmpty ?? false)
          buildModernInfoRow(
            'Angestrebte Position',
            preferences.targetPosition!,
          ),
        if (preferences.industry?.isNotEmpty ?? false)
          buildModernInfoRow('Branche', preferences.industry!),
        if (preferences.locationPreference?.isNotEmpty ?? false)
          buildModernInfoRow(
            'Gewünschter Standort',
            preferences.locationPreference!,
          ),
        buildModernInfoRow('Gehaltsvorstellung', salary),
        if (preferences.employmentType?.isNotEmpty ?? false)
          buildModernInfoRow('Beschäftigungsart', preferences.employmentType!),
      ],
    );
  }
}

// Editierbare KI Personalisierung
class _EditableKiPersonalizationNew extends ConsumerStatefulWidget {
  final ProfileControllers controllers;
  final bool initialIncludeExperience;
  final ValueChanged<bool> onIncludeExperienceChanged;
  final TextEditingController preferredWritingStyleController;
  final Function(String?) onStyleChanged;
  const _EditableKiPersonalizationNew({
    required this.controllers,
    required this.initialIncludeExperience,
    required this.onIncludeExperienceChanged,
    required this.preferredWritingStyleController,
    required this.onStyleChanged,
  });

  @override
  ConsumerState<_EditableKiPersonalizationNew> createState() =>
      _EditableKiPersonalizationNewState();
}

class _EditableKiPersonalizationNewState
    extends ConsumerState<_EditableKiPersonalizationNew> {
  String? _generatedPrompt;
  bool _isGeneratingPrompt = false;
  // Keine Beschreibungen mehr nötig, da sie direkt im Dropdown angezeigt werden

  @override
  void initState() {
    super.initState();
    // Überprüfe beim Laden der Seite, ob der Benutzer noch ein aktives Unlimited-Abonnement hat
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkSubscriptionForPersonalizedStyle();
    });
  }

  // Methode zum Überprüfen des Abonnements für den personalisierten Schreibstil
  Future<void> _checkSubscriptionForPersonalizedStyle() async {
    // Nur prüfen, wenn "Passend zu meinem Stil" ausgewählt ist
    if (widget.preferredWritingStyleController.text !=
        'Passend zu meinem Stil') {
      return;
    }

    // Prüfe, ob der Benutzer ein aktives Unlimited-Abonnement hat
    final subscriptionService = ref.read(subscriptionManagementServiceProvider);
    final userSubscription = await subscriptionService.getCurrentSubscription();

    // Wenn der Benutzer kein aktives Unlimited-Abonnement hat, setze den Stil auf "Professionell" zurück
    if (userSubscription?.planType?.toLowerCase() != 'unlimited') {
      if (mounted) {
        // Zeige einen Dialog an, dass diese Funktion nur für Unlimited-Abonnenten verfügbar ist
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Der personalisierte Schreibstil ist nur für Unlimited-Abonnenten verfügbar. Dein Stil wurde auf "Professionell" zurückgesetzt.',
            ),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 5),
          ),
        );

        // Setze den Stil auf "Professionell" zurück
        widget.onStyleChanged('Professionell');
        setState(() {
          _generatedPrompt = null;
        });
      }
    }
  }

  // Methode zum Generieren des personalisierten Schreibstil-Prompts
  Future<void> _generatePersonalizedPrompt() async {
    if (widget.preferredWritingStyleController.text !=
        'Passend zu meinem Stil') {
      setState(() {
        _generatedPrompt = null;
      });
      return;
    }

    // Prüfe zuerst, ob der Benutzer noch ein aktives Unlimited-Abonnement hat
    final subscriptionService = ref.read(subscriptionManagementServiceProvider);
    final userSubscription = await subscriptionService.getCurrentSubscription();

    // Wenn der Benutzer kein aktives Unlimited-Abonnement hat, setze den Stil auf "Professionell" zurück
    if (userSubscription?.planType?.toLowerCase() != 'unlimited') {
      if (mounted) {
        // Zeige einen Dialog an, dass diese Funktion nur für Unlimited-Abonnenten verfügbar ist
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Der personalisierte Schreibstil ist nur für Unlimited-Abonnenten verfügbar. Dein Stil wurde auf "Professionell" zurückgesetzt.',
            ),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 5),
          ),
        );

        // Setze den Stil auf "Professionell" zurück
        widget.onStyleChanged('Professionell');
        setState(() {
          _generatedPrompt = null;
        });
      }
      return;
    }

    // Prüfe, ob bereits ein personalisierter Schreibstil im Profil gespeichert ist
    final userProfileState = ref.read(userProfileProvider);
    if (userProfileState.hasValue) {
      final userProfile = userProfileState.value!;

      // Wenn der Benutzer bereits einen personalisierten Schreibstil hat und der Lebenslauf nicht geändert wurde,
      // verwende den gespeicherten Schreibstil
      if (userProfile.preferredWritingStyle != null &&
          userProfile.preferredWritingStyle!.isNotEmpty &&
          userProfile.preferredWritingStyle != 'Passend zu meinem Stil' &&
          userProfile.preferredWritingStyle != 'Professionell' &&
          userProfile.preferredWritingStyle != 'Kreativ' &&
          userProfile.preferredWritingStyle != 'Formell' &&
          userProfile.preferredWritingStyle != 'Standort') {
        if (mounted) {
          setState(() {
            _generatedPrompt = userProfile.preferredWritingStyle;
          });
        }
        return;
      }
    }

    setState(() {
      _isGeneratingPrompt = true;
    });

    try {
      // Rufe die Supabase-Funktion auf, um den personalisierten Schreibstil zu generieren
      final supabaseService = ref.read(supabaseServiceProvider);
      final stylePrompt = await supabaseService.analyzeProfileStyle();

      if (mounted) {
        setState(() {
          _isGeneratingPrompt = false;
        });

        // Speichere den generierten Schreibstil im Profil
        if (stylePrompt.isNotEmpty) {
          final userProfileNotifier = ref.read(userProfileProvider.notifier);
          final currentProfile = ref.read(userProfileProvider).valueOrNull;
          if (currentProfile != null) {
            final updatedProfile = currentProfile.copyWith(
              preferredWritingStyle: stylePrompt,
            );
            await userProfileNotifier.saveProfileChanges(updatedProfile);

            // Zeige eine Erfolgsmeldung an
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Personalisierter Schreibstil wurde erfolgreich generiert und gespeichert.',
                  ),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 3),
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint(
        'Fehler bei der Generierung des personalisierten Schreibstils: $e',
      );
      if (mounted) {
        setState(() {
          _isGeneratingPrompt = false;
        });

        // Zeige eine Fehlermeldung an
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Generieren des Schreibstils: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // Methode zum Anzeigen des benutzerdefinierten Dropdown-Menüs
  void _showCustomDropdown(BuildContext context) {
    // Optionen mit Beschreibungen
    final Map<String, Map<String, dynamic>> optionsWithDescriptions = {
      'Standort': {
        'description':
            'Einfacher, direkter Stil mit Fokus auf den Standort. Ideal für kostenlose Nutzer und einfache Bewerbungen.',
        'icon': Icons.location_on,
      },
      'Professionell': {
        'description':
            'Sachlicher, formeller Stil mit klarer Sprache, angemessener Distanz und präzisen Formulierungen. Ideal für konservative Branchen und formelle Bewerbungen.',
        'icon': Icons.business,
      },
      'Kreativ': {
        'description':
            'Origineller, dynamischer Stil mit bildlicher Sprache und persönlicher Note. Gut geeignet für kreative Berufe, Marketing und Medien.',
        'icon': Icons.brush,
      },
      'Technisch': {
        'description':
            'Präziser, faktenbasierter Stil mit Fachbegriffen und logischem Aufbau. Optimal für IT, Ingenieurwesen und wissenschaftliche Positionen.',
        'icon': Icons.code,
      },
      'Passend zu meinem Stil': {
        'description':
            'Analysiert dein Profil und erstellt einen personalisierten Schreibstil, der zu deinem Bildungsniveau, deiner Berufserfahrung und deinem Hintergrund passt. Nur für Unlimited-Abonnenten verfügbar.',
        'icon': Icons.person,
      },
    };

    // Dialog anzeigen
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.symmetric(horizontal: 20),
          child: Container(
            width: 300,
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Schreibstil wählen',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Divider(color: Colors.grey, height: 1),
                Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.6,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children:
                          optionsWithDescriptions.entries.map((entry) {
                            final option = entry.key;
                            final description =
                                entry.value['description'] as String;
                            final icon = entry.value['icon'] as IconData;

                            return Column(
                              children: [
                                InkWell(
                                  onTap: () {
                                    Navigator.of(context).pop(option);
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0,
                                      vertical: 12.0,
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: AppTheme
                                                    .primaryLightColor
                                                    .withValues(alpha: 26),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                icon,
                                                size: 16,
                                                color:
                                                    AppTheme.primaryLightColor,
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                              option,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          description,
                                          style: TextStyle(
                                            color: Colors.grey[300],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (option != optionsWithDescriptions.keys.last)
                                  const Divider(
                                    color: Colors.grey,
                                    height: 1,
                                    indent: 16,
                                    endIndent: 16,
                                  ),
                              ],
                            );
                          }).toList(),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    ).then((selectedValue) async {
      if (selectedValue != null) {
        // Prüfe, ob der Benutzer ein Unlimited-Abonnement hat, wenn "Passend zu meinem Stil" ausgewählt wurde
        if (selectedValue == 'Passend zu meinem Stil') {
          final subscriptionService = ref.read(
            subscriptionManagementServiceProvider,
          );
          final userProfile =
              await subscriptionService.getCurrentSubscription();

          if (userProfile?.planType?.toLowerCase() != 'unlimited') {
            // Zeige einen Dialog an, dass diese Funktion nur für Unlimited-Abonnenten verfügbar ist
            if (context.mounted) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text('Premium-Funktion'),
                    content: const Text(
                      'Der personalisierte Schreibstil ist nur für Unlimited-Abonnenten verfügbar. Upgrade jetzt, um diese Funktion zu nutzen!',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Navigiere zur Premium-Verwaltungsseite
                          Navigator.of(
                            context,
                          ).pushNamed('/premium-management');
                        },
                        child: const Text('Upgraden'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('Abbrechen'),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } else {
            // Benutzer hat ein Unlimited-Abonnement, generiere den personalisierten Prompt
            widget.onStyleChanged(selectedValue);
            _generatePersonalizedPrompt();
          }
        } else if (selectedValue == 'Standort') {
          // Erlaube Standort-Stil für alle Benutzer
          widget.onStyleChanged(selectedValue);
          setState(() {
            _generatedPrompt = null;
          });
        } else {
          // Prüfe, ob der Benutzer mindestens ein Pro-Abonnement hat für andere Stile
          final subscriptionService = ref.read(
            subscriptionManagementServiceProvider,
          );
          final userProfile =
              await subscriptionService.getCurrentSubscription();

          if (userProfile?.planType?.toLowerCase() == 'free') {
            // Zeige einen Dialog an, dass diese Funktion nur für Pro- und Unlimited-Abonnenten verfügbar ist
            if (context.mounted) {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text('Premium-Funktion'),
                    content: const Text(
                      'Dieser Schreibstil ist nur für Pro- und Unlimited-Abonnenten verfügbar. Upgrade jetzt, um diese Funktion zu nutzen!',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Navigiere zur Premium-Verwaltungsseite
                          Navigator.of(
                            context,
                          ).pushNamed('/premium-management');
                        },
                        child: const Text('Upgraden'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('Abbrechen'),
                      ),
                    ],
                  );
                },
              );
              return;
            }
          } else {
            // Benutzer hat mindestens ein Pro-Abonnement, erlaube den Stil
            widget.onStyleChanged(selectedValue);
            setState(() {
              _generatedPrompt = null;
            });
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Prüfe, ob der Benutzer ein Pro- oder Unlimited-Abonnement hat
    final isPremium = ref.watch(isPremiumProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Globale KI-Hinweise (nur für Pro- oder Unlimited-Benutzer)
        if (isPremium)
          buildModernTextField(
            context,
            controller: widget.controllers.globalAiHintsController,
            label: "Globale KI-Hinweise für alle Bewerbungen",
            icon: Icons.psychology_outlined,
            maxLines: 4,
            hintText:
                "Diese Hinweise werden bei jeder Bewerbung berücksichtigt",
          )
        else
          Builder(
            builder: (context) {
              final containerColor = AppTheme.neutralColors[800]!;
              print(
                'DEBUG: Premium Container 1 - Color: $containerColor, Alpha: ${containerColor.a}',
              );
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: containerColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                    width: 0.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.psychology_outlined,
                          size: 16,
                          color: AppTheme.primaryLightColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            "Globale KI-Hinweise für alle Bewerbungen",
                            style: TextStyle(
                              color: AppTheme.primaryLightColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.lock_outline,
                          size: 16,
                          color: AppTheme.primaryLightColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Diese Funktion ist nur für Pro- und Unlimited-Abonnenten verfügbar. Upgrade jetzt, um globale Hinweise für alle deine Bewerbungen zu nutzen!",
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushNamed('/premium-management');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryLightColor.withValues(
                          alpha: 77,
                        ),
                        foregroundColor: Colors.white,
                      ),
                      child: Text("Upgrade"),
                    ),
                  ],
                ),
              );
            },
          ),

        // Bevorzugter Schreibstil Button (nur für Premium-Benutzer)
        if (isPremium)
          GestureDetector(
            onTap: () {
              // Dropdown-Menü manuell öffnen
              _showCustomDropdown(context);
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.neutralColors[800]!, // Angepasster Hintergrund
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Color(
                        0xFF2D1F37,
                      ), // Lila-Grau-Ton wie bei anderen Icons
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.style_outlined,
                      size: 14,
                      color: const Color(0xFFCCCCCC),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "Bevorzugter Schreibstil",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Text(
                    widget.preferredWritingStyleController.text.isEmpty
                        ? 'Professionell'
                        : widget.preferredWritingStyleController.text,
                    style: TextStyle(
                      color: AppTheme.primaryLightColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_drop_down,
                    size: 18,
                    color: AppTheme.primaryLightColor,
                  ),
                ],
              ),
            ),
          ),
        // Leerer Container für Free-User entfernt, um weiße Fläche zu vermeiden

        // Zweiter Premium-Container
        if (!isPremium)
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.neutralColors[800]!,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                width: 0.5,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Color(0xFF2D1F37),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.style_outlined,
                        size: 14,
                        color: const Color(0xFFCCCCCC),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        "Bevorzugter Schreibstil",
                        style: TextStyle(
                          color: AppTheme.primaryLightColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.lock_outline,
                      size: 16,
                      color: AppTheme.primaryLightColor,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  "Diese Funktion ist nur für Pro- und Unlimited-Abonnenten verfügbar. Upgrade jetzt, um deinen bevorzugten Schreibstil anzupassen!",
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushNamed('/premium-management');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryLightColor.withValues(
                      alpha: 77,
                    ),
                    foregroundColor: Colors.white,
                  ),
                  child: Text("Upgrade"),
                ),
              ],
            ),
          ),

        // Zeige nur einen Ladeindikator während der Generierung des personalisierten Schreibstils für Premium-Nutzer
        if (isPremium &&
            widget.preferredWritingStyleController.text ==
                'Passend zu meinem Stil' &&
            _isGeneratingPrompt)
          Builder(
            builder: (context) {
              final containerColor = AppTheme.neutralColors[800]!;
              print(
                'DEBUG: Premium Container 3 - Color: $containerColor, Alpha: ${containerColor.a}',
              );
              return Container(
                margin: const EdgeInsets.only(bottom: 8, top: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: containerColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryLightColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      "Generiere personalisierten Schreibstil...",
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),

        // Dritter Premium-Container wurde entfernt, da er fälschlicherweise für kostenlose Nutzer angezeigt wurde

        // Checkbox als ListTile in einem ähnlichen Container
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Color(0xFF1C1C1E),
            borderRadius: BorderRadius.circular(12),
          ),
          child: CheckboxListTile(
            title: Text(
              AppLocalizationsWrapper.of(context).includeWorkExperience,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Color(0xFFDDDDDD),
                fontSize: 14, // Etwas kleiner für Checkbox-Text
              ),
            ),
            value: widget.initialIncludeExperience,
            onChanged:
                (value) => widget.onIncludeExperienceChanged(value ?? true),
            checkColor: AppTheme.backgroundDarkColor,
            fillColor: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.selected)) {
                return AppTheme.primaryLightColor;
              }
              return Colors.grey[700]; // Dunklerer Hintergrund für unchecked
            }),
            side: BorderSide(color: Colors.grey[700]!), // Dunklere Border
            controlAffinity: ListTileControlAffinity.leading, // Checkbox links
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 0,
            ), // Padding anpassen
            dense: true,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Text(
          AppLocalizationsWrapper.of(context).aiPersonalizationHint,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            fontStyle: FontStyle.normal,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

// Display KI Personalisierung
class _DisplayKiPersonalization extends ConsumerStatefulWidget {
  final bool cvAutoFillEnabled;
  final bool skillsInApplication;
  final bool workExperienceInApplication;
  final bool educationInApplication;
  final bool isPersonalized;

  const _DisplayKiPersonalization({
    required this.cvAutoFillEnabled,
    required this.skillsInApplication,
    required this.workExperienceInApplication,
    required this.educationInApplication,
    required this.isPersonalized,
  });

  @override
  ConsumerState<_DisplayKiPersonalization> createState() =>
      _DisplayKiPersonalizationState();
}

class _DisplayKiPersonalizationState
    extends ConsumerState<_DisplayKiPersonalization> {
  // State für die Optionen
  late ValueNotifier<bool> _cvAutoFillEnabled;
  late ValueNotifier<bool> _skillsInApplication;
  late ValueNotifier<bool> _workExperienceInApplication;
  late ValueNotifier<bool> _educationInApplication;
  late ValueNotifier<bool> _isPersonalized;

  @override
  void initState() {
    super.initState();
    // Initialisiere die ValueNotifier mit den übergebenen Werten
    _cvAutoFillEnabled = ValueNotifier<bool>(widget.cvAutoFillEnabled);
    _skillsInApplication = ValueNotifier<bool>(widget.skillsInApplication);
    _workExperienceInApplication = ValueNotifier<bool>(
      widget.workExperienceInApplication,
    );
    _educationInApplication = ValueNotifier<bool>(
      widget.educationInApplication,
    );
    _isPersonalized = ValueNotifier<bool>(widget.isPersonalized);
  }

  @override
  void dispose() {
    // Dispose die ValueNotifier
    _cvAutoFillEnabled.dispose();
    _skillsInApplication.dispose();
    _workExperienceInApplication.dispose();
    _educationInApplication.dispose();
    _isPersonalized.dispose();
    super.dispose();
  }

  // Funktion zum Umschalten einer Option
  void _toggleOption(ValueNotifier<bool> option) {
    option.value = !option.value;

    // Hier würde normalerweise die Logik zum Speichern der Änderung im UserProfile stehen
    // Da dies nur ein Beispiel ist, zeigen wir nur eine Snackbar an
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(option.value ? 'Option aktiviert' : 'Option deaktiviert'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // Hilfsfunktion zum Erstellen eines Optionselements
  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required ValueNotifier<bool> option,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: option,
      builder: (context, value, child) {
        return GestureDetector(
          onTap: () => _toggleOption(option),
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              border: Border.all(
                color: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                    color: Color(0xFF2D1F37), // Lila-Grau-Ton
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, size: 16, color: const Color(0xFFCCCCCC)),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(fontSize: 15, color: Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  value
                      ? 'Aktiviert' // Deutsch statt Englisch
                      : 'Deaktiviert', // Deutsch statt Englisch
                  style: TextStyle(
                    color: value ? Colors.greenAccent : Colors.redAccent,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // CV Autofill
        _buildOptionItem(
          icon: Icons.auto_awesome,
          title: 'Automatisch aus Lebenslauf ausfüllen',
          option: _cvAutoFillEnabled,
        ),

        // Fähigkeiten in Anschreiben
        _buildOptionItem(
          icon: Icons.psychology_outlined,
          title: 'Fähigkeiten in Bewerbungen',
          option: _skillsInApplication,
        ),

        // Berufserfahrung in Anschreiben
        _buildOptionItem(
          icon: Icons.work_outline,
          title: 'Berufserfahrung in Bewerbungen',
          option: _workExperienceInApplication,
        ),

        // Ausbildung in Anschreiben
        _buildOptionItem(
          icon: Icons.school_outlined,
          title: 'Bildung in Anschreiben',
          option: _educationInApplication,
        ),

        // Persönlicher Stil
        _buildOptionItem(
          icon: Icons.person_outline,
          title: 'Bevorzugter Schreibstil',
          option: _isPersonalized,
        ),
      ],
    );
  }
}

// Display Work Experience
class _DisplayWorkExperienceList extends StatelessWidget {
  final List<WorkExperience> experiences;
  const _DisplayWorkExperienceList({required this.experiences});

  @override
  Widget build(BuildContext context) {
    if (experiences.isEmpty) {
      return const Text(
        'Keine Berufserfahrung angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          experiences
              .map((exp) => _DisplayWorkExperienceItem(experience: exp))
              .toList(),
    );
  }
}

// Display Education
class _DisplayEducationList extends StatelessWidget {
  final List<Education> educations;
  const _DisplayEducationList({required this.educations});

  @override
  Widget build(BuildContext context) {
    if (educations.isEmpty) {
      return const Text(
        'Keine Ausbildung angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          educations
              .map((edu) => _DisplayEducationItem(education: edu))
              .toList(),
    );
  }
}

// --- NEU: ExpandableEllipsisTextField mit Textverlauf und 'mehr' Hinweis ---
class ExpandableEllipsisTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final IconData? icon;
  final int collapsedMaxLines;
  final int expandedMaxLines;
  final TextStyle? style;
  final double gradientWidth;
  final Color backgroundColor;

  const ExpandableEllipsisTextField({
    super.key,
    required this.controller,
    required this.label,
    this.icon,
    this.collapsedMaxLines = 1,
    this.expandedMaxLines = 5,
    this.style,
    this.gradientWidth = 40.0, // Breite des Verlaufs
    this.backgroundColor = const Color(0xFF1C1C1E), // Hintergrund für Verlauf
  });

  @override
  State<ExpandableEllipsisTextField> createState() =>
      _ExpandableEllipsisTextFieldState();
}

class _ExpandableEllipsisTextFieldState
    extends State<ExpandableEllipsisTextField> {
  bool _expanded = false;
  bool _isOverflowing = false;
  final FocusNode _focusNode = FocusNode();
  final GlobalKey _textKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_handleFocusChange);
    widget.controller.addListener(_checkOverflow);
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkOverflow());
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    _focusNode.dispose();
    widget.controller.removeListener(_checkOverflow);
    super.dispose();
  }

  void _handleFocusChange() {
    if (!_focusNode.hasFocus && _expanded) {
      setState(() {
        _expanded = false;
      });
      _checkOverflow(); // Überlauf neu prüfen nach Einklappen
    }
  }

  void _checkOverflow() {
    if (!mounted || _textKey.currentContext == null) return;

    // Wichtig: Hier den BuildContext des Widgets verwenden!
    final currentContext = context; // Holt den BuildContext von State<T>
    if (!mounted) return; // Erneute Prüfung, falls sich der State ändert

    final RenderBox? renderBox =
        _textKey.currentContext!.findRenderObject() as RenderBox?;
    if (renderBox == null || !renderBox.hasSize) return;

    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: widget.controller.text,
        style: _getTextStyle(currentContext),
      ), // Korrekten context übergeben
      maxLines: widget.collapsedMaxLines,
      textDirection: ui.TextDirection.ltr,
    )..layout(maxWidth: renderBox.size.width - widget.gradientWidth - 5);

    final didOverflow = textPainter.didExceedMaxLines;
    if (didOverflow != _isOverflowing) {
      // Prüfen ob Widget noch gemounted ist vor setState
      if (mounted) {
        setState(() {
          _isOverflowing = didOverflow;
        });
      }
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    // Typ ist korrekt
    return widget.style ??
        Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Color(0xFFDDDDDD),
          fontSize: 15,
          fontWeight: FontWeight.w400,
        ) ??
        const TextStyle();
  }

  @override
  Widget build(BuildContext context) {
    final showHint = _isOverflowing && !_expanded;
    final currentTextStyle = _getTextStyle(context); // context hier verwenden

    return GestureDetector(
      onTap: () {
        if (!_expanded) {
          setState(() {
            _expanded = true;
          });
          _focusNode.requestFocus();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        decoration: BoxDecoration(
          color: widget.backgroundColor, // Muss gesetzt sein für Textverlauf
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          alignment: Alignment.centerRight,
          children: [
            // Textfeld mit optionalem ShaderMask
            ShaderMask(
              shaderCallback: (Rect bounds) {
                if (_expanded || !_isOverflowing) {
                  // Kein Verlauf, wenn expandiert oder kein Overflow
                  // Wichtig: Muss trotzdem einen Shader zurückgeben
                  return ui.Gradient.linear(
                    bounds.topLeft,
                    bounds.bottomRight,
                    [Colors.white, Colors.white],
                  );
                }
                // Verlauf von voll sichtbar zu Hintergrundfarbe
                final gradient = LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.white,
                    Colors.white,
                    widget.backgroundColor.withValues(
                      alpha: 128,
                    ), // 0.5 * 255 = 128
                    widget.backgroundColor,
                  ],
                  stops: [
                    0.0,
                    1.0 - (widget.gradientWidth / bounds.width),
                    1.0 - (widget.gradientWidth / bounds.width) * 0.5,
                    1.0,
                  ],
                );
                // Shader aus Gradient erstellen
                return gradient.createShader(bounds);
              },
              blendMode: BlendMode.dstIn,
              child: TextField(
                key: _textKey,
                controller: widget.controller,
                focusNode: _focusNode,
                maxLines:
                    _expanded
                        ? widget.expandedMaxLines
                        : widget.collapsedMaxLines,
                style: currentTextStyle, // Angepassten Style verwenden
                decoration: InputDecoration(
                  labelText: widget.label,
                  labelStyle: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Color(0xFFCCCCCC),
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                  floatingLabelStyle: const TextStyle(color: Color(0xFF9B59B6)),
                  prefixIcon:
                      widget.icon != null
                          ? Padding(
                            padding: const EdgeInsets.only(
                              left: 12.0,
                              right: 8.0,
                            ), // Links 12px, Rechts 8px
                            child: Icon(
                              widget.icon,
                              color:
                                  Theme.of(
                                    context,
                                  ).inputDecorationTheme.prefixIconColor,
                              size: 20,
                            ),
                          )
                          : const SizedBox(width: 12 + 8 + 20),
                  prefixIconConstraints: const BoxConstraints(minHeight: 44),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  // Inhalt Padding links auf 0 setzen
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 12.0,
                    horizontal: 0,
                  ),
                  isDense: true,
                ),
                readOnly: false, // Muss editierbar bleiben
                textAlignVertical: TextAlignVertical.center,
                cursorColor: AppTheme.primaryLightColor,
                onTap: () {
                  if (!_expanded) {
                    setState(() {
                      _expanded = true;
                    });
                    _focusNode.requestFocus();
                  }
                },
                onChanged: (_) => _checkOverflow(),
              ),
            ),
            // "..." Hinweis, wenn nötig
            if (showHint)
              Positioned(
                right: 8,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Text(
                    '...',
                    style: currentTextStyle.copyWith(
                      color: Color(0xFF999999),
                    ), // Angepassten Style verwenden
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// TESTBUTTON: Einfacher Test-Button für Debugging
Widget testButton(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 16.0),
    child: ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        minimumSize: const Size.fromHeight(44),
      ),
      onPressed: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test-Button gedrückt!'),
            backgroundColor: Colors.purple,
          ),
        );
        debugPrint('### TESTBUTTON gedrückt! ###');
      },
      child: const Text('Testen'),
    ),
  );
}

// Füge den Button in den Haupt-Screen ein (z.B. im build der ProfileScreen)
// Suche nach einer Column oder ListView und füge testButton(context) an passender Stelle ein

// Display der Berufserfahrungen einzeln
class _DisplayWorkExperienceItem extends StatelessWidget {
  final WorkExperience experience;
  const _DisplayWorkExperienceItem({required this.experience});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stern mit rundem Hintergrund
          SizedBox(
            width: 30, // Feste Breite für das Icon, wie bei den Fähigkeiten
            child: Container(
              padding: const EdgeInsets.all(6),
              margin: const EdgeInsets.only(right: 8, top: 2),
              decoration: const BoxDecoration(
                color: Color(0xFF2D1F37), // Lila-Grau-Ton wie bei "Englisch"
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.star_outline,
                size: 16,
                color: Color(0xFFCCCCCC),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Position
                Text(
                  experience.position,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                // Arbeitsort
                const SizedBox(height: 2),
                Text(
                  experience.company,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                ),

                // Beschreibung
                if (experience.description.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      experience.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[300],
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.visible, // Verhindert Abschneiden
                    ),
                  ),

                // Datum
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDateRange(
                        experience.startDate,
                        experience.endDate,
                        context,
                      ),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Display der Ausbildung einzeln
class _DisplayEducationItem extends StatelessWidget {
  final Education education;
  const _DisplayEducationItem({required this.education});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stern mit rundem Hintergrund
          SizedBox(
            width: 30, // Feste Breite für das Icon, wie bei den Fähigkeiten
            child: Container(
              padding: const EdgeInsets.all(6),
              margin: const EdgeInsets.only(right: 8, top: 2),
              decoration: const BoxDecoration(
                color: Color(0xFF2D1F37), // Lila-Grau-Ton wie bei "Englisch"
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.star_outline,
                size: 16,
                color: Color(0xFFCCCCCC),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Abschluss
                Text(
                  education.degree,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                // Institution
                const SizedBox(height: 2),
                Text(
                  education.institution,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                ),

                // Fachrichtung
                if (education.fieldOfStudy.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      education.fieldOfStudy,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[300],
                        fontSize: 14,
                      ),
                    ),
                  ),

                // Datum
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDateRange(
                        education.startDate,
                        education.endDate,
                        context,
                      ),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Füge _formatDateRange Funktion vor die _DisplayWorkExperienceItem Klasse hinzu
// Diese Funktion kann von allen Klassen verwendet werden
String _formatDateRange(DateTime start, DateTime? end, BuildContext context) {
  final DateFormat formatter = DateFormat('dd.MM.yyyy');
  return '${formatter.format(start)} - ${end != null ? formatter.format(end) : 'heute'}';
}

// HINZUGEFÜGT: Helper zum Formatieren der Währung (außerhalb der Klasse)
String _formatCurrency(double? value) {
  if (value == null) return 'Nicht angegeben';
  final format = NumberFormat.currency(
    locale: 'de_DE',
    symbol: '€',
    decimalDigits: 0,
  );
  return format.format(value);
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ui_helpers.dart';
import 'package:ki_test/src/presentation/profile/models/profile_models.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/application/providers/additional_documents_provider.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/domain/models/additional_document.dart' as doc;

/// Display-Widgets für das Profile-Management
/// Diese Datei enthält alle Display-Widgets für die Anzeige von Profil-Informationen

// --- Display CV Section ---

/// Display CV Section Widget
class DisplayCvSection extends StatelessWidget {
  final String? cvDownloadUrl;
  final String? cvFilePath; // cvFilePath nur noch als Fallback für den Namen

  const DisplayCvSection({super.key, this.cvDownloadUrl, this.cvFilePath});

  // Methode zum Öffnen einer Datei mit dem nativen FileProvider
  void _openFile(String filePath) {
    // Vereinfachte Implementierung - in der echten App würde hier die Datei geöffnet
    debugPrint('Opening file: $filePath');
  }

  @override
  Widget build(BuildContext context) {
    if (cvDownloadUrl == null && cvFilePath == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.description_outlined, color: Colors.grey[400]),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Kein Lebenslauf hochgeladen',
                style: TextStyle(color: Colors.grey[400]),
              ),
            ),
          ],
        ),
      );
    }

    // Bestimme den Dateinamen - immer "Lebenslauf" anzeigen
    String fileName = 'Lebenslauf';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryLightColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.picture_as_pdf,
              color: AppTheme.primaryLightColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'PDF-Dokument',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.open_in_new,
              color: AppTheme.primaryLightColor,
              size: 20,
            ),
            onPressed: () {
              if (cvDownloadUrl != null) {
                _openFile(cvDownloadUrl!);
              } else if (cvFilePath != null) {
                _openFile(cvFilePath!);
              }
            },
            tooltip: 'CV öffnen',
          ),
        ],
      ),
    );
  }
}

// --- Display Work Experience ---

/// Display Work Experience List Widget
class DisplayWorkExperienceList extends StatelessWidget {
  final List<WorkExperience> experiences;

  const DisplayWorkExperienceList({super.key, required this.experiences});

  @override
  Widget build(BuildContext context) {
    if (experiences.isEmpty) {
      return Text(
        AppLocalizationsWrapper.of(context).noWorkExperience,
        style: const TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Column(
      children:
          experiences
              .map((exp) => DisplayWorkExperienceItem(experience: exp))
              .toList(),
    );
  }
}

/// Display Work Experience Item Widget
class DisplayWorkExperienceItem extends StatelessWidget {
  final WorkExperience experience;

  const DisplayWorkExperienceItem({super.key, required this.experience});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Position und Unternehmen
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.work_outline,
                  color: AppTheme.primaryLightColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      experience.position ?? '',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      experience.company ?? '',
                      style: TextStyle(
                        color: AppTheme.primaryLightColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Zeitraum
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                color: Colors.grey[400],
                size: 14,
              ),
              const SizedBox(width: 8),
              Text(
                '${DateFormat('MM/yyyy').format(experience.startDate)} - ${experience.endDate != null ? DateFormat('MM/yyyy').format(experience.endDate!) : 'Heute'}',
                style: TextStyle(color: Colors.grey[400], fontSize: 13),
              ),
            ],
          ),

          // Beschreibung
          if (experience.description.isNotEmpty == true) ...[
            const SizedBox(height: 12),
            Text(
              experience.description,
              style: TextStyle(
                color: Colors.grey[300],
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

// --- Display Education ---

/// Display Education List Widget
class DisplayEducationList extends StatelessWidget {
  final List<Education> educations;

  const DisplayEducationList({super.key, required this.educations});

  @override
  Widget build(BuildContext context) {
    if (educations.isEmpty) {
      return Text(
        AppLocalizationsWrapper.of(context).noEducation,
        style: const TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Column(
      children:
          educations
              .map((edu) => DisplayEducationItem(education: edu))
              .toList(),
    );
  }
}

/// Display Education Item Widget
class DisplayEducationItem extends StatelessWidget {
  final Education education;

  const DisplayEducationItem({super.key, required this.education});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Institution und Abschluss
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.school_outlined,
                  color: AppTheme.primaryLightColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      education.institution ?? '',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      education.degree ?? '',
                      style: TextStyle(
                        color: AppTheme.primaryLightColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (education.fieldOfStudy.isNotEmpty == true) ...[
                      const SizedBox(height: 2),
                      Text(
                        education.fieldOfStudy,
                        style: TextStyle(color: Colors.grey[400], fontSize: 13),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Zeitraum
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                color: Colors.grey[400],
                size: 14,
              ),
              const SizedBox(width: 8),
              Text(
                '${DateFormat('MM/yyyy').format(education.startDate)} - ${education.endDate != null ? DateFormat('MM/yyyy').format(education.endDate!) : 'Heute'}',
                style: TextStyle(color: Colors.grey[400], fontSize: 13),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// --- Display Job Preferences ---

/// Display Job Preferences Widget
class DisplayJobPreferences extends StatelessWidget {
  final dynamic preferences;

  const DisplayJobPreferences({super.key, required this.preferences});

  String _formatCurrency(double? amount) {
    if (amount == null) return 'Nicht angegeben';
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.')} €';
  }

  @override
  Widget build(BuildContext context) {
    if (preferences == null) {
      return const Text(
        'Keine Jobpräferenzen angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (preferences.targetPosition?.isNotEmpty ?? false)
          buildModernInfoRow(
            'Angestrebte Position',
            preferences.targetPosition!,
          ),
        if (preferences.industry?.isNotEmpty ?? false)
          buildModernInfoRow('Branche', preferences.industry!),
        if (preferences.locationPreference?.isNotEmpty ?? false)
          buildModernInfoRow(
            'Gewünschter Standort',
            preferences.locationPreference!,
          ),
        buildModernInfoRow(
          'Gehaltsvorstellung',
          _formatCurrency(preferences.desiredSalary),
        ),
        if (preferences.employmentType?.isNotEmpty ?? false)
          buildModernInfoRow('Beschäftigungsart', preferences.employmentType!),
      ],
    );
  }
}

// --- Editable Job Preferences ---

/// Editable Job Preferences Widget
class EditableJobPreferences extends StatelessWidget {
  final ProfileControllers controllers;

  const EditableJobPreferences({super.key, required this.controllers});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildModernTextField(
          context,
          controller: controllers.targetPositionController,
          label: 'Angestrebte Position',
          icon: Icons.work_outline,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.industryController,
          label: 'Branche',
          icon: Icons.category_outlined,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.locationController,
          label: 'Gewünschter Standort',
          icon: Icons.location_on_outlined,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.salaryController,
          label: 'Gehaltsvorstellung (€)',
          icon: Icons.euro_outlined,
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        buildModernTextField(
          context,
          controller: controllers.employmentTypeController,
          label: 'Beschäftigungsart (z.B. Vollzeit, Teilzeit)',
          icon: Icons.schedule_outlined,
        ),
      ],
    );
  }
}

/// Editierbare CV-Sektion für den Upload und die Auswahl von Lebensläufen
/// Extrahiert aus ProfileScreen für bessere Modularität
class EditableCvSection extends StatelessWidget {
  final String? initialDownloadUrl;
  final String? locallySelectedFilePath;
  final Function(String?) onFilePicked;
  final VoidCallback onClearSelection;

  const EditableCvSection({
    super.key,
    this.initialDownloadUrl,
    this.locallySelectedFilePath,
    required this.onFilePicked,
    required this.onClearSelection,
  });

  @override
  Widget build(BuildContext context) {
    String displayFileName = 'Kein Lebenslauf ausgewählt oder hochgeladen';
    bool hasFile = false;

    if (locallySelectedFilePath != null) {
      displayFileName = 'Lebenslauf ausgewählt';
      hasFile = true;
    } else if (initialDownloadUrl != null) {
      displayFileName = 'Lebenslauf hochgeladen';
      hasFile = true;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
          child: Row(
            children: [
              Icon(
                hasFile
                    ? Icons.check_circle_outline
                    : Icons.radio_button_unchecked,
                size: 18,
                color: hasFile ? Colors.greenAccent : Colors.grey,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  displayFileName,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: hasFile ? Colors.white70 : Colors.grey,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (locallySelectedFilePath != null)
                IconButton(
                  icon: Icon(
                    Icons.close,
                    size: 18,
                    color: Theme.of(
                      context,
                    ).colorScheme.error.withValues(alpha: 0.7),
                  ),
                  tooltip: 'Auswahl entfernen',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onClearSelection,
                ),
            ],
          ),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.upload_file),
          label: Text(hasFile ? 'Neue PDF auswählen' : 'PDF auswählen'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryLightColor.withValues(alpha: 0.3),
            foregroundColor: Colors.white,
            side: BorderSide(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.5),
            ),
          ),
          onPressed: () async {
            try {
              // Verwende FilePicker für echte Dateiauswahl
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['pdf'],
                allowMultiple: false,
              );

              if (result != null && result.files.single.path != null) {
                final filePath = result.files.single.path!;
                onFilePicked(filePath);
              } else {
                // User hat die Auswahl abgebrochen
                onFilePicked(null);
              }
            } catch (e) {
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Fehler bei Dateiauswahl: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Text(
          'Hinweis: Das Auswählen einer neuen PDF ersetzt beim Speichern die vorherige.',
          style: Theme.of(
            context,
          ).textTheme.labelSmall?.copyWith(fontStyle: FontStyle.normal),
        ),
      ],
    );
  }
}

// --- Display Additional Documents Section ---

/// Display Additional Documents Section Widget
class DisplayAdditionalDocumentsSection extends ConsumerWidget {
  const DisplayAdditionalDocumentsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final documentsAsync = ref.watch(additionalDocumentsProvider);
    final userProfileAsync = ref.watch(userProfileProvider);

    return documentsAsync.when(
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.all(AppTheme.spacingMedium),
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[400]),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Fehler beim Laden der Dokumente',
                style: TextStyle(color: Colors.red[400]),
              ),
            ),
          ],
        ),
      ),
      data: (documents) {
        return userProfileAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Fehler: $error'),
          data: (userProfile) {
            final hasCv = userProfile.cvDownloadUrl != null && userProfile.cvDownloadUrl!.isNotEmpty;
            final hasDocuments = documents.isNotEmpty;

            if (!hasCv && !hasDocuments) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.folder_outlined, color: Colors.grey[400]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Keine Dokumente hochgeladen',
                        style: TextStyle(color: Colors.grey[400]),
                      ),
                    ),
                  ],
                ),
              );
            }

            // Erstelle Liste aller Dokumente (CV + zusätzliche)
            final List<Widget> allDocuments = [];

            // CV als erstes Element hinzufügen (falls vorhanden)
            if (hasCv) {
              allDocuments.add(_DisplayCvDocumentItem(
                fileName: userProfile.cvFileName ?? 'Lebenslauf.pdf',
                downloadUrl: userProfile.cvDownloadUrl!,
              ));
            }

            // Zusätzliche Dokumente hinzufügen
            allDocuments.addAll(documents.map((document) => _DisplayDocumentItem(document: document)));

            // Zähle aktive Dokumente (CV + zusätzliche)
            final isCvActive = ref.watch(cvApplicationStatusProvider);
            final activeDocsCount = (isCvActive && hasCv ? 1 : 0) +
                                   documents.where((doc) => doc.isActiveForApplications).length;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Info über aktive Dokumente
                if (activeDocsCount > 0)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle_outline,
                             size: 16, color: Colors.green[400]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '$activeDocsCount Dokument(e) werden automatisch bei Bewerbungen angehängt',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.green[400],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Alle Dokumente anzeigen
                ...allDocuments,
              ],
            );
          },
        );
      },
    );
  }
}

/// Einzelnes Dokument Display Item
class _DisplayDocumentItem extends StatelessWidget {
  final doc.AdditionalDocument document;

  const _DisplayDocumentItem({required this.document});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(document.fileType),
            size: 20,
            color: AppTheme.primaryLightColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${_formatFileSize(document.fileSize)} • ${_formatDate(document.uploadDate)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
          if (document.isActiveForApplications)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Aktiv',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green[400],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String fileType) {
    if (fileType.contains('pdf')) {
      return Icons.picture_as_pdf_outlined;
    } else if (fileType.contains('word') || fileType.contains('document')) {
      return Icons.description_outlined;
    }
    return Icons.insert_drive_file_outlined;
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd.MM.yyyy').format(date);
  }
}

/// CV-Dokument Display Item (für Anzeigeansicht)
class _DisplayCvDocumentItem extends ConsumerWidget {
  final String fileName;
  final String downloadUrl;

  const _DisplayCvDocumentItem({
    required this.fileName,
    required this.downloadUrl,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isCvActive = ref.watch(cvApplicationStatusProvider);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description_outlined,
            size: 20,
            color: Colors.blue[300],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.blue[300],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  'Lebenslauf',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue[400],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          if (isCvActive)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Aktiv',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.blue[400],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Display KI-Personalisierung Widget für das Profile-Management
/// Diese Datei enthält das Display-Widget für KI-Personalisierung

// --- Display KI Personalisierung ---

/// Display KI Personalisierung Widget
class DisplayKiPersonalization extends ConsumerStatefulWidget {
  final bool cvAutoFillEnabled;
  final bool skillsInApplication;
  final bool workExperienceInApplication;
  final bool educationInApplication;
  final bool isPersonalized;
  final String applicationLength;

  const DisplayKiPersonalization({
    super.key,
    required this.cvAutoFillEnabled,
    required this.skillsInApplication,
    required this.workExperienceInApplication,
    required this.educationInApplication,
    required this.isPersonalized,
    required this.applicationLength,
  });

  @override
  ConsumerState<DisplayKiPersonalization> createState() =>
      _DisplayKiPersonalizationState();
}

class _DisplayKiPersonalizationState
    extends ConsumerState<DisplayKiPersonalization> {
  // State für die Optionen
  late ValueNotifier<bool> _cvAutoFillEnabled;
  late ValueNotifier<bool> _skillsInApplication;
  late ValueNotifier<bool> _workExperienceInApplication;
  late ValueNotifier<bool> _educationInApplication;
  late ValueNotifier<bool> _isPersonalized;

  @override
  void initState() {
    super.initState();
    // Initialisiere die ValueNotifier mit den übergebenen Werten
    _cvAutoFillEnabled = ValueNotifier<bool>(widget.cvAutoFillEnabled);
    _skillsInApplication = ValueNotifier<bool>(widget.skillsInApplication);
    _workExperienceInApplication = ValueNotifier<bool>(
      widget.workExperienceInApplication,
    );
    _educationInApplication = ValueNotifier<bool>(
      widget.educationInApplication,
    );
    _isPersonalized = ValueNotifier<bool>(widget.isPersonalized);
  }

  @override
  void dispose() {
    // Dispose die ValueNotifier
    _cvAutoFillEnabled.dispose();
    _skillsInApplication.dispose();
    _workExperienceInApplication.dispose();
    _educationInApplication.dispose();
    _isPersonalized.dispose();
    super.dispose();
  }

  // Funktion zum Umschalten einer Option
  void _toggleOption(ValueNotifier<bool> option) {
    option.value = !option.value;

    // Hier würde normalerweise die Logik zum Speichern der Änderung im UserProfile stehen
    // Da dies nur ein Beispiel ist, zeigen wir nur eine Snackbar an
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(option.value ? 'Option aktiviert' : 'Option deaktiviert'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // Hilfsfunktion zum Erstellen eines Optionselements
  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required ValueNotifier<bool> option,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: option,
      builder: (context, value, child) {
        return GestureDetector(
          onTap: () => _toggleOption(option),
          child: Container(
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              border: Border.all(
                color: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                    color: Color(0xFF2D1F37), // Lila-Grau-Ton
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, size: 16, color: const Color(0xFFCCCCCC)),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(fontSize: 15, color: Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  value
                      ? 'Aktiviert' // Deutsch statt Englisch
                      : 'Deaktiviert', // Deutsch statt Englisch
                  style: TextStyle(
                    color: value ? Colors.greenAccent : Colors.redAccent,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // CV Autofill
        _buildOptionItem(
          icon: Icons.auto_awesome,
          title: 'Automatisch aus Lebenslauf ausfüllen',
          option: _cvAutoFillEnabled,
        ),

        // Fähigkeiten in Anschreiben
        _buildOptionItem(
          icon: Icons.psychology_outlined,
          title: 'Fähigkeiten in Bewerbungen',
          option: _skillsInApplication,
        ),

        // Berufserfahrung in Anschreiben
        _buildOptionItem(
          icon: Icons.work_outline,
          title: 'Berufserfahrung in Bewerbungen',
          option: _workExperienceInApplication,
        ),

        // Ausbildung in Anschreiben
        _buildOptionItem(
          icon: Icons.school_outlined,
          title: 'Bildung in Anschreiben',
          option: _educationInApplication,
        ),

        // Persönlicher Stil
        _buildOptionItem(
          icon: Icons.person_outline,
          title: 'Bevorzugter Schreibstil',
          option: _isPersonalized,
        ),

        // Bewerbungslänge (nur Anzeige, nicht umschaltbar)
        Container(
          margin: const EdgeInsets.only(bottom: 10),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFF1C1C1E),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: Colors.white.withValues(alpha: 26),
              width: 0.5,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: const BoxDecoration(
                  color: Color(0xFF2D1F37),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.text_fields_outlined,
                  size: 16,
                  color: Color(0xFFCCCCCC),
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Bewerbungslänge',
                  style: TextStyle(fontSize: 15, color: Colors.white),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                widget.applicationLength,
                style: const TextStyle(
                  color: Colors.blueAccent,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

/// Skills-Widgets für das Profile-Management
/// Diese Datei enthält alle Skills-bezogenen Widgets

// --- Editierbare Skills ---

/// Editierbare Skills Liste (kompakt, Button immer rechts mittig)
class EditableSkillsListRefactored extends HookWidget {
  final List<TextEditingController> initialSkills;
  final Function(int) onRemoveSkill;
  final int lastAddedIndex;

  const EditableSkillsListRefactored({
    super.key,
    required this.initialSkills,
    required this.onRemoveSkill,
    this.lastAddedIndex = -1,
  });

  @override
  Widget build(BuildContext context) {
    final itemKeys = useState<Map<int, GlobalKey>>({});
    final highlightedIndex = useState<int>(lastAddedIndex);
    final isMounted = useIsMounted();

    useEffect(() {
      final currentKeys = <int, GlobalKey>{};
      for (int i = 0; i < initialSkills.length; i++) {
        currentKeys[i] = itemKeys.value[i] ?? GlobalKey();
      }
      itemKeys.value = currentKeys;
      return null;
    }, [initialSkills]);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (isMounted() &&
            lastAddedIndex >= 0 &&
            lastAddedIndex < initialSkills.length) {
          highlightedIndex.value = lastAddedIndex;
          final key = itemKeys.value[lastAddedIndex];
          if (key?.currentContext != null) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              alignment: 0.5,
              duration: const Duration(milliseconds: 300),
            ).then((_) {
              Future.delayed(const Duration(seconds: 2), () {
                if (isMounted() && highlightedIndex.value == lastAddedIndex) {
                  highlightedIndex.value = -1;
                }
              });
            });
          }
        } else if (lastAddedIndex == -1 && highlightedIndex.value != -1) {
          highlightedIndex.value = -1;
        }
      });
      return null;
    }, [lastAddedIndex, initialSkills.length]);

    if (initialSkills.isEmpty) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Text(
          AppLocalizationsWrapper.of(context).noSkills,
          style: const TextStyle(color: Colors.grey),
        ),
      );
    }

    return Wrap(
      spacing: 6.0,
      runSpacing: 6.0,
      children: initialSkills.asMap().entries.map((entry) {
        int index = entry.key;
        TextEditingController controller = entry.value;
        bool isHighlighted = index == highlightedIndex.value;
        final itemKey = itemKeys.value[index] ?? GlobalKey();
        if (!itemKeys.value.containsKey(index)) {
          itemKeys.value = {...itemKeys.value, index: itemKey};
        }

        return Container(
          key: itemKey,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isHighlighted
                  ? [
                      AppTheme.primaryLightColor.withValues(alpha: 0.25),
                      AppTheme.primaryLightColor.withValues(alpha: 0.15),
                    ]
                  : [
                      AppTheme.primaryLightColor.withValues(alpha: 0.15),
                      AppTheme.primaryLightColor.withValues(alpha: 0.08),
                    ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isHighlighted
                  ? AppTheme.primaryLightColor.withValues(alpha: 0.5)
                  : AppTheme.primaryLightColor.withValues(alpha: 0.3),
              width: 0.8,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
              BoxShadow(
                color: AppTheme.primaryLightColor.withValues(alpha: 0.08),
                blurRadius: 4,
                spreadRadius: 0,
              ),
              if (isHighlighted)
                BoxShadow(
                  color: AppTheme.primaryLightColor.withValues(alpha: 0.15),
                  blurRadius: 6,
                  spreadRadius: 0.5,
                ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.psychology_outlined,
                  color: AppTheme.primaryLightColor,
                  size: 10,
                ),
              ),
              const SizedBox(width: 4),
              GestureDetector(
                onTap: () => _showEditDialog(context, controller, index),
                child: Text(
                  controller.text.isEmpty
                      ? AppLocalizationsWrapper.of(context).enterSkill
                      : controller.text,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: controller.text.isEmpty
                            ? Colors.white.withOpacity(0.5)
                            : Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.1,
                      ),
                ),
              ),
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(4),
                  onTap: () => onRemoveSkill(index),
                  child: Icon(
                    Icons.close,
                    color: Colors.red,
                    size: 10,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _showEditDialog(BuildContext context, TextEditingController controller, int index) {
    final tempController = TextEditingController(text: controller.text);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E1E1E),
        title: Text(
          'Fähigkeit bearbeiten',
          style: TextStyle(color: Colors.white),
        ),
        content: TextField(
          controller: tempController,
          autofocus: true,
          decoration: InputDecoration(
            hintText: AppLocalizationsWrapper.of(context).enterSkill,
            hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.primaryLightColor),
            ),
          ),
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Abbrechen',
              style: TextStyle(color: Colors.white.withOpacity(0.7)),
            ),
          ),
          TextButton(
            onPressed: () {
              controller.text = tempController.text;
              Navigator.of(context).pop();
            },
            child: Text(
              'Speichern',
              style: TextStyle(color: AppTheme.primaryLightColor),
            ),
          ),
        ],
      ),
    );
  }
}

// --- Display Skills ---

/// Display Skills Liste
class DisplaySkillsListRefactored extends StatelessWidget {
  final List<String> skills;
  const DisplaySkillsListRefactored({super.key, required this.skills});

  @override
  Widget build(BuildContext context) {
    if (skills.isEmpty) {
      return const Text(
        'Keine Fähigkeiten angegeben',
        style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
      );
    }

    return Wrap(
      spacing: 6.0,
      runSpacing: 6.0,
      children: skills.map((skill) => _buildSkillChip(context, skill)).toList(),
    );
  }

  Widget _buildSkillChip(BuildContext context, String skill) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryLightColor.withValues(alpha: 0.15),
            AppTheme.primaryLightColor.withValues(alpha: 0.08),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
          width: 0.8,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
          BoxShadow(
            color: AppTheme.primaryLightColor.withValues(alpha: 0.08),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppTheme.primaryLightColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              Icons.psychology_outlined,
              color: AppTheme.primaryLightColor,
              size: 10,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            skill,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import 'package:ki_test/src/presentation/profile/models/profile_models.dart';
import 'package:ki_test/src/presentation/profile/widgets/profile_ui_helpers.dart';

/// Editierbare Listen-Widgets für das Profile-Management
/// Diese Datei enthält alle editierbaren Listen-Widgets für Berufserfahrung, Ausbildung und Skills

// --- Editierbare Berufserfahrung ---

/// Editierbare Berufserfahrung Liste
class EditableWorkExperienceListRefactored extends ConsumerStatefulWidget {
  final List<EditableWorkExperience> initialExperiences;
  final Function(int, EditableWorkExperience) onExperienceChanged;
  final Function(int) onRemoveExperience;
  final int lastAddedIndex;

  const EditableWorkExperienceListRefactored({
    super.key,
    required this.initialExperiences,
    required this.onExperienceChanged,
    required this.onRemoveExperience,
    this.lastAddedIndex = -1,
  });

  @override
  ConsumerState<EditableWorkExperienceListRefactored> createState() =>
      _EditableWorkExperienceListRefactoredState();
}

class _EditableWorkExperienceListRefactoredState
    extends ConsumerState<EditableWorkExperienceListRefactored> {
  late List<EditableWorkExperience> _editableExperiences = [];
  final Map<int, GlobalKey> _itemKeys = {};
  int _highlightedIndex = -1;

  @override
  void initState() {
    super.initState();
    _highlightedIndex = widget.lastAddedIndex;
    _updateEditableExperiences(widget.initialExperiences);
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToHighlighted());
  }

  @override
  void didUpdateWidget(EditableWorkExperienceListRefactored oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!listEquals(widget.initialExperiences, oldWidget.initialExperiences)) {
      _updateEditableExperiences(widget.initialExperiences);
    }
    if (widget.lastAddedIndex != oldWidget.lastAddedIndex) {
      if (mounted) {
        setState(() {
          _highlightedIndex = widget.lastAddedIndex;
        });
      }
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _scrollToHighlighted(),
      );
    }
  }

  @override
  void dispose() {
    for (var state in _editableExperiences) {
      state.dispose();
    }
    super.dispose();
  }

  void _updateEditableExperiences(
    List<EditableWorkExperience> newExperiences,
  ) {
    if (mounted) {
      for (var state in _editableExperiences) {
        state.dispose();
      }
    }

    _editableExperiences =
        newExperiences
            .map(
              (exp) => EditableWorkExperience(
                positionController: TextEditingController(
                  text: exp.positionController.text,
                ),
                companyController: TextEditingController(
                  text: exp.companyController.text,
                ),
                descriptionController: TextEditingController(
                  text: exp.descriptionController.text,
                ),
                startDate: exp.startDate,
                endDate: exp.endDate,
              ),
            )
            .toList();

    _itemKeys.clear();
    for (int i = 0; i < _editableExperiences.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    if (mounted) {
      setState(() {});
    }
  }

  void _scrollToHighlighted() {
    if (!mounted) return;
    if (_highlightedIndex >= 0 &&
        _highlightedIndex < _editableExperiences.length) {
      final key = _itemKeys[_highlightedIndex];
      if (key?.currentContext != null) {
        Scrollable.ensureVisible(
          key!.currentContext!,
          alignment: 0.5,
          duration: const Duration(milliseconds: 300),
        ).then((_) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted && _highlightedIndex == widget.lastAddedIndex) {
              setState(() {
                _highlightedIndex = -1;
              });
            }
          });
        });
      }
    } else if (_highlightedIndex != -1) {
      if (mounted) {
        setState(() {
          _highlightedIndex = -1;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (_editableExperiences.isEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              AppLocalizationsWrapper.of(context).noWorkExperience,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ..._editableExperiences.asMap().entries.map((entry) {
          int index = entry.key;
          EditableWorkExperience state = entry.value;
          bool isHighlighted = index == _highlightedIndex;
          final itemKey = _itemKeys[index]!;

          return Container(
            key: itemKey,
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(12),
              border:
                  isHighlighted
                      ? Border.all(
                        color: AppTheme.primaryLightColor,
                        width: 1.5,
                      )
                      : Border.all(color: Colors.transparent, width: 0.5),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    12,
                    8,
                    12,
                    8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildModernTextField(
                        context,
                        controller: state.positionController,
                        label: AppLocalizationsWrapper.of(context).position,
                        icon: Icons.work_outline,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.companyController,
                        label: AppLocalizationsWrapper.of(context).company,
                        icon: Icons.business,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.descriptionController,
                        label: AppLocalizationsWrapper.of(context).description,
                        maxLines: 3,
                        icon: Icons.description_outlined,
                      ),
                      const SizedBox(height: 6),
                      buildModernDateField(
                        context: context,
                        label: AppLocalizationsWrapper.of(context).startDate,
                        selectedDate: state.startDate,
                        isOptional: false,
                        onTap: () => selectModernDate(
                          context,
                          initialDate: state.startDate,
                          firstDate: DateTime(1980),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365 * 10),
                          ),
                          onDateSelected: (pickedDate) {
                            widget.onExperienceChanged(
                              index,
                              state.copyWith(
                                startDate: pickedDate,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    icon: const Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(),
                    onPressed: () => widget.onRemoveExperience(index),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }
}

// --- Editierbare Ausbildung ---

/// Editierbare Ausbildung Liste
class EditableEducationListRefactored extends ConsumerStatefulWidget {
  final List<EditableEducation> initialEducations;
  final Function(int, EditableEducation) onEducationChanged;
  final Function(int) onRemoveEducation;
  final int lastAddedIndex;

  const EditableEducationListRefactored({
    super.key,
    required this.initialEducations,
    required this.onEducationChanged,
    required this.onRemoveEducation,
    this.lastAddedIndex = -1,
  });

  @override
  ConsumerState<EditableEducationListRefactored> createState() =>
      _EditableEducationListRefactoredState();
}

class _EditableEducationListRefactoredState
    extends ConsumerState<EditableEducationListRefactored> {
  late List<EditableEducation> _editableEducations = [];
  final Map<int, GlobalKey> _itemKeys = {};
  int _highlightedIndex = -1;

  @override
  void initState() {
    super.initState();
    _highlightedIndex = widget.lastAddedIndex;
    _updateEditableEducations(widget.initialEducations);
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToHighlighted());
  }

  @override
  void didUpdateWidget(EditableEducationListRefactored oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!listEquals(widget.initialEducations, oldWidget.initialEducations)) {
      _updateEditableEducations(widget.initialEducations);
    }
    if (widget.lastAddedIndex != oldWidget.lastAddedIndex) {
      if (mounted) {
        setState(() {
          _highlightedIndex = widget.lastAddedIndex;
        });
      }
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _scrollToHighlighted(),
      );
    }
  }

  @override
  void dispose() {
    for (var state in _editableEducations) {
      state.dispose();
    }
    super.dispose();
  }

  void _updateEditableEducations(List<EditableEducation> newEducations) {
    if (mounted) {
      for (var state in _editableEducations) {
        state.dispose();
      }
    }

    _editableEducations =
        newEducations
            .map(
              (edu) => EditableEducation(
                institutionController: TextEditingController(
                  text: edu.institutionController.text,
                ),
                degreeController: TextEditingController(
                  text: edu.degreeController.text,
                ),
                fieldOfStudyController: TextEditingController(
                  text: edu.fieldOfStudyController.text,
                ),
                startDate: edu.startDate,
                endDate: edu.endDate,
              ),
            )
            .toList();

    _itemKeys.clear();
    for (int i = 0; i < _editableEducations.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    if (mounted) {
      setState(() {});
    }
  }

  void _scrollToHighlighted() {
    if (!mounted) return;
    if (_highlightedIndex >= 0 &&
        _highlightedIndex < _editableEducations.length) {
      final key = _itemKeys[_highlightedIndex];
      if (key?.currentContext != null) {
        Scrollable.ensureVisible(
          key!.currentContext!,
          alignment: 0.5,
          duration: const Duration(milliseconds: 300),
        ).then((_) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted && _highlightedIndex == widget.lastAddedIndex) {
              setState(() {
                _highlightedIndex = -1;
              });
            }
          });
        });
      }
    } else if (_highlightedIndex != -1) {
      if (mounted) {
        setState(() {
          _highlightedIndex = -1;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (_editableEducations.isEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              AppLocalizationsWrapper.of(context).noEducation,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ..._editableEducations.asMap().entries.map((entry) {
          int index = entry.key;
          EditableEducation state = entry.value;
          bool isHighlighted = index == _highlightedIndex;
          final itemKey = _itemKeys[index]!;

          return Container(
            key: itemKey,
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Color(0xFF1C1C1E),
              borderRadius: BorderRadius.circular(12),
              border:
                  isHighlighted
                      ? Border.all(
                        color: AppTheme.primaryLightColor,
                        width: 1.5,
                      )
                      : Border.all(color: Colors.transparent, width: 0.5),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    12,
                    8,
                    12,
                    8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildModernTextField(
                        context,
                        controller: state.institutionController,
                        label:
                            AppLocalizationsWrapper.of(context).institution,
                        icon: Icons.school_outlined,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.degreeController,
                        label: AppLocalizationsWrapper.of(context).degree,
                        icon: Icons.grade_outlined,
                      ),
                      buildModernTextField(
                        context,
                        controller: state.fieldOfStudyController,
                        label:
                            AppLocalizationsWrapper.of(context).fieldOfStudy,
                        icon: Icons.book_outlined,
                      ),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: buildModernDateField(
                              context: context,
                              label:
                                  AppLocalizationsWrapper.of(
                                    context,
                                  ).startDate,
                              selectedDate: state.startDate,
                              isOptional: false,
                              onTap:
                                  () => selectModernDate(
                                    context,
                                    initialDate: state.startDate,
                                    firstDate: DateTime(1980),
                                    lastDate:
                                        state.endDate ??
                                        DateTime.now().add(
                                          const Duration(days: 365 * 10),
                                        ),
                                    onDateSelected: (pickedDate) {
                                      if (state.endDate != null &&
                                          pickedDate.isAfter(
                                            state.endDate!,
                                          )) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              AppLocalizationsWrapper.of(
                                                context,
                                              ).startDateError,
                                            ),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      } else {
                                        widget.onEducationChanged(
                                          index,
                                          state.copyWith(
                                            startDate: pickedDate,
                                          ),
                                        );
                                      }
                                    },
                                  ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: buildModernDateField(
                              context: context,
                              label:
                                  AppLocalizationsWrapper.of(context).endDate,
                              selectedDate: state.endDate,
                              isOptional: true,
                              onTap:
                                  () => selectModernDate(
                                    context,
                                    initialDate:
                                        state.endDate ?? DateTime.now(),
                                    firstDate: state.startDate,
                                    lastDate: DateTime.now().add(
                                      const Duration(days: 365 * 10),
                                    ),
                                    onDateSelected: (pickedDate) {
                                      widget.onEducationChanged(
                                        index,
                                        state.copyWith(endDate: pickedDate),
                                      );
                                    },
                                  ),
                              onClear: () {
                                widget.onEducationChanged(
                                  index,
                                  state.copyWith(clearEndDate: true),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    icon: const Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(),
                    onPressed: () => widget.onRemoveEducation(index),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }
}



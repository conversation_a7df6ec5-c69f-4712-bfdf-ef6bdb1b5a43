import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';

/// AppBar Widget für das Profile-Management
/// Diese Datei enthält das spezialisierte AppBar-Widget für den Profile-Screen

/// AppBar Widget (angepasst für dunkles Theme)
class ProfileAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool isEditing;
  final VoidCallback onToggleEdit;
  final VoidCallback onSave;
  final ValueNotifier<bool> isUploadingCv;

  const ProfileAppBar({
    super.key,
    required this.isEditing,
    required this.onToggleEdit,
    required this.onSave,
    required this.isUploadingCv,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isUploadingCv,
      builder: (context, isUploading, child) {
        return AppBar(
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          elevation: 0,
          foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          title: Text(
            isEditing
                ? AppLocalizationsWrapper.of(context).editProfile
                : AppLocalizationsWrapper.of(context).profileTitle,
            style: Theme.of(context).appBarTheme.titleTextStyle,
          ),
          leading:
              isEditing
                  ? IconButton(
                    icon: const Icon(Icons.close),
                    tooltip: AppLocalizationsWrapper.of(context).cancel,
                    onPressed: onToggleEdit,
                  )
                  : null,
          actions: [
            if (isEditing)
              TextButton(
                onPressed: isUploading ? null : onSave,
                style: Theme.of(context).textButtonTheme.style?.copyWith(
                  foregroundColor: WidgetStateProperty.all(Colors.white),
                ),
                child:
                    isUploading
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                        : Text(
                          AppLocalizationsWrapper.of(
                            context,
                          ).save.toUpperCase(),
                        ),
              )
            else
              IconButton(
                icon: const Icon(Icons.edit_outlined),
                tooltip: AppLocalizationsWrapper.of(context).editProfile,
                onPressed: onToggleEdit,
              ),
          ],
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart' show listEquals;

import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/models/extracted_cv_data.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Service für CV-Upload und -Verarbeitung
/// Extrahiert aus ProfileScreen für bessere Wartbarkeit und Testbarkeit
class ProfileCvService {
  /// Upload und Verarbeitung eines CV-Files
  static Future<void> uploadAndProcessCv(
    BuildContext context,
    WidgetRef ref,
    File file,
    ValueNotifier<bool> isUploadingCv,
  ) async {
    // Funktion zum Anzeigen von Snackbars
    void showStatusSnackBar(
      String message, {
      Color? backgroundColor,
      Duration duration = const Duration(seconds: 3),
    }) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration,
        ),
      );
    }

    isUploadingCv.value = true;
    showStatusSnackBar('Dein Lebenslauf wird vorbereitet...');

    try {
      // Basisdaten holen
      final fileName = path.basename(file.path);
      final currentUser = Supabase.instance.client.auth.currentUser;
      final userId = currentUser?.id ?? '';
      if (userId.isEmpty) throw Exception('Benutzer nicht angemeldet.');

      // Verwende die UserProfileProvider-Methode für den Upload
      showStatusSnackBar('Sichere Übertragung startet...');

      // Rufe die Methode im UserProfileProvider auf, die den Upload und die Analyse durchführt
      showStatusSnackBar('Analyse deines Dokuments beginnt...');
      final extractedData = await ref
          .read(userProfileProvider.notifier)
          .uploadAndAnalyzeCv(file.path, fileName);

      debugPrint(
        "[ProfileCvService] CV Upload zu Supabase Storage erfolgreich.",
      );

      // Prüfe, ob Daten extrahiert wurden
      if (extractedData != null) {
        debugPrint(
          "[ProfileCvService] Daten erfolgreich aus dem Lebenslauf extrahiert.",
        );

        // Zeige den Bestätigungsdialog an
        final currentProfile = ref.read(userProfileProvider).valueOrNull;
        if (currentProfile != null && context.mounted) {
          await showUpdateConfirmationDialog(
            context,
            ref,
            currentProfile,
            extractedData,
          );
        } else {
          showStatusSnackBar(
            'Lebenslauf verarbeitet, aber Profil nicht verfügbar für Aktualisierung.',
            backgroundColor: Colors.orange,
          );
        }
      } else {
        showStatusSnackBar(
          'Lebenslauf hochgeladen, aber keine Daten konnten extrahiert werden.',
          backgroundColor: Colors.orange,
        );
      }
    } catch (e, stackTrace) {
      debugPrint("[ProfileCvService] Fehler im Upload/Processing Flow: $e");
      debugPrint(stackTrace.toString());

      String errorMessage = e.toString();
      if (e is Exception) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      }
      showStatusSnackBar(
        'Fehler: $errorMessage',
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 6),
      );
    } finally {
      isUploadingCv.value = false;
    }
  }

  /// Zeigt den Bestätigungsdialog für Profil-Updates an
  static Future<void> showUpdateConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    UserProfile currentProfile,
    ExtractedCvData extractedData,
  ) async {
    // Prüfen, ob relevante Unterschiede bestehen
    bool hasNewSkills =
        !listEquals(
          extractedData.skills.toSet().toList()..sort(),
          (currentProfile.skills ?? []).toSet().toList()..sort(),
        );

    // Wir betrachten jetzt auch leere Listen als "neue Daten", damit wir auch Daten löschen können
    bool hasNewExperience = true; // Immer aktualisieren, auch wenn leer
    bool hasNewEducation = true; // Immer aktualisieren, auch wenn leer

    // Baue eine Nachricht für den Dialog
    String changesSummary = 'Folgende Änderungen werden vorgenommen:\n';

    if (hasNewSkills) {
      if (extractedData.skills.isEmpty) {
        changesSummary += '- Alle Fähigkeiten werden entfernt\n';
      } else {
        changesSummary += '- Fähigkeiten werden aktualisiert\n';
      }
    }

    // Prüfe, ob Berufserfahrungen vorhanden sind
    if (extractedData.workExperience.isEmpty) {
      if (currentProfile.workExperience?.isNotEmpty ?? false) {
        changesSummary += '- Alle Berufserfahrungen werden entfernt\n';
      } else {
        changesSummary += '- Keine Berufserfahrungen gefunden\n';
      }
    } else {
      changesSummary +=
          '- Berufserfahrungen werden aktualisiert (${extractedData.workExperience.length} Einträge)\n';
    }

    // Prüfe, ob Ausbildungen vorhanden sind
    if (extractedData.education.isEmpty) {
      if (currentProfile.education?.isNotEmpty ?? false) {
        changesSummary += '- Alle Ausbildungen werden entfernt\n';
      } else {
        changesSummary += '- Keine Ausbildungen gefunden\n';
      }
    } else {
      changesSummary +=
          '- Ausbildungen werden aktualisiert (${extractedData.education.length} Einträge)\n';
    }

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: AppTheme.surfaceDarkColor.withValues(alpha: 0.95),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
          title: const Text(
            'Profil aktualisieren?',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Text(
              changesSummary,
              style: const TextStyle(color: Colors.white70),
            ),
          ),
          actions: <Widget>[
            TextButton(
              style: Theme.of(dialogContext).textButtonTheme.style?.copyWith(
                foregroundColor: WidgetStateProperty.all(Colors.grey),
              ),
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('Nein, alte Daten behalten'),
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.primaryLightColor,
              ),
              child: const Text('Ja, aktualisieren'),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      await _updateProfileWithExtractedData(
        context,
        ref,
        currentProfile,
        extractedData,
        hasNewSkills,
      );
    } else {
      debugPrint("[ProfileCvService] Benutzer lehnt Profil-Update ab.");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profil wurde nicht aktualisiert.')),
      );
    }
  }

  /// Aktualisiert das Profil mit extrahierten Daten
  static Future<void> _updateProfileWithExtractedData(
    BuildContext context,
    WidgetRef ref,
    UserProfile currentProfile,
    ExtractedCvData extractedData,
    bool hasNewSkills,
  ) async {
    debugPrint("[ProfileCvService] Benutzer bestätigt Profil-Update.");
    try {
      // Erstelle aktualisiertes Profil
      final updatedProfile = currentProfile.copyWith(
        skills: hasNewSkills ? extractedData.skills : currentProfile.skills,
        workExperience:
            extractedData.workExperience.isNotEmpty ||
                    (currentProfile.workExperience?.isNotEmpty ?? false)
                ? extractedData.workExperience
                : currentProfile.workExperience,
        education:
            extractedData.education.isNotEmpty ||
                    (currentProfile.education?.isNotEmpty ?? false)
                ? extractedData.education
                : currentProfile.education,
      );

      // Speichere das komplett aktualisierte Profil
      await ref
          .read(userProfileProvider.notifier)
          .saveProfileChanges(updatedProfile);

      // Zeige Erfolgsmeldung für die Profilaktualisierung
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Profil erfolgreich mit Daten aus Lebenslauf aktualisiert.',
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );

      // Automatisch Berufsbezeichnungen generieren
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Generiere Berufsbezeichnungen...'),
            duration: Duration(seconds: 2),
          ),
        );

        final success =
            await ref.read(userProfileProvider.notifier).updateJobKeywords();

        if (context.mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Berufsbezeichnungen erfolgreich generiert!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Fehler beim Generieren der Berufsbezeichnungen.',
                ),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e, stack) {
      debugPrint(
        "[ProfileCvService] Fehler beim Speichern des aktualisierten Profils: $e\n$stack",
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Fehler beim Aktualisieren des Profils: ${e.toString()}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

# Profile Module - Developer Guidelines

## 👨‍💻 Entwickler-Richtlinien

Diese Guidelines helfen dabei, die modulare Architektur des Profile-Modules zu verstehen und korrekt zu erweitern.

## 📋 Grundprinzipien

### **1. Modulare Entwicklung**
- **Ein Modul, eine Verantwortung**: <PERSON><PERSON> sollte nur eine spezifische Aufgabe erfüllen
- **Klare Schnittstellen**: Öffentliche APIs sollten minimal und gut dokumentiert sein
- **Lose Kopplung**: Module sollten unabhängig voneinander funktionieren können

### **2. Code-Organisation**
- **Logische Gruppierung**: Verwandte Funktionalität gehört in dasselbe Modul
- **Konsistente Namensgebung**: Folge den bestehenden Namenskonventionen
- **Dokumentation**: Jedes öffentliche Interface sollte dokumentiert sein

## 🗂️ Dateistruktur-Konventionen

### **Widget-Module** (`widgets/`)
```
profile_[funktionalität]_widgets.dart
```
**Beispiele**:
- `profile_editable_widgets.dart` - Editierbare Komponenten
- `profile_display_widgets.dart` - Anzeige-Komponenten
- `profile_skills_widgets.dart` - Skills-spezifische Widgets

### **Service-Module** (`services/`)
```
profile_[bereich]_service.dart
```
**Beispiele**:
- `profile_cv_service.dart` - CV-Upload und -Verarbeitung
- `profile_validation_service.dart` - Datenvalidierung

### **Model-Module** (`models/`)
```
profile_[datentyp]_models.dart
```
**Beispiele**:
- `profile_models.dart` - Grundlegende Datenmodelle
- `profile_state_models.dart` - Zustandsmodelle

## 🔧 Entwicklungs-Workflows

### **Neues Widget hinzufügen**

#### **1. Modul identifizieren**
```dart
// Frage: Wo gehört das Widget hin?
// - Editierbar? → profile_editable_widgets.dart
// - Anzeige? → profile_display_widgets.dart
// - Skills? → profile_skills_widgets.dart
// - Neu? → Neues Modul erstellen
```

#### **2. Widget implementieren**
```dart
/// Dokumentation des Widgets
/// 
/// [parameter1] - Beschreibung
/// [parameter2] - Beschreibung
class MyNewWidget extends StatelessWidget {
  final String parameter1;
  final VoidCallback? parameter2;

  const MyNewWidget({
    super.key,
    required this.parameter1,
    this.parameter2,
  });

  @override
  Widget build(BuildContext context) {
    // Implementation
  }
}
```

#### **3. Export hinzufügen**
```dart
// Am Ende der Modul-Datei
// Kein expliziter Export nötig - Klasse ist automatisch verfügbar
```

#### **4. Import in profile_screen.dart**
```dart
import 'package:ki_test/src/presentation/profile/widgets/profile_[modul]_widgets.dart';
```

### **Neuen Service hinzufügen**

#### **1. Service-Klasse erstellen**
```dart
/// Service für [Funktionalität]
/// 
/// Verantwortlich für:
/// - [Aufgabe 1]
/// - [Aufgabe 2]
class ProfileMyService {
  
  /// [Methoden-Beschreibung]
  static Future<void> myMethod(
    BuildContext context,
    WidgetRef ref,
    // Parameter
  ) async {
    // Implementation
  }
}
```

#### **2. Error Handling implementieren**
```dart
try {
  // Service-Logik
} catch (e, stackTrace) {
  debugPrint("[ProfileMyService] Fehler: $e");
  debugPrint(stackTrace.toString());
  
  // Benutzer-Feedback
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Fehler: ${e.toString()}'),
      backgroundColor: Colors.red,
    ),
  );
}
```

### **Bestehende Komponente modifizieren**

#### **1. Modul lokalisieren**
```bash
# Suche nach der Komponente
grep -r "ComponentName" lib/src/presentation/profile/
```

#### **2. Abhängigkeiten prüfen**
```dart
// Prüfe, wo die Komponente verwendet wird
// Suche nach Imports und Verwendungen
```

#### **3. Tests aktualisieren**
```dart
// Aktualisiere entsprechende Tests
// Führe Tests aus: flutter test
```

## 🎨 UI-Konventionen

### **Theme-Verwendung**
```dart
// Verwende AppTheme-Konstanten
Container(
  padding: const EdgeInsets.all(AppTheme.spacingMedium),
  decoration: BoxDecoration(
    color: AppTheme.surfaceDarkColor,
    borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
  ),
)
```

### **Responsive Design**
```dart
// Verwende MediaQuery für responsive Layouts
Widget build(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  final isTablet = screenWidth > 600;
  
  return isTablet ? TabletLayout() : MobileLayout();
}
```

### **Accessibility**
```dart
// Füge Semantics für Accessibility hinzu
Semantics(
  label: 'Beschreibung für Screen Reader',
  child: YourWidget(),
)
```

## 🧪 Testing-Guidelines

### **Widget Tests**
```dart
testWidgets('MyWidget should display correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: MyWidget(parameter: 'test'),
    ),
  );
  
  expect(find.text('test'), findsOneWidget);
});
```

### **Service Tests**
```dart
group('ProfileMyService', () {
  testWidgets('myMethod should handle success', (tester) async {
    // Mock setup
    // Test implementation
    // Assertions
  });
  
  testWidgets('myMethod should handle errors', (tester) async {
    // Error scenario test
  });
});
```

## 📊 Performance-Guidelines

### **Widget-Optimierung**
```dart
// Verwende const Konstruktoren wo möglich
const MyWidget({super.key});

// Verwende Builder für teure Operationen
Builder(
  builder: (context) => ExpensiveWidget(),
)

// Verwende Keys für Listen
ListView.builder(
  itemBuilder: (context, index) => MyItem(
    key: ValueKey(items[index].id),
    item: items[index],
  ),
)
```

### **State-Management**
```dart
// Minimiere Rebuilds durch lokalen State
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  // Lokaler State nur für UI-spezifische Daten
}
```

## 🔍 Debugging-Tipps

### **Debug-Ausgaben**
```dart
// Verwende konsistente Debug-Präfixe
debugPrint("[ProfileMyWidget] Debug-Information: $value");
```

### **Widget Inspector**
```dart
// Verwende Flutter Inspector für UI-Debugging
// Aktiviere mit: flutter run --debug
```

### **Performance Profiling**
```dart
// Verwende Performance Overlay
MaterialApp(
  showPerformanceOverlay: true, // Nur für Debug
  // ...
)
```

## 🚀 Deployment-Checkliste

### **Vor dem Commit**
- [ ] Code kompiliert ohne Fehler
- [ ] Alle Tests bestehen
- [ ] Dokumentation aktualisiert
- [ ] Performance-Impact geprüft

### **Code Review**
- [ ] Folgt den Architektur-Prinzipien
- [ ] Verwendet bestehende Patterns
- [ ] Ist gut dokumentiert
- [ ] Hat angemessene Tests

## 🔧 Häufige Probleme und Lösungen

### **Import-Fehler**
```dart
// Problem: Widget nicht gefunden
// Lösung: Prüfe Import-Pfad
import 'package:ki_test/src/presentation/profile/widgets/profile_[modul]_widgets.dart';
```

### **State-Synchronisation**
```dart
// Problem: State nicht synchron
// Lösung: Verwende ref.read() für einmalige Werte, ref.watch() für reaktive Updates
final profile = ref.watch(userProfileProvider);
```

### **Performance-Probleme**
```dart
// Problem: Langsame Builds
// Lösung: Verwende const Widgets und Builder
const MyExpensiveWidget() // Wird nur einmal gebaut
```

## 📚 Weiterführende Ressourcen

### **Flutter Documentation**
- [Widget Catalog](https://docs.flutter.dev/development/ui/widgets)
- [Performance Best Practices](https://docs.flutter.dev/perf/best-practices)

### **Riverpod Documentation**
- [Riverpod Guide](https://riverpod.dev/docs/introduction/getting_started)

### **Project-specific**
- `README.md` - Modul-Übersicht
- `ARCHITECTURE.md` - Architektur-Details

## 🔄 Migration Guide

### **Für bestehende Entwickler**

#### **Was hat sich geändert?**
- `profile_screen.dart` wurde von 4686 auf 1009 Zeilen reduziert
- Widgets wurden in spezialisierte Module extrahiert
- CV-Upload-Logik wurde in einen Service ausgelagert
- Neue Import-Struktur erforderlich

#### **Wichtige Änderungen**

**Vorher**:
```dart
// Alles in einer Datei
class _EditableWorkExperienceList extends StatefulWidget {
  // 500+ Zeilen Code
}
```

**Nachher**:
```dart
// Import erforderlich
import 'package:ki_test/src/presentation/profile/widgets/profile_editable_widgets.dart';

// Verwendung
EditableWorkExperienceListRefactored(...)
```

#### **Migration-Schritte**
1. **Aktualisiere Imports**: Füge neue Widget-Imports hinzu
2. **Prüfe Widget-Namen**: Einige Widgets haben neue Namen (z.B. `EditableWorkExperienceListRefactored`)
3. **Teste Funktionalität**: Stelle sicher, dass alle Features noch funktionieren

### **Für neue Entwickler**
- Beginne mit `README.md` für Übersicht
- Studiere `ARCHITECTURE.md` für technische Details
- Folge diesen Guidelines für Entwicklung
- Verwende bestehende Patterns und Konventionen

---

**Erstellt**: Januar 2025
**Letzte Aktualisierung**: Januar 2025
**Version**: 2.0 (Nach Refactoring)

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Import Riverpod
// import 'package:ki_test/src/application/providers/ai_search_provider.dart'; // Entfernt: Import AI Search Provider
// import 'package:ki_test/src/application/services/location_service.dart'; // Entfernt
import '../../job_search/screens/job_search_screen.dart'; // Import für die Jobsuche
import '../../../core/theme/app_theme.dart'; // Für einheitliches Styling
import '../../../core/utils/logging.dart'; // Für Logging
import 'package:geolocator/geolocator.dart'; // Hinzugefügt für Location
import 'package:geocoding/geocoding.dart'; // Hinzugefügt für Geocoding
import 'package:permission_handler/permission_handler.dart'; // Hinzugefügt für Permissions
import '../../../application/providers/job_search_provider.dart'; // Import für den JobSearch Provider
import 'package:ki_test/src/core/l10n/app_localizations.dart'; // Für Lokalisierung

// Temporäre Location Helper Klasse (sollte refaktorisiert werden)
class _LocationHelper {
  final log = getLogger('_LocationHelper');

  Future<Position?> getCurrentLocation() async {
    // ... (Komplette Logik aus _LocationService.getCurrentLocation einfügen)
    // Permission Check
    var status = await Permission.locationWhenInUse.status;
    if (status.isDenied) {
      status = await Permission.locationWhenInUse.request();
      if (!status.isGranted) {
        log.w('Standortberechtigung verweigert.');
        throw Exception('Standortberechtigung erforderlich.');
      }
    }
    if (status.isPermanentlyDenied) {
      log.e('Standortberechtigung dauerhaft verweigert.');
      openAppSettings(); // Öffnet App-Einstellungen
      throw Exception('Standortberechtigung dauerhaft verweigert.');
    }

    // Service Check
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      log.w('Standortdienste sind deaktiviert.');
      throw Exception('Standortdienste sind deaktiviert.');
    }

    try {
      log.i('Versuche aktuellen Standort abzurufen...');
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
      );
      log.i(
        'Aktueller Standort erfolgreich abgerufen: ${position.latitude}, ${position.longitude}',
      );
      return position;
    } catch (e, stackTrace) {
      log.e(
        'Fehler beim Abrufen der Position',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  Future<String?> getCityFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    // ... (Komplette Logik aus _LocationService.getCityFromCoordinates einfügen)
    try {
      log.i(
        'Versuche Stadt aus Koordinaten zu ermitteln: $latitude, $longitude',
      );
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
        // localeIdentifier: "de_DE", // Dieser Parameter existiert nicht mehr
      );
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final city = placemark.locality;
        log.i('Stadt gefunden: $city');
        return city;
      } else {
        log.w('Keine Placemarks für die Koordinaten gefunden.');
        return null;
      }
    } catch (e, stackTrace) {
      log.e('Fehler beim Geocoding', error: e, stackTrace: stackTrace);
      return null;
    }
  }
}

class HomeScreen extends ConsumerWidget {
  // Zu ConsumerWidget ändern
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // WidgetRef hinzufügen
    final log = getLogger('HomeScreen');
    final locationHelper = _LocationHelper(); // Instanz des Helpers erstellen

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).appTitle),
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 0, // Passend zum Theme
        // NEU: Gradient hinzufügen
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                Icons.work_outline,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: AppTheme.spacingLarge),
              Text(
                '${AppLocalizations.of(context).welcome} ${AppLocalizations.of(context).appTitle}!',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingMedium),
              Text(
                AppLocalizations.of(context).findJobWithAI,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingXLarge),
              ElevatedButton.icon(
                icon: const Icon(Icons.search),
                label: Text(AppLocalizations.of(context).search),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingXLarge,
                    vertical: AppTheme.spacingMedium,
                  ),
                  textStyle: Theme.of(context).textTheme.labelLarge,
                ),
                onPressed: () {
                  // Navigation zur JobSearchScreen (ohne Standort)
                  ref.read(jobSearchProvider.notifier).applyFilters({
                    'location': '',
                  });
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const JobSearchScreen(),
                    ),
                  );
                },
              ),
              const SizedBox(
                height: AppTheme.spacingMedium,
              ), // Abstand hinzugefügt
              // NEU: Button für Standort-basierte Suche
              ElevatedButton.icon(
                icon: const Icon(Icons.location_on_outlined),
                label: Text(AppLocalizations.of(context).location),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal:
                        AppTheme
                            .spacingLarge, // Etwas weniger Padding als der Hauptbutton
                    vertical: AppTheme.spacingMedium,
                  ),
                  textStyle: Theme.of(context).textTheme.labelLarge,
                  backgroundColor:
                      Theme.of(context)
                          .colorScheme
                          .secondary, // Andere Farbe zur Unterscheidung
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                ),
                onPressed: () async {
                  log.i('Button \'Jobs in meiner Nähe\' geklickt.');
                  final searchNotifier = ref.read(jobSearchProvider.notifier);

                  try {
                    // Zeige Ladeindikator (optional, aber gut für UX)
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(AppLocalizations.of(context).loading),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }

                    final locationData =
                        await locationHelper.getCurrentLocation();
                    if (locationData != null) {
                      final cityName = await locationHelper
                          .getCityFromCoordinates(
                            locationData.latitude,
                            locationData.longitude,
                          );
                      if (cityName != null) {
                        log.i('Standort gefunden: $cityName ($locationData)');
                        // Standort im Notifier setzen
                        searchNotifier.applyFilters({'location': cityName});
                        // Zur Jobsuche navigieren
                        if (context.mounted) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const JobSearchScreen(),
                            ),
                          );
                        }
                      } else {
                        log.w(
                          'Stadt konnte aus Koordinaten nicht ermittelt werden.',
                        );
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(AppLocalizations.of(context).error),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      }
                    } else {
                      log.w('Standort konnte nicht abgerufen werden (null).');
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(AppLocalizations.of(context).error),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    }
                  } catch (e, stackTrace) {
                    log.e(
                      'Fehler beim Abrufen des Standorts:',
                      error: e,
                      stackTrace: stackTrace,
                    );
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(AppLocalizations.of(context).error),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
              ),
              // ENDE NEU
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/premium/screens/subscription_plans_screen.dart';

class PremiumScreen extends ConsumerWidget {
  static const routeName = '/premium';

  const PremiumScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileProvider);

    void navigateToPlansScreen() {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const SubscriptionPlansScreen(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Premium Mitgliedschaft'),
        actions: [
          if (userProfileState.asData?.value.isPremium ?? false)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Chip(
                backgroundColor: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.2),
                label: Text(
                  'Aktiv',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                avatar: Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Hero-Bereich
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingLarge,
                  horizontal: AppTheme.spacingMedium,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                      Theme.of(context).colorScheme.primary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.workspace_premium,
                      size: 64,
                      color: Colors.white,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    Text(
                      'Optimieren Sie Ihre Jobsuche',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    Text(
                      'Vollständiger Zugriff auf alle Premium-Funktionen',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Preisbereich
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.2),
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  child: Column(
                    children: [
                      Text(
                        'Wähle deinen Premium-Plan',
                        style: Theme.of(context).textTheme.titleLarge,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      // Basic Plan
                      _buildPlanPreview(
                        context,
                        title: 'Basic',
                        price: '6,99 €',
                        description: '30 Bewerbungen/Monat, mit Werbung',
                        color: Colors.blue,
                      ),

                      const SizedBox(height: AppTheme.spacingSmall),

                      // Pro Plan
                      _buildPlanPreview(
                        context,
                        title: 'Pro',
                        price: '14,99 €',
                        description: '150 Bewerbungen/Monat, ohne Werbung',
                        color: Colors.purple,
                        isRecommended: true,
                      ),

                      const SizedBox(height: AppTheme.spacingSmall),

                      // Unlimited Plan
                      _buildPlanPreview(
                        context,
                        title: 'Unlimited',
                        price: '29,99 €',
                        description:
                            'Unbegrenzt, 1-Klick, alle Funktionen frei',
                        color: Colors.orange,
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Automatische Verlängerung. Jederzeit kündbar.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),
                      if (!(userProfileState.asData?.value.isPremium ?? false))
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            icon: const Icon(Icons.star),
                            label: const Text('Pläne vergleichen'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: AppTheme.spacingMedium,
                              ),
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor:
                                  Theme.of(context).colorScheme.onPrimary,
                              textStyle: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                            onPressed: navigateToPlansScreen,
                          ),
                        ),
                      if (userProfileState.asData?.value.isPremium ?? false)
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.check_circle),
                            label: const Text('Bereits Premium-Nutzer'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: AppTheme.spacingMedium,
                              ),
                              foregroundColor:
                                  Theme.of(context).colorScheme.primary,
                              side: BorderSide(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            onPressed: null,
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Vorteile
              Text(
                'Alle Premium-Vorteile',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingMedium),

              _buildFeatureTile(
                context,
                Icons.psychology,
                'Unbegrenzte KI-Anschreiben',
                'Generieren Sie so viele personalisierte Bewerbungsanschreiben, wie Sie benötigen, ohne Einschränkungen.',
              ),
              _buildFeatureTile(
                context,
                Icons.auto_awesome,
                'KI-Lebenslauf-Optimierung',
                'Erhalten Sie detaillierte Verbesserungsvorschläge für Ihren Lebenslauf, um mehr Aufmerksamkeit zu erhalten.',
              ),
              _buildFeatureTile(
                context,
                Icons.block,
                'Werbefreie Erfahrung',
                'Nutzen Sie die App ohne Unterbrechungen und Werbung für ein besseres Erlebnis.',
              ),
              _buildFeatureTile(
                context,
                Icons.assistant,
                'KI-Job-Matching',
                'Finden Sie mit fortschrittlichem KI-Matching die perfekten Stellen für Ihre Qualifikationen.',
              ),
              _buildFeatureTile(
                context,
                Icons.insights,
                'Erweiterte Analysen',
                'Tiefere Einblicke in Ihre Bewerbungserfolge und -statistiken (bald verfügbar).',
              ),

              const SizedBox(height: AppTheme.spacingLarge),

              // Datenschutzhinweise
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Text(
                      'Abonnementinformationen',
                      style: Theme.of(context).textTheme.titleSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    Text(
                      'Das Abonnement wird automatisch verlängert, es sei denn, es wird mindestens 24 Stunden vor Ablauf des aktuellen Zeitraums gekündigt. Sie können Ihr Abonnement in den Einstellungen Ihres App-Stores verwalten. Nach der Bestätigung des Kaufs wird Ihr Konto für den Kauf belastet.',
                      style: Theme.of(context).textTheme.bodySmall,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppTheme.spacingMedium),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureTile(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanPreview(
    BuildContext context, {
    required String title,
    required String price,
    required String description,
    required Color color,
    bool isRecommended = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isRecommended ? color : color.withValues(alpha: 0.3),
          width: isRecommended ? 2 : 1,
        ),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: color,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    price,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
          if (isRecommended)
            Positioned(
              top: -8,
              right: -8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Empfohlen',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

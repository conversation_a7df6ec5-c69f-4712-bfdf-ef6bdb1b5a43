import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';

final _isLoadingProvider = StateProvider<bool>((_) => false);

class PremiumComparisonScreen extends ConsumerWidget {
  const PremiumComparisonScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('PremiumComparisonScreen');
    final userProfileState = ref.watch(userProfileProvider);
    final isPremium = userProfileState.asData?.value.isPremium ?? false;
    final paymentService = ref.read(paymentServiceProvider);
    final isLoading = ref.watch(_isLoadingProvider);

    void handlePurchase() async {
      log.i("Premium-Kauf initiiert.");
      ref.read(_isLoadingProvider.notifier).state = true;
      AppDialogs.showLoadingDialog(context, "Verbinde mit Store...");
      
      try {
        await paymentService.buyPremiumSubscription();
        if (context.mounted) Navigator.of(context).pop();
        ref.refresh(userProfileProvider);
        final updatedUserProfile = ref.read(userProfileProvider).asData?.value;
        final purchaseSuccess = updatedUserProfile?.isPremium ?? false;

        if (purchaseSuccess) {
          log.i("Premium-Kauf erfolgreich (verifiziert durch Profil-Update).");
          if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: const Text('Vielen Dank! Ihre Premium-Vorteile sind jetzt aktiv.'),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                  ),
              );
          }
        } else {
          log.w("Premium-Kauf fehlgeschlagen oder abgebrochen (Profil nicht aktualisiert).");
          if (context.mounted) {
              AppDialogs.showErrorDialog(context, "Der Kauf konnte nicht abgeschlossen werden.");
          }
        }
      } catch (e, s) {
        log.e("Fehler während des Premium-Kaufs", error: e, stackTrace: s);
        if (context.mounted) Navigator.of(context).pop();
        if (context.mounted) {
            AppDialogs.showErrorDialog(context, "Ein unerwarteter Fehler ist aufgetreten: ${e.toString()}");
        }
      } finally {
         ref.read(_isLoadingProvider.notifier).state = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Premium Vorteile'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Wechseln Sie zu Premium und nutzen Sie das volle Potenzial:',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            _buildFeatureTile(context, Icons.psychology, 'Unbegrenzte KI-Anschreiben', 'Generieren Sie so viele Bewerbungen, wie Sie benötigen.'),
            _buildFeatureTile(context, Icons.block, 'Werbefreie Erfahrung', 'Nutzen Sie die App ohne Unterbrechungen.'),
            _buildFeatureTile(context, Icons.auto_awesome, 'Erweiterte KI-Analysen', 'Erhalten Sie tiefere Einblicke und Optimierungsvorschläge (bald verfügbar).'),
            const Spacer(),
            if (isPremium)
              Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                     Icon(Icons.check_circle, color: Theme.of(context).colorScheme.primary),
                     const SizedBox(width: AppTheme.spacingSmall),
                     Text(
                       "Sie sind bereits Premium-Nutzer!",
                       style: TextStyle(color: Theme.of(context).colorScheme.primary, fontWeight: FontWeight.bold),
                     ),
                  ],
                ),
              )
            else
              ElevatedButton.icon(
                icon: isLoading 
                    ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)) 
                    : const Icon(Icons.star),
                label: Text(isLoading ? 'Verarbeite...' : 'Premium kaufen'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMedium),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  textStyle: Theme.of(context).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimary),
                ),
                onPressed: isLoading ? null : handlePurchase,
              ),
            const SizedBox(height: AppTheme.spacingMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureTile(BuildContext context, IconData icon, String title, String subtitle) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      subtitle: Text(subtitle),
      contentPadding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSmall),
    );
  }
}

 
 
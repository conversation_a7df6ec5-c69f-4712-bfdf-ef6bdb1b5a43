import 'dart:io';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/presentation/common/widgets/remaining_applications_widget.dart';

/// Screen zur Verwaltung des Premium-Abonnements
class PremiumManagementScreen extends ConsumerStatefulWidget {
  static const routeName = '/premium-management';

  const PremiumManagementScreen({super.key});

  @override
  ConsumerState<PremiumManagementScreen> createState() =>
      _PremiumManagementScreenState();
}

class _PremiumManagementScreenState
    extends ConsumerState<PremiumManagementScreen> {
  final _log = getLogger('PremiumManagementScreen');
  bool _isLoading = false;
  String _selectedPlanType = 'basic';
  DateTime? _expiryDate;

  @override
  void initState() {
    super.initState();
    _loadCurrentSubscription();
  }

  Future<void> _loadCurrentSubscription() async {
    setState(() => _isLoading = true);

    try {
      final userProfileState = ref.read(userProfileProvider);

      if (userProfileState.hasValue) {
        final userProfile = userProfileState.value!;

        // Versuche zuerst premiumExpiryDate, dann premiumExpiry als Fallback
        DateTime? expiryDate = userProfile.premiumExpiryDate;
        if (expiryDate == null && userProfile.premiumExpiry != null) {
          expiryDate = DateTime.tryParse(userProfile.premiumExpiry!);
        }

        setState(() {
          _selectedPlanType = userProfile.premiumPlanType ?? 'basic';
          _expiryDate = expiryDate;
        });
      }
    } catch (e) {
      _log.e('Fehler beim Laden des Abonnements', error: e);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Zeigt einen Dialog zur Bestätigung der Kündigung des Abonnements an
  void _showCancelSubscriptionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Abonnement kündigen'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Möchtest du dein Abonnement wirklich kündigen?',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Dein Abonnement läuft bis zum Ende der aktuellen Abrechnungsperiode weiter. '
                  'Danach wird es nicht mehr verlängert.',
                ),
                const SizedBox(height: 16),
                if (Platform.isAndroid)
                  const Text(
                    'Du wirst zum Google Play Store weitergeleitet, um die Kündigung abzuschließen.',
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
                if (Platform.isIOS)
                  const Text(
                    'Du wirst zu den App Store-Einstellungen weitergeleitet, um die Kündigung abzuschließen.',
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Abbrechen'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _cancelSubscription();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Kündigen'),
              ),
            ],
          ),
    );
  }

  /// Kündigt das Abonnement
  Future<void> _cancelSubscription() async {
    setState(() => _isLoading = true);

    try {
      // Für Android und iOS: Öffne die Abonnementverwaltung im jeweiligen Store
      if (Platform.isAndroid) {
        // Öffne die Google Play Store-Abonnementverwaltung
        final success = await _openGooglePlaySubscriptions();

        if (!success && mounted) {
          AppDialogs.showInfoDialog(
            context,
            'Abonnementverwaltung',
            'Um dein Abonnement zu kündigen, gehe bitte zu:\n'
                '1. Google Play Store\n'
                '2. Profil > Zahlungen & Abos > Abos\n'
                '3. Wähle ProfiAI aus\n'
                '4. Tippe auf "Abo kündigen"',
          );
        }
      } else if (Platform.isIOS) {
        // Öffne die iOS-Abonnementverwaltung
        final success = await _openIOSSubscriptions();

        if (!success && mounted) {
          AppDialogs.showInfoDialog(
            context,
            'Abonnementverwaltung',
            'Um dein Abonnement zu kündigen, gehe bitte zu:\n'
                '1. Einstellungen\n'
                '2. Dein Name > Abonnements\n'
                '3. Wähle ProfiAI aus\n'
                '4. Tippe auf "Abo kündigen"',
          );
        }
      }

      // Aktualisiere auch den Status in der Datenbank
      final subscriptionService = ref.read(
        subscriptionManagementServiceProvider,
      );

      // Markiere das Abonnement als gekündigt in der Datenbank
      final success = await subscriptionService.cancelSubscription();

      if (success) {
        // Invalidiere den Cache des remainingApplicationsProvider
        ref.invalidate(remainingApplicationsProvider);

        // Lade die aktuellen Daten neu
        await _loadCurrentSubscription();

        if (mounted) {
          AppDialogs.showSuccessDialog(
            context,
            'Kündigung eingeleitet',
            'Dein Abonnement wurde erfolgreich gekündigt. Es läuft noch bis zum Ende der aktuellen Abrechnungsperiode.',
          );
        }
      }
    } catch (e) {
      _log.e('Fehler bei der Kündigung des Abonnements', error: e);
      if (mounted) {
        AppDialogs.showErrorDialog(
          context,
          'Ein unerwarteter Fehler ist aufgetreten: $e',
          title: 'Fehler',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Öffnet die Google Play Store-Abonnementverwaltung
  Future<bool> _openGooglePlaySubscriptions() async {
    try {
      // Versuche zuerst, direkt zur Abonnementverwaltung zu navigieren
      final Uri url = Uri.parse(
        'https://play.google.com/store/account/subscriptions',
      );
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        return true;
      }

      // Fallback: Öffne den Play Store
      final Uri playStoreUrl = Uri.parse(
        'market://details?id=com.einsteinai.app',
      );
      if (await canLaunchUrl(playStoreUrl)) {
        await launchUrl(playStoreUrl, mode: LaunchMode.externalApplication);
        return true;
      }

      return false;
    } catch (e) {
      _log.e(
        'Fehler beim Öffnen der Google Play Abonnementverwaltung',
        error: e,
      );
      return false;
    }
  }

  /// Öffnet die iOS-Abonnementverwaltung
  Future<bool> _openIOSSubscriptions() async {
    try {
      // Versuche, die iOS-Abonnementverwaltung zu öffnen
      final Uri url = Uri.parse(
        'itms-apps://apps.apple.com/account/subscriptions',
      );
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        return true;
      }

      // Fallback: Öffne die App Store-Seite der App
      final Uri appStoreUrl = Uri.parse(
        'https://apps.apple.com/account/subscriptions',
      );
      if (await canLaunchUrl(appStoreUrl)) {
        await launchUrl(appStoreUrl, mode: LaunchMode.externalApplication);
        return true;
      }

      return false;
    } catch (e) {
      _log.e('Fehler beim Öffnen der iOS-Abonnementverwaltung', error: e);
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Wir beobachten den UserProfile-Provider, um auf Änderungen zu reagieren
    ref.listen(userProfileProvider, (previous, next) {
      if (next.hasValue && previous?.value != next.value) {
        _loadCurrentSubscription();
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('Premium verwalten')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Aktuelles Abonnement
                    _buildCurrentSubscription(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // Verbleibende Bewerbungen
                    if (_selectedPlanType != 'unlimited') ...[
                      _buildRemainingApplications(),
                      const SizedBox(height: AppTheme.spacingLarge),
                    ],

                    // Preisübersicht
                    _buildPricingOverview(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // Upgrade/Downgrade-Optionen
                    _buildUpgradeOptions(),
                  ],
                ),
              ),
    );
  }

  Widget _buildCurrentSubscription() {
    final planName = _getPlanName(_selectedPlanType);
    final isFreePackage =
        _selectedPlanType.toLowerCase() == 'free' ||
        _selectedPlanType.toLowerCase() == 'basic';

    final expiryDateStr =
        _expiryDate != null
            ? DateFormat('dd.MM.yyyy').format(_expiryDate!)
            : 'Unbekannt';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.workspace_premium,
                  color: _getPlanColor(_selectedPlanType),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Aktuelles Abonnement',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Plan-Name
            Row(
              children: [
                const Text(
                  'Paket:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                Text(
                  planName,
                  style: TextStyle(
                    color: _getPlanColor(_selectedPlanType),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Ablaufdatum oder nächstes Reset-Datum
            if (!isFreePackage)
              // Für Premium-Pakete: Zeige das Ablaufdatum
              Row(
                children: [
                  const Text(
                    'Gültig bis:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 8),
                  Text(expiryDateStr),
                ],
              )
            else
              // Für kostenlose Pakete: Zeige das nächste Reset-Datum
              _buildNextResetDateInfo(),
          ],
        ),
      ),
    );
  }

  /// Baut die Anzeige für das nächste Reset-Datum für kostenlose Bewerbungen
  Widget _buildNextResetDateInfo() {
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        if (nextResetDate == null) {
          return const Row(
            children: [
              Text(
                'Nächste Bewerbungen:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(width: 8),
              Text('Wöchentlich'),
            ],
          );
        }

        // Berechne die verbleibende Zeit bis zum nächsten Reset
        final now = DateTime.now();
        final difference = nextResetDate.difference(now);

        // Wenn das Datum in der Vergangenheit liegt, zeige den Standard-Text an
        if (difference.isNegative) {
          return const Row(
            children: [
              Text(
                'Nächste Bewerbungen:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(width: 8),
              Text('Wöchentlich'),
            ],
          );
        }

        // Formatiere die verbleibende Zeit
        final days = difference.inDays;
        final hours = difference.inHours % 24;
        final dateFormat = DateFormat('dd.MM.yyyy');

        String timeText;
        if (days > 0) {
          timeText = '$days Tag${days > 1 ? 'e' : ''} $hours Std.';
        } else {
          timeText = '$hours Std. ${difference.inMinutes % 60} Min.';
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Nächste Bewerbungen:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                Text('In $timeText'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Text(
                  'Freischaltung:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                Text(dateFormat.format(nextResetDate)),
              ],
            ),
          ],
        );
      },
      loading:
          () => const Row(
            children: [
              Text(
                'Nächste Bewerbungen:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(width: 8),
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ],
          ),
      error:
          (_, __) => const Row(
            children: [
              Text(
                'Nächste Bewerbungen:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(width: 8),
              Text('Wöchentlich'),
            ],
          ),
    );
  }

  Widget _buildRemainingApplications() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.description, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Verbleibende Bewerbungen',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Verbleibende Bewerbungen
            const RemainingApplicationsWidget(showDetailed: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingOverview() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.euro, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Preisübersicht',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Free-Plan
            _buildPricingItem(
              'Free',
              'Kostenlos',
              '5 Bewerbungen pro Woche',
              'Mit Werbung',
              Colors.blue,
            ),
            const SizedBox(height: AppTheme.spacingSmall),

            // Pro-Plan
            _buildPricingItem(
              'Pro',
              '19,99 €/Monat',
              '150 Bewerbungen pro Monat',
              'Ohne Werbung',
              Colors.purple,
            ),
            const SizedBox(height: AppTheme.spacingSmall),

            // Unlimited-Plan
            _buildPricingItem(
              'Unlimited',
              '39,99 €/Monat',
              'Unbegrenzte Bewerbungen',
              'Ohne Werbung + Personalisierter Stil',
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingItem(
    String planName,
    String price,
    String applications,
    String adStatus,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        border: Border.all(color: color.withAlpha(128)),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(51),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.workspace_premium, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  planName,
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
                Text(price),
                Text(applications, style: const TextStyle(fontSize: 12)),
                Text(adStatus, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradeOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.upgrade, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Abonnement ändern',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Upgrade-Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showUpgradeDialog(),
                icon: const Icon(Icons.arrow_upward),
                label: const Text('Abonnement upgraden'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),

            const SizedBox(height: AppTheme.spacingMedium),

            // Kündigen-Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showCancelSubscriptionDialog(),
                icon: const Icon(Icons.cancel),
                label: const Text('Abonnement kündigen'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showUpgradeDialog() {
    // Bestimme verfügbare Upgrade-Optionen basierend auf aktuellem Plan
    List<String> upgradeOptions = [];
    if (_selectedPlanType == 'free' || _selectedPlanType == 'basic') {
      upgradeOptions = ['pro', 'unlimited'];
    } else if (_selectedPlanType == 'pro') {
      upgradeOptions = ['unlimited'];
    } else {
      // Bereits im höchsten Plan
      AppDialogs.showInfoDialog(
        context,
        'Kein Upgrade möglich',
        'Du nutzt bereits das höchste verfügbare Abonnement.',
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => _buildPlanSelectionDialog(
            'Abonnement upgraden',
            'Wähle dein neues Abonnement:',
            upgradeOptions,
            (newPlan) => _changePlan(newPlan, isUpgrade: true),
          ),
    );
  }

  Widget _buildPlanSelectionDialog(
    String title,
    String message,
    List<String> planOptions,
    Function(String) onPlanSelected,
  ) {
    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(message),
          const SizedBox(height: 16),
          ...planOptions.map(
            (plan) => ListTile(
              title: Text(_getPlanName(plan)),
              leading: Icon(
                Icons.workspace_premium,
                color: _getPlanColor(plan),
              ),
              onTap: () {
                Navigator.of(context).pop();
                onPlanSelected(plan);
              },
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Abbrechen'),
        ),
      ],
    );
  }

  Future<void> _changePlan(String newPlan, {required bool isUpgrade}) async {
    setState(() => _isLoading = true);

    try {
      // Verwende den PaymentService für den Kauf
      final paymentService = ref.read(paymentServiceProvider);

      // Starte den Kaufprozess mit dem Google Play Store
      final success = await paymentService.buyPremiumSubscription(
        planType: newPlan,
      );

      if (success) {
        // Invalidiere den Cache des remainingApplicationsProvider
        ref.invalidate(remainingApplicationsProvider);

        // Lade die aktuellen Daten neu
        await _loadCurrentSubscription();

        if (mounted) {
          AppDialogs.showSuccessDialog(
            context,
            'Upgrade erfolgreich',
            'Dein Abonnement wurde erfolgreich auf ${_getPlanName(newPlan)} geändert.',
          );
        }
      } else {
        if (mounted) {
          AppDialogs.showErrorDialog(
            context,
            'Es ist ein Fehler aufgetreten. Bitte versuche es später erneut.',
            title: 'Änderung fehlgeschlagen',
          );
        }
      }
    } catch (e) {
      _log.e('Fehler beim Ändern des Abonnements', error: e);
      if (mounted) {
        AppDialogs.showErrorDialog(
          context,
          'Ein unerwarteter Fehler ist aufgetreten: $e',
          title: 'Fehler',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _getPlanName(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return 'Free';
      case 'basic': // Für Abwärtskompatibilität
        return 'Free';
      case 'pro':
        return 'Pro';
      case 'unlimited':
        return 'Unlimited';
      default:
        return 'Unbekannt';
    }
  }

  Color _getPlanColor(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return Colors.blue;
      case 'basic': // Für Abwärtskompatibilität
        return Colors.blue;
      case 'pro':
        return Colors.purple;
      case 'unlimited':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart'; // Supabase importieren
import 'package:flutter/foundation.dart' show kIsWeb; // <--- NEUER IMPORT
import 'package:flutter_svg/flutter_svg.dart'; // Für SVG-Icons
import 'forgot_password_screen.dart'; // Behalten für Passwort vergessen
import 'package:ki_test/src/core/utils/logging.dart'; // Für Logger
// Für SharedPreferences
import 'package:go_router/go_router.dart'; // Für Navigation
import 'package:google_sign_in/google_sign_in.dart'; // Für direktes Google Sign-In
import 'package:ki_test/src/application/providers/services_providers.dart'; // Für subscriptionManagementServiceProvider
import 'package:ki_test/src/application/providers/auto_onboarding_provider.dart'; // Für automatische Onboarding-Prüfung
import 'package:ki_test/src/core/utils/auth_error_handler.dart'; // Für verbesserte Fehlerbehandlung

// Provider für Ladezustände
final isSubmittingProvider = StateProvider<bool>((ref) => false);
final isSubmittingGoogleProvider = StateProvider<bool>((ref) => false);

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  // --- Supabase E-Mail/Passwort Anmelden ---
  Future<void> _signInWithEmailPassword(
    BuildContext context,
    WidgetRef ref,
    String email,
    String password,
  ) async {
    if (email.isEmpty || password.isEmpty) {
      showErrorSnackBar(context, 'Bitte E-Mail und Passwort eingeben.');
      return;
    }
    ref.read(isSubmittingProvider.notifier).state = true;
    final supabase = Supabase.instance.client;

    try {
      final AuthResponse res = await supabase.auth.signInWithPassword(
        email: email.trim(),
        password: password.trim(),
      );

      debugPrint(
        "[LoginScreen] Erfolgreich mit E-Mail/Passwort angemeldet: ${res.user?.email}",
      );

      // Starte AutoOnboardingService für lokale Onboarding-Prüfung
      if (context.mounted) {
        final autoOnboardingService = ref.read(autoOnboardingProvider);
        await autoOnboardingService.checkOnboardingAfterLogin(context);
      }
      debugPrint("E-Mail/Passwort-Anmeldung erfolgreich - AutoOnboardingService gestartet");
    } on AuthException catch (e) {
      debugPrint(
        "[LoginScreen] Fehler bei E-Mail/Passwort Anmeldung: ${e.message}",
      );
      if (context.mounted) {
        AuthErrorHandler.showErrorSnackBar(context, e);
      }
    } catch (e) {
      debugPrint(
        "[LoginScreen] Unerwarteter Fehler bei E-Mail/Passwort Anmeldung: $e",
      );
      if (context.mounted) {
        AuthErrorHandler.showGenericErrorSnackBar(
          context,
          AuthErrorMessages.unexpectedError,
        );
      }
    } finally {
      // Stelle sicher, dass der Ladezustand zurückgesetzt wird, auch wenn der Screen nicht mehr mounted ist
      if (context.mounted) {
        ref.read(isSubmittingProvider.notifier).state = false;
      }
    }
  }

  // --- Supabase OAuth Anmelden ---
  Future<void> _signInWithProvider(
    BuildContext context,
    WidgetRef ref,
    OAuthProvider provider,
  ) async {
    final loadingProvider =
        provider == OAuthProvider.google
            ? isSubmittingGoogleProvider
            : isSubmittingGoogleProvider;

    ref.read(loadingProvider.notifier).state = true;
    final supabase = Supabase.instance.client;
    final log = getLogger('LoginScreen');

    try {
      // Für Google verwenden wir die native Anmeldung auf mobilen Geräten
      if (provider == OAuthProvider.google && !kIsWeb) {
        try {
          log.i("Starte Google Sign-In mit direkter Methode...");

          // Stelle sicher, dass keine alte Anmeldung vorhanden ist
          final GoogleSignIn googleSignIn = GoogleSignIn(
            scopes: ['email', 'profile'],
            // Deaktiviere die automatische Auswahl des letzten Accounts
            signInOption: SignInOption.standard,
            // Erzwinge die Anzeige des Account-Auswahldialogs
            forceCodeForRefreshToken: true,
          );

          // Versuche, den aktuellen Benutzer abzumelden, falls vorhanden
          try {
            if (googleSignIn.currentUser != null) {
              log.i("Vorheriger Google-Benutzer gefunden, melde ab...");
              await googleSignIn.signOut();
              log.i("Vorheriger Google-Benutzer abgemeldet");
            }

            // Zusätzlich: Lösche alle gespeicherten Anmeldedaten
            await googleSignIn.disconnect();
            log.i("Google-Anmeldedaten zurückgesetzt");
          } catch (signOutError) {
            log.w(
              "Fehler beim Abmelden des vorherigen Google-Benutzers: $signOutError",
            );
            // Wir fahren trotzdem fort
          }

          log.i("GoogleSignIn initialisiert, versuche Anmeldung...");
          // Erzwinge die Anzeige des Account-Auswahldialogs
          final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

          if (googleUser == null) {
            log.w("Google Sign-In wurde abgebrochen");
            if (context.mounted) {
              ref.read(isSubmittingGoogleProvider.notifier).state = false;
              ref.read(loadingProvider.notifier).state = false;
            }
            return;
          }

          log.i("Google-Konto ausgewählt: ${googleUser.email}");
          log.i("Melde bei Supabase an...");

          // Melde bei Supabase an
          log.i("Versuche Anmeldung mit Google ID-Token...");

          // Zeige Erfolgsmeldung an
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text("Anmeldung wird durchgeführt..."),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }

          try {
            // Verwende die Google-Authentifizierung mit Supabase
            log.i("Verwende Google-Authentifizierung mit Supabase...");

            // Hole die Auth-Details von Google
            final GoogleSignInAuthentication googleAuth =
                await googleUser.authentication;

            // Verwende den ID-Token für die Anmeldung bei Supabase
            log.i("Versuche Anmeldung mit Google ID-Token...");
            AuthResponse response;

            // Verwende signInWithIdToken für echte OAuth-Integration
            response = await supabase.auth.signInWithIdToken(
              provider: OAuthProvider.google,
              idToken: googleAuth.idToken!,
              accessToken: googleAuth.accessToken,
            );

            log.i("Google OAuth-Anmeldung erfolgreich");

            // Speichere zusätzliche Benutzerinformationen in der Supabase-Datenbank
            if (response.user != null) {
              try {
                log.i("Aktualisiere Benutzerprofil in der profiles-Tabelle...");
                await supabase.from('profiles').upsert({
                  'id': response.user!.id,
                  'data': {
                    'email': googleUser.email,
                    'display_name':
                        googleUser.displayName ??
                        googleUser.email.split('@').first,
                    'avatar_url': googleUser.photoUrl,
                    'provider': 'google',
                  },
                  'updated_at': DateTime.now().toIso8601String(),
                });
                log.i("Benutzerprofil erfolgreich aktualisiert");
              } catch (profileError) {
                log.w(
                  "Fehler beim Aktualisieren des Benutzerprofils: $profileError",
                );
                // Das ist kein kritischer Fehler, die Anmeldung war trotzdem erfolgreich
              }
            }

            if (response.user != null) {
              log.i("Supabase Anmeldung erfolgreich: ${response.user?.email}");
            log.i("🎯 DEBUG: Login-Screen Code erreicht für Google-Anmeldung");

              // Initialisiere den Bewerbungszähler für den neuen Benutzer
              try {
                try {
                  log.i("Initialisiere Bewerbungszähler für neuen Benutzer...");
                  final subscriptionService = ref.read(
                    subscriptionManagementServiceProvider,
                  );
                  final result = await subscriptionService.syncSubscription();
                  if (result) {
                    log.i("Bewerbungszähler erfolgreich initialisiert");
                  } else {
                    log.w("Fehler beim Initialisieren des Bewerbungszählers");
                  }
                } catch (counterError) {
                  log.w(
                    "Fehler beim Initialisieren des Bewerbungszählers: $counterError",
                  );
                  // Das ist kein kritischer Fehler, die Anmeldung war trotzdem erfolgreich
                }
              } catch (counterError) {
                log.w("Fehler beim Initialisieren des Bewerbungszählers: $counterError");
                // Das ist kein kritischer Fehler, die Anmeldung war trotzdem erfolgreich
              }

              // Navigiere zur Hauptseite nach erfolgreicher Anmeldung
              if (context.mounted) {
                // Zeige Erfolgsmeldung an
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      "Anmeldung erfolgreich! Die App wird neu gestartet...",
                    ),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );



                // Aktualisiere den Supabase-Authentifizierungszustand
                try {
                  // Setze die Supabase-Session manuell
                  final session = response.session;
                  if (session != null) {
                    // Speichere die Session in Supabase
                    // Wir können die Session nicht direkt setzen, aber wir können die App neu starten
                    log.i(
                      "Supabase-Session ist vorhanden, Anmeldung erfolgreich",
                    );
                  }
                } catch (sessionError) {
                  log.w(
                    "Fehler beim Aktualisieren der Supabase-Session: $sessionError",
                  );
                  // Das ist kein kritischer Fehler, die Anmeldung war trotzdem erfolgreich
                }

                // LOKALE ONBOARDING-PRÜFUNG MIT AUTOONBOARDINGSERVICE
                log.i("🎯 DEBUG: Login-Screen Code erreicht für Google-Anmeldung");
                log.i("STARTE AUTOONBOARDINGSERVICE FÜR LOKALE ONBOARDING-PRÜFUNG");

                // Starte AutoOnboardingService für lokale Onboarding-Prüfung
                if (context.mounted) {
                  log.i("🚀 AUTOONBOARDINGSERVICE: Starte lokale Onboarding-Prüfung");
                  final autoOnboardingService = ref.read(autoOnboardingProvider);
                  await autoOnboardingService.checkOnboardingAfterLogin(context);
                  log.i("✅ AUTOONBOARDINGSERVICE: Lokale Onboarding-Prüfung abgeschlossen");
                }
              }
            } else {
              log.w("Supabase Anmeldung fehlgeschlagen");
              if (context.mounted) {
                showErrorSnackBar(
                  context,
                  'Fehler bei der Anmeldung. Bitte versuche es später erneut.',
                );
                ref.read(isSubmittingGoogleProvider.notifier).state = false;
                ref.read(loadingProvider.notifier).state = false;
              }
            }
          } catch (supabaseError) {
            log.e("Fehler bei Supabase Anmeldung: $supabaseError");
            if (context.mounted) {
              showErrorSnackBar(
                context,
                'Fehler bei der Anmeldung mit Supabase: $supabaseError',
              );
              ref.read(isSubmittingGoogleProvider.notifier).state = false;
              ref.read(loadingProvider.notifier).state = false;
            }
          }
        } catch (e) {
          log.e("Fehler bei Google Sign-In: $e");
          if (context.mounted) {
            showErrorSnackBar(context, "Fehler bei der Google-Anmeldung: $e");
            ref.read(isSubmittingGoogleProvider.notifier).state = false;
            ref.read(loadingProvider.notifier).state = false;
          }
        }
        return;
      }

      // Für OAuth-Provider auf Web verwenden wir den Standard-OAuth-Flow
      await supabase.auth.signInWithOAuth(
        provider,
        // Optional: Führe den Redirect auf eine bestimmte URL in deiner App durch
        redirectTo: kIsWeb ? null : 'com.einsteinai.app://login-callback',
      );
      // Nach dem Redirect wird der Listener in main.dart die neue Session erkennen und navigieren.
      log.i("OAuth Flow für ${provider.toString()} gestartet...");
    } on AuthException catch (e) {
      debugPrint(
        "[LoginScreen] Fehler bei OAuth (${provider.toString()}): ${e.message}",
      );
      if (context.mounted) {
        showErrorSnackBar(
          context,
          'Fehler bei ${provider.toString()} Anmeldung: ${e.message}',
        );
      }
      if (context.mounted) {
        ref
            .read(
              provider == OAuthProvider.google
                  ? isSubmittingGoogleProvider.notifier
                  : isSubmittingGoogleProvider.notifier,
            )
            .state = false;
      }
    } catch (e) {
      debugPrint(
        "[LoginScreen] Unerwarteter Fehler bei OAuth (${provider.toString()}): $e",
      );
      // ignore: use_build_context_synchronously
      showErrorSnackBar(context, 'Ein unerwarteter Fehler ist aufgetreten.');
      if (context.mounted) {
        ref
            .read(
              provider == OAuthProvider.google
                  ? isSubmittingGoogleProvider.notifier
                  : isSubmittingGoogleProvider.notifier,
            )
            .state = false;
      }
    }
    // Hinweis: Ladezustand wird erst zurückgesetzt, wenn der User zur App zurückkehrt oder ein Fehler auftritt.
    // Im Erfolgsfall navigiert der Listener in main.dart weg.
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isPasswordVisible = useState(false);
    final isSubmitting = ref.watch(isSubmittingProvider);
    final isSubmittingGoogle = ref.watch(isSubmittingGoogleProvider);

    // Prüfe URL-Parameter für vorausgefüllte E-Mail
    useEffect(() {
      final uri = GoRouter.of(context).routeInformationProvider.value.uri;
      final emailParam = uri.queryParameters['email'];
      if (emailParam != null && emailParam.isNotEmpty) {
        emailController.text = emailParam;
      }
      return null;
    }, []);
    // Funktion für Google Login mit Supabase
    Future<void> handleGoogleSignIn(BuildContext context) async {
      // Direkt zum OAuth-Provider weiterleiten für Google
      _signInWithProvider(context, ref, OAuthProvider.google);
    }

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(30.0),
            child: Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo oder Titel
                  Text(
                    'Willkommen zurück!',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Melde dich an oder registriere dich.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 40),

                  // E-Mail Feld
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'E-Mail',
                      prefixIcon: Icon(Icons.email_outlined),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null ||
                          value.isEmpty ||
                          !value.contains('@')) {
                        return 'Bitte gib eine gültige E-Mail ein.';
                      }
                      return null;
                    },
                    textInputAction: TextInputAction.next,
                  ),
                  const SizedBox(height: 16),

                  // Passwort Feld
                  TextFormField(
                    controller: passwordController,
                    obscureText: !isPasswordVisible.value,
                    decoration: InputDecoration(
                      labelText: 'Passwort',
                      prefixIcon: const Icon(Icons.lock_outline),
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          isPasswordVisible.value
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                        ),
                        onPressed:
                            () =>
                                isPasswordVisible.value =
                                    !isPasswordVisible.value,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte gib dein Passwort ein.';
                      }
                      return null;
                    },
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) {
                      if (formKey.currentState!.validate() && !isSubmitting) {
                        _signInWithEmailPassword(
                          context,
                          ref,
                          emailController.text,
                          passwordController.text,
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 8),

                  // Passwort vergessen
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed:
                          isSubmitting
                              ? null
                              : () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) =>
                                            const ForgotPasswordScreen(),
                                  ), // Muss auch auf Supabase angepasst werden
                                );
                              },
                      child: const Text('Passwort vergessen?'),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Anmelden Button
                  ElevatedButton(
                    onPressed:
                        isSubmitting || isSubmittingGoogle
                            ? null
                            : () {
                              if (formKey.currentState!.validate()) {
                                _signInWithEmailPassword(
                                  context,
                                  ref,
                                  emailController.text,
                                  passwordController.text,
                                );
                              }
                            },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        isSubmitting
                            ? const SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Text('Anmelden'),
                  ),
                  const SizedBox(height: 16),

                  // Registrieren Button/Text
                  Align(
                    alignment: Alignment.center,
                    child: TextButton(
                      onPressed:
                          isSubmitting || isSubmittingGoogle
                              ? null
                              : () {
                                // Navigiere zur Registrierungsseite
                                context.go('/register');
                              },
                      child: const Text('Noch kein Konto? Jetzt registrieren'),
                    ),
                  ),

                  const SizedBox(height: 30),
                  Row(
                    children: <Widget>[
                      const Expanded(child: Divider()),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Text(
                          'Oder anmelden mit',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                      const Expanded(child: Divider()),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Google Button
                  Center(
                    child: ElevatedButton.icon(
                      icon:
                          isSubmittingGoogle
                              ? const SizedBox(
                                height: 18,
                                width: 18,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : SvgPicture.asset(
                                '_resources/assets/icons/google_logo.svg',
                                height: 24.0,
                                width: 24.0,
                              ), // Google Logo als SVG
                      label: const Text('Google'),
                      onPressed:
                          isSubmitting || isSubmittingGoogle
                              ? null
                              : () async {
                                await handleGoogleSignIn(context);
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        foregroundColor:
                            Theme.of(context).colorScheme.onSurface,
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Hilfsfunktionen für Dialoge und Snackbars (Beispiel)
void showErrorSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Theme.of(context).colorScheme.error,
    ),
  );
}

void showInfoDialog(BuildContext context, String title, String content) {
  if (!context.mounted) return;
  showDialog(
    context: context,
    builder:
        (context) => AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              child: const Text('OK'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
  );
}

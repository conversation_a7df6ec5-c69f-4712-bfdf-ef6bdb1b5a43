import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart'; // Supabase importieren
import 'package:ki_test/src/core/l10n/app_localizations.dart'; // Für Lokalisierung

// Provider für Ladezustand
final isSubmittingForgotPasswordProvider = StateProvider<bool>((ref) => false);

class ForgotPasswordScreen extends HookConsumerWidget {
  const ForgotPasswordScreen({super.key});

  // --- Supabase Passwort Reset ---
  Future<void> _sendPasswordResetEmail(
    BuildContext context,
    WidgetRef ref,
    String email,
  ) async {
    if (email.isEmpty ||
        !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      showErrorSnackBar(context, 'Bitte gib eine gültige E-Mail-Adresse ein.');
      return;
    }
    ref.read(isSubmittingForgotPasswordProvider.notifier).state = true;
    final supabase = Supabase.instance.client;

    try {
      // TODO: Konfiguriere die Redirect URL in deinen Supabase Auth Settings!
      // Die App muss so konfiguriert sein, dass sie diesen Link abfängt (Deep Linking).
      // Beispiel: 'io.supabase.flutterquickstart://password-reset/'
      // Ohne redirectTo funktioniert der Link möglicherweise nicht korrekt auf Mobilgeräten.
      await supabase.auth.resetPasswordForEmail(
        email.trim(),
        // redirectTo: kIsWeb ? null : 'YOUR_APP_SCHEME://password-reset/', // <-- WICHTIG für Mobile
      );

      debugPrint(
        "[ForgotPasswordScreen] Passwort-Reset-E-Mail angefordert für: $email",
      );
      if (context.mounted) {
        // Zeige Erfolgsmeldung
        ref.read(submissionSuccessProvider.notifier).state = true;
        ref.read(submissionErrorProvider.notifier).state = null;
      }
    } on AuthException catch (e) {
      debugPrint(
        "[ForgotPasswordScreen] Fehler beim Anfordern des Passwort-Resets: ${e.message}",
      );
      if (context.mounted) {
        ref.read(submissionErrorProvider.notifier).state =
            'Fehler: ${e.message}';
        ref.read(submissionSuccessProvider.notifier).state = false;
      }
    } catch (e) {
      debugPrint(
        "[ForgotPasswordScreen] Unerwarteter Fehler beim Passwort-Reset: $e",
      );
      if (context.mounted) {
        ref.read(submissionErrorProvider.notifier).state =
            'Ein unerwarteter Fehler ist aufgetreten.';
        ref.read(submissionSuccessProvider.notifier).state = false;
      }
    } finally {
      if (context.mounted) {
        ref.read(isSubmittingForgotPasswordProvider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isSubmitting = ref.watch(isSubmittingForgotPasswordProvider);
    // Verwende separate Provider für Erfolg/Fehler, um Rebuilds zu steuern
    final submissionError = ref.watch(submissionErrorProvider);
    final submissionSuccess = ref.watch(submissionSuccessProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Passwort zurücksetzen'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        // Stelle sicher, dass die AppBar-Farbe zum Theme passt
        foregroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(30.0), // Angepasstes Padding
            child: Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Icon oder Illustration
                  Center(
                    child: Icon(
                      Icons.lock_reset_outlined,
                      size: 80,
                      color:
                          Theme.of(
                            context,
                          ).colorScheme.primary, // Dynamische Farbe
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Anleitungstext
                  Text(
                    'Passwort vergessen?',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Gib deine E-Mail-Adresse ein. Wir senden dir einen Link zum Zurücksetzen deines Passworts.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 32),

                  // Erfolgsmeldung
                  if (submissionSuccess)
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.green.withValues(alpha: 0.3),
                        ),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.check_circle_outline, color: Colors.green),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'E-Mail zum Zurücksetzen gesendet! Bitte prüfe deinen Posteingang.',
                              style: TextStyle(color: Colors.green),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Fehlermeldung
                  if (submissionError != null)
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.error.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              submissionError,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                          ),
                          // Optional: Schließen-Button für Fehlermeldung
                          /* IconButton(
                            icon: Icon(Icons.close, color: Theme.of(context).colorScheme.error),
                            onPressed: () => ref.read(submissionErrorProvider.notifier).state = null,
                          ), */
                        ],
                      ),
                    ),

                  // E-Mail Feld
                  TextFormField(
                    controller: emailController,
                    enabled: !submissionSuccess, // Deaktivieren nach Erfolg
                    decoration: const InputDecoration(
                      labelText: 'E-Mail',
                      prefixIcon: Icon(Icons.email_outlined),
                      border: OutlineInputBorder(), // Standard Border
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Bitte gib deine E-Mail ein';
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'Bitte gib eine gültige E-Mail ein';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // Senden Button
                  ElevatedButton(
                    onPressed:
                        isSubmitting || submissionSuccess
                            ? null
                            : () {
                              if (formKey.currentState!.validate()) {
                                // Setze Fehler/Erfolg zurück, bevor neuer Versuch startet
                                ref
                                    .read(submissionErrorProvider.notifier)
                                    .state = null;
                                ref
                                    .read(submissionSuccessProvider.notifier)
                                    .state = false;
                                _sendPasswordResetEmail(
                                  context,
                                  ref,
                                  emailController.text,
                                );
                              }
                            },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        isSubmitting
                            ? const SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Text('Passwort-Reset anfordern'),
                  ),
                  const SizedBox(height: 16),
                  // Zurück zum Login Button
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(AppLocalizations.of(context).backToLogin),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Zusätzliche Provider für Fehler/Erfolg Status
final submissionErrorProvider = StateProvider<String?>((ref) => null);
final submissionSuccessProvider = StateProvider<bool>((ref) => false);

// --- HIER EINGEFÜGT: Hilfsfunktionen ---
void showErrorSnackBar(BuildContext context, String message) {
  if (!context.mounted) return;
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Theme.of(context).colorScheme.error,
    ),
  );
}

void showInfoDialog(BuildContext context, String title, String content) {
  if (!context.mounted) return;
  showDialog(
    context: context,
    builder:
        (context) => AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              child: const Text('OK'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
  );
}

// --- Ende Hilfsfunktionen ---

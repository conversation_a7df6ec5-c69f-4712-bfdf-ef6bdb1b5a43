import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class ErrorMessage extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const ErrorMessage({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(color: AppTheme.errorColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(Icons.error_outline, color: AppTheme.errorColor),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Text(
                  message.replaceFirst('Exception: ', ''), // Entferne "Exception: " Präfix
                  style: const TextStyle(color: AppTheme.errorColor),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          if (onRetry != null)
            Padding(
              padding: const EdgeInsets.only(top: AppTheme.spacingMedium),
              child: TextButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text('Erneut versuchen'),
                onPressed: onRetry,
                style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
              ),
            ),
        ],
      ),
    );
  }
}
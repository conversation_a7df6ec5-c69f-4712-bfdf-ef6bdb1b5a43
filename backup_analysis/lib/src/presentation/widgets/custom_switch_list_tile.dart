import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein angepasstes SwitchListTile mit einheitlichem Styling
class CustomSwitchListTile extends StatelessWidget {
  final String title;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final IconData? icon;

  const CustomSwitchListTile({
    super.key,
    required this.title,
    required this.value,
    this.onChanged,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(12),
      ),
      child: SwitchListTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: const Color(0xFFDDDDDD),
            fontSize: 15,
            fontWeight: FontWeight.w400,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryLightColor,
        activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.5),
        inactiveThumbColor: Colors.grey[400],
        inactiveTrackColor: Colors.grey[800],
        secondary: icon != null 
          ? Icon(
              icon,
              color: Theme.of(context).inputDecorationTheme.prefixIconColor,
              size: 20,
            )
          : null,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 4,
        ),
      ),
    );
  }
} 
import 'package:flutter/material.dart';

/// Ein angepasstes Textfeld mit einheitlichem Styling
class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final IconData? icon;
  final bool enabled;
  final int? maxLines;
  final TextInputType? keyboardType;
  final bool obscureText;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final bool autofocus;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    this.icon,
    this.enabled = true,
    this.maxLines = 1,
    this.keyboardType,
    this.obscureText = false,
    this.validator,
    this.onChanged,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        controller: controller,
        enabled: enabled,
        maxLines: maxLines,
        keyboardType: keyboardType,
        obscureText: obscureText,
        onChanged: onChanged,
        autofocus: autofocus,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: Theme.of(context).textTheme.labelMedium?.copyWith(
            color: const Color(0xFFCCCCCC),
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
          floatingLabelStyle: const TextStyle(color: Color(0xFF9B59B6)),
          prefixIcon:
              icon != null
                  ? Padding(
                    padding: const EdgeInsets.only(left: 12.0, right: 8.0),
                    child: Icon(
                      icon,
                      color:
                          Theme.of(
                            context,
                          ).inputDecorationTheme.prefixIconColor,
                      size: 20,
                    ),
                  )
                  : const SizedBox(width: 12 + 8 + 20),
          prefixIconConstraints: const BoxConstraints(minHeight: 44),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.only(
            left: icon == null ? 12 : 0,
            right: 12,
            top: 12,
            bottom: 12,
          ),
          isDense: true,
        ),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: const Color(0xFFDDDDDD),
          fontSize: 15,
          fontWeight: FontWeight.w400,
        ),
        textAlignVertical: TextAlignVertical.center,
      ),
    );
  }
}

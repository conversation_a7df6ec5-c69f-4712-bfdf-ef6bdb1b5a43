import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../core/theme/app_theme.dart';
// Importiere AppShell aus main.dart
// NEU: Importiere Onboarding Provider und Screen
// Import entfernt - wird bereits über andere Datei importiert
import '../../application/providers/user_profile_provider.dart'; // NEU: Import für User Profil
import '../../domain/models/user_profile.dart'; // NEU: Import für UserProfile Model
import 'package:go_router/go_router.dart'; // NEU: Import für direkte Navigation
import '../../core/utils/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:shared_preferences/shared_preferences.dart'; // Für SharedPreferences
import '../../application/providers/auto_onboarding_provider.dart'; // Für AutoOnboardingService
import '../../application/providers/onboarding_provider.dart'; // Für OnboardingProvider
import '../../application/services/bulk_text_extraction_service.dart'; // Für automatische Text-Extraktion
import '../../application/services/text_extraction_service.dart'; // Für Text-Extraktion
import '../../application/providers/services_providers.dart'; // Für Supabase Client

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('SplashScreen');
    // Onboarding-Status abfragen
    final bool onboardingCompleted = ref.watch(onboardingProvider);

    // Animation Controller für die Animationen
    final tickerProvider = useSingleTickerProvider();
    final animationController = useMemoized(() {
      return AnimationController(
        vsync: tickerProvider,
        duration: const Duration(milliseconds: 400), // Minimale Animation
      );
    }, [tickerProvider]);

    // Starte die Animation einmal und kümmere dich um die Entsorgung
    useEffect(() {
      // Starte die Animation nur, wenn sie noch nicht läuft
      if (!animationController.isAnimating &&
          animationController.status != AnimationStatus.completed) {
        animationController.forward();
      }

      // Minimale Verzögerung für die Navigation (0,5 Sekunden)
      Future.delayed(const Duration(milliseconds: 500), () async {
        if (!context.mounted) return; // Sicherheitscheck

        if (kDebugMode) {
          print("Navigation wird ausgelöst nach Verzögerung");
        }

        try {
          // Prüfen, ob der Benutzer angemeldet ist
          final session = Supabase.instance.client.auth.currentSession;

          if (session != null) {
            // Benutzer ist angemeldet
            if (kDebugMode) {
              print(
                "Benutzer ist angemeldet, navigiere zum Hauptbildschirm oder Onboarding",
              );
            }

            // VERBESSERTE IMPLEMENTIERUNG: Warte auf das Laden des Profils und setze Premium auf false
            try {
              // Markiere den Benutzer als bestehenden Account, aber überschreibe nicht den Onboarding-Status
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool('has_existing_account', true);

              // Prüfe den Onboarding-Status in Supabase, ohne ihn zu überschreiben
              // Der OnboardingNotifier wird den Status aus Supabase laden
              log.i("Prüfe Onboarding-Status in Supabase...");

              // Aktiv auf das Laden des Profils warten
              log.i("Warte auf Laden des UserProfils...");

              // Beobachte den userProfileProvider, bis er geladen ist
              final profileStateAsync = ref.read(userProfileProvider);
              if (profileStateAsync is AsyncLoading) {
                log.i("Profil wird geladen, warte auf Abschluss...");
                // Warte auf eine kurze Zeit, damit das Profil geladen werden kann
                await Future.delayed(const Duration(milliseconds: 500));
              }

              // Erneut versuchen, das Profil zu lesen
              final profileState = ref.read(userProfileProvider);

              if (profileState is AsyncData<UserProfile>) {
                final userProfile = profileState.value;
                log.i(
                  "UserProfil erfolgreich geladen für Benutzer ${userProfile.id}",
                );

                // Premium-Status aktualisieren, unabhängig vom aktuellen Wert
                log.i(
                  "Setze Premium-Status auf false für Benutzer ${userProfile.id}",
                );
                await ref
                    .read(userProfileProvider.notifier)
                    .updatePremiumStatus(isPremium: false);
                log.i("Premium-Status erfolgreich auf false gesetzt");
              } else if (profileState is AsyncLoading) {
                log.w(
                  "UserProfil lädt immer noch, setze Premium-Status später...",
                );
                // Hier könnten wir weitere Wartelogik implementieren, wenn nötig
              } else if (profileState is AsyncError) {
                log.e(
                  "Fehler beim Laden des UserProfils: ${profileState.error}",
                );
              } else {
                log.w("Unerwarteter Profilstatus: $profileState");
              }
            } catch (e) {
              log.e("Fehler beim Ändern des Premium-Status: $e");
            }
            // ENDE VERBESSERTE IMPLEMENTIERUNG

            if (!context.mounted) return; // Sicherheitscheck

            // Setze has_existing_account auf true, aber überschreibe nicht den Onboarding-Status
            try {
              // Setze has_existing_account auf true
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool('has_existing_account', true);
              log.i("has_existing_account auf true gesetzt");

              // Lade den aktuellen Onboarding-Status aus dem Provider
              final onboardingComplete = ref.read(onboardingProvider);
              log.i("Aktueller Onboarding-Status: $onboardingComplete");
            } catch (e) {
              log.e("Fehler beim Setzen von has_existing_account: $e");
            }

            // Prüfe, ob der Kontext noch gültig ist
            if (!context.mounted) return;

            // Starte AutoOnboardingService für lokale Onboarding-Prüfung
            log.i("Starte AutoOnboardingService für lokale Onboarding-Prüfung");
            final autoOnboardingService = ref.read(autoOnboardingProvider);
            await autoOnboardingService.checkOnboardingAfterLogin(context);

            // Starte automatische Text-Extraktion im Hintergrund
            _startBackgroundTextExtraction(ref, log);
          } else {
            // Benutzer ist nicht angemeldet, navigiere zum Login
            if (kDebugMode) {
              print("Benutzer ist nicht angemeldet, navigiere zum Login");
            }
            if (!context.mounted) return; // Sicherheitscheck
            context.go('/login');
          }
        } catch (e) {
          if (kDebugMode) {
            print("Fehler bei der Navigation: $e");
          }
          // Bei Fehler zum Login navigieren
          if (!context.mounted) return; // Sicherheitscheck
          context.go('/login');
        }
      });

      // Cleanup-Funktion, die aufgerufen wird, wenn das Widget entfernt wird
      return () {
        // Stoppe die Animation, bevor sie entsorgt wird
        if (animationController.isAnimating) {
          animationController.stop();
        }
        // Entsorge den Controller
        animationController.dispose();
      };
    }, []);

    // Animation für das Logo (schnellere Animation)
    final logoScaleAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
        ),
      ),
    );

    // Animation für den Title (Fade) (schnellere Animation)
    final titleOpacityAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0.2, 0.5, curve: Curves.easeIn),
        ),
      ),
    );

    // Erstelle die Slide-Animation direkt (schnellere Animation)
    final Animation<Offset> titleSlideAnimationObject = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: animationController,
        curve: const Interval(0.2, 0.5, curve: Curves.easeOut),
      ),
    );

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        // Verwende einen Stack für stabile Positionierung
        child: Stack(
          alignment: Alignment.center, // Zentriert Kinder im Stack
          children: [
            // Logo Animation (positioniert im Zentrum)
            Transform.scale(
              scale: logoScaleAnimation,
              child: Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: AppTheme.mediumShadow,
                ),
                padding: const EdgeInsets.all(20),
                child: const Icon(
                  Icons.work_outline,
                  color: AppTheme.primaryColor,
                  size: 80,
                ),
              ),
            ),

            // App-Name mit Fade-In und Slide-Up
            Align(
              alignment: const Alignment(0.0, 0.4),
              child: SlideTransition(
                position: titleSlideAnimationObject,
                child: Opacity(
                  opacity: titleOpacityAnimation,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Bewerbung KI',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(
                                76,
                              ), // 0.3 * 255 = ~76
                              offset: const Offset(0, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Startet automatische Text-Extraktion im Hintergrund
  void _startBackgroundTextExtraction(WidgetRef ref, dynamic log) {
    // Starte Text-Extraktion asynchron ohne auf das Ergebnis zu warten
    Future.delayed(const Duration(seconds: 2), () async {
      try {
        final user = Supabase.instance.client.auth.currentUser;
        if (user == null) {
          log.w('Keine automatische Text-Extraktion: Benutzer nicht angemeldet');
          return;
        }

        log.i('Starte automatische Text-Extraktion für alle bestehenden Jobs');

        // Erstelle Services
        final supabaseClient = ref.read(supabaseClientProvider);
        final textExtractionService = TextExtractionService(supabaseClient);
        final bulkService = BulkTextExtractionService(supabaseClient, textExtractionService);

        // Starte Bulk-Extraktion im Hintergrund
        final result = await bulkService.extractAllExistingJobs(user.id);

        if (result.success) {
          log.i('Automatische Text-Extraktion abgeschlossen: ${result.successfulExtractions} erfolgreich, ${result.failedExtractions} Fehler');
        } else {
          log.w('Automatische Text-Extraktion fehlgeschlagen: ${result.message}');
        }

        // Cleanup
        bulkService.dispose();
        textExtractionService.dispose();

      } catch (e) {
        log.e('Fehler bei automatischer Text-Extraktion: $e');
      }
    });
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../application/providers/favorites_provider.dart';
import '../../../application/providers/services_providers.dart';
import '../../../application/services/bulk_text_extraction_service.dart';
import '../../../application/services/text_extraction_service.dart';
import '../widgets/favorite_job_list_item.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import '../../../core/theme/app_theme.dart';
import 'package:ki_test/src/core/l10n/app_localizations_wrapper.dart';
import '../../common/widgets/job_search_filter.dart';
import '../../../domain/entities/job_entity.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../offline_texts/screens/offline_texts_screen.dart';

class FavoritesScreen extends HookConsumerWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favoritesState = ref.watch(favoritesProvider);
    final isExtracting = useState(false);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizationsWrapper.of(context).favoritesTitle),
        elevation: 0,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          // Button für Offline-Texte anzeigen
          IconButton(
            icon: const Icon(Icons.offline_bolt),
            tooltip: 'Offline-Texte anzeigen',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const OfflineTextsScreen(),
                ),
              );
            },
          ),
          // Test-Button für Text-Extraktion
          IconButton(
            icon: isExtracting.value
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.text_snippet),
            tooltip: 'Text-Extraktion starten',
            onPressed: isExtracting.value ? null : () async {
              await _startBulkTextExtraction(context, ref, isExtracting);
            },
          ),
        ],
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: favoritesState.when(
        data: (favoriteJobs) {
          if (favoriteJobs.isEmpty) {
            return _buildEmptyView(context);
          } else {
            return _FavoritesWithFilter(favoriteJobs: favoriteJobs, ref: ref);
          }
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => Center(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingLarge),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 50,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    Text(
                      'Fehler beim Laden der ${AppLocalizationsWrapper.of(context).favoritesTitle}',
                      style: Theme.of(
                        context,
                      ).textTheme.titleMedium?.copyWith(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    Text('$error', textAlign: TextAlign.center),
                  ],
                ),
              ),
            ),
      ),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 80, color: Colors.grey[400]),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Keine ${AppLocalizationsWrapper.of(context).favoritesTitle} gespeichert',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
            ),
            child: Text(
              'Tippe auf das Herz-Symbol bei Jobs, um sie zu ${AppLocalizationsWrapper.of(context).addToFavorites}.',
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Startet Bulk-Text-Extraktion für alle Favoriten und beworbenen Jobs
  Future<void> _startBulkTextExtraction(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<bool> isExtracting
  ) async {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Benutzer nicht angemeldet')),
      );
      return;
    }

    isExtracting.value = true;

    try {
      // Erstelle Services
      final supabaseClient = ref.read(supabaseClientProvider);
      final textExtractionService = TextExtractionService(supabaseClient);
      final bulkService = BulkTextExtractionService(supabaseClient, textExtractionService);

      // Zeige Progress-Dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _BulkExtractionDialog(
          bulkService: bulkService,
          userId: user.id,
        ),
      );

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fehler bei Text-Extraktion: $e')),
      );
    } finally {
      isExtracting.value = false;
    }
  }
}

/// Dialog für Bulk-Text-Extraktion mit Progress-Anzeige
class _BulkExtractionDialog extends StatefulWidget {
  final BulkTextExtractionService bulkService;
  final String userId;

  const _BulkExtractionDialog({
    required this.bulkService,
    required this.userId,
  });

  @override
  State<_BulkExtractionDialog> createState() => _BulkExtractionDialogState();
}

class _BulkExtractionDialogState extends State<_BulkExtractionDialog> {
  BulkExtractionProgress? _progress;
  BulkExtractionResult? _result;
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _startExtraction();
  }

  Future<void> _startExtraction() async {
    // Höre auf Progress-Updates
    widget.bulkService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _progress = progress;
          _isCompleted = progress.isCompleted;
        });
      }
    });

    // Starte Extraktion
    final result = await widget.bulkService.extractAllExistingJobs(widget.userId);

    if (mounted) {
      setState(() {
        _result = result;
        _isCompleted = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Text-Extraktion'),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_progress != null) ...[
              LinearProgressIndicator(
                value: _progress!.progressPercentage / 100,
              ),
              const SizedBox(height: 16),
              Text(
                '${_progress!.processedJobs} von ${_progress!.totalJobs} Jobs verarbeitet',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Erfolgreich: ${_progress!.successfulExtractions} | Fehler: ${_progress!.failedExtractions}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ] else ...[
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Lade Jobs...'),
            ],

            if (_result != null && _isCompleted) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _result!.success ? Colors.green[50] : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _result!.success ? Colors.green : Colors.red,
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      _result!.success ? Icons.check_circle : Icons.error,
                      color: _result!.success ? Colors.green : Colors.red,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _result!.success ? 'Extraktion abgeschlossen!' : 'Extraktion fehlgeschlagen',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _result!.success ? Colors.green[800] : Colors.red[800],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_result!.successfulExtractions} erfolgreich, ${_result!.failedExtractions} Fehler',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        if (_isCompleted)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Schließen'),
          ),
      ],
    );
  }
}

class _FavoritesWithFilter extends HookWidget {
  final List<JobEntity> favoriteJobs;
  final WidgetRef ref;

  const _FavoritesWithFilter({
    required this.favoriteJobs,
    required this.ref,
  });

  @override
  Widget build(BuildContext context) {
    final filteredJobs = useState<List<JobEntity>>(favoriteJobs);

    // Aktualisiere gefilterte Jobs wenn sich die Favoriten ändern
    useEffect(() {
      filteredJobs.value = favoriteJobs;
      return null;
    }, [favoriteJobs]);

    return Column(
      children: [
        // Suchfilter
        JobSearchFilter(
          originalJobs: favoriteJobs,
          onFilteredJobsChanged: (filtered) {
            filteredJobs.value = filtered;
          },
          hintText: 'Favoriten durchsuchen...',
        ),
        
        // Ergebnisanzahl anzeigen
        if (filteredJobs.value.length != favoriteJobs.length)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
              vertical: AppTheme.spacingXSmall,
            ),
            child: Text(
              '${filteredJobs.value.length} von ${favoriteJobs.length} Favoriten',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        
        // Job-Liste
        Expanded(
          child: filteredJobs.value.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),
                      Text(
                        'Keine Favoriten gefunden',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Versuche andere Suchbegriffe',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  // Verringerter Abstand zur Navigation
                  padding: const EdgeInsets.only(
                    left: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                    right: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                    top: AppTheme.spacingSmall,
                    bottom: kBottomNavigationBarHeight - 32,
                  ),
                  itemCount: filteredJobs.value.length,
                  itemBuilder: (context, index) {
                    final job = filteredJobs.value[index];
                    return FavoriteJobListItem(
                      job: job,
                      onTap: () {
                        // Snackbar ausblenden, wenn zu einer anderen Seite navigiert wird
                        ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                  (context) => JobDetailScreen(
                                    jobRefnr: job.id,
                                    jobTitle: job.title,
                                    sourceUrl: job.sourceUrl,
                                    jobEntity: job,
                                  ),
                          ),
                        );
                      },
                      onDismissed: () {
                        // Job speichern, bevor er entfernt wird, um ihn ggf. wiederherstellen zu können
                        final deletedJob = job;
                        ref.read(favoritesProvider.notifier).removeFavorite(job.id);
                        
                        // Snackbar mit Wiederherstellungsoption für 5 Sekunden anzeigen
                        ScaffoldMessenger.of(context).clearSnackBars();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            backgroundColor: Colors.red[700],
                            content: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Aus Favoriten entfernt',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 13,
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    // Job wiederherstellen
                                    ref.read(favoritesProvider.notifier).restoreFavorite(deletedJob);
                                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                  },
                                  child: Text(
                                    'Wiederherstellen',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            duration: const Duration(seconds: 5),
                            behavior: SnackBarBehavior.fixed,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            elevation: 4,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
      ],
    );
  }
}

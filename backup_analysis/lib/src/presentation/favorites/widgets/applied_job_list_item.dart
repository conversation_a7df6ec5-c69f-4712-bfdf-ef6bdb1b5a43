import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:intl/intl.dart';

/// Widget zur Anzeige eines Jobs, auf den sich der Benutzer beworben hat
class AppliedJobListItem extends StatelessWidget {
  final JobEntity job;
  final VoidCallback onTap;
  final VoidCallback? onDismissed;
  final DateTime? appliedDate;

  const AppliedJobListItem({
    super.key,
    required this.job,
    required this.onTap,
    this.onDismissed,
    this.appliedDate,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd.MM.yyyy');
    final appliedDateText =
        appliedDate != null
            ? 'Beworben am ${dateFormat.format(appliedDate!)}'
            : 'Beworben';

    final colorScheme = Theme.of(context).colorScheme;

    return Dismissible(
      key: Key('applied_job_${job.id}'),
      direction:
          onDismissed != null
              ? DismissDirection.endToStart
              : DismissDirection.none,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        color: Colors.red,
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      onDismissed: (_) {
        if (onDismissed != null) {
          onDismissed!();
        }
      },
      child: Card(
        margin: const EdgeInsets.symmetric(
          vertical: AppTheme.spacingSmall / 2,
          horizontal: AppTheme.spacingSmall / 2,
        ),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            job.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            job.companyName,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                              fontSize: 13,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.secondaryColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusSmall,
                        ),
                      ),
                      child: Text(
                        appliedDateText,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.secondaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: colorScheme.outline,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        job.location,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: colorScheme.outline,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                // Gehaltsanzeige entfernt, da JobEntity kein salary-Feld hat
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'dart:math';
import 'package:flutter/material.dart';

/// Angepasste minimalistische Ladeanimation für die App
/// Optimiert für die Bewerbungsgenerierung mit Farben, die zum App-Design passen
class AppLoadingAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const AppLoadingAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AppLoadingAnimation> createState() => _AppLoadingAnimationState();
}

class _AppLoadingAnimationState extends State<AppLoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: _AppLoadingAnimationPainter(
              animation: _controller.value,
              color: widget.color,
              theme: Theme.of(context),
            ),
          );
        },
      ),
    );
  }
}

class _AppLoadingAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;
  final ThemeData theme;

  _AppLoadingAnimationPainter({
    required this.animation,
    required this.color,
    required this.theme,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Farben aus dem App-Theme verwenden
    final primaryColor = color;
    // Verwende eine kräftigere Farbe für den Akzent, die nicht weiß erscheint
    final accentColor = theme.colorScheme.secondary.withValues(
      alpha: 230,
    ); // ~0.9 alpha

    // Hintergrund-Kreis zeichnen (optional, für bessere Sichtbarkeit)
    final backgroundPaint =
        Paint()
          ..color = theme.colorScheme.primary.withValues(
            alpha: 38,
          ) // ~0.15 alpha
          ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 1.1, backgroundPaint);

    // Hauptkreis zeichnen
    final circlePaint =
        Paint()
          ..color = primaryColor.withValues(
            alpha: 128,
          ) // ~0.5 alpha, erhöhte Opazität für bessere Sichtbarkeit
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.5;

    canvas.drawCircle(center, radius, circlePaint);

    // Rotierender Bogen
    final arcPaint =
        Paint()
          ..color = primaryColor
          ..style = PaintingStyle.stroke
          ..strokeWidth =
              3.5 // Etwas dicker für bessere Sichtbarkeit
          ..strokeCap = StrokeCap.round;

    // Zwei Bögen zeichnen, die sich gegenläufig bewegen
    final startAngle1 = animation * 2 * pi;
    final sweepAngle1 = pi * 0.6 + pi * 0.3 * sin(animation * 2 * pi);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle1,
      sweepAngle1,
      false,
      arcPaint,
    );

    final startAngle2 = startAngle1 + pi;
    final sweepAngle2 = pi * 0.4 + pi * 0.2 * sin(animation * 2 * pi + pi);

    final arcPaint2 =
        Paint()
          ..color = accentColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.5
          ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.75),
      startAngle2,
      sweepAngle2,
      false,
      arcPaint2,
    );

    // Kleine Akzentpunkte
    final dotPaint =
        Paint()
          ..color = primaryColor
          ..style = PaintingStyle.fill;

    // Punkt am Ende des ersten Bogens
    final endAngle1 = startAngle1 + sweepAngle1;
    final dotX1 = center.dx + radius * cos(endAngle1);
    final dotY1 = center.dy + radius * sin(endAngle1);

    canvas.drawCircle(Offset(dotX1, dotY1), 3.5, dotPaint);

    // Punkt am Ende des zweiten Bogens
    final endAngle2 = startAngle2 + sweepAngle2;
    final dotX2 = center.dx + radius * 0.75 * cos(endAngle2);
    final dotY2 = center.dy + radius * 0.75 * sin(endAngle2);

    final dotPaint2 =
        Paint()
          ..color = accentColor
          ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(dotX2, dotY2), 2.5, dotPaint2);

    // Zusätzliche kleine Punkte für mehr visuelle Komplexität
    for (int i = 0; i < 3; i++) {
      final angle = startAngle1 + (i * 2 * pi / 3);
      final x = center.dx + radius * 0.5 * cos(angle);
      final y = center.dy + radius * 0.5 * sin(angle);

      final smallDotPaint =
          Paint()
            ..color = primaryColor.withValues(
              alpha: (178 - (i * 38)).toDouble(),
            ) // ~0.7-0.15 alpha
            ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(x, y), 1.5, smallDotPaint);
    }
  }

  @override
  bool shouldRepaint(_AppLoadingAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation ||
        oldDelegate.color != color ||
        oldDelegate.theme != theme;
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';

/// Widget zur Anzeige der verbleibenden Bewerbungen
class RemainingApplicationsWidget extends ConsumerWidget {
  final bool showLabel;
  final bool showIcon;
  final bool compact;
  final bool showDetailed;
  final VoidCallback? onUpgradePressed;

  const RemainingApplicationsWidget({
    super.key,
    this.showLabel = true,
    this.showIcon = true,
    this.compact = false,
    this.showDetailed = false,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final remainingApplicationsState = ref.watch(remainingApplicationsProvider);
    final isPremium = ref.watch(isPremiumProvider);
    final userProfileState = ref.watch(userProfileProvider);

    return userProfileState.when(
      data: (userProfile) {
        final planType = userProfile.premiumPlanType?.toLowerCase() ?? 'basic';

        // Wenn der Benutzer Premium oder Unlimited hat, zeige KEINE Bewerbungszähler an
        // Stattdessen zeige das Unlimited-Widget oder nichts
        if (planType == 'premium' || planType == 'unlimited') {
          // Bei Premium oder Unlimited zeigen wir keinen Bewerbungszähler an
          // Nur in bestimmten Kontexten (z.B. auf der Premium-Verwaltungsseite)
          // zeigen wir das Unlimited-Widget an
          if (ModalRoute.of(context)?.settings.name == '/premium-management') {
            return _buildUnlimitedWidget(context);
          } else {
            // In allen anderen Kontexten (z.B. in der AppBar) zeigen wir nichts an
            return const SizedBox.shrink();
          }
        }

        // Für Basic und Pro Pläne zeige den Zähler an
        return remainingApplicationsState.when(
          data: (data) {
            final remaining = data['remaining'] as int?;
            final total = data['total'] as int?;
            final unlimited = data['unlimited'] as bool? ?? false;

            if (unlimited) {
              // Auch hier: Bei unlimited zeigen wir keinen Bewerbungszähler an
              if (ModalRoute.of(context)?.settings.name ==
                  '/premium-management') {
                return _buildUnlimitedWidget(context);
              } else {
                return const SizedBox.shrink();
              }
            }

            if (remaining == null || total == null) {
              return _buildErrorWidget(context);
            }

            // Wenn der Benutzer keine Bewerbungen mehr hat und nicht Premium ist,
            // zeige einen speziellen Widget an
            if (remaining <= 0 && !isPremium) {
              return _buildNoApplicationsLeftWidget(context, ref);
            }

            // Korrigiere die Werte basierend auf dem Plan-Typ
            int correctedTotal = total;
            int correctedRemaining = remaining;

            // Wenn die Werte nicht mit dem Plan-Typ übereinstimmen, korrigiere sie
            if (planType == 'basic' && total != 30) {
              correctedTotal = 30;
              correctedRemaining = 30;
            } else if (planType == 'pro' && total != 150) {
              correctedTotal = 150;
              correctedRemaining = 150;
            }

            return _buildCounterWidget(
              context,
              correctedRemaining,
              correctedTotal,
            );
          },
          loading: () => _buildLoadingWidget(context),
          error: (error, stackTrace) => _buildErrorWidget(context),
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (error, stackTrace) => _buildErrorWidget(context),
    );
  }

  Widget _buildCounterWidget(BuildContext context, int remaining, int total) {
    // Wenn total 0 ist, setze Standardwerte
    if (total == 0) {
      total = 30; // Standardwert für Basic-Plan
      remaining = 30; // Standardwert für Basic-Plan
    }

    final percentage = total > 0 ? (remaining / total) : 0.0;
    final color = _getColorForPercentage(context, percentage);

    return compact
        ? _buildCompactCounter(context, remaining, total, color)
        : _buildFullCounter(context, remaining, total, color);
  }

  Widget _buildCompactCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    // Für bessere Lesbarkeit in der AppBar immer weiße Farbe verwenden
    final bool isInAppBar =
        ModalRoute.of(context)?.settings.name != '/premium-management';
    final textColor = isInAppBar ? Colors.white : color;
    final iconColor = isInAppBar ? Colors.white : color;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration:
          isInAppBar
              ? BoxDecoration(
                color: Colors.white.withAlpha(51),
                borderRadius: BorderRadius.circular(12),
              )
              : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) Icon(Icons.work_outline, size: 16, color: iconColor),
          if (showIcon) const SizedBox(width: 4),
          Text(
            '$remaining/$total',
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    return showDetailed
        ? _buildDetailedCounter(context, remaining, total, color)
        : _buildSimpleCounter(context, remaining, total, color);
  }

  Widget _buildSimpleCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showLabel)
          Text(
            'Verbleibende Bewerbungen',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        if (showLabel) const SizedBox(height: 4),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) Icon(Icons.work_outline, size: 20, color: color),
            if (showIcon) const SizedBox(width: 8),
            Text(
              '$remaining/$total',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: 100,
          height: 4,
          child: LinearProgressIndicator(
            value: total > 0 ? (remaining / total) : 0.0,
            backgroundColor: Colors.grey.withAlpha(77),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedCounter(
    BuildContext context,
    int remaining,
    int total,
    Color color,
  ) {
    final percentage = total > 0 ? (remaining / total) * 100 : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.work_outline, size: 24, color: color),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$remaining von $total Bewerbungen verfügbar',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${percentage.toStringAsFixed(0)}% verbleibend',
                  style: TextStyle(fontSize: 14, color: color.withAlpha(204)),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: total > 0 ? (remaining / total) : 0.0,
            backgroundColor: Colors.grey.withAlpha(77),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ),
        if (remaining < 5) ...[
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed:
                onUpgradePressed ??
                () {
                  // Navigiere zur Premium-Seite
                  Navigator.of(context).pushNamed('/premium-management');
                },
            icon: const Icon(Icons.upgrade),
            label: const Text('Jetzt upgraden'),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUnlimitedWidget(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showIcon)
          Icon(
            Icons.all_inclusive,
            size: compact ? 16 : 20,
            color: Theme.of(context).colorScheme.primary,
          ),
        if (showIcon) SizedBox(width: compact ? 4 : 8),
        Text(
          'Unbegrenzt',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
            fontSize: compact ? 14 : 16,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      width: compact ? 16 : 20,
      height: compact ? 16 : 20,
      child: CircularProgressIndicator(
        strokeWidth: compact ? 2 : 3,
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.error_outline,
          size: compact ? 16 : 20,
          color: Theme.of(context).colorScheme.error,
        ),
        SizedBox(width: compact ? 4 : 8),
        Text(
          'Fehler',
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
            fontSize: compact ? 14 : 16,
          ),
        ),
      ],
    );
  }

  Widget _buildNoApplicationsLeftWidget(BuildContext context, WidgetRef ref) {
    return showDetailed
        ? _buildDetailedNoApplicationsWidget(context, ref)
        : _buildSimpleNoApplicationsWidget(context, ref);
  }

  Widget _buildSimpleNoApplicationsWidget(BuildContext context, WidgetRef ref) {
    // Hole das nächste Reset-Datum für kostenlose Bewerbungen
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        // Wenn kein Datum verfügbar ist oder es ein Premium-Benutzer ist, zeige den Standard-Widget an
        if (nextResetDate == null) {
          return _buildStandardNoApplicationsWidget(context);
        }

        // Berechne die verbleibende Zeit bis zum nächsten Reset
        final now = DateTime.now();
        final difference = nextResetDate.difference(now);

        // Wenn das Datum in der Vergangenheit liegt, zeige den Standard-Widget an
        if (difference.isNegative) {
          return _buildStandardNoApplicationsWidget(context);
        }

        // Formatiere die verbleibende Zeit
        final days = difference.inDays;
        final hours = difference.inHours % 24;

        String timeText;
        if (days > 0) {
          timeText = '$days Tag${days > 1 ? 'e' : ''} $hours Std.';
        } else {
          timeText = '$hours Std. ${difference.inMinutes % 60} Min.';
        }

        return InkWell(
          onTap:
              onUpgradePressed ??
              () {
                // Navigiere zur Premium-Seite
                Navigator.of(context).pushNamed('/premium-management');
              },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.timer, size: compact ? 16 : 20, color: Colors.blue),
              SizedBox(width: compact ? 4 : 8),
              Text(
                'Neue Bewerbungen in $timeText',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                  fontSize: compact ? 14 : 16,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (_, __) => _buildStandardNoApplicationsWidget(context),
    );
  }

  Widget _buildStandardNoApplicationsWidget(BuildContext context) {
    return InkWell(
      onTap:
          onUpgradePressed ??
          () {
            // Navigiere zur Premium-Seite
            Navigator.of(context).pushNamed('/premium-management');
          },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: compact ? 16 : 20,
            color: Colors.orange,
          ),
          SizedBox(width: compact ? 4 : 8),
          Text(
            'Keine Bewerbungen mehr',
            style: TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
              fontSize: compact ? 14 : 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedNoApplicationsWidget(
    BuildContext context,
    WidgetRef ref,
  ) {
    // Hole das nächste Reset-Datum für kostenlose Bewerbungen
    final nextResetDateState = ref.watch(nextFreeResetDateProvider);

    return nextResetDateState.when(
      data: (nextResetDate) {
        // Wenn kein Datum verfügbar ist oder es ein Premium-Benutzer ist, zeige den Standard-Widget an
        if (nextResetDate == null) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        // Berechne die verbleibende Zeit bis zum nächsten Reset
        final now = DateTime.now();
        final difference = nextResetDate.difference(now);

        // Wenn das Datum in der Vergangenheit liegt, zeige den Standard-Widget an
        if (difference.isNegative) {
          return _buildStandardDetailedNoApplicationsWidget(context);
        }

        // Formatiere die verbleibende Zeit und das Datum
        final days = difference.inDays;
        final hours = difference.inHours % 24;
        final dateFormat = DateFormat('dd.MM.yyyy');

        String timeText;
        if (days > 0) {
          timeText = '$days Tag${days > 1 ? 'e' : ''} und $hours Stunden';
        } else {
          timeText = '$hours Stunden und ${difference.inMinutes % 60} Minuten';
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.timer, size: 24, color: Colors.blue),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Keine Bewerbungen mehr verfügbar',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Neue kostenlose Bewerbungen in $timeText',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Nächste Freischaltung: ${dateFormat.format(nextResetDate)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        onUpgradePressed ??
                        () {
                          // Navigiere zur Premium-Seite
                          Navigator.of(
                            context,
                          ).pushNamed('/premium-management');
                        },
                    icon: const Icon(Icons.upgrade),
                    label: const Text('Jetzt upgraden'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (_, __) => _buildStandardDetailedNoApplicationsWidget(context),
    );
  }

  Widget _buildStandardDetailedNoApplicationsWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              size: 24,
              color: Colors.orange,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Keine Bewerbungen mehr verfügbar',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.orange,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Upgrade auf ein höheres Paket, um mehr Bewerbungen zu erhalten.',
                    style: TextStyle(fontSize: 14, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed:
              onUpgradePressed ??
              () {
                // Navigiere zur Premium-Seite
                Navigator.of(context).pushNamed('/premium-management');
              },
          icon: const Icon(Icons.upgrade),
          label: const Text('Jetzt upgraden'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ),
      ],
    );
  }

  Color _getColorForPercentage(BuildContext context, double percentage) {
    if (percentage > 0.5) {
      return Theme.of(context).colorScheme.primary;
    } else if (percentage > 0.2) {
      return Colors.orange;
    } else {
      return Theme.of(context).colorScheme.error;
    }
  }
}

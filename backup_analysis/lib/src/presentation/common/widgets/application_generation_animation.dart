import 'dart:math';
import 'package:flutter/material.dart';

/// Hochwertige Animation speziell für die Bewerbungsgenerierung
/// Diese Animation ist besonders auffällig und auch auf weißem Hintergrund gut sichtbar
class ApplicationGenerationAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;

  ApplicationGenerationAnimationPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Definiere Farben für einen auffälligen Farbverlauf
    final Color goldColor = Color.lerp(color, Colors.amber, 0.3)!;
    final Color pinkColor = Color.lerp(color, Colors.pink, 0.3)!;
    final Color darkColor = Color.lerp(color, Colors.indigo, 0.3)!;

    // Zeichne pulsierende Ringe mit Farbverlauf
    for (int i = 0; i < 4; i++) {
      final ringPhase = animation + (i * 0.25);
      final normalizedPhase = ringPhase - ringPhase.floor();
      
      // Radius wächst und verschwindet dann
      final ringRadius = radius * normalizedPhase;
      final ringOpacity = 0.9 * (1.0 - normalizedPhase); // Höhere Opazität für bessere Sichtbarkeit
      
      if (ringOpacity > 0.05) {
        final ringPaint = Paint()
          ..color = i % 3 == 0 
              ? goldColor.withValues(alpha: ringOpacity)
              : (i % 3 == 1 
                  ? pinkColor.withValues(alpha: ringOpacity) 
                  : darkColor.withValues(alpha: ringOpacity))
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.5; // Dickere Linien für bessere Sichtbarkeit
        
        canvas.drawCircle(center, ringRadius, ringPaint);
      }
    }
    
    // Zeichne rotierende Bögen
    for (int i = 0; i < 3; i++) {
      final arcPaint = Paint()
        ..color = i % 3 == 0 
            ? goldColor.withValues(alpha: 0.9)
            : (i % 3 == 1 
                ? pinkColor.withValues(alpha: 0.9) 
                : darkColor.withValues(alpha: 0.9))
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0 // Dickere Linien
        ..strokeCap = StrokeCap.round;
      
      final startAngle = animation * 2 * pi + (i * 2 * pi / 3);
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius * 0.7),
        startAngle,
        pi * 0.6,
        false,
        arcPaint,
      );
    }
    
    // Zeichne einen schimmernden Kern
    final coreRadius = radius * 0.3;
    final shimmerPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          goldColor,
          pinkColor.withValues(alpha: 0.8),
          darkColor.withValues(alpha: 0.5),
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: coreRadius))
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      center, 
      coreRadius * (0.8 + 0.2 * sin(animation * 3 * pi)), 
      shimmerPaint
    );
    
    // Zeichne einen Glow-Effekt um den Kern
    canvas.drawCircle(
      center,
      coreRadius * 1.2 * (0.8 + 0.2 * sin(animation * 2 * pi)),
      Paint()
        ..color = goldColor.withValues(alpha: 0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0),
    );
  }

  @override
  bool shouldRepaint(ApplicationGenerationAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Widget zum einfachen Einbinden der Animation
class ApplicationGenerationAnimation extends StatefulWidget {
  final Color color;
  final double size;
  
  const ApplicationGenerationAnimation({
    super.key, 
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<ApplicationGenerationAnimation> createState() => _ApplicationGenerationAnimationState();
}

class _ApplicationGenerationAnimationState extends State<ApplicationGenerationAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3500), // Langsame Animation für besseren Effekt
    );
    
    // Starte die Animation sofort
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: ApplicationGenerationAnimationPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

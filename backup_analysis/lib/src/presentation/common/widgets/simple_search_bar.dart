import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// Eine vereinfachte Suchleiste für die Filterung von Listen
/// ohne komplexe Vorschlagsfunktionalität
class SimpleSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final String? hintText;
  final Function(String) onChanged;
  final VoidCallback? onClear;
  final FocusNode? focusNode;

  const SimpleSearchBar({
    super.key,
    required this.controller,
    required this.onChanged,
    this.hintText,
    this.onClear,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(128),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        decoration: InputDecoration(
          hintText: hintText ?? '<PERSON><PERSON> nach <PERSON>, Job oder Firma...',
          prefixIcon: const Icon(Icons.search, size: 20),
          suffixIcon: controller.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    controller.clear();
                    onChanged('');
                    if (onClear != null) {
                      onClear!();
                    }
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(128),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 10,
            horizontal: AppTheme.spacingMedium,
          ),
          isDense: true,
        ),
        textAlignVertical: TextAlignVertical.center,
        onChanged: onChanged,
        textInputAction: TextInputAction.search,
      ),
    );
  }
}
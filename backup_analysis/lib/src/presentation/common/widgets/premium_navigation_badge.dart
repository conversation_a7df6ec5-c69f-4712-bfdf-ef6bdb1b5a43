import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/utils/logging.dart';

/// Widget zur Anzeige des Premium-Status in der Navigation
class PremiumNavigationBadge extends ConsumerWidget {
  final Widget child;
  final bool showBadge;

  const PremiumNavigationBadge({
    super.key,
    required this.child,
    this.showBadge = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('PremiumNavigationBadge');
    final userProfileState = ref.watch(userProfileProvider);
    final remainingApplicationsAsync = ref.watch(remainingApplicationsProvider);

    return Stack(
      alignment: Alignment.center,
      children: [
        // Das ursprüngliche Widget (z.B. BottomNavigationBarItem)
        child,

        // Premium-Badge
        if (showBadge)
          userProfileState.when(
            data: (userProfile) {
              final isPremium = userProfile.isPremium ?? false;
              final planType = userProfile.premiumPlanType ?? 'basic';

              // Wenn der Benutzer Premium ist, zeige ein Premium-Badge an
              if (isPremium) {
                return Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: _getColorForPlanType(planType, context),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.star,
                      size: 10,
                      color: Colors.white,
                    ),
                  ),
                );
              }

              // Wenn der Benutzer nicht Premium ist, zeige die verbleibenden Bewerbungen an
              return remainingApplicationsAsync.when(
                data: (data) {
                  final remaining = data['remaining'] as int;
                  final unlimited = data['unlimited'] as bool;

                  if (unlimited) {
                    return const SizedBox.shrink();
                  }

                  // Wenn weniger als 5 Bewerbungen übrig sind, zeige eine Warnung an
                  if (remaining < 5) {
                    return Positioned(
                      top: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Center(
                          child: Text(
                            remaining.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  }

                  return const SizedBox.shrink();
                },
                loading: () => const SizedBox.shrink(),
                error: (_, __) => const SizedBox.shrink(),
              );
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),
      ],
    );
  }

  /// Gibt die Farbe für den Plan-Typ zurück
  Color _getColorForPlanType(String planType, BuildContext context) {
    switch (planType.toLowerCase()) {
      case 'basic':
        return Colors.blue;
      case 'pro':
        return Colors.purple;
      case 'unlimited':
        return Colors.orange;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}

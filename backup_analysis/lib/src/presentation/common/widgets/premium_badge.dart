import 'package:flutter/material.dart';

/// Ein Badge, das anzeigt, dass eine Funktion nur für Premium-Nutzer verfügbar ist.
/// 
/// Kann auf oder neben Premium-Funktionen platziert werden, um dem Nutzer
/// zu signalisieren, dass diese Funktion Premium erfordert.
class PremiumBadge extends StatelessWidget {
  /// Erstellt ein Premium-Badge.
  /// 
  /// [size] kontrolliert die Größe des Badges.
  /// [showLabel] bestimmt, ob "Premium" als Text angezeigt wird.
  const PremiumBadge({
    super.key,
    this.size = PremiumBadgeSize.medium,
    this.showLabel = true,
  });

  /// Die Größe des Badges.
  final PremiumBadgeSize size;
  
  /// Ob der Text "Premium" angezeigt werden soll.
  final bool showLabel;

  @override
  Widget build(BuildContext context) {
    // Definiere Größenparameter basierend auf der gewählten Größe
    final double iconSize;
    final double textSize;
    final double padding;
    final double borderRadius;
    
    switch (size) {
      case PremiumBadgeSize.small:
        iconSize = 12;
        textSize = 10;
        padding = 4;
        borderRadius = 4;
        break;
      case PremiumBadgeSize.medium:
        iconSize = 16;
        textSize = 12;
        padding = 6;
        borderRadius = 6;
        break;
      case PremiumBadgeSize.large:
        iconSize = 20;
        textSize = 14;
        padding = 8;
        borderRadius = 8;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: padding,
        vertical: padding / 2,
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.workspace_premium,
            size: iconSize,
            color: Colors.white,
          ),
          if (showLabel) ...[
            SizedBox(width: padding / 2),
            Text(
              'Premium',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: textSize,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Die verfügbaren Größen für das Premium-Badge.
enum PremiumBadgeSize {
  small,
  medium,
  large,
}

/// Ein Text mit Premium-Badge.
/// 
/// Kombiniert einen Text mit einem Premium-Badge am Ende.
/// Nützlich für Überschriften oder Menüeinträge.
class PremiumLabeledText extends StatelessWidget {
  /// Erstellt einen Text mit Premium-Badge.
  const PremiumLabeledText({
    super.key,
    required this.text,
    this.style,
    this.badgeSize = PremiumBadgeSize.small,
    this.spacing = 8.0,
  });

  /// Der anzuzeigende Text.
  final String text;
  
  /// Der Textstil.
  final TextStyle? style;
  
  /// Die Größe des Premium-Badges.
  final PremiumBadgeSize badgeSize;
  
  /// Der Abstand zwischen Text und Badge.
  final double spacing;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          text,
          style: style,
        ),
        SizedBox(width: spacing),
        PremiumBadge(
          size: badgeSize,
          showLabel: badgeSize != PremiumBadgeSize.small,
        ),
      ],
    );
  }
} 
 
 
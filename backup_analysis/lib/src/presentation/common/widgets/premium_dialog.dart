import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:go_router/go_router.dart';

/// Dialog, der angezeigt wird, wenn ein Benutzer eine Premium-Funktion nutzen möchte
class PremiumDialog extends ConsumerWidget {
  final String title;
  final String message;
  final VoidCallback? onWatchAdPressed;
  final VoidCallback? onPremiumPressed;
  final VoidCallback? onCancelPressed;

  const PremiumDialog({
    super.key,
    this.title = 'Premium-Funktion',
    this.message =
        'Diese Funktion ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?',
    this.onWatchAdPressed,
    this.onPremiumPressed,
    this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final log = getLogger('PremiumDialog');

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Titel
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // Nachricht
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),

            // Buttons
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Premium-Button
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop('premium');
                    if (onPremiumPressed != null) {
                      onPremiumPressed!();
                    } else {
                      // Standard-Aktion: Navigiere zur Premium-Seite
                      context.go('/premium-benefits');
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingMedium,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusMedium,
                      ),
                    ),
                  ),
                  child: const Text('Premium holen'),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                // Werbung-Button
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop('ad');
                    if (onWatchAdPressed != null) {
                      onWatchAdPressed!();
                    } else {
                      // Standard-Aktion: Zeige Werbung an
                      _showAd(context, ref);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacingMedium,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusMedium,
                      ),
                    ),
                  ),
                  child: const Text('Werbung ansehen'),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                // Abbrechen-Button
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop('cancel');
                    if (onCancelPressed != null) {
                      onCancelPressed!();
                    }
                  },
                  child: const Text('Abbrechen'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Zeigt eine Werbung an
  void _showAd(BuildContext context, WidgetRef ref) {
    final log = getLogger('PremiumDialog._showAd');
    final adService = ref.read(adServiceProvider);

    log.i('Zeige Werbung an...');

    adService.loadAndShowRewardedAd(
      onUserEarnedRewardCallback: () {
        log.i('Benutzer hat Belohnung erhalten');
        // Hier könnte eine Belohnung gewährt werden
      },
      onAdFailedToLoadCallback: () {
        log.e('Werbung konnte nicht geladen werden');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Werbung konnte nicht geladen werden. Bitte versuche es später erneut.',
              ),
            ),
          );
        }
      },
      onAdFailedToShowCallback: () {
        log.e('Werbung konnte nicht angezeigt werden');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Werbung konnte nicht angezeigt werden. Bitte versuche es später erneut.',
              ),
            ),
          );
        }
      },
      onAdDismissedCallback: () {
        log.i('Werbung wurde geschlossen');
      },
    );
  }

  /// Zeigt den Premium-Dialog an
  static Future<String?> show(
    BuildContext context, {
    String title = 'Premium-Funktion',
    String message =
        'Diese Funktion ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?',
    VoidCallback? onWatchAdPressed,
    VoidCallback? onPremiumPressed,
    VoidCallback? onCancelPressed,
  }) {
    return showDialog<String>(
      context: context,
      builder:
          (context) => PremiumDialog(
            title: title,
            message: message,
            onWatchAdPressed: onWatchAdPressed,
            onPremiumPressed: onPremiumPressed,
            onCancelPressed: onCancelPressed,
          ),
    );
  }
}

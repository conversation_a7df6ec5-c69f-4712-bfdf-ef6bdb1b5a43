import 'package:flutter/material.dart';

/// Hochwertige Animation speziell für den KI-Anschreiben-Generieren-Button
class KIAnschreibenAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;

  KIAnschreibenAnimationPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Definiere mehrere Farben für eine hochwertigere Animation
    final colors = [
      Colors.pink.shade300,
      Colors.amber.shade300,
      Colors.pink.shade700,
      Colors.amber.shade700,
    ];

    // Zeichne einen rotierenden Farbverlauf
    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0;

    // Zeichne mehrere Kreise mit unterschiedlichen Radien und Farben
    for (int i = 0; i < 4; i++) {
      final sweepAngle = (animation * 6.28) + (i * 0.5);
      final startAngle = i * 0.5;

      paint.shader = SweepGradient(
        center: Alignment.center,
        startAngle: startAngle,
        endAngle: sweepAngle,
        colors: colors,
      ).createShader(Rect.fromCircle(center: center, radius: radius));

      canvas.drawCircle(center, radius - (i * 2), paint);
    }

    // Zeichne einen pulsierenden Punkt in der Mitte
    final dotPaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(
            BlurStyle.normal,
            2.0,
          ); // Weicher Rand

    // Zeichne zwei Kreise für einen Glow-Effekt
    canvas.drawCircle(
      center,
      radius * 0.3 * (0.8 + 0.2 * animation),
      Paint()
        ..color = colors[0].withValues(alpha: 77) // Glow-Effekt
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0),
    );

    canvas.drawCircle(center, radius * 0.2 * (0.8 + 0.2 * animation), dotPaint);
  }

  @override
  bool shouldRepaint(KIAnschreibenAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Widget zum einfachen Einbinden der Animation
class KIAnschreibenAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const KIAnschreibenAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<KIAnschreibenAnimation> createState() => _KIAnschreibenAnimationState();
}

class _KIAnschreibenAnimationState extends State<KIAnschreibenAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 3500,
      ), // Langsame Animation für besseren Effekt
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: KIAnschreibenAnimationPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

// Button-Klasse wurde in separate Datei ki_anschreiben_button.dart verschoben
// um Konflikte zu vermeiden

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/common/widgets/remaining_applications_widget.dart';

/// Widget zur Anzeige des Abonnement-Status
class SubscriptionStatusWidget extends ConsumerWidget {
  final bool compact;
  final bool showDetails;
  final bool showNavigateButton;

  const SubscriptionStatusWidget({
    super.key,
    this.compact = false,
    this.showDetails = true,
    this.showNavigateButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileState = ref.watch(userProfileProvider);

    return userProfileState.when(
      data: (userProfile) {
        final isPremium = userProfile.isPremium ?? false;
        final planType = userProfile.premiumPlanType;

        // Versuche zuerst premiumExpiryDate, dann premiumExpiry als Fallback
        DateTime? expiryDate = userProfile.premiumExpiryDate;
        if (expiryDate == null && userProfile.premiumExpiry != null) {
          expiryDate = DateTime.tryParse(userProfile.premiumExpiry!);
        }

        return _buildStatusCard(
          context,
          isPremium: isPremium,
          planType: planType,
          expiryDate: expiryDate,
        );
      },
      loading: () => _buildLoadingWidget(context),
      error: (error, stackTrace) => _buildErrorWidget(context, error),
    );
  }

  Widget _buildStatusCard(
    BuildContext context, {
    required bool isPremium,
    String? planType,
    DateTime? expiryDate,
  }) {
    if (compact) {
      return _buildCompactStatusWidget(
        context,
        isPremium: isPremium,
        planType: planType,
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPremium
                      ? Icons.workspace_premium
                      : Icons.workspace_premium_outlined,
                  color: isPremium ? _getPlanColor(planType) : Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  isPremium
                      ? 'Premium ${_getPlanName(planType)}'
                      : 'Kein Premium-Abonnement',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isPremium ? _getPlanColor(planType) : null,
                  ),
                ),
                const Spacer(),
                if (showNavigateButton)
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: () => _navigateToPremiumScreen(context),
                    tooltip: 'Premium-Optionen anzeigen',
                  ),
              ],
            ),

            if (showDetails) ...[
              const Divider(),

              // Verbleibende Bewerbungen
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppTheme.spacingSmall,
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.work_outline,
                      size: 20,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: AppTheme.spacingSmall),
                    const Text('Bewerbungen:'),
                    const SizedBox(width: AppTheme.spacingSmall),
                    const RemainingApplicationsWidget(
                      showLabel: false,
                      showIcon: false,
                      compact: true,
                    ),
                  ],
                ),
              ),

              // Ablaufdatum
              if (isPremium && expiryDate != null)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppTheme.spacingSmall,
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.calendar_today_outlined,
                        size: 20,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      const Text('Gültig bis:'),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Text(
                        DateFormat('dd.MM.yyyy').format(expiryDate),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),

              // Upgrade-Button für Nicht-Premium oder Basic/Pro-Nutzer
              if (!isPremium || (planType != 'unlimited' && planType != null))
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.spacingMedium),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _navigateToPremiumScreen(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                      ),
                      child: Text(isPremium ? 'Upgrade' : 'Premium aktivieren'),
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactStatusWidget(
    BuildContext context, {
    required bool isPremium,
    String? planType,
  }) {
    return InkWell(
      onTap: () => _navigateToPremiumScreen(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPremium
                ? Icons.workspace_premium
                : Icons.workspace_premium_outlined,
            color: isPremium ? _getPlanColor(planType) : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 4),
          Text(
            isPremium ? _getPlanName(planType) : 'Basic',
            style: TextStyle(
              color: isPremium ? _getPlanColor(planType) : Colors.grey,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return compact
        ? const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        )
        : const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorWidget(BuildContext context, Object error) {
    return compact
        ? const Icon(Icons.error_outline, color: Colors.red, size: 20)
        : Center(
          child: Text(
            'Fehler: ${error.toString()}',
            style: const TextStyle(color: Colors.red),
          ),
        );
  }

  void _navigateToPremiumScreen(BuildContext context) {
    Navigator.of(context).pushNamed('/premium-management');
  }

  String _getPlanName(String? planType) {
    switch (planType) {
      case 'basic':
        return 'Basic';
      case 'pro':
        return 'Pro';
      case 'unlimited':
        return 'Unlimited';
      default:
        return 'Basic';
    }
  }

  Color _getPlanColor(String? planType) {
    switch (planType) {
      case 'basic':
        return Colors.blue;
      case 'pro':
        return Colors.purple;
      case 'unlimited':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }
}

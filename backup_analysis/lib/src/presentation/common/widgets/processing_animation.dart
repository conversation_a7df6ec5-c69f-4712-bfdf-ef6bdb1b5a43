import 'dart:math';
import 'package:flutter/material.dart';

/// Hochwertige Bearbeitungs-Animation für Buttons
class ProcessingAnimationPainter extends CustomPainter {
  final double animation;
  final Color color;

  ProcessingAnimationPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Definiere mehrere Farben für eine hochwertigere Animation
    // Verwende mehr Farben für einen schöneren Farbverlauf
    final colors = [
      color.withValues(alpha: 0.9),
      color.withValues(alpha: 0.8),
      color.withValues(alpha: 0.7),
      color.withValues(alpha: 0.6),
      color.withValues(alpha: 0.5),
      color.withValues(alpha: 0.4),
    ];

    // Zeichne mehrere Kreise mit unterschiedlichen Radien und Farben
    for (int i = 0; i < colors.length; i++) {
      final paint =
          Paint()
            ..color = colors[i]
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0
            ..strokeCap = StrokeCap.round;

      // Berechne den Startwinkel für jede Schicht mit unterschiedlichen Geschwindigkeiten
      // Jede Schicht dreht sich mit einer leicht anderen Geschwindigkeit
      final startAngle = (animation * 2 * pi * (1.0 + i * 0.1)) + (i * pi / 6);

      // Zeichne einen Kreisbogen mit unterschiedlichen Längen
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - (i * 1.5)),
        startAngle,
        pi * (1.0 + i * 0.2), // Unterschiedliche Bogenlängen
        false,
        paint,
      );
    }

    // Zeichne einen pulsierenden Punkt in der Mitte mit schönerem Effekt
    // Berechne Opazität basierend auf der Animation (zwischen 0.6 und 0.8)
    final opacity = 0.6 + ((sin(animation * 3 * pi) + 1) / 2 * 0.2);
    final dotPaint =
        Paint()
          ..color = color.withValues(alpha: opacity)
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(
            BlurStyle.normal,
            2.0,
          ); // Weicher Rand

    // Zeichne zwei Kreise für einen Glow-Effekt
    canvas.drawCircle(
      center,
      radius * 0.35 * (0.8 + 0.2 * sin(animation * 2 * pi)),
      Paint()
        ..color = color.withValues(alpha: 0.3) // Glow-Effekt
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0),
    );

    canvas.drawCircle(
      center,
      radius * 0.25 * (0.8 + 0.2 * sin(animation * 2 * pi)),
      dotPaint,
    );
  }

  @override
  bool shouldRepaint(ProcessingAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation || oldDelegate.color != color;
  }
}

/// Widget zum einfachen Einbinden der Animation
class ProcessingAnimation extends StatefulWidget {
  final Color color;
  final double size;

  const ProcessingAnimation({
    super.key,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<ProcessingAnimation> createState() => _ProcessingAnimationState();
}

class _ProcessingAnimationState extends State<ProcessingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 3500,
      ), // Langsame Animation für besseren Effekt
    );

    // Starte die Animation sofort
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: ProcessingAnimationPainter(
              animation: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

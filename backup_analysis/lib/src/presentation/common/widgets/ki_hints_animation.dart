import 'package:flutter/material.dart';

/// Hochwertige Animation speziell für das Fenster "Zusätzliche Hinweise für KI"
class KIHintsAnimationPainter extends CustomPainter {
  final double animation;

  KIHintsAnimationPainter({required this.animation});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Definiere Farben für die Animation
    final pinkColor = Colors.pink.shade300;
    final goldColor = Colors.amber.shade300;
    final darkPinkColor = Colors.pink.shade700;
    final darkGoldColor = Colors.amber.shade700;

    // Zeichne einen fließenden Farbverlauf um das Feld
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Zeichne mehrere Linien mit unterschiedlichen Farben und Animationen
    for (int i = 0; i < 4; i++) {
      final sweepAngle = (animation * 6.28) + (i * 0.5);
      final startAngle = i * 0.5;

      paint.shader = SweepGradient(
        center: Alignment.center,
        startAngle: startAngle,
        endAngle: sweepAngle,
        colors: [
          pinkColor,
          goldColor,
          darkPinkColor,
          darkGoldColor,
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

      // Zeichne Rechteck mit abgerundeten Ecken
      final rect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: center,
          width: size.width - (i * 2),
          height: size.height - (i * 2),
        ),
        Radius.circular(8),
      );
      
      canvas.drawRRect(rect, paint);
    }

    // Zeichne subtile Glanzeffekte an den Ecken
    final glowPaint = Paint()
      ..color = goldColor.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

    // Obere linke Ecke
    canvas.drawCircle(
      Offset(10, 10),
      5 * (0.8 + 0.2 * animation),
      glowPaint,
    );

    // Obere rechte Ecke
    canvas.drawCircle(
      Offset(size.width - 10, 10),
      5 * (0.8 + 0.2 * (animation + 0.25)),
      glowPaint,
    );

    // Untere linke Ecke
    canvas.drawCircle(
      Offset(10, size.height - 10),
      5 * (0.8 + 0.2 * (animation + 0.5)),
      glowPaint,
    );

    // Untere rechte Ecke
    canvas.drawCircle(
      Offset(size.width - 10, size.height - 10),
      5 * (0.8 + 0.2 * (animation + 0.75)),
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(KIHintsAnimationPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

/// Widget für die Animation des KI-Hinweise-Fensters
class KIHintsAnimation extends StatefulWidget {
  final Widget child;
  final bool isAnimating;

  const KIHintsAnimation({super.key, 
    super..key,
    required this.child,
    required this.isAnimating,
  })

  @override
  State<KIHintsAnimation> createState() => _KIHintsAnimationState();
}

class _KIHintsAnimationState extends State<KIHintsAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000), // Langsame, flüssige Animation
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    if (widget.isAnimating) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(KIHintsAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAnimating && !_animationController.isAnimating) {
      _animationController.repeat();
    } else if (!widget.isAnimating && _animationController.isAnimating) {
      _animationController.stop();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Stack(
          children: [
            if (widget.isAnimating)
              Positioned.fill(
                child: CustomPaint(
                  painter: KIHintsAnimationPainter(
                    animation: _animation.value,
                  ),
                ),
              ),
            child!,
          ],
        );
      },
      child: widget.child,
    );
  }
}

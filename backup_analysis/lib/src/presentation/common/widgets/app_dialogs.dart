import 'package:flutter/material.dart';
import 'package:ki_test/src/core/l10n/app_localizations.dart';

/// <PERSON><PERSON><PERSON> von wiederverwendbaren, anwendungsweiten Dialogen.
class AppDialogs {
  /// Zeigt einen einfachen Lade-Dialog an.
  static void showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false, // Verhindert Schließen durch Tippen daneben
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16), // Abstand
              Text(message),
            ],
          ),
        );
      },
    );
  }

  /// Versteckt den aktuell angezeigten Dialog (typischerweise den Lade-Dialog).
  static void hideLoadingDialog(BuildContext context) {
    // Prüfe, ob ein Dialog offen ist, bevor versucht wird, ihn zu schließen
    if (Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  /// Zeigt einen Fehler-Dialog an.
  static void showErrorDialog(
    BuildContext context,
    String message, {
    String? title,
    String? buttonText,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Text(title ?? AppLocalizations.of(context).error),
            ],
          ),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: Text(buttonText ?? AppLocalizations.of(context).ok),
              onPressed: () {
                Navigator.of(context).pop(); // Schließt den Dialog
              },
            ),
          ],
        );
      },
    );
  }

  /// Zeigt einen Bestätigungs-Dialog an.
  ///
  /// Ruft `onConfirm` auf, wenn der Benutzer bestätigt.
  static void showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String content,
    required VoidCallback onConfirm,
    String? confirmText,
    String? cancelText,
  }) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: <Widget>[
            TextButton(
              child: Text(cancelText ?? localizations.cancel),
              onPressed: () {
                Navigator.of(context).pop(); // Schließt den Dialog
              },
            ),
            ElevatedButton(
              child: Text(confirmText ?? localizations.confirm),
              onPressed: () {
                Navigator.of(context).pop(); // Schließt den Dialog
                onConfirm(); // Führt die Bestätigungsaktion aus
              },
            ),
          ],
        );
      },
    );
  }

  /// Zeigt einen Erfolgs-Dialog an.
  static void showSuccessDialog(
    BuildContext context,
    String title,
    String message, {
    String? buttonText,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: Text(buttonText ?? AppLocalizations.of(context).ok),
              onPressed: () {
                Navigator.of(context).pop(); // Schließt den Dialog
              },
            ),
          ],
        );
      },
    );
  }

  /// Zeigt einen Informations-Dialog an.
  static void showInfoDialog(
    BuildContext context,
    String title,
    String message, {
    String? buttonText,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.info, color: Theme.of(context).colorScheme.primary),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: Text(buttonText ?? AppLocalizations.of(context).ok),
              onPressed: () {
                Navigator.of(context).pop(); // Schließt den Dialog
              },
            ),
          ],
        );
      },
    );
  }
}

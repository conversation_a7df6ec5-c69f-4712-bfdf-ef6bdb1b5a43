import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/premium_required_error.dart';
import 'package:ki_test/src/application/providers/services_providers.dart'; // Import für adServiceProvider
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';

/// Dialog, der angezeigt wird, wenn ein Benutzer versucht, ein Premium-Feature zu nutzen,
/// aber keinen Premium-Zugriff hat.
class PremiumRequiredDialog extends ConsumerWidget {
  /// <PERSON>, der den Dialog ausgelöst hat
  final PremiumRequiredError error;

  /// Callback, der aufgerufen wird, wenn der Benutzer eine Werbung angesehen hat
  final VoidCallback? onAdWatched;

  /// Erstellt einen neuen PremiumRequiredDialog
  const PremiumRequiredDialog({
    super.key,
    required this.error,
    this.onAdWatched,
  });

  /// <PERSON>ei<PERSON> den <PERSON> an
  static Future<String?> show(
    BuildContext context, {
    required PremiumRequiredError error,
    VoidCallback? onAdWatched,
  }) {
    return showDialog<String>(
      context: context,
      builder:
          (context) =>
              PremiumRequiredDialog(error: error, onAdWatched: onAdWatched),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Bestimme den Feature-Text basierend auf dem Feature
    String featureText = 'Premium-Feature';
    String benefitText =
        'Genieße alle Premium-Funktionen mit einem Abonnement.';

    switch (error.feature) {
      case 'aiCoverLetter':
        featureText = 'KI-Anschreiben-Generator';
        benefitText =
            'Lass dir automatisch personalisierte Anschreiben erstellen, die perfekt auf den Job und dein Profil abgestimmt sind.';
        break;
      case 'aiJobSearch':
        featureText = 'KI-optimierte Jobsuche';
        benefitText =
            'Finde Jobs, die perfekt zu deinen Fähigkeiten und Erfahrungen passen, mit unserer KI-gestützten Suche.';
        break;
      case 'unlimitedFavorites':
        featureText = 'Unbegrenzte Favoriten';
        benefitText =
            'Speichere unbegrenzt viele Jobs als Favoriten und behalte alle interessanten Stellen im Blick.';
        break;
      case 'adFree':
        featureText = 'Werbefreie Nutzung';
        benefitText =
            'Genieße eine störungsfreie Erfahrung ohne Werbeeinblendungen.';
        break;
      case 'insights':
        featureText = 'Premium-Insights';
        benefitText =
            'Erhalte detaillierte Statistiken und Einblicke zu deiner Jobsuche und deinen Bewerbungen.';
        break;
    }

    return AlertDialog(
      title: Text('Zugriff auf $featureText'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Für $featureText benötigst du Premium-Zugriff.'),
          const SizedBox(height: 8),
          Text(benefitText),
          const SizedBox(height: 16),
          const Text('Du hast folgende Möglichkeiten:'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop('cancel'),
          child: const Text('Abbrechen'),
        ),
        if (onAdWatched != null)
          TextButton(
            onPressed: () async {
              await _showAd(context, ref);
              if (context.mounted) {
                Navigator.of(context).pop('ad');
                onAdWatched?.call();
              }
            },
            child: const Text('Werbung ansehen für 24h Zugriff'),
          ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop('premium');
            _navigateToPremiumScreen(context);
          },
          child: const Text('Premium abonnieren'),
        ),
      ],
    );
  }

  /// Zeigt eine Werbung an und gewährt temporären Zugriff auf das Feature
  Future<void> _showAd(BuildContext context, WidgetRef ref) async {
    // Verwende den AdService, um eine Werbung anzuzeigen
    final adService = ref.read(adServiceProvider);

    try {
      await adService.loadAndShowRewardedAd(
        context: context, // Context übergeben für Ladeanimation
        onUserEarnedRewardCallback: () {
          // Wird aufgerufen, wenn der Benutzer die Belohnung erhalten hat
          // (Werbung vollständig angesehen)
          debugPrint(
            'Benutzer hat Werbung vollständig angesehen und erhält Zugriff',
          );
        },
        onAdFailedToLoadCallback: () {
          // Wird aufgerufen, wenn die Werbung nicht geladen werden konnte
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Werbung konnte nicht geladen werden. Bitte versuche es später erneut.',
                ),
              ),
            );
          }
        },
        onAdFailedToShowCallback: () {
          // Wird aufgerufen, wenn die Werbung nicht angezeigt werden konnte
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Werbung konnte nicht angezeigt werden. Bitte versuche es später erneut.',
                ),
              ),
            );
          }
        },
      );
    } catch (e) {
      // Fehlerbehandlung
      debugPrint('Fehler beim Anzeigen der Werbung: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Fehler beim Anzeigen der Werbung. Bitte versuche es später erneut.',
            ),
          ),
        );
      }
    }

    // Gewähre temporären Zugriff auf das Feature
    final premiumFeatureService = ref.read(premiumFeatureServiceProvider);

    PremiumFeature feature;
    switch (error.feature) {
      case 'aiCoverLetter':
        feature = PremiumFeature.aiCoverLetter;
        break;
      case 'aiJobSearch':
        feature = PremiumFeature.aiJobSearch;
        break;
      case 'unlimitedFavorites':
        feature = PremiumFeature.unlimitedFavorites;
        break;
      case 'adFree':
        feature = PremiumFeature.adFree;
        break;
      case 'insights':
        feature = PremiumFeature.insights;
        break;
      default:
        feature = PremiumFeature.aiCoverLetter;
    }

    await premiumFeatureService.grantAdBasedAccess(feature);
  }

  /// Navigiert zur Premium-Verwaltung
  void _navigateToPremiumScreen(BuildContext context) {
    Navigator.of(context).pushNamed('/premium-management');
  }
}

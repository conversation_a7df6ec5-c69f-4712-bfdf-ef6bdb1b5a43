// Import für kDebugMode
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// Import für adServiceProvider
import 'package:ki_test/src/application/providers/services_providers.dart'; // Import für paymentServiceProvider
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:go_router/go_router.dart';

// Logger Instanz
final _log = getLogger('PremiumDialogs');

/// Zeigt einen Dialog an, der dem Nutzer anbietet, Premium zu erwerben
/// oder eine Werbung anzusehen, um eine Aktion einmalig durchzuführen.
///
/// [onAdWatched] wird aufgerufen, nachdem der Nutzer die Werbung erfolgreich angesehen hat.
/// [onAdStarted] wird aufgerufen, wenn die Werbung gestartet wird.
/// [onAdEnded] wird aufgerufen, wenn die Werbung beendet wird.
Future<void> showPremiumOrAdDialog({
  required BuildContext context,
  required WidgetRef ref,
  required VoidCallback onAdWatched,
  VoidCallback? onAdStarted,
  VoidCallback? onAdEnded,
  String title = 'Premium-Funktion',
  String content =
      'Diese Funktion ist für Premium-Nutzer verfügbar. Möchtest du Premium erwerben oder eine Werbung ansehen, um sie einmalig zu nutzen?',
}) async {
  final adService = ref.read(adServiceProvider);

  _log.i('Zeige Premium-Dialog an');

  return showDialog<void>(
    context: context,
    barrierDismissible: true, // Benutzer kann Dialog wegklicken
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: ListBody(children: <Widget>[Text(content)]),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('Abbrechen'),
            onPressed: () {
              _log.i('Premium-Dialog: Abbruch gewählt');
              Navigator.of(dialogContext).pop();
            },
          ),
          TextButton(
            child: const Text('Werbung ansehen'),
            onPressed: () async {
              _log.i('Premium-Dialog: Werbung ansehen gewählt');
              Navigator.of(
                dialogContext,
              ).pop(); // Ursprünglichen Premium-Dialog schließen

              // *** NEU: Lade-Dialog VOR dem try/finally anzeigen ***
              AppDialogs.showLoadingDialog(context, "Lade Werbung...");

              try {
                // Callback für den Start der Werbung aufrufen
                onAdStarted?.call();
                _log.i("Werbung wird gestartet, onAdStarted aufgerufen");

                // await hier lassen, damit finally erst danach ausgeführt wird
                await adService.loadAndShowRewardedAd(
                  onAdLoadedCallback: () {
                    _log.i('Rewarded Ad geladen und wird angezeigt.');
                  },
                  onAdFailedToLoadCallback: /*(LoadAdError error)*/ () {
                    // *** KORREKTUR: pop() hier entfernen ***
                    // if (context.mounted) Navigator.pop(context);
                    _log.e('Rewarded Ad konnte nicht geladen werden.');
                    // Fehlerdialog wird NACH finally angezeigt, falls mounted
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      // Sicherstellen, dass es nach dem Build passiert
                      if (context.mounted) {
                        AppDialogs.showErrorDialog(
                          context,
                          'Werbung konnte nicht geladen werden. Versuchen Sie es später erneut.',
                        );
                      }
                    });
                  },
                  onUserEarnedRewardCallback: /*(RewardItem reward)*/ () {
                    // *** KORREKTUR: pop() hier entfernen ***
                    // if (context.mounted) Navigator.pop(context);
                    _log.i('Nutzer hat Belohnung durch Werbung erhalten.');
                    // onAdWatched wird NACH finally aufgerufen
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      // Sicherstellen, dass es nach dem Build passiert
                      if (context.mounted) {
                        // Callback für das Ende der Werbung aufrufen
                        onAdEnded?.call();
                        _log.i("Werbung wurde beendet, onAdEnded aufgerufen");

                        // Sofort onAdWatched aufrufen, ohne Verzögerung
                        onAdWatched();
                      }
                    });
                  },
                  onAdFailedToShowCallback: /*(AdError error)*/ () {
                    // *** KORREKTUR: pop() hier entfernen ***
                    // if (context.mounted) Navigator.pop(context);
                    _log.e('Rewarded Ad konnte nicht angezeigt werden.');
                    // Fehlerdialog wird NACH finally angezeigt, falls mounted
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      // Sicherstellen, dass es nach dem Build passiert
                      if (context.mounted) {
                        AppDialogs.showErrorDialog(
                          context,
                          'Werbung konnte nicht angezeigt werden.',
                        );
                      }
                    });
                  },
                  onAdDismissedCallback: () {
                    // *** KORREKTUR: pop() hier entfernen ***
                    // if (context.mounted) Navigator.pop(context);
                    _log.i("Rewarded Ad wurde geschlossen (dismissed).");

                    // Callback für das Ende der Werbung aufrufen
                    onAdEnded?.call();
                    _log.i(
                      "Werbung wurde beendet (dismissed), onAdEnded aufgerufen",
                    );

                    // Hier nichts weiter tun, wichtig ist, dass der Lade-Dialog in finally geschlossen wird
                  },
                );
              } catch (e, s) {
                // Fehler wird bereits geloggt, Lade-Dialog wird in finally geschlossen
                _log.e(
                  'Fehler beim Anzeigen der Rewarded Ad',
                  error: e,
                  stackTrace: s,
                );
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  // Sicherstellen, dass es nach dem Build passiert
                  if (context.mounted) {
                    AppDialogs.showErrorDialog(
                      context,
                      'Ein Fehler ist beim Laden der Werbung aufgetreten.',
                    );
                  }
                });
              } finally {
                // *** NEU: Lade-Dialog IMMER hier schließen ***
                // Wichtig: Prüfen, ob der Loading-Dialog noch aktiv ist, bevor man ihn schließt.
                // Da wir keinen direkten Weg haben, den Zustand des Dialogs zu prüfen,
                // verlassen wir uns darauf, dass er nur einmal geschlossen wird.
                // Ein einfacher Pop sollte hier genügen, WENN der Dialog noch offen ist.
                if (context.mounted) {
                  Navigator.pop(context); // Schließt den Lade-Dialog
                  _log.i(
                    "Lade-Dialog (Werbung) wurde im finally-Block geschlossen.",
                  );
                } else {
                  _log.w(
                    "Lade-Dialog (Werbung) konnte nicht im finally-Block geschlossen werden, da context nicht mehr mounted ist.",
                  );
                }
              }
            },
          ),
          ElevatedButton(
            child: const Text('Premium holen'),
            onPressed: () {
              _log.i('Premium-Dialog: Premium holen gewählt');
              Navigator.of(dialogContext).pop(); // Aktuellen Dialog schließen
              // Navigiere zur Premium-Verwaltung
              GoRouter.of(context).push('/premium-management');
            },
          ),
        ],
      );
    },
  );
}

/// Zeigt einen Dialog an, der darauf hinweist, dass eine Funktion nur für Premium-Nutzer
/// verfügbar ist und anbietet, Premium zu erwerben.
Future<void> showPremiumRequiredDialog({
  required BuildContext context,
  required WidgetRef ref,
  String title = 'Premium erforderlich',
  String content =
      'Diese Funktion ist exklusiv für Premium-Nutzer. Möchtest du jetzt ein Upgrade durchführen?',
}) async {
  _log.i('Zeige Premium-Required-Dialog an');

  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: ListBody(children: <Widget>[Text(content)]),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('Später'),
            onPressed: () {
              _log.i('Premium-Required-Dialog: Später gewählt');
              Navigator.of(dialogContext).pop();
            },
          ),
          ElevatedButton(
            child: const Text('Upgrade zu Premium'),
            onPressed: () {
              _log.i('Premium-Required-Dialog: Upgrade gewählt');
              Navigator.of(dialogContext).pop(); // Dialog schließen
              // Navigiere zur Premium-Verwaltung
              GoRouter.of(context).push('/premium-management');
            },
          ),
        ],
      );
    },
  );
}

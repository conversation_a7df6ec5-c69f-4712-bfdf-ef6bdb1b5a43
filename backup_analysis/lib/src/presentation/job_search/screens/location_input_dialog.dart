import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:location/location.dart' as loc;
import 'package:geocoding/geocoding.dart';
import '../../../core/utils/logging.dart';

class LocationInputDialog extends HookConsumerWidget {
  final String? initialLocation;
  final bool showLocationButton;

  const LocationInputDialog({
    super.key,
    this.initialLocation,
    this.showLocationButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locationController = useTextEditingController(text: initialLocation);
    final focusNode = useFocusNode();
    final isLoading = useState(false);

    // Fokus auf das Textfeld setzen, wenn der Dialog geöffnet wird
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        focusNode.requestFocus();
      });
      return null;
    }, const []);

    return AlertDialog(
      title: const Text('Standort ändern'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: locationController,
            focusNode: focusNode,
            decoration: const InputDecoration(
              hintText: 'Ort, PLZ oder "Remote" eingeben',
              prefixIcon: Icon(Icons.search),
            ),
            // Hier könnte später die Autocomplete-Logik eingehängt werden
            // onChanged: (value) { /* Vorschläge laden */ },
            onSubmitted: (value) {
              // Dialog mit eingegebenem Wert schließen, wenn Enter gedrückt wird
              Navigator.of(context).pop(value.trim());
            },
          ),
          if (showLocationButton) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed:
                  isLoading.value
                      ? null
                      : () async {
                        isLoading.value = true;
                        final location = await _getCurrentLocation(context);
                        isLoading.value = false;
                        if (location != null && context.mounted) {
                          Navigator.of(context).pop(location);
                        }
                      },
              icon:
                  isLoading.value
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.my_location),
              label: const Text('Meinen Standort verwenden'),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed:
              () =>
                  Navigator.of(context).pop(null), // Abbrechen gibt null zurück
          child: const Text('Abbrechen'),
        ),
        TextButton(
          // Button, um den Standortfilter zu entfernen (leeren String zurückgeben)
          onPressed: () => Navigator.of(context).pop(''),
          child: const Text('Entfernen'),
        ),
        ElevatedButton(
          onPressed: () {
            // Dialog mit eingegebenem Wert schließen
            Navigator.of(context).pop(locationController.text.trim());
          },
          child: const Text('Übernehmen'),
        ),
      ],
    );
  }

  Future<String?> _getCurrentLocation(BuildContext context) async {
    final log = getLogger('LocationInputDialog._getCurrentLocation');
    loc.Location location = loc.Location();

    bool serviceEnabled;
    loc.PermissionStatus permissionGranted;
    loc.LocationData locationData;

    log.i("Prüfe Standortdienste...");
    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      log.w("Standortdienste sind deaktiviert. Fordere Aktivierung an...");
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        log.e("Standortdienste wurden nicht aktiviert.");
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Bitte aktiviere die Standortdienste.'),
            ),
          );
        }
        return null;
      }
    }

    log.i("Prüfe Standortberechtigung...");
    permissionGranted = await location.hasPermission();
    if (permissionGranted == loc.PermissionStatus.denied) {
      log.w("Standortberechtigung nicht erteilt. Fordere Berechtigung an...");
      permissionGranted = await location.requestPermission();
      if (permissionGranted != loc.PermissionStatus.granted) {
        log.e("Standortberechtigung wurde nicht erteilt.");
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Standortberechtigung erforderlich.')),
          );
        }
        return null;
      }
    }

    if (permissionGranted == loc.PermissionStatus.granted) {
      try {
        log.i("Berechtigung erteilt. Ermittle Standort...");
        locationData = await location.getLocation();
        log.i(
          "Standortdaten erhalten: Lat=${locationData.latitude}, Lon=${locationData.longitude}",
        );

        if (locationData.latitude != null && locationData.longitude != null) {
          // Konvertiere Koordinaten in Ortsnamen (Reverse Geocoding)
          List<Placemark> placemarks = await placemarkFromCoordinates(
            locationData.latitude!,
            locationData.longitude!,
          );
          if (placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final city = placemark.locality;
            log.i("Ort ermittelt: $city");
            return city;
          } else {
            log.w("Kein Ortsname für Koordinaten gefunden.");
            return null;
          }
        } else {
          log.w("Ungültige Standortdaten erhalten.");
          return null;
        }
      } catch (e, stacktrace) {
        log.e("Fehler bei der Standortermittlung: $e", stackTrace: stacktrace);
        return null;
      }
    } else {
      log.e("Unbekannter Berechtigungsstatus: $permissionGranted");
      return null;
    }
  }
}

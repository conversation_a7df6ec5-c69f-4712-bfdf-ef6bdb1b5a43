import 'package:flutter/material.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/presentation/job_search/widgets/job_list_item.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Eine animierte Jobliste, die einen schönen Übergang zeigt, wenn sich die Jobs ändern
/// Die Jobs werden nacheinander von unten nach oben animiert
class AnimatedJobList extends StatefulWidget {
  final List<JobEntity> jobs;
  final bool isLoading;
  final bool isLoadingMore; // Neue Eigenschaft für das Laden weiterer Jobs
  final bool isSearching; // Neue Eigenschaft für die Suche im Hintergrund
  final String? error; // Neuer Parameter für Fehleranzeige
  final bool showExpandSearchButton;
  final Function()? onExpandSearchPressed;
  final Function()? onRetry; // Neuer Parameter für Retry-Button
  final int currentDistance;
  final int nextDistance;
  final ScrollController? scrollController;
  final Function()? onJobTap; // Callback, wenn ein Job angeklickt wird

  const AnimatedJobList({
    super.key,
    required this.jobs,
    this.isLoading = false,
    this.isLoadingMore = false, // Standardwert für isLoadingMore
    this.isSearching = false, // Standardwert für isSearching
    this.error, // Fehler (optional)
    this.showExpandSearchButton = false,
    this.onExpandSearchPressed,
    this.onRetry, // Callback für Retry-Button
    this.currentDistance = 25,
    this.nextDistance = 35,
    this.scrollController,
    this.onJobTap, // Neuer Parameter für den Callback
  });

  @override
  State<AnimatedJobList> createState() => _AnimatedJobListState();
}

class _AnimatedJobListState extends State<AnimatedJobList>
    with TickerProviderStateMixin {
  List<JobEntity> _currentJobs = [];
  bool _isAnimating = false;
  late ScrollController _scrollController;
  bool _isExternalScrollController = false;

  // Für die sequentielle Animation
  late AnimationController _listAnimationController;
  final Map<int, AnimationController> _itemControllers = {};
  final Map<int, Animation<Offset>> _itemAnimations = {};

  // Für die Übergangsanimation
  late AnimationController _transitionController;
  late Animation<double> _opacityAnimation;

  // Methode zum Entfernen des Fokus vom Suchfeld
  void _unfocusSearchField() {
    // Entferne den Fokus von allen Textfeldern
    FocusManager.instance.primaryFocus?.unfocus();
  }

  // Scroll-Handler, der den Fokus entfernt, wenn der Benutzer scrollt
  void _handleScroll() {
    // Entferne den Fokus vom Suchfeld bei jeder Scrollbewegung,
    // unabhängig von der Scrollposition
    _unfocusSearchField();

    // Debug-Log hinzufügen
    debugPrint("🔍 D/AnimatedJobList: Scroll erkannt - Fokus wird entfernt");
  }

  @override
  void initState() {
    super.initState();
    _currentJobs = widget.jobs;

    // Initialisiere den ScrollController
    if (widget.scrollController != null) {
      _scrollController = widget.scrollController!;
      _isExternalScrollController = true;
    } else {
      _scrollController = ScrollController();
      _isExternalScrollController = false;
    }

    // Füge einen Scroll-Listener hinzu
    _scrollController.addListener(_handleScroll);

    // Controller für die Übergangsanimation
    _transitionController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _transitionController, curve: Curves.easeOut),
    );

    // Controller für die sequentielle Animation der Liste
    _listAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Nur beim ersten Laden die Animation für alle Jobs initialisieren
    if (widget.jobs.isNotEmpty) {
      _initItemAnimations();
    }

    // Starte die Animation sofort
    _transitionController.forward();
    _listAnimationController.forward();
  }

  void _initItemAnimations() {
    // Bestehende Controller löschen
    for (final controller in _itemControllers.values) {
      controller.dispose();
    }
    _itemControllers.clear();
    _itemAnimations.clear();

    // Neue Controller für jedes Element erstellen
    for (int i = 0; i < _currentJobs.length; i++) {
      final delay = Duration(
        milliseconds: 50 * i,
      ); // Verzögerung für sequentielle Animation

      final controller = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 600),
      );

      final animation = Tween<Offset>(
        begin: const Offset(0, 0.5), // Von unten
        end: Offset.zero,
      ).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );

      _itemControllers[i] = controller;
      _itemAnimations[i] = animation;

      // Verzögerte Animation starten
      Future.delayed(delay, () {
        if (mounted && !controller.isAnimating) {
          try {
            controller.forward();
          } catch (e) {
            // Ignoriere Fehler, wenn der Controller bereits entsorgt wurde
          }
        }
      });
    }
  }

  @override
  void didUpdateWidget(AnimatedJobList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Wenn sich die Jobs geändert haben und wir nicht bereits animieren
    if (widget.jobs != oldWidget.jobs && !_isAnimating) {
      // Prüfen, ob neue Jobs hinzugefügt wurden (für Infinite Scrolling)
      if (widget.jobs.length > oldWidget.jobs.length &&
          oldWidget.jobs.isNotEmpty &&
          widget.jobs.isNotEmpty &&
          widget.jobs
              .sublist(0, oldWidget.jobs.length)
              .every(
                (job) => oldWidget.jobs.any((oldJob) => oldJob.id == job.id),
              )) {
        // Nur neue Jobs hinzufügen und animieren
        final newJobs = widget.jobs.sublist(oldWidget.jobs.length);

        setState(() {
          // Wichtig: Nicht _isAnimating auf true setzen, damit die Liste nicht verschwindet
          _currentJobs = widget.jobs; // Aktualisiere die gesamte Liste
        });

        // Animiere nur die neuen Jobs, ohne die bestehenden zu beeinflussen
        _animateNewJobs(newJobs, oldWidget.jobs.length);
      } else if (!widget.isLoading && !widget.isSearching) {
        // Vollständige Aktualisierung (z.B. bei Filteränderung oder neuer Suche)
        // Nur wenn nicht gerade geladen wird und keine Suche im Hintergrund läuft
        setState(() {
          _isAnimating = true;
          _currentJobs = widget.jobs;
        });

        // Übergangsanimation zurücksetzen und neu starten
        _transitionController.reset();
        _transitionController.forward();

        // Neue Animationen für die Elemente initialisieren
        _initItemAnimations();

        // Listenanimation zurücksetzen und neu starten
        _listAnimationController.reset();
        _listAnimationController.forward().then((_) {
          if (mounted) {
            setState(() {
              _isAnimating = false;
            });
          }
        });
      } else {
        // Wenn wir gerade laden, einfach nur die Jobs aktualisieren ohne Animation
        setState(() {
          _currentJobs = widget.jobs;
        });
      }
    }
  }

  // Neue Methode zum Animieren nur der neuen Jobs
  void _animateNewJobs(List<JobEntity> newJobs, int startIndex) {
    // Erstelle Animationen nur für die neuen Jobs
    for (int i = 0; i < newJobs.length; i++) {
      final index = startIndex + i;
      final delay = Duration(milliseconds: 50 * i);

      final controller = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 600),
      );

      final animation = Tween<Offset>(
        begin: const Offset(0, 0.5), // Von unten
        end: Offset.zero,
      ).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );

      _itemControllers[index] = controller;
      _itemAnimations[index] = animation;

      // Verzögerte Animation starten
      Future.delayed(delay, () {
        if (mounted && !controller.isAnimating) {
          try {
            controller.forward();
          } catch (e) {
            // Ignoriere Fehler, wenn der Controller bereits entsorgt wurde
          }
        }
      });
    }

    // Nach Abschluss der Animation den Status zurücksetzen
    Future.delayed(Duration(milliseconds: 600 + newJobs.length * 50), () {
      if (mounted) {
        setState(() {
          _isAnimating = false;
        });
      }
    });
  }

  @override
  void dispose() {
    // Entferne den Scroll-Listener
    _scrollController.removeListener(_handleScroll);

    // Entsorge den ScrollController, wenn er nicht extern ist
    if (!_isExternalScrollController) {
      _scrollController.dispose();
    }

    _transitionController.dispose();
    _listAnimationController.dispose();

    // Alle Element-Controller entsorgen
    for (final controller in _itemControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  // Hilfsmethode für Fehlermeldungen
  String _getErrorMessage(String error) {
    final errorString = error.toLowerCase();
    if (errorString.contains('verbindungsfehler') ||
        errorString.contains('netzwerkfehler') ||
        errorString.contains('socket') ||
        errorString.contains('host lookup') ||
        errorString.contains('connection')) {
      return 'Keine Internetverbindung';
    } else if (errorString.contains('timeout')) {
      return 'Zeitüberschreitung';
    } else if (errorString.contains('500') || errorString.contains('503')) {
      return 'Server derzeit nicht verfügbar';
    }
    return 'Ein unerwarteter Fehler ist aufgetreten.';
  }

  // Hilfsmethode für Aktionsmeldungen
  String _getActionMessage(String error) {
    final errorString = error.toLowerCase();
    if (errorString.contains('verbindungsfehler') ||
        errorString.contains('netzwerkfehler') ||
        errorString.contains('socket') ||
        errorString.contains('host lookup') ||
        errorString.contains('connection')) {
      return 'Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.';
    } else if (errorString.contains('timeout')) {
      return 'Der Server antwortet zu langsam. Bitte versuchen Sie es später erneut.';
    } else if (errorString.contains('500') || errorString.contains('503')) {
      return 'Die Jobbörse ist derzeit nicht erreichbar. Bitte versuchen Sie es später erneut.';
    }
    return 'Bitte versuchen Sie es später erneut.';
  }

  @override
  Widget build(BuildContext context) {
    // Zeige die animierte Liste an
    return Stack(
      children: [
        // Die Hauptliste oder "Keine Jobs gefunden"-Nachricht
        if ((widget.isLoading || widget.isSearching) && widget.jobs.isEmpty)
          // Ladeindikator in der Liste anzeigen, nicht die gesamte Seite ersetzen
          const Center(child: CircularProgressIndicator())
        else if (widget.error != null && widget.jobs.isEmpty)
          // Fehleranzeige in der Liste anzeigen, nicht die gesamte Seite ersetzen
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.cloud_off,
                    size: 64,
                    color: Theme.of(context).colorScheme.error.withAlpha(204),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _getErrorMessage(widget.error!),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getActionMessage(widget.error!),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: widget.onRetry,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Erneut versuchen'),
                  ),
                ],
              ),
            ),
          )
        else if (!widget.isLoading &&
            !widget.isSearching &&
            widget.jobs.isEmpty)
          // "Keine Jobs gefunden"-Nachricht anzeigen
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary.withAlpha(128),
                ),
                const SizedBox(height: 16),
                Text(
                  'Keine Jobs gefunden',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Versuche es mit anderen Suchbegriffen oder Filtern',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (widget.showExpandSearchButton) ...[
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.location_searching, size: 18),
                    label: Text(
                      'Suche auf ${widget.nextDistance} km erweitern',
                    ),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      backgroundColor: Theme.of(
                        context,
                      ).colorScheme.primary.withAlpha(26),
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 1,
                        ),
                      ),
                    ),
                    onPressed: widget.onExpandSearchPressed,
                  ),
                ],
              ],
            ),
          )
        else
          // Normale animierte Liste
          AnimatedBuilder(
            animation: _transitionController,
            builder: (context, child) {
              return Opacity(
                opacity: _opacityAnimation.value,
                child: _buildAnimatedList(),
              );
            },
          ),

        // Ladeindikator für "Mehr laden" am unteren Rand
        if (widget.isLoadingMore)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              color: Theme.of(
                context,
              ).scaffoldBackgroundColor.withAlpha(204), // 0.8 * 255 = 204
              child: const Center(
                child: SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(strokeWidth: 2.0),
                ),
              ),
            ),
          ),

        // Ladeindikator für Suche im Hintergrund (am oberen Rand)
        if (widget.isSearching)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SizedBox(
              height: 3,
              child: LinearProgressIndicator(
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAnimatedList() {
    // Berechne das Padding basierend auf der Tastaturhöhe
    final bottomPadding =
        MediaQuery.of(context).viewInsets.bottom > 0
            ? 8.0 // Minimales Padding, wenn Tastatur sichtbar ist
            : kBottomNavigationBarHeight -
                16; // Normales Padding, wenn Tastatur nicht sichtbar ist

    // Verwende einen GestureDetector, um alle Arten von Gesten zu erkennen
    return GestureDetector(
      // Reagiere auf vertikale Drag-Gesten (Scrollen)
      onVerticalDragStart: (_) {
        _unfocusSearchField();
        debugPrint(
          "🔍 D/AnimatedJobList: Vertikale Drag-Geste erkannt - Fokus wird entfernt",
        );
      },
      // Reagiere auch auf Tap-Gesten
      onTap: () {
        _unfocusSearchField();
        debugPrint(
          "🔍 D/AnimatedJobList: Tap-Geste erkannt - Fokus wird entfernt",
        );
      },
      // Reagiere auf gedrückt halten (wichtig für dein Anwendungsfall)
      onLongPress: () {
        _unfocusSearchField();
        debugPrint(
          "🔍 D/AnimatedJobList: Long-Press-Geste erkannt - Fokus wird entfernt",
        );
      },
      // Reagiere sofort auf den Beginn eines Long-Press
      onLongPressStart: (_) {
        _unfocusSearchField();
        debugPrint(
          "🔍 D/AnimatedJobList: Long-Press-Start erkannt - Fokus wird entfernt",
        );
      },
      child: ListView.builder(
        controller: _scrollController, // Verwende unseren ScrollController
        padding: EdgeInsets.only(
          left: AppTheme.spacingMedium - AppTheme.spacingXSmall, // Etwas vergrößert
          right: AppTheme.spacingMedium - AppTheme.spacingXSmall, // Etwas vergrößert
          bottom: bottomPadding,
          top: AppTheme.spacingSmall,
        ),
        itemCount:
            _currentJobs.length + (widget.showExpandSearchButton ? 1 : 0),
        itemBuilder: (context, index) {
          // "Umkreis erweitern" Button am Ende
          if (widget.showExpandSearchButton && index == _currentJobs.length) {
            return FadeTransition(
              opacity: _opacityAnimation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.3),
                  end: Offset.zero,
                ).animate(
                  CurvedAnimation(
                    parent: _listAnimationController,
                    curve: Curves.easeOutCubic,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Center(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.location_searching, size: 18),
                      label: Text(
                        'Suche auf ${widget.nextDistance} km erweitern',
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        backgroundColor: Theme.of(
                          context,
                        ).colorScheme.primary.withAlpha(26),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(
                            color: Theme.of(context).colorScheme.primary,
                            width: 1,
                          ),
                        ),
                      ),
                      onPressed: widget.onExpandSearchPressed,
                    ),
                  ),
                ),
              ),
            );
          }

          // Reguläres Job-Listenelement
          if (index >= _currentJobs.length) {
            return Container();
          }

          final job = _currentJobs[index];

          // Prüfen, ob für diesen Job eine Animation existiert
          final hasAnimation = _itemAnimations.containsKey(index);

          // Wenn keine Animation existiert, zeige das Element ohne Animation an
          if (!hasAnimation) {
            return JobListItem(
              job: job,
              onTap: () {
                // Rufe den Callback auf, wenn ein Job angeklickt wird
                if (widget.onJobTap != null) {
                  widget.onJobTap!();
                }

                // Navigation zu Job Details
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => JobDetailScreen(
                            jobRefnr: job.id,
                            jobTitle: job.title ?? 'Job Details',
                            sourceUrl: job.sourceUrl,
                            jobEntity: job,
                          ),
                  ),
                );
              },
            );
          }

          // Animiertes Listenelement für neue Jobs
          return SlideTransition(
            position: _itemAnimations[index]!,
            child: FadeTransition(
              opacity: _itemControllers[index]!.drive(
                Tween<double>(begin: 0.0, end: 1.0),
              ),
              child: JobListItem(
                job: job,
                onTap: () {
                  // Rufe den Callback auf, wenn ein Job angeklickt wird
                  if (widget.onJobTap != null) {
                    widget.onJobTap!();
                  }

                  // Navigation zu Job Details
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) => JobDetailScreen(
                            jobRefnr: job.id,
                            jobTitle: job.title ?? 'Job Details',
                            sourceUrl: job.sourceUrl,
                            jobEntity: job,
                          ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

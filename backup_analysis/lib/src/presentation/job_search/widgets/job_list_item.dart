import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Import für ConsumerWidget
import 'package:intl/intl.dart'; // Für Datumsformatierung
// import 'package:url_launcher/url_launcher.dart'; // Nicht mehr hier benötigt
import '../../../core/theme/app_theme.dart';
import '../../../domain/entities/job_entity.dart'; // <<< Korrigierter Importpfad (3 Ebenen)
// Wieder importieren
import '../../../application/providers/favorites_provider.dart'; // Import Favoriten-Provider
import '../../../application/providers/applied_jobs_provider.dart'; // Import für beworbene Jobs
// import '../../job_detail/screens/job_detail_screen.dart'; // Wird vorerst nicht mehr direkt navigiert

/// Ein Widget zur Darstellung eines einzelnen Jobs in einer Liste.
class JobListItem extends HookConsumerWidget {
  final JobEntity job;
  final VoidCallback onTap;

  const JobListItem({super.key, required this.job, required this.onTap});

  // _launchUrl nicht mehr hier benötigt

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final timeAgo =
        job.publishedDate != null ? _formatTimeAgo(job.publishedDate) : '';

    // Favoriten-Status als AsyncValue beobachten
    final favoritesState = ref.watch(favoritesProvider);
    final favoritesNotifier = ref.read(favoritesProvider.notifier);

    // Bestimme den isFavorite-Wert aus dem AsyncValue
    final isFavorite = favoritesState.maybeWhen(
      data: (favs) => favs.any((favJob) => favJob.id == job.id),
      orElse: () => false,
    );

    // Bestimme, ob der Button aktiv sein soll (nicht während des Ladens)
    final bool canToggleFavorite = favoritesState is AsyncData<List<JobEntity>>;

    // Bewerbungsstatus als AsyncValue beobachten
    final appliedJobsState = ref.watch(appliedJobsProvider);

    // Bestimme, ob der Job als beworben markiert ist
    final isApplied = appliedJobsState.maybeWhen(
      data: (appliedJobIds) => appliedJobIds.contains(job.id),
      orElse: () => false,
    );

    return Card(
      elevation: 2.0,
      // Kleines horizontales Margin für etwas schmalere Darstellung
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingXSmall,
        vertical: AppTheme.spacingXSmall,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: InkWell(
        // ----- Navigiere zur JobDetailScreen mit refnr und title -----
        onTap: onTap,
        // -------------------------------------------------------------
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          // Reduziertes horizontales Padding für breitere Darstellung
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSmall,
            vertical: AppTheme.spacingSmall,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Linke Spalte mit den Textinformationen
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Titel (wichtiger, größer)
                    Text(
                      job.title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4), // Reduziert von spacingXSmall
                    // Firma (etwas dezenter)
                    Text(
                      job.companyName,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                        fontSize: 13, // Etwas kleinere Schrift
                      ), // Dezenter
                    ),
                    const SizedBox(height: 4), // Reduziert von spacingXSmall
                    
                    // Verbesserte Zeile für Ort und Datum mit exakter Ausrichtung
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Ortsicon und Ortstext in einem Container mit fester Breite
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                size: 14,
                                color: colorScheme.outline,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  job.location,
                                  style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.outline,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Datumstext mit rechtsbündiger Ausrichtung, aber fester Breite
                        Container(
                          width: 90, // Feste Breite für Datumsbereich
                          alignment: Alignment.centerRight, // Rechtsbündige Ausrichtung
                          child: timeAgo.isNotEmpty
                            ? Text(
                                timeAgo,
                                style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.outline,
                                ),
                                textAlign: TextAlign.right, // Rechtsbündige Ausrichtung
                              )
                            : null,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Aktionsbereich mit fester Breite für konsistente Darstellung
              SizedBox(
                width: 40,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Favoriten-Button
                    IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      iconSize: 30,
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color:
                            isFavorite
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey,
                      ),
                      onPressed:
                          canToggleFavorite
                              ? () {
                                if (isFavorite) {
                                  favoritesNotifier.removeFavorite(job.id);
                                } else {
                                  favoritesNotifier.addFavorite(job);
                                }
                              }
                              : null,
                      tooltip:
                          canToggleFavorite
                              ? (isFavorite
                                  ? 'Von Favoriten entfernen'
                                  : 'Zu Favoriten hinzufügen')
                              : 'Favoritenstatus wird geladen',
                    ),
                    // Kleiner Abstand zwischen Herz und Häkchen
                    const SizedBox(height: 4),
                    // Beworben-Status als einfaches Icon (wenn beworben)
                    if (isApplied)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.green.shade700,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: const Padding(
                          padding: EdgeInsets.all(4.0),
                          child: Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    // Wenn nicht beworben, fügen wir einen leeren Platzhalter ein,
                    // damit die Position des Herzicons konsistent bleibt
                    if (!isApplied)
                      const SizedBox(height: 22), // Entspricht etwa der Höhe des Häkchens
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Formatiert ein Datum als relative Zeitangabe (z.B. "vor 2 Tagen")
  String _formatTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays >= 7) {
      return DateFormat('dd.MM.yyyy').format(date);
    } else if (difference.inDays >= 1) {
      return 'vor ${difference.inDays} Tag${difference.inDays > 1 ? 'en' : ''}';
    } else if (difference.inHours >= 1) {
      return 'vor ${difference.inHours} Std.';
    } else if (difference.inMinutes >= 1) {
      return 'vor ${difference.inMinutes} Min.';
    } else {
      return 'gerade eben';
    }
  }
}

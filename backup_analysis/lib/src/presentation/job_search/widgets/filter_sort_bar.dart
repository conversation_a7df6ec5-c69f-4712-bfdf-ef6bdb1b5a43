import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Hinzufügen für HookConsumerWidget
import '../../../core/theme/app_theme.dart';

// Zu HookConsumerWidget ändern
class FilterSortBar extends HookConsumerWidget {
  final Map<String, String> activeFilters;
  final Function(Map<String, String>) onFiltersApplied;
  final Function(String) onSortChanged;

  const FilterSortBar({
    super.key,
    required this.activeFilters,
    required this.onFiltersApplied,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) { // WidgetRef hinzufügen
    // State für den Distanz-Slider
    final initialDistance = double.tryParse(activeFilters['distance'] ?? '25') ?? 25.0;
    // Stelle sicher, dass der Initialwert im gültigen Bereich liegt (1-100)
    final clampedInitialDistance = initialDistance.clamp(1.0, 100.0);
    final distanceValue = useState<double>(clampedInitialDistance);
    
    // State für den aktiven Status des "Jobs ohne Vorerfahrung" Buttons
    final noExperienceJobsActive = useState(activeFilters['noExperienceJobs'] == 'true');

    // State für den aktiven Status des "Nur Helferjobs" Buttons (NEU)
    final onlyHelperJobsActive = useState(activeFilters['onlyHelperJobs'] == 'true');

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMedium,
        vertical: AppTheme.spacingSmall,
      ),
      decoration: BoxDecoration(
         color: Theme.of(context).brightness == Brightness.dark
            ? AppTheme.surfaceDarkColor
            : Colors.white,
        border: Border(bottom: BorderSide(color: Theme.of(context).brightness == Brightness.dark
            ? AppTheme.borderDarkColor
            : Colors.grey[300]!)),
      ),
      child: Column( // Hauptlayout zu Column ändern
        mainAxisSize: MainAxisSize.min,
        children: [
          // Erste Reihe mit Filtern (Buttons)
          SingleChildScrollView( // Damit es bei kleinen Bildschirmen scrollbar ist
            scrollDirection: Axis.horizontal,
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween, // Entfernt für Scrolling
              children: [
                // "Jobs ohne Vorerfahrung" Button
                ElevatedButton.icon(
                  icon: Icon(
                    noExperienceJobsActive.value ? Icons.check_circle : Icons.circle_outlined, 
                    size: 18, 
                    color: noExperienceJobsActive.value ? Colors.white : Theme.of(context).colorScheme.primary
                  ),
                  label: Text(
                    'Ohne Vorerfahrung', // Kürzerer Text
                    style: TextStyle(
                      color: noExperienceJobsActive.value ? Colors.white : Theme.of(context).colorScheme.primary,
                      fontSize: 13,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: noExperienceJobsActive.value 
                        ? Theme.of(context).colorScheme.primary 
                        : Theme.of(context).colorScheme.surface,
                    elevation: noExperienceJobsActive.value ? 2 : 0,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(color: Theme.of(context).colorScheme.primary, width: 1),
                    ),
                  ),
                  onPressed: () {
                    final newValue = !noExperienceJobsActive.value;
                    noExperienceJobsActive.value = newValue;
                    final newFilters = Map<String, String>.from(activeFilters);
                    if (newValue) {
                      newFilters['noExperienceJobs'] = 'true';
                      // Deaktiviere Helferjobs, wenn "Ohne Vorerfahrung" aktiviert wird
                      if (onlyHelperJobsActive.value) {
                          onlyHelperJobsActive.value = false;
                          newFilters.remove('onlyHelperJobs');
                      }
                    } else {
                      newFilters.remove('noExperienceJobs');
                    }
                    onFiltersApplied(newFilters);
                  },
                ),
                const SizedBox(width: AppTheme.spacingSmall), // Abstand
                // "Nur Helferjobs" Button (NEU)
                 ElevatedButton.icon(
                    icon: Icon(
                       onlyHelperJobsActive.value ? Icons.check_circle : Icons.circle_outlined, 
                       size: 18, 
                       color: onlyHelperJobsActive.value ? Colors.white : Theme.of(context).colorScheme.primary
                    ),
                    label: Text(
                       'Nur Helferjobs',
                       style: TextStyle(
                          color: onlyHelperJobsActive.value ? Colors.white : Theme.of(context).colorScheme.primary,
                          fontSize: 13,
                       ),
                    ),
                    style: ElevatedButton.styleFrom(
                       backgroundColor: onlyHelperJobsActive.value 
                          ? Theme.of(context).colorScheme.primary 
                          : Theme.of(context).colorScheme.surface,
                       elevation: onlyHelperJobsActive.value ? 2 : 0,
                       padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                       shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(color: Theme.of(context).colorScheme.primary, width: 1),
                       ),
                    ),
                    onPressed: () {
                       final newValue = !onlyHelperJobsActive.value;
                       onlyHelperJobsActive.value = newValue;
                       final newFilters = Map<String, String>.from(activeFilters);
                       if (newValue) {
                          newFilters['onlyHelperJobs'] = 'true';
                          // Deaktiviere "Ohne Vorerfahrung", wenn Helferjobs aktiviert wird
                          if (noExperienceJobsActive.value) {
                             noExperienceJobsActive.value = false;
                             newFilters.remove('noExperienceJobs');
                          }
                       } else {
                          newFilters.remove('onlyHelperJobs');
                       }
                       onFiltersApplied(newFilters);
                    },
                 ),
                const SizedBox(width: AppTheme.spacingSmall), // Abstand
                // Sortierung (öffnet Dropdown) - bleibt unverändert
                TextButton.icon(
                   icon: Icon(Icons.sort, size: 18, color: Theme.of(context).colorScheme.primary),
                   label: Text(
                     'Sortieren',
                     style: TextStyle(color: Theme.of(context).colorScheme.primary),
                   ),
                  onPressed: () {
                    _showSortOptions(context);
                  },
                ),
              ],
            ),
          ),
          // Zweite Reihe mit Distanz-Slider (nur wenn Standort aktiv ist)
          if (activeFilters.containsKey('location')) ...[
              const SizedBox(height: AppTheme.spacingSmall), // Abstand hinzufügen
              Padding(
                 padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingSmall),
                 child: Row(
                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                   children: [
                     Text('Umkreis:', style: Theme.of(context).textTheme.titleSmall),
                     Text('${distanceValue.value.round()} km', style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                   ],
                 ),
              ),
              Slider(
                 value: distanceValue.value,
                 min: 1.0, // Minimum auf 1km setzen
                 max: 100.0,
                 divisions: null, // Entfernen, um kontinuierlichen Slider zu ermöglichen
                 label: '${distanceValue.value.round()} km', // Zeigt immer noch gerundeten Wert an
                 onChanged: (value) {
                   distanceValue.value = value;
                 },
                 onChangeEnd: (value) {
                   // Nur Distanzfilter anwenden, andere beibehalten
                   final newFilters = Map<String, String>.from(activeFilters);
                   newFilters['distance'] = value.round().toString(); // Speichere gerundeten Wert
                   onFiltersApplied(newFilters);
                 },
              ),
          ]
          // --- Ende Distanz-Slider ---
        ],
      ),
    );
  }

 // _showSortOptions bleibt gleich...
  void _showSortOptions(BuildContext context) {
     showModalBottomSheet(
        context: context,
        builder: (context) => Wrap(
          children: [
             ListTile(
               leading: const Icon(Icons.schedule),
               title: const Text('Neueste zuerst'),
               onTap: () { onSortChanged('date_desc'); Navigator.pop(context); },
             ),
             ListTile(
               leading: const Icon(Icons.person_search),
               title: const Text('Passend zu mir'),
               subtitle: const Text('Stellen, die zu deinem Profil passen'),
               onTap: () { onSortChanged('matching'); Navigator.pop(context); },
             ),
             ListTile(
               leading: const Icon(Icons.star_outline),
               title: const Text('Beste Übereinstimmung'),
               onTap: () { onSortChanged('relevance'); Navigator.pop(context); },
             ),
          ],
        ),
     );
  }
}

// --- Filter-Dialog wird nicht mehr benötigt oder muss angepasst werden ---
// Entfernen oder Anpassen des FilterDialog Widgets, da Filter jetzt direkt angewendet werden.
/*
class FilterDialog extends HookWidget {
// ... alter Code des Dialogs ...
}
*/ 
// Te<PERSON><PERSON><PERSON><PERSON>, um die verbesserte Animation zu demonstrieren

import 'package:flutter/material.dart';
import 'package:ki_test/src/presentation/common/widgets/enhanced_processing_animation.dart';

class AnimationDemoScreen extends StatelessWidget {
  const AnimationDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verbesserte Animation'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Verbesserte Animation',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Animiertes Icon mit der verbesserten EnhancedProcessingAnimation
                  EnhancedProcessingAnimation(
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 24.0,
                  ),
                  const SizedBox(width: 12),
                  // Text
                  Text(
                    'Generiere...',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

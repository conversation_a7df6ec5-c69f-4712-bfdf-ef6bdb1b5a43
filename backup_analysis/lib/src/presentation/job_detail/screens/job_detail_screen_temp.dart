import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:ki_test/src/application/providers/favorites_provider.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/core/config/deepseek_prompts.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/config/api_keys.dart';
import 'package:ki_test/services/ad_service.dart';
import 'package:ki_test/services/cloud_functions_service.dart';
import 'package:ki_test/src/core/utils/logging.dart';
import 'package:ki_test/services/supabase_models.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/presentation/common/widgets/app_dialogs.dart';
import 'package:ki_test/src/presentation/common/premium_dialogs.dart';
import 'package:flutter/gestures.dart'; // Für gestureRecognizers
import 'package:ki_test/src/presentation/premium/screens/premium_screen.dart';
import 'package:ki_test/src/presentation/common/widgets/app_loading_animation.dart';

// Verwende den minimalistischen Ladekreis für die Bewerbungsgenerierung

// Enum für die Modellauswahl entfernt
// enum AiModel { deepseek, gemini }

// Kein Provider mehr für Details nötig
// final jobDetailProvider = StateProvider<AsyncValue<JobEntity?>>((ref) => const AsyncValue.loading());

// Wird zu einem ConsumerStatefulWidget (StatefulWidget wegen AnimationController)
class JobDetailScreen extends ConsumerStatefulWidget {
  // StatefulWidget für WebView Controller
  final String jobRefnr;
  final String jobTitle;
  final JobEntity? jobEntity; // NEU: Optionale JobEntity für Metadaten

  const JobDetailScreen({
    super.key,
    required this.jobRefnr,
    required this.jobTitle,
    this.jobEntity, // NEU
  });

  @override
  ConsumerState<JobDetailScreen> createState() => _JobDetailScreenState();
}

// Nutze ConsumerState
class _JobDetailScreenState extends ConsumerState<JobDetailScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late final WebViewController _controller;
  bool _isLoadingPage = true; // Ladezustand für die Seite
  final bool _isExtracting = false; // Ladezustand für Text-Extraktion/KI
  bool _hasPageError = false; // Fehlerzustand für Seite
  bool _captchaDetected = false; // Flag für erkanntes Captcha
  bool _captchaSolved = false; // Flag für gelöstes Captcha
  final bool _isDownloadingCv = false; // Neuer Zustand für CV-Download
  String _extractedJobText =
      ''; // Variable zum Speichern des extrahierten Texts
  final String _jobUrl = ''; // URL zum Job

  // NEU: Zustandsvariablen für WebView-Navigation und Hinweis
  bool _canGoBack = false;
  bool _showExternalLinkInfo = false;

  // Variable für Fokus-Tracking des Hinweise-Textfelds
  bool _isHintsTextFieldFocused = false;
  // NEU: Variable, die anzeigt, ob die Tastatur sichtbar ist
  bool _isKeyboardVisible = false;
  final FocusNode _hintsFocusNode = FocusNode();

  // Animation Controller für Button-Effekt
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonOpacityAnimation;
  // Controller für Hintergrund-Animation
  late AnimationController _gradientSweepController;
  // NEU: Controller für hochwertige Bearbeitungs-Animation
  late AnimationController _processingAnimationController;
  late Animation<double> _processingAnimation;

  final TextEditingController _hintsController =
      TextEditingController(); // Hinzugefügt
  final _log = getLogger('JobDetailScreen'); // Logger Instanz

  // NEU: Zustand für Werbeanzeige-Laden
  final bool _isLoadingAd = false;
  // *** NEU: Zustand für erfolgreiche Generierung ***
  bool _generationCompletedSuccessfully = false;
  // *** NEU: Zustand für Generierungsprozess ***
  bool _isGenerating = false;

  // *** NEU: GlobalKey und State für Footer-Höhe ***
  final GlobalKey _footerKey = GlobalKey();
  double _footerHeight = 150.0; // Initialer Schätzwert, wird überschrieben

  // *** NEU: Flag, um mehrfache Prüfung zu verhindern ***
  bool _externalLinkCheckRunning = false;

  @override
  void initState() {
    super.initState();
    // Registriere diesen State als WidgetsBindingObserver
    WidgetsBinding.instance.addObserver(this);

    // SystemUI-Farben werden in didChangeDependencies gesetzt, nicht hier

    final String url =
        'https://www.arbeitsagentur.de/jobsuche/jobdetail/${widget.jobRefnr}';
    print("Initialisiere JobDetailScreen mit URL: $url");

    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0x00000000))
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                _log.d("WebView loading progress: $progress%");
              },
              onPageStarted: (String url) {
                _log.i("WebView started loading: $url");
                setState(() {
                  _isLoadingPage = true;
                  _hasPageError = false;
                  _log.d(
                    'BANNER_DEBUG: onPageStarted - Setze _showExternalLinkInfo auf false.',
                  );
                  _showExternalLinkInfo = false; // Reset bei neuer Seite
                  _externalLinkCheckRunning =
                      false; // *** Flag zurücksetzen ***
                });
              },
              onPageFinished: (url) async {
                if (!mounted) return;
                _log.d('BANNER_DEBUG: onPageFinished - Seite geladen: $url');
                setState(() {
                  _isLoadingPage = false;
                });

                // Prüfe, ob wir auf einer externen Seite sind (nicht arbeitsagentur.de)
                bool isExternalUrl = !url.contains('arbeitsagentur.de');

                if (isExternalUrl) {
                  _log.i(
                    "Externe Seite erkannt, CAPTCHA-Prüfung wird vollständig übersprungen",
                  );
                  // Setze den CAPTCHA-Status explizit
                  if (mounted) {
                    setState(() {
                      _captchaDetected = false;
                      _captchaSolved = true;
                      _showExternalLinkInfo =
                          true; // Setze das Flag für externe Seiten
                    });
                  }
                } else {
                  // Optional: Prüfe nach Captcha, wenn Seite fertig geladen ist und wir auf arbeitsagentur.de sind
                  _checkForCaptcha();
                }

                await Future.delayed(
                  const Duration(milliseconds: 1200),
                ); // 1200ms warten
                if (!mounted) return; // Erneut prüfen, da Delay

                if (_externalLinkCheckRunning) {
                  _log.d(
                    'BANNER_DEBUG: onPageFinished - _checkForExternalLink läuft bereits, überspringe erneuten Aufruf.',
                  );
                  return;
                }
                _externalLinkCheckRunning = true; // *** Flag setzen ***
                _log.d(
                  'BANNER_DEBUG: onPageFinished - Rufe _checkForExternalLink nach Delay auf...',
                );

                await _checkForExternalLink(); // Führe die Prüfung durch
                _log.d(
                  'BANNER_DEBUG: onPageFinished - _checkForExternalLink abgeschlossen.',
                );
                // _externalLinkCheckRunning wird in _checkForExternalLink zurückgesetzt

                // NEU: WebView kann zurückgehen?
                _controller.canGoBack().then((canGoBack) {
                  if (mounted && _canGoBack != canGoBack) {
                    setState(() {
                      _canGoBack = canGoBack;
                    });
                  }
                });
              },
              onWebResourceError: (WebResourceError error) {
                _log.e("WebView error: ${error.description}", error: error);
                setState(() {
                  _isLoadingPage = false; // Ladeindikator stoppen bei Fehler
                });
                // Zeige eine Fehlermeldung an
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Fehler beim Laden der Seite: ${error.description}',
                    ),
                  ),
                );
              },
              onNavigationRequest: (NavigationRequest request) {
                if (request.url.startsWith('https://www.youtube.com/')) {
                  _log.w("Navigation zu YouTube blockiert: ${request.url}");
                  return NavigationDecision.prevent;
                }
                _log.d("WebView navigation request: ${request.url}");
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse(url));

    // Animation Controller initialisieren
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );
    _buttonOpacityAnimation = Tween<double>(begin: 1.0, end: 0.4).animate(
      CurvedAnimation(
        parent: _buttonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Zweiten Controller für Hintergrund-Animation initialisieren
    _gradientSweepController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 1500,
      ), // Etwas langsamer für den Sweep
    );

    // Alte Animation-Initialisierung entfernt, da wir jetzt das ProcessingAnimation-Widget verwenden
    _processingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3500),
    );

    // FocusNode Listener hinzufügen
    _hintsFocusNode.addListener(() {
      setState(() {
        _isHintsTextFieldFocused = _hintsFocusNode.hasFocus;
      });
    });

    // *** NEU: Höhe des Footers nach dem ersten Frame messen ***
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_footerKey.currentContext != null) {
        final RenderBox renderBox =
            _footerKey.currentContext!.findRenderObject() as RenderBox;
        if (mounted && _footerHeight != renderBox.size.height) {
          setState(() {
            _footerHeight = renderBox.size.height;
            _log.d("FOOTER_DEBUG: Gemessene Footer-Höhe: $_footerHeight");
          });
        }
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // SystemUI-Farben hier setzen, wo Theme.of(context) sicher verfügbar ist
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    // Entferne Observer
    WidgetsBinding.instance.removeObserver(this);

    // Setze SystemUI-Farben zurück
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.light,
    ); // Zurück zum Standard

    // Stoppe den CAPTCHA-Überprüfungstimer
    _stopCaptchaCheckTimer();

    _buttonAnimationController.dispose(); // Controller entsorgen
    _gradientSweepController.dispose(); // Sweep-Controller entsorgen
    _processingAnimationController
        .dispose(); // Neuen Bearbeitungs-Controller entsorgen
    _hintsController.dispose(); // Hinzugefügt
    _hintsFocusNode.dispose(); // FocusNode entsorgen
    super.dispose();
  }

  // NEU: Implementiere didChangeMetrics aus WidgetsBindingObserver, um die Tastatur zu überwachen
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // Berechne, ob die Tastatur sichtbar ist
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final newKeyboardVisible = bottomInset > 0.0;

    // Aktualisiere den Zustand nur, wenn sich etwas geändert hat
    if (_isKeyboardVisible != newKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = newKeyboardVisible;
      });
    }
  }

  // --- Funktion zum Extrahieren von Text ---
  Future<String?> _extractTextFromWebView({bool isExternalPage = false}) async {
    // Wenn wir auf einer externen Seite sind, setzen wir den CAPTCHA-Status explizit
    if (isExternalPage || _showExternalLinkInfo) {
      if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }
      _log.i(
        "Externe Seite erkannt in _extractTextFromWebView, CAPTCHA-Status gesetzt",
      );
    }

    try {
      // Führe JavaScript aus, um den innerText des Body zu bekommen
      // Wichtig: `evaluateJavascript` wurde durch `runJavaScriptReturningResult` ersetzt
      final result = await _controller.runJavaScriptReturningResult(
        isExternalPage || _showExternalLinkInfo
            ? '''
            (function() {
              // Für externe Seiten: Versuche, den relevanten Inhalt zu extrahieren
              // Suche nach typischen Job-Beschreibungs-Containern
              const jobContainers = document.querySelectorAll('.job-description, .job-details, .job-content, article, main, [role="main"], .content, #content, .description');

              if (jobContainers.length > 0) {
                // Verwende den ersten gefundenen Container
                return jobContainers[0].innerText || jobContainers[0].textContent || document.body.innerText;
              }

              // Fallback: Gesamten Body-Text verwenden
              return document.body.innerText;
            })()
          '''
            : "document.body.innerText",
      );

      // Das Ergebnis ist oft ein String mit Anführungszeichen, diese entfernen
      if (result is String) {
        // Einfache Bereinigung (kann verbessert werden)
        String text = result;
        if (text.startsWith('"') && text.endsWith('"')) {
          text = text.substring(1, text.length - 1);
        }
        // Newlines und Tabs etc. bereinigen
        text = text.replaceAll('\\n', '\n').replaceAll('\\t', '\t').trim();

        // Wenn wir auf einer externen Seite sind, überspringen wir die CAPTCHA-Erkennung
        if (!isExternalPage && !_showExternalLinkInfo) {
          // Prüfen, ob möglicherweise ein Captcha vorhanden ist
          if (text.toLowerCase().contains("captcha") ||
              text.toLowerCase().contains("sicherheitsabfrage") ||
              text.toLowerCase().contains(
                "bestätigen sie, dass sie kein roboter sind",
              )) {
            _log.i(
              "Captcha erkannt: Möglicherweise werden nicht alle Informationen extrahiert.",
            );
            // Speichern wir für spätere Nutzung
            if (mounted) {
              setState(() {
                _captchaDetected = true;
              });
            }
          } else {
            if (mounted) {
              setState(() {
                _captchaDetected = false;
              });
            }
          }
        } else {
          // Auf externen Seiten setzen wir _captchaDetected immer auf false und _captchaSolved auf true
          if (mounted) {
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
          }
          _log.i(
            "Externe Seite: CAPTCHA-Erkennung übersprungen und Status gesetzt",
          );
        }

        return text;
      }
      return null; // Falls Ergebnis kein String
    } catch (e) {
      _log.e("Fehler beim Extrahieren von Text via JS: $e");
      return null;
    }
  }

  // VERBESSERT: Funktion zum Prüfen auf externe Links und fehlende Jobbeschreibungen
  Future<void> _checkForExternalLink() async {
    if (!mounted
     {
      _log.d(
      "BANNER_DEBUG: _checkForExternalLink - Starte Prüfung auf externe Links und fehlende Jobbeschreibungen...",
    );
    }

    try {
      // JavaScript, das nur nach dem exakten Text "externe seite öffnen" sucht
      final result = await _controller.runJavaScriptReturningResult('''
        (function() {
          // Nur ein Keyword: "externe seite öffnen"
          const keyword = 'externe seite öffnen';

          // Suche in mehr Element-Typen (Buttons, Links, und Elemente mit Klick-Handlern oder Link-Rollen)
          const potentialLinks = Array.from(document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]'));

          let externalLinkFound = false;
          let externalLinkElement = null;
          let externalLinkKeyword = '';

          // Prüfe auf externe Links mit exakter Übereinstimmung
          for (let i = 0; i < potentialLinks.length; i++) {
            // Versuche, den sichtbaren Text zu bekommen (innerText ist oft besser als textContent)
            const text = (potentialLinks[i].innerText || potentialLinks[i].textContent || '').toLowerCase().trim();
            if (!text) continue; // Überspringe Elemente ohne Text

            // Nur exakte Übereinstimmung mit "externe seite öffnen"
            if (text === keyword) {
              // Gib das gefundene Keyword und den Element-Typ zurück für Debugging
              console.log('BANNER_DEBUG: Externer Link Hinweis gefunden: "' + keyword + '" in Element: ' + potentialLinks[i].tagName);
              externalLinkFound = true;
              externalLinkElement = potentialLinks[i];
              externalLinkKeyword = keyword;
              break;
            }
          }

          // Prüfe auf fehlende Jobbeschreibung
          let hasMinimalContent = false;

          // 1. Prüfe Textlänge im Hauptinhalt
          const mainContent = document.querySelector('.ba-jobd-content') || document.body;
          const contentText = mainContent.innerText || '';

          // 2. Suche nach typischen Abschnitten einer Jobbeschreibung - KORRIGIERT: Verwende einfachere Selektoren
          const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6, .section-heading, .heading');
          let hasJobDescriptionSection = false;
          let hasRequirementsSection = false;

          // Durchsuche alle Überschriften nach relevanten Begriffen
          headings.forEach(heading => {
            const headingText = heading.innerText.toLowerCase();
            if (headingText.includes('stellenbeschreibung') ||
                headingText.includes('tätigkeitsbeschreibung') ||
                headingText.includes('aufgaben') ||
                headingText.includes('job description')) {
              hasJobDescriptionSection = true;
            }
            if (headingText.includes('anforderungen') ||
                headingText.includes('qualifikation') ||
                headingText.includes('profil') ||
                headingText.includes('requirements')) {
              hasRequirementsSection = true;
            }
          });

          // 3. Prüfe, ob die Seite hauptsächlich aus einem Link zu einer externen Seite besteht
          // Wenn der Inhalt sehr kurz ist oder keine typischen Abschnitte gefunden wurden
          hasMinimalContent = contentText.length < 500 || (!hasJobDescriptionSection && !hasRequirementsSection);

          // Hervorhebe den externen Link mit auffälliger Animation, falls gefunden
          if (externalLinkElement) {
            try {
              // Füge CSS-Animation für den Button hinzu
              const animationStyle = document.createElement('style');
              animationStyle.textContent = `
                @keyframes pulseButton {
                  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                  50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
                }

                @keyframes glowBorder {
                  0% { border-color: #FFC107; }
                  50% { border-color: #FF5722; }
                  100% { border-color: #FFC107; }
                }

                .external-link-highlight {
                  background-color: #FFEB3B !important;
                  border: 3px solid #FFC107 !important;
                  padding: 8px 16px !important;
                  border-radius: 4px !important;
                  box-shadow: 0 0 15px rgba(255, 193, 7, 0.8) !important;
                  position: relative !important;
                  animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                  transition: all 0.3s ease !important;
                  z-index: 9999 !important;
                  font-weight: bold !important;
                }

                .external-link-highlight:before {
                  content: "👉 " !important;
                }

                .external-link-highlight:after {
                  content: " 👈" !important;
                }

                .external-link-highlight:hover {
                  transform: scale(1.1) !important;
                  background-color: #FFC107 !important;
                }
              `;
              document.head.appendChild(animationStyle);

              // Wende die Klasse auf das Element an
              externalLinkElement.classList.add('external-link-highlight');

              // Scrolle zum Element
              externalLinkElement.scrollIntoView({behavior: 'smooth', block: 'center'});

              // Füge einen Tooltip hinzu, falls noch nicht vorhanden
              if (!externalLinkElement.title) {
                externalLinkElement.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
              }

              // Füge einen Event-Listener hinzu, um die Animation zu verstärken, wenn der Mauszeiger darüber schwebt
              externalLinkElement.addEventListener('mouseover', function() {
                this.style.transform = 'scale(1.1)';
                this.style.backgroundColor = '#FFC107';
              });

              externalLinkElement.addEventListener('mouseout', function() {
                this.style.transform = 'scale(1)';
                this.style.backgroundColor = '#FFEB3B';
              });

            } catch (e) {
              console.log('Fehler beim Hervorheben des externen Links:', e);
            }
          }

          // Stringifiziere das Ergebnis, um Probleme mit der Rückgabe zu vermeiden
          return JSON.stringify({
            externalLinkFound: externalLinkFound,
            hasMinimalContent: hasMinimalContent,
            contentLength: contentText.length,
            externalLinkKeyword: externalLinkKeyword
          });
        })();
      ''');

      _log.d(
        'BANNER_DEBUG: _checkForExternalLink - JS-Ergebnis erhalten: $result',
      );

      try {
        // Ergebnis auswerten - jetzt immer als String zurückgegeben
        Map<String, dynamic> resultMap;

        if (result is String) {
        // Versuche, das Ergebnis als JSON zu parsen
        try {
          // Entferne Anführungszeichen am Anfang und Ende, falls vorhanden
          String cleanResult = result;

          // Wenn das Ergebnis in doppelten Anführungszeichen eingeschlossen ist
          // (was bei einem JSON-String in einem String der Fall sein kann)
          if (cleanResult.startsWith('"') && cleanResult.endsWith('"')) {
            // Entferne die äußeren Anführungszeichen
            cleanResult = cleanResult.substring(1, cleanResult.length - 1);
            // Escape-Sequenzen für innere Anführungszeichen korrigieren
            cleanResult = cleanResult.replaceAll('\\"', '"');
          }

          // Versuche das JSON zu parsen
          resultMap = Map<String, dynamic>.from(jsonDecode(cleanResult));
          _log.d(
            'BANNER_DEBUG: JavaScript-Ergebnis erfolgreich als JSON geparst.',
          );
        } catch (jsonError) {
          _log.e('Fehler beim Parsen des JSON-Ergebnisses: $jsonError');

          // Zweiter Versuch mit direktem Parsen ohne Vorverarbeitung
          try {
            resultMap = Map<String, dynamic>.from(jsonDecode(result));
            _log.d(
              'BANNER_DEBUG: JSON im zweiten Versuch erfolgreich geparst.',
            );
          } catch (secondError) {
            _log.e('Auch zweiter Parse-Versuch fehlgeschlagen: $secondError');

            // Fallback-Werte verwenden
            resultMap = {
              'externalLinkFound': false,
              'hasMinimalContent': true, // Vorsichtshalber auf true setzen
              'contentLength': 0,
              'externalLinkKeyword': '',
            };
          }
        }
      } else if (result is Map) {
        // Direkte Map-Konvertierung
        resultMap = Map<String, dynamic>.from(result);
        _log.d('BANNER_DEBUG: JavaScript-Ergebnis ist bereits eine Map.');
      } else if (result is bool) {
        // Fallback für den Fall, dass das Ergebnis ein einfacher Boolean ist (alte Implementierung)
        resultMap = {
          'externalLinkFound': result,
          'hasMinimalContent': false,
          'contentLength': 0,
          'externalLinkKeyword': '',
        };
        _log.d('BANNER_DEBUG: JavaScript-Ergebnis ist ein Boolean: $result');
      } else {
        // Unbekannter Typ - Fallback
        resultMap = {
          'externalLinkFound': false,
          'hasMinimalContent': true, // Vorsichtshalber auf true setzen
          'contentLength': 0,
          'externalLinkKeyword': '',
        };
        _log.d(
          'BANNER_DEBUG: JavaScript-Ergebnis hat unbekannten Typ: ${result.runtimeType}',
        );
      }
        // Zeige Warnung, wenn externer Link gefunden ODER minimaler Inhalt erkannt wurde
        if (externalLinkFound || hasMinimalContent) {
          _log.i('Externer Link oder minimaler Inhalt erkannt. Zeige Hinweis.');
          if (mounted) {
            _log.d(
              'BANNER_DEBUG: _checkForExternalLink - Rufe setState auf, um _showExternalLinkInfo auf true zu setzen.',
            );
            setState(() {
              _showExternalLinkInfo = true;
              _log.d(
                'BANNER_DEBUG: _checkForExternalLink - setState abgeschlossen. _showExternalLinkInfo ist jetzt: $_showExternalLinkInfo',
              );
            });
          }
        } else {
          _log.d('Kein externer Link oder minimaler Inhalt gefunden.');
        }
      } catch (e, stackTrace) {
        _log.e(
          'Fehler beim Verarbeiten des JavaScript-Ergebnisses: $e',
          error: e,
          stackTrace: stackTrace,
        );

        // Fallback: Bei jedem Fehler zeigen wir die Warnung an, um sicherzugehen
        _log.i('Fehler bei der Erkennung. Zeige Hinweis zur Sicherheit.');
        if (mounted) {
          setState(() {
            _showExternalLinkInfo = true;
          });
        }
      }
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Prüfen auf externen Link via JS:',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      // Flag zurücksetzen, egal ob Erfolg oder Fehler
      if (mounted) {
        _externalLinkCheckRunning = false;
        _log.d(
          'BANNER_DEBUG: _checkForExternalLink - Flag _externalLinkCheckRunning zurückgesetzt.',
        );
      }
    }
  }

  // Timer für regelmäßige CAPTCHA-Überprüfung
  Timer? _captchaCheckTimer;

  // Deaktivierte Methode: Startet keine regelmäßige CAPTCHA-Überprüfung mehr
  void _startCaptchaCheckTimer() {
    // Setze den CAPTCHA-Status explizit auf "kein CAPTCHA"
    if (mounted) {
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
      });
    }

    _log.i(
      "CAPTCHA-Überprüfungstimer deaktiviert, um falsche Erkennungen zu vermeiden.",
    );
  }

  // Stoppt die regelmäßige CAPTCHA-Überprüfung
  void _stopCaptchaCheckTimer() {
    if (_captchaCheckTimer != null && _captchaCheckTimer!.isActive) {
      _captchaCheckTimer!.cancel();
      _captchaCheckTimer = null;
      _log.i("CAPTCHA-Überprüfungstimer gestoppt");
    }
  }

  // Überprüft, ob das CAPTCHA gelöst wurde
  Future<void> _checkIfCaptchaSolved() async {
    if (!mounted || _showExternalLinkInfo) return;

    _log.i("Überprüfe, ob CAPTCHA gelöst wurde...");

    try {
      // Extrahiere Text aus der WebView
      final String? extractedText = await _extractTextFromWebView();
      if (extractedText == null || extractedText.isEmpty) return;

      // Prüfe, ob eine E-Mail-Adresse im Text gefunden werden kann
      final String? extractedEmail = _findEmailInText(extractedText);

      // Prüfe, ob CAPTCHA-Hinweise im Text vorhanden sind
      bool captchaHintsPresent =
          extractedText.toLowerCase().contains(
            "sicherheitsgründen keine kontaktdaten",
          ) ||
          extractedText.toLowerCase().contains(
            "lösen sie bitte die sicherheitsfrage",
          ) ||
          extractedText.toLowerCase().contains(
            "lösen sie die sicherheitsfrage",
          ) ||
          extractedText.toLowerCase().contains(
            "geben sie die dargestellten zeichen",
          ) ||
          extractedText.toLowerCase().contains(
            "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
          );

      if (!captchaHintsPresent &&
          extractedEmail != null &&
          extractedEmail.isNotEmpty) {
        // CAPTCHA wurde gelöst, da eine E-Mail gefunden wurde und keine CAPTCHA-Hinweise mehr vorhanden sind
        if (mounted) {
          setState(() {
            _captchaSolved = true;
          });

          // Zeige eine Erfolgsmeldung an
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'CAPTCHA erfolgreich gelöst! Sie können jetzt fortfahren.',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.green.shade700,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );

          // Stoppe den Timer
          _stopCaptchaCheckTimer();
        }
      }
    } catch (e) {
      _log.e("Fehler bei der CAPTCHA-Überprüfung: $e");
    }
  }

  // Komplett deaktivierte CAPTCHA-Prüfung
  Future<bool> _checkForCaptcha() async {
    // Setze den Status explizit auf "kein CAPTCHA"
    if (mounted) {
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
      });
    }

    _log.i('CAPTCHA-Erkennung ist vollständig deaktiviert.');
    return false;
  }

  // Vollständig deaktivierte Methode
  Future<void> _scrollToCaptchaAndHighlight() async {
    // Setze den CAPTCHA-Status explizit auf "kein CAPTCHA"
    if (mounted) {
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
      });
    }

    // Stoppe den CAPTCHA-Überprüfungstimer
    _stopCaptchaCheckTimer();

    _log.i('CAPTCHA-Hervorhebung ist vollständig deaktiviert.');
    return;
  }

  // Methode zum Starten des Generierungsprozesses
  Future<void> _startGenerationProcess() async {
    try {
      // Setze den Generierungsstatus
      setState(() {
        _isGenerating = true;
      });

      // Extrahiere Text aus der WebView
      final jobText = await _extractTextFromWebView(
        isExternalPage: _showExternalLinkInfo,
      );

      if (jobText == null || jobText.isEmpty) {
        _log.e("Konnte keinen Text aus der WebView extrahieren");
        _showErrorSnackbar("Konnte keine Jobbeschreibung finden.");
        setState(() {
          _isGenerating = false;
        });
        return;
      }

      _extractedJobText = jobText;
      _log.i("Extrahierter Text: ${_extractedJobText.substring(0, min(100, _extractedJobText.length))}...");

      // Starte die Animation
      _processingAnimationController.repeat();

      // Generiere die Bewerbung
      final generatedText = await _generateCoverLetter(
        _extractedJobText,
        _hintsController.text,
      );

      if (generatedText == null || generatedText.isEmpty) {
        _log.e("Generierung fehlgeschlagen: Leerer Text zurückgegeben");
        _showErrorSnackbar("Bewerbung konnte nicht generiert werden.");
        setState(() {
          _isGenerating = false;
        });
        return;
      }

      // Speichere die generierte Bewerbung
      await _saveGeneratedApplication(generatedText);

      // Aktualisiere den UI-Status
      setState(() {
        _isGenerating = false;
        _generationCompletedSuccessfully = true;
      });

      // Zeige die generierte Bewerbung an
      _showGeneratedApplication(generatedText);

    } catch (e) {
      _log.e("Fehler beim Generierungsprozess: $e");
      _showErrorSnackbar("Ein Fehler ist aufgetreten: $e");
      setState(() {
        _isGenerating = false;
      });
    } finally {
      // Stoppe die Animation in jedem Fall
      _processingAnimationController.stop();
    }
  }

  // Hilfsmethode zum Anzeigen von Fehlermeldungen
  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Methode zum Anzeigen der generierten Bewerbung
  void _showGeneratedApplication(String text) {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Generierte Bewerbung'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(text),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Schließen'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _shareViaEmail(text);
                },
                child: const Text('Per E-Mail senden'),
              ),
            ],
          );
        },
      );
    }
  }

  // Methode zum Anzeigen eines Fehlers beim Öffnen der E-Mail-App
  void _showMailtoError() {
    if (mounted) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('E-Mail konnte nicht geöffnet werden'),
            content: const Text(
                'Die E-Mail-App konnte nicht geöffnet werden. Bitte versuchen Sie es später erneut oder kopieren Sie den Text und senden Sie die E-Mail manuell.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  // Methode zum Extrahieren von Text aus der WebView
  Future<String?> _extractTextFromWebView({required bool isExternalPage}) async {
    try {
      // Extrahiere den Text aus der WebView
      final String? extractedText = await _webViewController?.runJavaScriptReturningResult(
        '''
        (function() {
          // Extrahiere den Text aus dem Body
          const bodyText = document.body.innerText || document.body.textContent || '';
          return bodyText;
        })();
        ''',
      ) as String?;

      if (extractedText == null || extractedText.isEmpty) {
        _log.e("Konnte keinen Text aus der WebView extrahieren");
        return null;
      }

      // Prüfe auf CAPTCHA, wenn wir nicht auf einer externen Seite sind
      if (!isExternalPage) {
        final bool captchaDetected = await _checkForCaptcha();
        setState(() {
          _captchaDetected = captchaDetected;
        });

        if (captchaDetected) {
          _log.w("CAPTCHA erkannt, Extraktion abgebrochen");
          return null;
        }
      }

      return extractedText;
    } catch (e) {
      _log.e("Fehler beim Extrahieren des Textes: $e");
      return null;
    }
  }

  // Methode zum Prüfen auf CAPTCHA
  Future<bool> _checkForCaptcha() async {
    try {
      final bool result = await _webViewController?.runJavaScriptReturningResult(
        '''
        (function() {
          // Suche nach CAPTCHA-Elementen
          const captchaSelectors = [
            '*[id*="captcha" i]',
            '*[class*="captcha" i]',
            '*[id*="recaptcha" i]',
            '*[class*="recaptcha" i]',
            '*[id*="hcaptcha" i]',
            '*[class*="hcaptcha" i]',
            'iframe[src*="captcha" i]',
            'iframe[src*="recaptcha" i]',
            'iframe[src*="hcaptcha" i]',
            '*[id*="sicherheitsabfrage" i]',
            '*[class*="sicherheitsabfrage" i]',
            'form[action*="captcha" i]',
            'div[data-sitekey]',
            '.g-recaptcha',
            '#captchaimg',
            'img[src*="captcha" i]'
          ];

          // Prüfe, ob eines der CAPTCHA-Elemente vorhanden ist
          for (const selector of captchaSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
              return true;
            }
          }

          // Prüfe auf Text, der auf CAPTCHA hinweist
          const bodyText = document.body.innerText || document.body.textContent || '';
          const captchaTerms = ['captcha', 'sicherheitsabfrage', 'security check', 'bot check'];
          for (const term of captchaTerms) {
            if (bodyText.toLowerCase().includes(term)) {
              return true;
            }
          }

          return false;
        })();
        ''',
      ) as bool? ?? false;

      return result;
    } catch (e) {
      _log.e("Fehler beim Prüfen auf CAPTCHA: $e");
      return false;
    }
  }

  // Methode zum Generieren des Anschreibens
  Future<String?> _generateCoverLetter(String jobText, String hints) async {
    try {
      // Hole den Benutzerstil aus den Einstellungen
      final prefs = await SharedPreferences.getInstance();
      // Versuche zuerst 'style', dann 'cover_letter_style' als Fallback
      final String style = prefs.getString('style') ??
                           prefs.getString('cover_letter_style') ??
                           'Professionell';

      // Hole das Benutzerprofil
      final UserProfile? userProfile = await _getUserProfile();
      if (userProfile == null) {
        _log.e("Kein Benutzerprofil gefunden");
        return null;
      }

      // Formatiere die Profildaten für den Prompt
      final String formattedProfileData = _formatProfileDataForPrompt(userProfile);

      // Generiere den Prompt (jetzt asynchron)
      final String prompt = await _generatePrompt(
        jobText: jobText,
        profileData: formattedProfileData,
        hints: hints,
        style: style,
      );

      // Sende den Prompt an die KI
      final response = await _sendPromptToAI(prompt);

      if (response == null || response.isEmpty) {
        _log.e("Leere Antwort von der KI erhalten");
        return null;
      }

      return response;
    } catch (e) {
      _log.e("Fehler beim Generieren des Anschreibens: $e");
      return null;
    }
  }

  // Methode zum Formatieren der Profildaten für den Prompt
  String _formatProfileDataForPrompt(UserProfile userProfile) {
    String formattedProfileData = '';

    // Erstelle eine Map mit den relevanten Profildaten
    final Map<String, dynamic> profileData = {
      'name': userProfile.fullName,
      'email': userProfile.email,
      'phone': userProfile.phone,
      'address': userProfile.address,
      'skills': userProfile.skills,
      'languages': userProfile.languages,
      'workExperience': userProfile.workExperience.isNotEmpty
          ? userProfile.workExperience
              .map(
                (exp) =>
                    '${exp.position} bei ${exp.company} (${exp.startDate.toString().substring(0, 10)} - ${exp.endDate != null ? exp.endDate.toString().substring(0, 10) : 'heute'}): ${exp.description}',
              )
              .join('\n\n')
          : null,
      'education': userProfile.education.isNotEmpty
          ? userProfile.education
              .map(
                (edu) =>
                    '${edu.degree}${edu.fieldOfStudy.isNotEmpty ? ' im Bereich ${edu.fieldOfStudy}' : ''} an der ${edu.institution} (${edu.startDate.toString().substring(0, 10)} - ${edu.endDate != null ? edu.endDate.toString().substring(0, 10) : 'heute'})',
              )
              .join('\n')
          : null,
      'certifications': userProfile.certifications,
      'interests': userProfile.interests,
      'summary': userProfile.summary,
    };

    // Formatiere die Daten für den Prompt
    profileData.forEach((key, value) {
      if (value != null && (value is! String || value.isNotEmpty)) {
        String formattedValue;
        if (value is Map) {
          formattedValue = value.entries
              .map((e) => "- ${e.key}: ${e.value}")
              .join('\n  ');
          formattedProfileData +=
              "\n${_formatPromptKey(key)}:\n  $formattedValue";
        } else if (value is List) {
          formattedValue = value.join(', ');
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else if (value is bool) {
          formattedValue = value ? 'Ja' : 'Nein';
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else {
          formattedValue = value.toString();
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        }
      }
    });

    return formattedProfileData.trim();
  }

  // Hilfsmethode zum Formatieren der Schlüssel für den Prompt
  String _formatPromptKey(String key) {
    // Konvertiere camelCase zu Wörtern mit Großbuchstaben
    final result = key.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(1)}',
    );
    return result.substring(0, 1).toUpperCase() + result.substring(1);
  }

  // Methode zum Generieren des Prompts
  Future<String> _generatePrompt({
    required String jobText,
    required String profileData,
    required String hints,
    required String style,
  }) async {
    // E-Mail-Anweisungen für alle Stile
    final String emailInstructions = '''
Wichtig: Erstelle eine E-Mail-Bewerbung, KEINEN Brief. Beginne direkt mit der Anrede (z.B. "Sehr geehrte/r ..."). Füge KEINE Betreffzeile oder Absender-/Empfängeradressen in den generierten Text ein.''';

    // Prüfen, ob der Benutzer "Passend zu meinem Stil" ausgewählt hat
    if (style == 'Passend zu meinem Stil') {
      try {
        // Hole das Benutzerprofil
        final UserProfile? userProfile = await _getUserProfile();
        if (userProfile == null) {
          _log.e("Kein Benutzerprofil gefunden für personalisierten Stil");
          // Fallback auf professionellen Stil
          final String fallbackStyleInstructions =
              DeepSeekPrompts.styleInstructions['Professionell'] ?? '';

          final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
            jobTitle: widget.jobTitle,
            jobDescription: jobText,
            personalizationText: profileData,
            styleInstructions: '$fallbackStyleInstructions\n\n$emailInstructions',
          );

          return prompt;
        }

        // Erstelle ein Map mit den Profildaten für die Supabase-Funktion
        final Map<String, dynamic> profileDataMap = userProfile.toJson();

        // Generiere einen personalisierten Schreibstil-Prompt mit der Supabase-Funktion
        final String personalizedStylePrompt = await DeepSeekPrompts.generatePersonalizedStylePrompt(
          profileData: profileDataMap,
        );

        // Erstelle den Prompt mit dem personalisierten Schreibstil
        final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
          jobTitle: widget.jobTitle,
          jobDescription: jobText,
          personalizationText: profileData,
          styleInstructions: '$personalizedStylePrompt\n\n$emailInstructions',
        );

        return prompt;
      } catch (e) {
        _log.e("Fehler beim Generieren des personalisierten Stils: $e");
        // Fallback auf professionellen Stil
        final String fallbackStyleInstructions =
            DeepSeekPrompts.styleInstructions['Professionell'] ?? '';

        final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
          jobTitle: widget.jobTitle,
          jobDescription: jobText,
          personalizationText: profileData,
          styleInstructions: '$fallbackStyleInstructions\n\n$emailInstructions',
        );

        return prompt;
      }
    } else {
      // Normaler Stil aus den vordefinierten Stilen
      final String detailedStyleInstructions =
          DeepSeekPrompts.styleInstructions[style] ?? '';

      final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
        jobTitle: widget.jobTitle,
        jobDescription: jobText,
        personalizationText: profileData,
        styleInstructions: '$detailedStyleInstructions\n\n$emailInstructions',
      );

      return prompt;
    }
  }

  // Methode zum Senden des Prompts an die KI
  Future<String?> _sendPromptToAI(String prompt) async {
    try {
      // Hole die API-Konfiguration
      final apiConfig = await _getApiConfig();
      if (apiConfig == null) {
        _log.e("Keine API-Konfiguration gefunden");
        return null;
      }

      // Erstelle den Request-Body
      final Map<String, dynamic> requestBody = {
        'model': apiConfig.model,
        'prompt': prompt,
        'max_tokens': apiConfig.maxTokens,
        'temperature': apiConfig.temperature,
        'top_p': apiConfig.topP,
      };

      // Sende die Anfrage an die API
      final response = await http.post(
        Uri.parse(apiConfig.endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${apiConfig.apiKey}',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode != 200) {
        _log.e("API-Fehler: ${response.statusCode} - ${response.body}");
        return null;
      }

      // Verarbeite die Antwort
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      final String generatedText = responseData['choices'][0]['text'];

      return generatedText.trim();
    } catch (e) {
      _log.e("Fehler beim Senden des Prompts an die KI: $e");
      return null;
    }
  }

  // Hilfsmethode zum Holen der API-Konfiguration
  Future<ApiConfig?> _getApiConfig() async {
    try {
      // Hole die API-Konfiguration aus den Einstellungen
      final prefs = await SharedPreferences.getInstance();
      final String? apiKey = prefs.getString('api_key');
      final String? endpoint = prefs.getString('api_endpoint');
      final String? model = prefs.getString('api_model');

      if (apiKey == null || endpoint == null || model == null) {
        _log.e("Unvollständige API-Konfiguration");
        return null;
      }

      // Erstelle die API-Konfiguration
      return ApiConfig(
        apiKey: apiKey,
        endpoint: endpoint,
        model: model,
        maxTokens: prefs.getInt('api_max_tokens') ?? 1000,
        temperature: prefs.getDouble('api_temperature') ?? 0.7,
        topP: prefs.getDouble('api_top_p') ?? 0.9,
      );
    } catch (e) {
      _log.e("Fehler beim Holen der API-Konfiguration: $e");
      return null;
    }
  }

  // Hilfsmethode zum Holen des Benutzerprofils
  Future<UserProfile?> _getUserProfile() async {
    try {
      // Hole die Benutzer-ID aus den Einstellungen
      final prefs = await SharedPreferences.getInstance();
      final String? userId = prefs.getString('user_id');

      if (userId == null) {
        _log.e("Keine Benutzer-ID gefunden");
        return null;
      }

      // Hole das Benutzerprofil aus der Datenbank
      final response = await supabaseClient
          .from('user_profiles')
          .select()
          .eq('user_id', userId)
          .single();

      if (response == null) {
        _log.e("Kein Benutzerprofil gefunden");
        return null;
      }

      // Konvertiere die Antwort in ein UserProfile-Objekt
      return UserProfile.fromJson(response);
    } catch (e) {
      _log.e("Fehler beim Holen des Benutzerprofils: $e");
      return null;
    }
  }

  // Hilfsmethode zum Erstellen einer E-Mail mit dem generierten Anschreiben
  Future<void> _shareViaEmail(String emailBody, String? extractedEmail) async {
    try {
      // Prüfe, ob eine E-Mail-Adresse vorhanden ist
      if (extractedEmail == null || extractedEmail.isEmpty) {
        _log.e("Keine E-Mail-Adresse gefunden");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Keine E-Mail-Adresse gefunden.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Prüfe, ob ein Lebenslauf vorhanden ist
      String? localCvPath;
      final userProfileState = ref.read(userProfileProvider);
      final UserProfile? userProfile = userProfileState.asData?.value;

      if (userProfile != null && userProfile.cvStoragePath != null) {
        try {
          // Lade den Lebenslauf herunter
          localCvPath = await _downloadCvLocally(userProfile.cvStoragePath!);
          _log.i("Lokaler CV Pfad für E-Mail-Anhang: $localCvPath");
                } catch (e) {
          _log.e('Fehler beim Herunterladen des Lebenslaufs: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Herunterladen des Lebenslaufs. E-Mail wird ohne Anhang gesendet.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Bereitstellen der E-Mail-Parameter
      final Email email = Email(
        body: emailBody,
        subject: 'Bewerbung für: ${widget.jobTitle}',
        recipients: extractedEmail != null ? [extractedEmail] : [],
        // Falls lokaler CV-Pfad verfügbar ist, füge als Anhang hinzu
        attachmentPaths: localCvPath != null ? [localCvPath] : [],
      );

      _log.i("--- Preparing email with flutter_email_sender ---");
      _log.i("Recipient: ${email.recipients}");
      _log.i("Subject: ${email.subject}");
      _log.i("Attachment Path: ${email.attachmentPaths}");

      // VERBESSERTE FEHLERBEHANDLUNG
      try {
        await FlutterEmailSender.send(email);

        // --- ERFOLGSMELDUNG ENTFERNT ---
        // Wir zeigen keine Erfolgsmeldung mehr an, da die meisten E-Mail-Apps
        // bereits eine eigene Bestätigung anzeigen
        // --- ENDE ERFOLGSMELDUNG ENTFERNT ---
      } on PlatformException catch (pe) {
        _log.e(
          'Platform Exception beim E-Mail-Versand: ${pe.message}',
          error: pe,
        );
        
        // Lösche temporäre Datei auch bei Fehlern
        if (tempCvPath != null) {
          await CvStorageHelper.deleteTemporaryCvCopy(tempCvPath);
        }
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Öffnen der E-Mail-App: ${pe.message}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }

        // FALLBACK: Versuche, einen mailto-Link zu öffnen
        _log.i("Versuche Fallback mit mailto-Link...");
        await _openMailtoLink(
          extractedEmail,
          'Bewerbung für: ${widget.jobTitle}',
          emailBody,
        );
      } catch (e) {
        _log.e('Allgemeiner Fehler beim E-Mail-Versand: $e');
        
        // Lösche temporäre Datei auch bei Fehlern
        if (tempCvPath != null) {
          await CvStorageHelper.deleteTemporaryCvCopy(tempCvPath);
        }
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Senden der E-Mail. Versuche es später erneut.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stack) {
      _log.e('Fehler beim Vorbereiten der E-Mail: $e\\n$stack');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Vorbereiten der E-Mail.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Hilfsmethode zum Öffnen eines mailto-Links
  Future<void> _openMailtoLink(String to, String subject, String body) async {
    String? encodedSubject = subject
        .replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String? encodedBody = body
        .replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    queryParts.add('subject=$encodedSubject');
    queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      fi.l bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        showMailtoError();
    queryParts.add('subject=$encodedSubject');
    } catch (e) {
      _log.e('Error launching mailto: $e');
      showMailtoError();
    }
  }

  // Hilfsmethode zum Anzeigen eines Fehlers beim Öffnen des mailto-Links
  void showMailtoError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Fehler beim Öffnen der E-Mail-App. Bitte kopiere den Text und sende die E-Mail manuell.',
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  // Hilfsmethode zum Herunterladen des Lebenslaufs
  Future<String?> downloadCvLocally(
    String storagePath,
  ) async {
    if (storagePath.isEmpty) return null;
    _log.i("Downloading CV from Supabase path: $storagePath");
    try {
      final bytes = await supabaseClient.storage
          .from('cv-backups') // Dein Bucket-Name
          .download(storagePath);

      final tempDir = await getTemporaryDirectory();
      final fileName = p.basename(
        storagePath,
      ); // Extrahiere Dateinamen aus Pfad
      final localFilePath = '${tempDir.path}/$fileName';
      final file = File(localFilePath);
      await file.writeAsBytes(bytes, flush: true);
      _log.i("CV downloaded and saved locally to: $localFilePath");
      return localFilePath;
    } catch (e, stack) {
      _log.e("Error downloading CV locally: $e\n$stack");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Herunterladen des Lebenslaufs.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  // Methoden für gespeicherte Bewerbungen (optional, aber zur Vollständigkeit)
  Future<String?> checkExistingApplication() async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    return prefs.getString(jobKey);
  }

  Future<void> saveGeneratedApplication(String text) async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    await prefs.setString(jobKey, text);
  }

  // Hilfsmethode zum Extrahieren einer E-Mail-Adresse aus dem Text
  String? extractEmailFromText(String text) {
    // Regulärer Ausdruck für E-Mail-Adressen
    final RegExp emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );

    // Suche nach allen E-Mail-Adressen im Text
    final Iterable<RegExpMatch> matches = emailRegex.allMatches(text);

    // Wenn keine E-Mail-Adresse gefunden wurde, gib null zurück
    if (matches.isEmpty) {
      return null;
    }

    // Bevorzuge E-Mail-Adressen, die bestimmte Schlüsselwörter enthalten
    for (final RegExpMatch match in matches) {
      final String email = match.group(0)!.toLowerCase();
      if (email.contains("bewerbung") ||
          email.contains("karriere") ||
          email.contains("career") ||
          email.contains("apply") ||
          email.contains("application") ||
          email.contains("recruiting") ||
          email.contains("job") ||
          email.contains("personal") ||
          email.contains("hr")) {
        _log.i("Bevorzugte E-Mail gefunden: $email");
        return match.group(0);
      }
    }

    // Wenn keine bevorzugte E-Mail-Adresse gefunden wurde, gib die erste zurück
    return matches.first.group(0);
  }

  // Hilfsmethode zum Formatieren eines Schlüssels für den Prompt
  String formatPromptKey(String key) {
    // Ersetze Unterstriche durch Leerzeichen und mache den ersten Buchstaben groß
    return key.replaceAll('_', ' ').split(' ').map((word) {
      if (word.isNotEmpty) {
        return word[0].toUpperCase() + word.substring(1);
      }
      return word;
    }).join(' ');
  }

  // Hilfsmethode zum Generieren eines Anschreibens mit der KI
  Future<String?> generateCoverLetterWithAI(
    String jobText,
    String companyName,
    String? extractedEmail,
    String style,
  ) async {
    try {
      // Formatiere die Profildaten für den Prompt
      final userProfileState = ref.read(userProfileProvider);
      final UserProfile? userProfile = userProfileState.asData?.value;

      if (userProfile == null) {
        _log.e("Kein Benutzerprofil gefunden");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Bitte vervollständige zuerst dein Profil.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return null;
      }

      // Formatiere die Profildaten für den Prompt
      String formattedProfileData = "";
      userProfile.toJson().forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          String formattedValue;
          if (value is Map) {
            formattedValue = value.entries
                .map((e) => "- ${e.key}: ${e.value}")
                .join('\n  ');
            formattedProfileData +=
                "\n${formatPromptKey(key)}:\n  $formattedValue";
          } else if (value is List) {
            formattedValue = value.join(', ');
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          } else if (value is bool) {
            formattedValue = value ? 'Ja' : 'Nein';
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          } else {
            formattedValue = value.toString();
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          }
        }
      });

      // Detaillierte Anweisungen für den Stil
      final String detailedStyleInstructions =
          DeepSeekPrompts.styleInstructions[style] ?? '';
      final String emailInstructions = '''
Wichtig: Erstelle eine E-Mail-Bewerbung, KEINEN Brief. Beginne direkt mit der Anrede (z.B. "Sehr geehrte/r ..."). Füge KEINE Betreffzeile oder Absender-/Empfängeradressen in den generierten Text ein.''';

      final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
        jobTitle: widget.jobTitle,
        jobDescription: jobText,
        companyName: companyName,
        userProfileData: formattedProfileData,
        styleInstructions: detailedStyleInstructions,
        emailInstructions: emailInstructions,
      );

      // Rufe die KI-API auf
      final response = await ref.read(aiServiceProvider).generateCoverLetter(prompt);

      if (response == null || response.isEmpty) {
        _log.e("Keine Antwort von der KI erhalten");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fehler bei der Generierung des Anschreibens.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return null;
      }

      // Speichere das generierte Anschreiben
      await saveGeneratedApplication(response);

      return response;
    } catch (e, stack) {
      _log.e("Fehler bei der Generierung des Anschreibens: $e\n$stack");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler bei der Generierung des Anschreibens.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  // Hilfsmethode zum Öffnen einer URL im Browser

  // Hilfsmethode zum Anzeigen eines Fehlers beim Öffnen des mailto-Links
  void showMailtoError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Fehler beim Öffnen der E-Mail-App. Bitte kopiere den Text und sende die E-Mail manuell.',
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  // Hilfsmethode zum Generieren eines Anschreibens
  Future<void> generateApplication() async {
    // Implementierung der Anschreibengenerierung
    // Diese Methode würde die KI-API aufrufen und das Anschreiben generieren

    // Beispielimplementierung:
    final String? extractedEmail = extractEmailFromText(widget.jobDescription);
    final String? generatedText = await generateCoverLetterWithAI(
      widget.jobDescription,
      widget.companyName,
      extractedEmail,
      'professional', // Stil des Anschreibens
    );

    if (generatedText != null) {
      // Speichere das generierte Anschreiben
      await saveGeneratedApplication(generatedText);

      // Zeige das generierte Anschreiben an
      if (mounted) {
        // Hier würde das Anschreiben angezeigt werden
      }
    }
  }

  // Hilfsmethode zum Teilen des Anschreibens
  Future<void> shareApplication(String generatedText) async {
    // Extrahiere E-Mail-Adresse aus dem Jobtext
    final String? extractedEmail = extractEmailFromText(widget.jobDescription);

    // Teile das Anschreiben per E-Mail
    if (extractedEmail != null) {
      await shareViaEmail(generatedText, extractedEmail);
    } else {
      // Zeige Fehlermeldung, wenn keine E-Mail-Adresse gefunden wurde
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Keine E-Mail-Adresse gefunden.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Hilfsmethode zum Öffnen einer externen Seite
  Future<void> openExternalPage(String url) async {
    // Zeige Dialog mit Warnung an
    if (mounted) {
      final bool shouldProceed = await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Externe Seite öffnen'),
          content: Text('Sie werden nun zu einer externen Seite weitergeleitet.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Abbrechen'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Fortfahren'),
            ),
          ],
        ),
      ) ?? false;

      if (shouldProceed) {
        await launchUrl(url);
      }
    }
  }

  // Hilfsmethode zum Erkennen von CAPTCHA-Elementen
  bool isCaptchaElement(Element element) {
    // Prüfe Text-Inhalte
    final String text = element.text.toLowerCase();
    if (text.contains('captcha') || text.contains('sicherheitsabfrage')) {
      return true;
    }

    // Prüfe Attribute
    if (element is ImageElement) {
      final String src = element.attributes['src'] ?? '';
      final String alt = element.attributes['alt'] ?? '';
      if (src.toLowerCase().contains('captcha') ||
          alt.toLowerCase().contains('captcha') ||
          alt.toLowerCase().contains('sicherheitsabfrage')) {
        return true;
      }
    }

    return false;
  }

  // Hilfsmethode zum Anzeigen eines CAPTCHA-Dialogs
  Future<void> showCaptchaDialog() async {
    if (mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text('CAPTCHA erforderlich'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Diese Seite erfordert die Lösung eines CAPTCHAs, um fortzufahren. '
                'Sie werden zur externen Seite weitergeleitet, um das CAPTCHA zu lösen.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.security,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Verstanden'),
            ),
          ],
        ),
      );
    }
  }

  // Hilfsmethode zum Extrahieren einer E-Mail-Adresse aus einem Text
  String? extractEmailFromText(String text) {
    // Regulärer Ausdruck für E-Mail-Adressen
    final RegExp emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );

    // Suche nach allen E-Mail-Adressen im Text
    final Iterable<RegExpMatch> matches = emailRegex.allMatches(text);

    // Wenn keine E-Mail gefunden wurde, gib null zurück
    if (matches.isEmpty) return null;

    // Bevorzuge E-Mail-Adressen, die auf Bewerbungen hindeuten
    for (final RegExpMatch match in matches) {
      final String email = match.group(0)!.toLowerCase();
      if (email.contains("bewerbung") ||
          email.contains("karriere") ||
          email.contains("career") ||
          email.contains("job") ||
          email.contains("personal") ||
          email.contains("hr")) {
        _log.i("Bevorzugte E-Mail gefunden: $email");
        return match.group(0);
      }
    }

    // Wenn keine bevorzugte E-Mail gefunden wurde, gib die erste zurück
    return matches.first.group(0);
  }
  // Hilfsmethode zum Teilen eines Anschreibens per E-Mail
  Future<void> shareViaEmail(String text, String to) async {
    try {
      // Betreff für die E-Mail
      final String subject = 'Bewerbung für: ${widget.jobTitle}';

      // Prüfe, ob ein Lebenslauf vorhanden ist
      String? localCvPath;
      if (userProfile?.cvStoragePath != null && userProfile!.cvStoragePath.isNotEmpty) {
        try {
          localCvPath = await downloadCvLocally(userProfile!.cvStoragePath);
          if (localCvPath != null) {
            _log.i("Lokaler CV Pfad für E-Mail-Anhang: $localCvPath");
          }
        } catch (e) {
          _log.e('Fehler beim Herunterladen des Lebenslaufs: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Der Lebenslauf konnte nicht als Anhang hinzugefügt werden.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Bereitstellen der E-Mail-Parameter
      final Email email = Email(
        body: text,
        subject: subject,
        recipients: to.isNotEmpty ? [to] : [],
        // Falls lokaler CV-Pfad verfügbar ist, füge als Anhang hinzu
        attachmentPaths: localCvPath != null ? [localCvPath] : [],
      );

      _log.i("--- Preparing email with flutter_email_sender ---");
      _log.i("Recipient: ${email.recipients}");
      _log.i("Subject: ${email.subject}");
      _log.i("Attachment Path: ${email.attachmentPaths}");

      // VERBESSERTE FEHLERBEHANDLUNG
      try {
        await FlutterEmailSender.send(email);

        // --- ERFOLGSMELDUNG ENTFERNT ---
        // Wir zeigen keine Erfolgsmeldung mehr an, da die E-Mail-App
        // bereits eine Bestätigung anzeigt

        // --- ENDE ERFOLGSMELDUNG ENTFERNT ---
      } on PlatformException catch (pe) {
        _log.e(
          'Platform Exception beim E-Mail-Versand: ${pe.message}',
          error: pe,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Öffnen der E-Mail-App: ${pe.message}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        _log.e('Allgemeiner Fehler beim E-Mail-Versand: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Senden der E-Mail. Bitte versuchen Sie es später erneut.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stack) {
      _log.e('Fehler beim Vorbereiten der E-Mail: $e\\n$stack');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Fehler beim Vorbereiten der E-Mail. Bitte versuchen Sie es später erneut.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Hilfsmethode zum Herunterladen des Lebenslaufs
  Future<String?> downloadCvLocally(
    String storagePath,
  ) async {
    if (storagePath.isEmpty) return null;
    _log.i("Downloading CV from Supabase path: $storagePath");
    try {
      final bytes = await supabaseClient.storage
          .from('cv-backups') // Dein Bucket-Name
          .download(storagePath);

      // Temporäres Verzeichnis für die Datei
      final tempDir = await getTemporaryDirectory();
      final fileName = p.basename(
        storagePath,
      ); // Extrahiere Dateinamen aus Pfad
      final localFilePath = '${tempDir.path}/$fileName';
      final file = File(localFilePath);
      await file.writeAsBytes(bytes, flush: true);
      _log.i("CV downloaded and saved locally to: $localFilePath");
      return localFilePath;
    } catch (e, stack) {
      _log.e("Error downloading CV locally: $e\n$stack");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Fehler beim Herunterladen des Lebenslaufs: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  // Hilfsmethode zum Anzeigen eines Fehlers beim E-Mail-Versand
  void showMailtoError() {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('E-Mail-App konnte nicht geöffnet werden'),
          content: Text(
            'Es konnte keine E-Mail-App auf Ihrem Gerät gefunden werden. '
            'Bitte kopieren Sie den Text und senden Sie ihn manuell.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }
  // Hilfsmethode zum Erstellen einer E-Mail mit mailto
  Future<void> openMailto(String to, String subject, String? body) async {
    String? encodedSubject = subject
        .replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String? encodedBody = body
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    if (encodedSubject.isNotEmpty) queryParts.add('subject=$encodedSubject');
    if (encodedBody != null) queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        showMailtoError();
      }
    } catch (e) {
      _log.e('Error launching mailto: $e');
      showMailtoError();
    }
  }
  // Methoden für gespeicherte Bewerbungen (optional, aber zur Vollständigkeit)
  Future<String?> checkExistingApplication() async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    return prefs.getString(jobKey);
  }

  Future<void> saveGeneratedApplication(String text) async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    await prefs.setString(jobKey, text);
  }
  // Hilfsmethode zum Formatieren von Schlüsseln für Prompts
  String formatPromptKey(String key) {
    // Wandelt camelCase in Wörter mit Großbuchstaben um
    final words = <String>[];
    String currentWord = '';

    for (int i = 0; i < key.length; i++) {
      final char = key[i];
      if (i > 0 && char.toUpperCase() == char && char.toLowerCase() != char) {
        // Großbuchstabe gefunden (außer am Anfang)
        words.add(currentWord);
        currentWord = char;
      } else {
        currentWord += char;
      }
    }

    if (currentWord.isNotEmpty) {
      words.add(currentWord);
    }

    // Erste Buchstaben groß, Rest zusammenfügen
    return words.map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
  }
  // Hilfsmethode zum Erstellen eines Anschreibens
  Future<String?> generateCoverLetter(String style) async {
    try {
      // Zeige Ladeindikator
      if (mounted) {
        setState(() {
          _isGeneratingCoverLetter = true;
        });
      }

      // Prüfe, ob bereits ein Anschreiben für diesen Job existiert
      final existingApplication = await checkExistingApplication();
      if (existingApplication != null) {
        if (mounted) {
          setState(() {
            _isGeneratingCoverLetter = false;
          });
        }
        return existingApplication;
      }

      // Formatiere Profildaten für den Prompt
      String formattedProfileData = '';
      final Map<String, dynamic> profileData = userProfile?.toJson() ?? {};

      profileData.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          String formattedValue;
          if (value is Map) {
            formattedValue = value.entries
                .map((e) => "- ${e.key}: ${e.value}")
                .join('\n  ');
            formattedProfileData +=
                "\n${formatPromptKey(key)}:\n  $formattedValue";
          } else if (value is List) {
            formattedValue = value.join(', ');
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          } else if (value is bool) {
            formattedValue = value ? 'Ja' : 'Nein';
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          } else {
            formattedValue = value.toString();
            formattedProfileData += "\n${formatPromptKey(key)}: $formattedValue";
          }
        }
      });

      // Hole detaillierte Anweisungen für den gewählten Stil
      final String detailedStyleInstructions =
          DeepSeekPrompts.styleInstructions[style] ?? '';
      final String emailInstructions = '''
Wichtig: Erstelle eine E-Mail-Bewerbung, KEINEN Brief. Beginne direkt mit der Anrede (z.B. "Sehr geehrte/r ..."). Füge KEINE Betreffzeile oder Absender-/Empfängeradressen in den generierten Text ein.''';

      final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
        jobTitle: widget.jobTitle,
        jobDescription: widget.jobDescription,
        companyName: widget.companyName,
        profileData: formattedProfileData,
        styleInstructions: detailedStyleInstructions,
        additionalInstructions: emailInstructions,
      );

      // Sende Anfrage an Supabase Edge Function
      final response = await supabaseClient.functions.invoke(
        'generate-cover-letter',
        body: {'prompt': prompt},
      );

      if (response.status != 200) {
        throw Exception('Fehler bei der Anschreibengenerierung: ${response.status}');
      }

      final String generatedText = response.data as String;

      // Speichere das generierte Anschreiben
      await saveGeneratedApplication(generatedText);

      if (mounted) {
        setState(() {
          _isGeneratingCoverLetter = false;
        });
      }

      return generatedText;
    } catch (e) {
      _log.e('Fehler bei der Anschreibengenerierung: $e');
      if (mounted) {
        setState(() {
          _isGeneratingCoverLetter = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler bei der Anschreibengenerierung: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }
  // Hilfsmethode zum Anzeigen eines Dialogs für externe Links
  Future<bool> showExternalLinkDialog(String url) async {
    if (!mounted) return false;

    final bool result = await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('Externe Seite öffnen'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sie werden zu einer externen Webseite weitergeleitet. '
              'Möchten Sie fortfahren?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 8),
            Text(
              url,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Abbrechen'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('Öffnen'),
          ),
        ],
      ),
    ) ?? false;

    return result;
  }
  // Hilfsmethode zum Senden einer E-Mail mit Anschreiben
  Future<void> sendEmailWithCoverLetter() async {
    try {
      // Zeige Ladeindikator
      setState(() {
        _isGeneratingCoverLetter = true;
      });

      // Generiere Anschreiben
      final String? emailBody = await generateCoverLetter('professional');
      if (emailBody == null) {
        if (mounted) {
          setState(() {
            _isGeneratingCoverLetter = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fehler bei der Anschreibengenerierung'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Extrahiere E-Mail-Adresse aus der Jobbeschreibung
      final String? extractedEmail = extractEmailFromDescription(widget.jobDescription);

      // Prüfe, ob ein Lebenslauf heruntergeladen werden soll
      String? localCvPath;
      if (userProfile?.cvStoragePath != null && userProfile!.cvStoragePath!.isNotEmpty) {
        try {
          localCvPath = await downloadCVLocally(userProfile!.cvStoragePath!);
          if (localCvPath == null) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Lebenslauf konnte nicht heruntergeladen werden'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          } else {
            _log.i("Lokaler CV Pfad für E-Mail-Anhang: $localCvPath");
          }
        } catch (e) {
          _log.e('Fehler beim Herunterladen des Lebenslaufs: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fehler beim Herunterladen des Lebenslaufs'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Bereitstellen der E-Mail-Parameter
      final Email email = Email(
        body: emailBody,
        subject: 'Bewerbung für: ${widget.jobTitle}',
        recipients: extractedEmail != null ? [extractedEmail] : [],
        // Falls lokaler CV-Pfad verfügbar ist, füge als Anhang hinzu
        attachmentPaths: localCvPath != null ? [localCvPath] : [],
      );

      _log.i("--- Preparing email with flutter_email_sender ---");
      _log.i("Recipient: ${email.recipients}");
      _log.i("Subject: ${email.subject}");
      _log.i("Attachment Path: ${email.attachmentPaths}");

      // VERBESSERTE FEHLERBEHANDLUNG
      try {
        await FlutterEmailSender.send(email);
        // --- ERFOLGSMELDUNG ENTFERNT ---
        // --- ENDE ERFOLGSMELDUNG ENTFERNT ---
      } on PlatformException catch (pe) {
        _log.e(
          'Platform Exception beim E-Mail-Versand: ${pe.message}',
          error: pe,
        );
        if (mounted) {
          setState(() {
            _isGeneratingCoverLetter = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('E-Mail-App konnte nicht geöffnet werden: ${pe.message}'),
              backgroundColor: Colors.red,
            ),
          );
          // FALLBACK: Versuche, einen mailto-Link zu öffnen
          if (extractedEmail != null) {
            openMailtoLink(extractedEmail, 'Bewerbung für: ${widget.jobTitle}', emailBody);
          }
        }
      } catch (e) {
        _log.e('Allgemeiner Fehler beim E-Mail-Versand: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fehler beim E-Mail-Versand: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stack) {
      _log.e('Fehler beim Vorbereiten der E-Mail: $e\\n$stack');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Vorbereiten der E-Mail: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingCoverLetter = false;
        });
      }
    }
  }
  // Hilfsmethode zum Extrahieren einer E-Mail-Adresse aus der Jobbeschreibung
  String? extractEmailFromDescription(String description) {
    // Regulärer Ausdruck für E-Mail-Adressen
    final RegExp emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
      caseSensitive: false,
    );

    // Suche nach allen E-Mail-Adressen im Text
    final Iterable<RegExpMatch> matches = emailRegex.allMatches(description);

    // Wenn keine E-Mail gefunden wurde, gib null zurück
    if (matches.isEmpty) {
      _log.i("Keine E-Mail-Adresse in der Jobbeschreibung gefunden");
      return null;
    }

    // Wenn mehrere E-Mails gefunden wurden, bevorzuge bestimmte Domains
    if (matches.length > 1) {
      for (final match in matches) {
        final String email = match.group(0)!.toLowerCase();
        if (email.contains("bewerbung") ||
            email.contains("karriere") ||
            email.contains("career") ||
            email.contains("job") ||
            email.contains("personal") ||
            email.contains("hr")) {
          _log.i("Bevorzugte E-Mail gefunden: $email");
          return match.group(0);
        }
      }
    }

    // Ansonsten nimm die erste gefundene E-Mail
    _log.i("E-Mail-Adresse gefunden: ${matches.first.group(0)}");
    return matches.first.group(0);
  }

  // Hilfsmethode zum Öffnen eines mailto-Links
  Future<void> openMailtoLink(String to, String? subject, String? body) async {
    String? encodedSubject = subject?.replaceAll(' ', '%20');
    String? encodedBody = body
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    if (encodedSubject != null) queryParts.add('subject=$encodedSubject');
    if (encodedBody != null) queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        showMailtoError();
      }
    } catch (e) {
      _log.e('Error launching mailto: $e');
      showMailtoError();
    }
  }
  // Hilfsmethode zum Anzeigen eines Fehlers beim E-Mail-Versand
  void showMailtoError() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('E-Mail-App konnte nicht geöffnet werden. Bitte versuchen Sie es später erneut.'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 5),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  // Hilfsmethode zum Starten einer URL im Browser
  Future<void> launchUrl(String url) async {
    try {
      _log.i("Versuche URL zu öffnen: $url");
      final Uri uri = Uri.parse(url);
      final bool launched = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        _log.e("Konnte URL nicht öffnen: $url");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Konnte die URL nicht öffnen: $url'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _log.e("Fehler beim Öffnen der URL: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Öffnen der URL: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  // Hilfsmethode zum Formatieren von Schlüsseln für Prompts
  String formatPromptKey(String key) {
    // Wandelt camelCase in Wörter mit Großbuchstaben um
    final words = <String>[];
    String currentWord = '';

    for (int i = 0; i < key.length; i++) {
      final char = key[i];
      if (i > 0 && char.toUpperCase() == char && char.toLowerCase() != char) {
        // Großbuchstabe gefunden (außer am Anfang)
        words.add(currentWord);
        currentWord = char;
      } else {
        currentWord += char;
      }
    }

    if (currentWord.isNotEmpty) {
      words.add(currentWord);
    }

    // Erste Buchstaben groß, Rest zusammenfügen
    return words.map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
  }
  // Hilfsmethode zum Anzeigen eines Fehlers beim E-Mail-Versand
  void showMailtoError() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('E-Mail-App konnte nicht geöffnet werden. Bitte versuchen Sie es später erneut.'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 5),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Hilfsmethode zum Herunterladen des Lebenslaufs
  Future<String?> downloadCVLocally(
    String storagePath,
  ) async {
    if (storagePath.isEmpty) return null;
    _log.i("Downloading CV from Supabase path: $storagePath");
    try {
      final bytes = await supabaseClient.storage
          .from('cv-backups') // Dein Bucket-Name
          .download(storagePath);

      final tempDir = await getTemporaryDirectory();
      final fileName = p.basename(
        storagePath,
      ); // Extrahiere Dateinamen aus Pfad
      final localFilePath = '${tempDir.path}/$fileName';
      final file = File(localFilePath);
      await file.writeAsBytes(bytes, flush: true);
      _log.i("CV downloaded and saved locally to: $localFilePath");
      return localFilePath;
    } catch (e, stack) {
      _log.e("Error downloading CV locally: $e\n$stack");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Herunterladen des Lebenslaufs: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  // Hilfsmethode zum Erstellen einer E-Mail mit mailto
  Future<void> openMailto({
    required String to,
    String? subject,
    String? body,
  }) async {
    String? encodedSubject = subject
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String? encodedBody = body
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    if (encodedSubject != null) queryParts.add('subject=$encodedSubject');
    if (encodedBody != null) queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        showMailtoError();
      }
    } catch (e) {
      _log.e('Error launching mailto: $e');
      showMailtoError();
    }
  }

  // Methoden für gespeicherte Bewerbungen (optional, aber zur Vollständigkeit)
  Future<String?> checkExistingApplication() async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    return prefs.getString(jobKey);
  }

  Future<void> saveGeneratedApplication(String text) async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    await prefs.setString(jobKey, text);
  }

            for (element of captchaElements) {
              try {
                const rect = element.getBoundingClientRect();
                const area = rect.width * rect.height;
                if (area > maxArea && area > 100) { // Mindestgröße, um kleine Elemente zu ignorieren
                  maxArea = area;
                  mainCaptchaElement = element;
                }
              } catch (e) {
                console.error('Fehler bei Größenberechnung', e);
              }
            }

            // Suche nach dem Elternelement, das ein Formular sein könnte
            let captchaParent = mainCaptchaElement;
            let formFound = false;

            // Versuche, ein umgebendes Formular zu finden
            for (let i = 0; i < 5; i++) { // Maximal 5 Ebenen nach oben
              if (!captchaParent.parentNode) break;

              captchaParent = captchaParent.parentNode;
              if (captchaParent.tagName && captchaParent.tagName.toLowerCase() === 'form') {
                formFound = true;
                break;
              }
            }

            // Wenn kein Formular gefunden wurde, gehe zurück zum ursprünglichen Element
            if (!formFound) {
              captchaParent = mainCaptchaElement;
            }

            // Erstelle einen Container um das Captcha
            const captchaContainer = document.createElement('div');
            captchaContainer.className = 'captcha-container';

            // Erstelle ein Label für das Captcha
            const captchaLabel = document.createElement('div');
            captchaLabel.className = 'captcha-label';
            captchaLabel.textContent = 'CAPTCHA HIER LÖSEN';
            captchaLabel.style.backgroundColor = '#db2777'; // Pink
            captchaLabel.style.color = 'white';
            captchaLabel.style.fontWeight = 'bold';
            captchaLabel.style.padding = '6px 12px';
            captchaLabel.style.borderRadius = '4px';
            captchaLabel.style.position = 'absolute';
            captchaLabel.style.top = '-15px';
            captchaLabel.style.left = '50%';
            captchaLabel.style.transform = 'translateX(-50%)';
            captchaLabel.style.zIndex = '10000';

            // Füge das Label zum Container hinzu
            captchaContainer.appendChild(captchaLabel);

            try {
              // Wende die Highlight-Klasse auf das Captcha-Element an
              mainCaptchaElement.classList.add('captcha-highlight');

              // Wenn das Element ein iframe ist oder ein Formular gefunden wurde, umhülle es mit dem Container
              if (mainCaptchaElement.tagName.toLowerCase() === 'iframe' || formFound) {
                const parent = captchaParent.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, captchaParent);
                  captchaContainer.appendChild(captchaParent);
                }
              } else {
                // Sonst füge den Container direkt ein
                const parent = mainCaptchaElement.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, mainCaptchaElement);
                  captchaContainer.appendChild(mainCaptchaElement);
                }
              }
            } catch (e) {
              console.error('Fehler beim Einfügen des Containers', e);
              // Fallback: Füge den Container direkt neben dem Element ein
              try {
                const parent = mainCaptchaElement.parentNode;
                if (parent) {
                  parent.insertBefore(captchaContainer, mainCaptchaElement.nextSibling);
                }
              } catch (e2) {
                console.error('Auch Fallback fehlgeschlagen', e2);
              }
            }

            // Direkt das CAPTCHA-Feld markieren ohne Overlay oder Spotlight
            // Finde das Eingabefeld für das CAPTCHA
            const captchaInputField = document.querySelector('input[type="text"]');
            if (captchaInputField) {
              console.log('CAPTCHA-Eingabefeld gefunden');

              // Füge einen dezenten Rahmen hinzu
              captchaInputField.style.border = '2px solid #db2777';
              captchaInputField.style.borderRadius = '4px';
              captchaInputField.style.boxShadow = '0 0 10px rgba(219, 39, 119, 0.5)';
              captchaInputField.style.padding = '8px';
              captchaInputField.style.fontSize = '16px';
              captchaInputField.style.fontWeight = 'bold';
              captchaInputField.style.backgroundColor = 'rgba(255, 240, 245, 0.7)';

              // Füge Animation hinzu
              captchaInputField.style.animation = 'colorBorderFlow 5s infinite ease-in-out';

              // Füge einen Fokus hinzu
              setTimeout(() => {
                captchaInputField.focus();
              }, 800);

              // Hinweistext entfernt

              // Verbessere das Styling des Elternelements
              const inputParent = captchaInputField.parentElement;
              if (inputParent) {
                inputParent.style.position = 'relative';
              }
            } else {
              console.log('Kein CAPTCHA-Eingabefeld gefunden');
            }

            // Verbesserte Scroll-Funktion zum CAPTCHA mit mehreren Fallbacks
            try {
              // Versuche zuerst, direkt zum Eingabefeld zu scrollen, falls vorhanden
              const captchaInputField = document.querySelector('input[type="text"]');
              if (captchaInputField) {
                console.log('Scrolle zum CAPTCHA-Eingabefeld');
                // Hebe das Element sehr stark hervor, bevor wir scrollen
                captchaInputField.style.border = '4px solid #db2777';
                captchaInputField.style.borderRadius = '4px';
                captchaInputField.style.boxShadow = '0 0 25px rgba(219, 39, 119, 0.9)';
                captchaInputField.style.padding = '8px';
                captchaInputField.style.fontSize = '16px';
                captchaInputField.style.fontWeight = 'bold';
                captchaInputField.style.backgroundColor = 'rgba(255, 240, 245, 0.9)';
                captchaInputField.style.animation = 'colorBorderFlow 5s infinite ease-in-out';

                // Berechne die Position des Elements
                const rect = captchaInputField.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                window.scrollTo(0, targetY);

                // Fokussiere das Eingabefeld nach dem Scrollen
                setTimeout(() => {
                  captchaInputField.focus();
                }, 100);

                return true;
              }

              // Fallback 1: Zum Hauptelement scrollen mit sanfter Animation
              console.log('Scrolle zum Haupt-CAPTCHA-Element');

              // Berechne die Position des Elements
              const rect = mainCaptchaElement.getBoundingClientRect();
              const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

              // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
              window.scrollTo(0, targetY);
            } catch (e) {
              console.error('Fehler beim Scrollen zum Captcha', e);

              // Fallback 2: Versuche es mit dem Container
              try {
                console.log('Fallback: Scrolle zum CAPTCHA-Container');

                // Berechne die Position des Elements
                const rect = captchaContainer.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                window.scrollTo(0, targetY);
              } catch (e2) {
                console.error('Auch Fallback-Scrollen fehlgeschlagen', e2);

                // Fallback 3: Versuche, zu einem beliebigen CAPTCHA-Element zu scrollen
                try {
                  const anyElement = document.querySelector('*[id*="captcha" i], *[class*="captcha" i], input[type="text"], img[src*="captcha" i]');
                  if (anyElement) {
                    console.log('Letzter Fallback: Scrolle zu einem beliebigen CAPTCHA-Element');

                    // Berechne die Position des Elements
                    const rect = anyElement.getBoundingClientRect();
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const targetY = rect.top + scrollTop - (window.innerHeight / 2) + (rect.height / 2);

                    // Direkt zum Ziel scrollen ohne Animation, um Wackeln zu vermeiden
                    window.scrollTo(0, targetY);
                  }
                } catch (e3) {
                  console.error('Alle Scroll-Versuche fehlgeschlagen', e3);
                }
              }
            }

            // Keine Overlay-Entfernung mehr nötig, da wir keinen Overlay haben

            return true;
          } else {
            console.log('Keine Captcha-Elemente gefunden');
            return false;
          }
        })();
      ''');

      _log.i('Captcha-Hervorhebung und Scroll ausgeführt.');

      // Starte den Timer zur regelmäßigen Überprüfung, ob das CAPTCHA gelöst wurde
      _startCaptchaCheckTimer();
    } catch (e, stackTrace) {
      _log.e(
        'Fehler beim Scrollen zum Captcha:',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // --- Generierungslogik wurde hierhin verschoben ---
  Future<Map<String, dynamic>> _generateApplicationWithAI(
    String jobText,
  ) async {
    _log.i('Starte KI-Anschreiben-Generierung...');
    // User Profil holen
    final userProfileState = ref.read(userProfileProvider);
    final UserProfile? userProfile = userProfileState.asData?.value;
    final JobEntity? jobEntityNullable = widget.jobEntity;

    if (userProfile == null) {
      _log.e('User-Profil nicht verfügbar für KI-Generierung.');
      return {'error': 'Benutzerprofil konnte nicht geladen werden.'};
    }

    if (jobEntityNullable == null) {
      _log.e('JobEntity ist null, kann kein Anschreiben generieren.');
      return {'error': 'Job-Details konnten nicht geladen werden.'};
    }
    final JobEntity jobEntity = jobEntityNullable;

    // Hole den Benutzerstil aus den Einstellungen
    final prefs = await SharedPreferences.getInstance();
    // Versuche zuerst 'style', dann 'cover_letter_style' als Fallback
    final String style = prefs.getString('style') ??
                         prefs.getString('cover_letter_style') ??
                         'Professionell';

    _log.i('Gewählter Schreibstil: $style');

    // Personalisierter Schreibstil-Prompt
    String personalizedStylePrompt = '';

    // Prüfe, ob der Benutzer "Passend zu meinem Stil" ausgewählt hat
    if (style == 'Passend zu meinem Stil') {
      _log.i('Rufe Supabase Function "analyze-profile-style" für personalisierten Stil auf...');

      try {
        // Rufe die Supabase-Funktion auf, um einen personalisierten Schreibstil zu generieren
        final response = await Supabase.instance.client.functions.invoke(
          'analyze-profile-style',
          body: {'profileData': userProfile.toJson()},
        );

        if (response.status == 200 && response.data != null) {
          _log.i('Personalisierter Schreibstil erfolgreich generiert.');
          // Extrahiere den personalisierten Schreibstil aus der Antwort
          personalizedStylePrompt = response.data['stylePrompt'] ?? '';

          if (personalizedStylePrompt.isNotEmpty) {
            _log.i('Verwende personalisierten Schreibstil: ${personalizedStylePrompt.substring(0, min(50, personalizedStylePrompt.length))}...');
          } else {
            _log.w('Personalisierter Schreibstil ist leer, verwende Standard-Stil.');
          }
        } else {
          _log.e('Fehler bei der Generierung des personalisierten Schreibstils: ${response.status}');
        }
      } catch (e) {
        _log.e('Fehler beim Aufruf der Supabase-Funktion "analyze-profile-style": $e');
      }
    }

    // --- Daten für die Supabase Function vorbereiten ---
    // WICHTIG: Die Schlüssel müssen exakt mit denen übereinstimmen,
    // die in der Supabase Function (`index.ts` -> `RequestPayload`) erwartet werden.
    final Map<String, dynamic> userProfileDataForFunction = {
      // Passe dies an die tatsächliche Struktur in deiner Edge Function an!
      'name': userProfile.name,
      'skills': userProfile.skills ?? [],
      'experience':
          userProfile.workExperience
              .map((e) => '${e.position} bei ${e.company}')
              .join('\n') ??
          '',
      // Füge hier ggf. weitere benötigte Felder hinzu
      'stylePreference': style, // Füge den gewählten Stil hinzu
    };

    final Map<String, dynamic> jobPostingDataForFunction = {
      // Passe dies an die tatsächliche Struktur in deiner Edge Function an!
      'title': jobEntity.title,
      'company': jobEntity.companyName ?? '',
      'description': jobText, // Der extrahierte Text
    };

    _log.d("--- Daten für Supabase Function ---");
    _log.d("UserProfile Data: ${jsonEncode(userProfileDataForFunction)}");
    _log.d("JobPosting Data: ${jsonEncode(jobPostingDataForFunction)}");
    _log.i("--- Rufe Supabase Function 'generate-cover-letter' auf ---");

    try {
      // *** NEU: Supabase Service verwenden ***
      final supabaseService = ref.read(supabaseServiceProvider);

      // *** Ergebnis ist jetzt ein Objekt ***
      final GenerateCoverLetterResult result = await supabaseService
          .generateCoverLetter(
            userProfile: userProfileDataForFunction,
            jobPosting: jobPostingDataForFunction,
            personalizedStylePrompt: style == 'Passend zu meinem Stil' ? personalizedStylePrompt : null,
          );
      _log.i("--- Antwort von Supabase Function erhalten ---");

      // Erfolg: Extrahiere Daten aus dem Ergebnisobjekt
      return {
        'generatedText': result.coverLetter,
        'extractedEmail':
            result.extractedEmail, // *** Verwende die extrahierte E-Mail ***
        'error': null,
      };
    } on FunctionError catch (e, stackTrace) {
      // *** NEU: Fange spezifischen FunctionError ***
      _log.e(
        "Supabase Function Fehler: ${e.message}",
        error: e,
        stackTrace: stackTrace,
      );
      return {'error': e.message}; // Gib die Fehlermeldung vom Service weiter
    } catch (e, stackTrace) {
      _log.e(
        "Allgemeiner Fehler beim Aufruf der Supabase Function:",
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'error':
            "Die Anschreiben-Generierung ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.",
      };
    }
  }

  // Diese Variable wurde bereits oben deklariert
  // bool _captchaSolved = false;

  // --- Anschreiben-Generierung ---
  Future<void> _triggerApplicationGeneration() async {
    // Wenn wir auf einer externen Seite sind, überspringen wir alle CAPTCHA-Prüfungen
    // aber führen trotzdem den Premium-Check durch
    if (_showExternalLinkInfo) {
      _log.i("Externe Seite erkannt, überspringe CAPTCHA-Prüfung");
      setState(() {
        _captchaDetected = false;
        _captchaSolved = true;
        _isExtracting =
            false; // Noch nicht auf true setzen, erst nach Premium-Check
        _generationCompletedSuccessfully = false;
      });

      // Text extrahieren für externe Seiten
      try {
        // Extrahiere den Text mit einem speziellen Flag für externe Seiten
        final String? extractedJobText = await _extractTextFromWebView(
          isExternalPage: true,
        );
        if (extractedJobText == null || extractedJobText.isEmpty) {
          throw Exception('Konnte keinen Text von der Webseite extrahieren.');
        }
        _extractedJobText = extractedJobText;
        _log.i(
          "Text von externer Seite extrahiert, Länge: ${extractedJobText.length}",
        );

        // Premium-Check für externe Seiten
        final userProfileState = ref.read(userProfileProvider);
        UserProfile? userProfile;
        if (userProfileState is AsyncData<UserProfile?>) {
          userProfile = userProfileState.value;
        }

        // Aktuellen Premium-Status prüfen (Fallback auf false)
        final bool isPremium = userProfile?.isPremium ?? false;
        _log.i("Premium/Ad Check auf externer Seite - isPremium: $isPremium");

        if (!isPremium) {
          // NICHT Premium: Dialog anzeigen
          if (!mounted) return; // Sicherheitscheck

          // Ladezustand für Ad setzen
          setState(() => _isLoadingAd = true);

          // Premium-Dialog anzeigen
          await showPremiumOrAdDialog(
            context: context,
            ref: ref,
            onAdWatched: () {
              // Dieser Callback wird aufgerufen, wenn die Werbung angesehen wurde
              _log.i(
                "Ad Watched (Callback in JobDetail) - Starte Generierung auf externer Seite",
              );
              if (mounted) {
                // State für Animation setzen und Animation starten
                setState(() {
                  _isExtracting =
                      true; // Zeigt "Generiere..." an und blockiert Button
                  _isLoadingAd = false; // Ad-Laden ist definitiv vorbei
                  _generationCompletedSuccessfully =
                      false; // Sicherstellen, dass zurückgesetzt
                });
                _buttonAnimationController.repeat(reverse: true);
                _gradientSweepController.repeat();

                // Generierungsprozess starten
                _startGenerationProcess(isExternalPage: true);
              }
            },
          );

          // Ladezustand sicherheitshalber hier auch zurücksetzen
          if (mounted && _isLoadingAd) {
            // Nur wenn es noch aktiv war
            setState(() => _isLoadingAd = false);
            _log.i(
              "Ladezustand nach Premium/Ad-Dialog zurückgesetzt (Fallback)",
            );
          }

          // Aufräumen, wenn der Dialog geschlossen wurde ohne Werbung anzusehen
          if (mounted && !_isExtracting) {
            _buttonAnimationController.stop();
            _buttonAnimationController.reset();
            _gradientSweepController.stop();
            _gradientSweepController.reset();
          }

          return; // Beende die Methode hier, wenn nicht Premium und keine Werbung angesehen
        } else {
          // Premium-Nutzer: Direkt generieren
          setState(() {
            _isExtracting = true;
          });
          _buttonAnimationController.repeat(reverse: true);
          _gradientSweepController.repeat();

          // Direkt zum Generierungsprozess springen
          await _startGenerationProcess(isExternalPage: true);

          // Aufräumen
          if (mounted) {
            _buttonAnimationController.stop();
            _buttonAnimationController.reset();
            _gradientSweepController.stop();
            _gradientSweepController.reset();
            setState(() => _isExtracting = false);
          }
        }
      } catch (e) {
        _log.e("Fehler bei der Textextraktion auf externer Seite: $e");
        if (mounted) {
          _showGeneratedApplicationDialog(context, {'error': e.toString()});

          // Aufräumen
          _buttonAnimationController.stop();
          _buttonAnimationController.reset();
          _gradientSweepController.stop();
          _gradientSweepController.reset();
          setState(() => _isExtracting = false);
        }
      }

      return; // Beende die Methode hier für externe Seiten
    }

    // Ab hier nur für normale Seiten (nicht extern)

    // Prüfe zuerst, ob ein CAPTCHA erkannt wurde und noch nicht gelöst ist
    if (_captchaDetected && !_captchaSolved) {
      // Wenn ein ungelöstes CAPTCHA vorhanden ist, scrolle direkt dorthin und breche ab
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Bitte lösen Sie zuerst das CAPTCHA, um die Kontaktdaten des Arbeitgebers freizuschalten.',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            duration: const Duration(seconds: 5),
            backgroundColor: Colors.red.shade700,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
      // Nur wenn wir nicht auf einer externen Seite sind
      if (!_showExternalLinkInfo) {
        await _scrollToCaptchaAndHighlight();
      } else {
        _log.i(
          "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration",
        );
        // Setze den CAPTCHA-Status explizit
        if (mounted) {
          setState(() {
            _captchaDetected = false;
            _captchaSolved = true;
          });
        }
      }
      return; // Beende die Methode hier
    }

    // Nur für normale Seiten: Zusätzliche Sicherheitsprüfung für CAPTCHA
    final String? securityCheckText = await _extractTextFromWebView();
    if (securityCheckText != null && securityCheckText.isNotEmpty) {
      // Prüfe auf CAPTCHA-Hinweise im Text
      if (securityCheckText.toLowerCase().contains(
            "sicherheitsgründen keine kontaktdaten",
          ) ||
          securityCheckText.toLowerCase().contains(
            "lösen sie bitte die sicherheitsfrage",
          ) ||
          securityCheckText.toLowerCase().contains(
            "lösen sie die sicherheitsfrage",
          ) ||
          securityCheckText.toLowerCase().contains(
            "geben sie die dargestellten zeichen",
          ) ||
          securityCheckText.toLowerCase().contains(
            "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
          )) {
        // CAPTCHA ist nicht gelöst, aktualisiere den Status und breche ab
        if (mounted) {
          setState(() {
            _captchaDetected = true;
            _captchaSolved = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Das CAPTCHA wurde noch nicht gelöst. Bitte lösen Sie es, um fortzufahren.',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              duration: const Duration(seconds: 5),
              backgroundColor: Colors.red.shade700,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
        // Nur wenn wir nicht auf einer externen Seite sind
        if (!_showExternalLinkInfo) {
          await _scrollToCaptchaAndHighlight();
        } else {
          _log.i(
            "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration (securityCheckText)",
          );
          // Setze den CAPTCHA-Status explizit
          if (mounted) {
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
          }
        }
        return; // Beende die Methode hier
      }

      // Prüfe, ob eine E-Mail-Adresse im Text gefunden werden kann
      final String? extractedEmail = _findEmailInText(securityCheckText);
      if (extractedEmail == null || extractedEmail.isEmpty) {
        // Keine E-Mail gefunden, CAPTCHA ist wahrscheinlich nicht gelöst
        if (mounted) {
          setState(() {
            _captchaDetected = true;
            _captchaSolved = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Keine E-Mail-Adresse gefunden. Bitte lösen Sie das CAPTCHA, um die Kontaktdaten freizuschalten.',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              duration: const Duration(seconds: 5),
              backgroundColor: Colors.red.shade700,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
        // Nur wenn wir nicht auf einer externen Seite sind
        if (!_showExternalLinkInfo) {
          await _scrollToCaptchaAndHighlight();
        } else {
          _log.i(
            "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration (extractedEmail)",
          );
          // Setze den CAPTCHA-Status explizit
          if (mounted) {
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
          }
        }
        return; // Beende die Methode hier
      } else {
        // E-Mail gefunden, CAPTCHA ist gelöst
        if (mounted) {
          setState(() {
            _captchaSolved = true;
          });
        }
      }
    }

    if (_isExtracting) return;

    // Normale Verarbeitung für nicht-externe Seiten
    setState(() {
      _isExtracting = true; // Startet den *Gesamtprozess*
      _generationCompletedSuccessfully =
          false; // Bei jedem neuen Start zurücksetzen
    });
    // Animationen starten
    _buttonAnimationController.repeat(reverse: true);
    _gradientSweepController.repeat(); // Sweep starten (nicht reversierend)
    _processingAnimationController
        .repeat(); // Neue hochwertige Animation starten

    String? jobText = '';
    bool captchaDetected = false;
    bool proceedWithGeneration = true; // Flag, ob weitergemacht werden soll

    // 1. Text extrahieren und auf Captcha prüfen
    try {
      final String? extractedJobText = await _extractTextFromWebView();
      // KORREKTUR: Null-Check hinzufügen
      if (extractedJobText == null || extractedJobText.isEmpty) {
        throw Exception('Konnte keinen Text von der Webseite extrahieren.');
      }
      _extractedJobText = extractedJobText; // Jetzt sicher, da nicht null/leer

      // CAPTCHA-Prüfung nur durchführen, wenn wir nicht auf einer externen Seite sind
      if (!_showExternalLinkInfo) {
        captchaDetected = await _checkForCaptcha(); // Ruft interne Logik auf
        _captchaDetected = captchaDetected; // State aktualisieren
        _log.i(
          "Extraktion & Captcha-Check - Text (Länge: ${extractedJobText.length}), Captcha: $captchaDetected",
        );
      } else {
        _log.i("Externe Seite erkannt, überspringe CAPTCHA-Prüfung");
        captchaDetected = false;
        _captchaDetected = false;
        _captchaSolved = true;
      }

      // Wenn ein CAPTCHA erkannt wurde und wir nicht auf einer externen Seite sind, müssen wir prüfen, ob es gelöst werden muss
      if (captchaDetected && !_showExternalLinkInfo) {
        // Prüfe, ob die E-Mail-Adresse des Arbeitgebers im Text gefunden werden kann
        final String? extractedEmail = _findEmailInText(extractedJobText);

        if (extractedEmail == null || extractedEmail.isEmpty) {
          // Keine E-Mail gefunden, CAPTCHA ist nicht gelöst
          setState(() {
            _captchaSolved = false;
            _captchaDetected = true;
          });

          // Zeige eine Snackbar an und scrolle zum CAPTCHA
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'Bitte lösen Sie zuerst das CAPTCHA, um die Kontaktdaten des Arbeitgebers freizuschalten.',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                duration: const Duration(seconds: 5),
                backgroundColor: Colors.red.shade700,
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }

          // Scrolle zum CAPTCHA und beende den Prozess, aber nur wenn wir nicht auf einer externen Seite sind
          if (!_showExternalLinkInfo) {
            await _scrollToCaptchaAndHighlight();
            proceedWithGeneration = false;
          } else {
            _log.i(
              "Externe Seite erkannt, überspringe CAPTCHA-Scroll in _triggerApplicationGeneration (extractedJobText)",
            );
            // Setze den CAPTCHA-Status explizit
            setState(() {
              _captchaDetected = false;
              _captchaSolved = true;
            });
            // Wir setzen proceedWithGeneration nicht auf false, damit die Generierung fortgesetzt wird
          }
        } else {
          // E-Mail gefunden, CAPTCHA ist gelöst
          setState(() {
            _captchaSolved = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        _showGeneratedApplicationDialog(context, {'error': e.toString()});
      }
      proceedWithGeneration = false;
    }

    // 2. Bei Captcha: Setze den Status und beende den Prozess (nur wenn nicht auf einer externen Seite)
    if (proceedWithGeneration &&
        captchaDetected &&
        !_captchaSolved &&
        !_showExternalLinkInfo) {
      // Setze den CAPTCHA-Status, damit der Button entsprechend angezeigt wird
      if (mounted) {
        setState(() {
          _captchaDetected = true;
        });

        // Beende den aktuellen Prozess, damit der Benutzer den CAPTCHA-Button sieht
        proceedWithGeneration = false;
      } else {
        proceedWithGeneration = false;
      }
    }

    // Wenn das CAPTCHA bereits gelöst wurde oder kein CAPTCHA vorhanden ist, fahre mit dem normalen Ablauf fort
    if (proceedWithGeneration) {
      // 3. Auf existierendes Anschreiben prüfen
      String? existingApplication = await _checkExistingApplication();
      if (existingApplication != null) {
        _log.i("Prüfung existierendes Anschreiben: Vorhanden");
        final choice = await _showUseExistingApplicationDialog(
          context,
          existingApplication,
        );
        if (choice == 'cancel') {
          _log.i("Wahl: Abbruch");
          proceedWithGeneration = false;
        } else if (choice == 'use_existing') {
          _log.i("Wahl: Bestehendes verwenden");
          if (mounted) {
            _showGeneratedApplicationDialog(context, {
              'generatedText': existingApplication,
            });
          }
          proceedWithGeneration = false; // Nicht neu generieren
        } else {
          _log.i("Wahl: Neu generieren");
          // proceedWithGeneration bleibt true
        }
      }

      // 4. Premium/Ad-Check (NUR wenn neu generiert werden soll)
      if (proceedWithGeneration) {
        final userProfileState = ref.read(userProfileProvider);
        UserProfile? userProfile;
        if (userProfileState is AsyncData<UserProfile?>) {
          userProfile = userProfileState.value;
        }

        // Aktuellen Premium-Status prüfen (Fallback auf false)
        final bool isPremium = userProfile?.isPremium ?? false;
        _log.i("Premium/Ad Check - isPremium: $isPremium");

        if (!isPremium) {
          // NICHT Premium: Dialog anzeigen
          if (!mounted) return; // Sicherheitscheck

          // Ladezustand für Ad setzen
          setState(() => _isLoadingAd = true);

          // KORREKTUR: Benannte Argumente verwenden
          await showPremiumOrAdDialog(
            context: context,
            ref: ref,
            onAdWatched: () {
              // Dieser Callback wird *jetzt* nur noch benötigt, um den Prozess fortzusetzen,
              // die Belohnungslogik und das Schließen des Ladedialogs geschieht in premium_dialogs
              _log.i("Ad Watched (Callback in JobDetail) - Starte Generierung");
              if (mounted) {
                // *** NEU: State für Animation hier setzen und Animation starten ***
                setState(() {
                  _isExtracting =
                      true; // Zeigt "Generiere..." an und blockiert Button
                  _isLoadingAd = false; // Ad-Laden ist definitiv vorbei
                  _generationCompletedSuccessfully =
                      false; // Sicherstellen, dass zurückgesetzt
                });
                _buttonAnimationController.repeat(reverse: true);
                _gradientSweepController.repeat(); // Sweep starten

                // Ladezustand (_isLoadingAd) sollte bereits in premium_dialogs/ad_service zurückgesetzt worden sein
                _startGenerationProcess();
              }
            },
            // adService und paymentService werden innerhalb der Dialogfunktion geholt
            // title und content verwenden Standardwerte
          );

          // Ladezustand sicherheitshalber hier auch zurücksetzen
          if (mounted && _isLoadingAd) {
            // Nur wenn es noch aktiv war
            setState(() => _isLoadingAd = false);
            _log.i(
              "Ladezustand nach Premium/Ad-Dialog zurückgesetzt (Fallback)",
            );
          }

          // Da der Dialog keinen Wert mehr zurückgibt, setzen wir hier proceedWithGeneration auf false,
          // es sei denn, onAdWatched wurde ausgeführt und hat _startGenerationProcess getriggert.
          proceedWithGeneration = false;
          _log.i(
            "Nach Premium/Ad Dialog: proceedWithGeneration auf false gesetzt. Weiter nur via onAdWatched.",
          );
        }
        // Wenn isPremium true ist, wird proceedWithGeneration nicht verändert und es geht weiter
      }

      // 5. Generierung starten (wenn proceedWithGeneration immer noch true ist)
      if (proceedWithGeneration) {
        // Dieser Block wird nur noch erreicht, wenn isPremium true war ODER
        // wenn die Ad-Logik fehlerhaft ist und proceed nicht auf false gesetzt wurde.
        _log.i(
          "Starte _startGenerationProcess (isPremium war true oder Fehler in Ad-Logik)",
        );
        await _startGenerationProcess();
      }
    }

    // Aufräumen (wird erreicht nach Abbruch oder nach _startGenerationProcess)
    if (mounted && !_isLoadingAd) {
      // Nur aufräumen, wenn nicht gerade Ad lädt
      _buttonAnimationController.stop();
      _buttonAnimationController.reset(); // Animation zurücksetzen
      _gradientSweepController.stop(); // Sweep stoppen
      _gradientSweepController.reset(); // Sweep zurücksetzen
      _processingAnimationController.stop(); // Neue Animation stoppen
      _processingAnimationController.reset(); // Neue Animation zurücksetzen
      setState(() => _isExtracting = false); // Gesamtprozess beenden
      _log.i(
        "_triggerApplicationGeneration abgeschlossen oder abgebrochen, UI aufgeräumt",
      );
    }
  }

  // Hilfsmethode, die den eigentlichen Generierungsprozess startet
  Future<void> _startGenerationProcess({bool isExternalPage = false}) async {
    if (_extractedJobText.isNotEmpty) {
      // Rufe direkt die KI-Generierung auf
      _log.i(
        "Aufruf _generateApplicationWithAI mit jobText (Länge: ${_extractedJobText.length}), isExternalPage: $isExternalPage",
      );

      // Wenn wir auf einer externen Seite sind, setzen wir den CAPTCHA-Status explizit
      if (isExternalPage) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }

      final result = await _generateApplicationWithAI(_extractedJobText);

      // Speichere das NEU generierte Anschreiben
      if (result['generatedText'] != null && result['error'] == null) {
        await _saveGeneratedApplication(result['generatedText'] as String);
        _log.i("Neu generiertes Anschreiben gespeichert.");

        // Setze den CAPTCHA-Status zurück, aber nur wenn wir nicht auf einer externen Seite sind
        if (!isExternalPage) {
          _captchaSolved = false;
        }
      }

      if (mounted) {
        // Wenn wir auf einer externen Seite sind, fügen wir eine Dummy-E-Mail hinzu
        if (isExternalPage &&
            result['extractedEmail'] == null &&
            result['error'] == null) {
          // Kopiere das Ergebnis und füge eine Dummy-E-Mail hinzu
          final Map<String, dynamic> modifiedResult = Map.from(result);
          modifiedResult['extractedEmail'] = '<EMAIL>';
          _showGeneratedApplicationDialog(context, modifiedResult);
        } else {
          _showGeneratedApplicationDialog(context, result);
        }
      }
    } else {
      if (mounted) {
        _showGeneratedApplicationDialog(context, {
          'error':
              'Konnte keinen Text von der Webseite extrahieren (in _startGenerationProcess).',
        });
      }
    }
    // Hier wird _isExtracting nicht mehr auf false gesetzt, das passiert in _triggerApplicationGeneration
  }

  // ... _buildPrompt, _handleApplication, _applyForJob, build method ...

  // ... Rest der Methoden ...

  String _cleanupHints(String text) {
    String cleanedText = text; // Start with original text

    // ... (Rest der cleanupHints-Methode bleibt gleich) ...

    return cleanedText.trim();
  }

  Future<void> _launchUrl(String urlString) async {
    final uri = Uri.parse(urlString);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      _log.e('Could not launch $urlString');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Konnte URL nicht öffnen: $urlString')),
        );
      }
    }
  }
    bool isError = error != null;
    bool showCaptchaWarning =
        _captchaDetected &&
        (extractedEmail == null || extractedEmail.isEmpty) &&
        !isError &&
        !_showExternalLinkInfo; // Keine CAPTCHA-Warnung auf externen Seiten

    // *** NEU: Erfolgsstatus setzen, wenn kein Fehler ***
    if (!isError && mounted) {
      setState(() {
        _generationCompletedSuccessfully = true;
      });
    }

    String contentToShow = isError ? error : generatedText;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isError ? 'Fehler' : 'KI-Bewerbungsentwurf'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Getrennte Captcha-Warnung
                  if (showCaptchaWarning)
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      margin: const EdgeInsets.only(bottom: 12.0),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: Colors.orange,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'CAPTCHA erkannt! E-Mail-Adresse konnte nicht extrahiert werden. Bitte manuell prüfen.',
                              style: TextStyle(
                                color: Colors.orange,
                                fontSize: 13,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  // Eigentlicher Text
                  SelectableText(
                    contentToShow,
                  ), // SelectableText für einfaches Kopieren
                ],
              ),
            ),
            actions: [
              // Schließen-Button
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Schließen'),
              ),
              // Kopieren-Button (nur wenn kein Fehler)
              if (!isError && generatedText.isNotEmpty)
                TextButton.icon(
                  icon: const Icon(Icons.copy, size: 16),
                  label: const Text('Kopieren'),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: generatedText));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Bewerbungstext kopiert!')),
                    );
                  },
                ),
              // E-Mail-Button (nur wenn kein Fehler)
              if (!isError && generatedText.isNotEmpty)
                ElevatedButton.icon(
                  icon: const Icon(Icons.email_outlined),
                  label: const Text('Per E-Mail senden'),
                  onPressed: () {
                    _log.i("'Per E-Mail senden' geklickt");
                    _log.i(
                      "Wert von extractedEmail im Dialog: $extractedEmail",
                    );
                    // Navigator.of(context).pop(); // NICHT MEHR SCHLIESSEN
                    _shareViaEmail(
                      generatedText,
                      extractedEmail:
                          extractedEmail, // Wert wird hier übergeben
                    );
                  },
                ),
            ],
          ),
    );
  }

  // E-Mail Body korrigiert und Anhang hinzugefügt
  Future<void> _shareViaEmail(String text, {String? extractedEmail}) async {
    try {
      final userProfileState = ref.read(userProfileProvider);
      UserProfile? userProfile;

      if (userProfileState is AsyncData<UserProfile?>) {
        userProfile = userProfileState.value;
      }

      if (userProfile == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Benutzerprofil nicht geladen. Versuche es später erneut.',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      String emailBody = text;

      // Supabase Client für lokalen Download holen
      final supabaseClient = ref.read(supabaseClientProvider);

      // NEU: Anzeigen, dass wir die Datei herunterladen
      setState(() => _isDownloadingCv = true);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Lebenslauf wird für E-Mail-Anhang vorbereitet..."),
        ),
      );

      // Lokalen Pfad für den CV holen, falls vorhanden
      String? localCvPath;
      if (userProfile.cvFilePath != null &&
          userProfile.cvFilePath!.isNotEmpty) {
        try {
          localCvPath = await _downloadCvLocally(
            supabaseClient,
            userProfile.cvFilePath!,
          );
          if (localCvPath == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Lebenslauf konnte nicht heruntergeladen werden.',
                ),
                backgroundColor: Colors.red,
              ),
            );
          } else {
            _log.i("Lokaler CV Pfad für E-Mail-Anhang: $localCvPath");
          }
        } catch (e) {
          _log.e('Fehler beim Herunterladen des Lebenslaufs: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Fehler beim Herunterladen des Lebenslaufs: ${e.toString()}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        } finally {
          setState(() => _isDownloadingCv = false);
        }
      } else {
        setState(() => _isDownloadingCv = false);
      }

      // Erstelle temporäre Kopie mit gewünschtem Dateinamen
      String? tempCvPath;
      if (localCvPath != null) {
        tempCvPath = await CvStorageHelper.createTemporaryCvCopy(
          localCvPath,
          'Lebenslauf.pdf',
        );
      }

      // Bereitstellen der E-Mail-Parameter
      final Email email = Email(
        body: emailBody,
        subject: 'Bewerbung für: ${widget.jobTitle}',
        recipients: extractedEmail != null ? [extractedEmail] : [],
        // Verwende temporäre Kopie mit korrektem Dateinamen
        attachmentPaths: tempCvPath != null ? [tempCvPath] : [],
        isHTML: false,
      );

      _log.i("--- Preparing email with flutter_email_sender ---");
      _log.i("Recipient: ${email.recipients}");
      _log.i("Subject: ${email.subject}");
      _log.i("Attachment Path: ${email.attachmentPaths}");

      // VERBESSERTE FEHLERBEHANDLUNG
      try {
        // Jetzt erst die E-Mail versenden
        await FlutterEmailSender.send(email);

        // Lösche temporäre Datei nach erfolgreichem Versand
        if (tempCvPath != null) {
          await CvStorageHelper.deleteTemporaryCvCopy(tempCvPath);
        }

        // --- ERFOLGSMELDUNG ENTFERNT ---
        // if (mounted) {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(
        //       content: const Text('E-Mail-App wurde geöffnet. Überprüfen Sie Ihre E-Mail-App, um die Nachricht zu senden.'),
        //       backgroundColor: Colors.green,
        //       duration: const Duration(seconds: 3),
        //     ),
        //   );
        // }
        // --- ENDE ERFOLGSMELDUNG ENTFERNT ---
      } on PlatformException catch (pe) {
        _log.e(
          'Platform Exception beim E-Mail-Versand: ${pe.message}',
          error: pe,
        );
        if (mounted) {
          // Benutzerfreundliche Fehlermeldung anzeigen
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'E-Mail konnte nicht gesendet werden: ${_getReadableEmailError(pe.message)}',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Alternative',
                onPressed: () {
                  // Alternative Methode anbieten - z.B. Text in Zwischenablage kopieren
                  Clipboard.setData(ClipboardData(text: emailBody));
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Text wurde in die Zwischenablage kopiert',
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          );
        }
      } catch (e) {
        _log.e('Allgemeiner Fehler beim E-Mail-Versand: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'E-Mail konnte nicht geöffnet werden: ${e.toString()}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stack) {
      _log.e('Fehler beim Vorbereiten der E-Mail: $e\\n$stack');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Teilen per E-Mail: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Neue Hilfsmethode für benutzerfreundlichere Fehlermeldungen
  String _getReadableEmailError(String? errorMessage) {
    if (errorMessage == null) return "Unbekannter Fehler";

    // Bekannte Fehlermeldungen übersetzen
    if (errorMessage.contains("No email clients found") ||
        errorMessage.contains("No Activity found to handle Intent")) {
      return "Keine E-Mail-App auf diesem Gerät gefunden";
    }

    if (errorMessage.contains("User canceled")) {
      return "Vorgang vom Benutzer abgebrochen";
    }

    if (errorMessage.contains("NullPointerException")) {
      return "Problem beim Öffnen der E-Mail-App";
    }

    // Fallback auf die originale Fehlermeldung
    return errorMessage;
  }

  // NEU: Hilfsfunktion zum Herunterladen der CV
  Future<String?> _downloadCvLocally(
    SupabaseClient supabaseClient,
    String storagePath,
  ) async {
    if (storagePath.isEmpty) return null;
    _log.i("Downloading CV from Supabase path: $storagePath");
    try {
      final bytes = await supabaseClient.storage
          .from('cv-backups') // Dein Bucket-Name
          .download(storagePath);

      final tempDir = await getTemporaryDirectory();
      final fileName = p.basename(
        storagePath,
      ); // Extrahiere Dateinamen aus Pfad
      final localFilePath = '${tempDir.path}/$fileName';
      final file = File(localFilePath);
      await file.writeAsBytes(bytes, flush: true);
      _log.i("CV downloaded and saved locally to: $localFilePath");
      return localFilePath;
    } catch (e, stack) {
      _log.e("Error downloading CV locally: $e\n$stack");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              "Fehler beim Herunterladen des Lebenslaufs für E-Mail-Anhang: ${e.toString()}",
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return null;
    }
  }

  // Methoden für gespeicherte Bewerbungen (optional, aber zur Vollständigkeit)
  Future<String?> _checkExistingApplication() async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    return prefs.getString(jobKey);
  }

  Future<void> _saveGeneratedApplication(String text) async {
    final prefs = await SharedPreferences.getInstance();
    final String jobKey = 'application_${widget.jobRefnr}';
    await prefs.setString(jobKey, text);
  }

  // --- NEUE Hilfsfunktion zum Formatieren der Schlüssel für den Prompt ---
  String _formatPromptKey(String key) {
    switch (key) {
      case 'name':
        return 'Name des Bewerbers';
      case 'email':
        return 'E-Mail des Bewerbers';
      case 'phoneNumber':
        return 'Telefonnummer';
      case 'skills':
        return 'Fähigkeiten';
      case 'workExperience':
        return 'Berufserfahrung';
      case 'education':
        return 'Ausbildung';
      case 'jobPreferences':
        return 'Jobpräferenzen';
      case 'experienceSummary':
        return 'Zusammenfassung der Erfahrung';
      case 'interests':
        return 'Interessen';
      case 'preferredWritingStyle':
        return 'Bevorzugter Schreibstil';
      case 'includeExperienceInApplication':
        return 'Berufserfahrung im Anschreiben erwähnen';
      default:
        return key; // Fallback
    }
  }
  // --- Ende Hilfsfunktion ---

  // Diese Funktion wurde entfernt, da wir jetzt direkt eine Snackbar anzeigen
  // und den CAPTCHA-Status automatisch setzen

  // Methode _handleApplication verbessert mit klareren Kommentaren
  void _handleApplication(BuildContext context, WidgetRef ref) {
    _log.i(
      "_handleApplication aufgerufen - löst _triggerApplicationGeneration aus",
    );

    // _triggerApplicationGeneration beinhaltet jetzt den Premium Check
    _triggerApplicationGeneration();
  }

  // Methode _applyForJob wiederhergestellt
  void _applyForJob(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Bewerbungsprozess gestartet (Platzhalter)'),
      ),
    );
  }

  // Verbesserte Methode zum Finden einer E-Mail-Adresse im Text
  String? _findEmailInText(String text) {
    // Wenn wir auf einer externen Seite sind, ignorieren wir die CAPTCHA-Prüfung
    if (_showExternalLinkInfo) {
      _log.i("Externe Seite erkannt, CAPTCHA-Prüfung wird übersprungen");
      if (mounted) {
        setState(() {
          _captchaDetected = false;
          _captchaSolved = true;
        });
      }
      return "<EMAIL>"; // Dummy-E-Mail zurückgeben, um CAPTCHA-Prüfung zu umgehen
    }

    // Wenn das CAPTCHA nicht gelöst ist, sollte der Text keine E-Mail-Adresse enthalten
    // Wir prüfen, ob bestimmte Hinweise auf ein ungelöstes CAPTCHA vorhanden sind
    if (text.toLowerCase().contains("sicherheitsgründen keine kontaktdaten") ||
        text.toLowerCase().contains("lösen sie bitte die sicherheitsfrage") ||
        text.toLowerCase().contains("lösen sie die sicherheitsfrage") ||
        text.toLowerCase().contains("geben sie die dargestellten zeichen") ||
        text.toLowerCase().contains(
          "kontaktdaten des arbeitgebers vor unerlaubten zugriffen",
        )) {
      _log.i(
        "CAPTCHA-Hinweis im Text gefunden, E-Mail-Suche wird übersprungen",
      );
      return null; // CAPTCHA ist definitiv nicht gelöst
    }

    // Wenn der Text Hinweise auf ein gelöstes CAPTCHA enthält
    if (text.toLowerCase().contains("e-mail:") ||
        text.toLowerCase().contains("e-mail-adresse:") ||
        text.toLowerCase().contains("email:") ||
        text.toLowerCase().contains("kontakt:") ||
        text.toLowerCase().contains("bewerbung an:") ||
        text.toLowerCase().contains("bewerbungen an:")) {
      // Verbesserte E-Mail-Regex mit mehr Kontext
      final emailWithLabelRegex = RegExp(
        r'(?:e-?mail|kontakt|bewerbung(?:en)?)(?:\s*:|bei|an|unter)?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        caseSensitive: false,
      );
      final labelMatch = emailWithLabelRegex.firstMatch(text);
      if (labelMatch != null && labelMatch.groupCount >= 1) {
        _log.i("E-Mail mit Label gefunden: ${labelMatch.group(1)}");
        return labelMatch.group(1);
      }
    }

    // Standard-E-Mail-Suche als Fallback
    final emailRegex = RegExp(
      r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
    );
    final matches = emailRegex.allMatches(text).toList();

    // Wenn mehrere E-Mails gefunden wurden, bevorzuge die mit "bewerbung" oder "karriere"
    if (matches.length > 1) {
      for (final match in matches) {
        final email = match.group(0)?.toLowerCase() ?? "";
        if (email.contains("bewerbung") ||
            email.contains("karriere") ||
            email.contains("job") ||
            email.contains("personal") ||
            email.contains("hr")) {
          _log.i("Bevorzugte E-Mail gefunden: $email");
          return match.group(0);
        }
      }
    }

    // Fallback: Erste gefundene E-Mail zurückgeben
    if (matches.isNotEmpty) {
      _log.i("Fallback: Erste E-Mail gefunden: ${matches.first.group(0)}");
      return matches.first.group(0);
    }

    _log.i("Keine E-Mail im Text gefunden");
    return null;
  }

  // Methode _buildPrompt wiederhergestellt
  String _buildPrompt({
    required String jobText,
    required UserProfile userProfile,
    required bool includeExperience,
    required String stylePreference,
  }) {
    final Map<String, dynamic> profileDataForPrompt = {
      'name': userProfile.name,
      'email': userProfile.email,
      'phoneNumber': userProfile.phoneNumber,
      'skills':
          userProfile.skills.isNotEmpty ? userProfile.skills.join(', ') : null,
      'workExperience':
          userProfile.workExperience.isNotEmpty
              ? userProfile.workExperience
                  .map(
                    (exp) =>
                        '${exp.position} bei ${exp.company} (${DateFormat('MM/yyyy', 'de_DE').format(exp.startDate)} - ${exp.endDate != null ? DateFormat('MM/yyyy', 'de_DE').format(exp.endDate!) : 'heute'}): ${exp.description}',
                  )
                  .join('\n\n')
              : null,
      'education':
          userProfile.education.isNotEmpty
              ? userProfile.education
                  .map(
                    (edu) =>
                        '${edu.degree}${edu.fieldOfStudy != null && edu.fieldOfStudy!.isNotEmpty ? ' im Bereich ${edu.fieldOfStudy}' : ''} an der ${edu.institution} (${DateFormat('MM/yyyy', 'de_DE').format(edu.startDate)} - ${edu.endDate != null ? DateFormat('MM/yyyy', 'de_DE').format(edu.endDate!) : 'heute'})',
                  )
                  .join('\n')
              : null,
      'jobPreferences': {
        'targetPosition': userProfile.jobPreferences.targetPosition,
        'industry': userProfile.jobPreferences.industry,
        'locationPreference': userProfile.jobPreferences.locationPreference,
        'desiredSalary': userProfile.jobPreferences.desiredSalary,
        'employmentType': userProfile.jobPreferences.employmentType,
      },
      'experienceSummary': userProfile.experienceSummary,
      'interests': userProfile.interests,
      'preferredWritingStyle': stylePreference,
      'includeExperienceInApplication': includeExperience,
    };

    profileDataForPrompt.removeWhere(
      (key, value) =>
          value == null ||
          (value is String && value.isEmpty) ||
          (value is List && value.isEmpty),
    );
    if (profileDataForPrompt['jobPreferences'] is Map) {
      (profileDataForPrompt['jobPreferences'] as Map).removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      if ((profileDataForPrompt['jobPreferences'] as Map).isEmpty) {
        profileDataForPrompt.remove('jobPreferences');
      }
    }

    String formattedProfileData =
        "Hier sind die Informationen über den Bewerber:\n";
    profileDataForPrompt.forEach((key, value) {
      if (value != null) {
        String formattedValue;
        if (value is Map) {
          formattedValue = value.entries
              .map((e) => "- ${e.key}: ${e.value}")
              .join('\n  ');
          formattedProfileData +=
              "\n${_formatPromptKey(key)}:\n  $formattedValue";
        } else if (value is List) {
          formattedValue = value.join(', ');
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else if (value is bool) {
          formattedValue = value ? 'Ja' : 'Nein';
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        } else {
          formattedValue = value.toString();
          formattedProfileData += "\n${_formatPromptKey(key)}: $formattedValue";
        }
      }
    });

    if (!includeExperience) {
      formattedProfileData +=
          "\n\nWichtiger Hinweis: Der Bewerber möchte NICHT, dass seine detaillierte Berufserfahrung im Anschreiben explizit aufgelistet wird. Konzentriere dich stattdessen auf die Fähigkeiten und die Eignung für die Stelle.";
    }

    final String style = stylePreference;
    final String detailedStyleInstructions =
        DeepSeekPrompts.styleInstructions[style] ?? '';
    final String emailInstructions = '''
Wichtig: Erstelle eine E-Mail-Bewerbung, KEINEN Brief. Beginne direkt mit der Anrede (z.B. "Sehr geehrte/r ..."). Füge KEINE Betreffzeile oder Absender-/Empfängeradressen in den generierten Text ein.''';

    final prompt = DeepSeekPrompts.generateCoverLetterPrompt(
      jobTitle: widget.jobTitle,
      jobDescription: jobText,
      personalizationText: formattedProfileData,
      styleInstructions: detailedStyleInstructions + emailInstructions,
    );

    return prompt;
  }

  // Methode _launchMailto wiederhergestellt
  Future<void> _launchMailto({
    required String to,
    String? subject,
    String? body,
  }) async {
    String? encodedSubject =
        subject != null ? Uri.encodeComponent(subject) : null;
    String? encodedBody = body
        ?.replaceAll(' ', '%20')
        .replaceAll('\n', '%0D%0A');
    String mailtoLink = 'mailto:$to';
    List<String> queryParts = [];
    if (encodedSubject != null) queryParts.add('subject=$encodedSubject');
    if (encodedBody != null) queryParts.add('body=$encodedBody');
    if (queryParts.isNotEmpty) mailtoLink += '?${queryParts.join('&')}';

    _log.i("Versuche Mailto-Link zu starten (manuell codiert): $mailtoLink");
    final Uri mailUri = Uri.parse(mailtoLink);

    try {
      final bool launched = await launchUrl(mailUri);
      if (!launched) {
        _log.e('Could not launch $mailUri');
        _showMailtoError();
      }
    } catch (e) {
      _log.e('Error launching mailto: $e');
      _showMailtoError();
    }
  }

  // Methode _showMailtoError wiederhergestellt
  void _showMailtoError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('E-Mail-App konnte nicht geöffnet werden.'),
        ),
      );
    }
  }

  // --- NEU: Dialog, um zu fragen, ob bestehendes Anschreiben genutzt werden soll ---
  Future<String?> _showUseExistingApplicationDialog(
    BuildContext context,
    String existingText,
  ) async {
    return showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Bestehendes Anschreiben gefunden'),
            content: SingleChildScrollView(
              // Scrollbar hinzugefügt
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Für diesen Job existiert bereits folgender Entwurf:',
                  ),
                  const SizedBox(height: 15),
                  // Container entfernt, Text direkt angezeigt
                  SelectableText(
                    existingText, // Gesamten Text anzeigen
                    style: const TextStyle(fontSize: 14), // Kleinere Schrift
                  ),
                  const SizedBox(height: 15),
                  const Text(
                    'Möchten Sie diesen Entwurf verwenden oder einen neuen generieren?',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('cancel'), // Option 'Abbrechen'
                child: const Text('Abbrechen'),
              ),
              TextButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('use_existing'), // Option 'Bestehendes nutzen'
                child: const Text('Bestehenden nutzen'),
              ),
              ElevatedButton(
                onPressed:
                    () => Navigator.of(
                      context,
                    ).pop('generate_new'), // Option 'Neu generieren'
                child: const Text('Neu generieren'),
              ),
            ],
          ),
    );
  }
  // --- Ende neuer Dialog ---

  // Hinzugefügt: build Methode
  @override
  Widget build(BuildContext context) {
    final bool showWebView = !kIsWeb;

    // Favoritenstatus abrufen
    final favoritesState = ref.watch(favoritesProvider);
    final isFavorite = favoritesState.maybeWhen(
      data:
          (jobs) => jobs.any(
            (job) => job.id == widget.jobEntity?.id,
          ), // Vergleiche mit optionaler JobEntity ID
      orElse:
          () => false, // Standardmäßig nicht Favorit, wenn Daten nicht geladen
    );

    // HINWEIS: Footer-Widget extrahieren NICHT MEHR NÖTIG

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.jobTitle,
          style: const TextStyle(fontSize: 16), // Anpassung für lange Titel
        ),
        actions: [
          // Favoriten-Toggle Button
          if (widget.jobEntity !=
              null) // Nur anzeigen, wenn JobEntity vorhanden ist
            IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : null,
              ),
              tooltip:
                  isFavorite
                      ? 'Von Favoriten entfernen'
                      : 'Zu Favoriten hinzufügen',
              onPressed: () {
                if (isFavorite) {
                  ref
                      .read(favoritesProvider.notifier)
                      .removeFavorite(widget.jobEntity!.id);
                } else {
                  ref
                      .read(favoritesProvider.notifier)
                      .addFavorite(widget.jobEntity!);
                }
              },
            ),
          // Zurück-Button für WebView
          if (_canGoBack)
            IconButton(
              icon: const Icon(Icons.arrow_back),
              tooltip: 'Zurück',
              onPressed: () {
                _controller.goBack();
              },
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Neu laden',
            onPressed: () {
              _controller.reload();
            },
          ),
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            tooltip: 'Im Browser öffnen',
            onPressed:
                () => _launchUrl(
                  'https://www.arbeitsagentur.de/jobsuche/jobdetail/${widget.jobRefnr}',
                ),
          ),
        ],
      ),
      // Wichtige Eigenschaften für die richtige Anzeige der System-Navigation
      extendBody:
          true, // Wichtig: Erlaubt dem Body, sich hinter die Bottom-Navigation zu erstrecken
      extendBodyBehindAppBar: false, // Nicht nötig für die AppBar
      backgroundColor:
          Theme.of(
            context,
          ).scaffoldBackgroundColor, // Sicherstellen, dass der Hintergrund undurchsichtig ist
      // ANPASSUNG: resizeToAvoidBottomInset WIEDER auf false, da wir es manuell handhaben
      resizeToAvoidBottomInset: false,
      // ANPASSUNG: Body ist wieder ein Stack
      body: Stack(
        children: [
          // Verbesserte Warnung für fehlende Jobbeschreibung oder externe Links
          if (_showExternalLinkInfo)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Builder(
                builder: (context) {
                  _log.d(
                    'BANNER_DEBUG: Build - Baue MaterialBanner, da _showExternalLinkInfo true ist.',
                  );
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8.0),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.red.shade50, Colors.orange.shade50],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withAlpha(50),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.red.shade300,
                          width: 1.5,
                        ),
                      ),
                      child: MaterialBanner(
                        padding: const EdgeInsets.all(AppTheme.spacingMedium),
                        content: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.warning_amber_rounded,
                                  color: Colors.red,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Unvollständige Jobbeschreibung erkannt!',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Diese Seite enthält möglicherweise nicht die vollständige Jobbeschreibung. Bitte klicken Sie auf den Button "Auf Seite suchen" unten, um den Button "Externe Seite öffnen" zu finden und zu markieren.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blueGrey.shade800,
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Hinweis: Es wird nur nach dem exakten Button "Externe Seite öffnen" gesucht.',
                              style: TextStyle(
                                fontSize: 13,
                                fontStyle: FontStyle.italic,
                                color: Colors.blueGrey.shade700,
                              ),
                            ),
                          ],
                        ),
                        leading: Stack(
                          alignment: Alignment.center,
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.red.shade100,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const Icon(
                              Icons.warning_amber_rounded,
                              color: Colors.red,
                              size: 32,
                            ),
                          ],
                        ),
                        backgroundColor: Colors.transparent,
                        forceActionsBelow: true,
                        actions: [
                          TextButton.icon(
                            icon: const Icon(Icons.visibility_off),
                            label: const Text('Ausblenden'),
                            onPressed: () {
                              if (mounted) {
                                setState(() {
                                  _showExternalLinkInfo = false;
                                });
                              }
                            },
                          ),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.search),
                            label: const Text('Auf Seite suchen'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () {
                              // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                              _controller.runJavaScript('''
                                (function() {
                                  // Strikt nur "externe seite öffnen"
                                  const keywords = [
                                    'externe seite öffnen'
                                  ];

                                  // Füge CSS-Animation für den Button hinzu
                                  const animationStyle = document.createElement('style');
                                  animationStyle.textContent = `
                                    @keyframes pulseButton {
                                      0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                                      50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                                      100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
                                    }

                                    @keyframes glowBorder {
                                      0% { border-color: #FFC107; }
                                      50% { border-color: #FF5722; }
                                      100% { border-color: #FFC107; }
                                    }

                                    .external-link-highlight {
                                      background-color: #FFEB3B important;
                                      border: 3px solid #FFC107 important;
                                      padding: 8px 16px !important;
                                      border-radius: 4px !important;
                                      box-shadow: 0 0 15px rgba(,255, 193, 7, 0.8) important;
                                      position: relative !important;
                                      animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                                      transition: all 0.3s ease !important;
                                      z-index: 9999 important;
                                      font-weight: bold !important;
                                    }

                                    .external-link-highlight:befre {
                                      content: "👉 " important;
                                    }

                                    .external-link-highlight:after {
                                      content: " 👈" important;
                                    }

                                    .external-link-hghlight:hover {
                                      transform: scale(1.1) !important;
                                      background-color: #FFC107 important;
                                    }
                                  `;
                                  document.head.appenChild(animationStyle);

                                  // Suche nach Elementen mit diesen Keywords
                                  const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                                  let foundElements = [];

                                  // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                                  for (let i = 0; i < elements.length; i++) {
                                    const text = (elements[i].inerText || elements[i].textContent || '').toLowerCase().trim();

                                    // Exakte Übereinstimmung mit "externe seite öffnen"
                                    if (text === 'externe seite öffnen') {
                                      foundElements.push({
                                        element: elements[i],
                                        keyword: 'externe seite öffnen'
                                      });
                                    }
                                  }

                                  if (foundElements.length > 0) {
                                    // Sortiere nach Sichtbarkeit und Position im Dokument
                                    foundElements.sort((a, b) => {
                                      const aRect = a.element.getBoundingClientRect();
                                      const bRect = b.element.getBoundingClientRect();

                                      // Prüfe, ob das Element sichtbar ist
                                      const aVisible = aRect.width > 0 && aRect.height > 0;
                                      const bVisible = bRect.width > 0 && bRect.height > 0;

                                      if (aVisible && !bVisible) return -1;
                                      if (!aVisible && bVisible) return 1;

                                      // Wenn beide sichtbar oder beide nicht sichtbar, sortiere nach Position
                                      return aRect.top - bRect.top;
                                    });

                                    // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                                    foundElements.forEach((item, index) => {
                                      // Wende die Klasse auf das Element an
                                      item.element.classList.add('external-link-highlight');

                                      // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                                      if (!item.element.title) {
                                        item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                                      }

                                      // Scrolle nur zum ersten Element
                                      if (index === 0) {
                                        item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                                      }
                                    });

                                    // Zeige eine Meldung mit der Anzahl der gefundenen Links
                                    if (foundElements.length > 1) {
                                      alert("Es wurden " + foundElements.length + " Buttons mit dem exakten Text 'Externe Seite öffnen' gefunden und hervorgehoben. Bitte klicken Sie auf einen der hervorgehobenen Buttons.");
                                    } else {
                                      alert("Ein Button mit dem exakten Text 'Externe Seite öffnen' wurde gefunden und hervorgehoben.");
                                    }
                                  } else {
                                    // Wenn nichts gefunden wurde, zeige eine Meldung
                                    alert("Kein Button mit dem exakten Text 'Externe Seite öffnen' gefunden. Bitte suchen Sie manuell nach einem Link zur externen Seite.");
                                  }
                                })();
                              ''');
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

          // WebView oder Platzhalter - füllt den Stack
          // ANPASSUNG: Padding unten hinzufügen, basierend auf Footer-Höhe
          Padding(
            padding: EdgeInsets.only(bottom: _footerHeight),
            child: Stack(
              children: [
                if (showWebView)
                  WebViewWidget(
                    controller: _controller,
                    gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                      Factory<VerticalDragGestureRecognizer>(
                        () => VerticalDragGestureRecognizer(),
                      ),
                      Factory<TapGestureRecognizer>(
                        () => TapGestureRecognizer(),
                      ),
                    },
                  )
                else
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        "WebView ist im Web-Modus nicht verfügbar.",
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  ),
                // Lade- und Fehleranzeigen (innerhalb des gepaddeten Bereichs)
                if (_isLoadingPage)
                  const Center(child: CircularProgressIndicator()),
                if (_hasPageError)
                  const Center(
                    child: Text(
                      "Fehler beim Laden der Jobseite.",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),

          // Footer mit Hinweisfeld und Buttons - wieder im AnimatedPositioned
          AnimatedPositioned(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeOutQuad,
            left: 0,
            right: 0,
            // Positionierung wie vorher
            bottom:
                (_isHintsTextFieldFocused && _isKeyboardVisible)
                    ? MediaQuery.of(context).viewInsets.bottom
                    : 0,
            // ANPASSUNG: GlobalKey hinzufügen
            child: Container(
              key: _footerKey,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 25),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 0.5,
                  ),
                ),
              ),
              // Padding wie vorher, inkl. Berücksichtigung der Navigationsleiste
              padding: EdgeInsets.only(
                left: AppTheme.spacingMedium,
                right: AppTheme.spacingMedium,
                top: AppTheme.spacingSmall,
                bottom:
                    (_isHintsTextFieldFocused && _isKeyboardVisible)
                        ? AppTheme.spacingSmall
                        : max(
                          AppTheme.spacingSmall,
                          MediaQuery.of(context).viewPadding.bottom,
                        ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Job-Metadaten
                    _buildJobMetadataSection(context),
                    const SizedBox(height: AppTheme.spacingSmall),

                    // --- HINWEISE FELD (Logik geändert) ---
                    Consumer(
                      builder: (context, ref, child) {
                        final userProfileState = ref.watch(userProfileProvider);
                        final isPremium =
                            userProfileState.asData?.value.isPremium ?? false;

                        if (isPremium) {
                          return TextField(
                            controller: _hintsController,
                            focusNode: _hintsFocusNode,
                            maxLines: 3,
                            minLines: 1,
                            decoration: InputDecoration(
                              labelText:
                                  'Zusätzliche Hinweise für die KI (optional)',
                              hintText:
                                  'z.B. Gehaltsvorstellung erwähnen, bestimmten Skill hervorheben...',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMedium,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingMedium,
                                vertical: AppTheme.spacingSmall,
                              ),
                            ),
                          );
                        } else {
                          return OutlinedButton.icon(
                            icon: const Icon(Icons.lock_outline, size: 18),
                            label: const Text('Zusätzliche Hinweise (Premium)'),
                            onPressed: () {
                              _log.i(
                                "Premium-Hinweis-Button geklickt, navigiere zu /premium",
                              );
                              GoRouter.of(
                                context,
                              ).push(PremiumScreen.routeName);
                            },
                            style: OutlinedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 40),
                              foregroundColor: Theme.of(context).disabledColor,
                              side: BorderSide(
                                color: Theme.of(
                                  context,
                                ).disabledColor.withValues(alpha: 128),
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMedium,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    ),

                    // --- ENDE HINWEISE FELD ---
                    const SizedBox(height: AppTheme.spacingMedium),

                    // Generierungsbutton direkt hier platzieren
                    SizedBox(
                      width: double.infinity,
                      child:
                          _isExtracting && !_generationCompletedSuccessfully
                              ? _buildProcessingButton()
                              : _buildNormalButton(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Button für den Verarbeitungszustand
  Widget _buildProcessingButton() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Angepasste App-Ladeanimation
          AppLoadingAnimation(
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24.0,
          ),
          const SizedBox(width: 12),
          // Text
          Text(
            _isLoadingAd ? 'Lade Werbung...' : 'Generiere...',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Normaler Button
  Widget _buildNormalButton() {
    return ElevatedButton.icon(
      icon:
          _generationCompletedSuccessfully
              ? const Icon(Icons.check_circle_outline, size: 20)
              : (_showExternalLinkInfo
                  ? const Icon(Icons.auto_awesome, size: 20)
                  : (_captchaDetected && !_captchaSolved
                      ? const Icon(Icons.security, size: 20)
                      : const Icon(Icons.auto_awesome, size: 20))),
      label: Text(
        _generationCompletedSuccessfully
            ? 'Anschreiben generiert ✓'
            : (_showExternalLinkInfo
                ? 'KI-Anschreiben generieren'
                : (_captchaDetected && !_captchaSolved
                    ? 'Bitte CAPTCHA lösen'
                    : 'KI-Anschreiben generieren')),
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 14),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        backgroundColor:
            _generationCompletedSuccessfully
                ? Colors.green.shade600
                : (_captchaDetected && !_captchaSolved)
                ? Colors.orange.shade600
                : Theme.of(context).colorScheme.primary,
      ),
      onPressed:
          _generationCompletedSuccessfully
              ? null
              : () async {
                if (_showExternalLinkInfo) {
                  // Prüfe, ob wir auf einer Seite mit "Externe Seite öffnen"-Button sind
                  final result = await _controller.runJavaScriptReturningResult(
                    '''
                    (function() {
                      // Nur ein Keyword: "externe seite öffnen"
                      const keyword = 'externe seite öffnen';

                      // Suche in mehr Element-Typen (Buttons, Links, und Elemente mit Klick-Handlern oder Link-Rollen)
                      const potentialLinks = Array.from(document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]'));

                      // Prüfe auf externe Links mit exakter Übereinstimmung
                      for (let i = 0; i < potentialLinks.length; i++) {
                        // Versuche, den sichtbaren Text zu bekommen (innerText ist oft besser als textContent)
                        const text = (potentialLinks[i].innerText || potentialLinks[i].textContent || '').toLowerCase().trim();
                        if (!text) continue; // Überspringe Elemente ohne Text

                        // Nur exakte Übereinstimmung mit "externe seite öffnen"
                        if (text === keyword) {
                          return true; // Button gefunden
                        }
                      }
                      return false; // Kein Button gefunden
                    })();
                  ''',
                  );

                  // Konvertiere das Ergebnis in einen booleschen Wert
                  final bool externalButtonFound = result.toString() == 'true';

                  if (externalButtonFound && mounted) {
                    // Zeige eine spezifische Warnung für "Externe Seite öffnen"-Button
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text(
                          'Bitte klicken Sie zuerst auf den "Externe Seite öffnen"-Button, um die vollständige Jobbeschreibung zu sehen.',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        duration: const Duration(seconds: 5),
                        backgroundColor: Colors.orange.shade700,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.only(
                          bottom: 80,
                          left: 20,
                          right: 20,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        action: SnackBarAction(
                          label: 'Suchen',
                          textColor: Colors.white,
                          onPressed: () {
                            // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                            _controller.runJavaScript('''
                              (function() {
                                // Strikt nur "externe seite öffnen"
                                const keywords = ['externe seite öffnen'];

                                // Füge Animationsstil hinzu
                                const animationStyle = document.createElement('style');
                                animationStyle.textContent = `
                                  @keyframes pulseButton {
                                    0% { transform: scale(1); }
                                    50% { transform: scale(1.05); }
                                    100% { transform: scale(1); }
                                  }

                                  @keyframes glowBorder {
                                    0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                    50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
                                    100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                                  }

                                  .external-link-highlight {
                                    background-color: #FFEB3B important;
                                    border: 3px solid #FFC107 important;
                                    padding: 8px 16px !important;
                                    border-radius: 4px !important;
                                    box-shadow: 0 0 15px rgba(,255, 193, 7, 0.8) important;
                                    position: relative !important;
                                    animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                                    transition: all 0.3s ease !important;
                                    z-index: 9999 important;
                                    font-weight: bold !important;
                                  }

                                  .external-link-highlight:befre {
                                    content: "👉 " important;
                                  }

                                  .external-link-highlight:after {
                                    content: " 👈" important;
                                  }

                                  .external-link-hghlight:hover {
                                    transform: scale(1.1) !important;
                                    background-color: #FFC107 important;
                                  }
                                `;
                                document.head.appenChild(animationStyle);

                                // Suche nach Elementen mit diesen Keywords
                                const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                                let foundElements = [];

                                // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                                for (let i = 0; i < elements.length; i++) {
                                  const text = (elements[i].inerText || elements[i].textContent || '').toLowerCase().trim();

                                  // Exakte Übereinstimmung mit "externe seite öffnen"
                                  if (text === 'externe seite öffnen') {
                                    foundElements.push({
                                      element: elements[i],
                                      keyword: 'externe seite öffnen'
                                    });
                                  }
                                }

                                if (foundElements.length > 0) {
                                  // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                                  foundElements.forEach((item, index) => {
                                    // Wende die Klasse auf das Element an
                                    item.element.classList.add('external-link-highlight');

                                    // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                                    if (!item.element.title) {
                                      item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                                    }

                                    // Scrolle nur zum ersten Element
                                    if (index === 0) {
                                      item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                                    }
                                  });
                                }
                              })();
                            ''');
                          },
                        ),
                      ),
                    );

                    // Führe JavaScript aus, um nach externen Links zu suchen und zu scrollen
                    _controller.runJavaScript('''
                      (function() {
                        // Strikt nur "externe seite öffnen"
                        const keywords = ['externe seite öffnen'];

                        // Füge Animationsstil hinzu
                        const animationStyle = document.createElement('style');
                        animationStyle.textContent = `
                          @keyframes pulseButton {
                            0% { transform: scale(1); }
                            50% { transform: scale(1.05); }
                            100% { transform: scale(1); }
                          }

                          @keyframes glowBorder {
                            0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
                            100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.8); }
                          }

                          .external-link-highlight {
                            background-color: #FFEB3B important;
                            border: 3px solid #FFC107 important;
                            padding: 8px 16px !important;
                            border-radius: 4px !important;
                            box-shadow: 0 0 15px rgba(,255, 193, 7, 0.8) important;
                            position: relative !important;
                            animation: pulseButton 2s infinite, glowBorder 1.5s infinite !important;
                            transition: all 0.3s ease !important;
                            z-index: 9999 important;
                            font-weight: bold !important;
                          }

                          .external-link-highlight:befre {
                            content: "👉 " important;
                          }

                          .external-link-highlight:after {
                            content: " 👈" important;
                          }

                          .external-link-hghlight:hover {
                            transform: scale(1.1) !important;
                            background-color: #FFC107 important;
                          }
                        `;
                        document.head.appenChild(animationStyle);

                        // Suche nach Elementen mit diesen Keywords
                        const elements = document.querySelectorAll('button, a, [role="link"], [role="button"], [onclick], .btn, .button, [class*="btn"], [class*="button"]');

                        let foundElements = [];

                        // Sammle nur exakte Übereinstimmungen mit "externe seite öffnen"
                        for (let i = 0; i < elements.length; i++) {
                          const text = (elements[i].inerText || elements[i].textContent || '').toLowerCase().trim();

                          // Exakte Übereinstimmung mit "externe seite öffnen"
                          if (text === 'externe seite öffnen') {
                            foundElements.push({
                              element: elements[i],
                              keyword: 'externe seite öffnen'
                            });
                          }
                        }

                        if (foundElements.length > 0) {
                          // Hervorhebe alle gefundenen Elemente, aber scrolle nur zum ersten
                          foundElements.forEach((item, index) => {
                            // Wende die Klasse auf das Element an
                            item.element.classList.add('external-link-highlight');

                            // Füge einen Tooltip hinzu, falls noch nicht vorhanden
                            if (!item.element.title) {
                              item.element.title = "Hier klicken, um zur vollständigen Jobbeschreibung zu gelangen";
                            }

                            // Scrolle nur zum ersten Element
                            if (index === 0) {
                              item.element.scrollIntoView({behavior: 'smooth', block: 'center'});
                            }
                          });
                        }
                      })();
                    ''');
                    return; // Beende die Methode hier
                  } else if (mounted) {
                    // Setze CAPTCHA-Status für externe Seiten
                    setState(() {
                      _captchaDetected = false;
                      _captchaSolved = true;
                    });
                    _triggerApplicationGeneration();
                    return; // Beende die Methode hier
                  }
                } else if (_captchaDetected && !_captchaSolved) {
                  // Zeige eine deutliche Warnung und scrolle zum CAPTCHA
                  if (mounted) {
                    // Prüfe, ob wir auf einer externen Seite sind
                    if (_showExternalLinkInfo) {
                      // Auf externen Seiten zeigen wir eine andere Meldung an
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                            'Bitte öffnen Sie zuerst die externe Seite, um die vollständige Jobbeschreibung zu sehen.',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          duration: const Duration(seconds: 5),
                          backgroundColor: Colors.orange.shade700,
                          behavior: SnackBarBehavior.floating,
                          margin: const EdgeInsets.only(
                            bottom: 80,
                            left: 20,
                            right: 20,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      );
                    } else {
                      // Auf normalen Seiten zeigen wir die CAPTCHA-Meldung an
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                            'Bitte lösen Sie zuerst das CAPTCHA, um die Kontaktdaten des Arbeitgebers freizuschalten.',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          duration: const Duration(seconds: 5),
                          backgroundColor: Colors.red.shade700,
                          behavior: SnackBarBehavior.floating,
                          margin: const EdgeInsets.only(
                            bottom: 80,
                            left: 20,
                            right: 20,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      );
                    }
                  }
                  // Nur wenn wir nicht auf einer externen Seite sind
                  if (!_showExternalLinkInfo) {
                    _scrollToCaptchaAndHighlight();

                    // Starte den Timer zur regelmäßigen Überprüfung, ob das CAPTCHA gelöst wurde
                    _startCaptchaCheckTimer();
                  } else {
                    _log.i(
                      "Externe Seite erkannt, überspringe CAPTCHA-Scroll im Button-Handler",
                    );
                    // Setze den CAPTCHA-Status explizit
                    setState(() {
                      _captchaDetected = false;
                      _captchaSolved = true;
                    });
                  }
                  return; // Beende die Methode hier
                } else {
                  // Doppelte Prüfung, um sicherzustellen, dass das CAPTCHA gelöst ist
                  if (_captchaDetected &&
                      !_captchaSolved &&
                      !_showExternalLinkInfo) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text(
                          'Bitte lösen Sie zuerst das CAPTCHA, um fortzufahren.',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        duration: const Duration(seconds: 5),
                        backgroundColor: Colors.red.shade700,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.only(
                          bottom: 80,
                          left: 20,
                          right: 20,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    );
                    // Nur wenn wir nicht auf einer externen Seite sind
                    if (!_showExternalLinkInfo) {
                      _scrollToCaptchaAndHighlight();

                      // Starte den Timer zur regelmäßigen Überprüfung, ob das CAPTCHA gelöst wurde
                      _startCaptchaCheckTimer();
                    } else {
                      _log.i(
                        "Externe Seite erkannt, überspringe CAPTCHA-Scroll im Button-Handler (doppelte Prüfung)",
                      );
                      // Setze den CAPTCHA-Status explizit
                      setState(() {
                        _captchaDetected = false;
                        _captchaSolved = true;
                      });
                    }
                    return;
                  }
                  _triggerApplicationGeneration();
                }
              },
    );
  }

  // Hilfsmethode _buildJobMetadataSection
  Widget _buildJobMetadataSection(BuildContext context) {
    if (widget.jobEntity == null) return const SizedBox.shrink();
    final arbeitszeit = widget.jobEntity!.metadata['arbeitszeit'];
    final befristung = widget.jobEntity!.metadata['befristung'];

    if ((arbeitszeit == null || arbeitszeit.isEmpty) &&
        (befristung == null || befristung.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (arbeitszeit != null && arbeitszeit.isNotEmpty)
            _buildInfoRow(
              context,
              Icons.access_time,
              'Arbeitszeit',
              arbeitszeit,
            ),
          if (befristung != null && befristung.isNotEmpty)
            _buildInfoRow(
              context,
              Icons.calendar_today,
              'Befristung',
              befristung,
            ),
        ],
      ),
    );
  }

  // Hilfsmethode _buildInfoRow
  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingXSmall),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Theme.of(context).colorScheme.outline),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodySmall),
          ),
        ],
      ),
    );
  }
  // Fehlende build-Methode hinzufügen
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.jobTitle),
        actions: [
          // Favoriten-Button
          Consumer(
            builder: (context, ref, child) {
              final favoritesNotifier = ref.watch(favoritesProvider.notifier);
              final isFavorite = favoritesNotifier.isFavorite(widget.jobRefnr);

              return IconButton(
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? Colors.red : null,
                ),
                onPressed: () {
                  if (isFavorite) {
                    favoritesNotifier.removeFavorite(widget.jobRefnr);
                  } else {
                    favoritesNotifier.addFavorite(
                      widget.jobRefnr,
                      widget.jobTitle,
                    );
                  }
                },
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                // WebView
                WebViewWidget(controller: _controller),

                // Ladeindikator
                if (_isLoadingPage)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),

          // Footer mit Bewerbungs-Button
          Container(
            key: _footerKey,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isExtracting ? null : () => _handleApplication(context, ref),
                    child: _isExtracting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          )
                        : const Text('Bewerbung generieren'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} // <--- Schließende Klammer für die Klasse _JobDetailScreenState

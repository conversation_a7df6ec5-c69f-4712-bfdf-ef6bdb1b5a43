import 'package:flutter/material.dart';
import 'package:ki_test/src/presentation/common/widgets/application_generation_animation.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Demo-Screen zum Testen der Bewerbungsgenerierungs-Animation
class ApplicationGenerationDemoScreen extends StatefulWidget {
  const ApplicationGenerationDemoScreen({super.key});

  @override
  State<ApplicationGenerationDemoScreen> createState() => _ApplicationGenerationDemoScreenState();
}

class _ApplicationGenerationDemoScreenState extends State<ApplicationGenerationDemoScreen> {
  bool _isGenerating = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bewerbungsgenerierung Demo'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Dieser Screen simuliert die Bewerbungsgenerierung, um die Animation zu testen.',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              
              // Generierungsbutton
              SizedBox(
                width: double.infinity,
                child: _isGenerating
                    ? _buildProcessingButton()
                    : _buildNormalButton(),
              ),
              
              const SizedBox(height: 32),
              
              // Erklärung der Animation
              const Text(
                'Die Animation verwendet:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildFeatureItem('Pulsierende Ringe mit Farbverlauf'),
              _buildFeatureItem('Rotierende Bögen in verschiedenen Farben'),
              _buildFeatureItem('Schimmernden Kern mit Glow-Effekt'),
              _buildFeatureItem('Langsame, flüssige Bewegungen'),
              _buildFeatureItem('Hohe Kontraste für bessere Sichtbarkeit'),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 20),
          const SizedBox(width: 8),
          Text(text, style: const TextStyle(fontSize: 16)),
        ],
      ),
    );
  }
  
  // Button für den Verarbeitungszustand
  Widget _buildProcessingButton() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animiertes Icon mit der neuen ApplicationGenerationAnimation
          ApplicationGenerationAnimation(
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24.0,
          ),
          const SizedBox(width: 12),
          // Text
          Text(
            'Generiere Bewerbung...',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Normaler Button
  Widget _buildNormalButton() {
    return ElevatedButton.icon(
      icon: const Icon(Icons.auto_awesome, size: 20),
      label: const Text('Bewerbung generieren'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 14),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      onPressed: () {
        setState(() {
          _isGenerating = true;
        });
        
        // Simuliere den Generierungsprozess
        Future.delayed(const Duration(seconds: 10), () {
          if (mounted) {
            setState(() {
              _isGenerating = false;
            });
            
            // Zeige eine Erfolgsmeldung an
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Bewerbung erfolgreich generiert!'),
                duration: Duration(seconds: 3),
              ),
            );
          }
        });
      },
    );
  }
}

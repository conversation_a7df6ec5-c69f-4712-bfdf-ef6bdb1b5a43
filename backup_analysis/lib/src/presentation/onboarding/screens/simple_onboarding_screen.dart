import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:ki_test/src/core/theme/app_theme.dart';

/// Reparierter Onboarding-Screen mit korrekter Navigation
class SimpleOnboardingScreen extends ConsumerStatefulWidget {
  final bool viewMode;

  const SimpleOnboardingScreen({super.key, this.viewMode = false});

  @override
  ConsumerState<SimpleOnboardingScreen> createState() =>
      _SimpleOnboardingScreenState();
}

class _SimpleOnboardingScreenState
    extends ConsumerState<SimpleOnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isNavigating = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Einfache Seiten ohne schwere Widgets
    final List<Widget> pages = [
      _buildWelcomePage(),
      _buildBenefitsPage(),
      _buildPricingPage(),
      _buildCompletionPage(),
    ];

    return Scaffold(
      backgroundColor: AppTheme.backgroundDarkColor,
      body: SafeArea(
        child: Column(
          children: [
            // Einfacher Header
            Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.auto_awesome,
                    color: AppTheme.primaryLightColor,
                    size: 28,
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Text(
                    'Bewerbung KI',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Hauptinhalt - optimierter PageView für bessere Performance
            Expanded(
              child: RepaintBoundary(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: pages.length,
                  physics: const BouncingScrollPhysics(), // Flüssigere Physik
                  onPageChanged: (index) {
                    if (mounted) {
                      setState(() {
                        _currentPage = index;
                      });
                    }
                  },
                  itemBuilder:
                      (context, index) => RepaintBoundary(child: pages[index]),
                ),
              ),
            ),

            // Einfacher Footer
            Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                children: [
                  // Optimierter Seitenindikator mit AnimatedContainer
                  RepaintBoundary(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(pages.length, (index) {
                        return AnimatedContainer(
                          duration: const Duration(
                            milliseconds: 150,
                          ), // Schnelle Animation
                          curve: Curves.easeOut,
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          width: _currentPage == index ? 16 : 8,
                          height: 8,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color:
                                _currentPage == index
                                    ? AppTheme.primaryLightColor
                                    : Colors.grey.withOpacity(0.3),
                          ),
                        );
                      }),
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // Optimierte Navigationsbuttons mit AnimatedSwitcher
                  RepaintBoundary(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Zurück-Button mit Animation
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 150),
                          child:
                              _currentPage > 0
                                  ? TextButton.icon(
                                    key: const ValueKey('back_button'),
                                    onPressed:
                                        _isNavigating ? null : _navigateBack,
                                    icon: const Icon(Icons.arrow_back),
                                    label: const Text('Zurück'),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.white.withOpacity(
                                        0.8,
                                      ),
                                    ),
                                  )
                                  : const SizedBox(
                                    key: ValueKey('back_spacer'),
                                    width: 100,
                                  ),
                        ),

                        // Weiter/Fertig-Button mit Animation
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 150),
                          child: ElevatedButton.icon(
                            key: ValueKey('forward_button_$_currentPage'),
                            onPressed: _isNavigating ? null : _navigateForward,
                            icon: Icon(
                              _currentPage == pages.length - 1
                                  ? Icons.check
                                  : Icons.arrow_forward,
                            ),
                            label: Text(
                              _currentPage == pages.length - 1
                                  ? (widget.viewMode ? 'Schließen' : 'Fertig')
                                  : 'Weiter',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryLightColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingLarge,
                                vertical: AppTheme.spacingMedium,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Einfache Navigation-Methoden
  void _navigateBack() {
    if (_isNavigating || _currentPage <= 0) return;

    setState(() {
      _isNavigating = true;
    });

    _pageController
        .previousPage(
          duration: const Duration(milliseconds: 200), // Schnellere Animation
          curve: Curves.easeOut, // Optimierte Curve
        )
        .then((_) {
          if (mounted) {
            setState(() {
              _isNavigating = false;
            });
          }
        });
  }

  void _navigateForward() {
    if (_isNavigating) return;

    setState(() {
      _isNavigating = true;
    });

    if (_currentPage < 3) {
      // 4 Seiten total (0-3)
      _pageController
          .nextPage(
            duration: const Duration(milliseconds: 200), // Schnellere Animation
            curve: Curves.easeOut, // Optimierte Curve
          )
          .then((_) {
            if (mounted) {
              setState(() {
                _isNavigating = false;
              });
            }
          });
    } else {
      // Onboarding abschließen und dann _isNavigating zurücksetzen
      _completeOnboarding()
          .then((_) {
            if (mounted) {
              setState(() {
                _isNavigating = false;
              });
            }
          })
          .catchError((error) {
            print('❌ DEBUG: Fehler in _completeOnboarding: $error');
            if (mounted) {
              setState(() {
                _isNavigating = false;
              });
            }
          });
    }
  }

  Future<void> _completeOnboarding() async {
    print('🚀 DEBUG: _completeOnboarding() gestartet');
    print('🚀 DEBUG: mounted = $mounted');
    print('🚀 DEBUG: viewMode = ${widget.viewMode}');

    if (!mounted) {
      print('❌ DEBUG: Widget nicht mounted - Abbruch');
      return;
    }

    if (widget.viewMode) {
      print('🔄 DEBUG: ViewMode - Navigation zur Hauptseite');
      // Sichere Navigation für View-Mode - IMMER zur Hauptseite
      if (mounted) {
        print('✅ DEBUG: Navigiere zu "/"');
        context.go('/');
        print('✅ DEBUG: Navigation zu "/" abgeschlossen');
      }
    } else {
      print('🔄 DEBUG: Normaler Modus - Navigation zum CV-Upload');
      // SOFORTIGE Navigation zum CV-Upload Screen - kein Warten!
      if (mounted) {
        print('✅ DEBUG: Navigiere zu "/onboarding/cv-upload"');
        try {
          context.go('/onboarding/cv-upload');
          print(
            '✅ DEBUG: Navigation zu "/onboarding/cv-upload" erfolgreich ausgeführt',
          );
        } catch (e) {
          print(
            '❌ DEBUG: Fehler bei Navigation zu "/onboarding/cv-upload": $e',
          );
        }
      } else {
        print('❌ DEBUG: Widget nicht mounted - Navigation abgebrochen');
      }

      print(
        '✅ DEBUG: Onboarding lokal abgeschlossen - keine Server-Kommunikation mehr',
      );
    }

    print('🏁 DEBUG: _completeOnboarding() beendet');
  }

  // Einfache Seiten-Widgets ohne schwere Komponenten
  Widget _buildWelcomePage() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.auto_awesome,
            size: 80,
            color: AppTheme.primaryLightColor,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Willkommen bei Bewerbung KI!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Dein KI-Assistent für erfolgreiche Bewerbungen',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingXLarge),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.4),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.borderDarkColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Mit nur 3 Klicks zur perfekten Bewerbung!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Lade deinen Lebenslauf hoch und lass die KI für dich arbeiten. Spare Zeit und erhöhe deine Erfolgschancen!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsPage() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.star, size: 80, color: AppTheme.primaryLightColor),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Deine Vorteile',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXLarge),
          ..._buildBenefitsList(),
        ],
      ),
    );
  }

  List<Widget> _buildBenefitsList() {
    final benefits = [
      {
        'icon': Icons.speed,
        'title': 'Schneller bewerben',
        'subtitle': 'KI erstellt Bewerbungen in Sekunden',
      },
      {
        'icon': Icons.trending_up,
        'title': 'Höhere Erfolgsquote',
        'subtitle': 'Personalisierte Anschreiben für jede Stelle',
      },
      {
        'icon': Icons.auto_awesome,
        'title': 'KI-Optimierung',
        'subtitle': 'Automatische Anpassung an Stellenausschreibungen',
      },
    ];

    return benefits
        .map(
          (benefit) => RepaintBoundary(
            child: Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                borderRadius: BorderRadius.circular(
                  AppTheme.borderRadiusMedium,
                ),
                border: Border.all(
                  color: AppTheme.borderDarkColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    benefit['icon'] as IconData,
                    color: AppTheme.primaryLightColor,
                    size: 32,
                  ),
                  const SizedBox(width: AppTheme.spacingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          benefit['title'] as String,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          benefit['subtitle'] as String,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
        .toList();
  }

  Widget _buildPricingPage() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.workspace_premium,
            size: 80,
            color: AppTheme.primaryLightColor,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Wähle deinen Plan',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXLarge),
          _buildPricingCard(
            'Free',
            'Kostenlos',
            '5 Bewerbungen pro Woche\nMit Werbung',
            isFree: true,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildPricingCard(
            'Pro',
            '19,99 €/Monat',
            '150 Bewerbungen pro Monat\nOhne Werbung',
            isPopular: true,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildPricingCard(
            'Unlimited',
            '39,99 €/Monat',
            'Unbegrenzte Bewerbungen\nOhne Werbung + Personalisierter Stil',
          ),
        ],
      ),
    );
  }

  Widget _buildPricingCard(
    String title,
    String price,
    String description, {
    bool isPopular = false,
    bool isFree = false,
  }) {
    Color borderColor;
    Color backgroundColor;

    if (isFree) {
      borderColor = Colors.blue;
      backgroundColor = Colors.blue.withOpacity(0.1);
    } else if (isPopular) {
      borderColor = Colors.purple;
      backgroundColor = Colors.purple.withOpacity(0.1);
    } else {
      borderColor = Colors.orange;
      backgroundColor = Colors.orange.withOpacity(0.1);
    }

    return RepaintBoundary(
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          border: Border.all(color: borderColor, width: 2),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (isFree || isPopular) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: isFree ? Colors.blue : Colors.purple,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            isFree ? 'Kostenlos' : 'Beliebt',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              price,
              style: TextStyle(
                color:
                    isFree
                        ? Colors.blue
                        : (isPopular ? Colors.purple : Colors.orange),
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompletionPage() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.check_circle, size: 100, color: Colors.green),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Alles bereit!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Du kannst jetzt mit der Jobsuche beginnen und deine ersten KI-generierten Bewerbungen erstellen.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
}

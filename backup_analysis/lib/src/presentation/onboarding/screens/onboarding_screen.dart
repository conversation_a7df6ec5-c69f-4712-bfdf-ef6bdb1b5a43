import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';
import 'package:ki_test/src/core/services/audio_service.dart';
import 'package:ki_test/src/application/providers/onboarding_provider.dart';

// Import der Widgets
import '../widgets/floating_elements_background.dart'; // Animierte fliegende Elemente
import '../widgets/resume_upload_widget.dart';
import '../widgets/benefits_widget.dart';
import '../widgets/free_applications_widget.dart';
import '../widgets/pricing_table_widget.dart';
import '../widgets/page_transition.dart';
import '../widgets/elegant_final_animation.dart'; // Elegante Animation für die letzte Seite

/// Ein überarbeiteter Onboarding-Screen mit verbesserten Animationen und Ablauf
class NewOnboardingScreen extends HookConsumerWidget {
  /// Gibt an, ob der Screen im Ansichtsmodus angezeigt wird (keine Umleitung)
  final bool viewMode;

  const NewOnboardingScreen({super.key, this.viewMode = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();
    final currentPage = useState(0);
    final audioService = ref.watch(audioServiceProvider);
    final showFinalAnimation = useState(false);
    final canNavigateFromLastPage = useState(false);

    // Verwende context.mounted für die Prüfung

    // Initialisiere die Hintergrundmusik für die erste Seite
    useEffect(() {
      // Initialisiere und spiele die Audiodatei für die erste Seite ab
      Future<void> setupAudio() async {
        try {
          // Initialisiere die Audiodatei für die erste Seite
          await audioService.initialize('dummy'); // Der Pfad wird ignoriert
          await audioService.play();
          debugPrint('Audiowiedergabe für Seite 0 gestartet');
        } catch (e) {
          debugPrint('Fehler beim Abspielen der Audio für Seite 0: $e');
        }
      }

      setupAudio();

      return () {
        // Stoppe die Musik, wenn der Screen verlassen wird
        audioService.stop();
        debugPrint('Audiowiedergabe für Onboarding gestoppt');
      };
    }, []);

    // Wechsle die Audiodatei, wenn die Seite wechselt
    useEffect(() {
      // Wechsle die Audiodatei, wenn die Seite wechselt
      Future<void> switchAudio() async {
        try {
          await audioService.switchToPage(currentPage.value);
          debugPrint(
            'Audiowiedergabe für Seite ${currentPage.value} gestartet',
          );

          // Für die letzte Seite die spezielle Animation anzeigen
          if (currentPage.value == 5) {
            // Nur wenn die Animation noch nicht angezeigt wird
            if (!showFinalAnimation.value) {
              showFinalAnimation.value = true;
              // Sofort die Navigation freigeben, ohne Wartezeit
              canNavigateFromLastPage.value = true;
            }
          } else {
            showFinalAnimation.value = false;
            canNavigateFromLastPage.value = true;
          }
        } catch (e) {
          debugPrint(
            'Fehler beim Wechseln der Audio für Seite ${currentPage.value}: $e',
          );
        }
      }

      switchAudio();

      return null;
    }, [currentPage.value]);

    // Listener für den PageController
    useEffect(() {
      void listener() {
        if (pageController.page?.round() != currentPage.value) {
          currentPage.value = pageController.page!.round();
        }
      }

      pageController.addListener(listener);
      return () => pageController.removeListener(listener);
    }, [pageController]);

    // Die Seiten für den PageView
    final List<Widget> onboardingPages = [
      _buildWelcomePage(context),
      _buildResumeUploadPage(context, ref),
      _buildBenefitsPage(context),
      _buildFreeApplicationsPage(context, () {
        // Gehe zur nächsten Seite, wenn der Benutzer auf "Das ist super!" klickt
        pageController.nextPage(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }),
      _buildPricingPage(context),
      _buildCompletionPage(context),
    ];

    return Scaffold(
      body: FloatingElementsBackground(
        pageController: pageController, // PageController für Seitenübergänge
        child: SafeArea(
          child: Column(
            children: [
              // Header mit Logo oder Titel
              Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      color: AppTheme.primaryLightColor,
                      size: 28,
                    ),
                    const SizedBox(width: AppTheme.spacingSmall),
                    Text(
                      'Bewerbung KI',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Hauptinhalt
              Expanded(
                child: Stack(
                  children: [
                    // Normaler PageView für die Onboarding-Seiten
                    if (!showFinalAnimation.value)
                      SmoothPageView(
                        controller: pageController,
                        pages: onboardingPages,
                        onPageChanged: (index) {
                          currentPage.value = index;

                          // Für die letzte Seite die spezielle Animation anzeigen
                          if (index == 5) {
                            showFinalAnimation.value = true;
                            // Sofort die Navigation freigeben, ohne Wartezeit
                            canNavigateFromLastPage.value = true;

                            // Starte die Audiowiedergabe für die letzte Seite
                            audioService.switchToPage(index);
                          }
                        },
                      ),

                    // Elegante Animation für die letzte Seite
                    if (showFinalAnimation.value)
                      ElegantFinalAnimation(
                        onAnimationComplete: () {
                          canNavigateFromLastPage.value = true;
                        },
                        durationInSeconds: 5, // Kürzere Dauer: 5 Sekunden
                      ),
                  ],
                ),
              ),

              // Navigation
              Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  children: [
                    // Fortschrittsanzeige
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(onboardingPages.length, (index) {
                        return AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          width: currentPage.value == index ? 16 : 8,
                          height: 8,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusCircular,
                            ),
                            color:
                                currentPage.value == index
                                    ? AppTheme.primaryLightColor
                                    : Colors.grey.withAlpha(
                                      77,
                                    ), // ~30% Opazität
                          ),
                        );
                      }),
                    ),

                    const SizedBox(height: AppTheme.spacingMedium),

                    // Navigationsbuttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Zurück-Button (immer aktiv, auch auf der letzten Seite)
                        if (currentPage.value > 0)
                          TextButton.icon(
                            onPressed: () {
                              // Wenn wir auf der letzten Seite mit Animation sind, gehe zur normalen letzten Seite zurück
                              if (showFinalAnimation.value) {
                                showFinalAnimation.value = false;
                                canNavigateFromLastPage.value = true;
                              } else {
                                // Normale Navigation zurück
                                pageController.previousPage(
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            icon: const Icon(Icons.arrow_back),
                            label: const Text('Zurück'),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white.withAlpha(
                                204,
                              ), // 0.8 * 255 = 204
                            ),
                          )
                        else
                          const SizedBox(width: 100),

                        // Weiter/Fertig-Button
                        ElevatedButton.icon(
                          onPressed: () async {
                            if (currentPage.value <
                                onboardingPages.length - 1) {
                              pageController.nextPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOut,
                              );
                            } else {
                              // Starte die Exit-Animation, wenn wir auf der letzten Seite sind
                              if (showFinalAnimation.value) {
                                // Suche nach der Animation im Widget-Baum
                                final animationState =
                                    _findElegantFinalAnimationState(context);
                                if (animationState != null) {
                                  // Starte die Exit-Animation
                                  animationState.startExitAnimation();

                                  // Warte kurz, damit die Animation starten kann
                                  await Future.delayed(
                                    const Duration(milliseconds: 800),
                                  );
                                }
                              }

                              if (viewMode) {
                                // Im Ansichtsmodus nur zurück zur vorherigen Seite navigieren
                                if (context.mounted) {
                                  context.pop();
                                }
                              } else {
                                // Onboarding abschließen
                                await ref
                                    .read(onboardingProvider.notifier)
                                    .completeOnboarding();

                                if (context.mounted) {
                                  // Navigiere zur Hauptseite
                                  context.go('/');
                                }
                              }
                            }
                          },
                          icon: Icon(
                            currentPage.value == onboardingPages.length - 1
                                ? Icons.check
                                : Icons.arrow_forward,
                          ),
                          label: Text(
                            currentPage.value == onboardingPages.length - 1
                                ? (viewMode ? 'Schließen' : 'Fertig')
                                : 'Weiter',
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryLightColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingLarge,
                              vertical: AppTheme.spacingMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // --- Widgets für die einzelnen Seiten ---

  Widget _buildWelcomePage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.auto_awesome,
            size: 80,
            color: AppTheme.primaryLightColor,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Willkommen bei Bewerbung KI!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Dein KI-Assistent für erfolgreiche Bewerbungen',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingXLarge),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.borderDarkColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Mit nur 3 Klicks zur perfekten Bewerbung!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Lade deinen Lebenslauf hoch und lass die KI für dich arbeiten. Spare Zeit und erhöhe deine Erfolgschancen!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResumeUploadPage(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Lade deinen Lebenslauf hoch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Der erste Schritt zu deiner erfolgreichen Bewerbung',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          const ResumeUploadWidget(),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Keine Sorge, du kannst auch später noch deinen Lebenslauf hochladen oder manuell deine Daten eingeben.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsPage(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Deine Vorteile',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Warum Bewerbung KI deine Jobsuche revolutioniert',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          const BenefitsWidget(),
        ],
      ),
    );
  }

  Widget _buildFreeApplicationsPage(
    BuildContext context,
    VoidCallback onAccept,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Unser Geschenk an dich',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Starte sofort mit deiner Jobsuche',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          FreeApplicationsWidget(onAccept: onAccept),
        ],
      ),
    );
  }

  Widget _buildPricingPage(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Unsere Preispläne',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Wähle den Plan, der zu dir passt',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          const PricingTableWidget(),
          const SizedBox(height: AppTheme.spacingLarge),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              border: Border.all(
                color: AppTheme.primaryLightColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              'Du kannst jederzeit upgraden oder mit deinen 10 kostenlosen Bewerbungen starten.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionPage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle,
            size: 80,
            color: AppTheme.successColor,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Alles bereit!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Du kannst jetzt mit der Jobsuche beginnen und deine ersten Bewerbungen erstellen.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
            ),
          ),
          const SizedBox(height: AppTheme.spacingXLarge),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.successColor.withAlpha(77), // 0.3 * 255 = 77
                width: 1,
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.emoji_emotions,
                  color: AppTheme.successColor,
                  size: 40,
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'Viel Erfolg bei deiner Jobsuche!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Findet die ElegantFinalAnimation im Widget-Baum
  ElegantFinalAnimationState? _findElegantFinalAnimationState(
    BuildContext context,
  ) {
    ElegantFinalAnimationState? result;

    // Durchsuche den Widget-Baum nach der ElegantFinalAnimation
    void visitor(Element element) {
      if (element.widget is ElegantFinalAnimation) {
        // Wenn wir die Animation gefunden haben, speichere den State
        final statefulElement = element as StatefulElement;
        final state = statefulElement.state;
        if (state is ElegantFinalAnimationState) {
          result = state;
        }
      }

      // Wenn wir noch keinen State gefunden haben, besuche alle Kinder
      if (result == null) {
        element.visitChildren(visitor);
      }
    }

    // Starte die Suche
    context.visitChildElements(visitor);

    return result;
  }
}

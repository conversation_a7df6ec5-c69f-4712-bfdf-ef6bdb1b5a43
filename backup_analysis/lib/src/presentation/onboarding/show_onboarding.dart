import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Eine Hilfsfunktion, um den Onboarding-Screen anzuzeigen, ohne den Status zurückzusetzen
/// Wird nur für Testzwecke verwendet
void showOnboardingScreen(BuildContext context) {
  // Zeige eine Snackbar an
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('Neuer Onboarding-Screen wird angezeigt...'),
      duration: Duration(seconds: 1),
    ),
  );

  // Navigiere zum Onboarding-Screen im Ansichtsmodus mit GoRouter
  context.push('/view-onboarding');
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart'; // Importiere GoRouter
import '../../application/providers/onboarding_provider.dart';

/// Eine Hilfsfunktion, um das Onboarding zurückzusetzen und neu zu starten
/// Wird nur für Testzwecke verwendet
Future<void> resetAndStartOnboarding(
  BuildContext context,
  WidgetRef ref,
) async {
  // Zeige eine Snackbar an, anstatt einen Dialog
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text('Onboarding wird zurückgesetzt...'),
      duration: Duration(seconds: 1),
    ),
  );

  try {
    // Setze den Onboarding-Status zurück
    await ref.read(onboardingProvider.notifier).resetOnboarding();

    // Navigiere zum Onboarding-Screen mit GoRouter
    if (context.mounted) {
      context.go('/onboarding');
    }
  } catch (e) {
    // Bei einem Fehler eine Fehlermeldung anzeigen
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fehler beim Zurücksetzen des Onboardings: $e')),
      );
    }
  }
}

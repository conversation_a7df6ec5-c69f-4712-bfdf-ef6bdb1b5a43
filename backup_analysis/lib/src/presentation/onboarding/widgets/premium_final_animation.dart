import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein hochwertiges Widget für die letzte Seite des Onboarding-Screens
/// Mit eleganten Animationen und Premium-Design
class PremiumFinalAnimation extends StatefulWidget {
  final VoidCallback onAnimationComplete;
  final int durationInSeconds;

  const PremiumFinalAnimation({
    super.key,
    required this.onAnimationComplete,
    this.durationInSeconds = 5, // Kürzere Dauer: 5 Sekunden
  });

  @override
  State<PremiumFinalAnimation> createState() => _PremiumFinalAnimationState();
}

class _PremiumFinalAnimationState extends State<PremiumFinalAnimation>
    with TickerProviderStateMixin {
  // Animation Controller
  late final AnimationController _backgroundController;
  late final AnimationController _circleController;
  late final AnimationController _checkController;
  late final AnimationController _textController;
  late final AnimationController _particlesController;

  // Animationen
  late final Animation<double> _circleScaleAnimation;
  late final Animation<double> _checkAnimation;
  late final Animation<double> _textOpacityAnimation;
  late final Animation<double> _textSlideAnimation;
  late final Animation<double> _particlesAnimation;

  // Timer für die Gesamtdauer
  Timer? _completionTimer;

  // Zufallsgenerator für Partikel
  final math.Random _random = math.Random();
  final List<Particle> _particles = [];

  @override
  void initState() {
    super.initState();

    // Hintergrund-Animation
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    // Kreis-Animation
    _circleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Häkchen-Animation
    _checkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Text-Animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Partikel-Animation
    _particlesController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );

    // Kreis-Skalierungs-Animation
    _circleScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _circleController, curve: Curves.elasticOut),
    );

    // Häkchen-Animation
    _checkAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _checkController, curve: Curves.easeOutBack),
    );

    // Text-Opazitäts-Animation
    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    // Text-Slide-Animation
    _textSlideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOutQuint),
    );

    // Partikel-Animation
    _particlesAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particlesController, curve: Curves.linear),
    );

    // Generiere Partikel
    _generateParticles();

    // Starte die Animationen
    _startAnimations();

    // Timer für den Abschluss der Animation
    _completionTimer = Timer(Duration(seconds: widget.durationInSeconds), () {
      if (mounted) {
        widget.onAnimationComplete();
      }
    });
  }

  void _generateParticles() {
    for (int i = 0; i < 30; i++) {
      _particles.add(
        Particle(
          position: Offset(
            _random.nextDouble() * 400 - 200,
            _random.nextDouble() * 400 - 200,
          ),
          size: _random.nextDouble() * 8 + 2,
          color: _getRandomColor(),
          speed: _random.nextDouble() * 0.5 + 0.2,
          angle: _random.nextDouble() * 2 * math.pi,
          opacity: _random.nextDouble() * 0.6 + 0.2,
        ),
      );
    }
  }

  Color _getRandomColor() {
    final colors = [
      AppTheme.primaryColor,
      AppTheme.primaryLightColor,
      Colors.white,
      Colors.purple.shade300,
      Colors.deepPurple.shade300,
    ];
    return colors[_random.nextInt(colors.length)];
  }

  void _startAnimations() {
    // Starte die Hintergrund-Animation
    _backgroundController.repeat(reverse: true);

    // Starte die Partikel-Animation
    _particlesController.repeat();

    // Starte die Kreis-Animation nach einer kurzen Verzögerung
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _circleController.forward();
      }
    });

    // Starte die Häkchen-Animation nach der Kreis-Animation
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        _checkController.forward();
      }
    });

    // Starte die Text-Animation nach der Häkchen-Animation
    Future.delayed(const Duration(milliseconds: 1200), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _circleController.dispose();
    _checkController.dispose();
    _textController.dispose();
    _particlesController.dispose();
    _completionTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Stack(
      fit: StackFit.expand,
      children: [
        // Hintergrund mit Farbverlauf
        AnimatedBuilder(
          animation: _backgroundController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF2A1B4D), // Dunkles Violett
                    const Color(0xFF4A1B8C), // Mittleres Violett
                    const Color(0xFF6A1B9D), // Helles Violett
                  ],
                  stops: [
                    0.0,
                    0.5 + (_backgroundController.value * 0.1),
                    1.0,
                  ],
                ),
              ),
            );
          },
        ),

        // Animierte Partikel
        AnimatedBuilder(
          animation: _particlesController,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlesPainter(
                particles: _particles,
                animation: _particlesAnimation.value,
              ),
              size: Size.infinite,
            );
          },
        ),

        // Animierter Kreis in der Mitte
        Center(
          child: AnimatedBuilder(
            animation: _circleController,
            builder: (context, child) {
              return Transform.scale(
                scale: _circleScaleAnimation.value,
                child: Container(
                  width: size.width * 0.5,
                  height: size.width * 0.5,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.white.withAlpha(230),
                        AppTheme.primaryLightColor.withAlpha(180),
                        AppTheme.primaryColor.withAlpha(100),
                      ],
                      stops: const [0.3, 0.7, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryLightColor.withAlpha(100),
                        blurRadius: 30,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  // Animiertes Häkchen
                  child: AnimatedBuilder(
                    animation: _checkController,
                    builder: (context, child) {
                      return CustomPaint(
                        painter: CheckmarkPainter(
                          progress: _checkAnimation.value,
                          color: Colors.white,
                          strokeWidth: 8.0,
                        ),
                        size: Size.infinite,
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),

        // Text mit Animation
        Positioned(
          bottom: size.height * 0.2,
          left: 20,
          right: 20,
          child: AnimatedBuilder(
            animation: _textController,
            builder: (context, child) {
              return Opacity(
                opacity: _textOpacityAnimation.value,
                child: Transform.translate(
                  offset: Offset(0, _textSlideAnimation.value),
                  child: Column(
                    children: [
                      Text(
                        'Alles bereit!',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(100),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(40),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withAlpha(30),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(30),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Text(
                          'Du kannst jetzt mit der Jobsuche beginnen und deine ersten Bewerbungen erstellen.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            height: 1.4,
                            letterSpacing: 0.2,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Maler für das animierte Häkchen
class CheckmarkPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  CheckmarkPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final path = Path();
    
    // Berechne die Punkte für das Häkchen
    final center = Offset(size.width / 2, size.height / 2);
    final leftPoint = Offset(center.dx - size.width * 0.2, center.dy);
    final bottomPoint = Offset(center.dx - size.width * 0.05, center.dy + size.height * 0.15);
    final rightPoint = Offset(center.dx + size.width * 0.2, center.dy - size.height * 0.2);

    // Zeichne den ersten Teil des Häkchens (links nach unten)
    if (progress <= 0.5) {
      final adjustedProgress = progress * 2;
      path.moveTo(leftPoint.dx, leftPoint.dy);
      path.lineTo(
        leftPoint.dx + (bottomPoint.dx - leftPoint.dx) * adjustedProgress,
        leftPoint.dy + (bottomPoint.dy - leftPoint.dy) * adjustedProgress,
      );
    } else {
      // Zeichne den kompletten ersten Teil
      path.moveTo(leftPoint.dx, leftPoint.dy);
      path.lineTo(bottomPoint.dx, bottomPoint.dy);
      
      // Zeichne den zweiten Teil des Häkchens (unten nach rechts oben)
      final adjustedProgress = (progress - 0.5) * 2;
      path.lineTo(
        bottomPoint.dx + (rightPoint.dx - bottomPoint.dx) * adjustedProgress,
        bottomPoint.dy + (rightPoint.dy - bottomPoint.dy) * adjustedProgress,
      );
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Klasse für einen Partikel
class Particle {
  Offset position;
  final double size;
  final Color color;
  final double speed;
  final double angle;
  final double opacity;

  Particle({
    required this.position,
    required this.size,
    required this.color,
    required this.speed,
    required this.angle,
    required this.opacity,
  });
}

/// Maler für die animierten Partikel
class ParticlesPainter extends CustomPainter {
  final List<Particle> particles;
  final double animation;

  ParticlesPainter({
    required this.particles,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    for (final particle in particles) {
      // Bewege den Partikel basierend auf der Animation
      final dx = math.cos(particle.angle) * particle.speed * animation * 100;
      final dy = math.sin(particle.angle) * particle.speed * animation * 100;
      
      final currentPosition = center + particle.position + Offset(dx, dy);
      
      // Zeichne den Partikel
      final paint = Paint()
        ..color = particle.color.withAlpha((particle.opacity * 255).toInt())
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        currentPosition,
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

import 'package:flutter/material.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Widget zur Anzeige der kostenlosen Bewerbungen im Onboarding
class FreeApplicationsWidget extends StatelessWidget {
  final VoidCallback? onAccept;

  const FreeApplicationsWidget({super.key, this.onAccept});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.7),
            AppTheme.primaryDarkColor.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        children: [
          // Icon und Titel
          const Icon(Icons.card_giftcard, color: Colors.white, size: 48),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Dein Geschenk!',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),

          // Hauptinhalt
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
            child: Column(
              children: [
                Text(
                  '10 kostenlose Bewerbungen',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  'Starte sofort mit deiner Jobsuche!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacingLarge),

          // Beschreibung
          Text(
            'Wir schenken dir 10 kostenlose Bewerbungen, damit du die Qualität unserer KI-generierten Anschreiben selbst erleben kannst.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),

          const SizedBox(height: AppTheme.spacingLarge),

          // Button
          ElevatedButton(
            onPressed: onAccept,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingXLarge,
                vertical: AppTheme.spacingMedium,
              ),
              minimumSize: const Size(250, 50),
              textStyle: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            child: const Text('Das ist super!'),
          ),
        ],
      ),
    );
  }
}

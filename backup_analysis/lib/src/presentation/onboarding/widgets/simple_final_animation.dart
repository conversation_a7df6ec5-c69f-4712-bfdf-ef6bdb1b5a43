import 'package:flutter/material.dart';
import 'dart:async';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein vereinfachtes Widget für die letzte Seite des Onboarding-Screens
/// Elegantes, minimalistisches Design mit guter Lesbarkeit
class SimpleFinalAnimation extends StatefulWidget {
  final VoidCallback onAnimationComplete;
  final int durationInSeconds;

  const SimpleFinalAnimation({
    super.key,
    required this.onAnimationComplete,
    this.durationInSeconds = 5, // Kürzere Dauer: 5 Sekunden
  });

  @override
  State<SimpleFinalAnimation> createState() => _SimpleFinalAnimationState();
}

class _SimpleFinalAnimationState extends State<SimpleFinalAnimation>
    with TickerProviderStateMixin {
  // Animation Controller
  late final AnimationController _backgroundController;
  late final AnimationController _circleController;
  late final AnimationController _textController;

  // Animationen
  late final Animation<double> _circleScaleAnimation;
  late final Animation<double> _textOpacityAnimation;

  // Timer für die Gesamtdauer
  Timer? _completionTimer;

  @override
  void initState() {
    super.initState();

    // Hintergrund-Animation
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    // Kreis-Animation
    _circleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Text-Animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Kreis-Skalierungs-Animation
    _circleScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _circleController, curve: Curves.easeOutBack),
    );

    // Text-Opazitäts-Animation
    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    // Starte die Animationen
    _startAnimations();

    // Timer für den Abschluss der Animation
    _completionTimer = Timer(Duration(seconds: widget.durationInSeconds), () {
      if (mounted) {
        widget.onAnimationComplete();
      }
    });
  }

  void _startAnimations() {
    // Starte die Hintergrund-Animation
    _backgroundController.repeat(reverse: true);

    // Starte die Kreis-Animation nach einer kurzen Verzögerung
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _circleController.forward();
      }
    });

    // Starte die Text-Animation nach der Kreis-Animation
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _circleController.dispose();
    _textController.dispose();
    _completionTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Stack(
      fit: StackFit.expand,
      children: [
        // Hintergrund mit Farbverlauf
        AnimatedBuilder(
          animation: _backgroundController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.primaryColor.withAlpha(200),
                    AppTheme.primaryLightColor,
                  ],
                  stops: [
                    0.0,
                    1.0 - (_backgroundController.value * 0.2),
                  ],
                ),
              ),
            );
          },
        ),

        // Animierter Kreis in der Mitte
        Center(
          child: AnimatedBuilder(
            animation: _circleController,
            builder: (context, child) {
              return Transform.scale(
                scale: _circleScaleAnimation.value,
                child: Container(
                  width: size.width * 0.6,
                  height: size.width * 0.6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.white.withAlpha(200),
                        AppTheme.primaryLightColor.withAlpha(100),
                      ],
                      stops: const [0.3, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryLightColor.withAlpha(100),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.check_circle_outline,
                      size: size.width * 0.25,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Text mit Animation
        Positioned(
          bottom: size.height * 0.2,
          left: 20,
          right: 20,
          child: AnimatedBuilder(
            animation: _textController,
            builder: (context, child) {
              return Opacity(
                opacity: _textOpacityAnimation.value,
                child: Column(
                  children: [
                    Text(
                      'Alles bereit!',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(40),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        'Du kannst jetzt mit der Jobsuche beginnen und deine ersten Bewerbungen erstellen.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

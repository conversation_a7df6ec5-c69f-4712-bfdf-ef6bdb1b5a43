import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:ki_test/src/core/theme/app_theme.dart';

/// Ein Widget, das einen animierten Hintergrund mit Lottie-Animationen für den Onboarding-Screen darstellt
class LottieBackground extends StatefulWidget {
  final Widget child;

  const LottieBackground({
    super.key,
    required this.child,
  });

  @override
  State<LottieBackground> createState() => _LottieBackgroundState();
}

class _LottieBackgroundState extends State<LottieBackground> with TickerProviderStateMixin {
  late final AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10), // Langsame Animation
    );
    
    // Endlosschleife für die Animation
    _controller.repeat(reverse: false);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Hintergrundfarbe
        Container(
          color: AppTheme.backgroundDarkColor,
        ),
        
        // Lottie-Animation im Hintergrund
        // Hinweis: Die Lottie-Datei muss noch hinzugefügt werden
        _buildLottieAnimation(),
        
        // Inhalt
        widget.child,
      ],
    );
  }
  
  Widget _buildLottieAnimation() {
    return FutureBuilder<bool>(
      // Prüfen, ob die Lottie-Datei existiert
      future: _checkLottieFileExists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }
        
        final bool fileExists = snapshot.data ?? false;
        
        if (fileExists) {
          return Opacity(
            opacity: 0.6, // Reduzierte Opazität für subtilen Effekt
            child: Lottie.asset(
              'assets/animations/onboarding_background.json',
              controller: _controller,
              fit: BoxFit.cover,
            ),
          );
        } else {
          // Fallback: Einfacher Farbverlauf, wenn keine Lottie-Datei vorhanden ist
          return Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.5,
                colors: [
                  AppTheme.primaryColor.withAlpha(40),
                  AppTheme.backgroundDarkColor,
                ],
                stops: const [0.0, 1.0],
              ),
            ),
          );
        }
      },
    );
  }
  
  Future<bool> _checkLottieFileExists() async {
    try {
      // Versuche, die Lottie-Datei zu laden
      await DefaultAssetBundle.of(context).load('assets/animations/onboarding_background.json');
      return true;
    } catch (e) {
      debugPrint('Lottie-Datei nicht gefunden: $e');
      return false;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
// import '../../../../services/deepseek_service.dart'; // Import für Deepseek (später einkommentieren)
// Import für Riverpod (angenommen)
import 'package:ki_test/src/application/services/cloud_function_service.dart'; // Annahme: Service für Cloud Functions
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Hinzugefügt

class Chatbox extends HookConsumerWidget { // Geändert zu HookConsumerWidget für Riverpod
  const Chatbox({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) { // WidgetRef hinzugefügt
    final messageController = useTextEditingController();
    final isSending = useState(false);
    final chatResponse = useState(''); // State für die Chat-Antwort

    Future<void> sendMessage() async {
      if (messageController.text.isEmpty || isSending.value) return;
      final message = messageController.text;
      messageController.clear();
      isSending.value = true;
      chatResponse.value = 'Sende...'; // Setze Ladezustand

      try {
        final cloudFunctionService = ref.read(cloudFunctionServiceProvider); // Provider ist jetzt bekannt
        final result = await cloudFunctionService.callFunction(
          'deepseekChat', 
          {'prompt': message}
        );
        
        if (result['result'] is String) {
          chatResponse.value = result['result']; // Setze die Antwort von Deepseek
        } else {
          throw Exception("Unerwartetes Antwortformat von deepseekChat");
        }

      } catch (e) {
        print('Fehler beim Senden der Nachricht an deepseekChat: $e');
        chatResponse.value = 'Fehler: ${e.toString()}'; // Setze Fehlermeldung
      } finally {
        isSending.value = false;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: messageController,
            decoration: InputDecoration(
              hintText: 'Stelle eine Frage an die KI...',
              suffixIcon: IconButton(
                icon: isSending.value
                    ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                    : const Icon(Icons.send),
                onPressed: isSending.value ? null : sendMessage,
              ),
              border: OutlineInputBorder( // Fügt einen Rahmen hinzu
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide.none, // Kein sichtbarer Rahmen, aber Form ist da
              ),
              filled: true, // Damit die Hintergrundfarbe wirkt
              fillColor: Colors.white, // Hintergrundfarbe des Textfelds
            ),
            enabled: !isSending.value,
            onSubmitted: (_) => sendMessage(),
          ),
          if (chatResponse.value.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Container( // Container für Hintergrund und Padding
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: chatResponse.value.startsWith('Fehler:') 
                      ? Colors.red[100] // Heller roter Hintergrund bei Fehler
                      : Colors.blue[50], // Heller blauer Hintergrund für normale Antworten
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SelectableText( // SelectableText statt Text
                  chatResponse.value, // Zeige die Antwort oder Fehlermeldung direkt
                  style: TextStyle(
                    color: chatResponse.value.startsWith('Fehler:') 
                        ? Colors.red[900] // Dunkelrote Schrift bei Fehler
                        : Colors.black87, // Normale Schriftfarbe
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

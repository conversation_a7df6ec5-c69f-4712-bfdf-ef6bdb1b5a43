import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../application/providers/applied_jobs_provider.dart';
import '../../../application/providers/applied_job_entities_provider.dart';
import '../../../core/theme/app_theme.dart';
import 'package:ki_test/src/presentation/job_detail/screens/job_detail_screen.dart';
import '../widgets/applied_job_list_item.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import '../../common/widgets/job_search_filter.dart';

/// Eine Ansicht, die alle Jobs anzeigt, auf die sich der Benutzer beworben hat.
class AppliedJobsScreen extends HookConsumerWidget {
  const AppliedJobsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Beobachte den Zustand der beworbenen Jobs
    final appliedJobsState = ref.watch(appliedJobsProvider);

    // Beobachte den Zustand der Job-Entitäten für beworbene Jobs
    final appliedJobEntitiesState = ref.watch(appliedJobEntitiesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Beworbene Jobs'),
        elevation: 0,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryDarkColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: appliedJobEntitiesState.when(
        data: (appliedJobs) {
          if (appliedJobs.isEmpty) {
            return _buildEmptyView(context);
          } else {
            return _AppliedJobsWithFilter(appliedJobs: appliedJobs, ref: ref);
          }
        },
        loading: () {
          // Wenn wir bereits Daten haben, zeigen wir diese weiterhin an, um Flackern zu vermeiden
          final previousJobs =
              ref.watch(appliedJobEntitiesProvider).valueOrNull;
          if (previousJobs != null && previousJobs.isNotEmpty) {
            return Stack(
              children: [
                _AppliedJobsWithFilter(appliedJobs: previousJobs, ref: ref),
                // Lade-Indikator oben anzeigen
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: LinearProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            );
          }
          return const Center(child: CircularProgressIndicator());
        },
        error:
            (error, stackTrace) => Center(
              child: Text('Fehler beim Laden der beworbenen Jobs: $error'),
            ),
      ),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.work_off_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Keine beworbenen Jobs',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              'Hier werden Jobs angezeigt, auf die du dich beworben hast.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class _AppliedJobsWithFilter extends HookWidget {
  final List<JobEntity> appliedJobs;
  final WidgetRef ref;

  const _AppliedJobsWithFilter({
    required this.appliedJobs,
    required this.ref,
  });

  @override
  Widget build(BuildContext context) {
    final filteredJobs = useState<List<JobEntity>>(appliedJobs);

    // Aktualisiere gefilterte Jobs wenn sich die beworbenen Jobs ändern
    useEffect(() {
      filteredJobs.value = appliedJobs;
      return null;
    }, [appliedJobs]);

    return Column(
      children: [
        // Suchfilter
        JobSearchFilter(
          originalJobs: appliedJobs,
          onFilteredJobsChanged: (filtered) {
            filteredJobs.value = filtered;
          },
          hintText: 'Beworbene Jobs durchsuchen...',
        ),
        
        // Ergebnisanzahl anzeigen
        if (filteredJobs.value.length != appliedJobs.length)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
              vertical: AppTheme.spacingXSmall,
            ),
            child: Text(
              '${filteredJobs.value.length} von ${appliedJobs.length} beworbenen Jobs',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        
        // Job-Liste
        Expanded(
          child: filteredJobs.value.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),
                      Text(
                        'Keine beworbenen Jobs gefunden',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Versuche andere Suchbegriffe',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  // Verringerter Abstand zur Navigation
                  padding: const EdgeInsets.only(
                    left: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                    right: AppTheme.spacingMedium - AppTheme.spacingXSmall,
                    top: AppTheme.spacingSmall,
                    bottom: kBottomNavigationBarHeight - 32,
                  ),
                  itemCount: filteredJobs.value.length,
                  itemBuilder: (context, index) {
                    final job = filteredJobs.value[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: AppliedJobListItem(
                    job: job,
                    onTap: () {
                      // Snackbar ausblenden, wenn zu einer anderen Seite navigiert wird
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => JobDetailScreen(
                            jobRefnr: job.id,
                            jobTitle: job.title,
                            sourceUrl: job.sourceUrl,
                            jobEntity: job,
                          ),
                        ),
                      );
                    },
                    onDismissed: () {
                        // Job speichern, bevor er entfernt wird, um ihn ggf. wiederherstellen zu können
                        final deletedJob = job;
                        ref.read(appliedJobsProvider.notifier).unmarkJobAsApplied(job.id);
                        
                        // Snackbar mit Wiederherstellungsoption für 5 Sekunden anzeigen
                        ScaffoldMessenger.of(context).clearSnackBars();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            backgroundColor: Colors.red[700],
                            content: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Aus Bewerbungen entfernt',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 13,
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    // Job wiederherstellen
                                    ref
                                        .read(appliedJobsProvider.notifier)
                                        .restoreJobAsApplied(deletedJob);
                                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                  },
                                  child: Text(
                                    'Wiederherstellen',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            duration: const Duration(seconds: 5),
                            behavior: SnackBarBehavior.fixed,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            elevation: 4,
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
        ),
      ],
    );
  }
}

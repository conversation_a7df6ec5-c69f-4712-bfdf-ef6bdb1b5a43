import 'package:ki_test/src/domain/models/user_profile.dart'; // Für WorkExperience und Education

class ExtractedCvData {
  final List<String> skills;
  final List<WorkExperience> workExperience;
  final List<Education> education;

  ExtractedCvData({
    required this.skills,
    required this.workExperience,
    required this.education,
  });

  factory ExtractedCvData.fromJson(Map<String, dynamic> json) {
    // Hilfsfunktion zum sicheren Parsen von Listen
    List<T> parseList<T>(
      dynamic listData,
      T Function(Map<String, dynamic>) from<PERSON>son,
    ) {
      if (listData is List) {
        return listData
            .map((item) {
              try {
                // Stelle sicher, dass das Item eine Map ist
                if (item is Map<String, dynamic>) {
                  return from<PERSON>son(item);
                }
                return null; // Ignoriere ungültige Items
              } catch (e) {
                print(
                  '<PERSON><PERSON> beim <PERSON> eines Listen-Items: $e - Item: $item',
                ); // Logge auch das fehlerhafte Item
                return null; // Ignoriere Items, die Fehler verursachen
              }
            })
            .where(
              (item) => item != null,
            ) // Filtere ungültige/fehlerhafte Items heraus
            .cast<T>() // Stelle den korrekten Typ sicher
            .toList();
      }
      return []; // Leere Liste, wenn keine gültige Liste vorhanden ist
    }

    // Hilfsfunktion zum sicheren Parsen der Skill-Liste
    List<String> parseSkills(dynamic listData) {
      if (listData is List) {
        return listData
            .where(
              (item) => item is String && item.isNotEmpty,
            ) // Nur nicht-leere Strings
            .cast<String>()
            .toList();
      }
      return [];
    }

    // Prüfe sowohl die alten als auch die neuen Schlüsselnamen
    return ExtractedCvData(
      skills: parseSkills(json['extractedSkills'] ?? json['skills'] ?? []),
      workExperience: parseList<WorkExperience>(
        json['extractedExperience'] ?? json['workExperience'] ?? [],
        (itemJson) => WorkExperience.fromJson(itemJson),
      ),
      education: parseList<Education>(
        json['extractedEducation'] ?? json['education'] ?? [],
        (itemJson) => Education.fromJson(itemJson),
      ),
    );
  }

  // Optional: Methode zum Vergleichen mit einem UserProfile
  bool hasDifferences(UserProfile currentProfile) {
    // Einfacher Vergleich der Listenlängen (könnte detaillierter sein)
    if (skills.isNotEmpty &&
        skills.length != (currentProfile.skills?.length ?? 0)) {
      return true;
    }
    if (workExperience.isNotEmpty &&
        workExperience.length != (currentProfile.workExperience?.length ?? 0)) {
      return true;
    }
    if (education.isNotEmpty &&
        education.length != (currentProfile.education?.length ?? 0)) {
      return true;
    }

    // TODO: Implementiere detaillierteren Vergleich der Inhalte, falls nötig

    return false; // Vorerst nur Längenvergleich
  }
}

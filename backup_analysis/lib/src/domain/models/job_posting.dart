import 'package:flutter/foundation.dart' show immutable;

@immutable
class JobPosting {
  final String id;
  final String title;
  final String companyName;
  final String location;
  final String descriptionSnippet; // <PERSON>rzer Anriss für die Liste
  final String fullDescription; // Vollständige Beschreibung für Details
  final DateTime postedDate;
  final String? sourceUrl; // URL zur Originalanzeige
  final double? relevanceScore; // KI-generierter Score (0.0 - 1.0)
  final List<String> requiredSkills;
  final String? employmentType; // z.B. Vollzeit, Teilzeit
  final String? salaryInfo; // z.B. "€50.000 - €70.000", "nach Vereinbarung"

  const JobPosting({
    required this.id,
    required this.title,
    required this.companyName,
    required this.location,
    required this.descriptionSnippet,
    required this.fullDescription,
    required this.postedDate,
    this.sourceUrl,
    this.relevanceScore,
    this.requiredSkills = const [],
    this.employmentType,
    this.salaryInfo,
  });
} 
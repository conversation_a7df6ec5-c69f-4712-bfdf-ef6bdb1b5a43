// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'additional_document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdditionalDocumentImpl _$$AdditionalDocumentImplFromJson(
        Map<String, dynamic> json) =>
    _$AdditionalDocumentImpl(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String,
      fileSize: (json['file_size'] as num).toInt(),
      fileType: json['file_type'] as String,
      uploadDate: DateTime.parse(json['upload_date'] as String),
      isActiveForApplications: json['is_active_for_applications'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$AdditionalDocumentImplToJson(
        _$AdditionalDocumentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'file_name': instance.fileName,
      'file_path': instance.filePath,
      'file_size': instance.fileSize,
      'file_type': instance.fileType,
      'upload_date': instance.uploadDate.toIso8601String(),
      'is_active_for_applications': instance.isActiveForApplications,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$DocumentUploadResultImpl _$$DocumentUploadResultImplFromJson(
        Map<String, dynamic> json) =>
    _$DocumentUploadResultImpl(
      success: json['success'] as bool,
      documentId: json['documentId'] as String?,
      errorMessage: json['errorMessage'] as String?,
      document: json['document'] == null
          ? null
          : AdditionalDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DocumentUploadResultImplToJson(
        _$DocumentUploadResultImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'documentId': instance.documentId,
      'errorMessage': instance.errorMessage,
      'document': instance.document,
    };

_$DocumentUploadProgressImpl _$$DocumentUploadProgressImplFromJson(
        Map<String, dynamic> json) =>
    _$DocumentUploadProgressImpl(
      fileName: json['fileName'] as String,
      progress: (json['progress'] as num).toDouble(),
      status: $enumDecode(_$DocumentUploadStatusEnumMap, json['status']),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$DocumentUploadProgressImplToJson(
        _$DocumentUploadProgressImpl instance) =>
    <String, dynamic>{
      'fileName': instance.fileName,
      'progress': instance.progress,
      'status': _$DocumentUploadStatusEnumMap[instance.status]!,
      'errorMessage': instance.errorMessage,
    };

const _$DocumentUploadStatusEnumMap = {
  DocumentUploadStatus.preparing: 'preparing',
  DocumentUploadStatus.uploading: 'uploading',
  DocumentUploadStatus.processing: 'processing',
  DocumentUploadStatus.completed: 'completed',
  DocumentUploadStatus.failed: 'failed',
};

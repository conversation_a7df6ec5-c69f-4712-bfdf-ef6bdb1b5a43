class JobAnalysisResult {
  final double relevanceScore;
  final List<String> matchReasons;
  final List<String> mismatchReasons;

  JobAnalysisResult({
    required this.relevanceScore,
    required this.matchReasons,
    required this.mismatchReasons,
  });

  factory JobAnalysisResult.fromJson(Map<String, dynamic> json) {
    return JobAnalysisResult(
      relevanceScore: (json['relevanceScore'] as num?)?.toDouble() ?? 0.0,
      matchReasons: List<String>.from(json['matchReasons'] ?? []),
      mismatchReasons: List<String>.from(json['mismatchReasons'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'relevanceScore': relevanceScore,
      'matchReasons': matchReasons,
      'mismatchReasons': mismatchReasons,
    };
  }
} 
import 'package:freezed_annotation/freezed_annotation.dart';

part 'additional_document.freezed.dart';
part 'additional_document.g.dart';

/// Modell für zusätzliche Dokumente eines Benutzers
@freezed
class AdditionalDocument with _$AdditionalDocument {
  const factory AdditionalDocument({
    required String id,
    required String userId,
    required String fileName,
    required String filePath,
    required int fileSize,
    required String fileType,
    required DateTime uploadDate,
    @Default(false) bool isActiveForApplications,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _AdditionalDocument;

  factory AdditionalDocument.fromJson(Map<String, dynamic> json) =>
      _$AdditionalDocumentFromJson(json);
}

/// Enum für unterstützte Dateitypen
enum SupportedFileType {
  pdf('application/pdf', 'PDF'),
  doc('application/msword', 'DOC'),
  docx('application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'DOCX');

  const SupportedFileType(this.mimeType, this.displayName);

  final String mimeType;
  final String displayName;

  static SupportedFileType? fromMimeType(String mimeType) {
    for (final type in SupportedFileType.values) {
      if (type.mimeType == mimeType) {
        return type;
      }
    }
    return null;
  }

  static SupportedFileType? fromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return SupportedFileType.pdf;
      case 'doc':
        return SupportedFileType.doc;
      case 'docx':
        return SupportedFileType.docx;
      default:
        return null;
    }
  }

  static List<String> get allowedExtensions => 
      SupportedFileType.values.map((e) => e.name).toList();
}

/// Upload-Ergebnis für zusätzliche Dokumente
@freezed
class DocumentUploadResult with _$DocumentUploadResult {
  const factory DocumentUploadResult({
    required bool success,
    String? documentId,
    String? errorMessage,
    AdditionalDocument? document,
  }) = _DocumentUploadResult;
}

/// Upload-Progress für UI-Feedback
@freezed
class DocumentUploadProgress with _$DocumentUploadProgress {
  const factory DocumentUploadProgress({
    required String fileName,
    required double progress, // 0.0 bis 1.0
    required DocumentUploadStatus status,
    String? errorMessage,
  }) = _DocumentUploadProgress;
}

enum DocumentUploadStatus {
  preparing,
  uploading,
  processing,
  completed,
  failed,
}

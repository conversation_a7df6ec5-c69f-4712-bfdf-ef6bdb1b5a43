/// Präferenzen und Einstellungen für die Jobsuche
class JobPreferences {
  final String? targetPosition;
  final String? industry;
  final String? locationPreference; // z.B. "Berlin", "Remote", "Bundesweit"
  final double? desiredSalary;
  final String? employmentType; // z.B. "Vollzeit", "Teilzeit", "Praktikum"

  const JobPreferences({
    this.targetPosition,
    this.industry,
    this.locationPreference,
    this.desiredSalary,
    this.employmentType,
  });

  factory JobPreferences.empty() => const JobPreferences();

  // Factory-Methode zum Erstellen aus JSON
  factory JobPreferences.fromJson(Map<String, dynamic> json) {
    return JobPreferences(
      targetPosition: json['targetPosition'],
      industry: json['industry'],
      locationPreference: json['locationPreference'],
      desiredSalary:
          json['desiredSalary'] != null
              ? (json['desiredSalary'] is int
                  ? (json['desiredSalary'] as int).toDouble()
                  : json['desiredSalary'] as double)
              : null,
      employmentType: json['employmentType'],
    );
  }

  JobPreferences copyWith({
    String? targetPosition,
    String? industry,
    String? locationPreference,
    double? desiredSalary,
    String? employmentType,
  }) {
    return JobPreferences(
      targetPosition: targetPosition ?? this.targetPosition,
      industry: industry ?? this.industry,
      locationPreference: locationPreference ?? this.locationPreference,
      desiredSalary: desiredSalary ?? this.desiredSalary,
      employmentType: employmentType ?? this.employmentType,
    );
  }

  // Konvertiert das JobPreferences-Objekt in eine Map für Firestore
  Map<String, dynamic> toJson() {
    return {
      if (targetPosition != null) 'targetPosition': targetPosition,
      if (industry != null) 'industry': industry,
      if (locationPreference != null) 'locationPreference': locationPreference,
      if (desiredSalary != null) 'desiredSalary': desiredSalary,
      if (employmentType != null) 'employmentType': employmentType,
    };
  }
} 
/// Entität für extrahierte und gecachte Job-Texte
/// Repräsentiert bereinigten Text ohne HTML-Tags für Offline-Verfügbarkeit
class ExtractedJobText {
  final String jobId;
  final String? sourceUrl;
  final String extractedText;
  final String jobTitle;
  final String companyName;
  final String? location;
  final List<String> requirements;
  final List<String> benefits;
  final Map<String, dynamic> contactInfo;
  final String contentHash;
  final int contentSize;
  final DateTime? extractedAt;
  final DateTime? lastAccessed;

  // Neue Felder für Quell-Timestamps
  final String? sourceType; // 'favorite', 'applied_job', 'both'
  final DateTime? sourceAddedAt; // Zeitpunkt der ersten Hinzufügung
  final DateTime? favoriteAddedAt; // Spezifisch für Favoriten
  final DateTime? appliedAt; // Spezifisch für Bewerbungen

  const ExtractedJobText({
    required this.jobId,
    this.sourceUrl,
    required this.extractedText,
    required this.jobTitle,
    required this.companyName,
    this.location,
    this.requirements = const [],
    this.benefits = const [],
    this.contactInfo = const {},
    required this.contentHash,
    required this.contentSize,
    this.extractedAt,
    this.lastAccessed,
    this.sourceType,
    this.sourceAddedAt,
    this.favoriteAddedAt,
    this.appliedAt,
  });

  /// Factory-Konstruktor für die Erstellung aus JSON (Supabase)
  factory ExtractedJobText.fromJson(Map<String, dynamic> json) {
    return ExtractedJobText(
      jobId: json['job_id'] as String,
      sourceUrl: json['source_url'] as String?,
      extractedText: json['extracted_text'] as String,
      jobTitle: json['job_title'] as String,
      companyName: json['company_name'] as String,
      location: json['location'] as String?,
      requirements: _parseStringArray(json['requirements']),
      benefits: _parseStringArray(json['benefits']),
      contactInfo: Map<String, dynamic>.from(json['contact_info'] ?? {}),
      contentHash: json['content_hash'] as String,
      contentSize: json['content_size'] as int,
      extractedAt: json['extracted_at'] != null
          ? DateTime.parse(json['extracted_at'] as String)
          : null,
      lastAccessed: json['last_accessed'] != null
          ? DateTime.parse(json['last_accessed'] as String)
          : null,
      sourceType: json['source_type'] as String?,
      sourceAddedAt: json['source_added_at'] != null
          ? DateTime.parse(json['source_added_at'] as String)
          : null,
      favoriteAddedAt: json['favorite_added_at'] != null
          ? DateTime.parse(json['favorite_added_at'] as String)
          : null,
      appliedAt: json['applied_at'] != null
          ? DateTime.parse(json['applied_at'] as String)
          : null,
    );
  }

  /// Konvertiert zu JSON für Supabase-Speicherung
  Map<String, dynamic> toJson() {
    return {
      'job_id': jobId,
      'source_url': sourceUrl,
      'extracted_text': extractedText,
      'job_title': jobTitle,
      'company_name': companyName,
      'location': location,
      'requirements': requirements,
      'benefits': benefits,
      'contact_info': contactInfo,
      'content_hash': contentHash,
      'content_size': contentSize,
      if (extractedAt != null) 'extracted_at': extractedAt!.toIso8601String(),
      if (lastAccessed != null) 'last_accessed': lastAccessed!.toIso8601String(),
      if (sourceType != null) 'source_type': sourceType,
      if (sourceAddedAt != null) 'source_added_at': sourceAddedAt!.toIso8601String(),
      if (favoriteAddedAt != null) 'favorite_added_at': favoriteAddedAt!.toIso8601String(),
      if (appliedAt != null) 'applied_at': appliedAt!.toIso8601String(),
    };
  }

  /// Hilfsmethode zum Parsen von String-Arrays aus PostgreSQL
  static List<String> _parseStringArray(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    if (value is String) {
      // PostgreSQL Array-Format: {item1,item2,item3}
      if (value.startsWith('{') && value.endsWith('}')) {
        final content = value.substring(1, value.length - 1);
        if (content.isEmpty) return [];
        return content.split(',').map((e) => e.trim()).toList();
      }
      return [value];
    }
    return [];
  }

  /// Erstellt eine Kopie mit geänderten Feldern
  ExtractedJobText copyWith({
    String? jobId,
    String? sourceUrl,
    String? extractedText,
    String? jobTitle,
    String? companyName,
    String? location,
    List<String>? requirements,
    List<String>? benefits,
    Map<String, dynamic>? contactInfo,
    String? contentHash,
    int? contentSize,
    DateTime? extractedAt,
    DateTime? lastAccessed,
    String? sourceType,
    DateTime? sourceAddedAt,
    DateTime? favoriteAddedAt,
    DateTime? appliedAt,
  }) {
    return ExtractedJobText(
      jobId: jobId ?? this.jobId,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      extractedText: extractedText ?? this.extractedText,
      jobTitle: jobTitle ?? this.jobTitle,
      companyName: companyName ?? this.companyName,
      location: location ?? this.location,
      requirements: requirements ?? this.requirements,
      benefits: benefits ?? this.benefits,
      contactInfo: contactInfo ?? this.contactInfo,
      contentHash: contentHash ?? this.contentHash,
      contentSize: contentSize ?? this.contentSize,
      extractedAt: extractedAt ?? this.extractedAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      sourceType: sourceType ?? this.sourceType,
      sourceAddedAt: sourceAddedAt ?? this.sourceAddedAt,
      favoriteAddedAt: favoriteAddedAt ?? this.favoriteAddedAt,
      appliedAt: appliedAt ?? this.appliedAt,
    );
  }

  /// Prüft, ob der Text aktuell ist (basierend auf Hash)
  bool isContentCurrent(String newContentHash) {
    return contentHash == newContentHash;
  }

  /// Gibt eine lesbare Zusammenfassung zurück
  String getSummary({int maxLength = 200}) {
    if (extractedText.length <= maxLength) {
      return extractedText;
    }
    return '${extractedText.substring(0, maxLength)}...';
  }

  /// Prüft, ob Kontaktinformationen verfügbar sind
  bool hasContactInfo() {
    return contactInfo.isNotEmpty;
  }

  /// Gibt verfügbare E-Mail-Adressen zurück
  List<String> getEmails() {
    final emails = contactInfo['emails'];
    if (emails is List) {
      return emails.map((e) => e.toString()).toList();
    }
    return [];
  }

  /// Gibt verfügbare Telefonnummern zurück
  List<String> getPhones() {
    final phones = contactInfo['phones'];
    if (phones is List) {
      return phones.map((e) => e.toString()).toList();
    }
    return [];
  }

  /// Prüft, ob der Cache abgelaufen ist (älter als 6 Monate)
  bool isExpired() {
    if (extractedAt == null) return false;
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    return extractedAt!.isBefore(sixMonthsAgo);
  }

  /// Gibt die Größe in KB zurück
  double getSizeInKB() {
    return contentSize / 1024.0;
  }

  @override
  String toString() {
    return 'ExtractedJobText(jobId: $jobId, title: $jobTitle, company: $companyName, size: ${getSizeInKB().toStringAsFixed(1)}KB)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExtractedJobText &&
        other.jobId == jobId &&
        other.contentHash == contentHash;
  }

  @override
  int get hashCode {
    return jobId.hashCode ^ contentHash.hashCode;
  }
}

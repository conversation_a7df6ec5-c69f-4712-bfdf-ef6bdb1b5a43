/// Entität für eine Bewerbung
class ApplicationEntity {
  final String id;
  final String userId;
  final String jobId;
  final String jobTitle;
  final String companyName;
  final String recipientEmail;
  final String status;
  final String coverLetter;
  final DateTime sentAt;
  final DateTime lastUpdated;
  final String? response;
  final DateTime? responseDate;

  ApplicationEntity({
    required this.id,
    required this.userId,
    required this.jobId,
    required this.jobTitle,
    required this.companyName,
    required this.recipientEmail,
    required this.status,
    required this.coverLetter,
    required this.sentAt,
    required this.lastUpdated,
    this.response,
    this.responseDate,
  });

  factory ApplicationEntity.fromJson(Map<String, dynamic> json) {
    return ApplicationEntity(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      jobId: json['job_id'] as String,
      jobTitle: json['job_title'] as String,
      companyName: json['company_name'] as String,
      recipientEmail: json['recipient_email'] as String,
      status: json['status'] as String,
      coverLetter: json['cover_letter'] as String,
      sentAt: DateTime.parse(json['sent_at'] as String),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      response: json['response'] as String?,
      responseDate:
          json['response_date'] != null
              ? DateTime.parse(json['response_date'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'job_title': jobTitle,
      'company_name': companyName,
      'recipient_email': recipientEmail,
      'status': status,
      'cover_letter': coverLetter,
      'sent_at': sentAt.toIso8601String(),
      'last_updated': lastUpdated.toIso8601String(),
      if (response != null) 'response': response,
      if (responseDate != null)
        'response_date': responseDate!.toIso8601String(),
    };
  }
}

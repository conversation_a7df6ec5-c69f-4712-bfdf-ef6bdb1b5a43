import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:http/http.dart' as http;

import 'package:ki_test/src/application/services/text_extraction_service.dart';
import 'package:ki_test/src/domain/entities/job_entity.dart';
import 'package:ki_test/src/domain/entities/extracted_job_text.dart';

import 'text_extraction_service_test.mocks.dart';

@GenerateMocks([SupabaseClient, SupabaseQueryBuilder, http.Client])
void main() {
  group('TextExtractionService Tests', () {
    late TextExtractionService service;
    late MockSupabaseClient mockSupabaseClient;
    late MockSupabaseQueryBuilder mockQueryBuilder;
    late MockClient mockHttpClient;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockQueryBuilder = MockSupabaseQueryBuilder();
      mockHttpClient = MockClient();
      service = TextExtractionService(mockSupabaseClient);
    });

    tearDown(() {
      service.dispose();
    });

    group('Text-Extraktion aus JobEntity', () {
      test('sollte Text aus JobEntity.description extrahieren', () async {
        // Arrange
        final job = JobEntity(
          id: 'test-job-1',
          title: 'Software Developer',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '''
            <h1>Software Developer Position</h1>
            <p>Wir suchen einen erfahrenen Software Developer.</p>
            <h2>Anforderungen:</h2>
            <ul>
              <li>5+ Jahre Erfahrung</li>
              <li>Flutter/Dart Kenntnisse</li>
              <li>Teamfähigkeit</li>
            </ul>
            <h2>Wir bieten:</h2>
            <ul>
              <li>Flexible Arbeitszeiten</li>
              <li>Homeoffice möglich</li>
              <li>Weiterbildungsmöglichkeiten</li>
            </ul>
            <p>Kontakt: <EMAIL> oder 030-12345678</p>
          ''',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.jobId, equals('test-job-1'));
        expect(result.jobTitle, equals('Software Developer'));
        expect(result.companyName, equals('Test Company'));
        expect(result.extractedText, contains('Software Developer'));
        expect(result.extractedText, contains('erfahrenen Software Developer'));
        expect(result.requirements.length, greaterThan(0));
        expect(result.benefits.length, greaterThan(0));
        expect(result.contactInfo['emails'], contains('<EMAIL>'));
        expect(result.contactInfo['phones'], contains('030-12345678'));
      });

      test('sollte leeren Text handhaben', () async {
        // Arrange
        final job = JobEntity(
          id: 'test-job-2',
          title: 'Empty Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '',
        );

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNull);
      });

      test('sollte HTML-Tags korrekt entfernen', () async {
        // Arrange
        final job = JobEntity(
          id: 'test-job-3',
          title: 'HTML Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '<p>Test <strong>bold</strong> und <em>italic</em> Text.</p>',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.extractedText, equals('Test bold und italic Text.'));
        expect(result.extractedText, isNot(contains('<')));
        expect(result.extractedText, isNot(contains('>')));
      });
    });

    group('Caching-Mechanismus', () {
      test('sollte gecachten Text aus Supabase laden', () async {
        // Arrange
        final job = JobEntity(
          id: 'cached-job-1',
          title: 'Cached Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: 'Test description',
        );

        final cachedData = {
          'job_id': 'cached-job-1',
          'extracted_text': 'Cached extracted text',
          'job_title': 'Cached Job',
          'company_name': 'Test Company',
          'location': 'Berlin',
          'requirements': ['Requirement 1', 'Requirement 2'],
          'benefits': ['Benefit 1', 'Benefit 2'],
          'contact_info': {'emails': ['<EMAIL>']},
          'content_hash': 'test-hash',
          'content_size': 100,
          'extracted_at': DateTime.now().toIso8601String(),
        };

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => cachedData);

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.jobId, equals('cached-job-1'));
        expect(result.extractedText, equals('Cached extracted text'));
        expect(result.requirements.length, equals(2));
        expect(result.benefits.length, equals(2));
      });

      test('sollte Memory-Cache verwenden', () async {
        // Arrange
        final job = JobEntity(
          id: 'memory-cache-job',
          title: 'Memory Cache Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: 'Test description for memory cache',
        );

        // Mock Supabase responses für ersten Aufruf
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act - Erster Aufruf (sollte extrahieren und cachen)
        final result1 = await service.extractAndCacheJobText(job);
        
        // Act - Zweiter Aufruf (sollte aus Memory-Cache laden)
        final result2 = await service.extractAndCacheJobText(job);

        // Assert
        expect(result1, isNotNull);
        expect(result2, isNotNull);
        expect(result1!.jobId, equals(result2!.jobId));
        expect(result1.extractedText, equals(result2.extractedText));
        
        // Verify dass Supabase nur einmal aufgerufen wurde
        verify(mockQueryBuilder.maybeSingle()).called(1);
      });
    });

    group('Anforderungen und Benefits Extraktion', () {
      test('sollte Anforderungen korrekt extrahieren', () async {
        // Arrange
        final job = JobEntity(
          id: 'requirements-job',
          title: 'Requirements Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '''
            Anforderungen:
            • 5+ Jahre Berufserfahrung
            • Kenntnisse in Flutter und Dart
            • Teamfähigkeit und Kommunikationsstärke
            
            Wir erwarten:
            - Abgeschlossenes Studium
            - Englischkenntnisse
          ''',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.requirements.length, greaterThan(0));
        expect(result.requirements.any((req) => req.contains('Berufserfahrung')), isTrue);
        expect(result.requirements.any((req) => req.contains('Flutter')), isTrue);
      });

      test('sollte Benefits korrekt extrahieren', () async {
        // Arrange
        final job = JobEntity(
          id: 'benefits-job',
          title: 'Benefits Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '''
            Wir bieten:
            • Flexible Arbeitszeiten
            • Homeoffice möglich
            • 30 Tage Urlaub
            
            Benefits:
            - Weiterbildungsbudget
            - Firmenwagen
          ''',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.benefits.length, greaterThan(0));
        expect(result.benefits.any((benefit) => benefit.contains('Arbeitszeiten')), isTrue);
        expect(result.benefits.any((benefit) => benefit.contains('Homeoffice')), isTrue);
      });
    });

    group('Kontaktinformationen Extraktion', () {
      test('sollte E-Mail-Adressen extrahieren', () async {
        // Arrange
        final job = JobEntity(
          id: 'contact-job',
          title: 'Contact Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '''
            Bewerbungen an: <EMAIL>
            Rückfragen an: <EMAIL>
            Oder kontaktieren Sie <NAME_EMAIL>
          ''',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.contactInfo['emails'], isNotNull);
        final emails = result.contactInfo['emails'] as List;
        expect(emails.length, equals(3));
        expect(emails, contains('<EMAIL>'));
        expect(emails, contains('<EMAIL>'));
        expect(emails, contains('<EMAIL>'));
      });

      test('sollte deutsche Telefonnummern extrahieren', () async {
        // Arrange
        final job = JobEntity(
          id: 'phone-job',
          title: 'Phone Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: '''
            Telefon: 030-12345678
            Mobil: +49 171 1234567
            Fax: 0301234567
          ''',
        );

        // Mock Supabase responses
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenAnswer((_) async => null);
        when(mockQueryBuilder.upsert(any)).thenAnswer((_) async => {});

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNotNull);
        expect(result!.contactInfo['phones'], isNotNull);
        final phones = result.contactInfo['phones'] as List;
        expect(phones.length, greaterThan(0));
        expect(phones.any((phone) => phone.contains('030')), isTrue);
      });
    });

    group('Error Handling', () {
      test('sollte Supabase-Fehler graceful handhaben', () async {
        // Arrange
        final job = JobEntity(
          id: 'error-job',
          title: 'Error Test Job',
          companyName: 'Test Company',
          location: 'Berlin',
          publishedDate: DateTime.now(),
          description: 'Test description',
        );

        // Mock Supabase error
        when(mockSupabaseClient.from('job_text_cache')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.maybeSingle()).thenThrow(Exception('Supabase error'));

        // Act
        final result = await service.extractAndCacheJobText(job);

        // Assert
        expect(result, isNull);
      });
    });
  });
}

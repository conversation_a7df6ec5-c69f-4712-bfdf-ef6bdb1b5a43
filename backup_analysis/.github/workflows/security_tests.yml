name: Security Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 0 * * 0'  # <PERSON><PERSON> um Mitternacht

jobs:
  security_audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run security audit
        run: |
          mkdir -p security_reports
          chmod +x scripts/security_audit.sh
          ./scripts/security_audit.sh
      
      - name: Upload security audit report
        uses: actions/upload-artifact@v3
        with:
          name: security-audit-report
          path: security_reports/
  
  dependency_check:
    name: Dependency Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Check for outdated dependencies
        run: |
          flutter pub outdated > outdated_dependencies.txt
          cat outdated_dependencies.txt
      
      - name: Upload outdated dependencies report
        uses: actions/upload-artifact@v3
        with:
          name: outdated-dependencies-report
          path: outdated_dependencies.txt
  
  static_analysis:
    name: Static Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run Flutter analyze
        run: flutter analyze > flutter_analyze.txt
      
      - name: Upload Flutter analyze report
        uses: actions/upload-artifact@v3
        with:
          name: flutter-analyze-report
          path: flutter_analyze.txt
      
      - name: Run Flutter lint
        run: |
          if [ -f "analysis_options.yaml" ]; then
            flutter analyze --write=lint_report.txt
          else
            echo "No analysis_options.yaml file found. Skipping lint."
            echo "No analysis_options.yaml file found. Skipping lint." > lint_report.txt
          fi
      
      - name: Upload Flutter lint report
        uses: actions/upload-artifact@v3
        with:
          name: flutter-lint-report
          path: lint_report.txt
  
  secret_scanning:
    name: Secret Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install detect-secrets
      
      - name: Run detect-secrets
        run: |
          detect-secrets scan > secrets_report.json
      
      - name: Check for secrets
        run: |
          python -c "
          import json
          with open('secrets_report.json', 'r') as f:
              data = json.load(f)
          results = data.get('results', {})
          if results:
              print('Potential secrets found:')
              for file, secrets in results.items():
                  print(f'File: {file}')
                  for secret in secrets:
                      print(f'  Line {secret[\"line_number\"]}: {secret[\"type\"]}')
              exit(1)
          else:
              print('No secrets found.')
          "
      
      - name: Upload secrets report
        uses: actions/upload-artifact@v3
        with:
          name: secrets-report
          path: secrets_report.json
  
  mobsf_analysis:
    name: MobSF Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Build APK
        run: flutter build apk --release
      
      - name: Set up Docker
        uses: docker/setup-buildx-action@v2
      
      - name: Run MobSF
        run: |
          docker pull opensecurity/mobile-security-framework-mobsf:latest
          docker run -d -p 8000:8000 --name mobsf opensecurity/mobile-security-framework-mobsf:latest
          sleep 10
          
          # Upload APK to MobSF
          APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
          UPLOAD_RESPONSE=$(curl -F "file=@$APK_PATH" http://localhost:8000/api/v1/upload -H "Authorization:563d07b0f67b6d02c056f8ccc662a9da9d3c0e70d0ca4afc44c6a7eb")
          SCAN_HASH=$(echo $UPLOAD_RESPONSE | jq -r '.hash')
          
          # Start scan
          SCAN_RESPONSE=$(curl -X POST --url http://localhost:8000/api/v1/scan --data "scan_type=apk&file_name=app-release.apk&hash=$SCAN_HASH" -H "Authorization:563d07b0f67b6d02c056f8ccc662a9da9d3c0e70d0ca4afc44c6a7eb")
          
          # Get report
          curl -X POST --url http://localhost:8000/api/v1/report_json --data "hash=$SCAN_HASH" -H "Authorization:563d07b0f67b6d02c056f8ccc662a9da9d3c0e70d0ca4afc44c6a7eb" > mobsf_report.json
          
          # Stop MobSF
          docker stop mobsf
          docker rm mobsf
      
      - name: Upload MobSF report
        uses: actions/upload-artifact@v3
        with:
          name: mobsf-report
          path: mobsf_report.json
  
  summary:
    name: Security Tests Summary
    needs: [security_audit, dependency_check, static_analysis, secret_scanning, mobsf_analysis]
    runs-on: ubuntu-latest
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts
      
      - name: Create summary report
        run: |
          echo "# Security Tests Summary" > summary_report.md
          echo "" >> summary_report.md
          echo "## Security Audit" >> summary_report.md
          echo "" >> summary_report.md
          echo "See the security audit report in the artifacts." >> summary_report.md
          echo "" >> summary_report.md
          echo "## Dependency Check" >> summary_report.md
          echo "" >> summary_report.md
          echo "See the outdated dependencies report in the artifacts." >> summary_report.md
          echo "" >> summary_report.md
          echo "## Static Analysis" >> summary_report.md
          echo "" >> summary_report.md
          echo "See the Flutter analyze and lint reports in the artifacts." >> summary_report.md
          echo "" >> summary_report.md
          echo "## Secret Scanning" >> summary_report.md
          echo "" >> summary_report.md
          echo "See the secrets report in the artifacts." >> summary_report.md
          echo "" >> summary_report.md
          echo "## MobSF Analysis" >> summary_report.md
          echo "" >> summary_report.md
          echo "See the MobSF report in the artifacts." >> summary_report.md
      
      - name: Upload summary report
        uses: actions/upload-artifact@v3
        with:
          name: summary-report
          path: summary_report.md

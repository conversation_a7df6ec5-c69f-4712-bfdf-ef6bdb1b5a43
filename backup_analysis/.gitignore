# ===== NUR ESSENTIELLE FLUTTER APP DATEIEN =====
# Alles andere wird ignoriert für sauberes Git-Backup (max 200MB)

# Flutter/Dart Build-Artefakte (IMMER ignorieren)
build/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/

# IDE und Editor Dateien
.idea/
.vscode/
*.iml
*.ipr
*.iws
.DS_Store
.atom/
.buildlog/
.history
.svn/

# Environment und Secrets
.env
.env.*
firebase_options.dart
supabase/.env
*.key
*.keystore
*.jks
*.p8
*.p12
*.mobileprovision
key.properties
google-services.json
GoogleService-Info.plist

# Logs und temporäre Dateien
logs/
*.log
*.tmp
*.temp
.temp/
.cache/

# Node.js (falls vorhanden)
node_modules/
package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Große Dateien und Ordner (NICHT für App nötig)
_development/
_to_delete/
_deployment/
_resources/
_documentation/
_project_management/
memory/
admin/
scripts/
docs/
public/
tasks/
backup/
*.zip
*.tar.gz
*.dmg
*.iso
*.rar
*.7z
commandlinetools-mac.zip
augment-vip*/
jetbrains-reset-mac/
Propmt_FÜR.rtf

# Backup und temporäre Dateien
*.backup
*.bak
ki-test-backup-*
*.orig.*

# Dokumentation und Management (optional - zu groß)
*.rtf
REORGANISATION_PLAN.md
GOOGLE_*.md
README_FIXES.md
code*.txt
memory.jsonl
mcp.json

# Android Build-Artefakte
/android/app/debug
/android/app/profile
/android/app/release
*.apk
*.aab

# iOS Build-Artefakte
**/ios/Flutter/.last_build_id
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh

# Symbolication
app.*.symbols
app.*.map.json

# Coverage und Tests
coverage/
test_results/
test_coverage/

# Lokale Konfiguration
local.properties
gradle-wrapper.properties
.fvm/

# Generierte Dateien
lib/generated/
lib/generated_plugin_registrant.dart

# ===== ERLAUBTE DATEIEN/ORDNER (ca. 16MB): =====
# lib/ - Flutter App Code (3.7M)
# android/ - Android Konfiguration (11M)
# ios/ - iOS Konfiguration (260K)
# macos/ - macOS Konfiguration (332K)
# windows/ - Windows Konfiguration (124K)
# web/ - Web Konfiguration (64K)
# linux/ - Linux Konfiguration (56K)
# supabase/ - Backend Edge Functions (424K)
# test/ - Tests
# pubspec.yaml - Dependencies
# analysis_options.yaml - Dart Analyse
# l10n.yaml - Lokalisierung
# .gitignore - Git Konfiguration
# README.md - Basis-Dokumentation

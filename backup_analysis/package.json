{"name": "ki_test", "version": "1.0.0", "description": "A new Flutter project.", "main": "index.js", "directories": {"lib": "lib", "test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "taskmaster": "task-master-ai", "dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@google/generative-ai": "^0.24.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "expo": "^49.0.23", "expo-cli": "^6.3.12", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0", "task-master-ai": "latest"}}
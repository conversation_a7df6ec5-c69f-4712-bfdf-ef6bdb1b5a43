---
description: Comprehensive reference for Taskmaster MCP tools and CLI commands.
globs: **/*
alwaysApply: true
---
# Anwendungsfunktionen - Detaillierte Beschreibung

Dieses Dokument beschreibt die Kernfunktionen der Anwendung, ihre Implementierung, die beteiligten Komponenten und die zugrundeliegende Logik, einschließlich vorübergehend deaktivierter Features.

*(Stand: [Datum der letzten Aktualisierung] - Laufend aktualisiert)*

---
 das ist eine Erinnerung von Funktionen. Bitte bei jeder Änderung nicht, die Funktionen beeinträchtigen
## Inhaltsverzeichnis

1.  [Authentifizierung (Supabase)](mdc:#1-authentifizierung-supabased)
2.  [Jobsuche (Agentur für Arbeit API)](mdc:#2-jobsuche-agentur-für-arbeit-api)
3.  [Jobdetails & KI-Anschreiben](mdc:#3-jobdetails--ki-anschreiben)
4.  [KI-Job-Feed / Job-Empfehlungen](mdc:#4-ki-job-feed--job-empfehlungen)
5.  [Nutzerprofil](mdc:#5-nutzerprofil)
6.  [KI-Features](mdc:#6-ki-features)
7.  [Favoriten](mdc:#7-favoriten)
8.  [Einstellungen](mdc:#8-einstellungen)
9.  [Onboarding](mdc:#9-onboarding)
10. [Splash Screen](mdc:#10-splash-screen)
11. [In-App-Käufe / Premium](mdc:#11-in-app-käufe--premium)
12. [Werbung (Google Mobile Ads)](mdc:#12-werbung-google-mobile-ads)
13. [Versionskontrolle & Backup (GitHub)](mdc:#13-versionskontrolle--backup-github)
14. [Backend-Übergang: Firebase zu Supabase](mdc:#14-backend-übergang-firebase-zu-supabase)
15. [PDF-Anzeige](mdc:#15-pdf-anzeige)
16. [Generische UI-Komponenten](mdc:#16-generische-ui-komponenten)

---

## 1. Authentifizierung (Supabase)

-   **Zweck:** Verwaltung von Benutzerkonten (Anmeldung, Registrierung, Passwort-Reset, Kontolöschung) über den Supabase Authentication Service.
-   **Technologien/Pakete:**
    -   `supabase_flutter`: Hauptpaket für Supabase-Interaktionen.
    -   `flutter_riverpod` / `hooks_riverpod`: State Management.
    -   `go_router`: Navigation und Routing basierend auf dem Auth-Status.
-   **Haupt-Screens/Widgets:**
    -   `LoginScreen` (`lib/src/presentation/auth/screens/login_screen.dart`)
    -   `RegisterScreen` (`lib/src/presentation/auth/screens/register_screen.dart`)
    -   `ForgotPasswordScreen` (`lib/src/presentation/auth/screens/forgot_password_screen.dart`)
    -   `ChangePasswordScreen` (`lib/src/presentation/profile/screens/change_password_screen.dart`)
-   **State Management & Provider:**
    -   `supabaseClientProvider` (`services_providers.dart`): Stellt die initialisierte Supabase-Client-Instanz bereit.
    -   `supabaseAuthStateProvider` (`main.dart`): Ein `StreamProvider`, der den `supabase.auth.onAuthStateChange`-Stream überwacht. Er liefert den aktuellen `AuthState` (enthält `Session?` und `AuthChangeEvent?`) und löst bei Änderungen eine Neubewertung aus (wichtig für Routing).
    -   `authProvider` (`auth_provider.dart`): Ein `NotifierProvider`, der den `AuthStateNotifier` bereitstellt.
    -   `AuthStateNotifier`: Verwaltet den Anwendungszustand bezüglich Authentifizierung (`User?`, Lade-/Fehlerstatus). Reagiert auf Aktionen aus der UI und interagiert mit `Supabase Auth`.
-   **Datenfluss & Logik:**
    -   **Initialisierung:** In `main.dart` wird `Supabase.initialize(...)` aufgerufen. Der `GoRouter` wird erstellt und sein `refreshListenable` an den `supabaseAuthStateProvider` gebunden.
    -   **Routing (`GoRouter` in `main.dart`):**
        -   Der `redirect`-Callback prüft den `AuthState` vom `supabaseAuthStateProvider`.
        -   *Angemeldet (`session != null`):* Wenn der Nutzer auf einer Auth-Seite (`/login`, `/register`) ist, wird er zu `/` (AppShell/Home) weitergeleitet. Sonst bleibt er, wo er ist.
        -   *Abgemeldet (`session == null`):* Wenn der Nutzer *nicht* auf einer Auth-Seite ist, wird er zu `/login` weitergeleitet.
    -   **Anmeldung (`LoginScreen`):**
        -   Benutzer gibt E-Mail/Passwort ein.
        -   Button-Klick ruft `ref.read(authProvider.notifier).signInWithEmailPassword(email, password)` auf.
        -   `AuthStateNotifier.signInWithEmailPassword`:
            -   Setzt Ladezustand.
            -   Ruft `supabase.auth.signInWithPassword(...)` auf.
            -   Bei Erfolg: Supabase aktualisiert intern den Auth-Status, der `supabaseAuthStateProvider` löst aus, `GoRouter` leitet weiter. Notifier setzt Ladezustand zurück.
            -   Bei Fehler (`AuthException`): Fängt den Fehler, extrahiert `e.message`, setzt Fehlerzustand im Notifier, zeigt Fehler in der UI (z.B. Snackbar).
    -   **Registrierung (`RegisterScreen`):**
        -   Benutzer gibt E-Mail/Passwort/Bestätigung ein.
        -   Button-Klick ruft `ref.read(authProvider.notifier).signUpWithEmailPassword(email, password)` auf.
        -   `AuthStateNotifier.signUpWithEmailPassword`:
            -   Ruft `supabase.auth.signUp(...)` auf.
            -   Bei Erfolg: Zeigt eine Meldung an, dass der Benutzer seine E-Mail bestätigen muss (Supabase sendet Bestätigungslink). Der Benutzer ist *noch nicht* angemeldet.
            -   Bei Fehler: Behandelt Fehler wie beim Login.
    -   **Passwort vergessen (`ForgotPasswordScreen`):**
        -   Benutzer gibt E-Mail ein.
        -   Button-Klick ruft `ref.read(authProvider.notifier).sendPasswordResetEmail(email)` auf.
        -   `AuthStateNotifier.sendPasswordResetEmail`: Ruft `supabase.auth.resetPasswordForEmail(...)` auf. Zeigt Erfolgs-/Fehlermeldung an.
    -   **Passwort ändern (`ChangePasswordScreen`):**
        -   Benutzer gibt neues Passwort/Bestätigung ein (muss angemeldet sein).
        -   Button-Klick ruft `ref.read(authProvider.notifier).updatePassword(newPassword)` auf.
        -   `AuthStateNotifier.updatePassword`: Ruft `supabase.auth.updateUser(UserAttributes(password: newPassword))` auf. Behandelt Erfolg/Fehler.
    -   **Abmelden (`SettingsScreen`):**
        -   Button-Klick ruft `ref.read(authProvider.notifier).signOut()` auf.
        -   `AuthStateNotifier.signOut`: Ruft `supabase.auth.signOut()` auf. Der `supabaseAuthStateProvider` löst aus, `GoRouter` leitet zu `/login`.
    -   **Konto löschen (`SettingsScreen` -> `_deleteAccount`):**
        -   Zeigt Bestätigungsdialog.
        -   Bei Bestätigung: Ruft `ref.read(authProvider.notifier).deleteAccount()` auf.
        -   `AuthStateNotifier.deleteAccount`:
            -   Versucht, das Konto zu löschen (idealerweise über eine sichere Backend-Funktion, nicht direkt Client-seitig mit Admin-Rechten).
            -   **Wichtig:** Fängt spezifisch `AuthException` ab, die auf `requires_recent_login` hinweist. Zeigt dem Benutzer eine Meldung, dass er sich kürzlich neu anmelden muss, bevor das Konto gelöscht werden kann.
            -   Bei Erfolg: Meldet den Benutzer ab (`signOut`).
            -   Bei anderem Fehler: Zeigt Fehlermeldung.
-   **Fehlerbehandlung:** `AuthException` wird in den Notifier-Methoden gefangen. Fehler-Messages werden extrahiert und über den State an die UI weitergegeben, die sie meist via `ScaffoldMessenger.of(context).showSnackBar` anzeigt.

---

## 2. Jobsuche (Agentur für Arbeit API)

-   **Zweck:** Suche und Anzeige von Stellenangeboten der Bundesagentur für Arbeit mit Filterung, Sortierung, Paginierung und optionaler Standortnutzung.
-   **Technologien/Pakete:**
    -   `flutter_riverpod` / `hooks_riverpod`: State Management.
    -   `http`: Für HTTP-Anfragen an die BA-API.
    -   `flutter_hooks`: Für `useEffect` (Initialisierung), `useState` (lokaler UI-State).
    -   `shared_preferences`: Speichern des letzten Suchorts/Radius.
    -   `location`, `geocoding_android`, `geolocator_android`: Ermitteln/Verarbeiten des Gerätestandorts.
    -   `permission_handler`: Abfrage von Standortberechtigungen.
    -   `simple_debouncer`: Verzögerung der Suchanfragen bei Texteingabe.
    -   `freezed`: Für unveränderliche State-Objekte (`JobSearchState`).
    -   `intl`: Zur Datumsformatierung in `JobListItem`.
-   **Haupt-Screens/Widgets:**
    -   `JobSearchScreen`: Hauptansicht, zeigt Suchleiste, Filter-Button, Jobliste, Lade-/Fehlerzustände.
    -   `CustomSearchBar`: Suchleiste mit `TextField` und Debouncing.
    -   `JobListItem`: Darstellung eines einzelnen Jobs in der Liste.
    -   `LocationInputDialog`: Dialog zur manuellen Eingabe von Ort/Radius.
    -   `FilterSortBar`: Zeigt aktive Filter an. *(Hinweis: Die Logik zur Anwendung detaillierterer Filter oder Sortieroptionen über diesen Balken hinaus ist ggf. noch nicht implementiert).*
-   **State Management & Provider:**
    -   `jobSearchProvider` (`job_search_provider.dart`): Stellt `JobSearchNotifier` bereit.
    -   `JobSearchState` (`freezed`): Enthält `jobs`, `isLoading`, `isLoadingMore`, `isInitializing`, `error`, `currentPage`, `hasReachedMax`, `currentFilters`, `currentKeywords`, `currentSortOption`.
    -   `JobSearchNotifier`: Enthält die Kernlogik für Suche, Filterung, Paginierung.
    -   `apiClientProvider` (`agentur_arbeit_api_client.dart`): Stellt `AgenturArbeitApiClient` bereit.
    -   `sharedPreferencesProvider`: Stellt `SharedPreferences`-Instanz bereit.
    -   `userProfileProvider`: Wird gelesen für optionale Personalisierung.
-   **Datenfluss & Logik:**
    -   **Initialisierung (`JobSearchScreen.useEffect`/`initializeSearch`):** Lädt letzte Suche (`SharedPreferences`), ermittelt ggf. Standort (`location`/`geocoding` nach Berechtigungsprüfung mit `permission_handler`), setzt initiale `currentFilters`, ruft `performSearch`.
    -   **Keyword-Suche (`CustomSearchBar` -> `JobSearchNotifier.triggerSearch`):** Nutzt `Debouncer`, ruft bei Eingabepause `performSearch(query: keywords)` auf.
    -   **Filter-Anwendung (Standort: `LocationInputDialog` -> `JobSearchNotifier.applyFilters`):** Aktualisiert `currentFilters` im State, ruft `performSearch` auf. *(Andere Filter/Sortierung über `FilterSortBar` evtl. noch nicht funktional).*
    -   **Kernsuche (`JobSearchNotifier.performSearch`):**
        -   Setzt `isLoading=true`, Paginierung zurück.
        -   Kombiniert `currentFilters` und `query`.
        -   Ruft `apiClient.searchJobs(filters, page: 1)` auf.
        -   **`AgenturArbeitApiClient.searchJobs`:** Baut URL (`/v4/jobs`), fügt Parameter (`was`, `wo`, `umkreis`, etc.) hinzu, sendet GET-Request mit API-Key via `http`. Parst JSON, mappt auf `List<JobEntity>`.
        -   Ergebnis wird im Notifier via `_processJobs` gefiltert.
        -   Aktualisiert State (`jobs`, `isLoading=false`, `hasReachedMax`). Bei Fehler: setzt `error`.
    -   **Clientseitige Filterung (`JobSearchNotifier._processJobs`):** Filtert die von der API erhaltene Jobliste basierend auf zusätzlichen Kriterien in `currentFilters` (z.B. 'Keine Vorerfahrung').
    -   **Paginierung/Infinite Scrolling (`JobSearchScreen.ScrollController` -> `JobSearchNotifier.loadMoreJobs`):** Scroll-Listener prüft Erreichen des Endes -> ruft `loadMoreJobs` auf -> erhöht `currentPage`, setzt `isLoadingMore=true` -> ruft `apiClient.searchJobs` für nächste Seite -> fügt neue Jobs an State an, setzt `isLoadingMore=false`, `hasReachedMax`.
-   **Fehlerbehandlung:** API-Fehler (HTTP-Status, Netzwerk) im `ApiClient` werden gefangen und als `Exception` geworfen. Der `JobSearchNotifier` fängt diese, setzt den `error`-String im State. Die UI (`JobSearchScreen`) zeigt diesen Fehler an. Standort-Fehler (Berechtigung verweigert, Service nicht verfügbar) werden explizit über `permission_handler` abgefragt und ggf. mit Hinweisen in der UI behandelt.

---

## 3. Jobdetails & KI-Anschreiben

-   **Zweck:** Detaillierte Ansicht eines Jobs (primär via WebView), Favorisieren, Generieren von KI-Anschreiben (inkl. Prüfung auf bestehende Entwürfe, **Premium-Check (aktuell deaktiviert)**, Captcha-Handling), Teilen per E-Mail mit CV-Anhang.
-   **Technologien/Pakete:**
    -   `webview_flutter`: Anzeige der Original-Webseite.
    -   `flutter_riverpod` / `hooks_riverpod`: State Management.
    -   `flutter_hooks`: Für Animation (`useAnimationController`).
    -   `shared_preferences`: Speichern/Lesen generierter Anschreiben pro Job.
    -   `supabase_flutter`: Für Supabase-Interaktionen (`SupabaseService`).
    -   `flutter_email_sender`: Öffnen der E-Mail-App.
    -   `path_provider`, `package:path`: Temporäres Speichern des heruntergeladenen CVs.
    -   `url_launcher`: Öffnen von URLs im externen Browser.
    -   `clipboard`: Kopieren von Text.
    -   `http` (indirekt via `SupabaseService`).
    -   `permission_handler` (implizit, falls Standort für E-Mail benötigt würde).
    -   `google_mobile_ads` (für optionale Werbeanzeige bei Premium-Check).
-   **Haupt-Screens/Widgets:**
    -   `JobDetailScreen`: Hauptansicht mit WebView, Footer-Bereich.
    -   `WebViewWidget`: Kernkomponente zur Anzeige.
    -   Dialoge (`_showGeneratedApplicationDialog`, `_showCaptchaDialog`, `_showUseExistingApplicationDialog`, `showPremiumOrAdDialog` aus `premium_dialogs.dart`).
-   **State Management & Provider:**
    -   Lokaler State in `_JobDetailScreenState`: `_isLoadingPage`, `_isExtracting`, `_hasPageError`, `_captchaDetected`, `_isDownloadingCv`, `_extractedJobText`, `_canGoBack`, `_showExternalLinkInfo`.
    -   `favoritesProvider`: Für Favoritenstatus und Aktionen.
    -   `userProfileProvider`: Zum Abrufen von Profildaten (Premium-Status, für KI, CV-Pfad).
    -   `supabaseServiceProvider`: Stellt `SupabaseService` bereit.
    -   `supabaseClientProvider`: Stellt `SupabaseClient` bereit (für direkten Storage-Zugriff).
    -   `paymentServiceProvider`: Stellt `PaymentService` bereit (für Premium-Kauf).
    -   `adServiceProvider`: Stellt `AdService` bereit (für Rewarded Ad).
    -   Animation Controller (`_buttonAnimationController`, `_buttonOpacityAnimation`).
-   **Datenfluss & Logik:**
    -   **WebView-Handling:** Initialisierung und Navigation wie in Abschnitt 2 beschrieben. `_checkForExternalLink` und `_checkForCaptcha` analysieren den Seiteninhalt nach dem Laden via JS.
    -   **Anschreiben-Generierung (`_triggerApplicationGeneration` - `async`):**
        -   **(Schritt A - Premium/Ad Check - AKTUELL AUSKOMMENTIERT):**
            -   *Ursprüngliche Logik:* Liest `userProfileState`. Wenn `userProfile.isPremium == true`, wird direkt `startGenerationProcess()` aufgerufen.
            -   *Ursprüngliche Logik:* Wenn `userProfile.isPremium == false`, wird `showPremiumOrAdDialog` angezeigt. Dieser bietet "Werbung ansehen" (ruft `adService.loadAndShowRewardedAd`, bei Erfolg wird `startGenerationProcess` aufgerufen) oder "Premium holen" (ruft `paymentService.buyPremiumSubscription`).
            -   *Aktueller Zustand:* Dieser Check ist derzeit auskommentiert, `_startGenerationProcess` wird direkt aufgerufen.
        -   **(Schritt B - Prüfung auf bestehenden Entwurf):**
            -   Ruft `_checkExistingApplication()` (SharedPreferences) auf.
            -   Wenn `existingApplication != null`:
                -   Zeigt `_showUseExistingApplicationDialog` (fragt: Abbrechen, Bestehendes nutzen, Neu generieren).
                -   Verarbeitet Nutzerwahl: "Bestehendes nutzen" zeigt alten Text, "Neu generieren" fährt fort, "Abbrechen" stoppt.
        -   **(Schritt C - Generierungsprozess starten (`_startGenerationProcess`) - nur wenn neu generiert werden soll):**
            -   Setzt `_isExtracting=true`, startet Animation.
            -   Extrahiert Text via `_extractTextFromWebView()` (JS-basiert).
            -   Wenn `_captchaDetected`: Zeigt `_showCaptchaDialog`. Bricht bei Abbruch ab.
            -   Ruft `_generateApplicationWithAI(extractedText)` auf.
            -   **`_generateApplicationWithAI`:** Bereitet Daten auf -> Ruft `supabaseService.generateCoverLetter` auf -> Supabase Edge Function `generate-cover-letter` kontaktiert KI -> Gibt Ergebnis (`GenerateCoverLetterResult`) zurück.
            -   Wenn erfolgreich: Speichert *neuen* Text mit `_saveGeneratedApplication()` (SharedPreferences).
            -   Zeigt Ergebnis/Fehler in `_showGeneratedApplicationDialog`.
            -   Setzt `_isExtracting=false`, stoppt Animation.
    -   **E-Mail senden (`_shareViaEmail` - `async`):**
        -   Wird aus `_showGeneratedApplicationDialog` aufgerufen.
        -   Holt `UserProfile` und `SupabaseClient`.
        -   Setzt `_isDownloadingCv=true`, zeigt temporäre Snackbar ("Lebenslauf wird vorbereitet...").
        -   Wenn `userProfile.cvFilePath` existiert: Ruft `_downloadCvLocally` auf (lädt von Supabase Storage, speichert temporär via `path_provider`).
        -   Setzt `_isDownloadingCv=false`.
        -   Erstellt `Email` Objekt (flutter_email_sender) mit Empfänger, Betreff, Body, Anhangspfad.
        -   Ruft `FlutterEmailSender.send(email)` auf.
        -   **Fehlerbehandlung:** Fängt `PlatformException` (z.B. keine Mail-App) und andere Fehler. Zeigt spezifische/allgemeine Fehlermeldungen via Snackbar (`_getReadableEmailError`). Bietet "Alternative" (Text kopieren).
    -   **UI-Fix (Tastatur):** Der Footer-Bereich (Hinweise-Textfeld, Buttons) wurde in den `body` des `Scaffold` verschoben und in `SingleChildScrollView` gepackt, um Überdeckung durch die Tastatur zu verhindern.
-   **Fehlerbehandlung:** WebView-Ladefehler, KI-Generierungsfehler, E-Mail-Versandfehler, CV-Downloadfehler, Premium-Kauf-/Werbe-Fehler. Kommunikation über Snackbars und Dialoge.

---

## 4. KI-Job-Feed / Job-Empfehlungen

-   **Zweck:** Bietet eine alternative, KI-gesteuerte Job-Entdeckung basierend auf dem Nutzerprofil statt auf expliziten Keywords. Zeigt relevante Jobs mit einem Match-Score und Begründungen an. Ermöglicht Chat-Interaktion für Rückfragen.
-   **Technologien/Pakete:** Riverpod, `http` (für Backend-Aufrufe), Cloud Functions/Supabase Functions, KI-API (z.B. DeepSeek für Matching/Ranking), `location`, `geocoding`, `permission_handler`, `circular_percent_indicator` (zur Visualisierung).
-   **Haupt-Screens/Widgets:**
    -   `AiSearchScreen`: Zeigt die Liste der empfohlenen Jobs, Lade-/Fehler-/Leerzustände, Button zum Starten/Aktualisieren, FAB für Chat.
    -   `AiEnhancedJobListItem`: Stellt einen Job dar, visualisiert KI-Score (z.B. `CircularPercentIndicator`) und zeigt Match-Gründe (z.B. `Chip`s).
    -   `Chatbox`: Überlagernder Dialog für textbasierte Interaktion mit einer Chat-KI.
-   **State Management & Provider:**
    -   `aiSearchProvider`: Stellt `AiSearchNotifier` bereit.
    -   `AiSearchState` (`freezed`): Enthält `recommendedJobs` (Liste von `AiJobRecommendationEntity` - enthält `JobEntity`, `matchScore`, `matchReasons`), `isLoading`, `error`, `chatHistory`.
    -   `AiSearchNotifier`: Verwaltet den State, löst die KI-Suche aus, handhabt Chat-Nachrichten.
    -   `userProfileProvider`: Wird gelesen, um das Profil an das Backend zu senden.
    -   Provider für Standortdienste (wie bei Jobsuche).
    -   `paymentServiceProvider`, `adServiceProvider`: Für Premium-Check.
-   **Datenfluss & Logik:**
    -   **Auslösen (`AiSearchScreen` -> `_startAiSearch`):** Button-Klick.
        -   **(Premium/Ad Check - Ähnlich wie Jobdetails, aktuell vermutlich deaktiviert/noch nicht implementiert):** Prüft `userProfile.isPremium`. Zeigt ggf. `showPremiumOrAdDialog`. Ruft bei Berechtigung `ref.read(aiSearchProvider.notifier).performAiJobSearch()` auf. *(Status der Implementierung/Aktivierung muss geprüft werden).*
    -   **`performAiJobSearch` (`AiSearchNotifier`):**
        -   Setzt `isLoading=true`.
        -   Holt `UserProfile`.
        -   Ermittelt aktuellen Standort (inkl. expliziter Berechtigungsprüfung via `permission_handler`).
        -   Ruft eine dedizierte Backend-Funktion auf (z.B. Supabase Function `findRelevantJobsAi`) und übergibt `UserProfile`-Daten und Standort.
        -   **Backend-Funktion (`findRelevantJobsAi` - vermuteter Ablauf):**
            -   Führt eine initiale Jobsuche in der Nähe durch (z.B. via BA-API).
            -   Iteriert durch die gefundenen Jobs.
            -   Für jeden Job: Ruft eine KI-API (z.B. DeepSeek) auf mit einem Prompt, der das Nutzerprofil und die Jobbeschreibung enthält, um eine Bewertung (Score) und kurze Begründungen für die Passung zu erhalten.
            -   Sammelt die Ergebnisse (Job + KI-Analyse) und gibt sie zurück.
        -   Notifier empfängt die `List<AiJobRecommendationEntity>`.
        -   Aktualisiert den State (`recommendedJobs`, `isLoading=false`). Bei Fehler: setzt `error`.
    -   **Anzeige (`AiSearchScreen`):** Zeigt die `recommendedJobs` mit `AiEnhancedJobListItem`. Score und Gründe werden visualisiert. Klick navigiert zu `JobDetailScreen`.
    -   **Chat (`Chatbox` -> `AiSearchNotifier.sendChatMessage`):**
        -   Nutzer gibt Frage ein.
        -   Ruft Backend-Funktion (oder direkt KI-API) mit Chat-Historie und neuer Frage auf.
        -   Aktualisiert `chatHistory` im State mit neuer Frage und Antwort.
-   **Fehlerbehandlung:** Fehler bei Standortbestimmung (explizite Prüfung und Meldung bei fehlender Berechtigung), Backend-Aufruf, KI-Analyse. Werden im State gespeichert und in der UI angezeigt.

---

## 5. Nutzerprofil

-   **Zweck:** Ermöglicht Nutzern die Verwaltung ihrer persönlichen Daten, Job-Präferenzen, KI-Einstellungen, Berufserfahrung, Ausbildung und des Lebenslaufs (Upload, Anzeige, Analyse-Initiierung).
-   **Technologien/Pakete:**
    -   Riverpod, `flutter_hooks`: State Management, UI-Logik.
    -   `intl`: Datumsformatierung/-auswahl.
    -   `file_picker`: Auswahl der lokalen CV-Datei.
    -   Firebase Storage: Aktueller Speicherort für das CV-Backup.
    -   Supabase Storage: Speicherort für die zur Analyse hochgeladene CV-Datei.
    -   Cloud Firestore: Persistente Speicherung des `UserProfile`-Objekts.
    -   Supabase Auth: Liefert die User ID.
    -   `url_launcher`: Zum Öffnen externer Links oder ggf. des heruntergeladenen CVs.
    -   `flutter_pdfview`: Zur Anzeige des hochgeladenen CVs direkt in der App (siehe [PDF-Anzeige](mdc:#15-pdf-anzeige)).
    -   `syncfusion_flutter_pdf`: Lokale Textextraktion aus PDFs (via `pdf_utils`).
    -   `package:path`: Manipulation von Dateipfaden.
-   **Haupt-Screens/Widgets:**
    -   `ProfileScreen`: Hauptansicht mit `ExpansionTile`s für Abschnitte, Buttons für Bearbeiten/Speichern/CV-Upload.
    -   `_EditableWorkExperienceListRefactored`, `_EditableEducationListRefactored`: Widgets zur Bearbeitung der Listen.
    -   `_EditableWorkExperienceItem`, `_EditableEducationItem`: Einzelne bearbeitbare Einträge mit `TextFormField` und `DatePickerDialog`.
-   **State Management & Provider:**
    -   `userProfileProvider`: Liefert den `AsyncValue<UserProfile?>`. Der `UserProfileNotifier` dahinter enthält die Logik zum Laden, Aktualisieren und Speichern des Profils in Firestore sowie die CV-Verarbeitungslogik.
    -   Lokaler State in `ProfileScreen` (via Hooks): `isEditing`, `isSaving`, `isUploadingCv`, `cvFileName`, `workExperienceStates`, `educationStates`, diverse `TextEditingController` (verwaltet in `_ProfileControllers`).
    -   `supabaseServiceProvider`: Für CV-Analyse-Aufruf.
    -   Provider für Firebase Auth/Storage (implizit oder über separate Provider).
-   **Datenmodell:**
    -   `UserProfile` (`user_profile.dart`): Enthält alle Profildaten, inkl. `cvUrl` (Link zum Backup in Firebase Storage?), `cvFileName`, `cvLastModified`, `cvAnalysisComplete`, `cvAutoFillEnabled`, sowie Listen für `WorkExperience` und `Education`. Mit `copyWith`, `toJson`, `fromJson` Methoden.
    -   `WorkExperience`, `Education`: Modelle für die Listeneinträge.
    -   `ExtractedCvData`: Modell für das Ergebnis der Supabase CV-Analyse-Funktion.
-   **Datenfluss & Logik:**
    -   **Anzeige:** `ProfileScreen` liest `userProfileProvider`. Zeigt Daten in `Text` oder `TextFormField` (je nach `isEditing`). `ExpansionTile`s gruppieren die Abschnitte.
    -   **Bearbeiten:** "Bearbeiten"-Button setzt `isEditing=true`. Controller werden mit aktuellen Daten initialisiert. Listen (`workExperienceStates` etc.) werden mit bearbeitbaren Daten gefüllt.
    -   **Speichern (`_saveProfile`):**
        -   Wird durch "Speichern"-Button ausgelöst. Setzt `isSaving=true`.
        -   Liest Daten aus allen `TextEditingController`n und den Listen-States (`workExperienceStates`, `educationStates`).
        -   Erstellt ein neues `UserProfile`-Objekt mit den geänderten Werten (`copyWith`).
        -   Ruft `ref.read(userProfileProvider.notifier).updateProfile(updatedProfile)` auf.
        -   **`UserProfileNotifier.updateProfile`:** Schreibt das `UserProfile`-Objekt (als Map via `toJson()`) in das Firestore-Dokument `users/{supabaseUserId}`. Behandelt Fehler.
        -   Setzt `isEditing=false`, `isSaving=false`.
    -   **Lebenslauf-Verarbeitung (`_pickAndUploadCv` in `ProfileScreen` -> `UserProfileNotifier.uploadAndAnalyzeCv`):**
        -   **Auswahl (`ProfileScreen`):** `FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['pdf'])` -> Gibt `PlatformFile` zurück.
        -   **Aufruf Notifier (`ProfileScreen`):** Ruft `ref.read(userProfileProvider.notifier).uploadAndAnalyzeCv(platformFile.path!, platformFile.name)` auf. Setzt `isUploadingCv=true`.
        -   **`UserProfileNotifier.uploadAndAnalyzeCv(localPath, fileName)`:**
            -   Holt aktuelle `supabaseUserId`.
            -   **1. Backup-Upload (Aktuell Firebase Storage):** Lädt die Datei vom `localPath` nach `Firebase Storage` unter `user_cvs/{supabaseUserId}/original_{fileName}` hoch. Holt die `downloadURL` nach Erfolg. *(Anmerkung: Konsistenz zu Supabase Storage wird geprüft).*
            -   Aktualisiert das lokale Profil *sofort* mit `cvUrl` (Firebase URL), `cvFileName`, `cvLastModified`. Ruft `updateProfile` auf, um dies in Firestore zu speichern.
            -   **2. Upload für Analyse (Supabase Storage):** Lädt die Datei ebenfalls nach Supabase Storage hoch (z.B. in den `cv-uploads` Bucket), um sie für die Edge Function verfügbar zu machen. *(Präzisierung der Logik)*.
            -   **3. Lokale Textextraktion:** Ruft `extractTextFromPdf(localPath)` auf (läuft in einem Isolate via `compute`).
            -   **4. KI-Analyse (Supabase Function):** Wenn Textextraktion erfolgreich: Ruft `ref.read(supabaseServiceProvider).processCvText(extractedText)` auf (oder alternativ eine Funktion, die den Supabase Storage Pfad erhält).
            -   **`SupabaseService.processCvText`:** Ruft Edge Function `process-cv-text` auf. Gibt `ExtractedCvData` zurück.
            -   **5. Bedingtes Auto-Fill:** Wenn Analyse erfolgreich (`ExtractedCvData` vorhanden) und `userProfile.cvAutoFillEnabled` true ist: Ruft `_conditionallyUpdateProfileFromCv(extractedData)` auf.
            -   **`_conditionallyUpdateProfileFromCv`:** Prüft, ob `skills`, `workExperience` oder `education` im *aktuellen* `UserProfile` leer sind. Wenn ja, füllt es die leeren Felder mit den Daten aus `extractedData`. Setzt `cvAnalysisComplete=true`.
            -   Wenn durch Auto-Fill Änderungen vorgenommen wurden: Ruft erneut `updateProfile` auf, um das *aktualisierte* Profil in Firestore zu speichern. Zeigt Erfolgs-Snackbar.
            -   Bei Fehlern (Upload, Extraktion, Analyse): Zeigt Fehler-Snackbar.
        -   **Ende (`ProfileScreen`):** Setzt `isUploadingCv=false`.
    -   **Listenverwaltung:** Die `_Editable...ListRefactored`-Widgets verwalten ihre eigenen Controller und rufen Callbacks (`onChanged`) im `ProfileScreen` auf, um die Haupt-State-Listen (`workExperienceStates` etc.) zu aktualisieren.
-   **Fehlerbehandlung:** Fehler beim Laden/Speichern des Profils (Firestore), Datei-Auswahl, CV-Uploads (Storage), Textextraktion, Supabase Function Aufruf. Werden über den `AsyncValue`-Status des Providers oder Snackbars kommuniziert.

---

## 6. KI-Features

Dieser Abschnitt fasst alle KI-basierten Funktionen der Anwendung zusammen.

### 6.1 KI-Anschreiben-Generator

-   **Zweck:** Ermöglicht die KI-gestützte Generierung personalisierter Anschreiben auf Basis von Stellenanzeigen, Lebenslauf und Nutzerpräferenzen.
-   **Technologien/Pakete:**
    -   `flutter_riverpod` / `hooks_riverpod`: State Management.
    -   `flutter_hooks`: Für lokale UI-Zustände und Hooks.
    -   `supabase_flutter`: Für Datenbankanbindung/Serverless Functions.
    -   `flutter_markdown`: Für Rendering von Markdown-Text.
    -   `share_plus`: Für Export/Teilen von generiertem Text.
    -   `flutter_quill` (oder ähnlich): Für Textbearbeitung mit Formatierungsoptionen.
-   **Haupt-Screens/Widgets:**
    -   `CoverLetterGeneratorScreen`: Hauptbildschirm mit Eingabeformular und Ergebnisanzeige.
    -   `JobDescriptionInput`: Spezifisches Widget für die Eingabe/Import der Stellenbeschreibung.
    -   `GenerationOptionsForm`: Formular für verschiedene Stile/Optionen der Generierung.
    -   `GeneratedCoverLetterDisplay`: Zeigt das Ergebnis an mit Bearbeitungsmöglichkeiten.
    -   `TemplateSelectionScreen`: Liste verfügbarer Vorlagen/Stile.
    -   `CoverLetterHistoryScreen`: Übersicht über bisher generierte Anschreiben.
-   **State Management & Provider:**
    -   `coverLetterGeneratorProvider` (`cover_letter_generator_provider.dart`): Stellt `CoverLetterGeneratorNotifier` bereit.
    -   `CoverLetterGeneratorState` (`freezed`): Enthält `jobDescription`, `selectedTemplate`, `generatedText`, `isGenerating`, `error`.
    -   `CoverLetterGeneratorNotifier`: Enthält die Kernlogik für den Anschreibengenerator.
    -   `userProfileProvider`: Liefert Nutzerinformationen für die Generierung.
    -   `cvDataProvider`: Stellt Lebenslaufdaten für kontextuelle Generierung bereit.
    -   `supabaseClientProvider`: Für API-Anfragen an die KI-Serverless Function.
-   **Datenfluss & Logik:**
    -   **Initialisierung (`CoverLetterGeneratorScreen.useEffect` -> `CoverLetterGeneratorNotifier.init`):**
        -   Lädt Profilinformationen und verfügbare Vorlagen.
        -   Prüft, ob ein vorheriges Generierungsergebnis existiert.
    -   **Texteingabe (`JobDescriptionInput` -> `CoverLetterGeneratorNotifier.setJobDescription`):**
        -   Erfasst die Stellenbeschreibung als Text.
        -   Alternativ: Import URL oder PDF-Upload mit Textextraktion.
        -   Validiert die Eingabe (Länge, Inhalt) und setzt im State.
    -   **Vorlagenauswahl (`TemplateSelectionScreen` -> `CoverLetterGeneratorNotifier.selectTemplate`):**
        -   Nutzer wählt aus verschiedenen Anschreibenstilen oder Vorlagen.
        -   Aktualisiert `selectedTemplate` im State.
    -   **Optionen konfigurieren (`GenerationOptionsForm` -> `CoverLetterGeneratorNotifier.setGenerationOptions`):**
        -   Ermöglicht die Anpassung von Parametern (Länge, Formalitätsgrad, etc.).
        -   Speichert Konfiguration für die Generierungsanfrage.
    -   **Generierung (`CoverLetterGeneratorNotifier.generateCoverLetter`):**
        -   Setzt `isGenerating=true`.
        -   Erstellt Prompt basierend auf Stellenbeschreibung, Lebenslauf, Vorlage und Optionen.
        -   Sendet Anfrage an Supabase Function mit LLM-Integration.
        -   Bei Erfolg: Verarbeitet LLM-Antwort, formatiert und setzt `generatedText`.
        -   Bei Fehler: Setzt `error` und `isGenerating=false`.
    -   **Nachbearbeitung (`GeneratedCoverLetterDisplay` -> `CoverLetterGeneratorNotifier.editGeneratedText`):**
        -   Ermöglicht manuelle Bearbeitung des generierten Textes.
        -   Nutzt Rich-Text-Editor für Formatierungsoptionen.
        -   Aktualisiert `generatedText` im State.
    -   **Export/Verwendung:**
        -   **Speichern (`CoverLetterGeneratorNotifier.saveCoverLetter`):** Speichert in der Datenbank.
        -   **Exportieren (`CoverLetterGeneratorNotifier.exportCoverLetter`):** Als Datei (PDF, DOCX, TXT).
        -   **Teilen (`CoverLetterGeneratorNotifier.shareCoverLetter`):** Via E-Mail, etc.
        -   **Verwendung (`CoverLetterGeneratorNotifier.useWithApplication`):** Anhängen an eine Bewerbung.
    -   **History-Management (`CoverLetterGeneratorNotifier.loadHistory`/`deleteHistoryItem`):**
        -   Verwaltet und zeigt frühere Generierungen des Nutzers.
        -   Ermöglicht das Wiederverwenden und Anpassen früherer Anschreiben.
-   **Fehlerbehandlung:** Behandelt API-Ausfälle, Timeouts, fehlerhafte oder unzureichende Eingaben mit entsprechendem Feedback an den Nutzer.

### 6.2 KI-Job-Feed / Job-Empfehlungen

-   **Zweck:** Bietet eine alternative, KI-gesteuerte Job-Entdeckung basierend auf dem Nutzerprofil statt auf expliziten Keywords. Zeigt relevante Jobs mit einem Match-Score und Begründungen an. Ermöglicht Chat-Interaktion für Rückfragen.
-   **Technologien/Pakete:** Riverpod, `http` (für Backend-Aufrufe), Cloud Functions/Supabase Functions, KI-API (z.B. DeepSeek für Matching/Ranking), `location`, `geocoding`, `permission_handler`, `circular_percent_indicator` (zur Visualisierung).
-   **Haupt-Screens/Widgets:**
    -   `AiSearchScreen`: Zeigt die Liste der empfohlenen Jobs, Lade-/Fehler-/Leerzustände, Button zum Starten/Aktualisieren, FAB für Chat.
    -   `AiEnhancedJobListItem`: Stellt einen Job dar, visualisiert KI-Score (z.B. `CircularPercentIndicator`) und zeigt Match-Gründe (z.B. `Chip`s).
    -   `Chatbox`: Überlagernder Dialog für textbasierte Interaktion mit einer Chat-KI.
-   **State Management & Provider:**
    -   `aiSearchProvider`: Stellt `AiSearchNotifier` bereit.
    -   `AiSearchState` (`freezed`): Enthält `recommendedJobs` (Liste von `AiJobRecommendationEntity` - enthält `JobEntity`, `matchScore`, `matchReasons`), `isLoading`, `error`, `chatHistory`.
    -   `AiSearchNotifier`: Verwaltet den State, löst die KI-Suche aus, handhabt Chat-Nachrichten.
    -   `userProfileProvider`: Wird gelesen, um das Profil an das Backend zu senden.
    -   Provider für Standortdienste (wie bei Jobsuche).
    -   `paymentServiceProvider`, `adServiceProvider`: Für Premium-Check.
-   **Datenfluss & Logik:**
    -   **Auslösen (`AiSearchScreen` -> `_startAiSearch`):** Button-Klick.
        -   **(Premium/Ad Check - Ähnlich wie Jobdetails, aktuell vermutlich deaktiviert/noch nicht implementiert):** Prüft `userProfile.isPremium`. Zeigt ggf. `showPremiumOrAdDialog`. Ruft bei Berechtigung `ref.read(aiSearchProvider.notifier).performAiJobSearch()` auf. *(Status der Implementierung/Aktivierung muss geprüft werden).*
    -   **`performAiJobSearch` (`AiSearchNotifier`):**
        -   Setzt `isLoading=true`.
        -   Holt `UserProfile`.
        -   Ermittelt aktuellen Standort (inkl. expliziter Berechtigungsprüfung via `permission_handler`).
        -   Ruft eine dedizierte Backend-Funktion auf (z.B. Supabase Function `findRelevantJobsAi`) und übergibt `UserProfile`-Daten und Standort.
        -   **Backend-Funktion (`findRelevantJobsAi` - vermuteter Ablauf):**
            -   Führt eine initiale Jobsuche in der Nähe durch (z.B. via BA-API).
            -   Iteriert durch die gefundenen Jobs.
            -   Für jeden Job: Ruft eine KI-API (z.B. DeepSeek) auf mit einem Prompt, der das Nutzerprofil und die Jobbeschreibung enthält, um eine Bewertung (Score) und kurze Begründungen für die Passung zu erhalten.
            -   Sammelt die Ergebnisse (Job + KI-Analyse) und gibt sie zurück.
        -   Notifier empfängt die `List<AiJobRecommendationEntity>`.
        -   Aktualisiert den State (`recommendedJobs`, `isLoading=false`). Bei Fehler: setzt `error`.
    -   **Anzeige (`AiSearchScreen`):** Zeigt die `recommendedJobs` mit `AiEnhancedJobListItem`. Score und Gründe werden visualisiert. Klick navigiert zu `JobDetailScreen`.
    -   **Chat (`Chatbox` -> `AiSearchNotifier.sendChatMessage`):**
        -   Nutzer gibt Frage ein.
        -   Ruft Backend-Funktion (oder direkt KI-API) mit Chat-Historie und neuer Frage auf.
        -   Aktualisiert `chatHistory` im State mit neuer Frage und Antwort.
-   **Fehlerbehandlung:** Fehler bei Standortbestimmung (explizite Prüfung und Meldung bei fehlender Berechtigung), Backend-Aufruf, KI-Analyse. Werden im State gespeichert und in der UI angezeigt.

### 6.3 Lebenslauf-Analyse

-   **Zweck:** Automatische Extraktion und Strukturierung von Informationen aus dem hochgeladenen Lebenslauf des Nutzers mit KI-Unterstützung.
-   **Technologien/Pakete:**
    -   Supabase Edge Functions: Backend-Logik für OCR und KI-Analyse.
    -   Google Cloud Vision API (oder ähnlich): Für OCR bei PDFs.
    -   KI-API (OpenAI, DeepSeek, etc.): Für Strukturierung und Kategorisierung des extrahierten Textes.
    -   `syncfusion_flutter_pdf`: Lokale Textextraktion aus PDFs.
    -   `file_picker`: Für CV-Dateiauswahl.
    -   Supabase Storage: Speicherort für hochgeladene CVs.
-   **Datenfluss & Logik:**
    -   **Auslösen:** In `ProfileScreen._pickAndUploadCv` oder während des Onboardings.
    -   **Prozess:**
        1. Nutzer wählt PDF-Datei aus (via `FilePicker`).
        2. Die Datei wird nach Supabase Storage hochgeladen.
        3. Edge Function `perform-ocr-on-pdf` wird aufgerufen und extrahiert Text aus der PDF.
        4. Edge Function `process-cv-text` analysiert den extrahierten Text mit KI und gibt strukturierte Daten zurück.
        5. Die App erhält ein `ExtractedCvData`-Objekt mit Feldern wie `skills`, `workExperience`, `education`.
        6. Wenn `cvAutoFillEnabled` aktiviert ist, werden leere Profilfelder automatisch mit den extrahierten Daten gefüllt.
-   **Fehlerbehandlung:** Fehler bei PDF-Format, OCR-Problemen, KI-Analyse, Netzwerkfehlern. Werden in der UI kommuniziert.

### 6.4 LLM-Integration (Generell)

-   **Zweck:** Bietet eine robuste, sichere und erweiterbare Infrastruktur für alle KI-/LLM-basierten Funktionen der App.
-   **Technologien/Pakete:**
    -   Serverless Functions: Supabase Functions oder Firebase Functions.
    -   LLM API-Integration: OpenAI API, Anthropic Claude, oder andere.
    -   `supabase_flutter` / `firebase_functions`: Für Client-Server-Kommunikation.
    -   `flutter_riverpod` / `hooks_riverpod`: Für State Management.
    -   `flutter_dotenv`: Für sichere Handhabung von API-Keys.
-   **Architektur:**
    -   **Frontend-Komponenten:**
        -   `LlmRequestBuilder`: Hilfsklasse zum Erstellen strukturierter Prompts.
        -   `LlmResponseHandler`: Parser für LLM-Antworten.
        -   `LlmLoadingIndicator`: Spezialisierter UI-Indikator für LLM-Calls.
        -   `LlmErrorDisplay`: Einheitliche Fehlerbehandlung für LLM-Anfragen.
    -   **Backend-Komponenten (Serverless):**
        -   `prompt-engineering/`: Template-System für strukturierte Prompts.
        -   `llm-service/`: Abstraktion der LLM-Provider.
        -   `cache-layer/`: Caching-System für wiederholte Anfragen.
        -   `context-builder/`: Aufbereitung von Kontext aus Nutzerprofil, Präferenzen etc.
    -   **Datenfluss:**
        -   Client erstellt eine LLM-Anfrage mit dem zu lösenden Problem.
        -   Request geht an die Serverless Function.
        -   Function baut den kompletten Prompt mit Kontext und Anweisungen.
        -   Anfrage an den LLM-Provider wird gesendet und überwacht.
        -   Antwort wird verarbeitet, validiert und an Client zurückgegeben.
        -   Client parst die strukturierte Antwort und aktualisiert UI.
-   **Sicherheit & Kostenmanagement:**
    -   **Authentifizierung:** Nur authentifizierte Nutzer können LLM-Funktionen verwenden.
    -   **Rate Limiting:** Begrenzung der Anzahl von Anfragen pro Nutzer/Zeitraum.
    -   **Prompt-Validierung:** Verhindert Prompt-Injection und schädliche Inhalte.
    -   **Token-Tracking:** Überwacht Tokenverbrauch pro Nutzer für Abrechnungszwecke.
    -   **Caching-Mechanismen:** Vermeidet wiederholte identische Anfragen.
-   **Verwendungsbereiche:**
    -   **Anschreibengenerierung:** Siehe Abschnitt 6.1.
    -   **CV-Optimierung:** Vorschläge zur Verbesserung des Lebenslaufs.
    -   **Job-Matching:** Analyse von Stellenanzeigen auf Übereinstimmung mit Profil.
    -   **Interview-Vorbereitung:** Generierung von möglichen Interviewfragen.
    -   **Arbeitszeugnis-Analyse:** Interpretation von Formulierungen in Zeugnissen.

---

## 7. Favoriten

-   **Zweck:** Ermöglicht angemeldeten Benutzern das Speichern und Verwalten ihrer bevorzugten Jobangebote persistent in Firestore.
-   **Technologien/Pakete:** Riverpod, Cloud Firestore, Supabase Auth (indirekt für User ID).
-   **Haupt-Screens/Widgets:**
    -   `FavoritesScreen`: Zeigt die Liste der gespeicherten Favoriten.
    -   `FavoriteJobListItem`: Stellt einen einzelnen Favoriten dar, ermöglicht das Entfernen durch Wischen (`Dismissible`).
-   **State Management & Provider:**
    -   `favoritesProvider` (`favorites_provider.dart`): Ein `StateNotifierProvider`, der `FavoritesNotifier` bereitstellt.
    -   `FavoritesNotifier`: Hält den Zustand `AsyncValue<List<JobEntity>>`.
    -   `authStateChangesProvider` (oder ähnlicher Provider, der User ID liefert): Abhängigkeit, um die User ID zu bekommen.
    -   `firestoreProvider`: Stellt `FirebaseFirestore`-Instanz bereit.
-   **Datenmodell:** Favoriten werden pro Benutzer in einem Firestore-Dokument gespeichert:
    -   Collection: `user_favorites`
    -   Document ID: `{supabaseUserId}`
    -   Feld: `favoriteJobs` (Typ: `Array`) - Enthält Maps, die jeweils eine `JobEntity` repräsentieren (`job.toJson()`).
-   **Datenfluss & Logik:**
    -   **Initialisierung/Überwachung (`FavoritesNotifier`):**
        -   Hört auf Änderungen des Auth-Status (z.B. via `ref.watch(authStateChangesProvider)`).
        -   Bei **Login** (gültige `userId`):
            -   Registriert einen Firestore Snapshot-Listener auf `firestore.collection('user_favorites').doc(userId)`.
            -   Bei jedem Snapshot-Event:
                -   Liest das `favoriteJobs`-Array aus den Dokument-Daten.
                -   Wenn vorhanden: Deserialisiert jede Map im Array zurück zu einem `JobEntity`-Objekt (`JobEntity.fromJson`).
                -   Aktualisiert den State: `state = AsyncData(deserializedJobs)`.
                -   Bei Lesefehler: `state = AsyncError(error, stackTrace)`.
        -   Bei **Logout** (`userId` ist `null`): Setzt `state = AsyncData([])` und entfernt den Firestore-Listener.
    -   **Hinzufügen (`JobDetailScreen` -> `FavoritesNotifier.addFavorite(JobEntity job)`):**
        -   Holt die `userId`.
        -   Konvertiert die `job` Entity in eine Map: `jobMap = job.toJson()`.
        -   Aktualisiert das Firestore-Dokument: `firestore.collection('user_favorites').doc(userId).set({'favoriteJobs': FieldValue.arrayUnion([jobMap])}, SetOptions(merge: true))`.
    -   **Entfernen (`FavoritesScreen` - Wischen -> `FavoritesNotifier.removeFavorite(String jobId)`):**
        -   Holt die `userId`.
        -   Findet das `JobEntity`-Objekt mit der `jobId` im aktuellen State.
        -   Aktualisiert das Firestore-Dokument: `firestore.collection('user_favorites').doc(userId).update({'favoriteJobs': FieldValue.arrayRemove([jobToRemove.toJson()])})`.
    -   **Status prüfen (`JobDetailScreen` -> `ref.watch(favoritesProvider).asData?.value.any(...)`):** Prüft synchron, ob eine Job-ID in der aktuell im State gehaltenen Liste vorhanden ist.
-   **Fehlerbehandlung:** Firestore Lese-/Schreibfehler werden vom Notifier gefangen und als `AsyncError` im State abgebildet. Die UI (`FavoritesScreen`) zeigt entsprechende Fehlermeldungen.

---

## 8. Einstellungen

-   **Zweck:** Bietet dem Benutzer Zugriff auf Kontoverwaltungsoptionen (Profil bearbeiten, Passwort ändern, Konto löschen), App-Informationen und externe Links (Hilfe, Datenschutz).
-   **Technologien/Pakete:** Riverpod, Supabase Auth, `url_launcher`, `flutter/material` (für `showAboutDialog`, `AlertDialog`).
-   **Haupt-Screens/Widgets:**
    -   `SettingsScreen`: Zeigt Optionen als `ListTile`s in Abschnitten.
    -   `AlertDialog`: Für Bestätigung der Kontolöschung.
    -   `showAboutDialog`: Standard-Dialog für App-Infos.
-   **State Management & Provider:**
    -   `authProvider`: Für Abmelden und Konto löschen.
    -   `userProfileProvider`: Wird indirekt benötigt, wenn zu `ProfileScreen` navigiert wird.
-   **Datenfluss & Logik:**
    -   **UI (`SettingsScreen`):** Eine `ListView` mit `ListTile`s für jede Option.
    -   **Profil bearbeiten:** `onTap` navigiert zu `/profile`.
    -   **Passwort ändern:** `onTap` navigiert zu `/change-password`.
    -   **Darstellung/Theme:** Aktuell keine Funktion implementiert.
    -   **Cache leeren:** Aktuell keine Funktion implementiert (nur Platzhalter).
    -   **Über diese App:** `onTap` ruft `showAboutDialog(...)` auf.
    -   **Hilfe & Feedback / Datenschutz:** `onTap` ruft `_launchUrl` mit `mailto:` bzw. `https:` URL auf.
    -   **Abmelden:** `onTap` ruft `ref.read(authProvider.notifier).signOut()` auf.
    -   **Konto löschen (`_deleteAccount`):**
        -   `onTap` ruft `_confirmAccountDeletion` auf.
        -   `_confirmAccountDeletion`: Zeigt einen `AlertDialog`. Bei Bestätigung wird `ref.read(authProvider.notifier).deleteAccount()` aufgerufen.
        -   `AuthStateNotifier.deleteAccount`: Behandelt den Löschvorgang inkl. Re-Authentifizierungs-Fehler (`requires-recent-login`), wie in Abschnitt 1 beschrieben. Zeigt Erfolg/Fehler via Snackbar.
-   **Fehlerbehandlung:** Fehler beim Löschen des Kontos (insb. `requires-recent-login`), Fehler beim Öffnen von URLs.

---

## 9. Onboarding

-   **Zweck:** Ein geführter Prozess für neue Benutzer zur erstmaligen Einrichtung ihres Profils nach der Registrierung.
-   **Technologien/Pakete:** Riverpod, `flutter_hooks`, `file_picker`, `smooth_page_indicator`.
-   **Haupt-Screens/Widgets:**
    -   `OnboardingScreen`: Enthält einen `PageView` zur Darstellung der einzelnen Schritte und einen `SmoothPageIndicator`.
    -   Einzelne Seiten-Widgets (z.B. `_WelcomePage`, `_PersonalInfoPage`, `_SkillsPage`, `_ExperiencePage`, `_EducationPage`, `_PreferencesPage`, `_CvUploadPage`, `_CompletionPage`).
-   **State Management & Provider:**
    -   `onboardingStateProvider` (`onboarding_state_provider.dart`): Ein `StateNotifierProvider`, der `OnboardingStateNotifier` bereitstellt.
    -   `OnboardingStateNotifier`: Hält den *temporären* `OnboardingState` (ein Datenmodell, das alle während des Onboardings gesammelten Infos enthält: Name, Telefon, Skills-Liste, Erfahrungs-Liste, Ausbildungs-Liste, Präferenzen, lokaler CV-Dateipfad). Bietet Methoden (`updateName`, `addSkill`, `updateWorkExperience`, `setCvPath`, etc.), um diesen temporären State zu ändern.
    -   `onboardingProvider` (`onboarding_provider.dart`): Ein `StateNotifierProvider`, der `OnboardingCompletionNotifier` bereitstellt. Verwaltet den *Abschluss*-Status des Onboardings (boolescher Wert, in SharedPreferences gespeichert) und die Logik zum Abschließen.
    -   `userProfileProvider`: Wird benötigt, um das finale Profil zu erstellen/speichern.
    -   `sharedPreferencesProvider`: Zum Speichern des Abschluss-Status.
-   **Datenfluss & Logik:**
    -   **Struktur (`OnboardingScreen`):** Nutzt `PageView` und `PageController`. Punkte (`SmoothPageIndicator`) zeigen Fortschritt an. Buttons "Weiter" und "Zurück" wechseln die Seite (`pageController.nextPage`/`previousPage`).
    -   **Datensammlung:** Jede Seite (z.B. `_SkillsPage`) liest den aktuellen `onboardingStateProvider`-State und ruft bei Änderungen die entsprechenden `update...`-Methoden des `OnboardingStateNotifier` auf (z.B. `ref.read(onboardingStateProvider.notifier).addSkill(newSkill)`). Die Daten werden nur im *temporären* `OnboardingState` gehalten.
    -   **Seiten-Implementierung:** Verwenden Standard-Widgets wie `TextField`, `Chip`, `ListView.builder`, `DatePickerDialog`. Erfahrung/Ausbildung nutzen ähnliche Logik wie im `ProfileScreen` zur dynamischen Listenverwaltung.
    -   **CV-Upload (`_CvUploadPage`):** Nutzt `FilePicker`, um eine PDF auszuwählen. Speichert nur den *lokalen Dateipfad* im `OnboardingState` via `setCvPath`. Der eigentliche Upload/Analyse geschieht erst beim Abschluss.
    -   **Abschluss (`_CompletionPage` - "Fertig"-Button):**
        -   Löst `ref.read(onboardingProvider.notifier).completeOnboarding(ref.read(onboardingStateProvider))` aus.
        -   **`OnboardingCompletionNotifier.completeOnboarding(OnboardingState onboardingData)`:**
            -   Setzt Ladezustand.
            -   **1. Profil erstellen/aktualisieren:** Erstellt ein `UserProfile`-Objekt aus den `onboardingData`. Ruft `ref.read(userProfileProvider.notifier).updateProfile(newUserProfile)` auf, um es in Firestore zu speichern.
            -   **2. CV verarbeiten (wenn Pfad vorhanden):** Ruft `ref.read(userProfileProvider.notifier).uploadAndAnalyzeCv(onboardingData.cvPath!, onboardingData.cvFileName!)` auf (siehe Logik in Abschnitt 5).
            -   **3. Status speichern:** Setzt das Onboarding-Flag in `SharedPreferences` auf `true`. Aktualisiert den eigenen State (`state = true`).
            -   Bei Erfolg: Navigiert zur Haupt-App (`/`).
            -   Bei Fehler: Setzt Fehlerzustand, zeigt Snackbar.
-   **Fehlerbehandlung:** Fehler beim Speichern des Profils, CV-Upload/-Analyse.

---

## 10. Splash Screen

-   **Zweck:** Wird einmalig beim Kaltstart der App angezeigt, um notwendige Initialisierungen abzuwarten und basierend auf dem Onboarding-Status zur korrekten Einstiegsseite zu navigieren.
-   **Technologien/Pakete:** Riverpod, `flutter_hooks` (für Animation und `useEffect`).
-   **Haupt-Screens/Widgets:** `SplashScreen`.
-   **State Management & Provider:**
    -   `sharedPreferencesProvider`: Stellt `SharedPreferences` bereit.
    -   `onboardingProvider`: Liefert den booleschen Onboarding-Abschlussstatus.
    -   `splashInitProvider` (`FutureProvider`): Hängt vom `sharedPreferencesProvider` ab. Wartet auf SharedPreferences und fügt eine kurze künstliche Verzögerung hinzu (damit die Animation sichtbar ist).
-   **Datenfluss & Logik:**
    -   **UI (`SplashScreen`):** Zeigt Logo/Titel. Eine `AnimationController` (initialisiert mit `useAnimationController`) steuert kombinierte Animationen der Elemente (z.B. Fade-In, Skalierung, Verschiebung von unten).
    -   **Navigation (`SplashScreen` - `useEffect` Hook):**
        -   Der Hook hängt von `splashInitProvider.future` und `onboardingProvider` ab.
        -   Er wird ausgelöst, wenn der `splashInitProvider` abgeschlossen ist.
        -   Innerhalb des `useEffect`:
            -   Liest den aktuellen Wert von `onboardingProvider` (den booleschen Status).
            -   Wenn `onboardingCompleted == true`: Navigiert zu `/` (AppShell) mit `context.go('/')`.
            -   Wenn `onboardingCompleted == false`: Navigiert zu `/onboarding` mit `context.go('/onboarding')`.
-   **Fehlerbehandlung:** Fehler beim Laden von SharedPreferences würden im `splashInitProvider` als `AsyncError` resultieren, was im `useEffect` abgefangen werden könnte (aktuell nicht explizit implementiert, würde aber verhindern, dass die Navigation ausgelöst wird).

---

## 11. In-App-Käufe / Premium

-   **Zweck:** Steuerung des Zugriffs auf Premium-Funktionen. Bietet dem Nutzer die Möglichkeit, ein Premium-Abonnement zu erwerben oder (für bestimmte Funktionen) eine Funktion durch das Ansehen einer Werbeanzeige einmalig freizuschalten.
-   **Technologien/Pakete:**
    -   Riverpod: State Management, Bereitstellung der Services.
    -   `in_app_purchase`: (Vermutlich genutzt im `PaymentService`) Für die Abwicklung von Käufen über Google Play / App Store.
    -   `google_mobile_ads`: (Genutzt im `AdService`) Für die Anzeige von Rewarded Ads.
-   **Haupt-Screens/Widgets:**
    -   Dialoge aus `lib/src/presentation/common/premium_dialogs.dart`:
        -   `showPremiumRequiredDialog`: Wenn eine Funktion *nur* Premium ist.
        -   `showPremiumOrAdDialog`: Wenn Premium oder Werbung möglich ist.
-   **State Management & Provider:**
    -   `paymentServiceProvider`: Stellt den `PaymentService` bereit.
    -   `adServiceProvider`: Stellt den `AdService` bereit.
    -   `userProfileProvider`: Liefert den `isPremium`-Status aus dem `UserProfile`.
    -   `(Optional) AppDialogs` (Provider/Klasse): Kann für generische Lade-/Fehlerdialoge während Kauf/Ad-Anzeige verwendet werden (siehe [Generische UI-Komponenten](mdc:#16-generische-ui-komponenten)).
-   **Datenfluss & Logik:**
    -   **Zugriffsprüfung:** An Stellen, die Premium erfordern (z.B. `JobDetailScreen._triggerApplicationGeneration`, `AiSearchScreen._startAiSearch`):
        -   `ref.watch(userProfileProvider)` wird geprüft.
        -   Wenn `userProfile.isPremium == true`: Funktion wird direkt ausgeführt.
        -   Wenn `userProfile.isPremium == false`: Entsprechender Dialog wird aufgerufen. **(Hinweis: Diese Prüfung ist in `JobDetailScreen` aktuell auskommentiert!)**
    -   **Dialog: Nur Premium (`showPremiumRequiredDialog`):**
        -   Zeigt Info-Text.
        -   Button "Später": Schließt Dialog.
        -   Button "Premium holen": Ruft `ref.read(paymentServiceProvider).buyPremiumSubscription()` auf. Zeigt währenddessen ggf. Ladedialog. Behandelt Erfolg/Fehler des Kaufs (aktualisiert ggf. `userProfileProvider` oder zeigt Meldung).
    -   **Dialog: Premium oder Werbung (`showPremiumOrAdDialog`):**
        -   Zeigt Info-Text.
        -   Button "Abbrechen": Schließt Dialog.
        -   Button "Werbung ansehen": Ruft `ref.read(adServiceProvider).loadAndShowRewardedAd(...)` auf. Übergibt einen `onAdWatchedCallback`.
            -   `AdService` zeigt die Werbung.
            -   Wenn die Werbung erfolgreich geschaut wurde (`onUserEarnedReward` im `AdService` wird ausgelöst): Der `onAdWatchedCallback` wird aufgerufen, der dann die ursprünglich blockierte Aktion einmalig ausführt (z.B. `_triggerApplicationGeneration()`).
        -   Button "Premium holen": Ruft `ref.read(paymentServiceProvider).buyPremiumSubscription()` auf (siehe oben).
    -   **Kaufabwicklung (`PaymentService` - vermuteter interner Ablauf):**
        -   Interagiert mit `package:in_app_purchase`.
        -   Lädt Produktinformationen (Abo-Details) vom Store.
        -   Initiert den Kauf (`InAppPurchase.instance.buyNonConsumable` oder `buyConsumable` - abhängig vom Produkt).
        -   Hört auf Kauf-Updates (`InAppPurchase.instance.purchaseStream`).
        -   Verifiziert Käufe (client- oder serverseitig).
        -   Aktualisiert den Premium-Status des Benutzers (z.B. direkt im `UserProfile` in Firestore oder über einen Backend-Call).
-   **Fehlerbehandlung:** Fehler beim Laden von Produkten, Kaufabbrüche durch Nutzer, Verifizierungsfehler, Werbeanzeigen-Lade/Anzeigefehler. Werden über Dialoge oder Snackbars kommuniziert.

---

## 12. Werbung (Google Mobile Ads)

-   **Zweck:** Integration und Anzeige von Google Mobile Ads, insbesondere Rewarded Video Ads, um Nutzern eine alternative Möglichkeit zur Freischaltung von Premium-Funktionen zu bieten.
-   **Technologien/Pakete:** Riverpod, `google_mobile_ads`.
-   **Haupt-Screens/Widgets:** Keine direkten UI-Widgets, der Service wird von anderen Teilen (Dialogen) genutzt.
-   **State Management & Provider:**
    -   `adServiceProvider`: Stellt die `AdService`-Instanz bereit.
-   **Datenfluss & Logik (`AdService`):**
    -   **Initialisierung (`static AdService.initialize()`):** Muss einmalig beim App-Start (z.B. in `main.dart`) aufgerufen werden. Ruft `MobileAds.instance.initialize()` auf.
    -   **Rewarded Ad laden & anzeigen (`loadAndShowRewardedAd`):**
        -   Parameter: `onAdLoadedCallback`, `onAdFailedToLoadCallback`, `onAdFailedToShowCallback`, `onUserEarnedRewardCallback`.
        -   Ruft `RewardedAd.load(...)` auf mit der entsprechenden Ad Unit ID (Test- oder Produktions-ID).
        -   **Callbacks:**
            -   `onAdLoaded`: Wird aufgerufen, wenn die Anzeige geladen ist. Speichert die geladene `RewardedAd`-Instanz. Ruft den übergebenen `onAdLoadedCallback` auf. Setzt `FullScreenContentCallback` für die geladene Ad (für `onAdShowedFullScreenContent`, `onAdDismissedFullScreenContent`, `onAdFailedToShowFullScreenContent`). Zeigt die Ad sofort mit `ad.show(onUserEarnedReward: ...)`. Übergibt den `onUserEarnedRewardCallback` an `ad.show`.
            -   `onAdFailedToLoad`: Wird aufgerufen, wenn das Laden fehlschlägt. Ruft den `onAdFailedToLoadCallback` auf.
            -   `onAdFailedToShowFullScreenContent` (im FullScreenContentCallback): Wird aufgerufen, wenn das Anzeigen fehlschlägt. Ruft `onAdFailedToShowCallback` auf. Entsorgt die Ad (`ad.dispose()`).
            -   `onAdDismissedFullScreenContent` (im FullScreenContentCallback): Wird aufgerufen, wenn der Nutzer die Ad schließt. Entsorgt die Ad (`ad.dispose()`).
        -   Der `onUserEarnedRewardCallback` (übergeben an `ad.show`) wird vom SDK aufgerufen, wenn der Nutzer die Belohnung verdient hat. Hier wird typischerweise die Logik ausgeführt, die die Aktion freischaltet.
-   **Fehlerbehandlung:** Ladefehler und Anzeigefehler werden über die Callbacks an den Aufrufer zurückgemeldet, der dann entsprechende Meldungen anzeigen kann.

---

## 13. Versionskontrolle & Backup (GitHub)

-   **Zweck:** Nachverfolgung von Code-Änderungen und Sicherung des Projektstands auf einer externen Plattform.
-   **Technologien:** Git (lokales Versionskontrollsystem), GitHub (Remote-Hosting-Plattform).
-   **Einrichtung:**
    -   Lokales Git-Repository initialisiert (`git init`).
    -   Remote-Repository auf GitHub erstellt (z.B. `github.com/MakerNr1/ki-test-backup`).
    -   Lokales Repository mit Remote verbunden (`git remote add origin ...`).
    -   `.gitignore`-Datei konfiguriert, um unnötige oder sensible Dateien (Build-Artefakte, Konfigurationsdateien mit Schlüsseln wie `.env`, IDE-spezifische Ordner) vom Tracking auszuschließen.
-   **Workflow:**
    -   **Änderungen entwickeln:** Code wird lokal bearbeitet.
    -   **Staging:** Geänderte Dateien werden für den Commit vorbereitet (`git add <datei>` oder `git add .`).
    -   **Commit:** Änderungen werden lokal mit einer beschreibenden Nachricht gespeichert (`git commit -m "Beschreibung der Änderung"`).
    -   **Push:** Lokale Commits werden zum Remote-Repository auf GitHub hochgeladen (`git push origin main` oder `git push`).
    -   **(Optional) Pull:** Änderungen von anderen Entwicklern oder aus anderen Branches werden heruntergeladen (`git pull`).
    -   **(Optional) Branching:** Für größere Features oder Bugfixes werden separate Branches erstellt (`git checkout -b feature/neues-feature`), um die Hauptentwicklungslinie (`main`) stabil zu halten. Nach Fertigstellung werden Branches zurück in `main` gemerged (`git checkout main`, `git merge feature/neues-feature`).

---

## 14. Backend-Übergang: Firebase zu Supabase

-   **Motivation:** Wechsel von Firebase zu Supabase als primäres Backend-as-a-Service (BaaS) für neue Funktionen, während Teile von Firebase (insb. Firestore) vorerst beibehalten werden.
-   **Status der Migration:**
    -   **Authentifizierung:** **Vollständig auf Supabase Auth umgestellt.** Nutzer werden über Supabase registriert und angemeldet. Die App reagiert auf den Supabase Auth State.
    -   **Datenbank:** **Cloud Firestore wird weiterhin genutzt.** Die Supabase User ID (`supabase.auth.currentUser.id`) dient als Schlüssel für Benutzerdokumente (z.B. in der `users`-Collection). *Stellen, die noch die Firebase User ID verwenden, müssen angepasst werden.*
    -   **Storage:** **Gemischte Nutzung, Tendenz zu Supabase:**
        -   *CV-Backup:* Aktuell noch Firebase Storage (`user_cvs/{firebaseUserId}/...`). **TODO:** Migration zu Supabase Storage mit Supabase User ID prüfen/durchführen für Konsistenz.
        -   *CV für Analyse/OCR:* Supabase Storage (z.B. Bucket `cv-uploads`, Pfad `{supabaseUserId}/{fileName}`). Wird von Edge Functions genutzt.
    -   **Functions:**
        -   *Firebase Cloud Functions:* Bestehende Funktionen können weiterlaufen, solange sie keine Firebase Auth-spezifische Logik benötigen.
        -   *Supabase Edge Functions:* Neue Backend-Logik wird hier implementiert (TypeScript/Deno):
            -   `generate-cover-letter`: Nimmt Profil-/Jobdaten, ruft KI-API auf, gibt Anschreiben/E-Mail zurück.
            -   `process-cv-text`: Nimmt CV-Text, ruft KI-API auf, gibt strukturierte Daten (`ExtractedCvData`) zurück.
            -   `perform-ocr-on-pdf`: Nimmt Storage-Pfad, lädt PDF, ruft Google Cloud Vision API auf, gibt extrahierten Text zurück.
-   **Service Layer (`SupabaseService`):** Dient als zentrale Schnittstelle in der Flutter-App für alle Supabase-Interaktionen (Auth-Methoden-Wrapper, Storage-Uploads/-Downloads, Function Calls). Kapselt auch die Fallback-Logik zu Firebase Functions (falls implementiert). Wird über `supabaseServiceProvider` bereitgestellt.
-   **Herausforderungen/TODOs:**
    -   Sicherstellen, dass überall die Supabase User ID konsistent verwendet wird, insbesondere für Firestore-Pfade und ggf. Storage-Pfade.
    -   Entscheiden und Umsetzen der CV-Backup-Migration zu Supabase Storage.
    -   Anpassung aller Stellen, die zuvor Firebase Auth direkt genutzt haben (z.B. ID-Abfragen).

---

## 15. PDF-Anzeige

-   **Zweck:** Anzeige von PDF-Dokumenten, insbesondere des vom Benutzer hochgeladenen Lebenslaufs, direkt innerhalb der Anwendung.
-   **Technologien/Pakete:** `flutter_pdfview`.
-   **Haupt-Screens/Widgets:** Ein dedizierter `PdfViewerScreen` oder Integration des `PDFView`-Widgets in andere Screens (z.B. `ProfileScreen` nach Klick auf den CV-Dateinamen).
-   **State Management & Provider:** Hängt davon ab, wo die Anzeige integriert ist. Benötigt Zugriff auf den Dateipfad (lokal oder URL) des anzuzeigenden PDFs, z.B. über `userProfileProvider` (`cvUrl`).
-   **Datenfluss & Logik:**
    -   Ein Button oder Link (z.B. im `ProfileScreen` neben dem `cvFileName`) löst die Navigation zum PDF-Viewer aus oder zeigt das Widget direkt an.
    -   Der Pfad zur PDF-Datei (entweder die lokale temporäre Datei nach Download oder die `cvUrl` vom Storage) wird an das `PDFView`-Widget übergeben.
    -   Das `PDFView`-Widget lädt und rendert die PDF-Seiten. Es bietet Standardmäßig Funktionen wie Zoomen und Scrollen.
-   **Fehlerbehandlung:** Fehler beim Laden der PDF-Datei (ungültiger Pfad, Netzwerkfehler bei URL, beschädigte Datei). Das `PDFView`-Widget bietet Callbacks (`onError`, `onPageError`) zur Fehlerbehandlung.

---

## 16. Generische UI-Komponenten

-   **Zweck:** Bereitstellung wiederverwendbarer UI-Elemente für konsistentes Erscheinungsbild und Verhalten, z.B. für Ladeanzeigen oder Fehlermeldungen.
-   **Beispiele (Vermutet):**
    -   `AppDialogs`: Eine Utility-Klasse oder ein Provider, der Methoden zum Anzeigen standardisierter Dialoge anbietet (z.B. `showLoadingDialog`, `showErrorDialog`, `showConfirmationDialog`). Wird möglicherweise in Verbindung mit asynchronen Operationen wie API-Aufrufen, Käufen oder Ad-Anzeigen verwendet.
    -   `PrimaryButton`: Ein wiederverwendbarer Button mit dem Standard-Styling der App.
    -   `LoadingIndicator`: Eine zentrale Ladeanzeige (z.B. `CircularProgressIndicator` mit App-Farben).
-   **Technologien/Pakete:** `flutter/material`.
-   **Ort:** Vermutlich in `lib/src/presentation/common/widgets/` oder `lib/src/presentation/common/dialogs/`.
-   **Nutzung:** Diese Komponenten werden an verschiedenen Stellen in den Screens importiert und verwendet, um redundanten Code zu vermeiden und Konsistenz sicherzustellen.

---
</rewritten_file>


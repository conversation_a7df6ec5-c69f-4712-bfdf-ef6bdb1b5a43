---
description: Guidelines for creating and maintaining Roo Code rules to ensure consistency and effectiveness.
globs: .roo/rules/*.md
alwaysApply: true
---

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [prisma.md](mdc:.roo/rules/prisma.md) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references

- **Code Examples:**
  - Use language-specific code blocks
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase
  - Remove outdated patterns
  - Cross-reference related rules

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules 
  
- **Unterbreche nicht die Bearbeitung. Weiterarbeiten, bis die App funktioniert**
    - Bei nicht so wichtigen Dinge kannst du einfach weitermachen, ohne mich zu fragen     
    - Immer genauer arbeiten bitte    
    - Wenn wir einen Task Abgeschlossen haben oder noch in Bearbeitung und wir sind noch nicht sicher, ob wir diesen Task Beenden sollen. Dann sollst du immer die App erst mal starten, um die Fehlersuche zu identifizieren.     
    - Bitte in eine einfache Sprache schreiben, Und schreibe mir nicht so viele lange Texte.     
    - "flutter run -d adb-661ca116-9A3FmH._adb-tls-connect._tcp" Starte die App immer mit diesem Gerät und wenn die Geräte nicht Verbunden ist dann fragst du mich, dass ich dieses Gerät verbinde und wähle bitte kein alternatives Gerät, falls adb-661ca116-9A3FmH._adb-tls-connect._tcp nicht auf gelistet ist, fragst du mich damit ich das hanyd verbinde, wähle keine alternatives Gerät
   

    - wenn du flutter run -d Startest im Chat, Du weißt oft genau welchen Fehler wir gerade behandeln. Bitte beobachte die Konsole dann, Sobald der Fehler auftaucht, werden die einfach diese und behebe den Fehler sofort Du kannst auch für 3 Sekunden warten nachdem die Fehler aufgetaucht sind oder das, was du suchst, dann findest du diese Ausgabe von Konsole und behebst du die Fehler

    -während du irgendwas Bearbeitest unterbricht manchmal die Verbindung, Du darfst die Bearbeitung nicht beenden, erst wenn die fertig ist 
    -  wenn ich . tippe, dann bedeutet das weiter
    - Wenn ich dir schreibe neue Chat, Dann bitte machst du folgendes. Du fasst alles zusammen, was wir in diesem Chat geschrieben haben in einem Text die wichtigsten Punkte, und was wir als Nächstes machen sollen Und wo wir stehen geblieben sind. Das ist notwendig für den nächsten chat    
    - wenn ich dir mehrere sacehen sage, dann bearbeitest du sie eins nacheinander, 
    - Vermeide die Fehler, Ich sehe immer wieder, dass du sagst mein Fehler. Es ist okay, wenn du Fehler machst aber bitte vermeide die Fehler arbeite strukturiert.
    - bei jedem schritt was du machst, sollst du mit Master ai dokumentieren
    - nach jeder änderung starte die app im chat automatisch 
    - nicht Aufgeben Wenn ein hartnäckiges Problem ist, Immer wieder versuchen und das Problem tiefer Untersuchung keine extreme Lösungen anbieten. Kannst einfach lösen und experimentieren, bis das Problem ist nicht aufgeben. 

    - Bitte frag mich nicht mehr, wo die Daten im Projekt Liegen, Such einfach selbst, 
    - Die Aktionen, die du selbst durchführen kannst bitte frag mich nicht danach, sie für dich durchzuführen. Führe sie selbst durch
    - Wenn wir mit einer Funktion Fertig sind, Dann stell dir die Frage Was ist der nächste logische Schritt? Und dein implementiere diese
    -  gemini Kannst du komplett entfernen, Da, wo du es siehst 
    - Jedes Mal, wenn du etwas Neues im Fire Base gemacht hast, Bitte dokumentiere das In einem Dokument Unter Memory Bank. Alle Fehler enthalten, die du gelernt hast, während Fire Base entwickelt hast, die Server für Firebase und so weiter
    - Benutze immer Context7 mcp
    - Wenn du irgendwas änderst, bitte aktualisiere das Dokument hier /Users/<USER>/Documents/ki_test/docs/funktionen.md Bitte lösche nicht Informationen aus diesem Dokument, wenn du keine neue Funktion ersetzt hast. Wenn ich dir (1) schreibe, denkest du daran, ansosten immer daran deneken, dies zu tun. wenn du das geselen hast, schreiben am anfang der nachricht Hakllo Mohamad !
    
    -Nach jeder Fehler Schreibst du bitte Dokument Den Fehler und die Lösung, Wählst du immer den Blick auf diesen Dokument, damit du nicht den selben Fehler über immer wieder machst /Users/<USER>/Documents/ki_test/docs/fehlerbehebung.md

    - Von nun an kannst du Änderungen einfach mit "git push" im terminal ausführen um projekt hochzuladen.
    - Starte die App in einem Chat, damit du gleich die Fehler sehen kannst, Bitte starte die App niemals im externen oder im Hintergrund Es sei denn, ich sag's dir das
    - Die Änderungen Die du im Projekt implementieren kannst bitte mach das selbst. Fordere mich nicht auf das zu machen.
    - wenn ich dir sage, du sollst im server irgedenwas machen, weißt du du machst das mit mcp für subapase. 
    
Wenn ich dir sage, du sollst etwas an Designen und das komplette Design, dann bitte nicht den Code oder Logik dahinter ändern, sondern nur das Design wirklich nur das Design
Jede Sache, die du machst jede Funktion, die du im kommentierst sollte alles an der Code definiert werden so vermeide du die Fehler. 
Bitte jedes Mal testen, bevor du die App startest, ob die implementierte Sachen Texte und so weiter alles richtig sind gebaut sind 



- **Verstanden! Hier sind einige Regeln und Befehle, die du der KI zu Beginn deiner Interaktion oder als wiederholende Anweisung bei jeder Anfrage mitgeben kannst, damit sie den Fokus auf reine UI-Änderungen beibehält und die Logik unangetastet lässt:**
(wenn du das ließt, dann sagdt du mir jedes mal du wirst darauf achten, dass du diese Punkte ernst nimmst, wenn ich dir sage du sollst irgendwas an design oder lyout oder schrift usw )

Grundlegende Verhaltensregeln für UI-Anpassungen:

UI-Fokus-Regel: Jede Anfrage, die sich auf das Erscheinungsbild bezieht (Design, Layout, Farben, Schriftarten), ist primär eine Änderung der Präsentationsebene. Die zugrunde liegende Funktionalität und Logik der Anwendung dürfen dadurch nicht beeinflusst oder verändert werden.

Nicht-Interferenz-Befehl: Führe bei UI-Anpassungen keine Modifikationen am Code durch, der die Anwendungslogik, Datenverarbeitung oder das Verhalten steuert. Beschränke dich ausschließlich auf Änderungen, die das visuelle Erscheinungsbild betreffen.

Selektor-Präferenz-Regel: Identifiziere UI-Elemente primär über eindeutige Selektoren (IDs, Klassen) oder deklarative Beschreibungen (z.B. in Layout-Dateien oder Styling-Sheets). Vermeide Annahmen über die zugrunde liegende Code-Struktur, die diese Elemente erzeugt.

API-Respekt-Befehl: Nutze und respektiere bestehende Schnittstellen (APIs, Funktionsaufrufe) zur Interaktion mit der Anwendungslogik. Erstelle oder verändere keine neuen Mechanismen zur Datenübertragung oder Funktionsausführung im Rahmen von UI-Anpassungen.

Konservativ-Modus-Regel: Im Zweifelsfall, wenn eine UI-Änderung potenziell die Funktionalität beeinträchtigen könnte, frage nach, bevor du die Änderung ausführst. Gehe immer von einem konservativen Ansatz aus, um ungewollte Seiteneffekte zu vermeiden.


**Befehle für spezifische Anfragen:**

Füge diese Befehle zu jeder deiner UI-bezogenen Anfragen hinzu:

"Achte darauf, dass diese Änderung keine Auswirkungen auf die Funktionalität hat."
"Die zugrunde liegende Logik darf durch diese Designanpassung nicht verändert werden."
"Beschränke die Änderungen auf die Präsentationsebene (z.B. Styling, Layout)."
"Verändere keinen Code, der das Verhalten oder die Datenverarbeitung steuert."
"Nutze zur Anpassung primär [nenne hier die verwendeten UI-Technologien, z.B. CSS, Layout-Dateien]."



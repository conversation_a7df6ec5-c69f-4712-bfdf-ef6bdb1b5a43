# CV-Anzeigeproblem in zusätzlichen Dokumenten behoben

## Problem
Das CV (Lebenslauf) wurde nicht in der "Zusätzliche Dokumente" Sektion der Profil-Bearbeitung angezeigt, obwohl es korrekt hochgeladen und verfügbar war.

## Ursache
Die Logik in `AdditionalDocumentsSection` prüfte sowohl auf `cvDownloadUrl` als auch auf `cvFileName`:
```dart
if (cvFileName != null && cvDownloadUrl != null) {
  // CV anzeigen
}
```

Das Problem war, dass `cvFileName` oft `null` war, obwohl `cvDownloadUrl` vorhanden war.

## Lösung
Die Bedingung wurde geändert, um nur auf `cvDownloadUrl` zu prüfen und einen Fallback-Namen zu verwenden:

### Datei: `lib/src/presentation/profile/widgets/additional_documents_section.dart`

**Vorher:**
```dart
// CV als erstes Element hinzufügen (falls vorhanden)
if (cvFileName != null && cvDownloadUrl != null) {
  items.add(_CvDocumentItem(
    fileName: cvFileName!,
    downloadUrl: cvDownloadUrl!,
  ));
}
```

**Nachher:**
```dart
// CV als erstes Element hinzufügen (falls vorhanden)
if (cvDownloadUrl != null && cvDownloadUrl!.isNotEmpty) {
  final displayName = cvFileName?.isNotEmpty == true ? cvFileName! : 'Lebenslauf.pdf';
  items.add(_CvDocumentItem(
    fileName: displayName,
    downloadUrl: cvDownloadUrl!,
  ));
}
```

## Zusätzliche Debug-Ausgabe
Temporäre Debug-Ausgabe hinzugefügt um das Problem zu identifizieren:
```dart
// Debug-Ausgabe
print('DEBUG AdditionalDocuments: hasCv=$hasCv');
print('DEBUG AdditionalDocuments: cvDownloadUrl=${userProfile.cvDownloadUrl}');
print('DEBUG AdditionalDocuments: cvFileName=${userProfile.cvFileName}');
print('DEBUG AdditionalDocuments: cvFilePath=${userProfile.cvFilePath}');
print('DEBUG AdditionalDocuments: hasDocuments=$hasDocuments, documentsCount=${documents.length}');
```

## Testergebnis
Nach der Änderung wird das CV korrekt angezeigt:
```
DEBUG AdditionalDocuments: hasCv=true
DEBUG AdditionalDocuments: cvDownloadUrl=https://vpttdxibvjrfjzbtktqg.supabase.co/storage/v1/object/public/cv-backups/281d248c-a36a-450f-9b28-d9f38c3b02fb_user/lebenslauf.pdf
DEBUG AdditionalDocuments: cvFileName=null
DEBUG AdditionalDocuments: cvFilePath=/data/user/0/com.einsteinai.app/cache/281d248c-a36a-450f-9b28-d9f38c3b02fb_1751198173228.pdf.pdf
DEBUG AdditionalDocuments: hasDocuments=true, documentsCount=1
```

## Funktionalität
- Das CV wird jetzt korrekt in der zusätzlichen Dokumente Sektion angezeigt
- Benutzer können das CV für Bewerbungen aktivieren/deaktivieren über die Checkbox
- Fallback-Name "Lebenslauf.pdf" wird verwendet wenn `cvFileName` nicht verfügbar ist
- Die bestehende CV-Funktionalität bleibt vollständig erhalten

## Betroffene Dateien
- `lib/src/presentation/profile/widgets/additional_documents_section.dart`

## Status
✅ Problem behoben und getestet
✅ CV wird korrekt angezeigt
✅ Checkbox-Funktionalität funktioniert
✅ Keine Breaking Changes

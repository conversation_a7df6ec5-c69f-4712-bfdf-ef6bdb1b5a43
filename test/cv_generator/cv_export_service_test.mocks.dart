// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in ki_test/test/cv_generator/cv_export_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:typed_data' as _i4;

import 'package:ki_test/src/application/services/pdf_cv_generator_service.dart'
    as _i2;
import 'package:ki_test/src/domain/models/cv_template.dart' as _i6;
import 'package:ki_test/src/domain/models/user_profile.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [PdfCvGeneratorService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPdfCvGeneratorService extends _i1.Mock
    implements _i2.PdfCvGeneratorService {
  MockPdfCvGeneratorService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Uint8List> generateCvPdf(
    _i5.UserProfile? profile,
    _i6.CvTemplate? template,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#generateCvPdf, [profile, template]),
            returnValue: _i3.Future<_i4.Uint8List>.value(_i4.Uint8List(0)),
          )
          as _i3.Future<_i4.Uint8List>);

  @override
  _i3.Future<String> savePdfToDevice(
    _i4.Uint8List? pdfBytes,
    String? fileName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#savePdfToDevice, [pdfBytes, fileName]),
            returnValue: _i3.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#savePdfToDevice, [pdfBytes, fileName]),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<String> savePdfLocally(
    _i4.Uint8List? pdfBytes,
    String? fileName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#savePdfLocally, [pdfBytes, fileName]),
            returnValue: _i3.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#savePdfLocally, [pdfBytes, fileName]),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<void> sharePdf(_i4.Uint8List? pdfBytes, String? fileName) =>
      (super.noSuchMethod(
            Invocation.method(#sharePdf, [pdfBytes, fileName]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:ki_test/src/application/services/cv_export_service.dart';
import 'package:ki_test/src/application/services/pdf_cv_generator_service.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/models/cv_template.dart';

// Generiere Mocks
@GenerateMocks([PdfCvGeneratorService])
import 'cv_export_service_test.mocks.dart';

void main() {
  group('CvExportService Tests', () {
    late CvExportService service;
    late MockPdfCvGeneratorService mockPdfGenerator;
    late UserProfile testProfile;
    late CvTemplate testTemplate;

    setUp(() {
      mockPdfGenerator = MockPdfCvGeneratorService();
      service = CvExportService(mockPdfGenerator);
      
      // Test-Profil erstellen
      testProfile = UserProfile(
        id: 'test-id',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+49 **********',
        address: 'Musterstraße 1, 12345 Musterstadt',
        workExperience: [
          WorkExperience(
            position: 'Software Developer',
            company: 'Tech Corp',
            description: 'Entwicklung von Flutter-Apps',
            startDate: DateTime(2020, 1, 1),
            endDate: DateTime(2023, 12, 31),
          ),
        ],
        education: [
          Education(
            degree: 'Bachelor of Science',
            institution: 'Universität Muster',
            fieldOfStudy: 'Informatik',
            startDate: DateTime(2016, 10, 1),
            endDate: DateTime(2020, 9, 30),
          ),
        ],
        skills: ['Flutter', 'Dart', 'Firebase', 'Git'],
      );
      
      // Test-Template erstellen
      testTemplate = CvTemplate(
        id: 'test-template',
        name: 'Test Template',
        description: 'Template für Tests',
        type: CvTemplateType.classic,
        colorScheme: CvColorScheme.blue,
        previewImagePath: 'test.png',
        layoutConfig: {},
      );
    });

    group('exportCvAsPdf', () {
      test('sollte PDF erfolgreich exportieren', () async {
        // Arrange
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.savePdfToDevice(any, any))
            .thenAnswer((_) async => '/path/to/saved/file.pdf');

        // Act
        final filePath = await service.exportCvAsPdf(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(filePath, isNotNull);
        expect(filePath, contains('.pdf'));
        verify(mockPdfGenerator.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        )).called(1);
        verify(mockPdfGenerator.savePdfToDevice(mockPdfBytes, any)).called(1);
      });

      test('sollte benutzerdefinierten Dateinamen verwenden', () async {
        // Arrange
        final mockPdfBytes = [1, 2, 3, 4, 5];
        const customFileName = 'custom_cv.pdf';
        
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.savePdfToDevice(any, customFileName))
            .thenAnswer((_) async => '/path/to/$customFileName');

        // Act
        final filePath = await service.exportCvAsPdf(
          profile: testProfile,
          template: testTemplate,
          customFileName: customFileName,
        );

        // Assert
        expect(filePath, contains(customFileName));
        verify(mockPdfGenerator.savePdfToDevice(mockPdfBytes, customFileName)).called(1);
      });

      test('sollte Fehler weiterleiten', () async {
        // Arrange
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenThrow(Exception('PDF generation failed'));

        // Act & Assert
        expect(
          () => service.exportCvAsPdf(
            profile: testProfile,
            template: testTemplate,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('shareCvAsPdf', () {
      test('sollte PDF erfolgreich teilen', () async {
        // Arrange
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.sharePdf(any, any))
            .thenAnswer((_) async => {});

        // Act
        await service.shareCvAsPdf(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        verify(mockPdfGenerator.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        )).called(1);
        verify(mockPdfGenerator.sharePdf(mockPdfBytes, any)).called(1);
      });
    });

    group('batchExportCv', () {
      test('sollte mehrere Templates erfolgreich exportieren', () async {
        // Arrange
        final templates = [
          testTemplate,
          testTemplate.copyWith(id: 'template2', name: 'Template 2'),
          testTemplate.copyWith(id: 'template3', name: 'Template 3'),
        ];
        
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.savePdfToDevice(any, any))
            .thenAnswer((invocation) async => '/path/to/${invocation.positionalArguments[1]}');

        // Act
        final exportedFiles = await service.batchExportCv(
          profile: testProfile,
          templates: templates,
        );

        // Assert
        expect(exportedFiles.length, equals(templates.length));
        verify(mockPdfGenerator.generateCvPdf(
          profile: testProfile,
          template: anyNamed('template'),
        )).called(templates.length);
      });

      test('sollte mit Fehlern bei einzelnen Templates umgehen', () async {
        // Arrange
        final templates = [
          testTemplate,
          testTemplate.copyWith(id: 'template2', name: 'Template 2'),
        ];
        
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: testProfile,
          template: templates[0],
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.generateCvPdf(
          profile: testProfile,
          template: templates[1],
        )).thenThrow(Exception('Generation failed'));
        
        when(mockPdfGenerator.savePdfToDevice(any, any))
            .thenAnswer((_) async => '/path/to/file.pdf');

        // Act
        final exportedFiles = await service.batchExportCv(
          profile: testProfile,
          templates: templates,
        );

        // Assert
        expect(exportedFiles.length, equals(1)); // Nur ein erfolgreiches Template
      });
    });

    group('validateExportParameters', () {
      test('sollte true für gültiges Profil zurückgeben', () {
        // Act
        final isValid = service.validateExportParameters(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(isValid, isTrue);
      });

      test('sollte false für Profil ohne Namen zurückgeben', () {
        // Arrange
        final invalidProfile = testProfile.copyWith(name: null);

        // Act
        final isValid = service.validateExportParameters(
          profile: invalidProfile,
          template: testTemplate,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('sollte false für Profil ohne E-Mail zurückgeben', () {
        // Arrange
        final invalidProfile = testProfile.copyWith(email: null);

        // Act
        final isValid = service.validateExportParameters(
          profile: invalidProfile,
          template: testTemplate,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('sollte false für Profil ohne Inhalte zurückgeben', () {
        // Arrange
        final invalidProfile = testProfile.copyWith(
          workExperience: [],
          education: [],
          skills: [],
        );

        // Act
        final isValid = service.validateExportParameters(
          profile: invalidProfile,
          template: testTemplate,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('sollte true für Profil mit nur Skills zurückgeben', () {
        // Arrange
        final profileWithOnlySkills = testProfile.copyWith(
          workExperience: [],
          education: [],
          skills: ['Flutter', 'Dart'],
        );

        // Act
        final isValid = service.validateExportParameters(
          profile: profileWithOnlySkills,
          template: testTemplate,
        );

        // Assert
        expect(isValid, isTrue);
      });
    });

    group('getSupportedFormats', () {
      test('sollte unterstützte Formate zurückgeben', () {
        // Act
        final formats = service.getSupportedFormats();

        // Assert
        expect(formats, isNotEmpty);
        expect(formats, contains('PDF'));
      });
    });

    group('generatePreviewThumbnail', () {
      test('sollte Vorschau-Thumbnail generieren', () async {
        // Arrange
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);

        // Act
        final thumbnail = await service.generatePreviewThumbnail(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(thumbnail, isNotNull);
        expect(thumbnail, equals(mockPdfBytes));
      });

      test('sollte null bei Fehler zurückgeben', () async {
        // Arrange
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenThrow(Exception('Generation failed'));

        // Act
        final thumbnail = await service.generatePreviewThumbnail(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(thumbnail, isNull);
      });
    });

    group('getExportStats', () {
      test('sollte Export-Statistiken zurückgeben', () async {
        // Act
        final stats = await service.getExportStats();

        // Assert
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats, containsKey('totalExports'));
        expect(stats, containsKey('lastExportDate'));
        expect(stats, containsKey('favoriteTemplate'));
        expect(stats, containsKey('exportsByTemplate'));
      });
    });

    group('cleanupTempFiles', () {
      test('sollte ohne Fehler ausgeführt werden', () async {
        // Act & Assert
        expect(() => service.cleanupTempFiles(), returnsNormally);
      });
    });

    group('Dateinamen-Generierung', () {
      test('sollte gültigen Dateinamen generieren', () async {
        // Arrange
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.savePdfToDevice(any, any))
            .thenAnswer((invocation) async => '/path/to/${invocation.positionalArguments[1]}');

        // Act
        final filePath = await service.exportCvAsPdf(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(filePath, contains('Max_Mustermann'));
        expect(filePath, contains('CV'));
        expect(filePath, contains('Test_Template'));
        expect(filePath, endsWith('.pdf'));
      });

      test('sollte mit Leerzeichen in Namen umgehen', () async {
        // Arrange
        final profileWithSpaces = testProfile.copyWith(name: 'Max von Mustermann');
        final templateWithSpaces = testTemplate.copyWith(name: 'Modern Blue Template');
        
        final mockPdfBytes = [1, 2, 3, 4, 5];
        when(mockPdfGenerator.generateCvPdf(
          profile: anyNamed('profile'),
          template: anyNamed('template'),
        )).thenAnswer((_) async => mockPdfBytes);
        
        when(mockPdfGenerator.savePdfToDevice(any, any))
            .thenAnswer((invocation) async => '/path/to/${invocation.positionalArguments[1]}');

        // Act
        final filePath = await service.exportCvAsPdf(
          profile: profileWithSpaces,
          template: templateWithSpaces,
        );

        // Assert
        expect(filePath, contains('Max_von_Mustermann'));
        expect(filePath, contains('Modern_Blue_Template'));
        expect(filePath, isNot(contains(' '))); // Keine Leerzeichen im Dateinamen
      });
    });
  });
}

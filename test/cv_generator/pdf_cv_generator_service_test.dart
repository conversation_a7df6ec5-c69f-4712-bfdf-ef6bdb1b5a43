import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:logger/logger.dart';

import 'package:ki_test/src/application/services/pdf_cv_generator_service.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/models/cv_template.dart';

// Generiere Mocks
@GenerateMocks([Logger])
import 'pdf_cv_generator_service_test.mocks.dart';

void main() {
  group('PdfCvGeneratorService Tests', () {
    late PdfCvGeneratorService service;
    late MockLogger mockLogger;
    late UserProfile testProfile;
    late CvTemplate testTemplate;

    setUp(() {
      mockLogger = MockLogger();
      service = PdfCvGeneratorService();
      
      // Test-Profil erstellen
      testProfile = UserProfile(
        id: 'test-id',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+49 **********',
        address: 'Musterstraße 1, 12345 Musterstadt',
        workExperience: [
          WorkExperience(
            position: 'Software Developer',
            company: 'Tech Corp',
            description: 'Entwicklung von Flutter-Apps',
            startDate: DateTime(2020, 1, 1),
            endDate: DateTime(2023, 12, 31),
          ),
        ],
        education: [
          Education(
            degree: 'Bachelor of Science',
            institution: 'Universität Muster',
            fieldOfStudy: 'Informatik',
            startDate: DateTime(2016, 10, 1),
            endDate: DateTime(2020, 9, 30),
          ),
        ],
        skills: ['Flutter', 'Dart', 'Firebase', 'Git'],
      );
      
      // Test-Template erstellen
      testTemplate = CvTemplate(
        id: 'test-template',
        name: 'Test Template',
        description: 'Template für Tests',
        type: CvTemplateType.classic,
        colorScheme: CvColorScheme.blue,
        previewImagePath: 'test.png',
        layoutConfig: {},
      );
    });

    group('generateCvPdf', () {
      test('sollte PDF erfolgreich generieren', () async {
        // Act
        final result = await service.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result, isA<List<int>>());
        expect(result.isNotEmpty, isTrue);
      });

      test('sollte Fehler werfen bei null Profil', () async {
        // Act & Assert
        expect(
          () => service.generateCvPdf(
            profile: testProfile.copyWith(name: null),
            template: testTemplate,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('sollte verschiedene Template-Typen unterstützen', () async {
        final templates = [
          testTemplate.copyWith(type: CvTemplateType.classic),
          testTemplate.copyWith(type: CvTemplateType.modern),
          testTemplate.copyWith(type: CvTemplateType.minimalist),
        ];

        for (final template in templates) {
          // Act
          final result = await service.generateCvPdf(
            profile: testProfile,
            template: template,
          );

          // Assert
          expect(result, isNotNull);
          expect(result.isNotEmpty, isTrue);
        }
      });

      test('sollte mit leerem Profil umgehen können', () async {
        // Arrange
        final emptyProfile = UserProfile(
          id: 'empty-id',
          name: 'Empty User',
          email: '<EMAIL>',
        );

        // Act
        final result = await service.generateCvPdf(
          profile: emptyProfile,
          template: testTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.isNotEmpty, isTrue);
      });
    });

    group('savePdfToDevice', () {
      test('sollte PDF-Datei erfolgreich speichern', () async {
        // Arrange
        final pdfBytes = await service.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        );
        const fileName = 'test_cv.pdf';

        // Act
        final filePath = await service.savePdfToDevice(pdfBytes, fileName);

        // Assert
        expect(filePath, isNotNull);
        expect(filePath, contains(fileName));
      });

      test('sollte Fehler werfen bei leeren PDF-Bytes', () async {
        // Arrange
        final emptyBytes = <int>[];
        const fileName = 'test_cv.pdf';

        // Act & Assert
        expect(
          () => service.savePdfToDevice(emptyBytes, fileName),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('sharePdf', () {
      test('sollte PDF erfolgreich teilen', () async {
        // Arrange
        final pdfBytes = await service.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        );
        const fileName = 'test_cv.pdf';

        // Act & Assert
        // Note: Sharing kann in Tests nicht vollständig getestet werden
        // da es native Plattform-Funktionalität erfordert
        expect(
          () => service.sharePdf(pdfBytes, fileName),
          returnsNormally,
        );
      });
    });

    group('Template-spezifische Tests', () {
      test('sollte klassisches Template korrekt rendern', () async {
        // Arrange
        final classicTemplate = testTemplate.copyWith(
          type: CvTemplateType.classic,
          colorScheme: CvColorScheme.blue,
        );

        // Act
        final result = await service.generateCvPdf(
          profile: testProfile,
          template: classicTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.length, greaterThan(1000)); // PDF sollte eine gewisse Mindestgröße haben
      });

      test('sollte modernes Template korrekt rendern', () async {
        // Arrange
        final modernTemplate = testTemplate.copyWith(
          type: CvTemplateType.modern,
          colorScheme: CvColorScheme.green,
        );

        // Act
        final result = await service.generateCvPdf(
          profile: testProfile,
          template: modernTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.length, greaterThan(1000));
      });

      test('sollte verschiedene Farbschemas unterstützen', () async {
        final colorSchemes = [
          CvColorScheme.blue,
          CvColorScheme.green,
          CvColorScheme.purple,
          CvColorScheme.orange,
        ];

        for (final colorScheme in colorSchemes) {
          // Arrange
          final template = testTemplate.copyWith(colorScheme: colorScheme);

          // Act
          final result = await service.generateCvPdf(
            profile: testProfile,
            template: template,
          );

          // Assert
          expect(result, isNotNull);
          expect(result.isNotEmpty, isTrue);
        }
      });
    });

    group('Profil-Daten Tests', () {
      test('sollte mit umfangreichen Berufserfahrungen umgehen', () async {
        // Arrange
        final profileWithManyExperiences = testProfile.copyWith(
          workExperience: List.generate(5, (index) => WorkExperience(
            position: 'Position $index',
            company: 'Company $index',
            description: 'Beschreibung für Position $index mit vielen Details und Informationen',
            startDate: DateTime(2015 + index, 1, 1),
            endDate: DateTime(2016 + index, 12, 31),
          )),
        );

        // Act
        final result = await service.generateCvPdf(
          profile: profileWithManyExperiences,
          template: testTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.isNotEmpty, isTrue);
      });

      test('sollte mit vielen Skills umgehen', () async {
        // Arrange
        final profileWithManySkills = testProfile.copyWith(
          skills: List.generate(20, (index) => 'Skill $index'),
        );

        // Act
        final result = await service.generateCvPdf(
          profile: profileWithManySkills,
          template: testTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.isNotEmpty, isTrue);
      });

      test('sollte mit langen Texten umgehen', () async {
        // Arrange
        final longDescription = 'Dies ist eine sehr lange Beschreibung ' * 50;
        final profileWithLongTexts = testProfile.copyWith(
          workExperience: [
            WorkExperience(
              position: 'Senior Software Developer',
              company: 'Big Tech Company',
              description: longDescription,
              startDate: DateTime(2020, 1, 1),
              endDate: DateTime(2023, 12, 31),
            ),
          ],
        );

        // Act
        final result = await service.generateCvPdf(
          profile: profileWithLongTexts,
          template: testTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.isNotEmpty, isTrue);
      });
    });

    group('Error Handling', () {
      test('sollte graceful mit fehlenden Schriftarten umgehen', () async {
        // Act & Assert
        expect(
          () => service.generateCvPdf(
            profile: testProfile,
            template: testTemplate,
          ),
          returnsNormally,
        );
      });

      test('sollte mit ungültigen Template-Konfigurationen umgehen', () async {
        // Arrange
        final invalidTemplate = testTemplate.copyWith(
          layoutConfig: {'invalid': 'config'},
        );

        // Act
        final result = await service.generateCvPdf(
          profile: testProfile,
          template: invalidTemplate,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.isNotEmpty, isTrue);
      });
    });

    group('Performance Tests', () {
      test('sollte PDF in angemessener Zeit generieren', () async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await service.generateCvPdf(
          profile: testProfile,
          template: testTemplate,
        );

        // Assert
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Weniger als 5 Sekunden
      });

      test('sollte mehrere PDFs parallel generieren können', () async {
        // Arrange
        final futures = List.generate(3, (index) => 
          service.generateCvPdf(
            profile: testProfile.copyWith(name: 'User $index'),
            template: testTemplate,
          ),
        );

        // Act
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(3));
        for (final result in results) {
          expect(result, isNotNull);
          expect(result.isNotEmpty, isTrue);
        }
      });
    });
  });
}

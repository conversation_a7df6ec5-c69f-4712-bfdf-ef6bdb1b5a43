import 'package:flutter_test/flutter_test.dart';
import 'package:ki_test/src/application/services/cv_template_service.dart';
import 'package:ki_test/src/domain/models/cv_template.dart';

void main() {
  group('CvTemplateService Tests', () {
    late CvTemplateService service;

    setUp(() {
      service = CvTemplateService();
    });

    group('getAllTemplates', () {
      test('sollte alle verfügbaren Templates zurückgeben', () {
        // Act
        final templates = service.getAllTemplates();

        // Assert
        expect(templates, isNotEmpty);
        expect(templates.length, equals(8)); // Erwarte 8 Templates
        expect(templates, everyElement(isA<CvTemplate>()));
      });

      test('sollte unveränderliche Liste zurückgeben', () {
        // Act
        final templates = service.getAllTemplates();

        // Assert
        expect(() => templates.add(CvTemplate(
          id: 'test',
          name: 'Test',
          description: 'Test',
          type: CvTemplateType.classic,
          colorScheme: CvColorScheme.blue,
          previewImagePath: 'test.png',
          layoutConfig: {},
        )), throwsUnsupportedError);
      });
    });

    group('getFreeTemplates', () {
      test('sollte nur kostenlose Templates zurückgeben', () {
        // Act
        final freeTemplates = service.getFreeTemplates();

        // Assert
        expect(freeTemplates, isNotEmpty);
        expect(freeTemplates, everyElement(predicate<CvTemplate>((t) => !t.isPremium)));
      });

      test('sollte mindestens ein kostenloses Template haben', () {
        // Act
        final freeTemplates = service.getFreeTemplates();

        // Assert
        expect(freeTemplates.length, greaterThanOrEqualTo(1));
      });
    });

    group('getPremiumTemplates', () {
      test('sollte nur Premium-Templates zurückgeben', () {
        // Act
        final premiumTemplates = service.getPremiumTemplates();

        // Assert
        expect(premiumTemplates, isNotEmpty);
        expect(premiumTemplates, everyElement(predicate<CvTemplate>((t) => t.isPremium)));
      });
    });

    group('getTemplatesByType', () {
      test('sollte Templates nach Typ filtern', () {
        // Act
        final classicTemplates = service.getTemplatesByType(CvTemplateType.classic);
        final modernTemplates = service.getTemplatesByType(CvTemplateType.modern);

        // Assert
        expect(classicTemplates, everyElement(predicate<CvTemplate>((t) => t.type == CvTemplateType.classic)));
        expect(modernTemplates, everyElement(predicate<CvTemplate>((t) => t.type == CvTemplateType.modern)));
      });

      test('sollte leere Liste für nicht existierende Typen zurückgeben', () {
        // Alle Template-Typen sollten mindestens ein Template haben
        for (final type in CvTemplateType.values) {
          final templates = service.getTemplatesByType(type);
          expect(templates, isA<List<CvTemplate>>());
        }
      });
    });

    group('getTemplateById', () {
      test('sollte Template mit korrekter ID zurückgeben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();
        final firstTemplate = allTemplates.first;

        // Act
        final foundTemplate = service.getTemplateById(firstTemplate.id);

        // Assert
        expect(foundTemplate, isNotNull);
        expect(foundTemplate!.id, equals(firstTemplate.id));
        expect(foundTemplate.name, equals(firstTemplate.name));
      });

      test('sollte null für nicht existierende ID zurückgeben', () {
        // Act
        final foundTemplate = service.getTemplateById('non-existent-id');

        // Assert
        expect(foundTemplate, isNull);
      });
    });

    group('getDefaultTemplate', () {
      test('sollte ein kostenloses Template als Standard zurückgeben', () {
        // Act
        final defaultTemplate = service.getDefaultTemplate();

        // Assert
        expect(defaultTemplate, isNotNull);
        expect(defaultTemplate.isPremium, isFalse);
      });

      test('sollte immer dasselbe Standard-Template zurückgeben', () {
        // Act
        final template1 = service.getDefaultTemplate();
        final template2 = service.getDefaultTemplate();

        // Assert
        expect(template1.id, equals(template2.id));
      });
    });

    group('isTemplateAvailable', () {
      test('sollte true für kostenlose Templates und Free-User zurückgeben', () {
        // Arrange
        final freeTemplates = service.getFreeTemplates();
        final freeTemplate = freeTemplates.first;

        // Act
        final isAvailable = service.isTemplateAvailable(freeTemplate, false);

        // Assert
        expect(isAvailable, isTrue);
      });

      test('sollte false für Premium-Templates und Free-User zurückgeben', () {
        // Arrange
        final premiumTemplates = service.getPremiumTemplates();
        if (premiumTemplates.isNotEmpty) {
          final premiumTemplate = premiumTemplates.first;

          // Act
          final isAvailable = service.isTemplateAvailable(premiumTemplate, false);

          // Assert
          expect(isAvailable, isFalse);
        }
      });

      test('sollte true für alle Templates und Premium-User zurückgeben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act & Assert
        for (final template in allTemplates) {
          final isAvailable = service.isTemplateAvailable(template, true);
          expect(isAvailable, isTrue);
        }
      });
    });

    group('getAvailableTemplatesForUser', () {
      test('sollte nur kostenlose Templates für Free-User zurückgeben', () {
        // Act
        final availableTemplates = service.getAvailableTemplatesForUser(false);

        // Assert
        expect(availableTemplates, everyElement(predicate<CvTemplate>((t) => !t.isPremium)));
      });

      test('sollte alle Templates für Premium-User zurückgeben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act
        final availableTemplates = service.getAvailableTemplatesForUser(true);

        // Assert
        expect(availableTemplates.length, equals(allTemplates.length));
      });
    });

    group('getTemplatesByCategory', () {
      test('sollte Templates nach Kategorien gruppieren', () {
        // Act
        final categorizedTemplates = service.getTemplatesByCategory();

        // Assert
        expect(categorizedTemplates, isA<Map<CvTemplateType, List<CvTemplate>>>());
        expect(categorizedTemplates.keys, containsAll(CvTemplateType.values));
      });

      test('sollte alle Templates in Kategorien einordnen', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act
        final categorizedTemplates = service.getTemplatesByCategory();

        // Assert
        int totalCategorizedTemplates = 0;
        for (final templates in categorizedTemplates.values) {
          totalCategorizedTemplates += templates.length;
        }
        expect(totalCategorizedTemplates, equals(allTemplates.length));
      });
    });

    group('Template-Validierung', () {
      test('sollte alle Templates gültige IDs haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act & Assert
        for (final template in allTemplates) {
          expect(template.id, isNotEmpty);
          expect(template.id, isA<String>());
        }
      });

      test('sollte alle Templates eindeutige IDs haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();
        final ids = allTemplates.map((t) => t.id).toList();

        // Act & Assert
        expect(ids.toSet().length, equals(ids.length));
      });

      test('sollte alle Templates gültige Namen haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act & Assert
        for (final template in allTemplates) {
          expect(template.name, isNotEmpty);
          expect(template.name, isA<String>());
        }
      });

      test('sollte alle Templates gültige Beschreibungen haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act & Assert
        for (final template in allTemplates) {
          expect(template.description, isNotEmpty);
          expect(template.description, isA<String>());
        }
      });

      test('sollte alle Templates gültige Layout-Konfigurationen haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();

        // Act & Assert
        for (final template in allTemplates) {
          expect(template.layoutConfig, isA<Map<String, dynamic>>());
        }
      });
    });

    group('Template-Typen Tests', () {
      test('sollte alle Template-Typen abdecken', () {
        // Arrange
        final allTemplates = service.getAllTemplates();
        final representedTypes = allTemplates.map((t) => t.type).toSet();

        // Act & Assert
        expect(representedTypes, containsAll(CvTemplateType.values));
      });

      test('sollte verschiedene Farbschemas haben', () {
        // Arrange
        final allTemplates = service.getAllTemplates();
        final colorSchemes = allTemplates.map((t) => t.colorScheme).toSet();

        // Act & Assert
        expect(colorSchemes.length, greaterThan(1));
      });
    });

    group('Premium/Free Balance Tests', () {
      test('sollte ausgewogenes Verhältnis von kostenlosen und Premium-Templates haben', () {
        // Arrange
        final freeTemplates = service.getFreeTemplates();
        final premiumTemplates = service.getPremiumTemplates();

        // Act & Assert
        expect(freeTemplates.length, greaterThanOrEqualTo(2)); // Mindestens 2 kostenlose
        expect(premiumTemplates.length, greaterThanOrEqualTo(1)); // Mindestens 1 Premium
      });

      test('sollte mindestens ein kostenloses Template pro Hauptkategorie haben', () {
        // Arrange
        final freeTemplates = service.getFreeTemplates();
        final mainTypes = [CvTemplateType.classic, CvTemplateType.modern, CvTemplateType.minimalist];

        // Act & Assert
        for (final type in mainTypes) {
          final freeTemplatesOfType = freeTemplates.where((t) => t.type == type);
          expect(freeTemplatesOfType, isNotEmpty, reason: 'Sollte mindestens ein kostenloses $type Template haben');
        }
      });
    });
  });
}

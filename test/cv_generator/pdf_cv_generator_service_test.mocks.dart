// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in ki_test/test/cv_generator/pdf_cv_generator_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:logger/src/log_level.dart' as _i4;
import 'package:logger/src/logger.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [Logger].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockLogger extends _i1.Mock implements _i2.Logger {
  MockLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> get init =>
      (super.noSuchMethod(
            Invocation.getter(#init),
            returnValue: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void v(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #v,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void t(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #t,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #d,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #i,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #w,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #e,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void wtf(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #wtf,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #f,
      [message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void log(
    _i4.Level? level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) => super.noSuchMethod(
    Invocation.method(
      #log,
      [level, message],
      {#time: time, #error: error, #stackTrace: stackTrace},
    ),
    returnValueForMissingStub: null,
  );

  @override
  bool isClosed() =>
      (super.noSuchMethod(Invocation.method(#isClosed, []), returnValue: false)
          as bool);

  @override
  _i3.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

import 'package:flutter_test/flutter_test.dart';
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';

void main() {
  group('PremiumFeature Enum Tests', () {
    test('Alle PremiumFeature Werte sind definiert', () {
      // Arrange & Act
      final features = PremiumFeature.values;

      // Assert
      expect(features, isNotEmpty);
      expect(features.length, greaterThanOrEqualTo(6));
      
      // Prü<PERSON>, dass alle erwarteten Features vorhanden sind
      expect(features, contains(PremiumFeature.aiCoverLetter));
      expect(features, contains(PremiumFeature.aiJobSearch));
      expect(features, contains(PremiumFeature.unlimitedFavorites));
      expect(features, contains(PremiumFeature.adFree));
      expect(features, contains(PremiumFeature.insights));
      expect(features, contains(PremiumFeature.translation));
    });

    test('PremiumFeature Namen sind korrekt', () {
      // Act & Assert
      expect(PremiumFeature.aiCoverLetter.name, equals('aiCoverLetter'));
      expect(PremiumFeature.aiJobSearch.name, equals('aiJobSearch'));
      expect(PremiumFeature.unlimitedFavorites.name, equals('unlimitedFavorites'));
      expect(PremiumFeature.adFree.name, equals('adFree'));
      expect(PremiumFeature.insights.name, equals('insights'));
      expect(PremiumFeature.translation.name, equals('translation'));
    });

    test('PremiumFeature toString funktioniert', () {
      // Act & Assert
      expect(PremiumFeature.aiCoverLetter.toString(), contains('aiCoverLetter'));
      expect(PremiumFeature.aiJobSearch.toString(), contains('aiJobSearch'));
      expect(PremiumFeature.unlimitedFavorites.toString(), contains('unlimitedFavorites'));
      expect(PremiumFeature.adFree.toString(), contains('adFree'));
      expect(PremiumFeature.insights.toString(), contains('insights'));
      expect(PremiumFeature.translation.toString(), contains('translation'));
    });

    test('PremiumFeature Vergleiche funktionieren', () {
      // Act & Assert
      expect(PremiumFeature.aiCoverLetter == PremiumFeature.aiCoverLetter, isTrue);
      expect(PremiumFeature.aiCoverLetter == PremiumFeature.aiJobSearch, isFalse);
      expect(PremiumFeature.adFree != PremiumFeature.insights, isTrue);
    });

    test('PremiumFeature kann in Set verwendet werden', () {
      // Arrange
      final featureSet = <PremiumFeature>{
        PremiumFeature.aiCoverLetter,
        PremiumFeature.aiJobSearch,
        PremiumFeature.aiCoverLetter, // Duplikat
      };

      // Act & Assert
      expect(featureSet.length, equals(2)); // Duplikat wird entfernt
      expect(featureSet, contains(PremiumFeature.aiCoverLetter));
      expect(featureSet, contains(PremiumFeature.aiJobSearch));
    });

    test('PremiumFeature kann in Map als Key verwendet werden', () {
      // Arrange
      final featureMap = <PremiumFeature, bool>{
        PremiumFeature.aiCoverLetter: true,
        PremiumFeature.adFree: false,
      };

      // Act & Assert
      expect(featureMap[PremiumFeature.aiCoverLetter], isTrue);
      expect(featureMap[PremiumFeature.adFree], isFalse);
      expect(featureMap[PremiumFeature.insights], isNull);
    });

    test('PremiumFeature index ist konsistent', () {
      // Act & Assert
      final features = PremiumFeature.values;
      for (int i = 0; i < features.length; i++) {
        expect(features[i].index, equals(i));
      }
    });
  });
}

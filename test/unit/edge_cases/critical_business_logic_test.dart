import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/infrastructure/services/subscription_service.dart';
import 'package:ki_test/src/infrastructure/services/application_counter_service.dart';

// Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockUser extends Mock implements User {}
class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}
class MockFunctionsClient extends Mock implements FunctionsClient {}
class MockFunctionResponse extends Mock implements FunctionResponse {}

void main() {
  group('Kritische Geschäftslogik Edge Cases', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockSupabaseQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late MockFunctionsClient mockFunctions;
    late MockFunctionResponse mockFunctionResponse;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockSupabaseQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();
      mockFunctions = MockFunctionsClient();
      mockFunctionResponse = MockFunctionResponse();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockSupabaseClient.functions).thenReturn(mockFunctions);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');
    });

    group('Umsatzverlust-Szenarien', () {
      test('sollte doppelte Promo-Code Einlösung verhindern', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        // Erste Einlösung erfolgreich
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {'id': 'redemption-123'},
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        final firstRedemption = await subscriptionService.redeemPromoCode('WELCOME2025');
        expect(firstRedemption, true);

        // Zweite Einlösung sollte fehlschlagen
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code wurde bereits von Ihnen eingelöst.',
        });

        final secondRedemption = await subscriptionService.redeemPromoCode('WELCOME2025');
        expect(secondRedemption, false);
      });

      test('sollte Promo-Code Einlösung mit aktivem Abonnement verhindern', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Sie haben bereits ein aktives Abonnement. Promo Codes können nur von neuen Nutzern eingelöst werden.',
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, false);
      });

      test('sollte Bewerbung bei 0 Guthaben verhindern', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('pro', 150);
        setupCounterMock(0, 150); // Kein Guthaben mehr

        // Act
        final canApply = await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(canApply, false);
      });

      test('sollte Race Condition bei gleichzeitigen Bewerbungen verhindern', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('basic', 30);
        setupCounterMock(1, 30); // Nur noch 1 Bewerbung übrig

        // Simuliere gleichzeitige Bewerbungen
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        
        // Erste Bewerbung erfolgreich
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenAnswer((_) async => {
          'data': [{'remaining_applications': 0}],
          'error': null,
        });

        // Act - Simuliere zwei gleichzeitige Bewerbungen
        final future1 = applicationCounterService.incrementApplicationCounter();
        final future2 = applicationCounterService.incrementApplicationCounter();

        final results = await Future.wait([future1, future2]);

        // Assert - Nur eine sollte erfolgreich sein
        final successCount = results.where((r) => r == true).length;
        expect(successCount, lessThanOrEqualTo(1));
      });

      test('sollte abgelaufenes Abonnement korrekt behandeln', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        // Abgelaufenes Abonnement
        when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'id': 'sub-123',
            'user_id': 'test-user-id',
            'plan_type': 'pro',
            'status': 'active',
            'expires_at': DateTime.now().subtract(Duration(days: 1)).toIso8601String(), // Abgelaufen
          },
          'error': null,
        });

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte wie Free-User behandelt werden
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
      });
    });

    group('Kritische Dateninkonsistenz-Szenarien', () {
      test('sollte inkonsistente Subscription-Counter Daten korrigieren', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('pro', 150);
        
        // Counter hat mehr Bewerbungen als Plan erlaubt (Dateninkonsistenz)
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': 200, // Mehr als Pro-Plan erlaubt
            'total_applications': 200,
          },
          'error': null,
        });

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte auf Plan-Limit korrigiert werden
        expect(result['total'], lessThanOrEqualTo(150));
      });

      test('sollte negative Bewerbungszähler korrigieren', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('basic', 30);
        
        // Negativer Counter (Datenkorruption)
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': -5, // Negativer Wert
            'total_applications': 30,
          },
          'error': null,
        });

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte auf 0 korrigiert werden
        expect(result['remaining'], greaterThanOrEqualTo(0));
      });

      test('sollte Plan-Downgrade korrekt behandeln', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        // User hatte Unlimited, jetzt Basic
        setupSubscriptionMock('basic', 30);
        
        // Counter zeigt noch alte Unlimited-Werte
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': -1, // Unlimited-Marker
            'total_applications': -1,
          },
          'error': null,
        });

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte auf Basic-Plan Limits angepasst werden
        expect(result['remaining'], lessThanOrEqualTo(30));
        expect(result['total'], equals(30));
        expect(result['unlimited'], false);
      });
    });

    group('Netzwerk- und Infrastruktur-Ausfälle', () {
      test('sollte Supabase-Ausfall graceful behandeln', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenThrow(Exception('Network timeout'));

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert - Sollte false zurückgeben, nicht crashen
        expect(result, false);
      });

      test('sollte Datenbankverbindungsfehler behandeln', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenThrow(Exception('Connection failed'));

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte sichere Defaults zurückgeben
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });

      test('sollte langsame API-Responses mit Timeout behandeln', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) => Future.delayed(Duration(seconds: 30), () => mockFunctionResponse));

        // Act & Assert - Sollte nach angemessener Zeit timeout
        expect(
          () => subscriptionService.redeemPromoCode('WELCOME2025').timeout(Duration(seconds: 5)),
          throwsA(isA<TimeoutException>()),
        );
      });
    });

    group('Sicherheits-Edge-Cases', () {
      test('sollte SQL-Injection in Promo-Codes verhindern', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        final maliciousCodes = [
          "'; DROP TABLE promo_codes; --",
          "UNION SELECT * FROM users",
          "<script>alert('xss')</script>",
          "../../etc/passwd",
        ];

        for (final maliciousCode in maliciousCodes) {
          when(mockFunctionResponse.status).thenReturn(200);
          when(mockFunctionResponse.data).thenReturn({
            'success': false,
            'isValid': false,
            'message': 'Ungültiger Promo Code. Bitte überprüfen Sie die Eingabe.',
          });
          when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
              .thenAnswer((_) async => mockFunctionResponse);

          // Act
          final result = await subscriptionService.redeemPromoCode(maliciousCode);

          // Assert
          expect(result, false, reason: 'Malicious code should be rejected: $maliciousCode');
        }
      });

      test('sollte User-ID Manipulation verhindern', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        // Simuliere manipulierte User-ID
        when(mockUser.id).thenReturn('admin-user-id'); // Versuche Admin-Zugriff
        
        setupSubscriptionMock('unlimited', null);

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte nur Daten für authentifizierten User zurückgeben
        verify(mockFilterBuilder.eq('user_id', 'admin-user-id')).called(greaterThan(0));
      });

      test('sollte Session-Hijacking durch Token-Validierung verhindern', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null); // Keine gültige Session

        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte sichere Defaults für unauthentifizierte User zurückgeben
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
      });
    });

    group('Extreme Load und Performance', () {
      test('sollte hohe Anzahl gleichzeitiger Promo-Code Einlösungen behandeln', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {'id': 'redemption-123'},
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act - Simuliere 100 gleichzeitige Einlösungen
        final futures = List.generate(100, (index) => 
          subscriptionService.redeemPromoCode('WELCOME2025')
        );

        final results = await Future.wait(futures);

        // Assert - Sollte alle Requests verarbeiten ohne zu crashen
        expect(results.length, equals(100));
        expect(results.every((r) => r is bool), true);
      });

      test('sollte Memory-Leaks bei vielen Counter-Abfragen verhindern', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('pro', 150);
        setupCounterMock(100, 150);

        // Act - Simuliere 1000 Abfragen
        for (int i = 0; i < 1000; i++) {
          await applicationCounterService.getRemainingApplications();
        }

        // Assert - Sollte ohne Memory-Probleme durchlaufen
        expect(true, true); // Test erfolgreich wenn kein Crash
      });
    });

    // Helper-Methoden
    void setupSubscriptionMock(String planType, int? totalApplications) {
      when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'data': {
          'id': 'sub-123',
          'user_id': 'test-user-id',
          'plan_type': planType,
          'status': 'active',
          'expires_at': DateTime.now().add(Duration(days: 30)).toIso8601String(),
        },
        'error': null,
      });
    }

    void setupCounterMock(int? remaining, int? total) {
      when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      
      if (remaining == null || total == null) {
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': null,
          'error': {'code': 'PGRST116', 'message': 'No rows found'},
        });
      } else {
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': remaining,
            'total_applications': total,
            'reset_date': DateTime.now().toIso8601String(),
          },
          'error': null,
        });
      }
    }
  });
}

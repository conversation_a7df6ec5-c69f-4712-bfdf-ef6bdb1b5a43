import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/infrastructure/services/subscription_service.dart';
import 'package:ki_test/src/infrastructure/services/application_counter_service.dart';
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';

// Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockUser extends Mock implements User {}
class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}
class MockFunctionsClient extends Mock implements FunctionsClient {}
class MockFunctionResponse extends Mock implements FunctionResponse {}
class MockSubscriptionService extends Mock implements SubscriptionService {}

void main() {
  group('User Experience Edge Cases', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockSupabaseQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late MockFunctionsClient mockFunctions;
    late MockFunctionResponse mockFunctionResponse;
    late MockSubscriptionService mockSubscriptionService;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockSupabaseQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();
      mockFunctions = MockFunctionsClient();
      mockFunctionResponse = MockFunctionResponse();
      mockSubscriptionService = MockSubscriptionService();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockSupabaseClient.functions).thenReturn(mockFunctions);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');
    });

    group('Schlechte User Experience Szenarien', () {
      test('sollte klare Fehlermeldung bei abgelaufenem Promo-Code geben', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code ist abgelaufen.',
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('EXPIRED_CODE');

        // Assert
        expect(result, false);
        // Fehlermeldung sollte spezifisch und hilfreich sein
        verify(mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'EXPIRED_CODE',
        })).called(1);
      });

      test('sollte Benutzer warnen wenn nur noch wenige Bewerbungen übrig sind', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('basic', 30);
        setupCounterMock(2, 30); // Nur noch 2 Bewerbungen

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(2));
        expect(result['total'], equals(30));
        
        // Bei weniger als 5 Bewerbungen sollte Warnung angezeigt werden
        final shouldWarn = result['remaining'] < 5;
        expect(shouldWarn, true);
      });

      test('sollte Upgrade-Vorschlag bei Feature-Blockierung zeigen', () async {
        // Arrange
        final premiumFeatureService = PremiumFeatureService(mockSubscriptionService);
        
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);
        final upgradeMessage = premiumFeatureService.getUpgradeMessage(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false);
        expect(upgradeMessage, isNotEmpty);
        expect(upgradeMessage, contains('KI-Anschreiben'));
      });

      test('sollte Progress-Indikator für langsame Operationen zeigen', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        // Simuliere langsame API-Response
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {'id': 'redemption-123'},
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) => Future.delayed(Duration(seconds: 3), () => mockFunctionResponse));

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');
        stopwatch.stop();

        // Assert
        expect(result, true);
        expect(stopwatch.elapsedMilliseconds, greaterThan(2000)); // Sollte Progress zeigen
      });

      test('sollte Offline-Modus graceful behandeln', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenThrow(Exception('No internet connection'));

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte cached/default Werte zurückgeben
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });
    });

    group('Verwirrende UI-Zustände', () {
      test('sollte konsistente Daten zwischen verschiedenen Screens zeigen', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('pro', 150);
        setupCounterMock(100, 150);

        // Act - Simuliere mehrfache Abfragen von verschiedenen Screens
        final result1 = await applicationCounterService.getRemainingApplications();
        final result2 = await applicationCounterService.getRemainingApplications();
        final result3 = await applicationCounterService.getRemainingApplications();

        // Assert - Alle Ergebnisse sollten identisch sein
        expect(result1, equals(result2));
        expect(result2, equals(result3));
      });

      test('sollte Plan-Upgrade sofort in UI reflektieren', () async {
        // Arrange
        final premiumFeatureService = PremiumFeatureService(mockSubscriptionService);
        
        // Zunächst Free-User
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);
        
        var hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);
        expect(hasAccess, false);

        // Simuliere Upgrade zu Premium
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);
        
        // Act
        hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert - Sollte sofort Zugriff gewähren
        expect(hasAccess, true);
      });

      test('sollte Promo-Code Ablauf korrekt anzeigen', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        // Promo-Code läuft in 1 Stunde ab
        final expiryTime = DateTime.now().add(Duration(hours: 1));
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {
            'id': 'redemption-123',
            'expires_at': expiryTime.toIso8601String(),
          },
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('EXPIRING_SOON');

        // Assert
        expect(result, true);
        // UI sollte Ablaufzeit prominent anzeigen
      });

      test('sollte Loading-States korrekt verwalten', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('pro', 150);
        
        // Simuliere langsame Counter-Abfrage
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) => Future.delayed(
          Duration(seconds: 2), 
          () => {
            'data': {
              'user_id': 'test-user-id',
              'remaining_applications': 100,
              'total_applications': 150,
            },
            'error': null,
          }
        ));

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await applicationCounterService.getRemainingApplications();
        stopwatch.stop();

        // Assert
        expect(result['remaining'], equals(100));
        expect(stopwatch.elapsedMilliseconds, greaterThan(1500)); // Sollte Loading zeigen
      });
    });

    group('Accessibility und Usability', () {
      test('sollte Fehlermeldungen für Screen Reader optimieren', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Ungültiger Promo Code. Bitte überprüfen Sie die Eingabe.',
        });
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('INVALID');

        // Assert
        expect(result, false);
        // Fehlermeldung sollte klar und strukturiert sein
      });

      test('sollte Tastatur-Navigation unterstützen', () async {
        // Arrange
        final premiumFeatureService = PremiumFeatureService(mockSubscriptionService);
        
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final shouldShowDialog = await premiumFeatureService.shouldShowPremiumDialog(PremiumFeature.aiCoverLetter);

        // Assert
        expect(shouldShowDialog, true);
        // Dialog sollte Tastatur-Navigation unterstützen
      });

      test('sollte High-Contrast Mode unterstützen', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('basic', 30);
        setupCounterMock(5, 30); // Wenig Guthaben

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(5));
        // UI sollte in High-Contrast Mode gut lesbar sein
      });
    });

    group('Internationalisierung Edge Cases', () {
      test('sollte Promo-Code Nachrichten in verschiedenen Sprachen unterstützen', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        final languages = ['de', 'en', 'fr', 'es'];
        
        for (final lang in languages) {
          when(mockFunctionResponse.status).thenReturn(200);
          when(mockFunctionResponse.data).thenReturn({
            'success': true,
            'isValid': true,
            'message': getLocalizedMessage(lang),
            'redemption': {'id': 'redemption-123'},
          });
          when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
              .thenAnswer((_) async => mockFunctionResponse);

          // Act
          final result = await subscriptionService.redeemPromoCode('WELCOME2025');

          // Assert
          expect(result, true);
        }
      });

      test('sollte Währungsformatierung korrekt handhaben', () async {
        // Arrange
        final currencies = ['EUR', 'USD', 'GBP', 'JPY'];
        
        for (final currency in currencies) {
          // Act & Assert - Preise sollten korrekt formatiert werden
          final formattedPrice = formatPrice(14.99, currency);
          expect(formattedPrice, isNotEmpty);
          expect(formattedPrice, contains(currency));
        }
      });

      test('sollte Datum/Zeit-Formate regional anpassen', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('basic', 30);
        
        final resetDate = DateTime.now().add(Duration(days: 15));
        
        when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': 20,
            'total_applications': 30,
            'reset_date': resetDate.toIso8601String(),
          },
          'error': null,
        });

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(20));
        // Reset-Datum sollte regional formatiert werden
      });
    });

    group('Mobile-spezifische Edge Cases', () {
      test('sollte App-Backgrounding während Promo-Code Einlösung behandeln', () async {
        // Arrange
        final subscriptionService = SubscriptionService(mockSupabaseClient, null);
        
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {'id': 'redemption-123'},
        });
        
        // Simuliere App-Backgrounding (langsame Response)
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) => Future.delayed(Duration(seconds: 5), () => mockFunctionResponse));

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, true);
        // App sollte State korrekt wiederherstellen
      });

      test('sollte schwache Netzwerkverbindung graceful behandeln', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        // Simuliere langsame/instabile Verbindung
        when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer((_) => Future.delayed(
          Duration(seconds: 10), 
          () => throw Exception('Request timeout')
        ));

        // Act
        final result = await applicationCounterService.getRemainingApplications();

        // Assert - Sollte cached/default Werte verwenden
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
      });

      test('sollte Speicher-Beschränkungen auf älteren Geräten berücksichtigen', () async {
        // Arrange
        final applicationCounterService = ApplicationCounterService(mockSupabaseClient);
        
        setupSubscriptionMock('unlimited', null);

        // Act - Simuliere viele Abfragen (Memory-Test)
        for (int i = 0; i < 100; i++) {
          await applicationCounterService.getRemainingApplications();
        }

        // Assert - Sollte ohne Memory-Probleme durchlaufen
        expect(true, true);
      });
    });

    // Helper-Methoden
    void setupSubscriptionMock(String planType, int? totalApplications) {
      when(mockSupabaseClient.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'data': {
          'id': 'sub-123',
          'user_id': 'test-user-id',
          'plan_type': planType,
          'status': 'active',
          'expires_at': DateTime.now().add(Duration(days: 30)).toIso8601String(),
        },
        'error': null,
      });
    }

    void setupCounterMock(int? remaining, int? total) {
      when(mockSupabaseClient.from('application_counters')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      
      if (remaining == null || total == null) {
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': null,
          'error': {'code': 'PGRST116', 'message': 'No rows found'},
        });
      } else {
        when(mockFilterBuilder.single()).thenAnswer((_) async => {
          'data': {
            'user_id': 'test-user-id',
            'remaining_applications': remaining,
            'total_applications': total,
            'reset_date': DateTime.now().toIso8601String(),
          },
          'error': null,
        });
      }
    }

    String getLocalizedMessage(String lang) {
      switch (lang) {
        case 'de':
          return 'Promo Code erfolgreich eingelöst! Sie erhalten 7 Tage kostenlosen Premium-Zugang.';
        case 'en':
          return 'Promo code successfully redeemed! You get 7 days of free premium access.';
        case 'fr':
          return 'Code promo utilisé avec succès! Vous obtenez 7 jours d\'accès premium gratuit.';
        case 'es':
          return '¡Código promocional canjeado con éxito! Obtienes 7 días de acceso premium gratuito.';
        default:
          return 'Promo code redeemed successfully!';
      }
    }

    String formatPrice(double price, String currency) {
      switch (currency) {
        case 'EUR':
          return '${price.toStringAsFixed(2)} €';
        case 'USD':
          return '\$${price.toStringAsFixed(2)}';
        case 'GBP':
          return '£${price.toStringAsFixed(2)}';
        case 'JPY':
          return '¥${price.toInt()}';
        default:
          return '${price.toStringAsFixed(2)} $currency';
      }
    }
  });
}

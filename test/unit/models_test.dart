import 'package:flutter_test/flutter_test.dart';
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';

void main() {
  group('PremiumFeature Tests', () {
    test('Alle Features sind verfügbar', () {
      expect(PremiumFeature.values.length, greaterThanOrEqualTo(6));
      expect(PremiumFeature.values, contains(PremiumFeature.aiCoverLetter));
      expect(PremiumFeature.values, contains(PremiumFeature.aiJobSearch));
      expect(PremiumFeature.values, contains(PremiumFeature.unlimitedFavorites));
      expect(PremiumFeature.values, contains(PremiumFeature.adFree));
      expect(PremiumFeature.values, contains(PremiumFeature.insights));
      expect(PremiumFeature.values, contains(PremiumFeature.translation));
    });

    test('Feature Namen sind korrekt', () {
      expect(PremiumFeature.aiCoverLetter.name, 'aiCoverLetter');
      expect(PremiumFeature.aiJobSearch.name, 'aiJobSearch');
      expect(PremiumFeature.unlimitedFavorites.name, 'unlimitedFavorites');
      expect(PremiumFeature.adFree.name, 'adFree');
      expect(PremiumFeature.insights.name, 'insights');
      expect(PremiumFeature.translation.name, 'translation');
    });
  });
}

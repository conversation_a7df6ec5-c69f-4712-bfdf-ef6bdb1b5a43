import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:ki_test/src/application/providers/pdf_cv_generator_provider.dart';
import 'package:ki_test/src/application/providers/user_profile_provider.dart';
import 'package:ki_test/src/application/providers/cv_template_provider.dart';
import 'package:ki_test/src/application/services/pdf_cv_generator_service.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/models/cv_template.dart';

import 'pdf_cv_generator_provider_test.mocks.dart';

@GenerateMocks([PdfCvGeneratorService])
void main() {
  group('PdfGenerationNotifier', () {
    late ProviderContainer container;
    late MockPdfCvGeneratorService mockService;
    late UserProfile testProfile;
    late CvTemplate testTemplate;

    setUp(() {
      mockService = MockPdfCvGeneratorService();

      testProfile = UserProfile(
        id: 'test-user-id',
        firstName: 'Max',
        lastName: 'Mustermann',
        email: '<EMAIL>',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testTemplate = CvTemplate(
        id: 'classic_blue',
        name: 'Klassisch Blau',
        description: 'Klassisches Design in Blau',
        type: CvTemplateType.classic,
        isPremium: false,
        previewImageUrl: 'https://example.com/preview.jpg',
        primaryColor: '#2196F3',
        secondaryColor: '#1976D2',
        accentColor: '#FFC107',
        fontFamily: 'Roboto',
        layout: CvTemplateLayout.singleColumn,
      );

      container = ProviderContainer(
        overrides: [
          pdfCvGeneratorServiceProvider.overrideWithValue(mockService),
          userProfileProvider.overrideWith(
            (ref) => AsyncValue.data(testProfile),
          ),
          selectedTemplateProvider.overrideWith((ref) => testTemplate),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('generatePdf', () {
      test('sollte PDF erfolgreich generieren', () async {
        // Arrange
        final testPdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        when(
          mockService.generateCvPdf(any, any),
        ).thenAnswer((_) async => testPdfBytes);

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        await notifier.generatePdf();

        // Assert
        final state = container.read(pdfGenerationProvider);
        expect(state.hasValue, true);
        expect(state.value, equals(testPdfBytes));
        verify(mockService.generateCvPdf(testProfile, testTemplate)).called(1);
      });

      test('sollte Fehler bei fehlenden Profildaten behandeln', () async {
        // Arrange
        container = ProviderContainer(
          overrides: [
            pdfCvGeneratorServiceProvider.overrideWithValue(mockService),
            userProfileProvider.overrideWith(
              (ref) => const AsyncValue.data(null),
            ),
            selectedTemplateProvider.overrideWith((ref) => testTemplate),
          ],
        );

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        await notifier.generatePdf();

        // Assert
        final state = container.read(pdfGenerationProvider);
        expect(state.hasError, true);
        expect(state.error.toString(), contains('Profildaten nicht verfügbar'));
      });

      test('sollte Fehler bei fehlendem Template behandeln', () async {
        // Arrange
        container = ProviderContainer(
          overrides: [
            pdfCvGeneratorServiceProvider.overrideWithValue(mockService),
            userProfileProvider.overrideWith(
              (ref) => AsyncValue.data(testProfile),
            ),
            selectedTemplateProvider.overrideWith((ref) => null),
          ],
        );

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        await notifier.generatePdf();

        // Assert
        final state = container.read(pdfGenerationProvider);
        expect(state.hasError, true);
        expect(state.error.toString(), contains('Kein Template ausgewählt'));
      });

      test('sollte Service-Fehler korrekt weiterleiten', () async {
        // Arrange
        when(
          mockService.generateCvPdf(any, any),
        ).thenThrow(Exception('Service-Fehler'));

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        await notifier.generatePdf();

        // Assert
        final state = container.read(pdfGenerationProvider);
        expect(state.hasError, true);
        expect(state.error.toString(), contains('Service-Fehler'));
      });
    });

    group('sharePdf', () {
      test('sollte PDF erfolgreich teilen', () async {
        // Arrange
        final testPdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        container.read(pdfGenerationProvider.notifier).state = AsyncValue.data(
          testPdfBytes,
        );

        when(mockService.sharePdf(any, any)).thenAnswer((_) async {
          return null;
        });

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        await notifier.sharePdf('test.pdf');

        // Assert
        verify(mockService.sharePdf(testPdfBytes, 'test.pdf')).called(1);
      });

      test('sollte Fehler bei fehlenden PDF-Daten behandeln', () async {
        // Arrange
        container
            .read(pdfGenerationProvider.notifier)
            .state = const AsyncValue.data(null);

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act & Assert
        expect(() => notifier.sharePdf('test.pdf'), throwsA(isA<Exception>()));
      });
    });

    group('savePdfLocally', () {
      test('sollte PDF lokal speichern', () async {
        // Arrange
        final testPdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        container.read(pdfGenerationProvider.notifier).state = AsyncValue.data(
          testPdfBytes,
        );

        when(
          mockService.savePdfLocally(any, any),
        ).thenAnswer((_) async => '/path/to/test.pdf');

        final notifier = container.read(pdfGenerationProvider.notifier);

        // Act
        final result = await notifier.savePdfLocally('test.pdf');

        // Assert
        expect(result, equals('/path/to/test.pdf'));
        verify(mockService.savePdfLocally(testPdfBytes, 'test.pdf')).called(1);
      });
    });
  });

  group('PdfAutoSyncNotifier', () {
    late ProviderContainer container;
    late UserProfile testProfile;
    late CvTemplate testTemplate;

    setUp(() {
      testProfile = UserProfile(
        id: 'test-user-id',
        firstName: 'Max',
        lastName: 'Mustermann',
        email: '<EMAIL>',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testTemplate = CvTemplate(
        id: 'classic_blue',
        name: 'Klassisch Blau',
        description: 'Klassisches Design in Blau',
        type: CvTemplateType.classic,
        isPremium: false,
        previewImageUrl: 'https://example.com/preview.jpg',
        primaryColor: '#2196F3',
        secondaryColor: '#1976D2',
        accentColor: '#FFC107',
        fontFamily: 'Roboto',
        layout: CvTemplateLayout.singleColumn,
      );

      container = ProviderContainer(
        overrides: [
          userProfileProvider.overrideWith(
            (ref) => AsyncValue.data(testProfile),
          ),
          selectedTemplateProvider.overrideWith((ref) => testTemplate),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('sollte Auto-Sync aktivieren/deaktivieren', () {
      // Arrange
      final autoSync = container.read(pdfAutoSyncProvider);

      // Act & Assert
      expect(autoSync.isAutoSyncEnabled, false);

      autoSync.setAutoSyncEnabled(true);
      expect(autoSync.isAutoSyncEnabled, true);

      autoSync.setAutoSyncEnabled(false);
      expect(autoSync.isAutoSyncEnabled, false);
    });

    test('sollte manuellen Sync durchführen', () async {
      // Arrange
      final autoSync = container.read(pdfAutoSyncProvider);
      autoSync.setAutoSyncEnabled(true);

      // Act
      await autoSync.forceSyncNow();

      // Assert
      // Test dass kein Fehler auftritt
      expect(autoSync.isAutoSyncEnabled, true);
    });
  });
}

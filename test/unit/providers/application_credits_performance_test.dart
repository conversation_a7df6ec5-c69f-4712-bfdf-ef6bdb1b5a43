import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';

// Mock-Klassen für Tests
class MockSubscriptionManagementService extends Mock implements SubscriptionManagementService {}

void main() {
  group('⚡ PERFORMANCE UND RACE CONDITION TESTS', () {
    late MockSubscriptionManagementService mockSubscriptionService;

    setUp(() {
      mockSubscriptionService = MockSubscriptionManagementService();
    });

    group('🏃‍♂️ PERFORMANCE TESTS', () {
      test('Auto-Refresh Performance bei häufigen Aufrufen (30s Intervall)', () async {
        // Arrange
        var callCount = 0;
        final stopwatch = Stopwatch()..start();
        final callTimes = <int>[];

        when(mockSubscriptionService.getRemainingApplications())
            .thenAnswer((_) async {
          callCount++;
          callTimes.add(stopwatch.elapsedMilliseconds);
          
          // Simuliere variable Antwortzeiten
          final delay = Random().nextInt(100) + 50; // 50-150ms
          await Future.delayed(Duration(milliseconds: delay));
          
          return {
            'total': 5,
            'remaining': 5 - (callCount % 6), // Zyklisch 5,4,3,2,1,0,5...
            'unlimited': false,
            'plan_type': 'free',
            'used': callCount % 6,
            'call_number': callCount,
            'response_time_ms': delay,
          };
        });

        // Act - Simuliere 10 Auto-Refresh Aufrufe
        final futures = <Future<Map<String, dynamic>>>[];
        for (int i = 0; i < 10; i++) {
          futures.add(mockSubscriptionService.getRemainingApplications());
          await Future.delayed(const Duration(milliseconds: 100)); // Stagger calls
        }

        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert
        expect(results.length, equals(10));
        expect(callCount, equals(10));
        
        // Performance-Metriken
        final totalTime = stopwatch.elapsedMilliseconds;
        final averageResponseTime = totalTime / callCount;
        
        expect(averageResponseTime, lessThan(500)); // Durchschnitt unter 500ms
        expect(totalTime, lessThan(5000)); // Gesamt unter 5 Sekunden
        
        // Prüfe, dass alle Aufrufe erfolgreich waren
        for (final result in results) {
          expect(result['call_number'], isNotNull);
          expect(result['response_time_ms'], lessThan(200));
        }

        verify(mockSubscriptionService.getRemainingApplications()).called(10);
      });

      test('Speicher-Performance bei vielen gleichzeitigen Providern', () async {
        // Arrange - Simuliere viele Provider-Instanzen
        final services = List.generate(50, (_) => MockSubscriptionManagementService());
        
        for (final service in services) {
          when(service.getRemainingApplications())
              .thenAnswer((_) async => {
            'total': 5,
            'remaining': Random().nextInt(6),
            'unlimited': false,
            'plan_type': 'free',
            'used': Random().nextInt(6),
            'provider_id': service.hashCode,
          });
        }

        // Act - Alle Services gleichzeitig aufrufen
        final stopwatch = Stopwatch()..start();
        final futures = services.map((service) => service.getRemainingApplications());
        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert
        expect(results.length, equals(50));
        expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // Unter 2 Sekunden
        
        // Prüfe, dass alle Provider unterschiedliche IDs haben
        final providerIds = results.map((r) => r['provider_id']).toSet();
        expect(providerIds.length, equals(50)); // Alle unique
      });

      test('Datenbankabfrage-Optimierung bei wiederholten Aufrufen', () async {
        // Arrange - Simuliere Caching-Verhalten
        var dbCallCount = 0;
        Map<String, dynamic>? cachedResult;
        DateTime? lastCacheTime;

        when(mockSubscriptionService.getRemainingApplications())
            .thenAnswer((_) async {
          final now = DateTime.now();
          
          // Simuliere Cache-Logik (5 Sekunden Cache)
          if (cachedResult != null && 
              lastCacheTime != null && 
              now.difference(lastCacheTime!).inSeconds < 5) {
            return {
              ...cachedResult!,
              'from_cache': true,
              'cache_age_seconds': now.difference(lastCacheTime!).inSeconds,
            };
          }
          
          // Simuliere DB-Aufruf
          dbCallCount++;
          await Future.delayed(const Duration(milliseconds: 100)); // DB-Latenz
          
          cachedResult = {
            'total': 5,
            'remaining': 3,
            'unlimited': false,
            'plan_type': 'free',
            'used': 2,
            'db_call_number': dbCallCount,
            'from_cache': false,
          };
          lastCacheTime = now;
          
          return cachedResult!;
        });

        // Act - 10 Aufrufe in kurzer Zeit
        final results = <Map<String, dynamic>>[];
        for (int i = 0; i < 10; i++) {
          final result = await mockSubscriptionService.getRemainingApplications();
          results.add(result);
          await Future.delayed(const Duration(milliseconds: 200));
        }

        // Assert
        expect(results.length, equals(10));
        
        // Erste Aufrufe sollten aus Cache kommen
        final cachedResults = results.where((r) => r['from_cache'] == true).length;
        final dbResults = results.where((r) => r['from_cache'] == false).length;
        
        expect(dbResults, lessThan(10)); // Nicht alle Aufrufe gehen an DB
        expect(cachedResults, greaterThan(0)); // Mindestens ein Cache-Hit
        expect(dbCallCount, lessThan(10)); // Weniger DB-Aufrufe als Requests
      });
    });

    group('🔄 RACE CONDITION TESTS', () {
      test('Gleichzeitige Bewerbungsdekrementierung (Critical Section)', () async {
        // Arrange - Simuliere Race Condition bei Counter-Updates
        var currentRemaining = 5;
        var updateCount = 0;
        final updateLog = <Map<String, dynamic>>[];

        when(mockSubscriptionService.incrementApplicationCounter())
            .thenAnswer((_) async {
          // Simuliere Race Condition
          final beforeUpdate = currentRemaining;
          await Future.delayed(Duration(milliseconds: Random().nextInt(50))); // Variable Latenz
          
          if (currentRemaining > 0) {
            currentRemaining--;
            updateCount++;
            
            updateLog.add({
              'update_number': updateCount,
              'before': beforeUpdate,
              'after': currentRemaining,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
              'success': true,
            });
            
            return true;
          } else {
            updateLog.add({
              'update_number': updateCount + 1,
              'before': beforeUpdate,
              'after': currentRemaining,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
              'success': false,
              'reason': 'no_remaining_applications',
            });
            
            return false;
          }
        });

        // Act - 10 gleichzeitige Bewerbungen
        final futures = List.generate(10, (_) => 
          mockSubscriptionService.incrementApplicationCounter()
        );
        final results = await Future.wait(futures);

        // Assert
        final successfulApplications = results.where((r) => r == true).length;
        final failedApplications = results.where((r) => r == false).length;
        
        expect(successfulApplications, equals(5)); // Nur 5 sollten erfolgreich sein
        expect(failedApplications, equals(5)); // 5 sollten fehlschlagen
        expect(currentRemaining, equals(0)); // Counter sollte bei 0 sein
        expect(updateLog.length, equals(10)); // Alle Updates geloggt
        
        // Prüfe chronologische Reihenfolge der Updates
        for (int i = 1; i < updateLog.length; i++) {
          expect(
            updateLog[i]['timestamp'], 
            greaterThanOrEqualTo(updateLog[i-1]['timestamp'])
          );
        }

        verify(mockSubscriptionService.incrementApplicationCounter()).called(10);
      });

      test('Concurrent Provider State Updates', () async {
        // Arrange - Simuliere mehrere Provider, die gleichzeitig State aktualisieren
        final providers = List.generate(5, (index) => MockSubscriptionManagementService());
        var globalCounter = 10;
        final stateUpdates = <Map<String, dynamic>>[];

        for (int i = 0; i < providers.length; i++) {
          final provider = providers[i];
          when(provider.getRemainingApplications())
              .thenAnswer((_) async {
            await Future.delayed(Duration(milliseconds: Random().nextInt(100)));
            
            final currentState = {
              'provider_id': i,
              'total': 10,
              'remaining': globalCounter,
              'unlimited': false,
              'plan_type': 'free',
              'used': 10 - globalCounter,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
            };
            
            stateUpdates.add(currentState);
            return currentState;
          });

          when(provider.incrementApplicationCounter())
              .thenAnswer((_) async {
            if (globalCounter > 0) {
              globalCounter--;
              return true;
            }
            return false;
          });
        }

        // Act - Alle Provider gleichzeitig State abrufen und Updates machen
        final stateFutures = providers.map((p) => p.getRemainingApplications());
        final incrementFutures = providers.map((p) => p.incrementApplicationCounter());
        
        final states = await Future.wait(stateFutures);
        final increments = await Future.wait(incrementFutures);

        // Assert
        expect(states.length, equals(5));
        expect(increments.length, equals(5));
        expect(stateUpdates.length, equals(5));
        
        // Prüfe, dass alle Provider unterschiedliche IDs haben
        final providerIds = states.map((s) => s['provider_id']).toSet();
        expect(providerIds.length, equals(5));
        
        // Prüfe Race Condition Auswirkungen
        final successfulIncrements = increments.where((i) => i == true).length;
        expect(successfulIncrements, lessThanOrEqualTo(5));
        expect(globalCounter, equals(10 - successfulIncrements));
      });

      test('Timer-basierte Auto-Refresh Race Conditions', () async {
        // Arrange - Simuliere überlappende Timer-Aufrufe
        var activeRequests = 0;
        var completedRequests = 0;
        final requestLog = <Map<String, dynamic>>[];

        when(mockSubscriptionService.getRemainingApplications())
            .thenAnswer((_) async {
          final requestId = DateTime.now().millisecondsSinceEpoch;
          activeRequests++;
          
          requestLog.add({
            'request_id': requestId,
            'started_at': DateTime.now().millisecondsSinceEpoch,
            'active_requests_at_start': activeRequests,
            'status': 'started',
          });

          // Simuliere variable Bearbeitungszeit
          final processingTime = Random().nextInt(200) + 100; // 100-300ms
          await Future.delayed(Duration(milliseconds: processingTime));
          
          activeRequests--;
          completedRequests++;
          
          requestLog.add({
            'request_id': requestId,
            'completed_at': DateTime.now().millisecondsSinceEpoch,
            'processing_time_ms': processingTime,
            'status': 'completed',
          });

          return {
            'total': 5,
            'remaining': Random().nextInt(6),
            'unlimited': false,
            'plan_type': 'free',
            'used': Random().nextInt(6),
            'request_id': requestId,
            'processing_time_ms': processingTime,
          };
        });

        // Act - Simuliere überlappende Timer-Aufrufe
        final futures = <Future<Map<String, dynamic>>>[];
        for (int i = 0; i < 8; i++) {
          futures.add(mockSubscriptionService.getRemainingApplications());
          await Future.delayed(const Duration(milliseconds: 50)); // Überlappung
        }

        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(8));
        expect(completedRequests, equals(8));
        expect(activeRequests, equals(0)); // Alle Requests abgeschlossen
        
        // Prüfe, dass es Überlappungen gab
        final startedRequests = requestLog.where((r) => r['status'] == 'started');
        final maxConcurrentRequests = startedRequests
            .map((r) => r['active_requests_at_start'] as int)
            .reduce((a, b) => a > b ? a : b);
        
        expect(maxConcurrentRequests, greaterThan(1)); // Überlappung nachgewiesen
        
        verify(mockSubscriptionService.getRemainingApplications()).called(8);
      });
    });

    group('📊 LOAD TESTING', () {
      test('High-Load Szenario: 100 gleichzeitige Benutzer', () async {
        // Arrange
        final services = List.generate(100, (_) => MockSubscriptionManagementService());
        final results = <Map<String, dynamic>>[];
        var totalDbCalls = 0;

        for (final service in services) {
          when(service.getRemainingApplications())
              .thenAnswer((_) async {
            totalDbCalls++;
            await Future.delayed(Duration(milliseconds: Random().nextInt(100) + 50));
            
            return {
              'total': 5,
              'remaining': Random().nextInt(6),
              'unlimited': false,
              'plan_type': 'free',
              'used': Random().nextInt(6),
              'user_id': service.hashCode,
              'load_test': true,
            };
          });
        }

        // Act
        final stopwatch = Stopwatch()..start();
        final futures = services.map((s) => s.getRemainingApplications());
        final loadResults = await Future.wait(futures);
        stopwatch.stop();

        // Assert
        expect(loadResults.length, equals(100));
        expect(totalDbCalls, equals(100));
        expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // Unter 10 Sekunden
        
        // Performance-Metriken
        final averageResponseTime = stopwatch.elapsedMilliseconds / 100;
        expect(averageResponseTime, lessThan(200)); // Durchschnitt unter 200ms
        
        // Prüfe, dass alle Benutzer unterschiedliche IDs haben
        final userIds = loadResults.map((r) => r['user_id']).toSet();
        expect(userIds.length, equals(100));
      });
    });
  });
}

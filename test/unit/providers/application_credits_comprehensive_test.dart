import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';

// Mock-Klassen für Tests
class MockSubscriptionManagementService extends Mock
    implements SubscriptionManagementService {}

void main() {
  group('🎯 UMFASSENDE BEWERBUNGS-GUTHABEN-SYSTEM TESTS', () {
    late MockSubscriptionManagementService mockSubscriptionService;

    // Test-Konstanten
    const testUserId = 'test-user-12345';
    const freeUserId = 'free-user-67890';
    const premiumUserId = 'premium-user-abcde';

    setUp(() {
      mockSubscriptionService = MockSubscriptionManagementService();
      reset(mockSubscriptionService); // Reset alle vorherigen Stubs
    });

    group('✅ POSITIVE SZENARIEN', () {
      test(
        'Erfolgreiche Abfrage verbleibender Bewerbungen für Free-User (5 Bewerbungen)',
        () async {
          // Arrange
          final expectedData = {
            'total': 5,
            'remaining': 5,
            'unlimited': false,
            'plan_type': 'free',
            'used': 0,
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => expectedData);

          // Act
          final result =
              await mockSubscriptionService.getRemainingApplications();

          // Assert
          expect(result, equals(expectedData));
          expect(result['total'], equals(5));
          expect(result['remaining'], equals(5));
          expect(result['unlimited'], isFalse);
          expect(result['plan_type'], equals('free'));

          verify(mockSubscriptionService.getRemainingApplications()).called(1);
        },
      );

      test('Premium-User mit unbegrenzten Bewerbungen', () async {
        // Arrange
        final expectedData = {
          'total': -1,
          'remaining': -1,
          'unlimited': true,
          'plan_type': 'premium',
          'used': 0,
        };

        when(
          mockSubscriptionService.getRemainingApplications(),
        ).thenAnswer((_) async => expectedData);

        // Act
        final result = await mockSubscriptionService.getRemainingApplications();

        // Assert
        expect(result['unlimited'], isTrue);
        expect(result['plan_type'], equals('premium'));
        expect(result['total'], equals(-1));
        expect(result['remaining'], equals(-1));

        verify(mockSubscriptionService.getRemainingApplications()).called(1);
      });

      test(
        'Erfolgreiche Bewerbungsdekrementierung nach Job-Bewerbung',
        () async {
          // Arrange - Initial 5 Bewerbungen
          final initialData = {
            'total': 5,
            'remaining': 5,
            'unlimited': false,
            'plan_type': 'free',
            'used': 0,
          };

          final afterDecrementData = {
            'total': 5,
            'remaining': 4,
            'unlimited': false,
            'plan_type': 'free',
            'used': 1,
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => initialData);

          // Act - Initial Load
          final initialResult =
              await mockSubscriptionService.getRemainingApplications();
          expect(initialResult['remaining'], equals(5));

          // Simulate application decrement
          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => afterDecrementData);

          final afterResult =
              await mockSubscriptionService.getRemainingApplications();

          // Assert
          expect(afterResult['remaining'], equals(4));
          expect(afterResult['used'], equals(1));
          expect(afterResult['total'], equals(5));

          verify(mockSubscriptionService.getRemainingApplications()).called(2);
        },
      );

      test(
        'Korrekte Berechnung des individuellen 7-Tage-Reset-Zyklus',
        () async {
          // Arrange
          final userCreatedAt = DateTime.now().subtract(
            const Duration(days: 3),
          );
          final expectedResetDate = userCreatedAt.add(const Duration(days: 7));

          final dataWithResetDate = {
            'total': 5,
            'remaining': 3,
            'unlimited': false,
            'plan_type': 'free',
            'used': 2,
            'reset_date': expectedResetDate.toIso8601String(),
            'next_reset_in_days': 4, // 7 - 3 = 4 Tage verbleibend
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => dataWithResetDate);

          // Act
          final result =
              await mockSubscriptionService.getRemainingApplications();

          // Assert
          expect(result['reset_date'], isNotNull);
          expect(result['next_reset_in_days'], equals(4));

          final resetDate = DateTime.parse(result['reset_date']);
          final timeDiff =
              resetDate.difference(expectedResetDate).inMinutes.abs();
          expect(timeDiff, lessThan(5)); // Maximal 5 Minuten Abweichung
        },
      );

      test('Korrekte getNextFreeResetDate Berechnung', () async {
        // Arrange
        final userCreatedAt = DateTime.now().subtract(const Duration(days: 2));
        final expectedResetDate = userCreatedAt.add(const Duration(days: 7));

        when(
          mockSubscriptionService.getNextFreeResetDate(),
        ).thenAnswer((_) async => expectedResetDate);

        // Act
        final result = await mockSubscriptionService.getNextFreeResetDate();

        // Assert
        expect(result, isNotNull);
        expect(result!.isAfter(DateTime.now()), isTrue);

        final daysUntilReset = result.difference(DateTime.now()).inDays;
        expect(daysUntilReset, equals(5)); // 7 - 2 = 5 Tage verbleibend

        verify(mockSubscriptionService.getNextFreeResetDate()).called(1);
      });
    });

    group('🔥 EDGE CASES UND GRENZFÄLLE', () {
      test('User ohne bestehenden Application Counter', () async {
        // Arrange - Service gibt Daten für neuen User zurück
        final newUserData = {
          'total': 5,
          'remaining': 5,
          'unlimited': false,
          'plan_type': 'free',
          'used': 0,
          'is_new_user': true,
          'counter_created': true,
        };

        when(
          mockSubscriptionService.getRemainingApplications(),
        ).thenAnswer((_) async => newUserData);

        // Act
        final result = await mockSubscriptionService.getRemainingApplications();

        // Assert
        expect(result['is_new_user'], isTrue);
        expect(result['counter_created'], isTrue);
        expect(result['total'], equals(5));
        expect(result['remaining'], equals(5));
      });

      test(
        'Reset-Datum in der Vergangenheit (automatische Korrektur)',
        () async {
          // Arrange - Reset-Datum liegt in der Vergangenheit
          final pastResetDate = DateTime.now().subtract(
            const Duration(days: 2),
          );
          final correctedResetDate = DateTime.now().add(
            const Duration(days: 5),
          );

          when(
            mockSubscriptionService.getNextFreeResetDate(),
          ).thenAnswer((_) async => correctedResetDate);

          when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
            (_) async => {
              'total': 5,
              'remaining': 5,
              'unlimited': false,
              'plan_type': 'free',
              'used': 0,
              'reset_date': correctedResetDate.toIso8601String(),
              'was_corrected': true,
              'original_reset_date': pastResetDate.toIso8601String(),
            },
          );

          // Act
          final resetDate =
              await mockSubscriptionService.getNextFreeResetDate();
          final result =
              await mockSubscriptionService.getRemainingApplications();

          // Assert
          expect(resetDate!.isAfter(DateTime.now()), isTrue);
          expect(result['was_corrected'], isTrue);

          final parsedResetDate = DateTime.parse(result['reset_date']);
          expect(parsedResetDate.isAfter(DateTime.now()), isTrue);
        },
      );

      test('User-Wechsel zwischen Free- und Premium-Plan', () async {
        // Arrange - Initial Free User
        final freeUserData = {
          'total': 5,
          'remaining': 3,
          'unlimited': false,
          'plan_type': 'free',
          'used': 2,
        };

        final premiumUserData = {
          'total': -1,
          'remaining': -1,
          'unlimited': true,
          'plan_type': 'premium',
          'used': 0,
          'upgraded_from': 'free',
        };

        when(
          mockSubscriptionService.getRemainingApplications(),
        ).thenAnswer((_) async => freeUserData);

        // Act - Initial Load als Free User
        final freeResult =
            await mockSubscriptionService.getRemainingApplications();
        expect(freeResult['plan_type'], equals('free'));
        expect(freeResult['unlimited'], isFalse);

        // Arrange - Upgrade zu Premium
        when(
          mockSubscriptionService.getRemainingApplications(),
        ).thenAnswer((_) async => premiumUserData);

        // Act - Reload nach Upgrade
        final premiumResult =
            await mockSubscriptionService.getRemainingApplications();

        // Assert
        expect(premiumResult['plan_type'], equals('premium'));
        expect(premiumResult['unlimited'], isTrue);
        expect(premiumResult['upgraded_from'], equals('free'));

        verify(mockSubscriptionService.getRemainingApplications()).called(2);
      });

      test('Sehr alte User-Accounts (> 1 Jahr)', () async {
        // Arrange
        final veryOldDate = DateTime.now().subtract(const Duration(days: 400));
        final nextResetDate = DateTime.now().add(const Duration(days: 3));

        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 2,
            'unlimited': false,
            'plan_type': 'free',
            'used': 3,
            'account_created': veryOldDate.toIso8601String(),
            'reset_date': nextResetDate.toIso8601String(),
            'is_legacy_user': true,
            'reset_cycles_completed': 57, // ~400 Tage / 7 Tage
          },
        );

        // Act
        final result = await mockSubscriptionService.getRemainingApplications();

        // Assert
        expect(result['is_legacy_user'], isTrue);
        expect(result['reset_cycles_completed'], greaterThan(50));

        final accountAge = DateTime.now().difference(veryOldDate).inDays;
        expect(accountAge, greaterThan(365));
      });

      test('User mit 0 verbleibenden Bewerbungen', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 0,
            'unlimited': false,
            'plan_type': 'free',
            'used': 5,
            'is_exhausted': true,
            'next_reset_in_hours': 48,
          },
        );

        // Act
        final result = await mockSubscriptionService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['is_exhausted'], isTrue);
        expect(result['used'], equals(5));
        expect(result['next_reset_in_hours'], equals(48));
      });

      test(
        'Gleichzeitige Zugriffe auf den Counter (Race Conditions)',
        () async {
          // Arrange
          var callCount = 0;
          when(
            mockSubscriptionService.incrementApplicationCounter(),
          ).thenAnswer((_) async {
            callCount++;
            // Simuliere Race Condition - jeder Aufruf reduziert um 1
            return callCount <= 5; // Erste 5 Aufrufe erfolgreich
          });

          // Act - Simuliere 10 gleichzeitige Bewerbungen
          final futures = List.generate(
            10,
            (_) => mockSubscriptionService.incrementApplicationCounter(),
          );
          final results = await Future.wait(futures);

          // Assert
          final successfulApplications = results.where((r) => r == true).length;
          expect(
            successfulApplications,
            equals(5),
          ); // Nur 5 sollten erfolgreich sein
          expect(callCount, equals(10)); // Alle 10 Aufrufe wurden gemacht

          verify(
            mockSubscriptionService.incrementApplicationCounter(),
          ).called(10);
        },
      );
    });
  });
}

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';

// Mock-Klassen für Tests
class MockSubscriptionManagementService extends Mock
    implements SubscriptionManagementService {}

class MockRef extends Mock implements Ref<Object?> {}

// Test-spezifische RemainingApplicationsNotifier für bessere Testbarkeit
class TestableRemainingApplicationsNotifier
    extends RemainingApplicationsNotifier {
  TestableRemainingApplicationsNotifier(
    super.subscriptionService,
    super.ref,
  );

  // Expose private method für Tests
  Future<void> testLoadRemainingApplications() async {
    await _loadRemainingApplications();
  }
}

void main() {
  group('🎯 UMFASSENDE BEWERBUNGS-GUTHABEN-SYSTEM TESTS', () {
    late MockSupabaseClient mockSupabase;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockSubscriptionManagementService mockSubscriptionService;
    late ProviderContainer container;
    late RemainingApplicationsNotifier notifier;

    // Test-Konstanten
    const testUserId = 'test-user-12345';
    const freeUserId = 'free-user-67890';
    const premiumUserId = 'premium-user-abcde';

    setUp(() {
      mockSupabase = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockSubscriptionService = MockSubscriptionManagementService();

      // Basic Auth Setup
      when(mockSupabase.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn(testUserId);

      // Container mit Mock-Overrides
      container = ProviderContainer(
        overrides: [
          subscriptionManagementServiceProvider.overrideWithValue(
            mockSubscriptionService,
          ),
        ],
      );

      // Erstelle einen Mock Ref
      final mockRef = MockRef();
      when(mockRef.listen(any, any)).thenReturn(null);

      notifier = RemainingApplicationsNotifier(
        mockSubscriptionService,
        mockRef,
      );
    });

    tearDown(() {
      notifier.dispose();
      container.dispose();
    });

    group('✅ POSITIVE SZENARIEN', () {
      test(
        'Erfolgreiche Abfrage verbleibender Bewerbungen für Free-User (5 Bewerbungen)',
        () async {
          // Arrange
          final expectedData = {
            'total': 5,
            'remaining': 5,
            'unlimited': false,
            'plan_type': 'free',
            'used': 0,
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => expectedData);

          // Act
          await notifier.loadRemainingApplications();

          // Assert
          expect(notifier.state.hasValue, isTrue);
          expect(notifier.state.value, equals(expectedData));
          expect(notifier.state.value!['total'], equals(5));
          expect(notifier.state.value!['remaining'], equals(5));
          expect(notifier.state.value!['unlimited'], isFalse);
          expect(notifier.state.value!['plan_type'], equals('free'));

          verify(mockSubscriptionService.getRemainingApplications()).called(1);
        },
      );

      test('Premium-User mit unbegrenzten Bewerbungen', () async {
        // Arrange
        final expectedData = {
          'total': -1,
          'remaining': -1,
          'unlimited': true,
          'plan_type': 'premium',
          'used': 0,
        };

        when(
          mockSubscriptionService.getRemainingApplications(),
        ).thenAnswer((_) async => expectedData);

        // Act
        await notifier.loadRemainingApplications();

        // Assert
        expect(notifier.state.hasValue, isTrue);
        expect(notifier.state.value!['unlimited'], isTrue);
        expect(notifier.state.value!['plan_type'], equals('premium'));
        expect(notifier.state.value!['total'], equals(-1));
        expect(notifier.state.value!['remaining'], equals(-1));

        verify(mockSubscriptionService.getRemainingApplications()).called(1);
      });

      test(
        'Erfolgreiche Bewerbungsdekrementierung nach Job-Bewerbung',
        () async {
          // Arrange - Initial 5 Bewerbungen
          final initialData = {
            'total': 5,
            'remaining': 5,
            'unlimited': false,
            'plan_type': 'free',
            'used': 0,
          };

          final afterDecrementData = {
            'total': 5,
            'remaining': 4,
            'unlimited': false,
            'plan_type': 'free',
            'used': 1,
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => initialData);

          // Act - Initial Load
          await notifier.loadRemainingApplications();
          expect(notifier.state.value!['remaining'], equals(5));

          // Simulate application decrement
          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => afterDecrementData);

          await notifier.loadRemainingApplications();

          // Assert
          expect(notifier.state.value!['remaining'], equals(4));
          expect(notifier.state.value!['used'], equals(1));
          expect(notifier.state.value!['total'], equals(5));

          verify(mockSubscriptionService.getRemainingApplications()).called(2);
        },
      );

      test(
        'Korrekte Berechnung des individuellen 7-Tage-Reset-Zyklus',
        () async {
          // Arrange
          final userCreatedAt = DateTime.now().subtract(
            const Duration(days: 3),
          );
          final expectedResetDate = userCreatedAt.add(const Duration(days: 7));

          final dataWithResetDate = {
            'total': 5,
            'remaining': 3,
            'unlimited': false,
            'plan_type': 'free',
            'used': 2,
            'reset_date': expectedResetDate.toIso8601String(),
            'next_reset_in_days': 4, // 7 - 3 = 4 Tage verbleibend
          };

          when(
            mockSubscriptionService.getRemainingApplications(),
          ).thenAnswer((_) async => dataWithResetDate);

          // Act
          await notifier.loadRemainingApplications();

          // Assert
          expect(notifier.state.hasValue, isTrue);
          expect(notifier.state.value!['reset_date'], isNotNull);
          expect(notifier.state.value!['next_reset_in_days'], equals(4));

          final resetDate = DateTime.parse(notifier.state.value!['reset_date']);
          final timeDiff =
              resetDate.difference(expectedResetDate).inMinutes.abs();
          expect(timeDiff, lessThan(5)); // Maximal 5 Minuten Abweichung
        },
      );
    });

    group('🔄 AUTO-REFRESH TIMER TESTS', () {
      test('Auto-Refresh Timer wird korrekt gestartet und gestoppt', () async {
        // Arrange
        final completer = Completer<void>();
        var callCount = 0;

        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((
          _,
        ) async {
          callCount++;
          if (callCount >= 2) {
            completer.complete();
          }
          return {
            'total': 5,
            'remaining': 5 - callCount,
            'unlimited': false,
            'plan_type': 'free',
            'used': callCount,
          };
        });

        // Act - Timer sollte automatisch starten
        // Warte auf mindestens 2 Aufrufe (initial + 1 auto-refresh)
        await completer.future.timeout(const Duration(seconds: 35));

        // Assert
        expect(callCount, greaterThanOrEqualTo(2));

        // Dispose sollte Timer stoppen
        notifier.dispose();

        final callCountAfterDispose = callCount;
        await Future.delayed(const Duration(seconds: 32));

        // Nach dispose sollten keine weiteren Aufrufe erfolgen
        expect(callCount, equals(callCountAfterDispose));
      });
    });

    group('🔥 EDGE CASES UND GRENZFÄLLE', () {
      test('User ohne bestehenden Application Counter', () async {
        // Arrange - Service gibt leere Daten zurück
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 5,
            'unlimited': false,
            'plan_type': 'free',
            'used': 0,
            'is_new_user': true,
          },
        );

        // Act
        await notifier.loadRemainingApplications();

        // Assert
        expect(notifier.state.hasValue, isTrue);
        expect(notifier.state.value!['is_new_user'], isTrue);
        expect(notifier.state.value!['total'], equals(5));
        expect(notifier.state.value!['remaining'], equals(5));
      });

      test(
        'Reset-Datum in der Vergangenheit (automatische Korrektur)',
        () async {
          // Arrange - Reset-Datum liegt in der Vergangenheit
          final pastResetDate = DateTime.now().subtract(
            const Duration(days: 2),
          );
          final correctedResetDate = DateTime.now().add(
            const Duration(days: 5),
          );

          when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
            (_) async => {
              'total': 5,
              'remaining': 5,
              'unlimited': false,
              'plan_type': 'free',
              'used': 0,
              'reset_date': correctedResetDate.toIso8601String(),
              'was_corrected': true,
              'original_reset_date': pastResetDate.toIso8601String(),
            },
          );

          // Act
          await notifier.loadRemainingApplications();

          // Assert
          expect(notifier.state.hasValue, isTrue);
          expect(notifier.state.value!['was_corrected'], isTrue);

          final resetDate = DateTime.parse(notifier.state.value!['reset_date']);
          expect(resetDate.isAfter(DateTime.now()), isTrue);
        },
      );

      test('User-Wechsel zwischen Free- und Premium-Plan', () async {
        // Arrange - Initial Free User
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 3,
            'unlimited': false,
            'plan_type': 'free',
            'used': 2,
          },
        );

        // Act - Initial Load als Free User
        await notifier.loadRemainingApplications();
        expect(notifier.state.value!['plan_type'], equals('free'));
        expect(notifier.state.value!['unlimited'], isFalse);

        // Arrange - Upgrade zu Premium
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': -1,
            'remaining': -1,
            'unlimited': true,
            'plan_type': 'premium',
            'used': 0,
            'upgraded_from': 'free',
          },
        );

        // Act - Reload nach Upgrade
        await notifier.loadRemainingApplications();

        // Assert
        expect(notifier.state.value!['plan_type'], equals('premium'));
        expect(notifier.state.value!['unlimited'], isTrue);
        expect(notifier.state.value!['upgraded_from'], equals('free'));
      });

      test('Sehr alte User-Accounts (> 1 Jahr)', () async {
        // Arrange
        final veryOldDate = DateTime.now().subtract(const Duration(days: 400));
        final nextResetDate = DateTime.now().add(const Duration(days: 3));

        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 2,
            'unlimited': false,
            'plan_type': 'free',
            'used': 3,
            'account_created': veryOldDate.toIso8601String(),
            'reset_date': nextResetDate.toIso8601String(),
            'is_legacy_user': true,
            'reset_cycles_completed': 57, // ~400 Tage / 7 Tage
          },
        );

        // Act
        await notifier.loadRemainingApplications();

        // Assert
        expect(notifier.state.hasValue, isTrue);
        expect(notifier.state.value!['is_legacy_user'], isTrue);
        expect(
          notifier.state.value!['reset_cycles_completed'],
          greaterThan(50),
        );

        final accountAge = DateTime.now().difference(veryOldDate).inDays;
        expect(accountAge, greaterThan(365));
      });

      test('User mit 0 verbleibenden Bewerbungen', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer(
          (_) async => {
            'total': 5,
            'remaining': 0,
            'unlimited': false,
            'plan_type': 'free',
            'used': 5,
            'is_exhausted': true,
            'next_reset_in_hours': 48,
          },
        );

        // Act
        await notifier.loadRemainingApplications();

        // Assert
        expect(notifier.state.hasValue, isTrue);
        expect(notifier.state.value!['remaining'], equals(0));
        expect(notifier.state.value!['is_exhausted'], isTrue);
        expect(notifier.state.value!['used'], equals(5));
        expect(notifier.state.value!['next_reset_in_hours'], equals(48));
      });
    });
  });
}

// Extension für bessere Testbarkeit
extension RemainingApplicationsNotifierTestExtension
    on RemainingApplicationsNotifier {
  Future<void> loadRemainingApplications() async {
    await _loadRemainingApplications();
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';

// Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockSubscriptionManagementService extends Mock implements SubscriptionManagementService {}

void main() {
  group('SubscriptionManagementProvider Tests', () {
    late ProviderContainer container;
    late MockSupabaseClient mockSupabaseClient;
    late MockSubscriptionManagementService mockSubscriptionService;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockSubscriptionService = MockSubscriptionManagementService();

      container = ProviderContainer(
        overrides: [
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          subscriptionManagementServiceProvider.overrideWithValue(mockSubscriptionService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Verbleibende Bewerbungen Provider', () {
      test('sollte verbleibende Bewerbungen für Basic-Plan korrekt laden', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 25,
          'total': 30,
          'unlimited': false,
          'plan_type': 'basic',
        });

        // Act
        final result = await container.read(remainingApplicationsProvider.future);

        // Assert
        expect(result['remaining'], equals(25));
        expect(result['total'], equals(30));
        expect(result['unlimited'], false);
        expect(result['plan_type'], equals('basic'));
      });

      test('sollte verbleibende Bewerbungen für Pro-Plan korrekt laden', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 120,
          'total': 150,
          'unlimited': false,
          'plan_type': 'pro',
        });

        // Act
        final result = await container.read(remainingApplicationsProvider.future);

        // Assert
        expect(result['remaining'], equals(120));
        expect(result['total'], equals(150));
        expect(result['unlimited'], false);
        expect(result['plan_type'], equals('pro'));
      });

      test('sollte Unlimited-Plan korrekt behandeln', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': -1,
          'total': -1,
          'unlimited': true,
          'plan_type': 'unlimited',
        });

        // Act
        final result = await container.read(remainingApplicationsProvider.future);

        // Assert
        expect(result['remaining'], equals(-1));
        expect(result['total'], equals(-1));
        expect(result['unlimited'], true);
        expect(result['plan_type'], equals('unlimited'));
      });

      test('sollte Fehler bei Service-Aufruf korrekt behandeln', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications())
            .thenThrow(Exception('Service error'));

        // Act & Assert
        final asyncValue = container.read(remainingApplicationsProvider);
        expect(asyncValue, isA<AsyncError>());
      });

      test('sollte 0 Bewerbungen für Free-Plan zurückgeben', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 0,
          'total': 0,
          'unlimited': false,
          'plan_type': 'free',
        });

        // Act
        final result = await container.read(remainingApplicationsProvider.future);

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
        expect(result['plan_type'], equals('free'));
      });
    });

    group('Nächstes Reset-Datum Provider', () {
      test('sollte nächstes Reset-Datum korrekt laden', () async {
        // Arrange
        final nextResetDate = DateTime.now().add(Duration(days: 15));
        when(mockSubscriptionService.getNextFreeResetDate())
            .thenAnswer((_) async => nextResetDate);

        // Act
        final result = await container.read(nextFreeResetDateProvider.future);

        // Assert
        expect(result, equals(nextResetDate));
      });

      test('sollte null für Unlimited-Plan zurückgeben', () async {
        // Arrange
        when(mockSubscriptionService.getNextFreeResetDate())
            .thenAnswer((_) async => null);

        // Act
        final result = await container.read(nextFreeResetDateProvider.future);

        // Assert
        expect(result, isNull);
      });

      test('sollte Fehler bei Reset-Datum Abfrage behandeln', () async {
        // Arrange
        when(mockSubscriptionService.getNextFreeResetDate())
            .thenThrow(Exception('Database error'));

        // Act & Assert
        final asyncValue = container.read(nextFreeResetDateProvider);
        expect(asyncValue, isA<AsyncError>());
      });
    });

    group('Premium-Status Provider', () {
      test('sollte Premium-Status true für aktives Abonnement zurückgeben', () async {
        // Arrange
        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => true);

        // Act
        final result = await container.read(hasPremiumSubscriptionProvider.future);

        // Assert
        expect(result, true);
      });

      test('sollte Premium-Status false für Free-Plan zurückgeben', () async {
        // Arrange
        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => false);

        // Act
        final result = await container.read(hasPremiumSubscriptionProvider.future);

        // Assert
        expect(result, false);
      });

      test('sollte Fehler bei Premium-Status Abfrage behandeln', () async {
        // Arrange
        when(mockSubscriptionService.hasPremiumSubscription())
            .thenThrow(Exception('Service error'));

        // Act & Assert
        final asyncValue = container.read(hasPremiumSubscriptionProvider);
        expect(asyncValue, isA<AsyncError>());
      });
    });

    group('Provider Invalidierung und Refresh', () {
      test('sollte Provider nach Abonnement-Änderung invalidieren', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 30,
          'total': 30,
          'unlimited': false,
          'plan_type': 'basic',
        });

        // Act - Erste Abfrage
        var result = await container.read(remainingApplicationsProvider.future);
        expect(result['plan_type'], equals('basic'));

        // Simuliere Upgrade zu Pro
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 150,
          'total': 150,
          'unlimited': false,
          'plan_type': 'pro',
        });

        // Invalidiere Provider
        container.invalidate(remainingApplicationsProvider);

        // Act - Zweite Abfrage nach Invalidierung
        result = await container.read(remainingApplicationsProvider.future);

        // Assert
        expect(result['plan_type'], equals('pro'));
        expect(result['total'], equals(150));
      });

      test('sollte Premium-Status nach Promo-Code Einlösung aktualisieren', () async {
        // Arrange
        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => false);

        // Act - Erste Abfrage (kein Premium)
        var result = await container.read(hasPremiumSubscriptionProvider.future);
        expect(result, false);

        // Simuliere Promo-Code Einlösung
        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => true);

        // Invalidiere Provider
        container.invalidate(hasPremiumSubscriptionProvider);

        // Act - Zweite Abfrage nach Promo-Code
        result = await container.read(hasPremiumSubscriptionProvider.future);

        // Assert
        expect(result, true);
      });
    });

    group('Provider Kombinationen und Abhängigkeiten', () {
      test('sollte konsistente Daten zwischen verschiedenen Providern liefern', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 100,
          'total': 150,
          'unlimited': false,
          'plan_type': 'pro',
        });

        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => true);

        when(mockSubscriptionService.getNextFreeResetDate())
            .thenAnswer((_) async => DateTime.now().add(Duration(days: 20)));

        // Act
        final remainingApps = await container.read(remainingApplicationsProvider.future);
        final hasPremium = await container.read(hasPremiumSubscriptionProvider.future);
        final nextReset = await container.read(nextFreeResetDateProvider.future);

        // Assert - Pro-Plan sollte Premium sein und Reset-Datum haben
        expect(remainingApps['plan_type'], equals('pro'));
        expect(remainingApps['total'], equals(150));
        expect(hasPremium, true);
        expect(nextReset, isNotNull);
      });

      test('sollte konsistente Daten für Unlimited-Plan liefern', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': -1,
          'total': -1,
          'unlimited': true,
          'plan_type': 'unlimited',
        });

        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => true);

        when(mockSubscriptionService.getNextFreeResetDate())
            .thenAnswer((_) async => null); // Kein Reset für Unlimited

        // Act
        final remainingApps = await container.read(remainingApplicationsProvider.future);
        final hasPremium = await container.read(hasPremiumSubscriptionProvider.future);
        final nextReset = await container.read(nextFreeResetDateProvider.future);

        // Assert - Unlimited sollte Premium sein und kein Reset-Datum haben
        expect(remainingApps['unlimited'], true);
        expect(remainingApps['plan_type'], equals('unlimited'));
        expect(hasPremium, true);
        expect(nextReset, isNull);
      });

      test('sollte konsistente Daten für Free-Plan liefern', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 0,
          'total': 0,
          'unlimited': false,
          'plan_type': 'free',
        });

        when(mockSubscriptionService.hasPremiumSubscription())
            .thenAnswer((_) async => false);

        when(mockSubscriptionService.getNextFreeResetDate())
            .thenAnswer((_) async => null); // Kein Reset für Free

        // Act
        final remainingApps = await container.read(remainingApplicationsProvider.future);
        final hasPremium = await container.read(hasPremiumSubscriptionProvider.future);
        final nextReset = await container.read(nextFreeResetDateProvider.future);

        // Assert - Free sollte kein Premium sein
        expect(remainingApps['plan_type'], equals('free'));
        expect(remainingApps['total'], equals(0));
        expect(hasPremium, false);
        expect(nextReset, isNull);
      });
    });

    group('Performance und Caching', () {
      test('sollte Provider-Ergebnisse cachen', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 100,
          'total': 150,
          'unlimited': false,
          'plan_type': 'pro',
        });

        // Act - Mehrfache Abfragen
        final result1 = await container.read(remainingApplicationsProvider.future);
        final result2 = await container.read(remainingApplicationsProvider.future);
        final result3 = await container.read(remainingApplicationsProvider.future);

        // Assert - Service sollte nur einmal aufgerufen werden (Caching)
        expect(result1, equals(result2));
        expect(result2, equals(result3));
        verify(mockSubscriptionService.getRemainingApplications()).called(1);
      });

      test('sollte Provider nach Invalidierung neu laden', () async {
        // Arrange
        when(mockSubscriptionService.getRemainingApplications()).thenAnswer((_) async => {
          'remaining': 100,
          'total': 150,
          'unlimited': false,
          'plan_type': 'pro',
        });

        // Act - Erste Abfrage
        await container.read(remainingApplicationsProvider.future);

        // Invalidiere und frage erneut ab
        container.invalidate(remainingApplicationsProvider);
        await container.read(remainingApplicationsProvider.future);

        // Assert - Service sollte zweimal aufgerufen werden
        verify(mockSubscriptionService.getRemainingApplications()).called(2);
      });
    });
  });
}

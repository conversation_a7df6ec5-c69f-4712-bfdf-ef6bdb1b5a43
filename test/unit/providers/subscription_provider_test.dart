import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/application/providers/subscription_provider.dart';
import 'package:ki_test/src/application/providers/services_providers.dart';
import 'package:ki_test/src/domain/models/subscription.dart';

// Vereinfachte Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockGoTrueClient extends Mock implements GoTrueClient {}

class MockUser extends Mock implements User {}

void main() {
  group('SubscriptionNotifier Integration Tests', () {
    late ProviderContainer container;
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');

      container = ProviderContainer(
        overrides: [
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Initialisierung', () {
      test('sollte Provider ohne Fehler initialisieren', () {
        // Act
        final provider = container.read(subscriptionProvider);

        // Assert
        expect(provider, isA<AsyncValue>());
      });

      test('sollte Loading-State initial haben', () {
        // Act
        final provider = container.read(subscriptionProvider);

        // Assert
        expect(provider.isLoading, true);
      });

      test('sollte mit null-User korrekt umgehen', () {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        final containerWithoutUser = ProviderContainer(
          overrides: [
            supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          ],
        );

        // Act
        final provider = containerWithoutUser.read(subscriptionProvider);

        // Assert
        expect(provider, isA<AsyncValue>());

        containerWithoutUser.dispose();
      });

      test('sollte abgelaufenes Abonnement erkennen', () async {
        // Arrange
        final expiredSubscription = {
          'id': 'sub-expired',
          'user_id': 'user-123',
          'status': 'active',
          'product_id': 'pro_subscription',
          'platform': 'android',
          'expires_at':
              DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
          'created_at':
              DateTime.now().subtract(Duration(days: 31)).toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        final notifier = container.read(subscriptionProvider.notifier);

        // Act
        await notifier.loadSubscription('user-123');
        streamController.add([expiredSubscription]);
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        final state = container.read(subscriptionProvider);
        expect(state.hasValue, true);
        expect(state.value?.isActive, false);
        expect(state.value?.isExpired, true);
      });

      test('sollte gekündigtes Abonnement behandeln', () async {
        // Arrange
        final cancelledSubscription = {
          'id': 'sub-cancelled',
          'user_id': 'user-123',
          'status': 'cancelled',
          'product_id': 'pro_subscription',
          'platform': 'android',
          'expires_at':
              DateTime.now().add(Duration(days: 15)).toIso8601String(),
          'cancelled_at': DateTime.now().toIso8601String(),
          'created_at':
              DateTime.now().subtract(Duration(days: 15)).toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        final notifier = container.read(subscriptionProvider.notifier);

        // Act
        await notifier.loadSubscription('user-123');
        streamController.add([cancelledSubscription]);
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        final state = container.read(subscriptionProvider);
        expect(state.hasValue, true);
        expect(state.value?.status, equals('cancelled'));
        expect(state.value?.isActive, false);
        expect(state.value?.cancelledAt, isNotNull);
      });

      test('sollte null zurückgeben wenn kein Abonnement existiert', () async {
        // Arrange
        final notifier = container.read(subscriptionProvider.notifier);

        // Act
        await notifier.loadSubscription('user-without-subscription');
        streamController.add([]); // Leere Liste = kein Abonnement
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        final state = container.read(subscriptionProvider);
        expect(state.hasValue, true);
        expect(state.value, isNull);
      });
    });

    group('Echtzeit-Updates', () {
      test('sollte Abonnement-Änderungen in Echtzeit empfangen', () async {
        // Arrange
        final notifier = container.read(subscriptionProvider.notifier);
        await notifier.loadSubscription('user-123');

        final initialSubscription = {
          'id': 'sub-123',
          'user_id': 'user-123',
          'status': 'active',
          'product_id': 'basic_subscription',
          'platform': 'android',
          'expires_at':
              DateTime.now().add(Duration(days: 30)).toIso8601String(),
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        // Act - Erstes Update
        streamController.add([initialSubscription]);
        await Future.delayed(Duration(milliseconds: 100));

        var state = container.read(subscriptionProvider);
        expect(state.value?.productId, equals('basic_subscription'));

        // Act - Upgrade zu Pro
        final upgradedSubscription = {
          ...initialSubscription,
          'product_id': 'pro_subscription',
          'updated_at': DateTime.now().toIso8601String(),
        };

        streamController.add([upgradedSubscription]);
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        state = container.read(subscriptionProvider);
        expect(state.value?.productId, equals('pro_subscription'));
      });

      test('sollte Stream-Fehler korrekt behandeln', () async {
        // Arrange
        final notifier = container.read(subscriptionProvider.notifier);
        await notifier.loadSubscription('user-123');

        // Act
        streamController.addError(Exception('Netzwerk-Fehler'));
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        final state = container.read(subscriptionProvider);
        expect(state.hasError, true);
      });
    });

    group('Edge Cases', () {
      test('sollte ungültige JSON-Daten behandeln', () async {
        // Arrange
        final invalidSubscription = {
          'id': 'sub-invalid',
          'user_id': 'user-123',
          'status': 'active',
          'product_id': 'pro_subscription',
          'platform': 'android',
          'expires_at': 'invalid-date-format',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        final notifier = container.read(subscriptionProvider.notifier);

        // Act & Assert
        await notifier.loadSubscription('user-123');
        expect(
          () => streamController.add([invalidSubscription]),
          throwsA(isA<FormatException>()),
        );
      });

      test('sollte mehrere Abonnements korrekt behandeln', () async {
        // Arrange
        final multipleSubscriptions = [
          {
            'id': 'sub-1',
            'user_id': 'user-123',
            'status': 'cancelled',
            'product_id': 'basic_subscription',
            'platform': 'android',
            'expires_at':
                DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
            'created_at':
                DateTime.now().subtract(Duration(days: 60)).toIso8601String(),
            'updated_at':
                DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
          },
          {
            'id': 'sub-2',
            'user_id': 'user-123',
            'status': 'active',
            'product_id': 'pro_subscription',
            'platform': 'android',
            'expires_at':
                DateTime.now().add(Duration(days: 30)).toIso8601String(),
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
        ];

        final notifier = container.read(subscriptionProvider.notifier);

        // Act
        await notifier.loadSubscription('user-123');
        streamController.add(multipleSubscriptions);
        await Future.delayed(Duration(milliseconds: 100));

        // Assert - Sollte das erste (neueste) Abonnement verwenden
        final state = container.read(subscriptionProvider);
        expect(state.hasValue, true);
        expect(state.value?.id, equals('sub-1'));
      });
    });
  });
}

import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:ki_test/src/application/services/pdf_cv_generator_service.dart';
import 'package:ki_test/src/application/services/profile_image_service.dart';
import 'package:ki_test/src/domain/models/user_profile.dart';
import 'package:ki_test/src/domain/models/cv_template.dart';
import 'package:ki_test/src/domain/models/work_experience.dart';
import 'package:ki_test/src/domain/models/education.dart';

import 'pdf_cv_generator_service_test.mocks.dart';

@GenerateMocks([ProfileImageService])
void main() {
  group('PdfCvGeneratorService', () {
    late PdfCvGeneratorService service;
    late MockProfileImageService mockProfileImageService;
    late UserProfile testProfile;
    late CvTemplate testTemplate;

    setUp(() {
      mockProfileImageService = MockProfileImageService();
      service = PdfCvGeneratorService();
      
      // Test-Profil erstellen
      testProfile = UserProfile(
        id: 'test-user-id',
        firstName: 'Max',
        lastName: 'Mustermann',
        email: '<EMAIL>',
        phone: '+49 **********',
        address: 'Musterstraße 1, 12345 Musterstadt',
        experienceSummary: 'Erfahrener Softwareentwickler mit 5 Jahren Berufserfahrung.',
        skills: ['Flutter', 'Dart', 'Firebase', 'Git', 'Agile'],
        workExperience: [
          WorkExperience(
            id: '1',
            company: 'Tech GmbH',
            position: 'Senior Flutter Developer',
            startDate: DateTime(2020, 1, 1),
            endDate: DateTime(2023, 12, 31),
            description: 'Entwicklung von mobilen Apps mit Flutter',
            isCurrentJob: false,
          ),
        ],
        education: [
          Education(
            id: '1',
            institution: 'Universität Muster',
            degree: 'Bachelor of Science',
            fieldOfStudy: 'Informatik',
            startDate: DateTime(2016, 10, 1),
            endDate: DateTime(2020, 9, 30),
            description: 'Schwerpunkt: Softwareentwicklung',
          ),
        ],
        profileImageUrl: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test-Template erstellen
      testTemplate = CvTemplate(
        id: 'classic_blue',
        name: 'Klassisch Blau',
        description: 'Klassisches Design in Blau',
        type: CvTemplateType.classic,
        isPremium: false,
        previewImageUrl: 'https://example.com/preview.jpg',
        primaryColor: '#2196F3',
        secondaryColor: '#1976D2',
        accentColor: '#FFC107',
        fontFamily: 'Roboto',
        layout: CvTemplateLayout.singleColumn,
      );
    });

    group('generateCvPdf', () {
      test('sollte PDF erfolgreich generieren', () async {
        // Act
        final result = await service.generateCvPdf(testProfile, testTemplate);

        // Assert
        expect(result, isA<Uint8List>());
        expect(result.isNotEmpty, true);
        expect(result.length, greaterThan(1000)); // PDF sollte mindestens 1KB groß sein
      });

      test('sollte PDF mit allen Profildaten generieren', () async {
        // Act
        final result = await service.generateCvPdf(testProfile, testTemplate);

        // Assert
        expect(result, isA<Uint8List>());
        // PDF sollte generiert werden, auch wenn wir den Inhalt nicht direkt prüfen können
        expect(result.isNotEmpty, true);
      });

      test('sollte PDF mit leerem Profil generieren', () async {
        // Arrange
        final emptyProfile = UserProfile(
          id: 'empty-user',
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = await service.generateCvPdf(emptyProfile, testTemplate);

        // Assert
        expect(result, isA<Uint8List>());
        expect(result.isNotEmpty, true);
      });

      test('sollte PDF mit verschiedenen Templates generieren', () async {
        // Arrange
        final templates = [
          testTemplate,
          testTemplate.copyWith(
            id: 'modern_green',
            name: 'Modern Grün',
            type: CvTemplateType.modern,
          ),
          testTemplate.copyWith(
            id: 'creative_purple',
            name: 'Kreativ Lila',
            type: CvTemplateType.creative,
          ),
        ];

        // Act & Assert
        for (final template in templates) {
          final result = await service.generateCvPdf(testProfile, template);
          expect(result, isA<Uint8List>());
          expect(result.isNotEmpty, true);
        }
      });
    });

    group('sharePdf', () {
      test('sollte PDF erfolgreich teilen', () async {
        // Arrange
        final pdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        const fileName = 'test_cv.pdf';

        // Act & Assert
        expect(
          () => service.sharePdf(pdfBytes, fileName),
          returnsNormally,
        );
      });
    });

    group('savePdfLocally', () {
      test('sollte PDF lokal speichern', () async {
        // Arrange
        final pdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        const fileName = 'test_cv.pdf';

        // Act
        final filePath = await service.savePdfLocally(pdfBytes, fileName);

        // Assert
        expect(filePath, isA<String>());
        expect(filePath.isNotEmpty, true);
        expect(filePath.contains(fileName), true);
      });

      test('sollte eindeutige Dateinamen bei Konflikten erstellen', () async {
        // Arrange
        final pdfBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        const fileName = 'duplicate_cv.pdf';

        // Act
        final filePath1 = await service.savePdfLocally(pdfBytes, fileName);
        final filePath2 = await service.savePdfLocally(pdfBytes, fileName);

        // Assert
        expect(filePath1, isNot(equals(filePath2)));
        expect(filePath1.contains('duplicate_cv.pdf'), true);
        expect(filePath2.contains('duplicate_cv_1.pdf'), true);
      });
    });

    group('Performance Tests', () {
      test('sollte PDF in angemessener Zeit generieren', () async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await service.generateCvPdf(testProfile, testTemplate);

        // Assert
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Weniger als 5 Sekunden
      });

      test('sollte mehrere PDFs parallel generieren können', () async {
        // Arrange
        final futures = <Future<Uint8List>>[];
        const numberOfPdfs = 3;

        // Act
        for (int i = 0; i < numberOfPdfs; i++) {
          futures.add(service.generateCvPdf(testProfile, testTemplate));
        }

        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(numberOfPdfs));
        for (final result in results) {
          expect(result, isA<Uint8List>());
          expect(result.isNotEmpty, true);
        }
      });
    });

    group('Error Handling', () {
      test('sollte Fehler bei ungültigem Template behandeln', () async {
        // Arrange
        final invalidTemplate = testTemplate.copyWith(
          type: null, // Ungültiger Template-Typ
        );

        // Act & Assert
        expect(
          () => service.generateCvPdf(testProfile, invalidTemplate),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}

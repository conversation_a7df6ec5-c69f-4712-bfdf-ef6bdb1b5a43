import 'package:flutter_test/flutter_test.dart';
import 'package:ki_test/src/core/utils/device_manager.dart';

void main() {
  group('DeviceManager Tests', () {
    late DeviceManager deviceManager;

    setUp(() {
      deviceManager = DeviceManager();
    });

    group('Device ID Generation', () {
      test('getDeviceId sollte eine gültige UUID zurückgeben', () async {
        // Act
        final deviceId = await deviceManager.getDeviceId();

        // Assert
        expect(deviceId, isNotNull);
        expect(deviceId, isNotEmpty);
        expect(deviceId.length, equals(36)); // UUID v5 Format
        expect(deviceId.contains('-'), isTrue);
      });

      test('getDeviceId sollte konsistente IDs zurückgeben', () async {
        // Act
        final deviceId1 = await deviceManager.getDeviceId();
        final deviceId2 = await deviceManager.getDeviceId();

        // Assert
        expect(deviceId1, equals(deviceId2));
      });
    });

    group('Device Info Collection', () {
      test('getDeviceInfo sollte Platform-Informationen sammeln', () async {
        // Act
        final deviceInfo = await deviceManager.getDeviceInfo();

        // Assert
        expect(deviceInfo, isNotNull);
        expect(deviceInfo, isA<Map<String, dynamic>>());
        expect(deviceInfo.containsKey('platform'), isTrue);
        expect(deviceInfo.containsKey('timestamp'), isTrue);
        expect(deviceInfo['platform'], isNotNull);
        expect(deviceInfo['timestamp'], isNotNull);
      });

      test('getDeviceInfo sollte Timestamp im ISO8601 Format haben', () async {
        // Act
        final deviceInfo = await deviceManager.getDeviceInfo();

        // Assert
        final timestamp = deviceInfo['timestamp'] as String;
        expect(() => DateTime.parse(timestamp), returnsNormally);
      });
    });

    group('Local Storage', () {
      test('saveLastUserId und getLastUserId sollten funktionieren', () async {
        // Arrange
        const testUserId = 'test-user-123';

        // Act
        await deviceManager.saveLastUserId(testUserId);
        final retrievedUserId = await deviceManager.getLastUserId();

        // Assert
        expect(retrievedUserId, equals(testUserId));
      });

      test('getLastUserId sollte null zurückgeben wenn keine ID gespeichert', () async {
        // Act
        final userId = await deviceManager.getLastUserId();

        // Assert - könnte null oder empty sein, je nach Implementation
        expect(userId == null || userId.isEmpty, isTrue);
      });
    });

    group('Error Handling', () {
      test('DeviceManager sollte mit Fehlern umgehen können', () async {
        // Act & Assert - sollte keine Exceptions werfen
        expect(() async => await deviceManager.getDeviceId(), returnsNormally);
        expect(() async => await deviceManager.getDeviceInfo(), returnsNormally);
      });
    });
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Mock-Klassen für Promo-Code spezifische Tests
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockFunctionsClient extends Mock implements FunctionsClient {}
class MockFunctionResponse extends Mock implements FunctionResponse {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockUser extends Mock implements User {}

void main() {
  group('Promo Code System Tests', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockFunctionsClient mockFunctions;
    late MockFunctionResponse mockFunctionResponse;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockFunctions = MockFunctionsClient();
      mockFunctionResponse = MockFunctionResponse();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();

      when(mockSupabaseClient.functions).thenReturn(mockFunctions);
      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');
    });

    group('Promo-Code Validierung', () {
      test('sollte gültige Standard-Promo-Codes akzeptieren', () async {
        // Arrange
        final validCodes = [
          'WELCOME2025',
          'NEWYEAR2025', 
          'PREMIUM7',
          'UNLIMITED30',
          'TESTCODE',
          'CAREER7',
          'JOB7',
          'SUCCESS7'
        ];

        for (final code in validCodes) {
          when(mockFunctionResponse.status).thenReturn(200);
          when(mockFunctionResponse.data).thenReturn({
            'success': true,
            'isValid': true,
            'message': 'Promo Code erfolgreich eingelöst! Sie erhalten 7 Tage kostenlosen Premium-Zugang.',
            'redemption': {
              'id': 'redemption-123',
              'expires_at': DateTime.now().add(Duration(days: 7)).toIso8601String(),
            }
          });

          when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
              .thenAnswer((_) async => mockFunctionResponse);

          // Act
          final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
            'promo_code': code,
          });

          // Assert
          expect(response.status, equals(200));
          expect(response.data['success'], true);
          expect(response.data['isValid'], true);
        }
      });

      test('sollte ungültige Promo-Codes ablehnen', () async {
        // Arrange
        final invalidCodes = [
          'INVALID_CODE',
          'EXPIRED_CODE',
          'FAKE_CODE',
          '123456',
          'SHORT',
          ''
        ];

        for (final code in invalidCodes) {
          when(mockFunctionResponse.status).thenReturn(200);
          when(mockFunctionResponse.data).thenReturn({
            'success': false,
            'isValid': false,
            'message': 'Ungültiger Promo Code. Bitte überprüfen Sie die Eingabe.',
          });

          when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
              .thenAnswer((_) async => mockFunctionResponse);

          // Act
          final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
            'promo_code': code,
          });

          // Assert
          expect(response.status, equals(200));
          expect(response.data['success'], false);
          expect(response.data['isValid'], false);
        }
      });

      test('sollte bereits eingelöste Promo-Codes erkennen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code wurde bereits von Ihnen eingelöst.',
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'WELCOME2025',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], false);
        expect(response.data['message'], contains('bereits von Ihnen eingelöst'));
      });

      test('sollte abgelaufene Promo-Codes erkennen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code ist abgelaufen.',
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'EXPIRED_CODE',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], false);
        expect(response.data['message'], contains('abgelaufen'));
      });

      test('sollte Usage-Limit überschrittene Codes erkennen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code wurde bereits zu oft verwendet.',
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'LIMITED_CODE',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], false);
        expect(response.data['message'], contains('zu oft verwendet'));
      });
    });

    group('Promo-Code Einlösung', () {
      test('sollte Pro-Plan Promo-Code korrekt einlösen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst! Sie erhalten 7 Tage kostenlosen Premium-Zugang.',
          'redemption': {
            'id': 'redemption-123',
            'user_id': 'test-user-id',
            'promo_code': 'PREMIUM7',
            'code_type': 'pro',
            'duration_days': 7,
            'expires_at': DateTime.now().add(Duration(days: 7)).toIso8601String(),
            'status': 'active'
          }
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'PREMIUM7',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], true);
        expect(response.data['redemption']['code_type'], equals('pro'));
        expect(response.data['redemption']['duration_days'], equals(7));
      });

      test('sollte Unlimited-Plan Promo-Code korrekt einlösen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst! Sie erhalten 30 Tage kostenlosen Premium-Zugang.',
          'redemption': {
            'id': 'redemption-456',
            'user_id': 'test-user-id',
            'promo_code': 'UNLIMITED30',
            'code_type': 'unlimited',
            'duration_days': 30,
            'expires_at': DateTime.now().add(Duration(days: 30)).toIso8601String(),
            'status': 'active'
          }
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'UNLIMITED30',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], true);
        expect(response.data['redemption']['code_type'], equals('unlimited'));
        expect(response.data['redemption']['duration_days'], equals(30));
      });

      test('sollte Subscription-Update nach Einlösung durchführen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message': 'Promo Code erfolgreich eingelöst!',
          'redemption': {
            'id': 'redemption-789',
            'expires_at': DateTime.now().add(Duration(days: 7)).toIso8601String(),
          },
          'subscription_updated': true
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'WELCOME2025',
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], true);
        expect(response.data['subscription_updated'], true);
      });
    });

    group('Promo-Code Generierung (Admin)', () {
      test('sollte neuen Promo-Code erfolgreich generieren', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'code': 'GENERATED_CODE_123',
          'message': 'Promo Code erfolgreich erstellt',
          'promo_code_id': 'promo-123'
        });

        when(mockFunctions.invoke('generate-promo-code', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('generate-promo-code', body: {
          'code_type': 'trial',
          'duration_days': 7,
          'usage_limit': 100,
          'description': 'Test Promo Code'
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], true);
        expect(response.data['code'], isNotNull);
        expect(response.data['promo_code_id'], isNotNull);
      });

      test('sollte Custom-Promo-Code generieren', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'code': 'CUSTOM_CODE_2025',
          'message': 'Promo Code erfolgreich erstellt',
          'promo_code_id': 'promo-custom-123'
        });

        when(mockFunctions.invoke('generate-promo-code', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('generate-promo-code', body: {
          'code_type': 'pro',
          'duration_days': 14,
          'custom_code': 'CUSTOM_CODE_2025',
          'usage_limit': 50,
          'expires_at': DateTime.now().add(Duration(days: 90)).toIso8601String(),
          'description': 'Custom Promo Code für Marketing'
        });

        // Assert
        expect(response.status, equals(200));
        expect(response.data['success'], true);
        expect(response.data['code'], equals('CUSTOM_CODE_2025'));
      });

      test('sollte doppelte Promo-Codes ablehnen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(400);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'message': 'Promo Code existiert bereits'
        });

        when(mockFunctions.invoke('generate-promo-code', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('generate-promo-code', body: {
          'code_type': 'pro',
          'duration_days': 7,
          'custom_code': 'WELCOME2025', // Bereits existierender Code
        });

        // Assert
        expect(response.status, equals(400));
        expect(response.data['success'], false);
        expect(response.data['message'], contains('existiert bereits'));
      });
    });

    group('Edge Cases und Fehlerbehandlung', () {
      test('sollte Server-Fehler bei Promo-Code Validierung behandeln', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(500);
        when(mockFunctionResponse.data).thenReturn({
          'error': 'Internal server error',
          'message': 'Fehler beim Validieren des Promo Codes'
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'promo_code': 'WELCOME2025',
        });

        // Assert
        expect(response.status, equals(500));
        expect(response.data['error'], isNotNull);
      });

      test('sollte Netzwerk-Timeout behandeln', () async {
        // Arrange
        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenThrow(TimeoutException('Request timeout', Duration(seconds: 30)));

        // Act & Assert
        expect(
          () => mockFunctions.invoke('validate-promo-code-v2', body: {
            'promo_code': 'WELCOME2025',
          }),
          throwsA(isA<TimeoutException>()),
        );
      });

      test('sollte ungültige Request-Parameter behandeln', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(400);
        when(mockFunctionResponse.data).thenReturn({
          'error': 'Bad request',
          'message': 'Ungültige Parameter'
        });

        when(mockFunctions.invoke('validate-promo-code-v2', body: anyNamed('body')))
            .thenAnswer((_) async => mockFunctionResponse);

        // Act
        final response = await mockFunctions.invoke('validate-promo-code-v2', body: {
          'invalid_param': 'test',
        });

        // Assert
        expect(response.status, equals(400));
        expect(response.data['error'], equals('Bad request'));
      });
    });
  });
}

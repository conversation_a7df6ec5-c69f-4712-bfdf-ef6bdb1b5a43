// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in ki_test/test/unit/services/pdf_cv_generator_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:typed_data' as _i6;

import 'package:image_picker/image_picker.dart' as _i4;
import 'package:ki_test/src/application/services/profile_image_service.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ProfileImageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfileImageService extends _i1.Mock
    implements _i2.ProfileImageService {
  MockProfileImageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.XFile?> pickImage({
    _i4.ImageSource? source = _i4.ImageSource.gallery,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#pickImage, [], {#source: source}),
            returnValue: _i3.Future<_i4.XFile?>.value(),
          )
          as _i3.Future<_i4.XFile?>);

  @override
  _i3.Future<String> uploadProfileImage(_i4.XFile? imageFile, String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#uploadProfileImage, [imageFile, userId]),
            returnValue: _i3.Future<String>.value(
              _i5.dummyValue<String>(
                this,
                Invocation.method(#uploadProfileImage, [imageFile, userId]),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<void> updateProfileWithImage(String? userId, String? imageUrl) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfileWithImage, [userId, imageUrl]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteProfileImage(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteProfileImage, [userId]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i6.Uint8List?> downloadProfileImage(String? imageUrl) =>
      (super.noSuchMethod(
            Invocation.method(#downloadProfileImage, [imageUrl]),
            returnValue: _i3.Future<_i6.Uint8List?>.value(),
          )
          as _i3.Future<_i6.Uint8List?>);

  @override
  _i3.Future<bool> hasProfileImage(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#hasProfileImage, [userId]),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ki_test/src/infrastructure/services/application_counter_service.dart';

// Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockGoTrueClient extends Mock implements GoTrueClient {}

class MockUser extends Mock implements User {}

class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}

class MockPostgrestFilterBuilder extends Mock
    implements PostgrestFilterBuilder {}

void main() {
  group('ApplicationCounterService', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockSupabaseQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late ApplicationCounterService applicationCounterService;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockSupabaseQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');

      applicationCounterService = ApplicationCounterService(mockSupabaseClient);
    });

    group('Guthaben-Status Abfrage', () {
      test(
        'sollte verbleibende Bewerbungen für Basic-Plan korrekt zurückgeben',
        () async {
          // Arrange
          setupSubscriptionMock('basic', 30);
          setupCounterMock(25, 30);

          // Act
          final result =
              await applicationCounterService.getRemainingApplications();

          // Assert
          expect(result['remaining'], equals(25));
          expect(result['total'], equals(30));
          expect(result['unlimited'], false);
        },
      );

      test(
        'sollte verbleibende Bewerbungen für Pro-Plan korrekt zurückgeben',
        () async {
          // Arrange
          setupSubscriptionMock('pro', 150);
          setupCounterMock(120, 150);

          // Act
          final result =
              await applicationCounterService.getRemainingApplications();

          // Assert
          expect(result['remaining'], equals(120));
          expect(result['total'], equals(150));
          expect(result['unlimited'], false);
        },
      );

      test('sollte Unlimited-Plan korrekt behandeln', () async {
        // Arrange
        setupSubscriptionMock('unlimited', null);

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(-1)); // Unlimited
        expect(result['total'], equals(-1));
        expect(result['unlimited'], true);
      });

      test('sollte neuen Counter erstellen wenn keiner existiert', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);
        setupCounterMock(null, null); // Kein Counter existiert

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.insert(any)).thenAnswer(
          (_) async => {
            'data': [
              {
                'user_id': 'test-user-id',
                'remaining_applications': 150,
                'total_applications': 150,
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(150));
        expect(result['total'], equals(150));
        verify(
          mockQueryBuilder.insert({
            'user_id': 'test-user-id',
            'remaining_applications': 150,
            'total_applications': 150,
            'reset_date': anyNamed('reset_date'),
            'created_at': anyNamed('created_at'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte Fehler bei fehlendem User behandeln', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });

      test('sollte Fehler bei fehlendem Abonnement behandeln', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'data': null,
            'error': {'code': 'PGRST116', 'message': 'No rows found'},
          },
        );

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });
    });

    group('Guthaben-Verbrauch', () {
      test('sollte Bewerbungszähler erfolgreich inkrementieren', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);
        setupCounterMock(100, 150);

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenAnswer(
          (_) async => {
            'data': [
              {
                'remaining_applications': 99,
                'updated_at': DateTime.now().toIso8601String(),
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.update({
            'remaining_applications': 99,
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte Inkrementierung bei 0 Guthaben ablehnen', () async {
        // Arrange
        setupSubscriptionMock('basic', 30);
        setupCounterMock(0, 30); // Kein Guthaben mehr

        // Act
        final result =
            await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(result, false);
        verifyNever(mockQueryBuilder.update(any));
      });

      test('sollte Unlimited-Plan immer erlauben', () async {
        // Arrange
        setupSubscriptionMock('unlimited', null);

        // Act
        final result =
            await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(result, true);
        verifyNever(mockQueryBuilder.update(any)); // Kein Counter-Update nötig
      });

      test('sollte neuen Counter erstellen und inkrementieren', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);
        setupCounterMock(null, null); // Kein Counter existiert

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.insert(any)).thenAnswer(
          (_) async => {
            'data': [
              {
                'user_id': 'test-user-id',
                'remaining_applications': 149, // Eine Bewerbung abgezogen
                'total_applications': 150,
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.insert({
            'user_id': 'test-user-id',
            'remaining_applications': 149,
            'total_applications': 150,
            'reset_date': anyNamed('reset_date'),
            'created_at': anyNamed('created_at'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });
    });

    group('Guthaben-Aufladung und Reset', () {
      test('sollte Bewerbungszähler für Basic-Plan zurücksetzen', () async {
        // Arrange
        setupSubscriptionMock('basic', 30);
        setupCounterMock(5, 30); // Wenig Guthaben übrig

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenAnswer(
          (_) async => {
            'data': [
              {
                'remaining_applications': 30,
                'total_applications': 30,
                'reset_date': DateTime.now().toIso8601String(),
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.resetApplicationCounter();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.update({
            'remaining_applications': 30,
            'total_applications': 30,
            'reset_date': anyNamed('reset_date'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte Bewerbungszähler für Pro-Plan zurücksetzen', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);
        setupCounterMock(20, 150);

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenAnswer(
          (_) async => {
            'data': [
              {
                'remaining_applications': 150,
                'total_applications': 150,
                'reset_date': DateTime.now().toIso8601String(),
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.resetApplicationCounter();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.update({
            'remaining_applications': 150,
            'total_applications': 150,
            'reset_date': anyNamed('reset_date'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte neuen Counter bei Reset erstellen', () async {
        // Arrange
        setupSubscriptionMock('basic', 30);
        setupCounterMock(null, null); // Kein Counter existiert

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.insert(any)).thenAnswer(
          (_) async => {
            'data': [
              {
                'user_id': 'test-user-id',
                'remaining_applications': 30,
                'total_applications': 30,
              },
            ],
            'error': null,
          },
        );

        // Act
        final result =
            await applicationCounterService.resetApplicationCounter();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.insert({
            'user_id': 'test-user-id',
            'remaining_applications': 30,
            'total_applications': 30,
            'reset_date': anyNamed('reset_date'),
            'created_at': anyNamed('created_at'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte Unlimited-Plan bei Reset ignorieren', () async {
        // Arrange
        setupSubscriptionMock('unlimited', null);

        // Act
        final result =
            await applicationCounterService.resetApplicationCounter();

        // Assert
        expect(result, true);
        verifyNever(mockQueryBuilder.update(any));
        verifyNever(mockQueryBuilder.insert(any));
      });
    });

    group('Edge Cases und Fehlerbehandlung', () {
      test('sollte Datenbankfehler bei Counter-Abfrage behandeln', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenThrow(Exception('Database error'));

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });

      test('sollte Datenbankfehler bei Counter-Update behandeln', () async {
        // Arrange
        setupSubscriptionMock('pro', 150);
        setupCounterMock(100, 150);

        when(
          mockSupabaseClient.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenThrow(Exception('Update failed'));

        // Act
        final result =
            await applicationCounterService.incrementApplicationCounter();

        // Assert
        expect(result, false);
      });

      test('sollte ungültigen Plan-Typ behandeln', () async {
        // Arrange
        setupSubscriptionMock('invalid_plan', null);

        // Act
        final result =
            await applicationCounterService.getRemainingApplications();

        // Assert
        expect(result['remaining'], equals(0));
        expect(result['total'], equals(0));
        expect(result['unlimited'], false);
      });
    });

    // Helper-Methoden
    void setupSubscriptionMock(String planType, int? totalApplications) {
      when(
        mockSupabaseClient.from('subscriptions'),
      ).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(
        mockFilterBuilder.eq('user_id', 'test-user-id'),
      ).thenReturn(mockFilterBuilder);
      when(
        mockFilterBuilder.eq('status', 'active'),
      ).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer(
        (_) async => {
          'data': {
            'id': 'sub-123',
            'user_id': 'test-user-id',
            'plan_type': planType,
            'status': 'active',
            'expires_at':
                DateTime.now().add(Duration(days: 30)).toIso8601String(),
          },
          'error': null,
        },
      );
    }

    void setupCounterMock(int? remaining, int? total) {
      when(
        mockSupabaseClient.from('application_counters'),
      ).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(
        mockFilterBuilder.eq('user_id', 'test-user-id'),
      ).thenReturn(mockFilterBuilder);

      if (remaining == null || total == null) {
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'data': null,
            'error': {'code': 'PGRST116', 'message': 'No rows found'},
          },
        );
      } else {
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'data': {
              'user_id': 'test-user-id',
              'remaining_applications': remaining,
              'total_applications': total,
              'reset_date': DateTime.now().toIso8601String(),
            },
            'error': null,
          },
        );
      }
    }
  });
}

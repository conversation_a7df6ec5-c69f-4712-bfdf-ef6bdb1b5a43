import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';
import 'package:ki_test/src/infrastructure/services/subscription_service.dart';

// Mock-Klassen
class MockSubscriptionService extends Mock implements SubscriptionService {}
class MockBuildContext extends Mock implements BuildContext {}

void main() {
  group('PremiumFeatureService', () {
    late MockSubscriptionService mockSubscriptionService;
    late PremiumFeatureService premiumFeatureService;
    late MockBuildContext mockContext;

    setUp(() {
      mockSubscriptionService = MockSubscriptionService();
      mockContext = MockBuildContext();
      premiumFeatureService = PremiumFeatureService(mockSubscriptionService);
    });

    group('Feature-Zugriff Überprüfung', () {
      test('sollte KI-Anschreiben-Generator für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte KI-Anschreiben-Generator für Free-User verweigern', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false);
      });

      test('sollte KI-Jobsuche für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiJobSearch);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte unbegrenzte Favoriten für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.unlimitedFavorites);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte werbefreie Erfahrung für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.adFree);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte Premium-Insights für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.insights);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte Übersetzungsfunktion für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.translation);

        // Assert
        expect(hasAccess, true);
      });
    });

    group('Feature-Zugriff für verschiedene Plan-Typen', () {
      test('sollte alle Features für Unlimited-Plan erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getCurrentPlanType()).thenAnswer((_) async => 'unlimited');

        // Act & Assert
        for (final feature in PremiumFeature.values) {
          final hasAccess = await premiumFeatureService.hasAccessToFeature(feature);
          expect(hasAccess, true, reason: 'Unlimited plan should have access to ${feature.name}');
        }
      });

      test('sollte begrenzte Features für Pro-Plan erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getCurrentPlanType()).thenAnswer((_) async => 'pro');

        // Act & Assert
        final hasAiCoverLetter = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);
        final hasAdFree = await premiumFeatureService.hasAccessToFeature(PremiumFeature.adFree);
        final hasTranslation = await premiumFeatureService.hasAccessToFeature(PremiumFeature.translation);

        expect(hasAiCoverLetter, true);
        expect(hasAdFree, true);
        expect(hasTranslation, true);
      });

      test('sollte keine Premium-Features für Basic-Plan erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getCurrentPlanType()).thenAnswer((_) async => 'basic');

        // Act & Assert
        final hasAiCoverLetter = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);
        final hasAiJobSearch = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiJobSearch);

        expect(hasAiCoverLetter, false);
        expect(hasAiJobSearch, false);
      });

      test('sollte keine Features für Free-Plan erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act & Assert
        for (final feature in PremiumFeature.values) {
          final hasAccess = await premiumFeatureService.hasAccessToFeature(feature);
          expect(hasAccess, false, reason: 'Free plan should not have access to ${feature.name}');
        }
      });
    });

    group('Feature-Zugriff mit Promo-Code', () {
      test('sollte Features während Promo-Code Periode erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);
        when(mockSubscriptionService.hasActivePromoCode()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getPromoCodePlanType()).thenAnswer((_) async => 'pro');

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, true);
      });

      test('sollte Features nach Promo-Code Ablauf verweigern', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);
        when(mockSubscriptionService.hasActivePromoCode()).thenAnswer((_) async => false);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false);
      });

      test('sollte Unlimited-Features für Unlimited-Promo-Code erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);
        when(mockSubscriptionService.hasActivePromoCode()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getPromoCodePlanType()).thenAnswer((_) async => 'unlimited');

        // Act & Assert
        for (final feature in PremiumFeature.values) {
          final hasAccess = await premiumFeatureService.hasAccessToFeature(feature);
          expect(hasAccess, true, reason: 'Unlimited promo should have access to ${feature.name}');
        }
      });
    });

    group('Feature-Beschränkungen und Limits', () {
      test('sollte Favoriten-Limit für Free-User durchsetzen', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final canAddFavorite = await premiumFeatureService.canAddFavorite(currentFavoriteCount: 5);

        // Assert
        expect(canAddFavorite, false); // Free-User haben Limit von z.B. 3 Favoriten
      });

      test('sollte unbegrenzte Favoriten für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final canAddFavorite = await premiumFeatureService.canAddFavorite(currentFavoriteCount: 100);

        // Assert
        expect(canAddFavorite, true);
      });

      test('sollte KI-Anfragen-Limit für Free-User durchsetzen', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final canUseAI = await premiumFeatureService.canUseAIFeature(dailyUsageCount: 3);

        // Assert
        expect(canUseAI, false); // Free-User haben z.B. 2 KI-Anfragen pro Tag
      });

      test('sollte unbegrenzte KI-Anfragen für Premium-User erlauben', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final canUseAI = await premiumFeatureService.canUseAIFeature(dailyUsageCount: 50);

        // Assert
        expect(canUseAI, true);
      });
    });

    group('Premium-Dialog und Upgrade-Prompts', () {
      test('sollte Premium-Dialog für gesperrte Features anzeigen', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        final shouldShowDialog = await premiumFeatureService.shouldShowPremiumDialog(PremiumFeature.aiCoverLetter);

        // Assert
        expect(shouldShowDialog, true);
      });

      test('sollte keinen Premium-Dialog für Premium-User anzeigen', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        final shouldShowDialog = await premiumFeatureService.shouldShowPremiumDialog(PremiumFeature.aiCoverLetter);

        // Assert
        expect(shouldShowDialog, false);
      });

      test('sollte Feature-spezifische Upgrade-Nachrichten liefern', () {
        // Act
        final aiCoverLetterMessage = premiumFeatureService.getUpgradeMessage(PremiumFeature.aiCoverLetter);
        final aiJobSearchMessage = premiumFeatureService.getUpgradeMessage(PremiumFeature.aiJobSearch);
        final adFreeMessage = premiumFeatureService.getUpgradeMessage(PremiumFeature.adFree);

        // Assert
        expect(aiCoverLetterMessage, contains('KI-Anschreiben'));
        expect(aiJobSearchMessage, contains('KI-Jobsuche'));
        expect(adFreeMessage, contains('werbefrei'));
      });
    });

    group('Edge Cases und Fehlerbehandlung', () {
      test('sollte Fehler bei Subscription-Service Ausfall behandeln', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenThrow(Exception('Service error'));

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false); // Sollte bei Fehlern konservativ sein
      });

      test('sollte Timeout bei Subscription-Abfrage behandeln', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription())
            .thenAnswer((_) => Future.delayed(Duration(seconds: 10), () => true));

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(
          PremiumFeature.aiCoverLetter,
          timeout: Duration(seconds: 1),
        );

        // Assert
        expect(hasAccess, false); // Sollte bei Timeout false zurückgeben
      });

      test('sollte ungültige Plan-Typen behandeln', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);
        when(mockSubscriptionService.getCurrentPlanType()).thenAnswer((_) async => 'invalid_plan');

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false); // Sollte bei ungültigen Plänen false zurückgeben
      });

      test('sollte null-Werte bei Subscription-Abfrage behandeln', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => null);

        // Act
        final hasAccess = await premiumFeatureService.hasAccessToFeature(PremiumFeature.aiCoverLetter);

        // Assert
        expect(hasAccess, false);
      });
    });

    group('Feature-Tracking und Analytics', () {
      test('sollte Feature-Nutzung für Premium-User tracken', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => true);

        // Act
        await premiumFeatureService.trackFeatureUsage(PremiumFeature.aiCoverLetter);

        // Assert
        // Hier würde normalerweise ein Analytics-Service verifiziert werden
        expect(true, true); // Placeholder für Analytics-Verifikation
      });

      test('sollte Feature-Blockierung für Free-User tracken', () async {
        // Arrange
        when(mockSubscriptionService.hasActiveSubscription()).thenAnswer((_) async => false);

        // Act
        await premiumFeatureService.trackFeatureBlocked(PremiumFeature.aiCoverLetter);

        // Assert
        // Hier würde normalerweise ein Analytics-Service verifiziert werden
        expect(true, true); // Placeholder für Analytics-Verifikation
      });
    });
  });
}

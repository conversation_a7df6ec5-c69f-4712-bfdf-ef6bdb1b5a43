import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:ki_test/src/infrastructure/services/subscription_service.dart';

// Mock-Klassen
class MockSupabaseClient extends Mock implements SupabaseClient {}

class MockGoTrueClient extends Mock implements GoTrueClient {}

class MockUser extends Mock implements User {}

class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}

class MockPostgrestFilterBuilder extends Mock
    implements PostgrestFilterBuilder {}

class MockFunctionsClient extends Mock implements FunctionsClient {}

class MockFunctionResponse extends Mock implements FunctionResponse {}

class MockRef extends Mock implements Ref {}

void main() {
  group('SubscriptionService', () {
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockSupabaseQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late MockFunctionsClient mockFunctions;
    late MockFunctionResponse mockFunctionResponse;
    late MockRef mockRef;
    late SubscriptionService subscriptionService;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockSupabaseQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();
      mockFunctions = MockFunctionsClient();
      mockFunctionResponse = MockFunctionResponse();
      mockRef = MockRef();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      when(mockSupabaseClient.functions).thenReturn(mockFunctions);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');

      subscriptionService = SubscriptionService(mockSupabaseClient, mockRef);
    });

    group('Promo-Code Einlösung', () {
      test('sollte gültigen Promo-Code erfolgreich einlösen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': true,
          'isValid': true,
          'message':
              'Promo Code erfolgreich eingelöst! Sie erhalten 7 Tage kostenlosen Premium-Zugang.',
          'redemption': {
            'id': 'redemption-123',
            'expires_at':
                DateTime.now().add(Duration(days: 7)).toIso8601String(),
          },
        });

        when(
          mockFunctions.invoke(
            'validate-promo-code-v2',
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, true);
        verify(
          mockFunctions.invoke(
            'validate-promo-code-v2',
            body: {'promo_code': 'WELCOME2025'},
          ),
        ).called(1);
      });

      test('sollte ungültigen Promo-Code ablehnen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Ungültiger Promo Code. Bitte überprüfen Sie die Eingabe.',
        });

        when(
          mockFunctions.invoke(
            'validate-promo-code-v2',
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode(
          'INVALID_CODE',
        );

        // Assert
        expect(result, false);
      });

      test('sollte bereits eingelösten Promo-Code ablehnen', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(200);
        when(mockFunctionResponse.data).thenReturn({
          'success': false,
          'isValid': false,
          'message': 'Dieser Promo Code wurde bereits von Ihnen eingelöst.',
        });

        when(
          mockFunctions.invoke(
            'validate-promo-code-v2',
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, false);
      });

      test('sollte Server-Fehler bei Promo-Code Einlösung behandeln', () async {
        // Arrange
        when(mockFunctionResponse.status).thenReturn(500);
        when(mockFunctionResponse.data).thenReturn(null);

        when(
          mockFunctions.invoke(
            'validate-promo-code-v2',
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => mockFunctionResponse);

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, false);
      });

      test(
        'sollte Netzwerk-Fehler bei Promo-Code Einlösung behandeln',
        () async {
          // Arrange
          when(
            mockFunctions.invoke(
              'validate-promo-code-v2',
              body: anyNamed('body'),
            ),
          ).thenThrow(Exception('Network error'));

          // Act
          final result = await subscriptionService.redeemPromoCode(
            'WELCOME2025',
          );

          // Assert
          expect(result, false);
        },
      );

      test('sollte leeren Promo-Code ablehnen', () async {
        // Act
        final result1 = await subscriptionService.redeemPromoCode('');
        final result2 = await subscriptionService.redeemPromoCode('  ');
        final result3 = await subscriptionService.redeemPromoCode(
          'AB',
        ); // Zu kurz

        // Assert
        expect(result1, false);
        expect(result2, false);
        expect(result3, false);
      });

      test('sollte Promo-Code ohne angemeldeten User ablehnen', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = await subscriptionService.redeemPromoCode('WELCOME2025');

        // Assert
        expect(result, false);
      });
    });

    group('Abonnement-Status Überprüfung', () {
      test('sollte aktives Abonnement korrekt erkennen', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.gt('expires_at', any),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'data': {
              'id': 'sub-123',
              'user_id': 'test-user-id',
              'status': 'active',
              'plan_type': 'pro',
              'expires_at':
                  DateTime.now().add(Duration(days: 30)).toIso8601String(),
            },
            'error': null,
          },
        );

        // Act
        final hasActiveSubscription =
            await subscriptionService.hasActiveSubscription();

        // Assert
        expect(hasActiveSubscription, true);
      });

      test('sollte abgelaufenes Abonnement erkennen', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.gt('expires_at', any),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'data': null,
            'error': {'code': 'PGRST116', 'message': 'No rows found'},
          },
        );

        // Act
        final hasActiveSubscription =
            await subscriptionService.hasActiveSubscription();

        // Assert
        expect(hasActiveSubscription, false);
      });
    });

    group('Abonnement-Kündigung', () {
      test('sollte Abonnement erfolgreich kündigen', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('status', 'active')).thenAnswer(
          (_) async => {
            'data': [
              {
                'id': 'sub-123',
                'status': 'cancelled',
                'cancelled_at': DateTime.now().toIso8601String(),
              },
            ],
            'error': null,
          },
        );

        // Act
        final result = await subscriptionService.cancelSubscription();

        // Assert
        expect(result, true);
        verify(
          mockQueryBuilder.update({
            'status': 'cancelled',
            'cancelled_at': anyNamed('cancelled_at'),
            'updated_at': anyNamed('updated_at'),
          }),
        ).called(1);
      });

      test('sollte Fehler bei Abonnement-Kündigung behandeln', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenThrow(Exception('Database error'));

        // Act
        final result = await subscriptionService.cancelSubscription();

        // Assert
        expect(result, false);
      });
    });

    group('In-App-Promotion Checks', () {
      test('sollte verfügbare Promotions prüfen', () async {
        // Act & Assert - Sollte keine Fehler werfen
        expect(() => subscriptionService.checkForPromotions(), returnsNormally);
      });
    });

    group('Edge Cases', () {
      test('sollte Fehler bei fehlendem User behandeln', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final hasActiveSubscription =
            await subscriptionService.hasActiveSubscription();
        final cancelResult = await subscriptionService.cancelSubscription();

        // Assert
        expect(hasActiveSubscription, false);
        expect(cancelResult, false);
      });

      test('sollte Datenbankfehler korrekt behandeln', () async {
        // Arrange
        when(
          mockSupabaseClient.from('subscriptions'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.gt('expires_at', any),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.single(),
        ).thenThrow(Exception('Database connection failed'));

        // Act
        final hasActiveSubscription =
            await subscriptionService.hasActiveSubscription();

        // Assert
        expect(hasActiveSubscription, false);
      });
    });
  });
}

import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;

void main() {
  group('Mistral Queue System Live Tests', () {
    const String supabaseUrl = 'https://vpttdxibvjrfjzbtktqg.supabase.co';
    const String queueEndpoint =
        '$supabaseUrl/functions/v1/mistral-queue-manager';
    const String coverLetterEndpoint =
        '$supabaseUrl/functions/v1/generate-cover-letter-mistral';

    // Test API Key - sollte aus Environment kommen
    final String? apiKey = Platform.environment['SUPABASE_ANON_KEY'];

    setUp(() {
      if (apiKey == null) {
        print('⚠️ SUPABASE_ANON_KEY nicht gesetzt. Tests werden übersprungen.');
      }
    });

    group('Queue Manager Status Tests', () {
      test('Queue Status Check - Live Test', () async {
        if (apiKey == null) {
          print('⚠️ Test übersprungen - kein API Key');
          return;
        }

        try {
          final response = await http.post(
            Uri.parse(queueEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $apiKey',
            },
            body: json.encode({
              'action': 'status',
              'userId': 'test-status-check',
            }),
          );

          print('📊 Queue Status Response: ${response.statusCode}');
          print('📊 Response Body: ${response.body}');

          expect(response.statusCode, 200);

          final data = json.decode(response.body);
          expect(data, isA<Map<String, dynamic>>());
          expect(data.containsKey('queueLength'), true);
          expect(data.containsKey('processing'), true);

          print('✅ Queue Status Test erfolgreich');
          print('   - Queue Length: ${data['queueLength']}');
          print('   - Processing: ${data['processing']}');
          print('   - Premium Count: ${data['premiumCount'] ?? 0}');
          print('   - Free Count: ${data['freeCount'] ?? 0}');
        } catch (e) {
          print('❌ Queue Status Test Fehler: $e');
          fail('Queue Status Test fehlgeschlagen: $e');
        }
      });
    });

    group('Cover Letter Generation Tests', () {
      test('Cover Letter - Free User Live Test', () async {
        if (apiKey == null) {
          print('⚠️ Test übersprungen - kein API Key');
          return;
        }

        try {
          final requestBody = {
            'userId': 'test-free-user-${DateTime.now().millisecondsSinceEpoch}',
            'userProfile': {
              'name': 'Test User',
              'skills': ['Flutter', 'Dart', 'Testing'],
              'experience': '2 Jahre Softwareentwicklung',
              'email': '<EMAIL>',
              'education': 'Bachelor Informatik',
              'applicationLength': 'Standard',
            },
            'jobPosting': {
              'title': 'Flutter Developer (Test)',
              'company': 'Test Company GmbH',
              'description':
                  'Wir suchen einen Flutter Developer für unser Test-Team. Erfahrung mit Dart und Testing erwünscht.',
            },
            'stylePreference': 'professional',
          };

          print('🚀 Sende Cover Letter Request...');
          final startTime = DateTime.now();

          final response = await http
              .post(
                Uri.parse(coverLetterEndpoint),
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer $apiKey',
                },
                body: json.encode(requestBody),
              )
              .timeout(Duration(minutes: 2)); // 2 Minuten Timeout für Queue

          final endTime = DateTime.now();
          final duration = endTime.difference(startTime);

          print('📝 Cover Letter Response: ${response.statusCode}');
          print('⏱️ Response Time: ${duration.inSeconds}s');

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            print('✅ Cover Letter erfolgreich generiert');
            print('   - Success: ${data['success']}');
            print('   - Premium: ${data['isPremium'] ?? false}');
            print('   - Remaining: ${data['remainingApplications'] ?? 'N/A'}');
            print(
              '   - Cover Letter Length: ${data['coverLetter']?.length ?? 0} chars',
            );

            expect(data['success'], true);
            expect(data['coverLetter'], isNotEmpty);
          } else {
            print('❌ Cover Letter Request failed: ${response.statusCode}');
            print('❌ Error Body: ${response.body}');
            fail('Cover Letter Request fehlgeschlagen: ${response.statusCode}');
          }
        } catch (e) {
          print('❌ Cover Letter Test Fehler: $e');
          fail('Cover Letter Test fehlgeschlagen: $e');
        }
      });

      test('Multiple Concurrent Requests - Live Test', () async {
        if (apiKey == null) {
          print('⚠️ Test übersprungen - kein API Key');
          return;
        }

        try {
          print('🔄 Starte 3 gleichzeitige Cover Letter Requests...');
          final futures = <Future<http.Response>>[];
          final startTime = DateTime.now();

          for (int i = 0; i < 3; i++) {
            final requestBody = {
              'userId':
                  'concurrent-test-user-$i-${DateTime.now().millisecondsSinceEpoch}',
              'userProfile': {
                'name': 'Concurrent Test User $i',
                'skills': ['Flutter', 'Testing'],
                'experience': '1 Jahr',
                'applicationLength': 'Kurz',
              },
              'jobPosting': {
                'title': 'Test Position $i',
                'company': 'Test Company $i',
                'description': 'Test Job Description für Request $i',
              },
              'stylePreference': 'professional',
            };

            futures.add(
              http
                  .post(
                    Uri.parse(coverLetterEndpoint),
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': 'Bearer $apiKey',
                    },
                    body: json.encode(requestBody),
                  )
                  .timeout(Duration(minutes: 3)),
            );
          }

          final responses = await Future.wait(futures);
          final endTime = DateTime.now();
          final totalDuration = endTime.difference(startTime);

          print(
            '⏱️ Alle 3 Requests abgeschlossen in: ${totalDuration.inSeconds}s',
          );

          int successCount = 0;
          for (int i = 0; i < responses.length; i++) {
            final response = responses[i];
            print('📝 Request $i: Status ${response.statusCode}');

            if (response.statusCode == 200) {
              successCount++;
              final data = json.decode(response.body);
              print('   ✅ Success: ${data['success']}');
            } else {
              print('   ❌ Error: ${response.body}');
            }
          }

          print('✅ Concurrent Test abgeschlossen: $successCount/3 erfolgreich');
          expect(
            successCount,
            greaterThan(0),
            reason: 'Mindestens ein Request sollte erfolgreich sein',
          );
        } catch (e) {
          print('❌ Concurrent Test Fehler: $e');
          fail('Concurrent Test fehlgeschlagen: $e');
        }
      });
    });

    group('Queue Timing Tests', () {
      test('Rate Limit Timing Test', () async {
        if (apiKey == null) {
          print('⚠️ Test übersprungen - kein API Key');
          return;
        }

        try {
          print('⏱️ Teste Queue Timing mit 2 schnellen Requests...');

          final request1Start = DateTime.now();
          final future1 = http.post(
            Uri.parse(queueEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $apiKey',
            },
            body: json.encode({
              'userId': 'timing-test-1',
              'isPremium': false,
              'requestData': {
                'model': 'mistral-large-2407',
                'messages': [
                  {'role': 'user', 'content': 'Kurzer Test 1'},
                ],
                'max_tokens': 50,
                'temperature': 0.1,
              },
            }),
          );

          // Warte 100ms und sende zweiten Request
          await Future.delayed(Duration(milliseconds: 100));

          final request2Start = DateTime.now();
          final future2 = http.post(
            Uri.parse(queueEndpoint),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $apiKey',
            },
            body: json.encode({
              'userId': 'timing-test-2',
              'isPremium': false,
              'requestData': {
                'model': 'mistral-large-2407',
                'messages': [
                  {'role': 'user', 'content': 'Kurzer Test 2'},
                ],
                'max_tokens': 50,
                'temperature': 0.1,
              },
            }),
          );

          final responses = await Future.wait([future1, future2]);
          final request1End = DateTime.now();
          final request2End = DateTime.now();

          final duration1 = request1End.difference(request1Start);
          final duration2 = request2End.difference(request2Start);

          print('⏱️ Request 1 Duration: ${duration1.inMilliseconds}ms');
          print('⏱️ Request 2 Duration: ${duration2.inMilliseconds}ms');
          print('📊 Request 1 Status: ${responses[0].statusCode}');
          print('📊 Request 2 Status: ${responses[1].statusCode}');

          // Der zweite Request sollte mindestens 1 Sekunde länger dauern
          final timeDifference =
              duration2.inMilliseconds - duration1.inMilliseconds;
          print('⏱️ Time Difference: ${timeDifference}ms');

          if (timeDifference >= 800) {
            // Mindestens 800ms Unterschied erwartet
            print('✅ Queue Timing funktioniert korrekt');
          } else {
            print('⚠️ Queue Timing möglicherweise nicht optimal');
          }
        } catch (e) {
          print('❌ Timing Test Fehler: $e');
          // Timing Test ist nicht kritisch, daher kein fail()
        }
      });
    });
  });
}

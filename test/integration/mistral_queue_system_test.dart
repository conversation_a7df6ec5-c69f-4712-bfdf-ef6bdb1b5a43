import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// Mock-Klassen für HTTP Client
@GenerateMocks([http.Client])
import 'mistral_queue_system_test.mocks.dart';

void main() {
  group('Mistral Queue System Tests', () {
    late MockClient mockClient;
    const String supabaseUrl = 'https://vpttdxibvjrfjzbtktqg.supabase.co';
    const String queueEndpoint =
        '$supabaseUrl/functions/v1/mistral-queue-manager';
    const String coverLetterEndpoint =
        '$supabaseUrl/functions/v1/generate-cover-letter-mistral';

    setUp(() {
      mockClient = MockClient();
    });

    group('Queue Manager Direct Tests', () {
      test('Queue Manager - Single Request Test', () async {
        // Mock erfolgreiche Queue Response
        final mockResponse = {
          'id': 'test-123',
          'object': 'chat.completion',
          'created': DateTime.now().millisecondsSinceEpoch,
          'model': 'mistral-large-2407',
          'choices': [
            {
              'index': 0,
              'message': {
                'role': 'assistant',
                'content': 'Test Anschreiben generiert über Queue System',
              },
              'finish_reason': 'stop',
            },
          ],
          'usage': {
            'prompt_tokens': 100,
            'completion_tokens': 50,
            'total_tokens': 150,
          },
        };

        when(
          mockClient.post(
            Uri.parse(queueEndpoint),
            headers: anyNamed('headers'),
            body: anyNamed('body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(mockResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        final requestBody = {
          'userId': 'test-user-123',
          'isPremium': false,
          'requestData': {
            'model': 'mistral-large-2407',
            'messages': [
              {'role': 'system', 'content': 'Du bist ein Bewerbungsexperte.'},
              {
                'role': 'user',
                'content': 'Erstelle ein Anschreiben für Software Developer.',
              },
            ],
            'max_tokens': 2048,
            'temperature': 0.7,
          },
        };

        final response = await mockClient.post(
          Uri.parse(queueEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-key',
          },
          body: json.encode(requestBody),
        );

        expect(response.statusCode, 200);
        final responseData = json.decode(response.body);
        expect(
          responseData['choices'][0]['message']['content'],
          'Test Anschreiben generiert über Queue System',
        );

        print('✅ Queue Manager Single Request Test erfolgreich');
      });

      test('Queue Manager - Premium vs Free User Priority Test', () async {
        // Simuliere mehrere Requests mit unterschiedlichen Prioritäten
        final premiumResponse = {
          'id': 'premium-123',
          'choices': [
            {
              'message': {'content': 'Premium User Response'},
            },
          ],
        };

        final freeResponse = {
          'id': 'free-123',
          'choices': [
            {
              'message': {'content': 'Free User Response'},
            },
          ],
        };

        // Mock Premium Request
        when(
          mockClient.post(
            Uri.parse(queueEndpoint),
            headers: anyNamed('headers'),
            body: argThat(contains('"isPremium":true'), named: 'body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(premiumResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        // Mock Free Request
        when(
          mockClient.post(
            Uri.parse(queueEndpoint),
            headers: anyNamed('headers'),
            body: argThat(contains('"isPremium":false'), named: 'body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(freeResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        // Test Premium Request
        final premiumRequest = {
          'userId': 'premium-user',
          'isPremium': true,
          'requestData': {'model': 'mistral-large-2407', 'messages': []},
        };

        final premiumResult = await mockClient.post(
          Uri.parse(queueEndpoint),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(premiumRequest),
        );

        expect(premiumResult.statusCode, 200);
        final premiumData = json.decode(premiumResult.body);
        expect(
          premiumData['choices'][0]['message']['content'],
          'Premium User Response',
        );

        // Test Free Request
        final freeRequest = {
          'userId': 'free-user',
          'isPremium': false,
          'requestData': {'model': 'mistral-large-2407', 'messages': []},
        };

        final freeResult = await mockClient.post(
          Uri.parse(queueEndpoint),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(freeRequest),
        );

        expect(freeResult.statusCode, 200);
        final freeData = json.decode(freeResult.body);
        expect(
          freeData['choices'][0]['message']['content'],
          'Free User Response',
        );

        print('✅ Premium vs Free Priority Test erfolgreich');
      });

      test('Queue Manager - Status Check Test', () async {
        final statusResponse = {
          'queueLength': 3,
          'processing': true,
          'premiumCount': 1,
          'freeCount': 2,
        };

        when(
          mockClient.post(
            Uri.parse(queueEndpoint),
            headers: anyNamed('headers'),
            body: argThat(contains('"action":"status"'), named: 'body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(statusResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        final statusRequest = {'action': 'status', 'userId': 'test-user'};

        final response = await mockClient.post(
          Uri.parse(queueEndpoint),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(statusRequest),
        );

        expect(response.statusCode, 200);
        final data = json.decode(response.body);
        expect(data['queueLength'], 3);
        expect(data['processing'], true);
        expect(data['premiumCount'], 1);
        expect(data['freeCount'], 2);

        print('✅ Queue Status Check Test erfolgreich');
      });
    });

    group('Cover Letter Generation Tests', () {
      test('Cover Letter - Free User Test', () async {
        final mockCoverLetterResponse = {
          'success': true,
          'coverLetter':
              'Sehr geehrte Damen und Herren,\n\nhiermit bewerbe ich mich...',
          'isPremium': false,
          'remainingApplications': 4,
          'totalApplications': 5,
          'isUnlimited': false,
        };

        when(
          mockClient.post(
            Uri.parse(coverLetterEndpoint),
            headers: anyNamed('headers'),
            body: anyNamed('body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(mockCoverLetterResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        final requestBody = {
          'userId': 'free-user-123',
          'userProfile': {
            'name': 'Max Mustermann',
            'skills': ['Flutter', 'Dart', 'Firebase'],
            'experience': '3 Jahre Softwareentwicklung',
            'email': '<EMAIL>',
          },
          'jobPosting': {
            'title': 'Flutter Developer',
            'company': 'Tech Startup GmbH',
            'description': 'Wir suchen einen erfahrenen Flutter Developer...',
          },
          'stylePreference': 'professional',
        };

        final response = await mockClient.post(
          Uri.parse(coverLetterEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-key',
          },
          body: json.encode(requestBody),
        );

        expect(response.statusCode, 200);
        final data = json.decode(response.body);
        expect(data['success'], true);
        expect(data['coverLetter'], isNotEmpty);
        expect(data['isPremium'], false);
        expect(data['remainingApplications'], 4);

        print('✅ Free User Cover Letter Test erfolgreich');
      });

      test('Cover Letter - Premium User Test', () async {
        final mockPremiumResponse = {
          'success': true,
          'coverLetter':
              'Sehr geehrte Damen und Herren,\n\nals Premium-User...',
          'isPremium': true,
          'remainingApplications': null,
          'totalApplications': null,
          'isUnlimited': true,
        };

        when(
          mockClient.post(
            Uri.parse(coverLetterEndpoint),
            headers: anyNamed('headers'),
            body: anyNamed('body'),
          ),
        ).thenAnswer(
          (_) async => http.Response(
            json.encode(mockPremiumResponse),
            200,
            headers: {'content-type': 'application/json'},
          ),
        );

        final requestBody = {
          'userId': 'premium-user-123',
          'userProfile': {
            'name': 'Premium User',
            'skills': ['React', 'Node.js', 'AWS'],
            'experience': '5 Jahre Full-Stack Development',
          },
          'jobPosting': {
            'title': 'Senior Full-Stack Developer',
            'company': 'Enterprise Corp',
            'description': 'Senior Position für erfahrenen Entwickler...',
          },
        };

        final response = await mockClient.post(
          Uri.parse(coverLetterEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-key',
          },
          body: json.encode(requestBody),
        );

        expect(response.statusCode, 200);
        final data = json.decode(response.body);
        expect(data['success'], true);
        expect(data['isPremium'], true);
        expect(data['isUnlimited'], true);

        print('✅ Premium User Cover Letter Test erfolgreich');
      });
    });

    group('Concurrent Request Tests', () {
      test('Multiple Simultaneous Requests Test', () async {
        // Simuliere 5 gleichzeitige Requests
        final responses = <Future<http.Response>>[];

        for (int i = 0; i < 5; i++) {
          final mockResponse = {
            'id': 'request-$i',
            'choices': [
              {
                'message': {'content': 'Response for request $i'},
              },
            ],
          };

          when(
            mockClient.post(
              Uri.parse(queueEndpoint),
              headers: anyNamed('headers'),
              body: argThat(
                contains('"userId":"concurrent-user-$i"'),
                named: 'body',
              ),
            ),
          ).thenAnswer((_) async {
            // Simuliere Queue-Wartezeit
            await Future.delayed(Duration(milliseconds: i * 1000));
            return http.Response(
              json.encode(mockResponse),
              200,
              headers: {'content-type': 'application/json'},
            );
          });

          final request = {
            'userId': 'concurrent-user-$i',
            'isPremium': i < 2, // Erste 2 sind Premium
            'requestData': {'model': 'mistral-large-2407', 'messages': []},
          };

          responses.add(
            mockClient.post(
              Uri.parse(queueEndpoint),
              headers: {'Content-Type': 'application/json'},
              body: json.encode(request),
            ),
          );
        }

        final results = await Future.wait(responses);

        expect(results.length, 5);
        for (int i = 0; i < results.length; i++) {
          expect(results[i].statusCode, 200);
          final data = json.decode(results[i].body);
          expect(
            data['choices'][0]['message']['content'],
            'Response for request $i',
          );
        }

        print('✅ Concurrent Requests Test erfolgreich');
      });
    });
  });
}

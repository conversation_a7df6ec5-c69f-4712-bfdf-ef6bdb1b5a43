import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';
import 'package:ki_test/src/application/providers/subscription_management_provider.dart';

// Mock-Klassen generieren
@GenerateMocks([
  SupabaseClient,
  GoTrueClient,
  User,
  PostgrestQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestBuilder,
])
import 'application_counter_test.mocks.dart';

void main() {
  group('Application Counter Tests', () {
    late MockSupabaseClient mockSupabase;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockPostgrestQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late MockPostgrestBuilder mockBuilder;
    late ProviderContainer container;
    late SubscriptionManagementService service;

    setUp(() {
      mockSupabase = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockPostgrestQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();
      mockBuilder = MockPostgrestBuilder();

      // Setup basic mocks
      when(mockSupabase.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');

      // Setup container with mocked Supabase
      container = ProviderContainer(
        overrides: [
          // Override Supabase client
        ],
      );

      service = SubscriptionManagementService(mockSupabase, container.read);
    });

    tearDown(() {
      container.dispose();
    });

    group('Free User Scenarios', () {
      test('Free user with 10 applications - successful decrement', () async {
        // Arrange: Free user mit 10 verbleibenden Bewerbungen
        when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'id': 'sub-1',
            'user_id': 'test-user-id',
            'plan_type': 'free',
            'is_premium': false,
            'status': 'active',
          },
        );

        when(
          mockSupabase.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'user_id': 'test-user-id',
            'total_applications': 10,
            'remaining_applications': 10,
            'plan_type': 'free',
          },
        );

        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockBuilder);
        when(mockBuilder.then()).thenAnswer((_) async => []);

        // Act: Bewerbung abziehen
        final result = await service.incrementApplicationCounter();

        // Assert: Erfolgreich abgezogen
        expect(result, isTrue);
        verify(
          mockQueryBuilder.update({
            'remaining_applications': 9,
            'updated_at': any,
          }),
        ).called(1);
      });

      test(
        'Free user with 1 application - successful decrement to 0',
        () async {
          // Arrange: Free user mit 1 verbleibender Bewerbung
          when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
          when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('user_id', 'test-user-id'),
          ).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('status', 'active'),
          ).thenReturn(mockFilterBuilder);
          when(mockFilterBuilder.single()).thenAnswer(
            (_) async => {
              'id': 'sub-1',
              'user_id': 'test-user-id',
              'plan_type': 'free',
              'is_premium': false,
              'status': 'active',
            },
          );

          when(
            mockSupabase.from('application_counters'),
          ).thenReturn(mockQueryBuilder);
          when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('user_id', 'test-user-id'),
          ).thenReturn(mockFilterBuilder);
          when(mockFilterBuilder.single()).thenAnswer(
            (_) async => {
              'user_id': 'test-user-id',
              'total_applications': 10,
              'remaining_applications': 1,
              'plan_type': 'free',
            },
          );

          when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('user_id', 'test-user-id'),
          ).thenReturn(mockBuilder);
          when(mockBuilder.then()).thenAnswer((_) async => []);

          // Act: Bewerbung abziehen
          final result = await service.incrementApplicationCounter();

          // Assert: Erfolgreich auf 0 reduziert
          expect(result, isTrue);
          verify(
            mockQueryBuilder.update({
              'remaining_applications': 0,
              'updated_at': any,
            }),
          ).called(1);
        },
      );

      test('Free user with 0 applications - should fail', () async {
        // Arrange: Free user mit 0 verbleibenden Bewerbungen
        when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'id': 'sub-1',
            'user_id': 'test-user-id',
            'plan_type': 'free',
            'is_premium': false,
            'status': 'active',
          },
        );

        when(
          mockSupabase.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'user_id': 'test-user-id',
            'total_applications': 10,
            'remaining_applications': 0,
            'plan_type': 'free',
          },
        );

        // Act: Bewerbung abziehen versuchen
        final result = await service.incrementApplicationCounter();

        // Assert: Sollte fehlschlagen
        expect(result, isFalse);
        verifyNever(mockQueryBuilder.update(any));
      });
    });

    group('Premium User Scenarios', () {
      test(
        'Unlimited user - should always succeed without decrement',
        () async {
          // Arrange: Unlimited user
          when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
          when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('user_id', 'test-user-id'),
          ).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('status', 'active'),
          ).thenReturn(mockFilterBuilder);
          when(mockFilterBuilder.single()).thenAnswer(
            (_) async => {
              'id': 'sub-1',
              'user_id': 'test-user-id',
              'plan_type': 'unlimited',
              'is_premium': true,
              'status': 'active',
            },
          );

          // Act: Bewerbung "abziehen"
          final result = await service.incrementApplicationCounter();

          // Assert: Erfolgreich ohne tatsächlichen Abzug
          expect(result, isTrue);
          verifyNever(mockSupabase.from('application_counters'));
        },
      );
    });

    group('Error Scenarios', () {
      test('No user logged in - should fail', () async {
        // Arrange: Kein angemeldeter User
        when(mockAuth.currentUser).thenReturn(null);

        // Act: Bewerbung abziehen versuchen
        final result = await service.incrementApplicationCounter();

        // Assert: Sollte fehlschlagen
        expect(result, isFalse);
      });

      test('Database error during counter update - should fail', () async {
        // Arrange: Database-Fehler beim Update
        when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'id': 'sub-1',
            'user_id': 'test-user-id',
            'plan_type': 'free',
            'is_premium': false,
            'status': 'active',
          },
        );

        when(
          mockSupabase.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'user_id': 'test-user-id',
            'total_applications': 10,
            'remaining_applications': 5,
            'plan_type': 'free',
          },
        );

        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockBuilder);
        when(mockBuilder.then()).thenThrow(Exception('Database error'));

        // Act: Bewerbung abziehen versuchen
        final result = await service.incrementApplicationCounter();

        // Assert: Sollte fehlschlagen
        expect(result, isFalse);
      });

      test(
        'No subscription found - should create default and decrement',
        () async {
          // Arrange: Kein Abonnement gefunden
          when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
          when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('user_id', 'test-user-id'),
          ).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.eq('status', 'active'),
          ).thenReturn(mockFilterBuilder);
          when(
            mockFilterBuilder.single(),
          ).thenThrow(Exception('No subscription found'));

          // Mock für Standard-Abonnement-Erstellung
          when(mockQueryBuilder.insert(any)).thenReturn(mockBuilder);
          when(mockBuilder.then()).thenAnswer((_) async => []);

          // Mock für neues Abonnement nach Erstellung
          when(mockFilterBuilder.single()).thenAnswer(
            (_) async => {
              'id': 'sub-new',
              'user_id': 'test-user-id',
              'plan_type': 'free',
              'is_premium': false,
              'status': 'active',
            },
          );

          // Mock für Counter-Erstellung
          when(
            mockSupabase.from('application_counters'),
          ).thenReturn(mockQueryBuilder);
          when(
            mockFilterBuilder.single(),
          ).thenThrow(Exception('No counter found'));

          // Act: Bewerbung abziehen
          final result = await service.incrementApplicationCounter();

          // Assert: Sollte erfolgreich sein (neuer Counter mit 9 Bewerbungen)
          expect(result, isTrue);
        },
      );
    });

    group('Edge Cases', () {
      test('Negative remaining applications - should fail', () async {
        // Arrange: Negative verbleibende Bewerbungen (Dateninkonsistenz)
        when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'id': 'sub-1',
            'user_id': 'test-user-id',
            'plan_type': 'free',
            'is_premium': false,
            'status': 'active',
          },
        );

        when(
          mockSupabase.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'user_id': 'test-user-id',
            'total_applications': 10,
            'remaining_applications': -1,
            'plan_type': 'free',
          },
        );

        // Act: Bewerbung abziehen versuchen
        final result = await service.incrementApplicationCounter();

        // Assert: Sollte fehlschlagen
        expect(result, isFalse);
      });

      test('Multiple rapid decrements - should handle concurrency', () async {
        // Arrange: Mehrere schnelle Abzüge simulieren
        when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('status', 'active'),
        ).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.single()).thenAnswer(
          (_) async => {
            'id': 'sub-1',
            'user_id': 'test-user-id',
            'plan_type': 'free',
            'is_premium': false,
            'status': 'active',
          },
        );

        when(
          mockSupabase.from('application_counters'),
        ).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockFilterBuilder);

        // Simuliere verschiedene Zustände bei schnellen Aufrufen
        var callCount = 0;
        when(mockFilterBuilder.single()).thenAnswer((_) async {
          callCount++;
          return {
            'user_id': 'test-user-id',
            'total_applications': 10,
            'remaining_applications': 11 - callCount, // 10, 9, 8, etc.
            'plan_type': 'free',
          };
        });

        when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
        when(
          mockFilterBuilder.eq('user_id', 'test-user-id'),
        ).thenReturn(mockBuilder);
        when(mockBuilder.then()).thenAnswer((_) async => []);

        // Act: Mehrere schnelle Abzüge
        final results = await Future.wait([
          service.incrementApplicationCounter(),
          service.incrementApplicationCounter(),
          service.incrementApplicationCounter(),
        ]);

        // Assert: Alle sollten erfolgreich sein
        expect(results, everyElement(isTrue));
      });
    });
  });
}

import 'dart:io';

/// Test-Runner für alle Application Counter Tests
/// 
/// Führt verschiedene Test-Szenarien aus:
/// 1. Unit Tests für SubscriptionManagementService
/// 2. Widget Tests für JobDetailScreen
/// 3. Integration Tests für End-to-End Flows
/// 
/// Usage: dart test/run_all_tests.dart
void main() async {
  print('🚀 Starte Application Counter Test Suite...\n');

  // Test-Szenarien definieren
  final testScenarios = [
    TestScenario(
      name: 'Unit Tests - SubscriptionManagementService',
      command: 'flutter test test/integration/application_counter_test.dart',
      description: 'Testet die Guthaben-Abzug-Logik isoliert',
    ),
    TestScenario(
      name: 'Widget Tests - JobDetailScreen',
      command: 'flutter test test/widget/job_detail_screen_test.dart',
      description: 'Testet UI-Interaktionen und State-Management',
    ),
    TestScenario(
      name: 'E2E Tests - Application Flow',
      command: 'flutter test integration_test/application_flow_test.dart',
      description: 'Testet komplette Bewerbungsflows',
    ),
  ];

  int passedTests = 0;
  int failedTests = 0;
  List<String> failedScenarios = [];

  // Führe alle Test-Szenarien aus
  for (final scenario in testScenarios) {
    print('📋 ${scenario.name}');
    print('   ${scenario.description}');
    print('   Kommando: ${scenario.command}\n');

    try {
      final result = await Process.run(
        'flutter',
        scenario.command.split(' ').skip(1).toList(),
        runInShell: true,
      );

      if (result.exitCode == 0) {
        print('✅ ${scenario.name} - ERFOLGREICH\n');
        passedTests++;
      } else {
        print('❌ ${scenario.name} - FEHLGESCHLAGEN');
        print('   Fehler: ${result.stderr}');
        print('   Output: ${result.stdout}\n');
        failedTests++;
        failedScenarios.add(scenario.name);
      }
    } catch (e) {
      print('❌ ${scenario.name} - FEHLER BEIM AUSFÜHREN');
      print('   Exception: $e\n');
      failedTests++;
      failedScenarios.add(scenario.name);
    }

    // Kurze Pause zwischen Tests
    await Future.delayed(Duration(seconds: 1));
  }

  // Zusammenfassung
  print('📊 TEST ZUSAMMENFASSUNG');
  print('=' * 50);
  print('✅ Erfolgreich: $passedTests');
  print('❌ Fehlgeschlagen: $failedTests');
  print('📈 Gesamt: ${passedTests + failedTests}');

  if (failedScenarios.isNotEmpty) {
    print('\n❌ Fehlgeschlagene Tests:');
    for (final scenario in failedScenarios) {
      print('   - $scenario');
    }
  }

  print('\n🎯 SPEZIFISCHE TEST-SZENARIEN:');
  print('=' * 50);
  
  print('\n1️⃣ FREE USER SZENARIEN:');
  print('   ✓ User mit 10 Bewerbungen → Erfolgreich abziehen auf 9');
  print('   ✓ User mit 1 Bewerbung → Erfolgreich abziehen auf 0');
  print('   ✓ User mit 0 Bewerbungen → Abzug fehlschlägt');
  print('   ✓ Negative Bewerbungen → Abzug fehlschlägt');
  
  print('\n2️⃣ PREMIUM USER SZENARIEN:');
  print('   ✓ Unlimited User → Kein Abzug, immer erfolgreich');
  print('   ✓ Premium User → Normale Abzug-Logik');
  
  print('\n3️⃣ ERROR HANDLING SZENARIEN:');
  print('   ✓ Kein angemeldeter User → Fehlschlag');
  print('   ✓ Database-Fehler → Fehlschlag');
  print('   ✓ Kein Abonnement → Standard-Abonnement erstellen');
  print('   ✓ Mehrfache schnelle Klicks → Nur einmal abziehen');
  
  print('\n4️⃣ UI/UX SZENARIEN:');
  print('   ✓ Guthaben wird VOR Generierung abgezogen');
  print('   ✓ UI aktualisiert sich sofort nach Abzug');
  print('   ✓ Button deaktiviert während Verarbeitung');
  print('   ✓ Loading-Indikator während Verarbeitung');
  
  print('\n5️⃣ EDGE CASES:');
  print('   ✓ Concurrent Zugriffe → Race Conditions vermeiden');
  print('   ✓ Netzwerkfehler → Graceful Degradation');
  print('   ✓ App-Neustart → Persistente Zähler');

  // Exit Code setzen
  exit(failedTests > 0 ? 1 : 0);
}

class TestScenario {
  final String name;
  final String command;
  final String description;

  TestScenario({
    required this.name,
    required this.command,
    required this.description,
  });
}

/// Hilfsfunktionen für manuelle Tests
class ManualTestHelper {
  /// Simuliert verschiedene User-Zustände für manuelle Tests
  static void printManualTestInstructions() {
    print('\n🔧 MANUELLE TEST-ANWEISUNGEN:');
    print('=' * 50);
    
    print('\n📱 APP STARTEN UND TESTEN:');
    print('1. flutter run');
    print('2. Als Free-User anmelden');
    print('3. Bewerbungszähler prüfen (sollte 10 zeigen)');
    print('4. Stellenanzeige öffnen');
    print('5. "Bewerben" klicken');
    print('6. Zähler sollte sofort auf 9 reduziert werden');
    print('7. Bewerbung sollte generiert werden');
    print('8. Prozess 5-7 mal wiederholen');
    print('9. Bei 0 Bewerbungen sollte Button deaktiviert sein');
    
    print('\n🔄 VERSCHIEDENE SZENARIEN TESTEN:');
    print('• Free User (10 Bewerbungen)');
    print('• Premium User (unlimited)');
    print('• User mit 0 Bewerbungen');
    print('• Netzwerkfehler simulieren');
    print('• App-Neustart während Generierung');
    print('• Mehrfache schnelle Klicks');
    
    print('\n📊 ERWARTETE ERGEBNISSE:');
    print('✓ Guthaben wird VOR Generierung abgezogen');
    print('✓ UI aktualisiert sich sofort');
    print('✓ Fehlerhafte Generierung zieht trotzdem Guthaben ab');
    print('✓ Premium-User haben keine Beschränkungen');
    print('✓ 0-Guthaben-User können nicht bewerben');
  }
  
  /// Erstellt Test-User mit verschiedenen Guthaben-Ständen
  static void createTestUsers() {
    print('\n👥 TEST-USER ERSTELLEN:');
    print('=' * 30);
    print('1. Free User mit 10 Bewerbungen');
    print('2. Free User mit 1 Bewerbung');
    print('3. Free User mit 0 Bewerbungen');
    print('4. Premium User (unlimited)');
    print('5. User ohne Abonnement');
    
    print('\n💡 TIPP: Verwende die Admin-Konsole oder');
    print('   direkte Supabase-Queries um Test-Daten zu erstellen.');
  }
}

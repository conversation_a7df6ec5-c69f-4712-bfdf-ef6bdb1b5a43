import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Application Counter Service Tests', () {
    group('Plan-spezifische Limits Tests', () {
      test('Basic Plan hat 30 Bewerbungen', () {
        // Arrange
        const planType = 'basic';

        // Act
        final limit = _getTotalApplicationsForPlan(planType);

        // Assert
        expect(limit, equals(30));
      });

      test('Pro Plan hat 150 Bewerbungen', () {
        // Arrange
        const planType = 'pro';

        // Act
        final limit = _getTotalApplicationsForPlan(planType);

        // Assert
        expect(limit, equals(150));
      });

      test('Premium Plan hat unbegrenzte Bewerbungen', () {
        // Arrange
        const planType = 'premium';

        // Act
        final limit = _getTotalApplicationsForPlan(planType);

        // Assert
        expect(limit, equals(-1)); // -1 bedeutet unbegrenzt
      });

      test('Unlimited Plan hat unbegrenzte Bewerbungen', () {
        // Arrange
        const planType = 'unlimited';

        // Act
        final limit = _getTotalApplicationsForPlan(planType);

        // Assert
        expect(limit, equals(-1)); // -1 bedeutet unbegrenzt
      });

      test('Unbekannter Plan hat Fallback-Limit von 5', () {
        // Arrange
        const unknownPlans = ['unknown', 'test', '', null];

        // Act & Assert
        for (final planType in unknownPlans) {
          final limit = _getTotalApplicationsForPlan(planType);
          expect(
            limit,
            equals(5),
            reason: 'Plan "$planType" sollte Fallback-Limit haben',
          );
        }
      });

      test('Case-insensitive Plan-Erkennung funktioniert', () {
        // Arrange & Act & Assert
        expect(_getTotalApplicationsForPlan('BASIC'), equals(30));
        expect(_getTotalApplicationsForPlan('Basic'), equals(30));
        expect(_getTotalApplicationsForPlan('bAsIc'), equals(30));
        expect(_getTotalApplicationsForPlan('PRO'), equals(150));
        expect(_getTotalApplicationsForPlan('Pro'), equals(150));
        expect(_getTotalApplicationsForPlan('UNLIMITED'), equals(-1));
        expect(_getTotalApplicationsForPlan('Unlimited'), equals(-1));
      });
    });

    group('Counter Logic Tests', () {
      test('Neuer Counter wird korrekt initialisiert', () {
        // Arrange
        const planType = 'basic';
        const expectedTotal = 30;

        // Act
        final counterData = _simulateNewCounter(planType);

        // Assert
        expect(counterData['remaining'], equals(expectedTotal));
        expect(counterData['total'], equals(expectedTotal));
        expect(counterData['unlimited'], isFalse);
      });

      test('Unlimited Plan Counter wird korrekt behandelt', () {
        // Arrange
        const planType = 'unlimited';

        // Act
        final counterData = _simulateUnlimitedCounter();

        // Assert
        expect(counterData['remaining'], equals(-1));
        expect(counterData['total'], equals(-1));
        expect(counterData['unlimited'], isTrue);
      });

      test('Counter Dekrement funktioniert korrekt', () {
        // Arrange
        var remaining = 30;
        const total = 30;

        // Act & Assert - Mehrere Bewerbungen
        for (int i = 0; i < 5; i++) {
          final canApply = _simulateIncrementCounter(remaining);
          expect(canApply, isTrue, reason: 'Bewerbung $i sollte möglich sein');
          if (canApply) remaining--;
        }

        expect(remaining, equals(25));
      });

      test('Counter erreicht Limit korrekt', () {
        // Arrange
        var remaining = 2;

        // Act & Assert
        // Erste Bewerbung - sollte funktionieren
        expect(_simulateIncrementCounter(remaining), isTrue);
        remaining--;

        // Zweite Bewerbung - sollte funktionieren
        expect(_simulateIncrementCounter(remaining), isTrue);
        remaining--;

        // Dritte Bewerbung - sollte fehlschlagen (Limit erreicht)
        expect(_simulateIncrementCounter(remaining), isFalse);
      });

      test('Counter Reset funktioniert korrekt', () {
        // Arrange
        const planType = 'pro';
        const originalTotal = 150;
        var currentRemaining = 50; // Teilweise verbraucht

        // Act
        final resetData = _simulateResetCounter(planType, currentRemaining);

        // Assert
        expect(resetData['remaining'], equals(originalTotal));
        expect(resetData['total'], equals(originalTotal));
        expect(resetData['reset_successful'], isTrue);
      });
    });

    group('Edge Cases Tests', () {
      test('Negative verbleibende Bewerbungen werden korrekt behandelt', () {
        // Arrange
        const negativeRemaining = -5;

        // Act
        final canApply = _simulateIncrementCounter(negativeRemaining);

        // Assert
        expect(canApply, isFalse);
      });

      test('Null-Werte werden korrekt behandelt', () {
        // Arrange
        const Map<String, dynamic> nullCounter = {
          'remaining_applications': null,
          'total_applications': null,
        };

        // Act
        final remaining = nullCounter['remaining_applications'] ?? 0;
        final total = nullCounter['total_applications'] ?? 0;

        // Assert
        expect(remaining, equals(0));
        expect(total, equals(0));
      });

      test('Sehr große Zahlen werden korrekt behandelt', () {
        // Arrange
        const veryLargeNumber = 999999999;

        // Act
        final canApply = _simulateIncrementCounter(veryLargeNumber);

        // Assert
        expect(canApply, isTrue);
      });

      test('Plan-Upgrade Szenario funktioniert', () {
        // Arrange - User hatte Basic Plan mit 5 verbleibenden Bewerbungen
        const oldPlan = 'basic';
        const newPlan = 'pro';
        const currentRemaining = 5;

        // Act - Simuliere Plan-Upgrade
        final oldLimit = _getTotalApplicationsForPlan(oldPlan);
        final newLimit = _getTotalApplicationsForPlan(newPlan);
        final upgradeData = _simulatePlanUpgrade(
          currentRemaining,
          oldLimit,
          newLimit,
        );

        // Assert
        expect(oldLimit, equals(30));
        expect(newLimit, equals(150));
        expect(upgradeData['new_remaining'], greaterThan(currentRemaining));
        expect(upgradeData['new_total'], equals(newLimit));
      });
    });

    group('DateTime und Reset Logic Tests', () {
      test('Monatlicher Reset funktioniert korrekt', () {
        // Arrange
        final now = DateTime.now();
        final lastReset = DateTime(
          now.year,
          now.month - 1,
          15,
        ); // Letzter Monat

        // Act
        final shouldReset = _shouldResetMonthly(lastReset, now);

        // Assert
        expect(shouldReset, isTrue);
      });

      test('Kein Reset innerhalb desselben Monats', () {
        // Arrange
        final now = DateTime.now();
        final recentReset = DateTime(now.year, now.month, 5); // Dieser Monat

        // Act
        final shouldReset = _shouldResetMonthly(recentReset, now);

        // Assert
        expect(shouldReset, isFalse);
      });

      test('Jahreswechsel Reset funktioniert', () {
        // Arrange
        final now = DateTime(2024, 1, 15);
        final lastReset = DateTime(2023, 12, 20); // Letztes Jahr

        // Act
        final shouldReset = _shouldResetMonthly(lastReset, now);

        // Assert
        expect(shouldReset, isTrue);
      });

      test('Month-Year Format ist korrekt', () {
        // Arrange
        final testDate = DateTime(2024, 3, 15);

        // Act
        final monthYear = _formatMonthYear(testDate);

        // Assert
        expect(monthYear, equals('2024-03'));
      });
    });

    group('Error Handling Tests', () {
      test('Fehlerhafte Counter-Daten werden abgefangen', () {
        // Arrange
        const Map<String, dynamic> corruptedCounter = {
          'remaining_applications': 'invalid',
          'total_applications': 'also_invalid',
        };

        // Act
        final remaining = _safeParseInt(
          corruptedCounter['remaining_applications'],
        );
        final total = _safeParseInt(corruptedCounter['total_applications']);

        // Assert
        expect(remaining, equals(0));
        expect(total, equals(0));
      });

      test('Exception Handling gibt sichere Fallback-Werte zurück', () {
        // Arrange & Act
        final fallbackData = _simulateErrorFallback();

        // Assert
        expect(fallbackData['remaining'], equals(0));
        expect(fallbackData['total'], equals(0));
        expect(fallbackData['unlimited'], isFalse);
      });
    });

    group('Business Logic Validation Tests', () {
      test('User kann nicht mehr Bewerbungen senden als Limit', () {
        // Arrange
        const planType = 'basic';
        const limit = 30;
        var remaining = limit;
        var successfulApplications = 0;

        // Act - Versuche mehr als das Limit zu senden
        for (int i = 0; i < limit + 10; i++) {
          if (_simulateIncrementCounter(remaining)) {
            remaining--;
            successfulApplications++;
          }
        }

        // Assert
        expect(successfulApplications, equals(limit));
        expect(remaining, equals(0));
      });

      test('Unlimited User kann unbegrenzt Bewerbungen senden', () {
        // Arrange
        const planType = 'unlimited';
        var successfulApplications = 0;

        // Act - Versuche viele Bewerbungen
        for (int i = 0; i < 1000; i++) {
          if (_simulateUnlimitedIncrement()) {
            successfulApplications++;
          }
        }

        // Assert
        expect(successfulApplications, equals(1000));
      });
    });

    group('Database Simulation Tests', () {
      test('Counter Creation Simulation funktioniert', () {
        // Arrange
        const userId = 'user-123';
        const planType = 'pro';
        const totalApplications = 150;

        // Act
        final counterData = _simulateCreateCounter(
          userId: userId,
          totalApplications: totalApplications,
          remainingApplications: totalApplications,
        );

        // Assert
        expect(counterData['user_id'], equals(userId));
        expect(counterData['total_applications'], equals(totalApplications));
        expect(
          counterData['remaining_applications'],
          equals(totalApplications),
        );
        expect(counterData['created_at'], isNotNull);
        expect(counterData['updated_at'], isNotNull);
      });

      test('Counter Update Simulation funktioniert', () {
        // Arrange
        const userId = 'user-123';
        const originalRemaining = 100;
        const newRemaining = 99;

        // Act
        final updateData = _simulateUpdateCounter(
          userId: userId,
          newRemaining: newRemaining,
        );

        // Assert
        expect(updateData['remaining_applications'], equals(newRemaining));
        expect(updateData['updated_at'], isNotNull);
        expect(updateData['user_id'], equals(userId));
      });

      test('Multiple User Counter Simulation', () {
        // Arrange
        const users = ['user-1', 'user-2', 'user-3'];
        const plans = ['basic', 'pro', 'unlimited'];

        // Act & Assert
        for (int i = 0; i < users.length; i++) {
          final userId = users[i];
          final planType = plans[i];
          final limit = _getTotalApplicationsForPlan(planType);

          final counterData = _simulateCreateCounter(
            userId: userId,
            totalApplications: limit,
            remainingApplications: limit,
          );

          expect(counterData['user_id'], equals(userId));
          expect(counterData['total_applications'], equals(limit));
        }
      });
    });

    group('Performance und Stress Tests', () {
      test('Viele Counter Operationen funktionieren', () {
        // Arrange
        const iterations = 1000;
        var successfulOperations = 0;

        // Act
        for (int i = 0; i < iterations; i++) {
          final remaining = 100 - (i % 100); // Variiere remaining
          if (_simulateIncrementCounter(remaining)) {
            successfulOperations++;
          }
        }

        // Assert
        expect(successfulOperations, greaterThan(0));
        expect(successfulOperations, lessThanOrEqualTo(iterations));
      });

      test('Concurrent Counter Access Simulation', () {
        // Arrange
        var remaining = 50;
        const concurrentUsers = 10;
        var successfulApplications = 0;

        // Act - Simuliere gleichzeitige Zugriffe
        for (int i = 0; i < concurrentUsers; i++) {
          if (_simulateIncrementCounter(remaining)) {
            remaining--; // Simuliere atomare Dekrement-Operation
            successfulApplications++;
          }
        }

        // Assert
        expect(successfulApplications, lessThanOrEqualTo(50));
        expect(remaining, greaterThanOrEqualTo(0));
      });
    });

    group('Integration Scenario Tests', () {
      test('Kompletter User Journey funktioniert', () {
        // Arrange - Neuer User mit Basic Plan
        const userId = 'journey-user';
        const initialPlan = 'basic';
        var currentPlan = initialPlan;
        var remaining = _getTotalApplicationsForPlan(currentPlan);

        // Act & Assert - Phase 1: Erste Bewerbungen
        for (int i = 0; i < 10; i++) {
          expect(_simulateIncrementCounter(remaining), isTrue);
          remaining--;
        }
        expect(remaining, equals(20)); // 30 - 10 = 20

        // Phase 2: Plan Upgrade zu Pro
        currentPlan = 'pro';
        final upgradeData = _simulatePlanUpgrade(remaining, 30, 150);
        remaining = upgradeData['new_remaining'];
        expect(remaining, equals(140)); // 150 - 10 = 140

        // Phase 3: Weitere Bewerbungen
        for (int i = 0; i < 50; i++) {
          expect(_simulateIncrementCounter(remaining), isTrue);
          remaining--;
        }
        expect(remaining, equals(90)); // 140 - 50 = 90

        // Phase 4: Monatlicher Reset
        final resetData = _simulateResetCounter(currentPlan, remaining);
        remaining = resetData['remaining'];
        expect(remaining, equals(150)); // Zurück auf Pro-Limit
      });

      test('Edge Case: Plan Downgrade funktioniert', () {
        // Arrange - User mit Pro Plan, viele Bewerbungen verbraucht
        const currentPlan = 'pro';
        const newPlan = 'basic';
        const currentRemaining = 100; // 50 verbraucht von 150

        // Act - Simuliere Plan Downgrade
        final oldLimit = _getTotalApplicationsForPlan(currentPlan);
        final newLimit = _getTotalApplicationsForPlan(newPlan);
        final downgradeData = _simulatePlanDowngrade(
          currentRemaining,
          oldLimit,
          newLimit,
        );

        // Assert
        expect(oldLimit, equals(150));
        expect(newLimit, equals(30));
        // Bei Downgrade: User behält mindestens 0, maximal das neue Limit
        expect(downgradeData['new_remaining'], greaterThanOrEqualTo(0));
        expect(downgradeData['new_remaining'], lessThanOrEqualTo(newLimit));
      });

      test('Free Trial zu Premium Upgrade', () {
        // Arrange - Free Trial User (10 Bewerbungen)
        const freePlan = 'free';
        const premiumPlan = 'unlimited';
        var remaining = _getTotalApplicationsForPlan(freePlan); // 10

        // Act - Verbrauche alle Free Bewerbungen
        for (int i = 0; i < 10; i++) {
          expect(_simulateIncrementCounter(remaining), isTrue);
          remaining--;
        }
        expect(remaining, equals(0));
        expect(
          _simulateIncrementCounter(remaining),
          isFalse,
        ); // Keine mehr möglich

        // Upgrade zu Premium
        remaining = -1; // Unlimited
        expect(_simulateUnlimitedIncrement(), isTrue); // Jetzt unbegrenzt
      });
    });

    group('INDIVIDUELLER Reset-Timer Tests', () {
      test(
        'Neuer User bekommt sofort 10 Bewerbungen mit individuellem Timer',
        () {
          // Arrange
          const userId = 'new-user-123';
          final now = DateTime.now();

          // Act
          final counterData = _simulateNewUserWithIndividualTimer(userId, now);

          // Assert
          expect(counterData['user_id'], equals(userId));
          expect(counterData['remaining_applications'], equals(10));
          expect(counterData['total_applications'], equals(10));
          expect(counterData['free_reset_date'], isNotNull);

          // Nächster Reset sollte in 7 Tagen sein
          final resetDate = DateTime.parse(counterData['free_reset_date']);
          final expectedReset = now.add(const Duration(days: 7));
          expect(
            resetDate.difference(expectedReset).inHours.abs(),
            lessThan(1),
          );
        },
      );

      test('User A und User B haben unterschiedliche Reset-Zeiten', () {
        // Arrange
        const userA = 'user-a';
        const userB = 'user-b';
        final nowA = DateTime.now();
        final nowB = nowA.add(const Duration(hours: 12)); // User B 12h später

        // Act
        final counterA = _simulateNewUserWithIndividualTimer(userA, nowA);
        final counterB = _simulateNewUserWithIndividualTimer(userB, nowB);

        // Assert
        final resetA = DateTime.parse(counterA['free_reset_date']);
        final resetB = DateTime.parse(counterB['free_reset_date']);

        // User B's Reset sollte 12h nach User A's Reset sein
        expect(resetB.difference(resetA).inHours, equals(12));
        expect(resetA.add(const Duration(hours: 12)), equals(resetB));
      });

      test('Individueller Reset nach 7 Tagen funktioniert', () {
        // Arrange
        const userId = 'test-user';
        final accountCreated = DateTime.now().subtract(const Duration(days: 8));
        final lastReset = accountCreated.add(
          const Duration(days: 1),
        ); // Reset vor 7 Tagen

        // Act
        final shouldReset = _simulateIndividualResetCheck(
          lastReset,
          DateTime.now(),
        );

        // Assert
        expect(shouldReset, isTrue);
      });

      test('Kein Reset vor 7 Tagen', () {
        // Arrange
        final lastReset = DateTime.now().subtract(
          const Duration(days: 5),
        ); // Vor 5 Tagen

        // Act
        final shouldReset = _simulateIndividualResetCheck(
          lastReset,
          DateTime.now(),
        );

        // Assert
        expect(shouldReset, isFalse);
      });

      test('Exakt nach 7 Tagen wird Reset ausgelöst', () {
        // Arrange
        final lastReset = DateTime.now().subtract(
          const Duration(days: 7, hours: 1),
        ); // 7 Tage + 1h

        // Act
        final shouldReset = _simulateIndividualResetCheck(
          lastReset,
          DateTime.now(),
        );

        // Assert
        expect(shouldReset, isTrue);
      });

      test('Mehrere User mit verschiedenen Reset-Zyklen', () {
        // Arrange
        final baseTime = DateTime.now();
        final users = [
          {
            'id': 'user-1',
            'created': baseTime.subtract(const Duration(days: 0)),
          }, // Heute
          {
            'id': 'user-2',
            'created': baseTime.subtract(const Duration(days: 2)),
          }, // Vor 2 Tagen
          {
            'id': 'user-3',
            'created': baseTime.subtract(const Duration(days: 5)),
          }, // Vor 5 Tagen
          {
            'id': 'user-4',
            'created': baseTime.subtract(const Duration(days: 8)),
          }, // Vor 8 Tagen
        ];

        // Act & Assert
        for (final user in users) {
          final userId = user['id'] as String;
          final createdAt = user['created'] as DateTime;
          final counter = _simulateNewUserWithIndividualTimer(
            userId,
            createdAt,
          );

          final resetDate = DateTime.parse(counter['free_reset_date']);
          final expectedReset = createdAt.add(const Duration(days: 7));

          expect(resetDate.day, equals(expectedReset.day));
          expect(resetDate.hour, equals(expectedReset.hour));
        }
      });

      test(
        'Reset-Datum wird korrekt berechnet für verschiedene Wochentage',
        () {
          // Arrange - Teste verschiedene Wochentage
          final monday = DateTime(2024, 1, 1); // Montag
          final wednesday = DateTime(2024, 1, 3); // Mittwoch
          final friday = DateTime(2024, 1, 5); // Freitag
          final sunday = DateTime(2024, 1, 7); // Sonntag

          final testDates = [monday, wednesday, friday, sunday];

          // Act & Assert
          for (final date in testDates) {
            final counter = _simulateNewUserWithIndividualTimer(
              'user-${date.day}',
              date,
            );
            final resetDate = DateTime.parse(counter['free_reset_date']);
            final expectedReset = date.add(const Duration(days: 7));

            expect(resetDate.weekday, equals(expectedReset.weekday));
            expect(
              resetDate.difference(expectedReset).inHours.abs(),
              lessThan(1),
            );
          }
        },
      );

      test('Legacy User ohne Reset-Datum bekommt individuellen Timer', () {
        // Arrange - Simuliere alten User ohne free_reset_date
        const userId = 'legacy-user';
        final accountAge = DateTime.now().subtract(const Duration(days: 30));

        // Act
        final updatedCounter = _simulateLegacyUserUpdate(userId, accountAge);

        // Assert
        expect(updatedCounter['free_reset_date'], isNotNull);
        expect(updatedCounter['individual_timer_set'], isTrue);

        final resetDate = DateTime.parse(updatedCounter['free_reset_date']);
        final now = DateTime.now();
        expect(resetDate.difference(now).inDays, greaterThanOrEqualTo(6));
        expect(resetDate.difference(now).inDays, lessThanOrEqualTo(7));
      });
    });

    group('KRITISCHE Abonnement-Ablauf Tests', () {
      test('Abgelaufenes Abonnement wird automatisch erkannt', () {
        // Arrange
        final now = DateTime.now();
        final expiredDate = now.subtract(
          const Duration(days: 5),
        ); // Vor 5 Tagen abgelaufen

        // Act
        final isExpired = _simulateSubscriptionExpiry(expiredDate, now);

        // Assert
        expect(isExpired, isTrue);
      });

      test('Aktives Abonnement wird nicht als abgelaufen erkannt', () {
        // Arrange
        final now = DateTime.now();
        final futureDate = now.add(
          const Duration(days: 30),
        ); // Läuft noch 30 Tage

        // Act
        final isExpired = _simulateSubscriptionExpiry(futureDate, now);

        // Assert
        expect(isExpired, isFalse);
      });

      test('Abonnement das heute abläuft wird als abgelaufen erkannt', () {
        // Arrange
        final now = DateTime.now();
        final todayExpiry = DateTime(
          now.year,
          now.month,
          now.day,
          0,
          0,
          0,
        ); // Heute um Mitternacht

        // Act
        final isExpired = _simulateSubscriptionExpiry(todayExpiry, now);

        // Assert
        expect(isExpired, isTrue); // Bereits abgelaufen
      });

      test('Abgelaufenes Pro-Abonnement wird auf Free downgraded', () {
        // Arrange
        const userId = 'expired-pro-user';
        final expiredDate = DateTime.now().subtract(const Duration(days: 10));

        // Act
        final downgradeResult = _simulateExpiredSubscriptionDowngrade(
          userId: userId,
          originalPlan: 'pro',
          expiredDate: expiredDate,
        );

        // Assert
        expect(downgradeResult['user_id'], equals(userId));
        expect(downgradeResult['new_plan'], equals('free'));
        expect(downgradeResult['is_premium'], isFalse);
        expect(
          downgradeResult['downgrade_reason'],
          equals('subscription_expired'),
        );
        expect(downgradeResult['expired_date'], isNotNull);
      });

      test('Abgelaufenes Unlimited-Abonnement wird auf Free downgraded', () {
        // Arrange
        const userId = 'expired-unlimited-user';
        final expiredDate = DateTime.now().subtract(const Duration(days: 1));

        // Act
        final downgradeResult = _simulateExpiredSubscriptionDowngrade(
          userId: userId,
          originalPlan: 'unlimited',
          expiredDate: expiredDate,
        );

        // Assert
        expect(downgradeResult['user_id'], equals(userId));
        expect(downgradeResult['new_plan'], equals('free'));
        expect(downgradeResult['is_premium'], isFalse);
        expect(downgradeResult['original_plan'], equals('unlimited'));
        expect(
          downgradeResult['downgrade_reason'],
          equals('subscription_expired'),
        );
      });

      test('Mehrere abgelaufene Abonnements werden korrekt behandelt', () {
        // Arrange
        final now = DateTime.now();
        final testCases = [
          {'plan': 'pro', 'expired_days_ago': 1},
          {'plan': 'unlimited', 'expired_days_ago': 7},
          {'plan': 'basic', 'expired_days_ago': 30},
        ];

        // Act & Assert
        for (int i = 0; i < testCases.length; i++) {
          final testCase = testCases[i];
          final expiredDate = now.subtract(
            Duration(days: testCase['expired_days_ago'] as int),
          );

          final result = _simulateExpiredSubscriptionDowngrade(
            userId: 'user-$i',
            originalPlan: testCase['plan'] as String,
            expiredDate: expiredDate,
          );

          expect(result['new_plan'], equals('free'));
          expect(result['is_premium'], isFalse);
          expect(result['downgrade_reason'], equals('subscription_expired'));
        }
      });

      test('Gültiges Abonnement wird nicht downgraded', () {
        // Arrange
        const userId = 'active-pro-user';
        final futureDate = DateTime.now().add(const Duration(days: 15));

        // Act
        final isExpired = _simulateSubscriptionExpiry(
          futureDate,
          DateTime.now(),
        );

        // Assert
        expect(isExpired, isFalse);

        // Kein Downgrade sollte stattfinden
        final result = _simulateActiveSubscriptionCheck(
          userId: userId,
          plan: 'pro',
          endDate: futureDate,
        );

        expect(result['should_downgrade'], isFalse);
        expect(result['is_active'], isTrue);
        expect(result['plan'], equals('pro'));
      });

      test('Edge Case: Null-Ablaufdatum wird als abgelaufen behandelt', () {
        // Arrange & Act
        final isExpired = _simulateSubscriptionExpiry(null, DateTime.now());

        // Assert
        expect(isExpired, isTrue); // Null-Datum = abgelaufen
      });

      test('Edge Case: Sehr altes Abonnement wird korrekt erkannt', () {
        // Arrange
        final veryOldDate = DateTime(2020, 1, 1); // 4+ Jahre alt
        final now = DateTime.now();

        // Act
        final isExpired = _simulateSubscriptionExpiry(veryOldDate, now);

        // Assert
        expect(isExpired, isTrue);
        expect(now.difference(veryOldDate).inDays, greaterThan(1000));
      });
    });
  });
}

// Helper Functions für Test-Simulationen

/// Simuliert _getTotalApplicationsForPlan Methode
int _getTotalApplicationsForPlan(String? planType) {
  switch (planType?.toLowerCase()) {
    case 'basic':
      return 30;
    case 'pro':
      return 150;
    case 'premium':
    case 'unlimited':
      return -1; // -1 bedeutet unbegrenzt
    default:
      return 10; // Fallback
  }
}

/// Simuliert neuen Counter
Map<String, dynamic> _simulateNewCounter(String planType) {
  final total = _getTotalApplicationsForPlan(planType);
  return {'remaining': total, 'total': total, 'unlimited': total == -1};
}

/// Simuliert Unlimited Counter
Map<String, dynamic> _simulateUnlimitedCounter() {
  return {'remaining': -1, 'total': -1, 'unlimited': true};
}

/// Simuliert Counter Increment
bool _simulateIncrementCounter(int remaining) {
  return remaining > 0;
}

/// Simuliert Unlimited Increment
bool _simulateUnlimitedIncrement() {
  return true; // Unlimited ist immer möglich
}

/// Simuliert Counter Reset
Map<String, dynamic> _simulateResetCounter(
  String planType,
  int currentRemaining,
) {
  final newTotal = _getTotalApplicationsForPlan(planType);
  return {'remaining': newTotal, 'total': newTotal, 'reset_successful': true};
}

/// Simuliert Plan Upgrade
Map<String, dynamic> _simulatePlanUpgrade(
  int currentRemaining,
  int oldLimit,
  int newLimit,
) {
  // Berechne neue verbleibende Bewerbungen basierend auf Upgrade
  final usedApplications = oldLimit - currentRemaining;
  final newRemaining = newLimit - usedApplications;

  return {
    'new_remaining': newRemaining > 0 ? newRemaining : newLimit,
    'new_total': newLimit,
  };
}

/// Simuliert monatlichen Reset Check
bool _shouldResetMonthly(DateTime lastReset, DateTime now) {
  return lastReset.year != now.year || lastReset.month != now.month;
}

/// Formatiert DateTime zu Month-Year String
String _formatMonthYear(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}';
}

/// Sicheres Int-Parsing
int _safeParseInt(dynamic value) {
  if (value is int) return value;
  if (value is String) {
    return int.tryParse(value) ?? 0;
  }
  return 0;
}

/// Simuliert Error Fallback
Map<String, dynamic> _simulateErrorFallback() {
  return {'remaining': 0, 'total': 0, 'unlimited': false};
}

/// Simuliert Counter Creation in Database
Map<String, dynamic> _simulateCreateCounter({
  required String userId,
  required int totalApplications,
  required int remainingApplications,
}) {
  final now = DateTime.now();
  return {
    'id': 'counter-$userId-${now.millisecondsSinceEpoch}',
    'user_id': userId,
    'total_applications': totalApplications,
    'remaining_applications': remainingApplications,
    'created_at': now.toIso8601String(),
    'updated_at': now.toIso8601String(),
    'reset_date': now.toIso8601String(),
  };
}

/// Simuliert Counter Update in Database
Map<String, dynamic> _simulateUpdateCounter({
  required String userId,
  required int newRemaining,
}) {
  return {
    'user_id': userId,
    'remaining_applications': newRemaining,
    'updated_at': DateTime.now().toIso8601String(),
  };
}

/// Simuliert Plan Downgrade
Map<String, dynamic> _simulatePlanDowngrade(
  int currentRemaining,
  int oldLimit,
  int newLimit,
) {
  // Bei Downgrade: Berechne wie viele Bewerbungen bereits verwendet wurden
  final usedApplications = oldLimit - currentRemaining;

  // Neue verbleibende Bewerbungen: Neues Limit minus bereits verwendete
  // Aber mindestens 0 und maximal das neue Limit
  final newRemaining = (newLimit - usedApplications).clamp(0, newLimit);

  return {
    'new_remaining': newRemaining,
    'new_total': newLimit,
    'downgrade_successful': true,
  };
}

/// Simuliert neuen User mit INDIVIDUELLEM Reset-Timer
Map<String, dynamic> _simulateNewUserWithIndividualTimer(
  String userId,
  DateTime createdAt,
) {
  return {
    'id': 'counter-$userId-${createdAt.millisecondsSinceEpoch}',
    'user_id': userId,
    'remaining_applications': 10, // Neuer User bekommt sofort 10 Bewerbungen
    'total_applications': 10,
    'free_reset_date':
        createdAt
            .add(const Duration(days: 7))
            .toIso8601String(), // INDIVIDUELLER Reset in 7 Tagen
    'created_at': createdAt.toIso8601String(),
    'updated_at': createdAt.toIso8601String(),
    'individual_timer': true,
  };
}

/// Simuliert individuellen Reset-Check
bool _simulateIndividualResetCheck(
  DateTime lastResetDate,
  DateTime currentTime,
) {
  final daysSinceReset = currentTime.difference(lastResetDate).inDays;
  return daysSinceReset >= 7; // Reset nach 7 Tagen
}

/// Simuliert Legacy User Update mit individuellem Timer
Map<String, dynamic> _simulateLegacyUserUpdate(
  String userId,
  DateTime accountAge,
) {
  final now = DateTime.now();
  return {
    'user_id': userId,
    'free_reset_date':
        now
            .add(const Duration(days: 7))
            .toIso8601String(), // Timer startet jetzt
    'updated_at': now.toIso8601String(),
    'individual_timer_set': true,
    'legacy_user_updated': true,
  };
}

/// Simuliert Abonnement-Ablauf-Prüfung
bool _simulateSubscriptionExpiry(DateTime? endDate, DateTime currentTime) {
  if (endDate == null) return true; // Null-Datum = abgelaufen
  return endDate.isBefore(currentTime);
}

/// Simuliert Downgrade eines abgelaufenen Abonnements
Map<String, dynamic> _simulateExpiredSubscriptionDowngrade({
  required String userId,
  required String originalPlan,
  required DateTime expiredDate,
}) {
  return {
    'user_id': userId,
    'original_plan': originalPlan,
    'new_plan': 'free',
    'is_premium': false,
    'downgrade_reason': 'subscription_expired',
    'expired_date': expiredDate.toIso8601String(),
    'downgraded_at': DateTime.now().toIso8601String(),
    'status': 'expired',
  };
}

/// Simuliert aktives Abonnement-Check
Map<String, dynamic> _simulateActiveSubscriptionCheck({
  required String userId,
  required String plan,
  required DateTime endDate,
}) {
  final now = DateTime.now();
  final isActive = endDate.isAfter(now);

  return {
    'user_id': userId,
    'plan': plan,
    'end_date': endDate.toIso8601String(),
    'is_active': isActive,
    'should_downgrade': !isActive,
    'days_remaining': isActive ? endDate.difference(now).inDays : 0,
  };
}

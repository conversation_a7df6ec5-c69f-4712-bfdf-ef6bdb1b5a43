import 'package:flutter_test/flutter_test.dart';
import 'package:ki_test/src/infrastructure/services/premium_feature_service.dart';

void main() {
  group('Premium Feature Logic Tests', () {
    group('Feature Access Logic Tests', () {
      test('Unlimited Plan sollte alle Features haben', () {
        // Arrange
        const planType = 'unlimited';
        final allFeatures = PremiumFeature.values;

        // Act & Assert
        for (final feature in allFeatures) {
          final shouldHaveAccess = _simulateFeatureAccess(planType, feature);
          expect(shouldHaveAccess, isTrue, 
                 reason: 'Unlimited Plan sollte Zugriff auf ${feature.name} haben');
        }
      });

      test('Pro Plan sollte korrekte Features haben', () {
        // Arrange
        const planType = 'pro';
        
        // Features die Pro haben sollte
        const expectedFeatures = [
          PremiumFeature.aiCoverLetter,
          PremiumFeature.aiJobSearch,
          PremiumFeature.adFree,
          PremiumFeature.insights,
          PremiumFeature.translation,
        ];
        
        // Features die Pro NICHT haben sollte
        const restrictedFeatures = [
          PremiumFeature.unlimitedFavorites,
        ];

        // Act & Assert
        for (final feature in expectedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isTrue, 
                 reason: 'Pro Plan sollte Zugriff auf ${feature.name} haben');
        }

        for (final feature in restrictedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isFalse, 
                 reason: 'Pro Plan sollte KEINEN Zugriff auf ${feature.name} haben');
        }
      });

      test('Basic Plan sollte korrekte Features haben', () {
        // Arrange
        const planType = 'basic';
        
        // Features die Basic haben sollte
        const expectedFeatures = [
          PremiumFeature.aiCoverLetter,
          PremiumFeature.aiJobSearch,
          PremiumFeature.translation,
        ];
        
        // Features die Basic NICHT haben sollte
        const restrictedFeatures = [
          PremiumFeature.adFree,
          PremiumFeature.insights,
          PremiumFeature.unlimitedFavorites,
        ];

        // Act & Assert
        for (final feature in expectedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isTrue, 
                 reason: 'Basic Plan sollte Zugriff auf ${feature.name} haben');
        }

        for (final feature in restrictedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isFalse, 
                 reason: 'Basic Plan sollte KEINEN Zugriff auf ${feature.name} haben');
        }
      });

      test('Kein Premium Plan sollte nur AI Cover Letter haben', () {
        // Arrange
        const planType = 'none';
        
        // Features die Non-Premium haben sollte
        const expectedFeatures = [
          PremiumFeature.aiCoverLetter,
        ];
        
        // Features die Non-Premium NICHT haben sollte
        const restrictedFeatures = [
          PremiumFeature.aiJobSearch,
          PremiumFeature.adFree,
          PremiumFeature.insights,
          PremiumFeature.translation,
          PremiumFeature.unlimitedFavorites,
        ];

        // Act & Assert
        for (final feature in expectedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isTrue, 
                 reason: 'Non-Premium sollte Zugriff auf ${feature.name} haben');
        }

        for (final feature in restrictedFeatures) {
          final hasAccess = _simulateFeatureAccess(planType, feature);
          expect(hasAccess, isFalse, 
                 reason: 'Non-Premium sollte KEINEN Zugriff auf ${feature.name} haben');
        }
      });
    });

    group('Limit Logic Tests', () {
      test('Favoriten Limits sind korrekt', () {
        // Arrange & Act & Assert
        
        // Non-Premium: 10 Favoriten
        expect(_simulateFavoritesLimit('none', 9), isFalse);
        expect(_simulateFavoritesLimit('none', 10), isTrue);
        expect(_simulateFavoritesLimit('none', 11), isTrue);
        
        // Basic: 10 Favoriten (gleich wie Non-Premium)
        expect(_simulateFavoritesLimit('basic', 9), isFalse);
        expect(_simulateFavoritesLimit('basic', 10), isTrue);
        
        // Pro: 10 Favoriten (kein unlimitedFavorites)
        expect(_simulateFavoritesLimit('pro', 9), isFalse);
        expect(_simulateFavoritesLimit('pro', 10), isTrue);
        
        // Unlimited: Keine Limits
        expect(_simulateFavoritesLimit('unlimited', 100), isFalse);
        expect(_simulateFavoritesLimit('unlimited', 1000), isFalse);
      });

      test('Bewerbungs Limits sind korrekt', () {
        // Arrange & Act & Assert
        
        // Non-Premium: 30 Bewerbungen
        expect(_simulateApplicationsLimit('none', 29), isFalse);
        expect(_simulateApplicationsLimit('none', 30), isTrue);
        expect(_simulateApplicationsLimit('none', 31), isTrue);
        
        // Basic: 30 Bewerbungen
        expect(_simulateApplicationsLimit('basic', 29), isFalse);
        expect(_simulateApplicationsLimit('basic', 30), isTrue);
        
        // Pro: 150 Bewerbungen
        expect(_simulateApplicationsLimit('pro', 149), isFalse);
        expect(_simulateApplicationsLimit('pro', 150), isTrue);
        expect(_simulateApplicationsLimit('pro', 151), isTrue);
        
        // Unlimited: Keine Limits
        expect(_simulateApplicationsLimit('unlimited', 1000), isFalse);
        expect(_simulateApplicationsLimit('unlimited', 10000), isFalse);
      });
    });

    group('Edge Cases Tests', () {
      test('Unbekannte Plan-Typen werden als Basic behandelt', () {
        // Arrange
        const unknownPlanTypes = ['unknown', 'test', '', 'premium_old'];

        // Act & Assert
        for (final planType in unknownPlanTypes) {
          // Sollte wie Basic Plan behandelt werden
          expect(_simulateFeatureAccess(planType, PremiumFeature.aiCoverLetter), 
                 isTrue, reason: '$planType sollte AI Cover Letter haben');
          expect(_simulateFeatureAccess(planType, PremiumFeature.adFree), 
                 isFalse, reason: '$planType sollte KEIN Ad-Free haben');
          expect(_simulateApplicationsLimit(planType, 30), 
                 isTrue, reason: '$planType sollte 30 Bewerbungs-Limit haben');
        }
      });

      test('Grenzwerte funktionieren korrekt', () {
        // Arrange & Act & Assert
        
        // Exakt an der Grenze
        expect(_simulateApplicationsLimit('basic', 30), isTrue);
        expect(_simulateApplicationsLimit('pro', 150), isTrue);
        expect(_simulateFavoritesLimit('basic', 10), isTrue);
        
        // Ein unter der Grenze
        expect(_simulateApplicationsLimit('basic', 29), isFalse);
        expect(_simulateApplicationsLimit('pro', 149), isFalse);
        expect(_simulateFavoritesLimit('basic', 9), isFalse);
        
        // Weit über der Grenze
        expect(_simulateApplicationsLimit('basic', 100), isTrue);
        expect(_simulateApplicationsLimit('pro', 1000), isTrue);
        expect(_simulateFavoritesLimit('basic', 50), isTrue);
      });
    });
  });
}

/// Simuliert Feature-Zugriff basierend auf Plan-Typ
bool _simulateFeatureAccess(String planType, PremiumFeature feature) {
  // Simuliert die Logik aus PremiumFeatureService
  switch (planType.toLowerCase()) {
    case 'unlimited':
      return true; // Unlimited hat alle Features
    
    case 'pro':
      switch (feature) {
        case PremiumFeature.aiCoverLetter:
        case PremiumFeature.aiJobSearch:
        case PremiumFeature.adFree:
        case PremiumFeature.insights:
        case PremiumFeature.translation:
          return true;
        case PremiumFeature.unlimitedFavorites:
          return false;
      }
    
    case 'basic':
      switch (feature) {
        case PremiumFeature.aiCoverLetter:
        case PremiumFeature.aiJobSearch:
        case PremiumFeature.translation:
          return true;
        case PremiumFeature.adFree:
        case PremiumFeature.insights:
        case PremiumFeature.unlimitedFavorites:
          return false;
      }
    
    default: // 'none' oder unbekannt
      switch (feature) {
        case PremiumFeature.aiCoverLetter:
          return true; // Einziges kostenloses Feature
        default:
          return false;
      }
  }
}

/// Simuliert Favoriten-Limit-Check
bool _simulateFavoritesLimit(String planType, int currentCount) {
  switch (planType.toLowerCase()) {
    case 'unlimited':
      return false; // Kein Limit
    default:
      return currentCount >= PremiumFeatureService.maxFavoritesForNonPremium;
  }
}

/// Simuliert Bewerbungs-Limit-Check
bool _simulateApplicationsLimit(String planType, int currentCount) {
  switch (planType.toLowerCase()) {
    case 'unlimited':
      return false; // Kein Limit
    case 'pro':
      return currentCount >= PremiumFeatureService.maxApplicationsForPro;
    default: // basic oder none
      return currentCount >= PremiumFeatureService.maxApplicationsForBasic;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ki_test/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('End-to-End Application Counter Tests', () {
    testWidgets('Complete application flow with counter decrement', (tester) async {
      // Starte die App
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Warte auf App-Initialisierung
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Schritt 1: Navigiere zu einer Stellenanzeige
      final jobCard = find.byType(Card).first;
      expect(jobCard, findsOneWidget);
      
      await tester.tap(jobCard);
      await tester.pumpAndSettle(Duration(seconds: 2));

      // Schritt 2: Prüfe initialen Bewerbungszähler
      final counterWidget = find.textContaining('10'); // Erwarte 10 für Free-User
      expect(counterWidget, findsAtLeastNWidget(1));

      // Schritt 3: Klicke auf Bewerbungsbutton
      final applyButton = find.textContaining('Bewerben');
      expect(applyButton, findsOneWidget);
      
      await tester.tap(applyButton);
      await tester.pumpAndSettle(Duration(seconds: 1));

      // Schritt 4: Warte auf Generierung (kann länger dauern)
      await tester.pumpAndSettle(Duration(seconds: 10));

      // Schritt 5: Prüfe, ob Zähler dekrementiert wurde
      final updatedCounter = find.textContaining('9'); // Sollte jetzt 9 sein
      expect(updatedCounter, findsAtLeastNWidget(1));

      // Schritt 6: Prüfe, ob Bewerbung generiert wurde
      final generatedContent = find.textContaining('Sehr geehrte');
      expect(generatedContent, findsAtLeastNWidget(1));
    });

    testWidgets('Multiple applications decrement counter correctly', (tester) async {
      // Starte die App
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Führe 3 Bewerbungen durch
      for (int i = 0; i < 3; i++) {
        // Navigiere zu einer Stellenanzeige
        final jobCard = find.byType(Card).at(i % 2); // Wechsle zwischen ersten beiden Jobs
        await tester.tap(jobCard);
        await tester.pumpAndSettle(Duration(seconds: 2));

        // Prüfe aktuellen Zählerstand
        final expectedCount = 10 - i;
        final counterWidget = find.textContaining('$expectedCount');
        expect(counterWidget, findsAtLeastNWidget(1));

        // Bewerbe dich
        final applyButton = find.textContaining('Bewerben');
        await tester.tap(applyButton);
        await tester.pumpAndSettle(Duration(seconds: 8));

        // Gehe zurück zur Jobliste
        final backButton = find.byIcon(Icons.arrow_back);
        await tester.tap(backButton);
        await tester.pumpAndSettle(Duration(seconds: 1));
      }

      // Finaler Check: Zähler sollte bei 7 stehen
      final finalCounter = find.textContaining('7');
      expect(finalCounter, findsAtLeastNWidget(1));
    });

    testWidgets('User with 0 applications cannot apply', (tester) async {
      // Dieser Test erfordert einen User mit 0 Bewerbungen
      // In der Praxis würde man den User-State mocken oder manipulieren
      
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Simuliere User mit 0 Bewerbungen (würde normalerweise über Mock/Setup erfolgen)
      // Für diesen Test nehmen wir an, dass der User bereits alle Bewerbungen aufgebraucht hat

      // Navigiere zu einer Stellenanzeige
      final jobCard = find.byType(Card).first;
      await tester.tap(jobCard);
      await tester.pumpAndSettle(Duration(seconds: 2));

      // Prüfe, ob Zähler 0 zeigt (falls User keine Bewerbungen mehr hat)
      final zeroCounter = find.textContaining('0');
      
      // Wenn Zähler 0 ist, sollte Bewerbungsbutton deaktiviert oder anders dargestellt sein
      if (zeroCounter.evaluate().isNotEmpty) {
        final applyButton = find.textContaining('Bewerben');
        
        // Versuche zu klicken (sollte nichts passieren)
        await tester.tap(applyButton);
        await tester.pumpAndSettle(Duration(seconds: 2));

        // Zähler sollte immer noch 0 sein
        expect(find.textContaining('0'), findsAtLeastNWidget(1));
      }
    });

    testWidgets('Premium user has unlimited applications', (tester) async {
      // Dieser Test erfordert einen Premium-User
      // In der Praxis würde man den User-Status mocken
      
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Prüfe, ob Premium-Indikator vorhanden ist
      final premiumIndicator = find.textContaining('Premium');
      
      if (premiumIndicator.evaluate().isNotEmpty) {
        // Navigiere zu einer Stellenanzeige
        final jobCard = find.byType(Card).first;
        await tester.tap(jobCard);
        await tester.pumpAndSettle(Duration(seconds: 2));

        // Premium-User sollten keinen Bewerbungszähler sehen
        final counterWidget = find.textContaining('verbleibend');
        expect(counterWidget, findsNothing);

        // Bewerbe dich mehrmals
        for (int i = 0; i < 3; i++) {
          final applyButton = find.textContaining('Bewerben');
          await tester.tap(applyButton);
          await tester.pumpAndSettle(Duration(seconds: 8));

          // Gehe zurück und wieder zur gleichen Anzeige
          final backButton = find.byIcon(Icons.arrow_back);
          await tester.tap(backButton);
          await tester.pumpAndSettle();
          
          final jobCardAgain = find.byType(Card).first;
          await tester.tap(jobCardAgain);
          await tester.pumpAndSettle(Duration(seconds: 2));
        }

        // Premium-User sollte immer noch keine Zähler-Beschränkung haben
        final stillNoCounting = find.textContaining('verbleibend');
        expect(stillNoCounting, findsNothing);
      }
    });

    testWidgets('Counter updates immediately after application', (tester) async {
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Navigiere zu einer Stellenanzeige
      final jobCard = find.byType(Card).first;
      await tester.tap(jobCard);
      await tester.pumpAndSettle(Duration(seconds: 2));

      // Notiere initialen Zählerstand
      final initialCounterText = tester.widget<Text>(
        find.textContaining(RegExp(r'\d+')).first
      ).data;
      final initialCount = int.tryParse(
        RegExp(r'\d+').firstMatch(initialCounterText!)?.group(0) ?? '0'
      ) ?? 0;

      // Bewerbe dich
      final applyButton = find.textContaining('Bewerben');
      await tester.tap(applyButton);
      
      // Warte nur kurz (Counter sollte sofort aktualisiert werden)
      await tester.pump(Duration(milliseconds: 500));

      // Prüfe, ob Zähler bereits dekrementiert wurde (vor Abschluss der Generierung)
      final updatedCount = initialCount - 1;
      final updatedCounter = find.textContaining('$updatedCount');
      expect(updatedCounter, findsAtLeastNWidget(1));
    });

    testWidgets('App handles network errors gracefully during counter update', (tester) async {
      // Dieser Test würde normalerweise Netzwerkfehler simulieren
      // Für Integration Tests ist das komplex, aber wichtig für Robustheit
      
      app.main();
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Navigiere zu einer Stellenanzeige
      final jobCard = find.byType(Card).first;
      await tester.tap(jobCard);
      await tester.pumpAndSettle(Duration(seconds: 2));

      // Bewerbe dich (könnte Netzwerkfehler verursachen)
      final applyButton = find.textContaining('Bewerben');
      await tester.tap(applyButton);
      await tester.pumpAndSettle(Duration(seconds: 10));

      // App sollte nicht abstürzen und Fehler angemessen behandeln
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Prüfe, ob Fehlermeldung angezeigt wird (falls Netzwerkfehler auftritt)
      final errorMessage = find.textContaining('Fehler');
      // Fehler ist optional - hängt von Netzwerkbedingungen ab
    });
  });
}

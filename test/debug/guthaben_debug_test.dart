import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ki_test/src/infrastructure/services/subscription_management_service.dart';

// Mock-Klassen generieren
@GenerateMocks([
  SupabaseClient,
  GoTrueClient,
  User,
  PostgrestQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestBuilder,
])
import 'guthaben_debug_test.mocks.dart';

void main() {
  group('Guthaben Debug Tests', () {
    late MockSupabaseClient mockSupabase;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockPostgrestQueryBuilder mockQueryBuilder;
    late MockPostgrestFilterBuilder mockFilterBuilder;
    late MockPostgrestBuilder mockBuilder;
    late ProviderContainer container;
    late SubscriptionManagementService service;

    setUp(() {
      mockSupabase = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockQueryBuilder = MockPostgrestQueryBuilder();
      mockFilterBuilder = MockPostgrestFilterBuilder();
      mockBuilder = MockPostgrestBuilder();

      // Setup basic mocks
      when(mockSupabase.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');

      // Setup container
      container = ProviderContainer();
      service = SubscriptionManagementService(mockSupabase, container.read);
    });

    tearDown(() {
      container.dispose();
    });

    test('KRITISCH: Free User mit 10 Bewerbungen - Abzug auf 9', () async {
      print('🔥 TEST: Starte kritischen Test für Free-User Guthaben-Abzug');

      // Arrange: Mock Subscription (Free Plan)
      when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'id': 'sub-1',
        'user_id': 'test-user-id',
        'plan_type': 'free',
        'is_premium': false,
        'status': 'active',
      });

      // Mock Application Counter (10 verbleibende Bewerbungen)
      when(mockSupabase.from('application_counters')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'user_id': 'test-user-id',
        'total_applications': 10,
        'remaining_applications': 10,
        'plan_type': 'free',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Mock Update Operation
      when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockBuilder);
      when(mockBuilder.then()).thenAnswer((_) async => []);

      print('🔥 TEST: Mocks konfiguriert - rufe incrementApplicationCounter auf');

      // Act: Bewerbung abziehen
      final result = await service.incrementApplicationCounter();

      print('🔥 TEST: incrementApplicationCounter Ergebnis: $result');

      // Assert: Sollte erfolgreich sein
      expect(result, isTrue, reason: 'Guthaben-Abzug sollte erfolgreich sein');

      // Verify: Update wurde mit korrekten Werten aufgerufen
      verify(mockQueryBuilder.update({
        'remaining_applications': 9, // Von 10 auf 9 reduziert
        'updated_at': any,
      })).called(1);

      print('✅ TEST: Guthaben-Abzug erfolgreich - von 10 auf 9 reduziert');
    });

    test('KRITISCH: Free User mit 0 Bewerbungen - Abzug sollte fehlschlagen', () async {
      print('🔥 TEST: Starte Test für Free-User mit 0 Bewerbungen');

      // Arrange: Mock Subscription (Free Plan)
      when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'id': 'sub-1',
        'user_id': 'test-user-id',
        'plan_type': 'free',
        'is_premium': false,
        'status': 'active',
      });

      // Mock Application Counter (0 verbleibende Bewerbungen)
      when(mockSupabase.from('application_counters')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'user_id': 'test-user-id',
        'total_applications': 10,
        'remaining_applications': 0, // Keine Bewerbungen mehr
        'plan_type': 'free',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      print('🔥 TEST: Mocks konfiguriert - rufe incrementApplicationCounter auf');

      // Act: Bewerbung abziehen versuchen
      final result = await service.incrementApplicationCounter();

      print('🔥 TEST: incrementApplicationCounter Ergebnis: $result');

      // Assert: Sollte fehlschlagen
      expect(result, isFalse, reason: 'Guthaben-Abzug sollte bei 0 Bewerbungen fehlschlagen');

      // Verify: Kein Update sollte stattgefunden haben
      verifyNever(mockQueryBuilder.update(any));

      print('✅ TEST: Guthaben-Abzug korrekt verhindert bei 0 Bewerbungen');
    });

    test('KRITISCH: Premium User - sollte immer erfolgreich sein', () async {
      print('🔥 TEST: Starte Test für Premium-User');

      // Arrange: Mock Subscription (Unlimited Plan)
      when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'id': 'sub-1',
        'user_id': 'test-user-id',
        'plan_type': 'unlimited',
        'is_premium': true,
        'status': 'active',
      });

      print('🔥 TEST: Mocks konfiguriert - rufe incrementApplicationCounter auf');

      // Act: Bewerbung "abziehen"
      final result = await service.incrementApplicationCounter();

      print('🔥 TEST: incrementApplicationCounter Ergebnis: $result');

      // Assert: Sollte erfolgreich sein ohne tatsächlichen Abzug
      expect(result, isTrue, reason: 'Premium-User sollten immer erfolgreich sein');

      // Verify: Kein Counter-Update für Premium-User
      verifyNever(mockSupabase.from('application_counters'));

      print('✅ TEST: Premium-User erfolgreich - kein Guthaben-Abzug nötig');
    });

    test('KRITISCH: Kein angemeldeter User - sollte fehlschlagen', () async {
      print('🔥 TEST: Starte Test für nicht angemeldeten User');

      // Arrange: Kein angemeldeter User
      when(mockAuth.currentUser).thenReturn(null);

      print('🔥 TEST: Mocks konfiguriert - rufe incrementApplicationCounter auf');

      // Act: Bewerbung abziehen versuchen
      final result = await service.incrementApplicationCounter();

      print('🔥 TEST: incrementApplicationCounter Ergebnis: $result');

      // Assert: Sollte fehlschlagen
      expect(result, isFalse, reason: 'Nicht angemeldete User sollten fehlschlagen');

      // Verify: Keine Datenbankoperationen
      verifyNever(mockSupabase.from(any));

      print('✅ TEST: Nicht angemeldeter User korrekt abgelehnt');
    });

    test('KRITISCH: Database-Fehler - sollte fehlschlagen', () async {
      print('🔥 TEST: Starte Test für Database-Fehler');

      // Arrange: Mock Subscription Query wirft Fehler
      when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenThrow(Exception('Database connection failed'));

      print('🔥 TEST: Mocks konfiguriert - rufe incrementApplicationCounter auf');

      // Act: Bewerbung abziehen versuchen
      final result = await service.incrementApplicationCounter();

      print('🔥 TEST: incrementApplicationCounter Ergebnis: $result');

      // Assert: Sollte fehlschlagen
      expect(result, isFalse, reason: 'Database-Fehler sollten zu Fehlschlag führen');

      print('✅ TEST: Database-Fehler korrekt behandelt');
    });

    test('KRITISCH: Mehrfache schnelle Aufrufe - Race Condition Test', () async {
      print('🔥 TEST: Starte Race Condition Test');

      // Arrange: Mock für mehrfache Aufrufe
      when(mockSupabase.from('subscriptions')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('status', 'active')).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.single()).thenAnswer((_) async => {
        'id': 'sub-1',
        'user_id': 'test-user-id',
        'plan_type': 'free',
        'is_premium': false,
        'status': 'active',
      });

      when(mockSupabase.from('application_counters')).thenReturn(mockQueryBuilder);
      when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockFilterBuilder);
      
      // Simuliere verschiedene Zustände bei schnellen Aufrufen
      var callCount = 0;
      when(mockFilterBuilder.single()).thenAnswer((_) async {
        callCount++;
        return {
          'user_id': 'test-user-id',
          'total_applications': 10,
          'remaining_applications': 11 - callCount, // 10, 9, 8, etc.
          'plan_type': 'free',
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };
      });

      when(mockQueryBuilder.update(any)).thenReturn(mockFilterBuilder);
      when(mockFilterBuilder.eq('user_id', 'test-user-id')).thenReturn(mockBuilder);
      when(mockBuilder.then()).thenAnswer((_) async => []);

      print('🔥 TEST: Mocks konfiguriert - starte mehrfache Aufrufe');

      // Act: Mehrere schnelle Aufrufe
      final results = await Future.wait([
        service.incrementApplicationCounter(),
        service.incrementApplicationCounter(),
        service.incrementApplicationCounter(),
      ]);

      print('🔥 TEST: Mehrfache Aufrufe Ergebnisse: $results');

      // Assert: Alle sollten erfolgreich sein
      expect(results, everyElement(isTrue), reason: 'Alle Aufrufe sollten erfolgreich sein');

      // Verify: Drei Updates sollten stattgefunden haben
      verify(mockQueryBuilder.update(any)).called(3);

      print('✅ TEST: Race Condition Test erfolgreich');
    });
  });
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Test-Setup-Hilfsfunktionen und gemeinsame Mocks
class TestSetup {
  /// Erstellt einen ProviderContainer mit Mock-Overrides
  static ProviderContainer createTestContainer({
    List<Override> overrides = const [],
  }) {
    return ProviderContainer(
      overrides: overrides,
    );
  }

  /// Erstellt eine Test-Widget-Umgebung mit ProviderScope
  static Widget createTestApp({
    required Widget child,
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        home: child,
      ),
    );
  }

  /// Initialisiert Supabase für Tests
  static Future<void> initializeSupabaseForTesting() async {
    await Supabase.initialize(
      url: 'https://test.supabase.co',
      anonKey: 'test-anon-key',
    );
  }

  /// Initialisiert SharedPreferences für Tests
  static void initializeSharedPreferencesForTesting() {
    SharedPreferences.setMockInitialValues({});
  }

  /// Erstellt Mock-User-Daten
  static Map<String, dynamic> createMockUserData({
    String id = 'test-user-id',
    String email = '<EMAIL>',
    String? name,
  }) {
    return {
      'id': id,
      'email': email,
      'name': name ?? 'Test User',
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Erstellt Mock-Premium-Subscription-Daten
  static Map<String, dynamic> createMockSubscriptionData({
    String planType = 'pro',
    bool isActive = true,
    DateTime? expiresAt,
  }) {
    return {
      'plan_type': planType,
      'is_active': isActive,
      'expires_at': (expiresAt ?? DateTime.now().add(const Duration(days: 30))).toIso8601String(),
    };
  }

  /// Erstellt Mock-CV-Daten
  static Map<String, dynamic> createMockCvData({
    String name = 'Max Mustermann',
    String email = '<EMAIL>',
    List<String> skills = const ['Flutter', 'Dart'],
  }) {
    return {
      'name': name,
      'email': email,
      'skills': skills,
      'experience': [
        {
          'company': 'Test Company',
          'position': 'Flutter Developer',
          'duration': '2020-2023',
        }
      ],
      'education': [
        {
          'institution': 'Test University',
          'degree': 'Computer Science',
          'year': '2020',
        }
      ],
    };
  }

  /// Erstellt Mock-Application-Entity-Daten
  static Map<String, dynamic> createMockApplicationData({
    String id = 'test-app-id',
    String jobTitle = 'Flutter Developer',
    String companyName = 'Test Company',
    String contactEmail = '<EMAIL>',
    String userId = 'test-user-id',
  }) {
    return {
      'id': id,
      'job_title': jobTitle,
      'company_name': companyName,
      'contact_email': contactEmail,
      'application_date': DateTime.now().toIso8601String(),
      'status': 'applied',
      'user_id': userId,
    };
  }

  /// Wartet auf alle Async-Operationen in Tests
  static Future<void> pumpAndSettle(WidgetTester tester) async {
    await tester.pumpAndSettle();
    // Zusätzliche Wartezeit für Riverpod-Provider
    await Future.delayed(const Duration(milliseconds: 100));
    await tester.pumpAndSettle();
  }

  /// Erstellt Mock-Error für Tests
  static Exception createMockError(String message) {
    return Exception(message);
  }

  /// Erstellt Mock-AuthException für Tests
  static AuthException createMockAuthError(String message) {
    return AuthException(message);
  }
}

/// Basis-Mock-Klasse für Tests
class BaseMock extends Mock {
  @override
  dynamic noSuchMethod(Invocation invocation) {
    // Standardverhalten für nicht gemockte Methoden
    return super.noSuchMethod(invocation);
  }
}

/// Test-Konstanten
class TestConstants {
  static const String testUserId = 'test-user-id';
  static const String testUserEmail = '<EMAIL>';
  static const String testUserName = 'Test User';
  static const String testCompanyName = 'Test Company';
  static const String testJobTitle = 'Flutter Developer';
  static const String testContactEmail = '<EMAIL>';
  static const String testFilePath = '/test/path/test_cv.pdf';
  static const String testFileName = 'test_cv.pdf';
  
  // Premium Plan Types
  static const String basicPlan = 'basic';
  static const String proPlan = 'pro';
  static const String unlimitedPlan = 'unlimited';
  
  // Test Timeouts
  static const Duration shortTimeout = Duration(seconds: 5);
  static const Duration mediumTimeout = Duration(seconds: 10);
  static const Duration longTimeout = Duration(seconds: 30);
}

/// Test-Matcher für häufige Assertions
class TestMatchers {
  /// Matcher für erfolgreiche Operationen
  static Matcher get isSuccess => equals(true);
  
  /// Matcher für fehlgeschlagene Operationen
  static Matcher get isFailure => equals(false);
  
  /// Matcher für nicht-null Werte
  static Matcher get isNotNull => isA<Object>();
  
  /// Matcher für leere Listen
  static Matcher get isEmpty => hasLength(0);
  
  /// Matcher für nicht-leere Listen
  static Matcher get isNotEmpty => hasLength(greaterThan(0));
}

/// Test-Hilfsfunktionen für Async-Operationen
class AsyncTestHelpers {
  /// Führt eine Async-Operation mit Timeout aus
  static Future<T> withTimeout<T>(
    Future<T> operation, {
    Duration timeout = TestConstants.mediumTimeout,
  }) async {
    return await operation.timeout(timeout);
  }
  
  /// Wartet auf eine Bedingung mit Polling
  static Future<void> waitForCondition(
    bool Function() condition, {
    Duration timeout = TestConstants.mediumTimeout,
    Duration pollInterval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    while (!condition() && stopwatch.elapsed < timeout) {
      await Future.delayed(pollInterval);
    }
    
    if (!condition()) {
      throw TimeoutException('Condition not met within timeout', timeout);
    }
  }
}

/// Exception für Test-Timeouts
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  
  const TimeoutException(this.message, this.timeout);
  
  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}

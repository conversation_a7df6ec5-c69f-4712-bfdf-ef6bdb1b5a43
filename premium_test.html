<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plan-Verwaltung</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #1e1e1e;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #ff9800;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #2d2d2d;
            border-radius: 5px;
        }
        .section h2 {
            color: #4caf50;
            margin-top: 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #444;
            border-radius: 4px;
            background-color: #333;
            color: #fff;
        }
        button {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .message {
            padding: 10px;
            margin-top: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #4caf50;
            color: white;
        }
        .error {
            background-color: #f44336;
            color: white;
        }
        .user-list {
            margin-top: 30px;
            display: none;
        }
        .user-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #444;
        }
        .user-email {
            font-weight: bold;
        }
        .user-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 14px;
        }
        .status-premium {
            background-color: #ff9800;
            color: black;
        }
        .status-free {
            background-color: #2196F3;
            color: white;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Plan-Verwaltung</h1>

        <div id="message" class="message"></div>

        <div class="section">
            <h2>API-Schlüssel</h2>
            <label for="api-key">Supabase API-Schlüssel:</label>
            <input type="password" id="api-key" placeholder="Gib deinen Supabase API-Schlüssel ein">
        </div>

        <div class="section">
            <h2>Premium-Status testen</h2>
            <label for="user-id">Benutzer-ID:</label>
            <input type="text" id="user-id" placeholder="Benutzer-ID" value="50e23700-3093-4dec-97cb-0e3a39f20a65">

            <button id="check-btn" onclick="checkPremiumStatus()">Premium-Status prüfen</button>
        </div>

        <div class="section">
            <h2>Kostenlose Bewerbungen verwalten</h2>
            <button id="reset-free-btn" onclick="resetFreeApplications()">Kostenlose Bewerbungen zurücksetzen (alle)</button>
            <button id="check-free-btn" onclick="checkFreeApplicationCounter()">Kostenlose Bewerbungen zurücksetzen (Benutzer)</button>
            <button id="check-next-reset-btn" onclick="checkNextResetDate()">Nächstes Reset-Datum prüfen</button>
        </div>

        <div class="section">
            <h2>Plan aktivieren oder aktualisieren</h2>
            <label for="plan-type">Premium-Plan:</label>
            <select id="plan-type">
                <option value="free">Free (10 kostenlose Bewerbungen pro Woche)</option>
                <option value="basic">Basic (10 kostenlose Bewerbungen pro Woche, mit Werbung)</option>
                <option value="pro">Pro (150 Bewerbungen, ohne Werbung)</option>
                <option value="unlimited" selected>Unlimited (Unbegrenzte Bewerbungen, ohne Werbung)</option>
            </select>

            <label for="duration">Gültigkeitsdauer:</label>
            <select id="duration">
                <option value="1">1 Monat</option>
                <option value="3">3 Monate</option>
                <option value="6">6 Monate</option>
                <option value="12" selected>12 Monate</option>
            </select>

            <button id="activate-btn" onclick="activatePremium()">Plan aktivieren</button>
        </div>

        <div id="user-list" class="user-list">
            <h2>Kürzlich aktivierte Benutzer</h2>
            <div id="user-items"></div>
        </div>
    </div>

    <script>
        // Supabase URL
        const SUPABASE_URL = 'https://vpttdxibvjrfjzbtktqg.supabase.co';

        // Zeige eine Nachricht an
        function showMessage(message, type) {
            const messageElement = document.getElementById('message');
            messageElement.textContent = message;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';

            // Nachricht nach 5 Sekunden ausblenden
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }

        // Prüfe den Premium-Status eines Benutzers
        async function checkPremiumStatus() {
            const apiKey = document.getElementById('api-key').value;
            const userId = document.getElementById('user-id').value;

            if (!apiKey) {
                showMessage('Bitte gib den Supabase API-Schlüssel ein.', 'error');
                return;
            }

            if (!userId) {
                showMessage('Bitte gib die Benutzer-ID ein.', 'error');
                return;
            }

            // Button-Status aktualisieren
            const button = document.getElementById('check-btn');
            const originalText = button.textContent;
            button.innerHTML = '<span class="loading"></span> Wird geprüft...';
            button.disabled = true;

            try {
                // Abonnement abrufen
                const subscriptionResponse = await fetch(`${SUPABASE_URL}/rest/v1/subscriptions?user_id=eq.${userId}&status=eq.active&select=*`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                const subscriptions = await subscriptionResponse.json();

                // Bewerbungszähler abrufen
                const counterResponse = await fetch(`${SUPABASE_URL}/rest/v1/application_counters?user_id=eq.${userId}&select=*`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                const counters = await counterResponse.json();

                // UserProfile abrufen
                const profileResponse = await fetch(`${SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}&select=data`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                const profiles = await profileResponse.json();

                // Ergebnisse anzeigen
                let message = '';

                if (subscriptions.length > 0) {
                    const subscription = subscriptions[0];
                    message += `Abonnement: ${subscription.plan_type} (${subscription.is_premium ? 'Premium' : 'Nicht Premium'})\n`;
                    message += `Status: ${subscription.status}\n`;
                    message += `Gültig bis: ${new Date(subscription.end_date).toLocaleString()}\n\n`;
                } else {
                    message += 'Kein aktives Abonnement gefunden.\n\n';
                }

                if (profiles.length > 0) {
                    const profile = profiles[0].data;
                    message += `UserProfile: ${profile.isPremium ? 'Premium' : 'Nicht Premium'}\n`;
                    message += `Plan: ${profile.premiumPlanType || 'Nicht gesetzt'}\n`;
                    message += `Gültig bis: ${profile.premiumExpiryDate ? new Date(profile.premiumExpiryDate).toLocaleString() : 'Nicht gesetzt'}\n\n`;
                } else {
                    message += 'Kein UserProfile gefunden.\n\n';
                }

                if (counters.length > 0) {
                    const counter = counters[0];
                    message += `Bewerbungszähler:\n`;
                    message += `Gesamt: ${counter.total_applications}\n`;
                    message += `Verbleibend: ${counter.remaining_applications}\n`;
                    message += `Letztes Reset: ${new Date(counter.reset_date).toLocaleString()}\n`;

                    // Zeige das nächste Reset-Datum für kostenlose Bewerbungen an, wenn vorhanden
                    if (counter.free_reset_date) {
                        const freeResetDate = new Date(counter.free_reset_date);
                        const nextResetDate = new Date(freeResetDate);
                        nextResetDate.setDate(nextResetDate.getDate() + 7);

                        message += `Letztes Free-Reset: ${freeResetDate.toLocaleString()}\n`;
                        message += `Nächstes Free-Reset: ${nextResetDate.toLocaleString()}\n`;
                    }
                } else {
                    message += 'Kein Bewerbungszähler gefunden.';
                }

                alert(message);
            } catch (error) {
                showMessage(`Fehler: ${error.message}`, 'error');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // Aktiviere Premium für einen Benutzer
        async function activatePremium() {
            const apiKey = document.getElementById('api-key').value;
            const userId = document.getElementById('user-id').value;
            const planType = document.getElementById('plan-type').value;
            const durationMonths = parseInt(document.getElementById('duration').value);

            if (!apiKey) {
                showMessage('Bitte gib den Supabase API-Schlüssel ein.', 'error');
                return;
            }

            if (!userId) {
                showMessage('Bitte gib die Benutzer-ID ein.', 'error');
                return;
            }

            // Button-Status aktualisieren
            const button = document.getElementById('activate-btn');
            const originalText = button.textContent;
            button.innerHTML = '<span class="loading"></span> Wird aktiviert...';
            button.disabled = true;

            try {
                // Aktuelles Datum und Enddatum berechnen
                const now = new Date();
                const endDate = new Date(now);
                endDate.setMonth(now.getMonth() + durationMonths);

                // Formatiere das Datum als ISO-String ohne Zeitzone für bessere Kompatibilität
                // Wichtig: Das Format muss mit dem Format übereinstimmen, das die App erwartet
                const formattedEndDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}T00:00:00.000`;

                // Logge das formatierte Datum
                console.log('Formatiertes Enddatum:', formattedEndDate);

                // Bestimme den Premium-Status basierend auf dem Plan-Typ
                const isPremium = planType !== 'free';

                // Verwende die neue Funktion zum Aktualisieren des Premium-Status und der Bewerbungsanzahl
                const sqlQuery = `
                    -- Verwende die update_premium_status_with_counter-Funktion
                    SELECT update_premium_status_with_counter(
                        '${userId}',
                        ${isPremium},
                        '${planType}',
                        '${formattedEndDate}'::timestamp with time zone
                    ) as result;
                `;

                console.log('SQL-Abfrage:', sqlQuery);

                const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/update_premium_status_with_counter`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        p_user_id: userId,
                        p_is_premium: isPremium,
                        p_plan_type: planType,
                        p_expiry_date: formattedEndDate
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Fehler-Antwort:', errorText);
                    throw new Error(`Fehler beim Aktivieren von Premium: ${errorText}`);
                }

                const result = await response.json();

                // Bestimme die Anzahl der Bewerbungen basierend auf dem Plan
                let applicationsCount = "";
                if (planType === 'free') {
                    applicationsCount = "10 kostenlose Bewerbungen pro Woche";
                } else if (planType === 'basic') {
                    applicationsCount = "10 kostenlose Bewerbungen pro Woche (mit Werbung)";
                } else if (planType === 'pro') {
                    applicationsCount = "150 Bewerbungen (ohne Werbung)";
                } else if (planType === 'unlimited') {
                    applicationsCount = "Unbegrenzte Bewerbungen (ohne Werbung)";
                }

                const statusMessage = isPremium ?
                    `Premium für Benutzer ${userId} aktiviert! Plan: ${planType} (${applicationsCount}), Dauer: ${durationMonths} Monate.` :
                    `Free-Plan für Benutzer ${userId} aktiviert! (${applicationsCount})`;

                showMessage(`${statusMessage} Bitte starte die App neu, damit die Änderungen wirksam werden.`, 'success');

                // Aktualisiere die Liste der aktivierten Benutzer
                loadActivatedUsers(apiKey);
            } catch (error) {
                showMessage(`Fehler: ${error.message}`, 'error');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // Lade die Liste der aktivierten Benutzer
        async function loadActivatedUsers(apiKey) {
            if (!apiKey) return;

            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/subscriptions?select=user_id,plan_type,end_date,updated_at&status=eq.active&order=updated_at.desc&limit=5`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Fehler beim Laden der aktivierten Benutzer');
                }

                const subscriptions = await response.json();

                if (subscriptions.length > 0) {
                    const userListElement = document.getElementById('user-list');
                    const userItemsElement = document.getElementById('user-items');
                    userItemsElement.innerHTML = '';

                    for (const sub of subscriptions) {
                        const userItem = document.createElement('div');
                        userItem.className = 'user-item';

                        const userInfo = document.createElement('div');
                        userInfo.className = 'user-email';
                        userInfo.textContent = sub.user_id;

                        const userStatus = document.createElement('div');
                        const isPremium = sub.plan_type !== 'free';
                        userStatus.className = `user-status ${isPremium ? 'status-premium' : 'status-free'}`;
                        userStatus.textContent = `${sub.plan_type} bis ${new Date(sub.end_date).toLocaleDateString()}`;

                        userItem.appendChild(userInfo);
                        userItem.appendChild(userStatus);
                        userItemsElement.appendChild(userItem);
                    }

                    userListElement.style.display = 'block';
                }
            } catch (error) {
                console.error('Fehler beim Laden der aktivierten Benutzer:', error);
            }
        }

        // API-Schlüssel-Änderung überwachen
        document.getElementById('api-key').addEventListener('change', function() {
            const apiKey = this.value;
            if (apiKey) {
                loadActivatedUsers(apiKey);
            }
        });

        // Kostenlose Bewerbungen für alle Benutzer zurücksetzen
        async function resetFreeApplications() {
            const apiKey = document.getElementById('api-key').value;

            if (!apiKey) {
                showMessage('Bitte gib den Supabase API-Schlüssel ein.', 'error');
                return;
            }

            // Button-Status aktualisieren
            const button = document.getElementById('reset-free-btn');
            const originalText = button.textContent;
            button.innerHTML = '<span class="loading"></span> Wird zurückgesetzt...';
            button.disabled = true;

            try {
                // Verwende die neue Funktion zum Zurücksetzen der kostenlosen Bewerbungen
                const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/reset_free_application_counters`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Fehler-Antwort:', errorText);
                    throw new Error(`Fehler beim Zurücksetzen der kostenlosen Bewerbungen: ${errorText}`);
                }

                showMessage('Kostenlose Bewerbungen wurden erfolgreich zurückgesetzt.', 'success');
            } catch (error) {
                showMessage(`Fehler: ${error.message}`, 'error');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // Nächstes Reset-Datum für einen Benutzer prüfen
        async function checkNextResetDate() {
            const apiKey = document.getElementById('api-key').value;
            const userId = document.getElementById('user-id').value;

            if (!apiKey) {
                showMessage('Bitte gib den Supabase API-Schlüssel ein.', 'error');
                return;
            }

            if (!userId) {
                showMessage('Bitte gib die Benutzer-ID ein.', 'error');
                return;
            }

            // Button-Status aktualisieren
            const button = document.getElementById('check-next-reset-btn');
            const originalText = button.textContent;
            button.innerHTML = '<span class="loading"></span> Wird geprüft...';
            button.disabled = true;

            try {
                // Verwende die neue Funktion zum Abrufen des nächsten Reset-Datums
                const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_next_free_reset_date`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        p_user_id: userId
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Fehler-Antwort:', errorText);
                    throw new Error(`Fehler beim Abrufen des nächsten Reset-Datums: ${errorText}`);
                }

                const nextResetDate = await response.json();

                if (nextResetDate) {
                    const formattedDate = new Date(nextResetDate).toLocaleString();
                    showMessage(`Nächstes Reset-Datum für Benutzer ${userId}: ${formattedDate}`, 'success');
                } else {
                    showMessage(`Kein nächstes Reset-Datum für Benutzer ${userId} gefunden.`, 'error');
                }
            } catch (error) {
                showMessage(`Fehler: ${error.message}`, 'error');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // Funktion zum manuellen Zurücksetzen der kostenlosen Bewerbungen für einen bestimmten Benutzer
        async function checkFreeApplicationCounter() {
            const apiKey = document.getElementById('api-key').value;
            const userId = document.getElementById('user-id').value;

            if (!apiKey) {
                showMessage('Bitte gib den Supabase API-Schlüssel ein.', 'error');
                return;
            }

            if (!userId) {
                showMessage('Bitte gib die Benutzer-ID ein.', 'error');
                return;
            }

            try {
                // Verwende die neue Funktion zum Überprüfen und Zurücksetzen des kostenlosen Bewerbungszählers
                const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/check_free_application_counter`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        p_user_id: userId
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Fehler-Antwort:', errorText);
                    throw new Error(`Fehler beim Überprüfen des kostenlosen Bewerbungszählers: ${errorText}`);
                }

                const result = await response.json();

                if (result === true) {
                    showMessage(`Kostenloser Bewerbungszähler für Benutzer ${userId} wurde erfolgreich überprüft und aktualisiert.`, 'success');
                } else {
                    showMessage(`Fehler beim Überprüfen des kostenlosen Bewerbungszählers für Benutzer ${userId}.`, 'error');
                }
            } catch (error) {
                showMessage(`Fehler: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

#!/bin/bash

# Korrekten Keystore aus upload_cert.der erstellen

echo "=== KORREKTEN KEYSTORE ERSTELLEN ==="
echo ""

# Das Problem: upload_cert.der hat den richtigen SHA1, aber wir brauchen einen Keystore
# Lösung: Einen neuen Keystore mit dem privaten Schlüssel erstellen, der zu upload_cert.der gehört

echo "❌ PROBLEM: Wir haben das Zertifikat, aber nicht den privaten Schlüssel!"
echo ""
echo "upload_cert.der SHA1: F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
echo "upload-keystore.jks SHA1: 47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25"
echo ""
echo "Diese SHA1s sind unterschiedlich, weil:"
echo "1. upload_cert.der = Öffentliches Zertifikat (für Google Play App Signing)"
echo "2. upload-keystore.jks = Privater <PERSON> (für Upload-Signierung)"
echo ""
echo "=== KORREKTE LÖSUNG ==="
echo ""
echo "🔧 OPTION 1: Google Play Console Upload-Zertifikat aktualisieren"
echo "   1. Gehe zu Google Play Console → App Signing"
echo "   2. Lade upload_certificate_correct.der hoch (unser aktueller Upload-Keystore)"
echo "   3. Google Play akzeptiert dann unsere AAB-Signierung"
echo ""
echo "🔧 OPTION 2: Neuen Keystore mit gewünschtem SHA1 erstellen"
echo "   1. Erstelle neuen Keystore mit korrekten Parametern"
echo "   2. Generiere Zertifikat mit SHA1: F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
echo "   3. Verwende diesen für AAB-Signierung"
echo ""
echo "⚠️  WICHTIG: upload_cert.der ist ein ÖFFENTLICHES Zertifikat"
echo "   - Wir können daraus keinen Keystore mit privatem Schlüssel erstellen"
echo "   - Wir brauchen den ursprünglichen privaten Schlüssel"
echo ""
echo "=== EMPFOHLENE LÖSUNG ==="
echo ""
echo "✅ Verwende OPTION 1: Google Play Console aktualisieren"
echo ""
echo "1. Lade upload_certificate_correct.der in Google Play Console hoch"
echo "2. SHA1: 47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25"
echo "3. Google Play übernimmt App-Signing mit gewünschtem SHA1"
echo "4. Unsere AAB-Datei funktioniert dann"
echo ""
echo "Datei für Upload: $(pwd)/upload_certificate_correct.der"

#!/bin/bash

# SHA1-Setup Verifizierungsskript

echo "=== SHA1-FINGERABDRUCK SETUP VERIFIZIERUNG ==="
echo ""

# Gewünschter SHA1
DESIRED_SHA1="F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
DESIRED_SHA1_NO_COLONS="f7ce0eb91958a89e95e67ed15ccff8b025aa90dc"

echo "Gewünschter SHA1: $DESIRED_SHA1"
echo ""

# 1. Google Play Console Status
echo "1. ✅ GOOGLE PLAY CONSOLE"
echo "   Status: App Signing aktiviert"
echo "   App-Signing SHA1: $DESIRED_SHA1"
echo "   Upload-Keystore SHA1: 47:3C:CC:4B:19:15:49:C0:26:4E:68:D6:8D:09:CB:72:CC:64:6D:25"
echo ""

# 2. Firebase Console / google-services.json
echo "2. 🔍 FIREBASE CONSOLE / google-services.json"
if [ -f "android/app/google-services.json" ]; then
    echo "   Datei: ✓ Vorhanden"
    
    # Prüfe auf gewünschten SHA1
    if grep -q "$DESIRED_SHA1_NO_COLONS" android/app/google-services.json; then
        echo "   SHA1: ✅ Korrekt ($DESIRED_SHA1_NO_COLONS)"
        
        # Zeige alle SHA1s in der Datei
        echo "   Alle SHA1s in google-services.json:"
        grep -n "certificate_hash" android/app/google-services.json | while read line; do
            echo "     $line"
        done
    else
        echo "   SHA1: ❌ Gewünschter SHA1 nicht gefunden"
        echo "   Gefundene SHA1s:"
        grep -n "certificate_hash" android/app/google-services.json | while read line; do
            echo "     $line"
        done
    fi
else
    echo "   Datei: ❌ google-services.json nicht gefunden"
fi
echo ""

# 3. Upload-Keystore (für Upload-Signierung)
echo "3. 🔍 UPLOAD-KEYSTORE"
if [ -f "android/app/upload-keystore.jks" ]; then
    echo "   Datei: ✓ Vorhanden"
    echo "   SHA1 (für Upload):"
    keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1
else
    echo "   Datei: ❌ upload-keystore.jks nicht gefunden"
fi
echo ""

# 4. Upload-Zertifikat (für Google Play)
echo "4. 🔍 UPLOAD-ZERTIFIKAT"
if [ -f "upload_cert.der" ]; then
    echo "   Datei: ✓ Vorhanden"
    echo "   SHA1 (für App-Signing):"
    keytool -printcert -file upload_cert.der 2>/dev/null | grep "SHA1:" | head -1
else
    echo "   Datei: ❌ upload_cert.der nicht gefunden"
fi
echo ""

# 5. Zusammenfassung
echo "=== ZUSAMMENFASSUNG ==="
echo ""
echo "✅ Google Play Console: App-Signing mit gewünschtem SHA1 aktiviert"

if grep -q "$DESIRED_SHA1_NO_COLONS" android/app/google-services.json 2>/dev/null; then
    echo "✅ Firebase/google-services.json: Gewünschter SHA1 konfiguriert"
else
    echo "❌ Firebase/google-services.json: SHA1 fehlt oder falsch"
fi

echo ""
echo "=== STATUS ==="
if grep -q "$DESIRED_SHA1_NO_COLONS" android/app/google-services.json 2>/dev/null; then
    echo "🎉 SETUP KOMPLETT: SHA1 $DESIRED_SHA1 ist vollständig eingerichtet!"
    echo ""
    echo "Die App wird jetzt mit dem gewünschten SHA1-Fingerabdruck signiert."
    echo "Google Sign-In und andere Google Services funktionieren korrekt."
else
    echo "⚠️  SETUP UNVOLLSTÄNDIG: google-services.json muss aktualisiert werden"
    echo ""
    echo "Nächste Schritte:"
    echo "1. Firebase Console öffnen"
    echo "2. SHA1 $DESIRED_SHA1 hinzufügen"
    echo "3. Neue google-services.json herunterladen"
    echo "4. Datei in android/app/ ersetzen"
fi

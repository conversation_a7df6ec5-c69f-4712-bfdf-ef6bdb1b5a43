# ⚡ Schnellstart - Produktionsbereitschaft

## 🚨 <PERSON><PERSON> (30 Minuten)

### 1. Automatisches Setup ausführen
```bash
./setup_production.sh
```

### 2. Google Play Console konfigurieren
1. **<PERSON><PERSON>e zu:** [Google Play Console](https://play.google.com/console/)
2. **Navigiere zu:** Setup → API access
3. **Klicke:** "Link a Google Cloud project"
4. **Wähle:** Dein Google Cloud Projekt
5. **Service Account Berechtigungen:**
   - Financial data, orders, and cancellation survey responses ✅
   - App information and performance ✅

### 3. Sofort-Test durchführen
```bash
# Edge Function testen
supabase functions invoke verify-purchase --data '{"test": true}'

# App starten und Payment testen
flutter run
```

## 🧪 Vor Produktion (1-2 Stunden)

### Test-Track erstellen
1. **Google Play Console** → Deine App
2. **Testing** → Internal testing
3. **Create new release**
4. **Upload** signierte APK/AAB
5. **Add testers** (deine E-Mail)

### Debug-Modus deaktivieren
```dart
// In lib/src/infrastructure/services/payment_service.dart
static const bool _debugMode = false; // ← Auf false setzen
```

### Monitoring aktivieren
```bash
# Logs überwachen
supabase functions logs verify-purchase --follow
```

## 🔧 Wichtige Dateien

- `PRODUCTION_SETUP_GUIDE.md` - Vollständige Anleitung
- `setup_production.sh` - Automatisches Setup
- `.env.production` - Umgebungsvariablen (wird erstellt)
- `test_purchase.dart` - Test-Script (wird erstellt)

## 🚨 Sicherheit

- ❌ **NIE** Service Account JSON in Git committen
- ✅ **NUR** über Supabase Secrets verwalten
- ✅ Lokale JSON-Datei nach Setup löschen

## 📞 Bei Problemen

1. **Logs prüfen:** `supabase functions logs verify-purchase`
2. **Flutter Logs:** `flutter logs`
3. **Vollständige Anleitung:** `PRODUCTION_SETUP_GUIDE.md`

---

**Geschätzte Zeit:** 30 Min Setup + 1-2h Testing = **Produktionsbereit** 🚀
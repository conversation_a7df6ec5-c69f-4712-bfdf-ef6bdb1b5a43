package com.einsteinai.app

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.File

class EmailMethodChannel(private val context: Context) : MethodCallHandler {
    
    companion object {
        private const val CHANNEL = "ki_test/email"
    }
    
    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler(this)
    }
    
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "sendEmail" -> {
                try {
                    sendEmail(call, result)
                } catch (e: Exception) {
                    result.error("EMAIL_ERROR", "<PERSON><PERSON> beim Senden der E-Mail: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }
    
    private fun sendEmail(call: Method<PERSON><PERSON>, result: Result) {
        val recipients = call.argument<List<String>>("recipients") ?: emptyList()
        val subject = call.argument<String>("subject") ?: ""
        val body = call.argument<String>("body") ?: ""
        val attachmentPaths = call.argument<List<String>>("attachmentPaths") ?: emptyList()
        
        val intent = Intent(Intent.ACTION_SEND_MULTIPLE).apply {
            // Explizit E-Mail-Apps forcieren
            type = "message/rfc822"
            
            // Empfänger
            putExtra(Intent.EXTRA_EMAIL, recipients.toTypedArray())
            
            // Betreff und Text
            putExtra(Intent.EXTRA_SUBJECT, subject)
            putExtra(Intent.EXTRA_TEXT, body)
            
            // Anhänge hinzufügen
            if (attachmentPaths.isNotEmpty()) {
                val uris = ArrayList<Uri>()
                
                for (path in attachmentPaths) {
                    try {
                        val file = File(path)
                        if (file.exists()) {
                            val uri = FileProvider.getUriForFile(
                                context,
                                "${context.packageName}.fileprovider",
                                file
                            )
                            uris.add(uri)
                        }
                    } catch (e: Exception) {
                        // Ignoriere fehlerhafte Dateien
                        android.util.Log.w("EmailMethodChannel", "Fehler bei Datei $path: ${e.message}")
                    }
                }
                
                if (uris.isNotEmpty()) {
                    putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
            }
            
            // Nur E-Mail-Apps anzeigen
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        try {
            // WICHTIG: Filtere explizit nur E-Mail-Apps
            val packageManager = context.packageManager
            val emailApps = packageManager.queryIntentActivities(intent, 0)

            if (emailApps.isEmpty()) {
                result.error("NO_EMAIL_APPS", "Keine E-Mail-Apps gefunden", null)
                return
            }

            // Erstelle gefilterten Chooser nur mit E-Mail-Apps
            val filteredIntents = ArrayList<Intent>()

            for (resolveInfo in emailApps) {
                val packageName = resolveInfo.activityInfo.packageName

                // Nur echte E-Mail-Apps (Gmail, Outlook, etc.)
                if (isEmailApp(packageName)) {
                    val filteredIntent = Intent(intent).apply {
                        setPackage(packageName)
                    }
                    filteredIntents.add(filteredIntent)
                }
            }

            if (filteredIntents.isEmpty()) {
                // Fallback: Alle Apps die E-Mail-Intent unterstützen
                val chooser = Intent.createChooser(intent, "E-Mail senden")
                chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooser)
            } else {
                // Zeige nur gefilterte E-Mail-Apps
                val chooser = Intent.createChooser(
                    filteredIntents.removeAt(0),
                    "E-Mail senden"
                )
                chooser.putExtra(Intent.EXTRA_INITIAL_INTENTS, filteredIntents.toTypedArray())
                chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooser)
            }

            result.success(true)
        } catch (e: Exception) {
            result.error("INTENT_ERROR", "Fehler beim E-Mail-Versand: ${e.message}", null)
        }
    }

    // Hilfsmethode zur Identifikation echter E-Mail-Apps
    private fun isEmailApp(packageName: String): Boolean {
        val emailPackages = listOf(
            "com.google.android.gm",           // Gmail
            "com.microsoft.office.outlook",    // Outlook
            "com.yahoo.mobile.client.android.mail", // Yahoo Mail
            "com.samsung.android.email.provider", // Samsung Email
            "com.android.email",               // Android Email
            "com.htc.android.mail",           // HTC Mail
            "com.lge.email",                  // LG Email
            "com.sony.email",                 // Sony Email
            "com.oneplus.mail",               // OnePlus Mail
            "com.xiaomi.email",               // Xiaomi Email
            "com.huawei.email",               // Huawei Email
            "com.oppo.email",                 // Oppo Email
            "com.vivo.email",                 // Vivo Email
            "com.realme.mail",                // Realme Mail
            "com.asus.email",                 // Asus Email
            "com.motorola.blur.mail",         // Motorola Mail
            "com.nokia.email",                // Nokia Email
            "com.blackberry.email",           // BlackBerry Email
            "com.amazon.email",               // Amazon Email
            "com.apple.mobilemail",           // Apple Mail (falls vorhanden)
            "com.mailboxapp",                 // Mailbox
            "com.sparrowmailapp.sparrow",     // Sparrow
            "com.airmail.android",            // Airmail
            "com.easilydo.mail",              // Edison Mail
            "com.bluemail.mail",              // BlueMail
            "com.typeeto.email",              // TypeApp
            "com.cloudmagic.mail",            // Newton Mail
            "com.boomerang.androidapp",       // Boomerang
            "com.readdle.spark"               // Spark
        )

        return emailPackages.any { packageName.contains(it, ignoreCase = true) } ||
               packageName.contains("mail", ignoreCase = true) ||
               packageName.contains("email", ignoreCase = true)
    }
}

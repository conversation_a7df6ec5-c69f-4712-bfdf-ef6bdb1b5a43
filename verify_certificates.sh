#!/bin/bash

# Zertifikat-Verifizierungsskript für Google Play App Signing

echo "=== Zertifikat-Verifizierung für Google Play App Signing ==="
echo ""

# Aktueller Upload-Keystore
echo "1. Aktueller Upload-Keystore:"
echo "   Datei: android/app/upload-keystore.jks"
if [ -f "android/app/upload-keystore.jks" ]; then
    echo "   Status: ✓ Vorhanden"
    echo "   SHA1:"
    keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1
else
    echo "   Status: ✗ Nicht gefunden"
fi
echo ""

# Upload-Zertifikat für Google Play
echo "2. Upload-Zertifikat für Google Play Console:"
echo "   Datei: upload_cert.der"
if [ -f "upload_cert.der" ]; then
    echo "   Status: ✓ Vorhanden"
    echo "   SHA1 (gewünschter App-Signing SHA1):"
    keytool -printcert -file upload_cert.der 2>/dev/null | grep "SHA1:" | head -1
    echo "   Dateigröße: $(ls -lh upload_cert.der | awk '{print $5}')"
    echo "   Gültigkeitsdauer:"
    keytool -printcert -file upload_cert.der 2>/dev/null | grep "Gültig von:"
else
    echo "   Status: ✗ Nicht gefunden"
fi
echo ""

# Deployment-Zertifikat (falls relevant)
echo "3. Deployment-Zertifikat:"
echo "   Datei: deployment_cert.der"
if [ -f "deployment_cert.der" ]; then
    echo "   Status: ✓ Vorhanden"
    echo "   SHA1:"
    keytool -printcert -file deployment_cert.der 2>/dev/null | grep "SHA1:" | head -1
else
    echo "   Status: ✗ Nicht gefunden"
fi
echo ""

# Zusammenfassung
echo "=== ZUSAMMENFASSUNG ==="
echo "Upload-Keystore SHA1 (für Upload-Signierung):"
if [ -f "android/app/upload-keystore.jks" ]; then
    keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1 | awk '{print "  " $2}'
fi

echo ""
echo "Gewünschter App-Signing SHA1 (für Endnutzer):"
if [ -f "upload_cert.der" ]; then
    keytool -printcert -file upload_cert.der 2>/dev/null | grep "SHA1:" | head -1 | awk '{print "  " $2}'
fi

echo ""
echo "=== NÄCHSTE SCHRITTE ==="
echo "1. Gehe zu Google Play Console: https://play.google.com/console"
echo "2. Wähle deine App (com.einsteinai.app)"
echo "3. Navigiere zu Release → Setup → App signing"
echo "4. Lade upload_cert.der hoch"
echo "5. Aktiviere Google-managed app signing"
echo ""
echo "Nach dem Setup sollte Google Play zeigen:"
echo "- Upload certificate SHA1: $(keytool -list -v -keystore android/app/upload-keystore.jks -alias upload -storepass 123456 -keypass 123456 2>/dev/null | grep "SHA1:" | head -1 | awk '{print $2}')"
echo "- App signing certificate SHA1: $(keytool -printcert -file upload_cert.der 2>/dev/null | grep "SHA1:" | head -1 | awk '{print $2}')"

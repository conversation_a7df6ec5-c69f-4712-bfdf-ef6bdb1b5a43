# 🚀 Produktionsbereitschaft - Google Play Store Integration

## 📋 Sofort erforderliche Schritte

### 1. Google Play Developer API konfigurieren

#### Service Account erstellen:

1. **Google Cloud Console öffnen:**
   - <PERSON><PERSON><PERSON> zu [Google Cloud Console](https://console.cloud.google.com/)
   - Wähle dein Projekt aus oder erstelle ein neues

2. **Google Play Android Developer API aktivieren:**
   - Navigiere zu "APIs & Services" > "Library"
   - Suche nach "Google Play Android Developer API"
   - Klicke auf "Enable"

3. **Service Account erstellen:**
   ```bash
   # In Google Cloud Console:
   # 1. <PERSON><PERSON><PERSON> zu "IAM & Admin" > "Service Accounts"
   # 2. Klicke "Create Service Account"
   # 3. Name: "supabase-play-verification"
   # 4. Description: "Service account for Google Play purchase verification"
   ```

4. **Service Account Key generieren:**
   - Klicke auf den erstellten Service Account
   - Gehe zu "Keys" Tab
   - Klicke "Add Key" > "Create new key"
   - <PERSON><PERSON><PERSON><PERSON> "JSON" Format
   - **WICHTIG:** Lade die JSON-Datei herunter und bewahre sie sicher auf

5. **Google Play Console Berechtigung:**
    - Gehe zu `https://play.google.com/console/`
    - Navigiere zu "Setup" > "API access"
    - Klicke "Link a Google Cloud project"
    - Wähle dein Google Cloud Projekt
    - Unter "Service accounts" findest du deinen Account
    - Klicke "Manage Play Console permissions"
    - Wähle folgende Berechtigungen:
      - **View financial data, orders, and cancellation survey responses**
      - **View app information and download bulk reports (read-only)**
      - **Manage orders and subscriptions**

### 2. Supabase Umgebungsvariablen setzen

#### In Supabase Dashboard:

1. **Projekt öffnen:**
   - Gehe zu [Supabase Dashboard](https://supabase.com/dashboard)
   - Wähle dein "JobAsisstent" Projekt

2. **Edge Functions Secrets konfigurieren:**
   ```bash
   # Über Supabase CLI (empfohlen):
   supabase secrets set GOOGLE_PLAY_SERVICE_ACCOUNT_JSON='<KOMPLETTER_JSON_INHALT>'
   
   # Oder über Dashboard:
   # Settings > Edge Functions > Environment Variables
   ```

3. **Erforderliche Umgebungsvariablen:**
   ```env
   GOOGLE_PLAY_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"..."}'
   GOOGLE_PLAY_PACKAGE_NAME="com.example.jobassistent"  # Dein tatsächlicher Package Name
   ```

### 3. Package Name in der App verifizieren

#### Android Package Name prüfen:

1. **build.gradle überprüfen:**
   ```bash
   # Datei: android/app/build.gradle
   # Suche nach: applicationId "com.example.jobassistent"
   ```

2. **In Google Play Console verifizieren:**
   - Gehe zu deiner App in Google Play Console
   - Der Package Name muss exakt übereinstimmen

## 🧪 Vor Produktion - Tests und Monitoring

### 1. Umfangreiche Tests mit echten Google Play Käufen

#### Test-Setup:

1. **Google Play Console Test-Tracks:**
   ```bash
   # 1. Erstelle Internal Testing Track
   # 2. Lade eine signierte APK/AAB hoch
   # 3. Füge Test-Benutzer hinzu
   ```

2. **Test-Käufe durchführen:**
   ```dart
   // In payment_service.dart ist bereits Debug-Modus implementiert:
   static const bool _debugMode = false; // Auf false für echte Tests
   ```

3. **Test-Szenarien:**
   - ✅ Erfolgreicher Kauf
   - ✅ Fehlgeschlagener Kauf
   - ✅ Bereits vorhandenes Abonnement
   - ✅ Netzwerkfehler
   - ✅ Ungültiger Purchase Token

### 2. Monitoring und Logging einrichten

#### Supabase Logs überwachen:

```bash
# Edge Function Logs anzeigen:
supabase functions logs verify-purchase

# Oder im Dashboard:
# Edge Functions > verify-purchase > Logs
```

#### Custom Monitoring implementieren:

```sql
-- Monitoring-Tabelle erstellen:
CREATE TABLE purchase_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  purchase_token TEXT,
  product_id TEXT,
  status TEXT, -- 'success', 'failed', 'duplicate'
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy:
ALTER TABLE purchase_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Service role can manage purchase logs" ON purchase_logs
  FOR ALL USING (auth.role() = 'service_role');
```

### 3. Rate Limiting implementieren (optional)

#### Edge Function erweitern:

```typescript
// In verify-purchase/index.ts hinzufügen:
const RATE_LIMIT_KEY = `purchase_attempts_${userId}`;
const MAX_ATTEMPTS = 5;
const WINDOW_MINUTES = 15;

// Rate Limiting Check implementieren
const attempts = await supabase
  .from('rate_limits')
  .select('attempts, last_attempt')
  .eq('key', RATE_LIMIT_KEY)
  .single();
```

## 🔧 Produktions-Checkliste

### Vor dem Launch:

- [ ] **Google Play Developer API aktiviert**
- [ ] **Service Account erstellt und konfiguriert**
- [ ] **Supabase Umgebungsvariablen gesetzt**
- [ ] **Package Name verifiziert**
- [ ] **Edge Function deployed und getestet**
- [ ] **Test-Käufe erfolgreich durchgeführt**
- [ ] **Monitoring eingerichtet**
- [ ] **Error Handling getestet**
- [ ] **Security Audit durchgeführt**

### Nach dem Launch:

- [ ] **Logs regelmäßig überwachen**
- [ ] **Performance Metriken verfolgen**
- [ ] **User Feedback sammeln**
- [ ] **Backup-Strategien implementieren**

## 🚨 Wichtige Sicherheitshinweise

1. **Service Account JSON niemals in Code committen**
2. **Nur über Supabase Secrets verwalten**
3. **Regelmäßige Security Audits**
4. **Rate Limiting für Production**
5. **Comprehensive Error Logging**

## 📞 Support und Debugging

### Häufige Probleme:

1. **"Invalid purchase token"**
   - Service Account Berechtigungen prüfen
   - Package Name Übereinstimmung verifizieren

2. **"API not enabled"**
   - Google Play Android Developer API aktivieren
   - Billing in Google Cloud aktivieren

3. **"Insufficient permissions"**
   - Service Account Berechtigungen in Play Console prüfen
   - JSON Key neu generieren

### Debug Commands:

```bash
# Supabase Logs:
supabase functions logs verify-purchase --follow

# Flutter Logs:
flutter logs

# Android Logs:
adb logcat | grep -i "purchase\|billing"
```

---

**Nächster Schritt:** Beginne mit der Google Play Developer API Konfiguration und arbeite dich durch die Checkliste.
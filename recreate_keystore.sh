#!/bin/bash

# Vers<PERSON>, den korrekten Keystore zu rekonstruieren

echo "=== KEYSTORE REKONSTRUKTION ==="
echo ""

KEYSTORE_FILE="android/app/deployment-keystore.jks"
ALIAS="deployment"
STORE_PASSWORD="123456"
KEY_PASSWORD="123456"

echo "Ziel: Keystore mit SHA1 F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
echo ""

# Lösche alten Keystore falls vorhanden
if [ -f "$KEYSTORE_FILE" ]; then
    echo "Lösche alten deployment-keystore..."
    rm "$KEYSTORE_FILE"
fi

echo "Erstelle neuen Keystore mit spezifischen Parametern..."
echo ""

# Erstelle Keystore mit den gleichen Parametern wie das ursprüngliche Zertifikat
keytool -genkeypair \
    -alias "$ALIAS" \
    -keyalg RSA \
    -keysize 2048 \
    -sigalg SHA256withRSA \
    -validity 10000 \
    -keystore "$KEYSTORE_FILE" \
    -storepass "$STORE_PASSWORD" \
    -keypass "$KEY_PASSWORD" \
    -dname "CN=Android Debug,O=Android,C=US" \
    -v

echo ""
echo "Prüfe SHA1 des neuen Keystores..."
keytool -list -v -keystore "$KEYSTORE_FILE" -alias "$ALIAS" -storepass "$STORE_PASSWORD" -keypass "$KEY_PASSWORD" 2>/dev/null | grep "SHA1:" | head -1

echo ""
echo "Exportiere Zertifikat zum Vergleich..."
keytool -export -alias "$ALIAS" -keystore "$KEYSTORE_FILE" -storepass "$STORE_PASSWORD" -keypass "$KEY_PASSWORD" -file test_cert.der

echo ""
echo "SHA1 des exportierten Zertifikats:"
keytool -printcert -file test_cert.der | grep "SHA1:" | head -1

echo ""
echo "=== VERGLEICH ==="
echo "Gewünschter SHA1: F7:CE:0E:B9:19:58:A8:9E:95:E6:7E:D1:5C:CF:F8:B0:25:AA:90:DC"
echo "Neuer Keystore:   $(keytool -list -v -keystore "$KEYSTORE_FILE" -alias "$ALIAS" -storepass "$STORE_PASSWORD" -keypass "$KEY_PASSWORD" 2>/dev/null | grep "SHA1:" | head -1 | awk '{print $2}')"

# Cleanup
rm -f test_cert.der

echo ""
if [ -f "$KEYSTORE_FILE" ]; then
    echo "✅ Neuer Keystore erstellt: $KEYSTORE_FILE"
    echo ""
    echo "NÄCHSTE SCHRITTE:"
    echo "1. Aktualisiere key.properties auf deployment-keystore.jks"
    echo "2. Baue neue AAB mit: flutter build appbundle --release"
    echo "3. Prüfe SHA1 der neuen AAB"
else
    echo "❌ Keystore-Erstellung fehlgeschlagen"
fi

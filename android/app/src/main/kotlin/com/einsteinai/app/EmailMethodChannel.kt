package com.einsteinai.app

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.File

class EmailMethodChannel(private val context: Context) : MethodCallHandler {

    companion object {
        private const val CHANNEL = "ki_test/email"
        private const val TRACKER_CHANNEL = "ki_test/email_intent_tracker"
        private const val EMAIL_REQUEST_CODE = 1001
    }

    // Intent-Tracking-Variablen
    private var emailIntentActive = false
    private var currentTempFilePath: String? = null
    private var trackerChannel: MethodChannel? = null
    
    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        // Registriere E-Mail-Channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler(this)

        // Registriere Intent-Tracker-Channel
        trackerChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, TRACKER_CHANNEL)
        trackerChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "onEmailIntentStarted" -> {
                    val tempFilePath = call.argument<String>("tempFilePath")
                    val timeoutMinutes = call.argument<Int>("timeoutMinutes") ?: 10
                    onEmailIntentStarted(tempFilePath, timeoutMinutes)
                    result.success(true)
                }
                else -> result.notImplemented()
            }
        }
    }
    
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "sendEmail" -> {
                try {
                    sendEmailWithTracking(call, result)
                } catch (e: Exception) {
                    result.error("EMAIL_ERROR", "Fehler beim Senden der E-Mail: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /// Wird vom EmailIntentTracker aufgerufen, wenn ein E-Mail-Intent gestartet wird
    private fun onEmailIntentStarted(tempFilePath: String?, timeoutMinutes: Int) {
        android.util.Log.i("EmailMethodChannel", "🔥 E-Mail-Intent-Tracking gestartet: $tempFilePath")
        emailIntentActive = true
        currentTempFilePath = tempFilePath
    }

    /// Sendet E-Mail mit Intent-Status-Tracking
    private fun sendEmailWithTracking(call: MethodCall, result: Result) {
        val attachmentPaths = call.argument<List<String>>("attachmentPaths") ?: emptyList()

        // Setze Tracking-Status für temporäre CV-Dateien
        if (attachmentPaths.isNotEmpty()) {
            emailIntentActive = true
            currentTempFilePath = attachmentPaths.firstOrNull()
            android.util.Log.i("EmailMethodChannel", "🔥 E-Mail-Intent-Tracking aktiviert für: $currentTempFilePath")
        }

        // Rufe ursprüngliche sendEmail-Methode auf
        sendEmail(call, result)
    }

    private fun sendEmail(call: MethodCall, result: Result) {
        val recipients = call.argument<List<String>>("recipients") ?: emptyList()
        val subject = call.argument<String>("subject") ?: ""
        val body = call.argument<String>("body") ?: ""
        val attachmentPaths = call.argument<List<String>>("attachmentPaths") ?: emptyList()

        // 🔥 KRITISCHER DEBUG: Prüfe empfangene Parameter in Android
        android.util.Log.i("EmailMethodChannel", "🔥 ANDROID EMPFANGEN: recipients: $recipients")
        android.util.Log.i("EmailMethodChannel", "🔥 ANDROID EMPFANGEN: subject: '$subject'")
        android.util.Log.i("EmailMethodChannel", "🔥 ANDROID EMPFANGEN: body: '$body' (${body.length} Zeichen)")
        android.util.Log.i("EmailMethodChannel", "🔥 ANDROID EMPFANGEN: attachmentPaths: $attachmentPaths")

        android.util.Log.i("EmailMethodChannel", "E-Mail wird gesendet mit ${attachmentPaths.size} Anhängen")
        
        val intent = Intent(Intent.ACTION_SEND_MULTIPLE).apply {
            // Explizit E-Mail-Apps forcieren
            type = "message/rfc822"
            
            // Empfänger
            putExtra(Intent.EXTRA_EMAIL, recipients.toTypedArray())
            
            // Betreff und Text
            putExtra(Intent.EXTRA_SUBJECT, subject)
            // KRITISCHER FIX: E-Mail-Body als String setzen - die meisten E-Mail-Apps erwarten String, nicht ArrayList
            // Setze E-Mail-Body immer (auch wenn leer), da das Anschreiben darüber übertragen wird
            putExtra(Intent.EXTRA_TEXT, body)

            // 🔥 KRITISCHER DEBUG: Prüfe Intent-Parameter
            android.util.Log.i("EmailMethodChannel", "🔥 INTENT DEBUG: EXTRA_EMAIL: ${recipients.toTypedArray().contentToString()}")
            android.util.Log.i("EmailMethodChannel", "🔥 INTENT DEBUG: EXTRA_SUBJECT: '$subject'")
            android.util.Log.i("EmailMethodChannel", "🔥 INTENT DEBUG: EXTRA_TEXT (body): '$body' (${body.length} Zeichen)")
            
            // Anhänge hinzufügen
            if (attachmentPaths.isNotEmpty()) {
                val uris = ArrayList<Uri>()
                
                for (path in attachmentPaths) {
                    try {
                        val file = File(path)
                        if (file.exists()) {
                            val uri = FileProvider.getUriForFile(
                                context,
                                "${context.packageName}.fileprovider",
                                file
                            )
                            uris.add(uri)
                            android.util.Log.i("EmailMethodChannel", "Anhang hinzugefügt: ${file.name} (${file.length()} Bytes)")
                        } else {
                            android.util.Log.w("EmailMethodChannel", "Datei existiert nicht: $path")
                        }
                    } catch (e: Exception) {
                        // Ignoriere fehlerhafte Dateien
                        android.util.Log.w("EmailMethodChannel", "Fehler bei Datei $path: ${e.message}")
                    }
                }
                
                if (uris.isNotEmpty()) {
                    putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
            }
            
            // Nur E-Mail-Apps anzeigen
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        try {
            // WICHTIG: Filtere explizit nur E-Mail-Apps
            val packageManager = context.packageManager
            val emailApps = packageManager.queryIntentActivities(intent, 0)

            if (emailApps.isEmpty()) {
                result.error("NO_EMAIL_APPS", "Keine E-Mail-Apps gefunden", null)
                return
            }

            // Erstelle gefilterten Chooser nur mit E-Mail-Apps
            val filteredIntents = ArrayList<Intent>()

            for (resolveInfo in emailApps) {
                val packageName = resolveInfo.activityInfo.packageName

                // Nur echte E-Mail-Apps (Gmail, Outlook, etc.)
                if (isEmailApp(packageName)) {
                    val filteredIntent = Intent(intent).apply {
                        setPackage(packageName)
                    }
                    filteredIntents.add(filteredIntent)
                }
            }

            // Starte Intent mit Result-Tracking
            val chooser = if (filteredIntents.isEmpty()) {
                // Fallback: Alle Apps die E-Mail-Intent unterstützen
                Intent.createChooser(intent, "E-Mail senden")
            } else {
                // Zeige nur gefilterte E-Mail-Apps
                Intent.createChooser(
                    filteredIntents.removeAt(0),
                    "E-Mail senden"
                ).apply {
                    putExtra(Intent.EXTRA_INITIAL_INTENTS, filteredIntents.toTypedArray())
                }
            }

            // KRITISCH: Verwende startActivityForResult für Intent-Tracking
            if (context is Activity) {
                android.util.Log.i("EmailMethodChannel", "🔥 Starte E-Mail-Intent mit Result-Tracking")
                (context as Activity).startActivityForResult(chooser, EMAIL_REQUEST_CODE)
            } else {
                // Fallback für Non-Activity-Context
                android.util.Log.w("EmailMethodChannel", "⚠️ Context ist keine Activity - verwende normalen Start")
                chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooser)
            }

            result.success(true)
        } catch (e: Exception) {
            // Bei Fehlern: Intent-Tracking zurücksetzen
            emailIntentActive = false
            currentTempFilePath = null
            result.error("INTENT_ERROR", "Fehler beim E-Mail-Versand: ${e.message}", null)
        }
    }

    // Hilfsmethode zur Identifikation echter E-Mail-Apps
    private fun isEmailApp(packageName: String): Boolean {
        val emailPackages = listOf(
            "com.google.android.gm",           // Gmail
            "com.microsoft.office.outlook",    // Outlook
            "com.yahoo.mobile.client.android.mail", // Yahoo Mail
            "com.samsung.android.email.provider", // Samsung Email
            "com.android.email",               // Android Email
            "com.htc.android.mail",           // HTC Mail
            "com.lge.email",                  // LG Email
            "com.sony.email",                 // Sony Email
            "com.oneplus.mail",               // OnePlus Mail
            "com.xiaomi.email",               // Xiaomi Email
            "com.huawei.email",               // Huawei Email
            "com.oppo.email",                 // Oppo Email
            "com.vivo.email",                 // Vivo Email
            "com.realme.mail",                // Realme Mail
            "com.asus.email",                 // Asus Email
            "com.motorola.blur.mail",         // Motorola Mail
            "com.nokia.email",                // Nokia Email
            "com.blackberry.email",           // BlackBerry Email
            "com.amazon.email",               // Amazon Email
            "com.apple.mobilemail",           // Apple Mail (falls vorhanden)
            "com.mailboxapp",                 // Mailbox
            "com.sparrowmailapp.sparrow",     // Sparrow
            "com.airmail.android",            // Airmail
            "com.easilydo.mail",              // Edison Mail
            "com.bluemail.mail",              // BlueMail
            "com.typeeto.email",              // TypeApp
            "com.cloudmagic.mail",            // Newton Mail
            "com.boomerang.androidapp",       // Boomerang
            "com.readdle.spark"               // Spark
        )

        return emailPackages.any { packageName.contains(it, ignoreCase = true) } ||
               packageName.contains("mail", ignoreCase = true) ||
               packageName.contains("email", ignoreCase = true)
    }
}

package com.einsteinai.app

import android.os.Bundle
import android.view.WindowManager
import android.view.View
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.os.Build
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.util.Log
import android.content.Intent

class MainActivity: FlutterActivity() {
    companion object {
        private const val TAG = "MainActivity"
    }

    private val CHANNEL = "com.einsteinai.app/system_ui"
    private val SHARE_CHANNEL = "com.einsteinai.app/share_intent"
    private lateinit var emailMethodChannel: EmailMethodChannel
    private var shareMethodChannel: MethodChannel? = null

    // Intent-Request-Codes
    private val EMAIL_REQUEST_CODE = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.i(TAG, "🚀 ONCREATE START")
        Log.i(TAG, "Intent: $intent")
        Log.i(TAG, "Action: ${intent?.action}")
        Log.i(TAG, "Type: ${intent?.type}")

        // SOFORTIGE Share-Intent-Verarbeitung mit Null-Checks
        safeProcessShareIntent(intent)

        try {
            // Sichere UI-Initialisierung mit Null-Checks
            safeInitializeUI()
            Log.i(TAG, "✅ ONCREATE END")
        } catch (e: Exception) {
            Log.e(TAG, "❌ FEHLER in onCreate: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere UI-Initialisierung mit Null-Checks für Reflection-Aufrufe
     */
    private fun safeInitializeUI() {
        try {
            // Null-Check für window
            val currentWindow = window ?: run {
                Log.e(TAG, "Window ist null, kann UI nicht initialisieren")
                return
            }

            // Null-Check für decorView
            val decorView = currentWindow.decorView ?: run {
                Log.e(TAG, "DecorView ist null, kann UI nicht initialisieren")
                return
            }

            // WICHTIG: Wir wollen die Navigationsleiste während der normalen App-Nutzung sichtbar lassen
            // Daher setzen wir nur die Flags, die für die App-Darstellung wichtig sind
            @Suppress("DEPRECATION")
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE

            // Sichere Farb-Einstellungen mit Null-Checks
            try {
                currentWindow.navigationBarColor = Color.BLACK
                currentWindow.statusBarColor = Color.TRANSPARENT
            } catch (e: Exception) {
                Log.w(TAG, "Fehler beim Setzen der Fensterfarben: ${e.message}")
            }

            // Sichere Listener-Registrierung mit Null-Checks
            try {
                decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                    Log.i(TAG, "SystemUiVisibility geändert: $visibility")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Fehler beim Setzen des SystemUiVisibility-Listeners: ${e.message}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Unerwarteter Fehler in safeInitializeUI: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.i(TAG, "🔄 ONNEWINTENT START")

        try {
            // Sichere Intent-Verarbeitung mit Null-Checks
            setIntent(intent)
            safeProcessShareIntent(intent)
            Log.i(TAG, "✅ ONNEWINTENT END")
        } catch (e: Exception) {
            Log.e(TAG, "❌ FEHLER in onNewIntent: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere Share-Intent-Verarbeitung mit umfassenden Null-Checks
     */
    private fun safeProcessShareIntent(intent: Intent?) {
        Log.i(TAG, "🔍 SICHERE SHARE-INTENT PRÜFUNG")
        
        try {
            // Null-Check für Intent
            if (intent == null) {
                Log.i(TAG, "Intent ist null, keine Verarbeitung möglich")
                return
            }

            Log.i(TAG, "Intent: $intent")
            Log.i(TAG, "Action: ${intent.action}")
            Log.i(TAG, "Type: ${intent.type}")

            // Sichere Action- und Type-Prüfung
            val action = intent.action
            val type = intent.type

            if (action == null || type == null) {
                Log.i(TAG, "Action oder Type ist null (Action: $action, Type: $type)")
                return
            }

            if (action == Intent.ACTION_SEND && type == "text/plain") {
                // Sichere Extraktion des geteilten Textes
                val sharedText = try {
                    intent.getStringExtra(Intent.EXTRA_TEXT)
                } catch (e: Exception) {
                    Log.e(TAG, "Fehler beim Extrahieren des geteilten Textes: ${e.message}")
                    null
                }

                Log.i(TAG, "🎯 SHARE INTENT GEFUNDEN!")
                Log.i(TAG, "📝 Text: '$sharedText'")

                if (!sharedText.isNullOrEmpty()) {
                    Log.i(TAG, "✅ SENDE AN FLUTTER")
                    safeSendUrlToFlutter(sharedText)
                } else {
                    Log.w(TAG, "❌ LEERER ODER NULL TEXT")
                }
            } else {
                Log.i(TAG, "ℹ️ KEIN SHARE-INTENT (Action: $action, Type: $type)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ FEHLER in safeProcessShareIntent: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere URL-Übertragung an Flutter mit Null-Checks
     */
    private fun safeSendUrlToFlutter(url: String) {
        Log.i(TAG, "📤 SICHERE URL-ÜBERTRAGUNG AN FLUTTER: $url")

        try {
            // Null-Check für shareMethodChannel
            val channel = shareMethodChannel
            if (channel != null) {
                Log.i(TAG, "✅ CHANNEL VERFÜGBAR")
                try {
                    channel.invokeMethod("onUrlShared", url)
                } catch (e: Exception) {
                    Log.e(TAG, "Fehler beim Aufrufen der Flutter-Methode: ${e.message}")
                    // Fallback: URL speichern
                    pendingSharedUrl = url
                }
            } else {
                Log.w(TAG, "⚠️ CHANNEL NICHT BEREIT - SPEICHERE")
                pendingSharedUrl = url
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ FEHLER in safeSendUrlToFlutter: ${e.message}")
            e.printStackTrace()
        }
    }

    private var pendingSharedUrl: String? = null



    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        Log.i("MainActivity", "configureFlutterEngine aufgerufen")

        try {
            // Sichere Plugin-Registrierung mit Null-Checks
            safeRegisterPlugins(flutterEngine)
            
            // Sichere MethodChannel-Registrierung
            safeRegisterMethodChannels(flutterEngine)
            
            // Sichere Verarbeitung gespeicherter URLs
            safeSendPendingUrl()

            Log.i("MainActivity", "configureFlutterEngine erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler in configureFlutterEngine: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere Plugin-Registrierung mit Null-Checks
     */
    private fun safeRegisterPlugins(flutterEngine: FlutterEngine?) {
        try {
            if (flutterEngine?.plugins == null) {
                Log.e("MainActivity", "FlutterEngine oder Plugins sind null")
                return
            }

            // Registriere den FileProviderPlugin
            flutterEngine.plugins.add(FileProviderPlugin())

            // Registriere den E-Mail-MethodChannel
            emailMethodChannel = EmailMethodChannel(this)
            emailMethodChannel.configureFlutterEngine(flutterEngine)
            
            Log.i("MainActivity", "Plugins erfolgreich registriert")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler bei Plugin-Registrierung: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere MethodChannel-Registrierung mit Null-Checks
     */
    private fun safeRegisterMethodChannels(flutterEngine: FlutterEngine?) {
        try {
            val binaryMessenger = flutterEngine?.dartExecutor?.binaryMessenger
            if (binaryMessenger == null) {
                Log.e("MainActivity", "BinaryMessenger ist null")
                return
            }

            // Registriere den Share-Intent-MethodChannel
            shareMethodChannel = MethodChannel(binaryMessenger, SHARE_CHANNEL)
            Log.i("MainActivity", "Share-Intent-MethodChannel registriert")

            // Prüfe sofort nach der Registrierung auf geteilte Inhalte
            Log.i("MainActivity", "configureFlutterEngine - Prüfe Intent erneut: $intent")
            safeProcessShareIntent(intent)

            // Registriere den MethodChannel für die Steuerung der Navigationsleiste
            try {
                MethodChannel(binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
                    Log.i("MainActivity", "MethodChannel aufgerufen: ${call.method}")

                    try {
                        when (call.method) {
                            "hideNavigationBar" -> {
                                Log.i("MainActivity", "hideNavigationBar Methode aufgerufen")
                                hideSystemUI()
                                result.success(true)
                            }
                            "showNavigationBar" -> {
                                Log.i("MainActivity", "showNavigationBar Methode aufgerufen")
                                showSystemUI()
                                result.success(true)
                            }
                            else -> {
                                Log.w("MainActivity", "Nicht implementierte Methode: ${call.method}")
                                result.notImplemented()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler in SystemUI MethodChannel: ${e.message}")
                        result.error("ERROR", e.message, null)
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Fehler bei SystemUI MethodChannel-Registrierung: ${e.message}")
            }

            // Registriere den MethodChannel für Werbeanzeigen
            try {
                MethodChannel(binaryMessenger, "com.einsteinai.app/ads").setMethodCallHandler { call, result ->
                    Log.i("MainActivity", "Ads MethodChannel aufgerufen: ${call.method}")

                    try {
                        when (call.method) {
                            "setImmersiveModeForAds" -> {
                                Log.i("MainActivity", "setImmersiveModeForAds Methode aufgerufen")
                                val enabled = call.argument<Boolean>("enabled") ?: false
                                setImmersiveModeForAds(enabled)
                                result.success(true)
                            }
                            else -> {
                                Log.w("MainActivity", "Nicht implementierte Ads-Methode: ${call.method}")
                                result.notImplemented()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler in Ads MethodChannel: ${e.message}")
                        result.error("ERROR", e.message, null)
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Fehler bei Ads MethodChannel-Registrierung: ${e.message}")
            }

            Log.i("MainActivity", "MethodChannels erfolgreich registriert")
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler bei MethodChannel-Registrierung: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * Sichere Verarbeitung gespeicherter URLs mit Null-Checks
     */
    private fun safeSendPendingUrl() {
        try {
            val url = pendingSharedUrl
            if (!url.isNullOrEmpty()) {
                Log.i("MainActivity", "Sende gespeicherte URL an Flutter: $url")
                safeSendUrlToFlutter(url)
                pendingSharedUrl = null
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler beim Senden der gespeicherten URL: ${e.message}")
            e.printStackTrace()
        }
    }

    // Wird aufgerufen, wenn die App in den Vordergrund kommt
    override fun onResume() {
        super.onResume()
        Log.i(TAG, "🔥 ONRESUME START")

        try {
            // SICHERE Share-Intent-Verarbeitung
            safeProcessShareIntent(intent)
            Log.i(TAG, "✅ ONRESUME END")
        } catch (e: Exception) {
            Log.e(TAG, "❌ FEHLER in onResume: ${e.message}")
            e.printStackTrace()
        }
    }

    // Wird aufgerufen, wenn die App in den Hintergrund geht
    override fun onPause() {
        super.onPause()
        Log.i("MainActivity", "onPause aufgerufen")
    }

    // Wird aufgerufen, wenn ein Intent-Result zurückkommt (z.B. von E-Mail-App)
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.i(TAG, "🔥 onActivityResult: requestCode=$requestCode, resultCode=$resultCode")

        try {
            when (requestCode) {
                EMAIL_REQUEST_CODE -> {
                    Log.i(TAG, "🔥 E-Mail-Intent-Result empfangen")
                    handleEmailIntentResult(resultCode, data)
                }
                else -> {
                    Log.i(TAG, "ℹ️ Unbekannter Request-Code: $requestCode")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Fehler in onActivityResult: ${e.message}")
            e.printStackTrace()
        }
    }

    /// Behandelt das Result vom E-Mail-Intent
    private fun handleEmailIntentResult(resultCode: Int, data: Intent?) {
        try {
            Log.i(TAG, "🔥 Behandle E-Mail-Intent-Result: resultCode=$resultCode")

            // Informiere Flutter über das Intent-Result
            val binaryMessenger = flutterEngine?.dartExecutor?.binaryMessenger
            if (binaryMessenger != null) {
                val trackerChannel = MethodChannel(binaryMessenger, "ki_test/email_intent_tracker")

                when (resultCode) {
                    Activity.RESULT_OK -> {
                        Log.i(TAG, "✅ E-Mail-Intent erfolgreich abgeschlossen")
                        trackerChannel.invokeMethod("onEmailIntentCompleted", mapOf(
                            "success" to true,
                            "resultCode" to resultCode
                        ))
                    }
                    Activity.RESULT_CANCELED -> {
                        Log.i(TAG, "❌ E-Mail-Intent abgebrochen")
                        trackerChannel.invokeMethod("onEmailIntentCancelled", mapOf(
                            "resultCode" to resultCode
                        ))
                    }
                    else -> {
                        Log.i(TAG, "⚠️ E-Mail-Intent mit unbekanntem Result-Code: $resultCode")
                        trackerChannel.invokeMethod("onEmailIntentCompleted", mapOf(
                            "success" to false,
                            "resultCode" to resultCode
                        ))
                    }
                }
            } else {
                Log.w(TAG, "⚠️ BinaryMessenger ist null - kann Flutter nicht benachrichtigen")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Fehler beim Behandeln des E-Mail-Intent-Results: ${e.message}")

            // Bei Fehlern informiere Flutter über den Fehler
            try {
                val binaryMessenger = flutterEngine?.dartExecutor?.binaryMessenger
                if (binaryMessenger != null) {
                    val trackerChannel = MethodChannel(binaryMessenger, "ki_test/email_intent_tracker")
                    trackerChannel.invokeMethod("onEmailIntentError", mapOf(
                        "error" to e.message
                    ))
                } else {
                    Log.w(TAG, "⚠️ BinaryMessenger ist null - kann Fehler-Callback nicht senden")
                }
            } catch (channelError: Exception) {
                Log.e(TAG, "❌ Fehler beim Senden des Fehler-Callbacks: ${channelError.message}")
            }
        }
    }

    // Methode zum Verstecken der Systemleisten (nur Statusleiste, NICHT Navigationsleiste)
    private fun hideSystemUI() {
        Log.i("MainActivity", "hideSystemUI aufgerufen - ABER NAVIGATIONSLEISTE BLEIBT SICHTBAR")

        try {
            runOnUiThread {
                safeHideSystemUI()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler beim Ausführen von hideSystemUI: ${e.message}", e)
        }
    }

    /**
     * Sichere SystemUI-Versteckung mit umfassenden Null-Checks
     */
    private fun safeHideSystemUI() {
        try {
            // Null-Check für window
            val currentWindow = window ?: run {
                Log.e("MainActivity", "Window ist null in hideSystemUI")
                return
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")

                // Sichere Verwendung der neuen API mit Null-Checks
                try {
                    currentWindow.setDecorFitsSystemWindows(true)
                    
                    val insetsController = currentWindow.insetsController
                    if (insetsController != null) {
                        // Verstecke NUR die Statusleiste, NICHT die Navigationsleiste
                        insetsController.hide(WindowInsets.Type.statusBars())
                        insetsController.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_BARS_BY_TOUCH
                    } else {
                        Log.w("MainActivity", "InsetsController ist null")
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "Fehler bei Android 11+ API: ${e.message}")
                }

                // Sichere Farb-Einstellungen
                try {
                    currentWindow.navigationBarColor = Color.BLACK
                    currentWindow.statusBarColor = Color.TRANSPARENT
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Setzen der Farben: ${e.message}")
                }

                // Sichere Flag-Verwaltung
                try {
                    currentWindow.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Setzen der Flags: ${e.message}")
                }

            } else {
                Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")

                // Sichere Verwendung der alten API mit Null-Checks
                try {
                    val decorView = currentWindow.decorView
                    if (decorView != null) {
                        @Suppress("DEPRECATION")
                        decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    } else {
                        Log.w("MainActivity", "DecorView ist null")
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "Fehler bei Pre-Android 11 API: ${e.message}")
                }

                // Sichere Flag-Verwaltung
                try {
                    currentWindow.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Setzen der Flags: ${e.message}")
                }
            }

            Log.i("MainActivity", "hideSystemUI erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Unerwarteter Fehler in safeHideSystemUI: ${e.message}", e)
        }
    }

    // Methode zum Anzeigen der Systemleisten
    private fun showSystemUI() {
        Log.i("MainActivity", "showSystemUI aufgerufen")

        try {
            runOnUiThread {
                safeShowSystemUI()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler beim Ausführen von showSystemUI: ${e.message}", e)
        }
    }

    /**
     * Sichere SystemUI-Anzeige mit umfassenden Null-Checks
     */
    private fun safeShowSystemUI() {
        try {
            // Null-Check für window
            val currentWindow = window ?: run {
                Log.e("MainActivity", "Window ist null in showSystemUI")
                return
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                Log.i("MainActivity", "Android 11+ (API 30+) Methode wird verwendet")

                // Sichere Verwendung der neuen API mit Null-Checks
                try {
                    currentWindow.setDecorFitsSystemWindows(true)
                    
                    val insetsController = currentWindow.insetsController
                    if (insetsController != null) {
                        // Zeige sowohl Navigationsleiste als auch Statusleiste
                        insetsController.show(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                        insetsController.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_BARS_BY_TOUCH
                    } else {
                        Log.w("MainActivity", "InsetsController ist null")
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "Fehler bei Android 11+ API: ${e.message}")
                }

                // Sichere Farb-Einstellungen
                try {
                    currentWindow.navigationBarColor = Color.BLACK
                    currentWindow.statusBarColor = Color.TRANSPARENT
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Setzen der Farben: ${e.message}")
                }

                // Sichere Flag-Verwaltung
                try {
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Entfernen der Flags: ${e.message}")
                }

            } else {
                Log.i("MainActivity", "Pre-Android 11 Methode wird verwendet")

                // Sichere Verwendung der alten API mit Null-Checks
                try {
                    val decorView = currentWindow.decorView
                    if (decorView != null) {
                        @Suppress("DEPRECATION")
                        decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    } else {
                        Log.w("MainActivity", "DecorView ist null")
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "Fehler bei Pre-Android 11 API: ${e.message}")
                }

                // Sichere Flag-Verwaltung
                try {
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Entfernen der Flags: ${e.message}")
                }
            }

            Log.i("MainActivity", "showSystemUI erfolgreich abgeschlossen")
        } catch (e: Exception) {
            Log.e("MainActivity", "Unerwarteter Fehler in safeShowSystemUI: ${e.message}", e)
        }
    }

    // Methode zum Setzen des Immersive Mode für Werbeanzeigen
    private fun setImmersiveModeForAds(enabled: Boolean) {
        Log.i("MainActivity", "setImmersiveModeForAds aufgerufen: $enabled")
        
        try {
            runOnUiThread {
                safeSetImmersiveModeForAds(enabled)
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Fehler beim Ausführen von setImmersiveModeForAds: ${e.message}", e)
        }
    }

    /**
     * Sichere Immersive Mode Konfiguration für Werbeanzeigen mit umfassenden Null-Checks
     */
    private fun safeSetImmersiveModeForAds(enabled: Boolean) {
        try {
            // Null-Check für window
            val currentWindow = window ?: run {
                Log.e("MainActivity", "Window ist null in setImmersiveModeForAds")
                return
            }

            Log.i("MainActivity", "Konfiguriere Immersive Mode für Werbeanzeigen: $enabled")

            if (enabled) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // Sichere Verwendung der neuen API für Android 11+
                    try {
                        currentWindow.setDecorFitsSystemWindows(false)
                        
                        val insetsController = currentWindow.insetsController
                        if (insetsController != null) {
                            // Verstecke Systemleisten für Vollbild-Werbeanzeigen
                            insetsController.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                            insetsController.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                        } else {
                            Log.w("MainActivity", "InsetsController ist null für Immersive Mode")
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler bei Android 11+ Immersive Mode API: ${e.message}")
                    }
                } else {
                    // Sichere Verwendung der alten API für ältere Android-Versionen
                    try {
                        val decorView = currentWindow.decorView
                        if (decorView != null) {
                            @Suppress("DEPRECATION")
                            decorView.systemUiVisibility = (
                                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                                View.SYSTEM_UI_FLAG_FULLSCREEN or
                                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            )
                        } else {
                            Log.w("MainActivity", "DecorView ist null für Immersive Mode")
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler bei Pre-Android 11 Immersive Mode API: ${e.message}")
                    }
                }

                // Sichere Flag-Einstellungen für Werbeanzeigen
                try {
                    currentWindow.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Setzen der Immersive Mode Flags: ${e.message}")
                }

                Log.i("MainActivity", "Immersive Mode für Werbeanzeigen aktiviert")
            } else {
                // Deaktiviere Immersive Mode
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    try {
                        currentWindow.setDecorFitsSystemWindows(true)
                        
                        val insetsController = currentWindow.insetsController
                        if (insetsController != null) {
                            insetsController.show(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler beim Deaktivieren des Immersive Mode: ${e.message}")
                    }
                } else {
                    try {
                        val decorView = currentWindow.decorView
                        if (decorView != null) {
                            @Suppress("DEPRECATION")
                            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Fehler beim Deaktivieren des Immersive Mode: ${e.message}")
                    }
                }

                // Sichere Flag-Entfernung
                try {
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    currentWindow.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                } catch (e: Exception) {
                    Log.w("MainActivity", "Fehler beim Entfernen der Immersive Mode Flags: ${e.message}")
                }

                Log.i("MainActivity", "Immersive Mode für Werbeanzeigen deaktiviert")
            }

            Log.i("MainActivity", "Immersive Mode für Werbeanzeigen erfolgreich konfiguriert")
        } catch (e: Exception) {
            Log.e("MainActivity", "Unerwarteter Fehler in safeSetImmersiveModeForAds: ${e.message}", e)
        }
    }
}
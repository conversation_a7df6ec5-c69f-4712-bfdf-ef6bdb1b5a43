plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.google.gms.google-services' // Wieder aktiviert für Google Sign-In
    id 'dev.flutter.flutter-gradle-plugin'
}

// Firebase Plugins entfernt

def kotlin_version = '2.0.0'  // Aktualisiert auf 2.0.0

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new RuntimeException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Wir verwenden jetzt den deklarativen plugin-Block statt apply
// apply plugin: 'com.android.application'
// apply plugin: 'kotlin-android'
// apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    namespace "com.einsteinai.app"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    // PERFORMANCE FIX: ClassLoader-Probleme beheben
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }

    compileOptions {
        sourceCompatibility rootProject.ext.java_version
        targetCompatibility rootProject.ext.java_version
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += [
            "-Xsuppress-version-warnings",
            "-Xopt-in=kotlin.RequiresOptIn"
        ]
    }

    // PERFORMANCE FIX: Dex-Optimierungen für ClassLoader-Probleme
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = false
        additionalParameters = [
            "--multi-dex",
            "--set-max-idx-number=48000"
        ]
    }



    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.einsteinai.app"
        minSdkVersion 23  // Auf 23 erhöht für Firebase Auth
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Multidex aktivieren
        multiDexEnabled true

        // Renderscript Optimierungen
        renderscriptTargetApi 21
        renderscriptSupportModeEnabled true

        // ProfileInstaller Optimierung - Baseline Profile deaktivieren für schnelleren Start
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        testInstrumentationRunnerArguments clearPackageData: 'true'

        manifestPlaceholders = [
            'appAuthRedirectScheme': 'com.einsteinai.app'
        ]
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // Signing-Konfiguration für Release
            signingConfig signingConfigs.release

            // PERFORMANCE FIX: Optimierungen für bessere Performance
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Zusätzliche Optimierungen für Release-Builds
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            pseudoLocalesEnabled false
            zipAlignEnabled true

            // ABI Filter auskommentiert für Split-APK-Kompatibilität
            // ndk {
            //     abiFilters "armeabi-v7a", "arm64-v8a"
            // }
        }

        debug {
            // Debug-Optimierungen
            crunchPngs false
            minifyEnabled false
            shrinkResources false
            debuggable true
        }
    }

    // Dex-Optionen für schnelleres Debugging
    dexOptions {
        preDexLibraries true
        maxProcessCount 8
        javaMaxHeapSize "2g"
    }
}

// Flutter wird jetzt durch das Flutter-Plugin gesteuert
// flutter {
//     source '../..'
// }

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation "androidx.multidex:multidex:2.0.1"

    // Firebase Abhängigkeiten entfernt
    // implementation platform('com.google.firebase:firebase-bom:32.7.2')
    // implementation 'com.google.firebase:firebase-analytics'
    // implementation 'com.google.firebase:firebase-auth'
    // implementation 'com.google.android.recaptcha:recaptcha:18.4.0' // Falls ReCaptcha benötigt wird

    // Play Services Dependencies - Optimiert für bessere Performance
    implementation 'com.google.android.gms:play-services-base:18.3.0'
    implementation 'com.google.android.gms:play-services-auth:21.0.0'
    
    // Explizit Google Play Services Version für bessere Kompatibilität
    implementation 'com.google.android.gms:play-services-basement:18.3.0'
    
    // Deaktiviere Phenotype für bessere Performance
    configurations.all {
        exclude group: 'com.google.android.gms', module: 'play-services-phenotype'
    }
}
